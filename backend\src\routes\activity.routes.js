/**
 * 活动相关路由
 *
 * 处理活动的创建、查询、更新、删除等请求
 */

const express = require('express');
const router = express.Router();
const activityController = require('../controllers/activity.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 配置文件上传存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // 使用相对路径，确保文件保存在正确的位置
    const uploadDir = 'uploads/activities';
    const fullPath = path.join(process.cwd(), uploadDir);

    // 确保上传目录存在
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }

    console.log('文件上传目录:', {
      uploadDir,
      fullPath,
      cwd: process.cwd()
    });

    cb(null, fullPath);
  },
  filename: (req, file, cb) => {
    // 确保文件名正确编码
    const decodedOriginalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
    console.log('文件上传 - 文件名编码转换:', {
      original: file.originalname,
      decoded: decodedOriginalName
    });

    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(decodedOriginalName);
    cb(null, 'activity-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = [
    // 图片文件
    'image/jpeg',
    'image/png',
    'image/gif',
    // 文档文件
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'application/rtf',
    'application/zip'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型，只允许上传图片和常见文档格式'), false);
  }
};

// 配置multer - 设置非常大的文件大小限制（实质上无限制）
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 1024 * 1024 * 1024 // 1GB
  }
});

// 获取活动列表 (所有人可访问)
router.get('/',
  activityController.getActivities
);

// 获取活动详情 (所有人可访问)
router.get('/:id',
  activityController.getActivityById
);

// 创建活动 (需要认证和权限)
router.post('/',
  authMiddleware,
  checkPermission('activity:manage'),
  activityController.createActivity
);

// 上传活动图片 (需要认证和权限)
router.post('/upload-image',
  authMiddleware,
  checkPermission('activity:manage'),
  upload.single('image'),
  activityController.uploadActivityImage
);

// 更新活动 (需要认证和权限)
router.put('/:id',
  authMiddleware,
  checkPermission('activity:manage'),
  activityController.updateActivity
);

// 删除活动 (需要认证和权限)
router.delete('/:id',
  authMiddleware,
  checkPermission('activity:manage'),
  activityController.deleteActivity
);

// 获取活动附件列表 (所有人可访问)
router.get('/:id/attachments',
  activityController.getActivityAttachments
);

// 上传活动附件 (需要认证和权限)
router.post('/:id/attachments',
  authMiddleware,
  checkPermission('activity:manage'),
  upload.single('file'),
  activityController.uploadActivityAttachment
);

// 下载活动附件 (所有人可访问)
router.get('/attachments/:attachmentId/download',
  activityController.downloadActivityAttachment
);

// 删除活动附件 (需要认证和权限)
router.delete('/attachments/:attachmentId',
  authMiddleware,
  checkPermission('activity:manage'),
  activityController.deleteActivityAttachment
);

// 更新活动状态 (需要认证和权限)
router.put('/:id/status',
  authMiddleware,
  checkPermission('activity:manage'),
  activityController.updateActivityStatus
);

module.exports = router;
