"use client"

import { useState } from "react"
import { Check, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { KnowledgeFile } from "@/components/knowledge/types"
import { KnowledgeFileDetail } from "@/components/knowledge"

// 组件属性
interface KnowledgeFileReviewDialogProps {
  isOpen: boolean
  onClose: () => void
  file: KnowledgeFile | null
  onApprove: (fileId: string) => void
  onReject: (fileId: string, reason: string) => void
  loading: boolean
}

/**
 * 知识库文件审核对话框组件
 * 
 * 用于审核知识库文件
 */
export function KnowledgeFileReviewDialog({
  isOpen,
  onClose,
  file,
  onApprove,
  onReject,
  loading
}: KnowledgeFileReviewDialogProps) {
  const [rejectReason, setRejectReason] = useState("")
  const [showRejectForm, setShowRejectForm] = useState(false)

  if (!file) return null

  // 处理通过
  const handleApprove = () => {
    onApprove(file.id)
  }

  // 处理驳回
  const handleReject = () => {
    if (showRejectForm) {
      if (!rejectReason.trim()) {
        alert("请输入驳回原因")
        return
      }
      onReject(file.id, rejectReason)
    } else {
      setShowRejectForm(true)
    }
  }

  // 取消驳回
  const handleCancelReject = () => {
    setShowRejectForm(false)
    setRejectReason("")
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl">审核文件</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          {file && <KnowledgeFileDetail file={file} onClose={() => {}} />}
        </div>

        {showRejectForm ? (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">驳回原因</h3>
            <Textarea
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              placeholder="请输入驳回原因"
              className="min-h-[100px]"
              disabled={loading}
            />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={handleCancelReject} disabled={loading}>
                取消
              </Button>
              <Button
                className="bg-red-600 hover:bg-red-700 text-white"
                onClick={handleReject}
                disabled={loading || !rejectReason.trim()}
              >
                {loading ? "提交中..." : "确认驳回"}
              </Button>
            </div>
          </div>
        ) : (
          <DialogFooter>
            <div className="flex justify-end space-x-2 w-full">
              <Button
                variant="outline"
                className="border-red-500 text-red-500 hover:bg-red-50 hover:text-red-600"
                onClick={handleReject}
                disabled={loading}
              >
                <X className="h-4 w-4 mr-2" />
                驳回
              </Button>
              <Button
                className="bg-green-600 hover:bg-green-700 text-white"
                onClick={handleApprove}
                disabled={loading}
              >
                <Check className="h-4 w-4 mr-2" />
                {loading ? "提交中..." : "通过"}
              </Button>
            </div>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  )
}
