"use client"

import { Component, ErrorInfo, ReactNode } from "react"
import { AlertCircle, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"

/**
 * 错误边界组件
 * 
 * 捕获子组件中的JavaScript错误，并显示备用UI
 */
interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // 更新状态，下次渲染时显示备用UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 可以在这里记录错误信息
    console.error("Error caught by ErrorBoundary:", error, errorInfo)
    this.props.onError?.(error, errorInfo)
  }

  handleRetry = (): void => {
    this.setState({ hasError: false, error: undefined })
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // 如果提供了自定义的fallback，则使用它
      if (this.props.fallback) {
        return this.props.fallback
      }

      // 否则使用默认的错误UI
      return (
        <div className="p-6 rounded-lg border border-red-200 bg-red-50 text-red-800">
          <div className="flex items-center mb-4">
            <AlertCircle className="h-6 w-6 mr-2 text-red-600" />
            <h3 className="text-lg font-medium">出现错误</h3>
          </div>
          <p className="mb-4">组件渲染时发生错误。</p>
          {this.state.error && (
            <div className="mb-4 p-3 bg-white/50 rounded border border-red-200 overflow-auto max-h-32">
              <p className="text-sm font-mono">{this.state.error.toString()}</p>
            </div>
          )}
          <Button
            onClick={this.handleRetry}
            className="bg-red-600 hover:bg-red-700 text-white flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            重试
          </Button>
        </div>
      )
    }

    return this.props.children
  }
}
