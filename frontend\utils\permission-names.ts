/**
 * 权限名称映射
 *
 * 将权限代码映射为友好的中文名称，用于UI显示
 */

// 权限代码到中文名称的映射
export const permissionNameMap: Record<string, string> = {
  // 基础页面权限
  'home:access': '访问首页',
  'family:access': '访问家族专题',
  'activity:view': '查看活动',
  'activity:manage': '管理活动',
  'exhibition:manage': '管理展览',

  // 系统管理权限
  'system:access': '访问系统管理',
  'system:config': '系统配置',
  'user:manage': '用户管理',
  'role:manage': '角色管理',
  'ai:manage': 'AI管理',
  'security:manage': '安全管理',
  'permission:manage': '权限管理',

  // 知识库权限
  'knowledge:access': '访问知识库',
  'knowledge:manage': '管理知识库',
  'knowledge:create_user': '创建用户知识库',
  'knowledge:create_system': '创建系统知识库',
  'file:upload': '上传文件',
  'file:download': '下载文件',
  'file:manage': '管理文件',
  'file:review': '审核文件',

  // 个人专题权限
  'personal:access': '访问个人专题',
  'personal:ai_use': '使用个人专题AI',
  'personal:manage_materials': '管理个人资料',

  // 数据查询权限
  'data:access': '访问数据查询',
  'data:ai_query': '使用数据查询AI',

  // AI研究助手权限
  'assistant:use': '使用AI研究助手',

  // 其他权限
  'comment:manage': '管理评论',
  'notification:manage': '管理通知',
  'timeline:manage': '管理时间线'
};

/**
 * 获取权限的友好名称
 *
 * @param code 权限代码
 * @returns 权限的友好名称，如果没有映射则返回原始代码
 */
export function getPermissionName(code: string): string {
  return permissionNameMap[code] || code;
}
