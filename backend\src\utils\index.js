/**
 * 工具函数
 */

const crypto = require('crypto');
const DOMPurify = require('dompurify');
const { JSDOM } = require('jsdom');

/**
 * 生成随机字符串
 * @param {number} length 字符串长度
 * @returns {string} 随机字符串
 */
const generateRandomString = (length = 32) => {
  return crypto.randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
};

/**
 * 生成哈希密码
 * @param {string} password 原始密码
 * @param {string} salt 盐值
 * @returns {string} 哈希后的密码
 */
const hashPassword = (password, salt) => {
  return crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
};

/**
 * 验证密码
 * @param {string} password 原始密码
 * @param {string} hash 哈希后的密码
 * @param {string} salt 盐值
 * @returns {boolean} 密码是否匹配
 */
const verifyPassword = (password, hash, salt) => {
  const newHash = hashPassword(password, salt);
  return newHash === hash;
};

/**
 * 清理输入，防止XSS攻击
 * @param {string} input 输入字符串
 * @returns {string} 清理后的字符串
 */
const sanitizeInput = (input) => {
  if (!input) return '';

  const window = new JSDOM('').window;
  const purify = DOMPurify(window);

  // 清理HTML标签和脚本内容
  const cleanHtml = purify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true,
    FORBID_CONTENTS: ['script']
  });

  // 移除脚本内容
  return cleanHtml.replace(/alert\([^)]*\)/g, '').trim();
};

/**
 * 分页结果
 * @param {Array} items 项目数组
 * @param {number} page 页码
 * @param {number} pageSize 每页大小
 * @returns {Object} 分页结果
 */
const paginateResults = (items, page, pageSize) => {
  // 确保页码和页大小为正数
  const currentPage = page > 0 ? page : 1;
  const size = pageSize > 0 ? pageSize : 10;

  const total = items.length;
  const totalPages = Math.ceil(total / size);
  const start = (currentPage - 1) * size;
  const end = start + size;

  return {
    items: items.slice(start, end),
    total,
    page: currentPage,
    pageSize: size,
    totalPages
  };
};

/**
 * 格式化日期
 * @param {Date} date 日期对象
 * @param {string} format 格式字符串
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return '';

  const d = new Date(date);

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

/**
 * 验证邮箱格式
 * @param {string} email 邮箱地址
 * @returns {boolean} 是否是有效的邮箱
 */
const isValidEmail = (email) => {
  if (!email) return false;

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证手机号格式
 * @param {string} phone 手机号
 * @returns {boolean} 是否是有效的手机号
 */
const isValidPhone = (phone) => {
  if (!phone) return false;

  // 中国大陆手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 验证密码强度
 * @param {string} password 密码
 * @returns {Object} 密码强度评估结果
 */
const validatePasswordStrength = (password) => {
  if (!password) {
    return {
      isValid: false,
      message: '密码不能为空'
    };
  }

  if (password.length < 8) {
    return {
      isValid: false,
      message: '密码长度至少为8个字符'
    };
  }

  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const strength = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChars].filter(Boolean).length;

  if (strength < 3) {
    return {
      isValid: false,
      message: '密码必须包含大写字母、小写字母、数字和特殊字符中的至少3种'
    };
  }

  return {
    isValid: true,
    message: '密码强度符合要求'
  };
};

module.exports = {
  generateRandomString,
  hashPassword,
  verifyPassword,
  sanitizeInput,
  paginateResults,
  formatDate,
  isValidEmail,
  isValidPhone,
  validatePasswordStrength
};
