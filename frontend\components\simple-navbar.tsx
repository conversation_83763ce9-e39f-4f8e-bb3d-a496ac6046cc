"use client"

import Link from "next/link"

export function SimpleNavbar() {
  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm border-b border-gray-100">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <img
              src="/logo图标.png"
              alt="和富家族研究平台图标"
              className="h-8 w-8 mr-2"
            />
            <span className="text-xl font-bold text-green-700 tracking-tight">和富家族研究平台</span>
          </Link>

          {/* 导航链接 */}
          <div className="hidden md:flex items-center space-x-4">
            <Link href="/" className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-green-700 hover:bg-green-50">
              首页
            </Link>
            <Link href="/family-topic" className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-green-700 hover:bg-green-50">
              家族专题
            </Link>
            <Link href="/knowledge" className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-green-700 hover:bg-green-50">
              知识库
            </Link>
          </div>

          {/* 登录按钮 */}
          <div className="hidden md:flex items-center space-x-3">
            <button className="px-4 py-1.5 rounded-md text-sm font-medium text-green-700 border border-green-700 hover:bg-green-50">
              登录
            </button>
            <button className="px-4 py-1.5 rounded-md text-sm font-medium text-white bg-orange-500 hover:bg-orange-600">
              注册
            </button>
          </div>
        </div>
      </div>
    </nav>
  )
}
