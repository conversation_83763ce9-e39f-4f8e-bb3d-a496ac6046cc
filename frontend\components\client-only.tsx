"use client"

import { useEffect, useState, ReactNode } from 'react'

interface ClientOnlyProps {
  children: ReactNode
  fallback?: ReactNode
}

/**
 * 仅在客户端渲染的组件包装器
 *
 * 在服务器端渲染时显示fallback，在客户端渲染后显示children
 *
 * @param children 仅在客户端渲染的内容
 * @param fallback 服务器端渲染时显示的内容
 */
export default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
