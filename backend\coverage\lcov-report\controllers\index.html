
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for controllers</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> controllers</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.1% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>189/1044</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.55% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>46/609</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">12.16% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>9/74</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.22% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>189/1037</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="activity.controller.js"><a href="activity.controller.js.html">activity.controller.js</a></td>
	<td data-value="10.44" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.44" class="pct low">10.44%</td>
	<td data-value="134" class="abs low">14/134</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="92" class="abs low">0/92</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="10.44" class="pct low">10.44%</td>
	<td data-value="134" class="abs low">14/134</td>
	</tr>

<tr>
	<td class="file low" data-value="ai-assistant.controller.js"><a href="ai-assistant.controller.js.html">ai-assistant.controller.js</a></td>
	<td data-value="7.63" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.63" class="pct low">7.63%</td>
	<td data-value="131" class="abs low">10/131</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="75" class="abs low">0/75</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="7.63" class="pct low">7.63%</td>
	<td data-value="131" class="abs low">10/131</td>
	</tr>

<tr>
	<td class="file low" data-value="comment.controller.js"><a href="comment.controller.js.html">comment.controller.js</a></td>
	<td data-value="9.37" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.37" class="pct low">9.37%</td>
	<td data-value="64" class="abs low">6/64</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="30" class="abs low">0/30</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="9.83" class="pct low">9.83%</td>
	<td data-value="61" class="abs low">6/61</td>
	</tr>

<tr>
	<td class="file low" data-value="file.controller.js"><a href="file.controller.js.html">file.controller.js</a></td>
	<td data-value="36.08" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 36%"></div><div class="cover-empty" style="width: 64%"></div></div>
	</td>
	<td data-value="36.08" class="pct low">36.08%</td>
	<td data-value="230" class="abs low">83/230</td>
	<td data-value="23.22" class="pct low">23.22%</td>
	<td data-value="155" class="abs low">36/155</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="10" class="abs medium">5/10</td>
	<td data-value="36.08" class="pct low">36.08%</td>
	<td data-value="230" class="abs low">83/230</td>
	</tr>

<tr>
	<td class="file low" data-value="knowledge.controller.js"><a href="knowledge.controller.js.html">knowledge.controller.js</a></td>
	<td data-value="6.66" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.66" class="pct low">6.66%</td>
	<td data-value="165" class="abs low">11/165</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="100" class="abs low">0/100</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="6.7" class="pct low">6.7%</td>
	<td data-value="164" class="abs low">11/164</td>
	</tr>

<tr>
	<td class="file medium" data-value="notification.controller.js"><a href="notification.controller.js.html">notification.controller.js</a></td>
	<td data-value="62.96" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 62%"></div><div class="cover-empty" style="width: 38%"></div></div>
	</td>
	<td data-value="62.96" class="pct medium">62.96%</td>
	<td data-value="54" class="abs medium">34/54</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="12" class="abs high">10/12</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="6" class="abs medium">4/6</td>
	<td data-value="65.38" class="pct medium">65.38%</td>
	<td data-value="52" class="abs medium">34/52</td>
	</tr>

<tr>
	<td class="file low" data-value="permission.controller.js"><a href="permission.controller.js.html">permission.controller.js</a></td>
	<td data-value="17.85" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.85" class="pct low">17.85%</td>
	<td data-value="28" class="abs low">5/28</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="18.51" class="pct low">18.51%</td>
	<td data-value="27" class="abs low">5/27</td>
	</tr>

<tr>
	<td class="file low" data-value="role.controller.js"><a href="role.controller.js.html">role.controller.js</a></td>
	<td data-value="9" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9" class="pct low">9%</td>
	<td data-value="100" class="abs low">9/100</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="47" class="abs low">0/47</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="9" class="pct low">9%</td>
	<td data-value="100" class="abs low">9/100</td>
	</tr>

<tr>
	<td class="file low" data-value="system.controller.js"><a href="system.controller.js.html">system.controller.js</a></td>
	<td data-value="14.7" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 14%"></div><div class="cover-empty" style="width: 86%"></div></div>
	</td>
	<td data-value="14.7" class="pct low">14.7%</td>
	<td data-value="34" class="abs low">5/34</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="14.7" class="pct low">14.7%</td>
	<td data-value="34" class="abs low">5/34</td>
	</tr>

<tr>
	<td class="file low" data-value="userController.js"><a href="userController.js.html">userController.js</a></td>
	<td data-value="11.53" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.53" class="pct low">11.53%</td>
	<td data-value="104" class="abs low">12/104</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="60" class="abs low">0/60</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="11.53" class="pct low">11.53%</td>
	<td data-value="104" class="abs low">12/104</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-05T12:32:10.633Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    