"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ArrowLeft, Bell, Search, Info, Calendar, MessageSquare } from "lucide-react"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Input } from "@/components/ui/input"
import { NotificationDetailModal } from "@/components/notification-detail-modal-new"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON>ertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import notificationService, { Notification } from "@/services/notification-service"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

/**
 * 通知页面
 * 
 * 显示用户的所有通知，支持筛选、分页、标记已读和删除
 */
export default function NotificationsPage() {
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deleteMode, setDeleteMode] = useState<"selected" | "read">("selected")
  const [totalCount, setTotalCount] = useState(0)
  const itemsPerPage = 5

  // 获取通知列表
  useEffect(() => {
    fetchNotifications()
  }, [currentPage, activeTab])

  // 获取通知列表
  const fetchNotifications = async () => {
    try {
      setLoading(true)
      const params: any = {
        page: currentPage,
        limit: itemsPerPage,
      }

      if (activeTab === "unread") {
        params.is_read = false
      } else if (activeTab !== "all") {
        params.type = activeTab
      }

      const response = await notificationService.getNotifications(params)
      setNotifications(response.notifications)
      setTotalCount(response.pagination.total)
    } catch (error) {
      console.error("获取通知列表失败:", error)
    } finally {
      setLoading(false)
    }
  }

  // 过滤通知
  const filteredNotifications = notifications.filter((notification) => {
    const matchesSearch =
      notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.content.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesSearch
  })

  // 计算总页数
  const totalPages = Math.ceil(totalCount / itemsPerPage)

  // 标记通知为已读
  const markAsRead = async (id: string) => {
    try {
      await notificationService.markNotificationAsRead(id)
      setNotifications(notifications.map((n) => (n.id === id ? { ...n, is_read: true } : n)))
    } catch (error) {
      console.error("标记通知为已读失败:", error)
    }
  }

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    try {
      await notificationService.markAllNotificationsAsRead()
      setNotifications(notifications.map((n) => ({ ...n, is_read: true })))
      fetchNotifications() // 刷新列表
    } catch (error) {
      console.error("标记所有通知为已读失败:", error)
    }
  }

  // 处理通知点击
  const handleNotificationClick = (notification: Notification) => {
    setSelectedNotification(notification)
    setIsDetailModalOpen(true)
    if (!notification.is_read) {
      markAsRead(notification.id)
    }
  }

  // 关闭通知详情模态框
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false)
    setSelectedNotification(null)
  }

  // 选择通知
  const handleSelectNotification = (id: string) => {
    setSelectedNotifications((prev) => {
      if (prev.includes(id)) {
        return prev.filter((item) => item !== id)
      } else {
        return [...prev, id]
      }
    })
  }

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([])
    } else {
      setSelectedNotifications(filteredNotifications.map((n) => n.id))
    }
  }

  // 删除选中的通知
  const handleDeleteSelected = () => {
    setDeleteMode("selected")
    setIsDeleteDialogOpen(true)
  }

  // 删除已读通知
  const handleDeleteRead = () => {
    setDeleteMode("read")
    setIsDeleteDialogOpen(true)
  }

  // 确认删除
  const confirmDelete = async () => {
    try {
      if (deleteMode === "selected") {
        // 删除选中的通知
        for (const id of selectedNotifications) {
          await notificationService.deleteNotification(id)
        }
        setSelectedNotifications([])
      } else {
        // 删除所有已读通知
        await notificationService.deleteAllNotifications()
      }
      fetchNotifications() // 刷新列表
    } catch (error) {
      console.error("删除通知失败:", error)
    }
    setIsDeleteDialogOpen(false)
  }

  // 获取通知类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "system":
        return <Info className="h-5 w-5 text-blue-500" />
      case "activity":
        return <Calendar className="h-5 w-5 text-green-500" />
      case "file":
        return <Bell className="h-5 w-5 text-orange-500" />
      case "comment":
        return <MessageSquare className="h-5 w-5 text-purple-500" />
      default:
        return <Bell className="h-5 w-5 text-gray-500" />
    }
  }

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      return format(new Date(dateString), "yyyy-MM-dd HH:mm", { locale: zhCN })
    } catch (error) {
      return dateString
    }
  }

  return (
    <div className="min-h-screen bg-[#fdf9f1]">
      <Navbar />
      <main className="pt-20 pb-10">
        <div className="container mx-auto px-4">
          <div className="flex items-center mb-6">
            <Link href="/" className="mr-4">
              <Button
                variant="outline"
                size="sm"
                className="gap-1 text-[#111827] hover:text-[#1e7a43] transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                返回
              </Button>
            </Link>
            <h1 className="text-2xl font-bold text-[#111827]">消息通知</h1>
          </div>

          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <CardTitle className="text-[#111827]">全部通知</CardTitle>
                <div className="flex items-center gap-2 flex-wrap">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                      type="search"
                      placeholder="搜索通知..."
                      className="pl-8 w-full md:w-[200px] border-gray-200"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={markAllAsRead}
                    disabled={!notifications.some((n) => !n.is_read)}
                    className="border-[#f5a623] text-[#f5a623] hover:bg-[#f5a623]/10"
                  >
                    全部标为已读
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDeleteSelected}
                    disabled={selectedNotifications.length === 0}
                    className="border-red-500 text-red-500 hover:bg-red-50"
                  >
                    删除选中
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDeleteRead}
                    disabled={!notifications.some((n) => n.is_read)}
                    className="border-red-500 text-red-500 hover:bg-red-50"
                  >
                    删除已读
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4 bg-gray-100">
                  <TabsTrigger value="all" className="data-[state=active]:bg-[#f5a623] data-[state=active]:text-white">
                    全部
                  </TabsTrigger>
                  <TabsTrigger
                    value="unread"
                    className="data-[state=active]:bg-[#f5a623] data-[state=active]:text-white"
                  >
                    未读
                  </TabsTrigger>
                  <TabsTrigger
                    value="system"
                    className="data-[state=active]:bg-[#f5a623] data-[state=active]:text-white"
                  >
                    系统通知
                  </TabsTrigger>
                  <TabsTrigger
                    value="activity"
                    className="data-[state=active]:bg-[#f5a623] data-[state=active]:text-white"
                  >
                    活动通知
                  </TabsTrigger>
                  <TabsTrigger
                    value="file"
                    className="data-[state=active]:bg-[#f5a623] data-[state=active]:text-white"
                  >
                    文件通知
                  </TabsTrigger>
                  <TabsTrigger
                    value="comment"
                    className="data-[state=active]:bg-[#f5a623] data-[state=active]:text-white"
                  >
                    评论通知
                  </TabsTrigger>
                </TabsList>

                <TabsContent value={activeTab}>
                  {loading ? (
                    <div className="py-8 text-center">
                      <p className="text-gray-500">加载中...</p>
                    </div>
                  ) : filteredNotifications.length > 0 ? (
                    <>
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <Checkbox
                            id="select-all"
                            checked={
                              selectedNotifications.length === filteredNotifications.length &&
                              filteredNotifications.length > 0
                            }
                            onCheckedChange={handleSelectAll}
                            className="mr-2 data-[state=checked]:bg-[#f5a623] data-[state=checked]:border-[#f5a623]"
                          />
                          <label htmlFor="select-all" className="text-sm cursor-pointer text-[#111827]">
                            全选
                          </label>
                        </div>
                        <div className="text-sm text-gray-500">共 {totalCount} 条通知</div>
                      </div>
                      <div className="space-y-2">
                        {filteredNotifications.map((notification) => (
                          <div
                            key={notification.id}
                            className={`p-4 rounded-md border ${
                              notification.is_read ? "bg-white border-gray-200" : "bg-blue-50 border-blue-100"
                            } hover:bg-gray-50`}
                          >
                            <div className="flex items-start gap-3">
                              <Checkbox
                                id={`notification-${notification.id}`}
                                checked={selectedNotifications.includes(notification.id)}
                                onCheckedChange={() => handleSelectNotification(notification.id)}
                                className="mt-1 data-[state=checked]:bg-[#f5a623] data-[state=checked]:border-[#f5a623]"
                                onClick={(e) => e.stopPropagation()}
                              />
                              <div
                                className="flex-1 cursor-pointer"
                                onClick={() => handleNotificationClick(notification)}
                              >
                                <div className="flex justify-between items-start">
                                  <div className="flex items-start gap-3">
                                    {getTypeIcon(notification.type)}
                                    <div>
                                      <p className="font-medium text-[#111827]">{notification.title}</p>
                                      <p className="text-sm text-gray-600 mt-1">{notification.content}</p>
                                    </div>
                                  </div>
                                  <span className="text-xs text-gray-500">{formatTime(notification.created_at)}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                      {totalPages > 1 && (
                        <Pagination className="mt-4">
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                                className={`${
                                  currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"
                                } text-[#111827]`}
                              />
                            </PaginationItem>
                            {Array.from({ length: totalPages }).map((_, i) => (
                              <PaginationItem key={i}>
                                <PaginationLink
                                  isActive={currentPage === i + 1}
                                  onClick={() => setCurrentPage(i + 1)}
                                  className={`cursor-pointer ${currentPage === i + 1 ? "bg-[#f5a623] text-white" : ""}`}
                                >
                                  {i + 1}
                                </PaginationLink>
                              </PaginationItem>
                            ))}
                            <PaginationItem>
                              <PaginationNext
                                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                                className={`${
                                  currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"
                                } text-[#111827]`}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      )}
                    </>
                  ) : (
                    <div className="py-8 text-center">
                      <p className="text-gray-500">暂无通知</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />

      <NotificationDetailModal
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
        notification={selectedNotification}
      />
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              {deleteMode === "selected"
                ? `您确定要删除选中的 ${selectedNotifications.length} 条通知吗？`
                : "您确定要删除所有已读通知吗？"}
              此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-[#f5a623] hover:bg-[#f5a623]/90 text-white">
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
