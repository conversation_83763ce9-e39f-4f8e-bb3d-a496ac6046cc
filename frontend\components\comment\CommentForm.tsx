/**
 * 评论表单组件
 * 
 * 用于提交评论
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { toast } from '@/components/ui/use-toast'

// 评论表单组件属性
interface CommentFormProps {
  onSubmit: (content: string) => void
  parentId?: number
  placeholder?: string
}

// 评论表单组件
export const CommentForm: React.FC<CommentFormProps> = ({
  onSubmit,
  parentId,
  placeholder = '请输入您的评论...'
}) => {
  const [content, setContent] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 处理提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!content.trim()) {
      toast({
        title: '评论内容不能为空',
        variant: 'destructive'
      })
      return
    }
    
    setIsSubmitting(true)
    
    try {
      await onSubmit(content)
      setContent('') // 清空输入
    } catch (error) {
      console.error('提交评论失败', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Textarea
        placeholder={placeholder}
        value={content}
        onChange={(e) => setContent(e.target.value)}
        rows={4}
        className="resize-none"
      />
      <div className="flex justify-end">
        <Button type="submit" disabled={isSubmitting || !content.trim()}>
          {isSubmitting ? '提交中...' : '提交评论'}
        </Button>
      </div>
    </form>
  )
}

export default CommentForm
