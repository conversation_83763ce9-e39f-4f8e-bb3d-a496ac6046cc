
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35.5% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>502/1414</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">13.43% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>93/692</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">37.3% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>47/126</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35.65% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>501/1405</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="controllers"><a href="controllers/index.html">controllers</a></td>
	<td data-value="18.1" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 18%"></div><div class="cover-empty" style="width: 82%"></div></div>
	</td>
	<td data-value="18.1" class="pct low">18.1%</td>
	<td data-value="1044" class="abs low">189/1044</td>
	<td data-value="7.55" class="pct low">7.55%</td>
	<td data-value="609" class="abs low">46/609</td>
	<td data-value="12.16" class="pct low">12.16%</td>
	<td data-value="74" class="abs low">9/74</td>
	<td data-value="18.22" class="pct low">18.22%</td>
	<td data-value="1037" class="abs low">189/1037</td>
	</tr>

<tr>
	<td class="file high" data-value="middlewares"><a href="middlewares/index.html">middlewares</a></td>
	<td data-value="86.11" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.11" class="pct high">86.11%</td>
	<td data-value="36" class="abs high">31/36</td>
	<td data-value="69.23" class="pct medium">69.23%</td>
	<td data-value="26" class="abs medium">18/26</td>
	<td data-value="75" class="pct high">75%</td>
	<td data-value="4" class="abs high">3/4</td>
	<td data-value="86.11" class="pct high">86.11%</td>
	<td data-value="36" class="abs high">31/36</td>
	</tr>

<tr>
	<td class="file high" data-value="models"><a href="models/index.html">models</a></td>
	<td data-value="98.98" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 98%"></div><div class="cover-empty" style="width: 2%"></div></div>
	</td>
	<td data-value="98.98" class="pct high">98.98%</td>
	<td data-value="99" class="abs high">98/99</td>
	<td data-value="76.92" class="pct high">76.92%</td>
	<td data-value="13" class="abs high">10/13</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="29" class="abs high">29/29</td>
	<td data-value="98.98" class="pct high">98.98%</td>
	<td data-value="99" class="abs high">98/99</td>
	</tr>

<tr>
	<td class="file high" data-value="routes"><a href="routes/index.html">routes</a></td>
	<td data-value="85.18" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 85%"></div><div class="cover-empty" style="width: 15%"></div></div>
	</td>
	<td data-value="85.18" class="pct high">85.18%</td>
	<td data-value="162" class="abs high">138/162</td>
	<td data-value="7.14" class="pct low">7.14%</td>
	<td data-value="14" class="abs low">1/14</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="85.18" class="pct high">85.18%</td>
	<td data-value="162" class="abs high">138/162</td>
	</tr>

<tr>
	<td class="file low" data-value="services/api"><a href="services/api/index.html">services/api</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	</tr>

<tr>
	<td class="file high" data-value="utils"><a href="utils/index.html">utils</a></td>
	<td data-value="73.01" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 73%"></div><div class="cover-empty" style="width: 27%"></div></div>
	</td>
	<td data-value="73.01" class="pct high">73.01%</td>
	<td data-value="63" class="abs high">46/63</td>
	<td data-value="62.06" class="pct medium">62.06%</td>
	<td data-value="29" class="abs medium">18/29</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="9" class="abs medium">6/9</td>
	<td data-value="73.77" class="pct high">73.77%</td>
	<td data-value="61" class="abs high">45/61</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-05T12:32:10.633Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    