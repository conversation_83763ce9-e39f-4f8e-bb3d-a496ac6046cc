/**
 * API使用示例组件
 * 
 * 展示如何使用API服务进行数据请求
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Table, message, Space, Modal, Form, Input, DatePicker } from 'antd';
import { activityApi } from '../../api';
import moment from 'moment';

const ApiExample = () => {
  const [loading, setLoading] = useState(false);
  const [activities, setActivities] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingId, setEditingId] = useState(null);

  // 获取活动列表
  const fetchActivities = async (page = 1, limit = 10) => {
    try {
      setLoading(true);
      const response = await activityApi.getActivityList({
        page,
        limit,
        status: 'published'
      });
      
      setActivities(response.data.activities);
      setPagination({
        current: page,
        pageSize: limit,
        total: response.data.pagination.total
      });
    } catch (error) {
      console.error('获取活动列表失败:', error);
      message.error('获取活动列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建活动
  const createActivity = async (values) => {
    try {
      setLoading(true);
      await activityApi.createActivity({
        ...values,
        date: values.date.format('YYYY-MM-DD'),
        status: 'published'
      });
      
      message.success('创建活动成功');
      setModalVisible(false);
      form.resetFields();
      fetchActivities();
    } catch (error) {
      console.error('创建活动失败:', error);
      message.error('创建活动失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新活动
  const updateActivity = async (values) => {
    try {
      setLoading(true);
      await activityApi.updateActivity(editingId, {
        ...values,
        date: values.date.format('YYYY-MM-DD')
      });
      
      message.success('更新活动成功');
      setModalVisible(false);
      form.resetFields();
      setEditingId(null);
      fetchActivities();
    } catch (error) {
      console.error('更新活动失败:', error);
      message.error('更新活动失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除活动
  const deleteActivity = async (id) => {
    try {
      setLoading(true);
      await activityApi.deleteActivity(id);
      
      message.success('删除活动成功');
      fetchActivities();
    } catch (error) {
      console.error('删除活动失败:', error);
      message.error('删除活动失败');
    } finally {
      setLoading(false);
    }
  };

  // 编辑活动
  const editActivity = (record) => {
    setEditingId(record.id);
    form.setFieldsValue({
      title: record.title,
      date: moment(record.date),
      description: record.description
    });
    setModalVisible(true);
  };

  // 表格列定义
  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title'
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      render: (text) => moment(text).format('YYYY-MM-DD')
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => editActivity(record)}>编辑</Button>
          <Button type="link" danger onClick={() => {
            Modal.confirm({
              title: '确认删除',
              content: `确定要删除活动"${record.title}"吗？`,
              onOk: () => deleteActivity(record.id)
            });
          }}>删除</Button>
        </Space>
      )
    }
  ];

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    fetchActivities(pagination.current, pagination.pageSize);
  };

  // 处理表单提交
  const handleFormSubmit = (values) => {
    if (editingId) {
      updateActivity(values);
    } else {
      createActivity(values);
    }
  };

  // 组件挂载时获取活动列表
  useEffect(() => {
    fetchActivities();
  }, []);

  return (
    <div>
      <Card
        title="活动列表"
        extra={
          <Button type="primary" onClick={() => {
            setEditingId(null);
            form.resetFields();
            setModalVisible(true);
          }}>
            创建活动
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={activities}
          rowKey="id"
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
        />
      </Card>

      <Modal
        title={editingId ? '编辑活动' : '创建活动'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
        >
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入活动标题' }]}
          >
            <Input placeholder="请输入活动标题" />
          </Form.Item>
          
          <Form.Item
            name="date"
            label="日期"
            rules={[{ required: true, message: '请选择活动日期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入活动描述' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入活动描述" />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingId ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ApiExample;
