/**
 * 简单测试文件
 * 用于验证测试环境是否正常工作
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// 简单组件
const SimpleComponent = ({ text }: { text: string }) => {
  return <div data-testid="simple-component">{text}</div>;
};

describe('简单测试', () => {
  test('应该正确渲染组件', () => {
    render(<SimpleComponent text="测试文本" />);
    expect(screen.getByTestId('simple-component')).toHaveTextContent('测试文本');
  });
});
