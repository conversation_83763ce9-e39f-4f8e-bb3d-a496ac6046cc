"use client"

import type React from "react"

import { useState } from "react"
import { Navbar } from "@/components/navbar"
import { X, ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function DataMonitoringPage() {
  // 监测配置状态
  const [monitoringStatus, setMonitoringStatus] = useState(true)
  const [keywordInput, setKeywordInput] = useState("")
  const [platformInput, setPlatformInput] = useState("")
  const [keywords, setKeywords] = useState(["品牌声誉", "免面评价", "客户投诉", "产品质量", "服务态度"])
  const [platforms, setPlatforms] = useState(["小红书", "百度贴吧", "豆瓣"])

  // 数据统计
  const dataStats = {
    today: {
      count: 2847,
      growth: 12.5,
    },
    total: {
      count: 128394,
      days: 30,
    },
    pending: 156,
  }

  // 情感分析数据
  const sentimentData = [
    {
      content: "这家店的服务态度真的很棒，等了一个小时...",
      sentiment: "负面",
      confidence: "89%",
      source: "微博",
      time: "2024-01-20 14:23",
    },
  ]

  // 预警处理数据
  const alertData = [
    {
      id: 1,
      level: "紧急",
      content: "品牌声誉存在严重问题，多人投诉...",
      source: "https://weibo.com/...",
      time: "2024-01-20 15:30",
      status: "待处理",
    },
  ]

  // 添加关键词
  const addKeyword = () => {
    if (keywordInput.trim() && !keywords.includes(keywordInput.trim())) {
      setKeywords([...keywords, keywordInput.trim()])
      setKeywordInput("")
    }
  }

  // 删除关键词
  const removeKeyword = (keyword: string) => {
    setKeywords(keywords.filter((k) => k !== keyword))
  }

  // 添加平台
  const addPlatform = () => {
    if (platformInput.trim() && !platforms.includes(platformInput.trim())) {
      setPlatforms([...platforms, platformInput.trim()])
      setPlatformInput("")
    }
  }

  // 删除平台
  const removePlatform = (platform: string) => {
    setPlatforms(platforms.filter((p) => p !== platform))
  }

  // 切换监测状态
  const toggleMonitoringStatus = () => {
    setMonitoringStatus(!monitoringStatus)
  }

  // 处理关键词输入回车事件
  const handleKeywordKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      addKeyword()
    }
  }

  // 处理平台输入回车事件
  const handlePlatformKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      addPlatform()
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        {/* 监测配置 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold">监测配置</h2>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <span className="text-sm mr-2">监测状态:</span>
                <span className={`inline-flex items-center ${monitoringStatus ? "text-green-600" : "text-gray-500"}`}>
                  <span
                    className={`inline-block w-2 h-2 rounded-full mr-1 ${monitoringStatus ? "bg-green-600" : "bg-gray-400"}`}
                  ></span>
                  {monitoringStatus ? "运行中" : "已停止"}
                </span>
              </div>
              <Button
                className={monitoringStatus ? "bg-[#862633] hover:bg-[#862633]/90" : "bg-gray-700 hover:bg-gray-800"}
                onClick={toggleMonitoringStatus}
              >
                {monitoringStatus ? "停止监测" : "启动监测"}
              </Button>
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2">关键词设置</h3>
            <div className="flex">
              <input
                type="text"
                value={keywordInput}
                onChange={(e) => setKeywordInput(e.target.value)}
                onKeyDown={handleKeywordKeyDown}
                placeholder="请输入关键词，多个关键词用逗号分隔"
                className="flex-1 border border-gray-300 rounded-l-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#862633] focus:border-transparent"
              />
              <Button onClick={addKeyword} className="rounded-l-none bg-[#862633] hover:bg-[#862633]/90">
                添加
              </Button>
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2">已添加关键词:</h3>
            <div className="flex flex-wrap gap-2">
              {keywords.map((keyword, index) => (
                <div key={index} className="flex items-center bg-gray-100 rounded-md px-3 py-1">
                  <span className="text-sm">{keyword}</span>
                  <button onClick={() => removeKeyword(keyword)} className="ml-2 text-gray-500 hover:text-red-600">
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2">监测平台</h3>
            <Tabs defaultValue="weibo">
              <TabsList className="mb-4">
                <TabsTrigger value="weibo">微博</TabsTrigger>
                <TabsTrigger value="zhihu">知乎</TabsTrigger>
                <TabsTrigger value="douyin">抖音</TabsTrigger>
              </TabsList>
              <TabsContent value="weibo" className="space-y-4">
                <div className="flex">
                  <input
                    type="text"
                    value={platformInput}
                    onChange={(e) => setPlatformInput(e.target.value)}
                    onKeyDown={handlePlatformKeyDown}
                    placeholder="请输入新增监测平台URL"
                    className="flex-1 border border-gray-300 rounded-l-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#862633] focus:border-transparent"
                  />
                  <Button onClick={addPlatform} className="rounded-l-none bg-[#862633] hover:bg-[#862633]/90">
                    添加平台
                  </Button>
                </div>
              </TabsContent>
              <TabsContent value="zhihu">
                <p className="text-gray-500">知乎平台配置与微博相同</p>
              </TabsContent>
              <TabsContent value="douyin">
                <p className="text-gray-500">抖音平台配置与微博相同</p>
              </TabsContent>
            </Tabs>
          </div>

          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2">已添加自定义平台:</h3>
            <div className="flex flex-wrap gap-2">
              {platforms.map((platform, index) => (
                <div key={index} className="flex items-center bg-gray-100 rounded-md px-3 py-1">
                  <span className="text-sm">{platform}</span>
                  <button onClick={() => removePlatform(platform)} className="ml-2 text-gray-500 hover:text-red-600">
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end">
            <Button className="bg-[#862633] hover:bg-[#862633]/90">保存配置</Button>
          </div>
        </div>

        {/* 数据抓取 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-bold mb-6">数据抓取</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 mb-2">今日抓取量</h3>
              <div className="flex items-end">
                <span className="text-3xl font-bold">{dataStats.today.count.toLocaleString()}</span>
                <span className="text-sm text-green-600 ml-2">↑ 较昨日增长 {dataStats.today.growth}%</span>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 mb-2">累计抓取量</h3>
              <div className="flex items-end">
                <span className="text-3xl font-bold">{dataStats.total.count.toLocaleString()}</span>
                <span className="text-sm text-gray-500 ml-2">近 {dataStats.total.days} 天数据</span>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 mb-2">待处理数据</h3>
              <div className="flex items-end">
                <span className="text-3xl font-bold">{dataStats.pending}</span>
                <span className="text-sm text-red-600 ml-2">需要及时处理</span>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-between items-center">
            <div className="flex items-center">
              <span className="text-sm mr-2">任务状态:</span>
              <span className="inline-flex items-center text-green-600">
                <span className="inline-block w-2 h-2 rounded-full bg-green-600 mr-1"></span>
                正常运行中
              </span>
            </div>
            <Button className="bg-[#862633] hover:bg-[#862633]/90">刷新</Button>
          </div>
        </div>

        {/* 情感分析 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-bold mb-6">情感分析</h2>

          <div className="h-64 bg-gray-50 rounded-lg mb-6 flex items-center justify-center">
            <p className="text-gray-500">情感分析图表将在此处显示</p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">内容预览</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">情感倾向</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">置信度</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">来源平台</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">发布时间</th>
                </tr>
              </thead>
              <tbody>
                {sentimentData.map((item, index) => (
                  <tr key={index} className="border-b border-gray-200">
                    <td className="py-4 px-4 text-sm">{item.content}</td>
                    <td className="py-4 px-4">
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          item.sentiment === "正面"
                            ? "bg-green-100 text-green-800"
                            : item.sentiment === "负面"
                              ? "bg-red-100 text-red-800"
                              : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {item.sentiment}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-sm">{item.confidence}</td>
                    <td className="py-4 px-4 text-sm">{item.source}</td>
                    <td className="py-4 px-4 text-sm">{item.time}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* 预警处理 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold">预警处理</h2>
            <div className="flex space-x-2">
              <Button className="bg-[#862633] hover:bg-[#862633]/90">批量处理</Button>
              <Button variant="outline">批量忽略</Button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">预警等级</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">预警内容</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">来源链接</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">发现时间</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">处理状态</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">操作</th>
                </tr>
              </thead>
              <tbody>
                {alertData.map((alert) => (
                  <tr key={alert.id} className="border-b border-gray-200">
                    <td className="py-4 px-4">
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          alert.level === "紧急"
                            ? "bg-red-100 text-red-800"
                            : alert.level === "警告"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {alert.level}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-sm">{alert.content}</td>
                    <td className="py-4 px-4 text-sm text-blue-600 hover:underline">
                      <a href={alert.source} target="_blank" rel="noopener noreferrer">
                        {alert.source}
                      </a>
                    </td>
                    <td className="py-4 px-4 text-sm">{alert.time}</td>
                    <td className="py-4 px-4">
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          alert.status === "已处理"
                            ? "bg-green-100 text-green-800"
                            : alert.status === "处理中"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {alert.status}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 text-sm">处理</button>
                        <button className="text-gray-600 hover:text-gray-800 text-sm">忽略</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="mt-6 flex justify-between items-center">
            <div className="text-sm text-gray-500">显示 1 到 10 条，共 156 条</div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" className="flex items-center">
                <ChevronLeft className="h-4 w-4 mr-1" />
                上一页
              </Button>
              <Button className="bg-[#862633] hover:bg-[#862633]/90 h-8 w-8 p-0">1</Button>
              <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                2
              </Button>
              <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                3
              </Button>
              <Button variant="outline" size="sm" className="flex items-center">
                下一页
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
