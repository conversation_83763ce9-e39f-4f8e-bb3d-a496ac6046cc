"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

/**
 * 知识库页面的操作按钮组件
 * 使用客户端组件避免服务器端和客户端渲染不一致的问题
 *
 * @param props 组件属性
 * @returns 操作按钮组件
 */
interface ActionButtonsProps {
  isAdmin: boolean
  pendingFilesCount: number
  onCreateKnowledgeBase: () => void
  onUploadFile: () => void
}

const KnowledgeActionButtons: React.FC<ActionButtonsProps> = ({
  isAdmin,
  pendingFilesCount,
  onCreateKnowledgeBase,
  onUploadFile
}) => {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)

  // 只在客户端渲染
  useEffect(() => {
    setMounted(true)
  }, [])

  // 在服务器端渲染时返回空内容
  if (!mounted) {
    return <div className="h-10"></div> // 占位符，避免布局跳动
  }

  return (
    <div className="flex gap-3">
      <button
        type="button"
        className="bg-[#1e7a43] hover:bg-[#1e7a43]/90 text-white shadow-md transition-all flex items-center px-4 py-2 rounded-md text-sm font-medium"
        onClick={onCreateKnowledgeBase}
      >
        创建知识库
      </button>

      {isAdmin && (
        <button
          type="button"
          className="bg-[#f5a623] hover:bg-[#f5a623]/90 text-white shadow-md transition-all flex items-center px-4 py-2 rounded-md text-sm font-medium"
          onClick={() => router.push('/knowledge/review')}
        >
          文件审核
          {pendingFilesCount > 0 && (
            <span className="ml-1 bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">
              {pendingFilesCount}
            </span>
          )}
        </button>
      )}

      <button
        type="button"
        className="bg-[#f5a623] hover:bg-[#f5a623]/90 text-white shadow-md transition-all flex items-center px-4 py-2 rounded-md text-sm font-medium"
        onClick={onUploadFile}
      >
        上传文件
      </button>
    </div>
  )
}

export default KnowledgeActionButtons
