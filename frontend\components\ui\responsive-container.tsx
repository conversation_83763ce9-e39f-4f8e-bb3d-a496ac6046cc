"use client"

import { cn } from "@/lib/utils"
import { useEffect, useState } from "react"

/**
 * 响应式容器组件
 * 
 * 根据屏幕尺寸自动调整布局和内容
 */
interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  mobileClassName?: string
  tabletClassName?: string
  desktopClassName?: string
  mobileContent?: React.ReactNode
  tabletContent?: React.ReactNode
  desktopContent?: React.ReactNode
  breakpoints?: {
    tablet?: number
    desktop?: number
  }
}

export function ResponsiveContainer({
  children,
  className,
  mobileClassName,
  tabletClassName,
  desktopClassName,
  mobileContent,
  tabletContent,
  desktopContent,
  breakpoints = {
    tablet: 768,
    desktop: 1024
  }
}: ResponsiveContainerProps) {
  const [screenSize, setScreenSize] = useState<"mobile" | "tablet" | "desktop">("desktop")
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    
    const handleResize = () => {
      const width = window.innerWidth
      if (width < breakpoints.tablet!) {
        setScreenSize("mobile")
      } else if (width < breakpoints.desktop!) {
        setScreenSize("tablet")
      } else {
        setScreenSize("desktop")
      }
    }
    
    // 初始化
    handleResize()
    
    // 添加事件监听
    window.addEventListener("resize", handleResize)
    
    // 清理
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [breakpoints.tablet, breakpoints.desktop])

  // 确定要显示的内容
  const content = (() => {
    if (!isClient) return children // 服务器端渲染时使用默认内容
    
    if (screenSize === "mobile" && mobileContent !== undefined) {
      return mobileContent
    } else if (screenSize === "tablet" && tabletContent !== undefined) {
      return tabletContent
    } else if (screenSize === "desktop" && desktopContent !== undefined) {
      return desktopContent
    }
    
    return children
  })()

  // 确定要使用的类名
  const containerClassName = cn(
    className,
    screenSize === "mobile" && mobileClassName,
    screenSize === "tablet" && tabletClassName,
    screenSize === "desktop" && desktopClassName
  )

  return <div className={containerClassName}>{content}</div>
}
