/**
 * 查询用户列表
 */

// 加载环境变量
require('dotenv').config();

// 导入模型
const db = require('./src/models');

// 查询用户列表
async function queryUsers() {
  try {
    // 连接数据库
    await db.sequelize.authenticate();
    console.log('数据库连接成功');

    // 查询用户列表
    const users = await db.User.findAll({
      attributes: ['id', 'username', 'email', 'role']
    });

    console.log('用户列表:', JSON.stringify(users, null, 2));

    // 关闭数据库连接
    await db.sequelize.close();
  } catch (error) {
    console.error('查询失败:', error);
  }
}

// 执行查询
queryUsers();
