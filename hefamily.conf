# HTTP服务器 - 重定向到HTTPS
server {
    listen 80;
    server_name hefuf.com www.hefuf.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS服务器
server {
    listen 443 ssl http2;
    server_name hefuf.com www.hefuf.com;  # 您的域名

    # SSL证书配置 (请根据实际情况配置SSL证书路径)
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 全局设置
    client_max_body_size 1024M;  # 允许上传最大1GB的文件

    # 超时设置
    proxy_connect_timeout 600;    # 连接后端服务器的超时时间（秒）
    proxy_send_timeout 600;       # 发送请求到后端服务器的超时时间（秒）
    proxy_read_timeout 600;       # 从后端服务器读取响应的超时时间（秒）
    send_timeout 600;             # 客户端与服务器连接的超时时间（秒）

    # 前端应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 后端API
    location /api {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 上传文件的静态服务
    location /uploads {
        alias /soft/hefu/20250513/backend/uploads;
        try_files $uri =404;
    }
}
