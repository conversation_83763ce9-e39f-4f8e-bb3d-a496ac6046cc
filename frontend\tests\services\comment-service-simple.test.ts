/**
 * 评论服务简单测试
 */

import apiService from '@/services/api-service';
import { mockFetch, resetMockFetch } from '../test-utils';

// 模拟API服务
jest.mock('@/services/api-service', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  del: jest.fn()
}));

describe('评论服务简单测试', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch();
    jest.clearAllMocks();
  });

  test('API服务应该被正确模拟', () => {
    // 模拟成功的响应
    const mockResponse = {
      success: true,
      data: {
        id: 1,
        content: '测试评论'
      }
    };

    // 设置模拟返回值
    (apiService.get as jest.Mock).mockResolvedValue(mockResponse);

    // 调用API服务
    const result = apiService.get('/comments');

    // 验证API服务被调用
    expect(apiService.get).toHaveBeenCalledWith('/comments');

    // 验证返回值
    return result.then(data => {
      expect(data).toEqual(mockResponse);
    });
  });

  test('API服务应该处理错误', async () => {
    // 模拟失败的响应
    const mockError = {
      success: false,
      message: '请求失败'
    };

    // 设置模拟返回值
    (apiService.get as jest.Mock).mockRejectedValue(mockError);

    // 调用API服务并捕获错误
    try {
      await apiService.get('/comments');
      // 如果没有抛出错误，测试失败
      expect(true).toBe(false);
    } catch (error) {
      // 验证错误
      expect(error).toEqual(mockError);
    }
  });
});
