/**
 * 添加额外权限脚本
 *
 * 运行方法：
 * node add-additional-permissions.js
 */

const db = require('./src/models');

(async () => {
  try {
    console.log('开始添加额外权限...');

    // 基础页面权限
    const basicPagePermissions = [
      {
        name: '访问首页',
        code: 'home:access',
        description: '访问系统首页',
        module: 'basic'
      },
      {
        name: '访问家族专题',
        code: 'family:access',
        description: '访问家族专题页面',
        module: 'basic'
      }
    ];

    // AI研究助手权限
    const aiAssistantPermissions = [
      {
        name: '使用AI研究助手',
        code: 'assistant:use',
        description: '使用AI研究助手功能',
        module: 'assistant'
      }
    ];

    // 合并所有权限
    const allPermissions = [
      ...basicPagePermissions,
      ...aiAssistantPermissions
    ];

    // 获取访问者角色
    let visitorRole = await db.Role.findOne({
      where: { name: '访问者' }
    });

    // 如果找不到"访问者"角色，尝试查找"basic_user"角色
    if (!visitorRole) {
      console.log('未找到"访问者"角色，尝试查找"basic_user"角色');
      visitorRole = await db.Role.findOne({
        where: { name: 'basic_user' }
      });
    }

    // 如果仍然找不到，尝试查找任何非管理员角色
    if (!visitorRole) {
      console.log('未找到"basic_user"角色，尝试查找任何非管理员角色');
      visitorRole = await db.Role.findOne({
        where: {
          name: {
            [db.Sequelize.Op.ne]: 'admin'
          }
        }
      });
    }

    // 如果仍然找不到，创建一个新的访问者角色
    if (!visitorRole) {
      console.log('未找到任何非管理员角色，创建新的访问者角色');
      visitorRole = await db.Role.create({
        name: 'basic_user',
        description: '基本用户角色',
        is_system: true
      });
    }

    console.log('使用角色:', visitorRole.name, '，ID:', visitorRole.id);

    // 添加权限并分配给访问者角色
    for (const permissionData of allPermissions) {
      // 检查权限是否已存在
      let permission = await db.Permission.findOne({
        where: { code: permissionData.code }
      });

      // 如果权限不存在，创建它
      if (!permission) {
        console.log(`创建权限: ${permissionData.name} (${permissionData.code})`);
        permission = await db.Permission.create(permissionData);
        console.log(`创建成功，权限ID: ${permission.id}`);
      } else {
        console.log(`权限已存在: ${permissionData.name} (${permissionData.code}), ID: ${permission.id}`);
      }

      // 检查角色是否已有该权限
      const hasPermission = await db.RolePermission.findOne({
        where: {
          role_id: visitorRole.id,
          permission_id: permission.id
        }
      });

      if (!hasPermission) {
        console.log(`为访问者角色分配权限: ${permissionData.name} (${permissionData.code})`);
        await db.RolePermission.create({
          role_id: visitorRole.id,
          permission_id: permission.id
        });
        console.log('权限分配成功');
      } else {
        console.log(`访问者角色已有权限: ${permissionData.name} (${permissionData.code})`);
      }
    }

    console.log('额外权限添加完成');
  } catch (error) {
    console.error('添加额外权限失败:', error);
  } finally {
    process.exit();
  }
})();
