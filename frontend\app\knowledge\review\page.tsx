"use client"

import { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { FileText, Check, X, Clock, Search, Filter, Eye, User, Calendar, RefreshCw, AlertCircle, Download } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select"
import Link from "next/link"

// 文件审核状态类型
type ReviewStatus = "pending" | "approved" | "rejected"

// 文件类型
interface ReviewFile {
  id: string
  name: string
  knowledgeBaseId: string
  knowledgeBaseName: string
  size: string
  type: string
  uploadedBy: string
  uploadedAt: string
  reviewStatus: ReviewStatus
  reviewedBy?: string
  reviewedAt?: string
  reviewComment?: string
}

export default function KnowledgeReviewPage() {
  const [activeTab, setActiveTab] = useState<ReviewStatus>("pending")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<string>("all")
  const [selectedFileType, setSelectedFileType] = useState<string>("all")
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [selectedFile, setSelectedFile] = useState<ReviewFile | null>(null)
  const [reviewComment, setReviewComment] = useState("")
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")

  // 从API获取知识库数据
  const [knowledgeBases, setKnowledgeBases] = useState<{ id: string, name: string, type: string }[]>([])
  const [pendingFiles, setPendingFiles] = useState<ReviewFile[]>([])
  const [approvedFiles, setApprovedFiles] = useState<ReviewFile[]>([])
  const [rejectedFiles, setRejectedFiles] = useState<ReviewFile[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [loadError, setLoadError] = useState<string | null>(null)

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 加载数据
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      setLoadError(null)

      try {
        // 获取所有知识库
        // 确保只在客户端环境中访问localStorage
        let token = '';
        if (typeof window !== 'undefined') {
          token = localStorage.getItem('hefamily_token') || '';
          if (!token) {
            throw new Error('未登录，请先登录');
          }
        } else {
          console.log('服务器端渲染环境，跳过认证检查');
        }

        const kbResponse = await fetch('/api/knowledge', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (!kbResponse.ok) throw new Error('获取知识库失败')
        const kbData = await kbResponse.json()

        // 检查API返回的数据结构
        console.log('知识库API返回数据:', kbData);

        // 确保数据结构正确
        if (!kbData.data || !kbData.data.knowledgeBases) {
          throw new Error('API返回的数据结构不正确');
        }

        const kbs = kbData.data.knowledgeBases.map((kb: any) => ({
          id: kb.id,
          name: kb.name,
          type: kb.type === 'system' ? '系统' : '用户'
        }))
        setKnowledgeBases(kbs)

        // 获取待审核文件
        const pendingResponse = await fetch('/api/files?status=pending', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        if (!pendingResponse.ok) throw new Error('获取待审核文件失败')
        const pendingData = await pendingResponse.json()

        // 检查API返回的数据结构
        console.log('待审核文件API返回数据:', pendingData);

        // 确保数据结构正确
        const pendingFiles = pendingData.data?.files || pendingData.files || [];

        const pendingFilesList = pendingFiles.map((file: any) => {
          const kb = kbs.find((k: any) => k.id === file.knowledge_base_id)
          return {
            id: file.id,
            name: file.original_name,
            knowledgeBaseId: file.knowledge_base_id,
            knowledgeBaseName: kb?.name || '未知知识库',
            size: formatFileSize(file.size),
            type: file.type.split('/')[1] || file.type,
            uploadedBy: file.uploader?.username || '未知用户',
            uploadedAt: new Date(file.created_at).toLocaleString(),
            reviewStatus: 'pending' as ReviewStatus
          }
        })
        setPendingFiles(pendingFilesList)

        // 获取已批准文件
        const approvedResponse = await fetch('/api/files?status=approved', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        if (!approvedResponse.ok) throw new Error('获取已批准文件失败')
        const approvedData = await approvedResponse.json()

        // 检查API返回的数据结构
        console.log('已批准文件API返回数据:', approvedData);

        // 确保数据结构正确
        const approvedFiles = approvedData.data?.files || approvedData.files || [];

        const approvedFilesList = approvedFiles.map((file: any) => {
          const kb = kbs.find((k: any) => k.id === file.knowledge_base_id)
          return {
            id: file.id,
            name: file.original_name,
            knowledgeBaseId: file.knowledge_base_id,
            knowledgeBaseName: kb?.name || '未知知识库',
            size: formatFileSize(file.size),
            type: file.type.split('/')[1] || file.type,
            uploadedBy: file.uploader?.username || '未知用户',
            uploadedAt: new Date(file.created_at).toLocaleString(),
            reviewStatus: 'approved' as ReviewStatus,
            reviewedBy: file.reviewer?.username || '系统管理员',
            reviewedAt: file.reviewed_at ? new Date(file.reviewed_at).toLocaleString() : undefined
          }
        })
        setApprovedFiles(approvedFilesList)

        // 获取已拒绝文件
        const rejectedResponse = await fetch('/api/files?status=rejected', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        if (!rejectedResponse.ok) throw new Error('获取已拒绝文件失败')
        const rejectedData = await rejectedResponse.json()

        // 检查API返回的数据结构
        console.log('已拒绝文件API返回数据:', rejectedData);

        // 确保数据结构正确
        const rejectedFiles = rejectedData.data?.files || rejectedData.files || [];

        const rejectedFilesList = rejectedFiles.map((file: any) => {
          const kb = kbs.find((k: any) => k.id === file.knowledge_base_id)
          return {
            id: file.id,
            name: file.original_name,
            knowledgeBaseId: file.knowledge_base_id,
            knowledgeBaseName: kb?.name || '未知知识库',
            size: formatFileSize(file.size),
            type: file.type.split('/')[1] || file.type,
            uploadedBy: file.uploader?.username || '未知用户',
            uploadedAt: new Date(file.created_at).toLocaleString(),
            reviewStatus: 'rejected' as ReviewStatus,
            reviewedBy: file.reviewer?.username || '系统管理员',
            reviewedAt: file.reviewed_at ? new Date(file.reviewed_at).toLocaleString() : undefined,
            reviewComment: file.review_comment
          }
        })
        setRejectedFiles(rejectedFilesList)
      } catch (error) {
        console.error('加载数据失败:', error)
        setLoadError(error instanceof Error ? error.message : '未知错误')
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  // 根据当前选择的标签获取文件列表
  const getFilesByStatus = (status: ReviewStatus) => {
    if (status === "pending") {
      return pendingFiles
    } else if (status === "approved") {
      return approvedFiles
    } else {
      return rejectedFiles
    }
  }

  // 过滤文件
  const filterFiles = (files: ReviewFile[]) => {
    return files.filter((file) => {
      // 搜索过滤
      const matchesSearch = !searchQuery || file.name.toLowerCase().includes(searchQuery.toLowerCase())

      // 知识库过滤
      const matchesKnowledgeBase = selectedKnowledgeBase === "all" || file.knowledgeBaseId === selectedKnowledgeBase

      // 文件类型过滤
      const matchesFileType = selectedFileType === "all" || file.type === selectedFileType

      return matchesSearch && matchesKnowledgeBase && matchesFileType
    })
  }

  // 获取文件图标
  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case "pdf":
        return <FileText className="text-red-500" />
      case "docx":
      case "doc":
        return <FileText className="text-blue-500" />
      case "xlsx":
      case "xls":
        return <FileText className="text-green-500" />
      case "pptx":
      case "ppt":
        return <FileText className="text-orange-500" />
      case "png":
      case "jpg":
      case "jpeg":
        return <FileText className="text-purple-500" />
      case "mp4":
      case "mov":
        return <FileText className="text-indigo-500" />
      case "zip":
      case "rar":
        return <FileText className="text-yellow-500" />
      default:
        return <FileText className="text-gray-500" />
    }
  }

  // 当前审核操作类型
  const [reviewAction, setReviewAction] = useState<"approve" | "reject" | null>(null)

  // 处理审核操作
  const handleReview = (file: ReviewFile, action: "approve" | "reject") => {
    setSelectedFile(file)
    setReviewComment("")
    setReviewAction(action) // 保存当前操作类型
    setShowReviewModal(true)
  }

  // 处理文件下载
  const handleDownload = async (file: ReviewFile) => {
    try {
      // 获取认证令牌
      const token = localStorage.getItem('hefamily_token') || '';
      if (!token) {
        throw new Error('未登录，请先登录');
      }

      // 发起下载请求
      const response = await fetch(`/api/files/${file.id}/download`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('下载文件失败');
      }

      // 获取文件blob
      const blob = await response.blob();

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.name;
      document.body.appendChild(a);
      a.click();

      // 清理
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // 显示成功消息
      setSuccessMessage(`文件 ${file.name} 开始下载`);
      setShowSuccessMessage(true);
      setTimeout(() => {
        setShowSuccessMessage(false);
      }, 3000);
    } catch (error) {
      console.error('下载文件失败:', error);
      alert('下载文件失败，请稍后再试');
    }
  }

  // 提交审核结果
  const handleSubmitReview = async (action: "approve" | "reject") => {
    if (selectedFile) {
      try {
        // 获取认证令牌
        // 确保只在客户端环境中访问localStorage
        let token = '';
        if (typeof window !== 'undefined') {
          token = localStorage.getItem('hefamily_token') || '';
          if (!token) {
            throw new Error('未登录，请先登录');
          }
        } else {
          console.log('服务器端渲染环境，跳过认证检查');
        }

        // 调用API更新文件状态
        const response = await fetch(`/api/files/${selectedFile.id}/review`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            status: action === "approve" ? "approved" : "rejected",
            comment: action === "reject" ? reviewComment : undefined
          })
        })

        if (!response.ok) {
          throw new Error('审核操作失败')
        }

        const result = await response.json()

        // 更新本地状态
        if (action === "approve") {
          // 从待审核列表中移除
          setPendingFiles(prev => prev.filter(file => file.id !== selectedFile.id))

          // 添加到已批准列表
          const approvedFile = {
            ...selectedFile,
            reviewStatus: "approved" as ReviewStatus,
            reviewedBy: "系统管理员",
            reviewedAt: new Date().toLocaleString()
          }
          setApprovedFiles(prev => [approvedFile, ...prev])
        } else {
          // 从待审核列表中移除
          setPendingFiles(prev => prev.filter(file => file.id !== selectedFile.id))

          // 添加到已拒绝列表
          const rejectedFile = {
            ...selectedFile,
            reviewStatus: "rejected" as ReviewStatus,
            reviewedBy: "系统管理员",
            reviewedAt: new Date().toLocaleString(),
            reviewComment: reviewComment
          }
          setRejectedFiles(prev => [rejectedFile, ...prev])
        }

        // 显示成功消息
        setSuccessMessage(
          `已${action === "approve" ? "批准" : "拒绝"}文件：${selectedFile.name}${
            action === "reject" && reviewComment ? `，原因：${reviewComment}` : ""
          }`,
        )
        setShowSuccessMessage(true)
        setTimeout(() => {
          setShowSuccessMessage(false)
        }, 3000)

        // 关闭模态框
        setShowReviewModal(false)
        setSelectedFile(null)
        setReviewAction(null) // 重置审核操作类型
      } catch (error) {
        console.error('审核操作失败:', error)
        alert('审核操作失败，请稍后再试')
      }
    }
  }

  // 获取当前标签的文件数量
  const getPendingCount = () => pendingFiles.length
  const getApprovedCount = () => approvedFiles.length
  const getRejectedCount = () => rejectedFiles.length

  // 获取所有文件类型
  const getAllFileTypes = () => {
    const types = new Set<string>()
    ;[...pendingFiles, ...approvedFiles, ...rejectedFiles].forEach((file) => {
      types.add(file.type)
    })
    return Array.from(types)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        {/* 页面标题 */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">知识库文件审核</h1>
            <p className="text-gray-500">管理上传到系统知识库的文件，确保内容质量和安全</p>
          </div>
          <Link href="/knowledge">
            <Button variant="outline">返回知识库</Button>
          </Link>
        </div>

        {/* 加载状态 */}
        {isLoading && (
          <div className="bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-6 rounded shadow-sm">
            <div className="flex items-center">
              <RefreshCw className="h-5 w-5 mr-2 text-blue-500 animate-spin" />
              <p>正在加载数据，请稍候...</p>
            </div>
          </div>
        )}

        {/* 错误提示 */}
        {loadError && (
          <div className="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded shadow-sm">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
              <p>加载数据失败: {loadError}</p>
            </div>
          </div>
        )}

        {/* 成功消息提示 */}
        {showSuccessMessage && (
          <div className="bg-green-50 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded shadow-sm animate-in slide-in-from-top duration-300">
            <div className="flex items-center">
              <Check className="h-5 w-5 mr-2 text-green-500" />
              <p>{successMessage}</p>
            </div>
          </div>
        )}

        {/* 标签页 */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as ReviewStatus)} className="w-full">
          <TabsList className="w-full grid grid-cols-3 rounded-none bg-gray-100 p-0 h-12">
            <TabsTrigger
              value="pending"
              className="rounded-none data-[state=active]:bg-white data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-amber-500 h-full"
            >
              <Clock className="h-4 w-4 mr-2 text-amber-500" />
              待审核
              <Badge className="ml-2 bg-amber-500">{getPendingCount()}</Badge>
            </TabsTrigger>
            <TabsTrigger
              value="approved"
              className="rounded-none data-[state=active]:bg-white data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-green-500 h-full"
            >
              <Check className="h-4 w-4 mr-2 text-green-500" />
              已批准
              <Badge className="ml-2 bg-green-500">{getApprovedCount()}</Badge>
            </TabsTrigger>
            <TabsTrigger
              value="rejected"
              className="rounded-none data-[state=active]:bg-white data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-red-500 h-full"
            >
              <X className="h-4 w-4 mr-2 text-red-500" />
              已拒绝
              <Badge className="ml-2 bg-red-500">{getRejectedCount()}</Badge>
            </TabsTrigger>
          </TabsList>

          {/* 搜索和筛选区域 */}
          <div className="p-4 bg-white border-b border-gray-200">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
              <div className="relative flex-grow">
                <input
                  type="text"
                  placeholder="搜索文件名称"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent bg-white shadow-sm"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              </div>

              <div className="flex gap-2">
                <div className="w-48">
                  <Select value={selectedKnowledgeBase} onValueChange={setSelectedKnowledgeBase}>
                    <SelectTrigger className="border-gray-300">
                      <div className="flex items-center">
                        <Filter className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="truncate">
                          {selectedKnowledgeBase === "all"
                            ? "所有知识库"
                            : knowledgeBases.find((kb) => kb.id === selectedKnowledgeBase)?.name || "选择知识库"}
                        </span>
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有知识库</SelectItem>
                      {knowledgeBases.map((kb) => (
                        <SelectItem key={kb.id} value={kb.id}>
                          {kb.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="w-36">
                  <Select value={selectedFileType} onValueChange={setSelectedFileType}>
                    <SelectTrigger className="border-gray-300">
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="truncate">
                          {selectedFileType === "all" ? "所有类型" : selectedFileType.toUpperCase()}
                        </span>
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有类型</SelectItem>
                      {getAllFileTypes().map((type) => (
                        <SelectItem key={type} value={type}>
                          {type.toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>

          {/* 待审核文件列表 */}
          <TabsContent value="pending" className="p-0 m-0">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      文件名
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      所属知识库
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      上传者
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      上传时间
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      文件大小
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filterFiles(getFilesByStatus("pending")).length > 0 ? (
                    filterFiles(getFilesByStatus("pending")).map((file) => (
                      <tr key={file.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-9 w-9 rounded-md bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0 shadow-sm border border-gray-200">
                              {getFileIcon(file.type)}
                            </div>
                            <div>
                              <div className="font-medium text-gray-800 truncate max-w-xs">{file.name}</div>
                              <div className="text-xs text-gray-500">{file.type.toUpperCase()}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.knowledgeBaseName}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.uploadedBy}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.uploadedAt}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.size}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              className="bg-[#1e7a43] hover:bg-[#1e7a43]/90 text-white"
                              onClick={() => handleReview(file, "approve")}
                            >
                              <Check className="h-4 w-4 mr-1" />
                              批准
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-red-600 text-red-600 hover:bg-red-50"
                              onClick={() => handleReview(file, "reject")}
                            >
                              <X className="h-4 w-4 mr-1" />
                              拒绝
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-blue-600 text-blue-600 hover:bg-blue-50"
                              onClick={() => handleDownload(file)}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              下载
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                        <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p className="text-lg font-medium mb-2">暂无待审核文件</p>
                        <p className="text-sm">当用户上传文件到系统知识库时，将在此处显示</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </TabsContent>

          {/* 已批准文件列表 */}
          <TabsContent value="approved" className="p-0 m-0">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      文件名
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      所属知识库
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      上传者
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      审核时间
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      审核人
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filterFiles(getFilesByStatus("approved")).length > 0 ? (
                    filterFiles(getFilesByStatus("approved")).map((file) => (
                      <tr key={file.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-9 w-9 rounded-md bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0 shadow-sm border border-gray-200">
                              {getFileIcon(file.type)}
                            </div>
                            <div>
                              <div className="font-medium text-gray-800 truncate max-w-xs">{file.name}</div>
                              <div className="text-xs text-gray-500">{file.type.toUpperCase()}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.knowledgeBaseName}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.uploadedBy}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.reviewedAt}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.reviewedBy}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline" className="border-gray-300">
                              <Eye className="h-4 w-4 mr-1" />
                              查看
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-blue-600 text-blue-600 hover:bg-blue-50"
                              onClick={() => handleDownload(file)}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              下载
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                        <Check className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p className="text-lg font-medium mb-2">暂无已批准文件</p>
                        <p className="text-sm">批准的文件将在此处显示</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </TabsContent>

          {/* 已拒绝文件列表 */}
          <TabsContent value="rejected" className="p-0 m-0">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      文件名
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      所属知识库
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      上传者
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      审核时间
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      拒绝原因
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filterFiles(getFilesByStatus("rejected")).length > 0 ? (
                    filterFiles(getFilesByStatus("rejected")).map((file) => (
                      <tr key={file.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-9 w-9 rounded-md bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0 shadow-sm border border-gray-200">
                              {getFileIcon(file.type)}
                            </div>
                            <div>
                              <div className="font-medium text-gray-800 truncate max-w-xs">{file.name}</div>
                              <div className="text-xs text-gray-500">{file.type.toUpperCase()}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.knowledgeBaseName}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.uploadedBy}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{file.reviewedAt}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="max-w-xs truncate text-sm text-red-600">{file.reviewComment}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline" className="border-gray-300">
                              <Eye className="h-4 w-4 mr-1" />
                              查看
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-blue-600 text-blue-600 hover:bg-blue-50"
                              onClick={() => handleDownload(file)}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              下载
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                        <X className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p className="text-lg font-medium mb-2">暂无已拒绝文件</p>
                        <p className="text-sm">拒绝的文件将在此处显示</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* 审核模态框 */}
      {showReviewModal && selectedFile && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-lg p-6 relative">
            <button
              onClick={() => {
                setShowReviewModal(false)
                setSelectedFile(null)
              }}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </button>

            <h2 className="text-xl font-bold mb-4">
              {selectedFile.reviewStatus === "pending"
                ? reviewAction === "approve"
                  ? "批准文件"
                  : reviewAction === "reject"
                    ? "拒绝文件"
                    : "审核文件"
                : "文件详情"}
            </h2>

            <div className="mb-6">
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-md bg-gray-100 flex items-center justify-center mr-4 flex-shrink-0 shadow-sm border border-gray-200">
                  {getFileIcon(selectedFile.type)}
                </div>
                <div>
                  <div className="font-medium text-lg">{selectedFile.name}</div>
                  <div className="text-sm text-gray-500">
                    {selectedFile.size} · {selectedFile.type.toUpperCase()}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-500 mb-1">所属知识库</p>
                  <p className="font-medium">{selectedFile.knowledgeBaseName}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">上传者</p>
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-500 mr-1" />
                    <span>{selectedFile.uploadedBy}</span>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">上传时间</p>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-500 mr-1" />
                    <span>{selectedFile.uploadedAt}</span>
                  </div>
                </div>
              </div>

              {selectedFile.reviewStatus === "pending" && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    审核意见
                    {reviewAction === "reject" && (
                      <span className="text-red-500 ml-1">(请填写拒绝原因，必填)</span>
                    )}
                    {reviewAction === "approve" && (
                      <span className="text-gray-400 ml-1">(可选)</span>
                    )}
                  </label>
                  <textarea
                    value={reviewComment}
                    onChange={(e) => setReviewComment(e.target.value)}
                    className={`w-full border ${
                      reviewAction === "reject" ? "border-red-300" : "border-gray-300"
                    } rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent h-24 resize-none`}
                    placeholder={reviewAction === "reject" ? "请输入拒绝原因..." : "请输入审核意见（可选）..."}
                  />
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowReviewModal(false)
                  setSelectedFile(null)
                  setReviewAction(null) // 重置审核操作类型
                }}
              >
                取消
              </Button>
              {selectedFile.reviewStatus === "pending" && reviewAction === "approve" && (
                <Button
                  className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                  onClick={() => handleSubmitReview("approve")}
                >
                  <Check className="h-4 w-4 mr-2" />
                  确认批准
                </Button>
              )}

              {selectedFile.reviewStatus === "pending" && reviewAction === "reject" && (
                <Button
                  variant="outline"
                  className="border-red-600 text-red-600 hover:bg-red-50"
                  onClick={() => handleSubmitReview("reject")}
                  disabled={reviewComment.trim() === ""}
                >
                  <X className="h-4 w-4 mr-2" />
                  确认拒绝
                </Button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
