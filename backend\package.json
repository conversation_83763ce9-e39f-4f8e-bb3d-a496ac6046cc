{"name": "hefamily-backend", "version": "1.0.0", "description": "和富家族研究平台后端服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:controllers": "node tests/run-tests.js controllers", "test:models": "node tests/run-tests.js models", "test:middlewares": "node tests/run-tests.js middlewares", "test:unit": "node tests/run-tests.js unit", "test:integration": "node tests/run-tests.js integration", "test:check-coverage": "node tests/check-coverage.js", "test:report": "node tests/generate-report.js", "test:custom-report": "node tests/generate-custom-report.js", "test:badges": "node tests/generate-badges.js", "db:create": "sequelize db:create", "db:migrate": "sequelize db:migrate", "db:seed": "sequelize db:seed:all", "db:reset": "sequelize db:drop && sequelize db:create && sequelize db:migrate && sequelize db:seed:all"}, "dependencies": {"express": "^4.18.2", "express-session": "^1.17.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.1", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "axios": "^1.4.0", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "sequelize-cli": "^6.6.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "jest-html-reporter": "^3.10.1", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "sqlite3": "^5.1.6"}}