/**
 * 添加Dify相关字段到files表
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 添加dify_task_id字段
    await queryInterface.addColumn('files', 'dify_task_id', {
      type: Sequelize.STRING(100),
      allowNull: true,
      comment: 'Dify分析任务ID'
    });

    // 添加analysis_status字段
    await queryInterface.addColumn('files', 'analysis_status', {
      type: Sequelize.ENUM('pending', 'processing', 'completed', 'failed'),
      allowNull: true,
      defaultValue: 'pending',
      comment: '分析状态：待分析、处理中、已完成、失败'
    });

    // 添加analysis_started_at字段
    await queryInterface.addColumn('files', 'analysis_started_at', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: '分析开始时间'
    });

    // 添加analysis_completed_at字段
    await queryInterface.addColumn('files', 'analysis_completed_at', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: '分析完成时间'
    });

    // 添加analysis_error字段
    await queryInterface.addColumn('files', 'analysis_error', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: '分析错误信息'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // 删除analysis_error字段
    await queryInterface.removeColumn('files', 'analysis_error');

    // 删除analysis_completed_at字段
    await queryInterface.removeColumn('files', 'analysis_completed_at');

    // 删除analysis_started_at字段
    await queryInterface.removeColumn('files', 'analysis_started_at');

    // 删除analysis_status字段
    await queryInterface.removeColumn('files', 'analysis_status');

    // 删除dify_task_id字段
    await queryInterface.removeColumn('files', 'dify_task_id');
  }
};
