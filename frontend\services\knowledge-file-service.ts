/**
 * 知识库文件服务
 *
 * 处理知识库文件的上传、查询、下载和删除等操作
 */

import apiService from './api-service'
import { PermissionError } from '../utils/error-handler'

// 知识库文件类型
export interface KnowledgeFile {
  id: string
  name: string
  original_name: string
  size: number
  type: string
  knowledge_base_id: string
  creator_id: string
  uploader_id?: string
  creator?: {
    id: string
    username: string
    email: string
  }
  uploader?: {
    id: string
    username: string
    email: string
  }
  status: 'pending' | 'approved' | 'rejected'
  summary?: string
  description?: string
  created_at: string
  updated_at: string
}

// 文件查询参数
export interface FileQueryParams {
  page?: number
  limit?: number
  search?: string
  type?: string
  status?: 'pending' | 'approved' | 'rejected'
  knowledge_base_id?: string
  size_min?: number
  size_max?: number
  knowledge_base_type?: string
}

// 文件上传参数
export interface UploadFileParams {
  file: File
  knowledge_base_id: string
  onProgress?: (progress: number) => void
}

// 文件审核参数
export interface ReviewFileParams {
  status: 'approved' | 'rejected'
  reason?: string
}

/**
 * 获取知识库文件列表
 * @param knowledgeBaseId 知识库ID
 * @param params 查询参数
 */
export const getKnowledgeBaseFiles = async (
  knowledgeBaseId: string,
  params?: FileQueryParams
): Promise<{
  files: KnowledgeFile[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}> => {
  // 添加knowledge_base_id参数
  const queryParams = { ...params, knowledge_base_id: knowledgeBaseId };

  return await apiService.get<{
    files: KnowledgeFile[]
    pagination: {
      total: number
      page: number
      limit: number
      totalPages: number
    }
  }>('/files/knowledge-base/' + knowledgeBaseId, queryParams)
}

/**
 * 获取所有文件列表
 * @param params 查询参数
 */
export const getAllFiles = async (params?: FileQueryParams): Promise<{
  files: KnowledgeFile[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}> => {
  return await apiService.get<{
    files: KnowledgeFile[]
    pagination: {
      total: number
      page: number
      limit: number
      totalPages: number
    }
  }>('/files', params)
}

/**
 * 获取待审核文件列表
 * @param params 查询参数
 */
export const getPendingFiles = async (params?: FileQueryParams): Promise<{
  files: KnowledgeFile[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}> => {
  // 添加status=pending参数
  const queryParams = { ...params, status: 'pending' };

  return await apiService.get<{
    files: KnowledgeFile[]
    pagination: {
      total: number
      page: number
      limit: number
      totalPages: number
    }
  }>('/files', queryParams)
}

/**
 * 获取文件详情
 * @param id 文件ID
 */
export const getFileById = async (id: string): Promise<KnowledgeFile> => {
  return await apiService.get<KnowledgeFile>(`/files/${id}`)
}

/**
 * 上传文件
 * @param params 上传参数
 * @param useDify 是否使用Dify分析文件，默认为true
 */
export const uploadFile = async (params: UploadFileParams, useDify: boolean = true): Promise<KnowledgeFile> => {
  const formData = new FormData()
  formData.append('file', params.file)
  formData.append('knowledge_base_id', params.knowledge_base_id)

  // 添加是否使用Dify分析的参数
  formData.append('use_dify', useDify ? 'true' : 'false')

  try {
    return await apiService.postFormData<KnowledgeFile>(`/files/upload/${params.knowledge_base_id}`, formData, {
      onUploadProgress: (progressEvent) => {
        if (params.onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          params.onProgress(progress)
        }
      }
    })
  } catch (error: any) {
    // 处理403权限错误
    if (error.response?.status === 403) {
      // 使用自定义的PermissionError类
      throw new PermissionError(
        error.response.data?.message || '您没有权限上传文件到该知识库',
        error.response
      )
    }

    // 其他错误直接抛出
    throw error
  }
}

/**
 * 删除文件
 * @param id 文件ID
 */
export const deleteFile = async (id: string): Promise<{ message: string }> => {
  return await apiService.del<{ message: string }>(`/files/${id}`)
}

/**
 * 审核文件
 * @param id 文件ID
 * @param params 审核参数
 */
export const reviewFile = async (id: string, params: ReviewFileParams): Promise<KnowledgeFile> => {
  return await apiService.put<KnowledgeFile>(`/files/${id}/review`, params)
}

/**
 * 下载文件
 * @param id 文件ID
 */
export const downloadFile = async (id: string): Promise<Blob> => {
  return await apiService.getBlob(`/files/${id}/download`)
}

/**
 * 获取文件预览URL
 * @param id 文件ID
 */
export const getFilePreviewUrl = (id: string): string => {
  return `${apiService.getBaseUrl()}/files/${id}/preview`
}

export default {
  getKnowledgeBaseFiles,
  getAllFiles,
  getPendingFiles,
  getFileById,
  uploadFile,
  deleteFile,
  reviewFile,
  downloadFile,
  getFilePreviewUrl
}
