/**
 * AI助手流式响应控制器
 *
 * 提供流式响应API，用于实现打字机效果
 */

const axios = require('axios');
const { AIAssistant } = require('../models');

/**
 * 流式查询AI助手
 *
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.streamQueryAssistant = async (req, res) => {
  try {
    // 设置SSE头部
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no'); // 禁用Nginx缓冲

    // 获取请求参数
    const { id } = req.params;
    const { query, conversation_id } = req.body;

    if (!query) {
      res.write(`data: ${JSON.stringify({ type: 'error', error: '查询内容不能为空' })}\n\n`);
      return res.end();
    }

    // 查找助手
    const assistant = await AIAssistant.findByPk(id);
    if (!assistant) {
      res.write(`data: ${JSON.stringify({ type: 'error', error: '未找到指定的AI助手' })}\n\n`);
      return res.end();
    }

    // 发送初始消息
    res.write(`data: ${JSON.stringify({ type: 'start', message: '正在处理您的请求...' })}\n\n`);

    // 构建API请求
    let apiUrl = assistant.api_endpoint;

    // 确保URL格式正确
    if (apiUrl.endsWith('/')) {
      apiUrl = apiUrl.slice(0, -1);
    }

    // 如果URL不包含chat-messages，添加它
    if (!apiUrl.includes('chat-messages')) {
      apiUrl = `${apiUrl}/chat-messages`;
    }

    // 构建请求体
    const apiRequestBody = {
      inputs: {
        // 添加助手名称作为上下文信息
        assistant_name: assistant.name
      },
      query,
      response_mode: 'streaming', // 使用流式模式
      conversation_id: conversation_id || null, // 确保conversation_id为null而不是undefined
      user: req.user ? req.user.id.toString() : 'anonymous'
    };

    // 记录请求信息
    console.log(`流式查询请求: 助手ID=${id}, 会话ID=${conversation_id || '新会话'}, 用户=${req.user ? req.user.id : 'anonymous'}`);


    // 添加app_id和app_code字段
    if (assistant.app_id) {
      apiRequestBody.app_id = assistant.app_id;
    }
    if (assistant.app_code) {
      apiRequestBody.app_code = assistant.app_code;
    }

    // 调用Dify API
    try {
      const response = await axios({
        method: 'post',
        url: apiUrl,
        data: apiRequestBody,
        headers: {
          'Authorization': `Bearer ${assistant.api_key}`,
          'Content-Type': 'application/json'
        },
        responseType: 'stream' // 重要：使用流式响应类型
      });

      // 处理流式响应
      let buffer = '';

      response.data.on('data', (chunk) => {
        // 将Buffer转换为字符串并添加到缓冲区
        buffer += chunk.toString();

        // 检查缓冲区是否包含完整的SSE消息
        while (buffer.includes('\n\n')) {
          const messageEndIndex = buffer.indexOf('\n\n');
          const message = buffer.substring(0, messageEndIndex);
          buffer = buffer.substring(messageEndIndex + 2);

          // 处理SSE消息
          if (message.startsWith('data:')) {
            try {
              const jsonStr = message.substring(5).trim();
              if (jsonStr === '[DONE]') {
                // 流结束
                res.write(`data: ${JSON.stringify({ type: 'done' })}\n\n`);
              } else {
                const data = JSON.parse(jsonStr);

                // 根据事件类型处理
                if (data.event === 'message') {
                  // 消息事件，提取文本内容
                  const content = data.message || data.content || data.text || data.answer || '';
                  if (content) {
                    res.write(`data: ${JSON.stringify({ type: 'token', content })}\n\n`);
                  }
                } else if (data.event === 'text_generation') {
                  // 文本生成事件，提取文本内容
                  const content = data.text || data.content || data.message || data.answer || '';
                  if (content) {
                    res.write(`data: ${JSON.stringify({ type: 'token', content })}\n\n`);
                  }
                } else if (data.event === 'message_end') {
                  // 消息结束事件
                  const finalAnswer = data.answer || data.text || data.message || data.content || '';
                  const conversationId = data.conversation_id || conversation_id;

                  // 记录会话ID
                  console.log(`流式响应结束: 会话ID=${conversationId || '未知'}`);

                  res.write(`data: ${JSON.stringify({
                    type: 'end',
                    content: finalAnswer,
                    conversation_id: conversationId
                  })}\n\n`);
                } else if (data.event === 'error') {
                  // 错误事件
                  res.write(`data: ${JSON.stringify({
                    type: 'error',
                    error: data.error || '处理请求时发生错误'
                  })}\n\n`);
                }
              }
            } catch (parseError) {
              console.error('解析SSE消息失败:', parseError);
            }
          }
        }
      });

      response.data.on('end', () => {
        // 如果还有未处理的缓冲区内容，尝试处理
        if (buffer.trim()) {
          try {
            if (buffer.startsWith('data:')) {
              const jsonStr = buffer.substring(5).trim();
              if (jsonStr && jsonStr !== '[DONE]') {
                const data = JSON.parse(jsonStr);
                if (data.event === 'message_end') {
                  const finalAnswer = data.answer || data.text || data.message || data.content || '';
                  const conversationId = data.conversation_id || conversation_id;
                  res.write(`data: ${JSON.stringify({
                    type: 'end',
                    content: finalAnswer,
                    conversation_id: conversationId
                  })}\n\n`);
                }
              }
            }
          } catch (e) {
            console.error('处理剩余缓冲区失败:', e);
          }
        }

        // 发送结束消息并关闭连接
        res.write(`data: ${JSON.stringify({ type: 'done' })}\n\n`);
        res.end();
      });

      response.data.on('error', (error) => {
        console.error('流式响应错误:', error);
        res.write(`data: ${JSON.stringify({ type: 'error', error: error.message })}\n\n`);
        res.end();
      });

    } catch (apiError) {
      console.error('API调用错误:', apiError);
      res.write(`data: ${JSON.stringify({ type: 'error', error: apiError.message })}\n\n`);
      res.end();
    }

  } catch (error) {
    console.error('流式查询处理错误:', error);
    res.write(`data: ${JSON.stringify({ type: 'error', error: error.message })}\n\n`);
    res.end();
  }
};

/**
 * 流式查询数据查询助手
 *
 * 提供数据查询助手的流式响应API，用于实现打字机效果
 *
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.streamQueryDataAssistant = async (req, res) => {
  try {
    // 设置SSE头部
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no'); // 禁用Nginx缓冲

    // 获取请求参数
    const { id } = req.params;
    const {
      query,
      conversation_id,
      knowledge_base_ids,
      api_key,
      api_endpoint,
      app_id,
      app_code
    } = req.body;

    if (!query) {
      res.write(`data: ${JSON.stringify({ type: 'error', error: '查询内容不能为空' })}\n\n`);
      return res.end();
    }

    // 发送初始消息
    res.write(`data: ${JSON.stringify({ type: 'start', message: '正在处理您的请求...' })}\n\n`);

    // 优先使用前端传递的API配置信息
    let assistantApiKey = api_key;
    let assistantApiEndpoint = api_endpoint;
    let assistantAppId = app_id;
    let assistantAppCode = app_code;

    // 如果前端没有传递API配置信息，则从数据库中查询
    if (!assistantApiKey || !assistantApiEndpoint) {
      console.log('数据查询助手流式查询 - 前端未提供完整API配置，尝试从数据库查询');

      // 查询条件
      const whereCondition = {
        type: 'data-query',
        status: 'active'
      };

      // 如果提供了助手ID，则查询特定ID的助手
      if (id) {
        whereCondition.id = id;
      }

      // 查询数据查询助手
      const assistant = await AIAssistant.findOne({
        where: whereCondition
      });

      if (!assistant) {
        res.write(`data: ${JSON.stringify({ type: 'error', error: '数据查询助手不存在或未激活' })}\n\n`);
        return res.end();
      }

      // 使用数据库中的配置
      assistantApiKey = assistant.api_key;
      assistantApiEndpoint = assistant.api_endpoint;
      assistantAppId = assistant.app_id;
      assistantAppCode = assistant.app_code;
    }

    // 验证API配置是否完整
    if (!assistantApiKey || !assistantApiEndpoint) {
      res.write(`data: ${JSON.stringify({ type: 'error', error: 'API配置不完整，请联系管理员配置数据查询助手' })}\n\n`);
      return res.end();
    }

    // 准备API URL
    let apiUrl = assistantApiEndpoint;

    // 确保URL格式正确
    if (apiUrl.endsWith('/')) {
      apiUrl = apiUrl.slice(0, -1);
    }

    // 如果URL不包含chat-messages，添加它
    if (!apiUrl.includes('chat-messages')) {
      apiUrl = `${apiUrl}/chat-messages`;
    }

    // 从会话存储中获取知识库ID
    const knowledgeBaseId = req.session?.dataAssistant?.knowledgeBaseId;

    // 构建请求体
    const apiRequestBody = {
      inputs: {
        // 如果有知识库ID，添加到请求中
        knowledge_base_id: knowledgeBaseId
      },
      query,
      response_mode: 'streaming', // 使用流式模式
      conversation_id: conversation_id || null, // 确保conversation_id为null而不是undefined
      user: req.user ? req.user.id.toString() : 'anonymous'
    };

    // 记录请求信息
    console.log(`数据查询助手流式查询 - 请求: 会话ID=${conversation_id || '新会话'}, 用户=${req.user ? req.user.id : 'anonymous'}`);

    // 添加app_id和app_code字段
    if (assistantAppId) {
      apiRequestBody.app_id = assistantAppId;
    }
    if (assistantAppCode) {
      apiRequestBody.app_code = assistantAppCode;
    }

    // 如果前端传递了知识库IDs，添加到请求中
    if (knowledge_base_ids && knowledge_base_ids.length > 0) {
      apiRequestBody.inputs.knowledge_base_ids = knowledge_base_ids;
    }

    console.log('数据查询助手流式查询 - API请求体:', JSON.stringify(apiRequestBody, null, 2));
    console.log('数据查询助手流式查询 - API URL:', apiUrl);
    console.log('数据查询助手流式查询 - API密钥前10个字符:', assistantApiKey.substring(0, 10) + '...');

    // 调用Dify API
    try {
      console.log('数据查询助手流式查询 - 开始调用Dify API');

      const response = await axios({
        method: 'post',
        url: apiUrl,
        data: apiRequestBody,
        headers: {
          'Authorization': `Bearer ${assistantApiKey}`,
          'Content-Type': 'application/json'
        },
        responseType: 'stream' // 重要：使用流式响应类型
      });

      console.log('数据查询助手流式查询 - Dify API调用成功，开始处理流式响应');

      // 处理流式响应
      let buffer = '';

      response.data.on('data', (chunk) => {
        // 将Buffer转换为字符串并添加到缓冲区
        const chunkStr = chunk.toString();
        buffer += chunkStr;

        console.log('数据查询助手流式查询 - 收到数据块，长度:', chunkStr.length);

        // 检查缓冲区是否包含完整的SSE消息
        while (buffer.includes('\n\n')) {
          const messageEndIndex = buffer.indexOf('\n\n');
          const message = buffer.substring(0, messageEndIndex);
          buffer = buffer.substring(messageEndIndex + 2);

          // 处理SSE消息
          if (message.startsWith('data:')) {
            try {
              const jsonStr = message.substring(5).trim();
              if (jsonStr === '[DONE]') {
                // 流结束
                res.write(`data: ${JSON.stringify({ type: 'done' })}\n\n`);
              } else {
                const data = JSON.parse(jsonStr);

                // 根据事件类型处理
                if (data.event === 'message') {
                  // 消息事件，提取文本内容
                  const content = data.message || data.content || data.text || data.answer || '';
                  if (content) {
                    console.log('数据查询助手流式查询 - 发送token事件，内容长度:', content.length);
                    res.write(`data: ${JSON.stringify({ type: 'token', content })}\n\n`);
                  } else {
                    console.log('数据查询助手流式查询 - 消息事件没有内容');
                  }
                } else if (data.event === 'text_generation') {
                  // 文本生成事件，提取文本内容
                  const content = data.text || data.content || data.message || data.answer || '';
                  if (content) {
                    console.log('数据查询助手流式查询 - 发送text_generation事件，内容长度:', content.length);
                    res.write(`data: ${JSON.stringify({ type: 'token', content })}\n\n`);
                  } else {
                    console.log('数据查询助手流式查询 - text_generation事件没有内容');
                  }
                } else if (data.event === 'message_end') {
                  // 消息结束事件
                  const finalAnswer = data.answer || data.text || data.message || data.content || '';
                  const conversationId = data.conversation_id || conversation_id;

                  // 记录会话ID和最终答案
                  console.log(`数据查询助手流式响应结束: 会话ID=${conversationId || '未知'}`);
                  console.log('数据查询助手流式响应 - 最终答案长度:', finalAnswer.length);
                  console.log('数据查询助手流式响应 - 最终答案前100个字符:', finalAnswer.substring(0, 100));

                  // 发送end事件
                  const endEventData = JSON.stringify({
                    type: 'end',
                    content: finalAnswer,
                    conversation_id: conversationId
                  });
                  console.log('数据查询助手流式响应 - 发送end事件，数据长度:', endEventData.length);

                  res.write(`data: ${endEventData}\n\n`);
                } else if (data.event === 'error') {
                  // 错误事件
                  res.write(`data: ${JSON.stringify({
                    type: 'error',
                    error: data.error || '处理请求时发生错误'
                  })}\n\n`);
                }
              }
            } catch (parseError) {
              console.error('解析SSE消息失败:', parseError);
            }
          }
        }
      });

      response.data.on('end', () => {
        // 如果还有未处理的缓冲区内容，尝试处理
        if (buffer.trim()) {
          try {
            if (buffer.startsWith('data:')) {
              const jsonStr = buffer.substring(5).trim();
              if (jsonStr && jsonStr !== '[DONE]') {
                const data = JSON.parse(jsonStr);
                if (data.event === 'message_end') {
                  const finalAnswer = data.answer || data.text || data.message || data.content || '';
                  const conversationId = data.conversation_id || conversation_id;
                  res.write(`data: ${JSON.stringify({
                    type: 'end',
                    content: finalAnswer,
                    conversation_id: conversationId
                  })}\n\n`);
                }
              }
            }
          } catch (e) {
            console.error('处理剩余缓冲区失败:', e);
          }
        }

        // 发送结束消息并关闭连接
        res.write(`data: ${JSON.stringify({ type: 'done' })}\n\n`);
        res.end();
      });

      response.data.on('error', (error) => {
        console.error('数据查询助手流式响应错误:', error);
        res.write(`data: ${JSON.stringify({ type: 'error', error: error.message })}\n\n`);
        res.end();
      });

    } catch (apiError) {
      console.error('数据查询助手API调用错误:', apiError);
      res.write(`data: ${JSON.stringify({ type: 'error', error: apiError.message })}\n\n`);
      res.end();
    }

  } catch (error) {
    console.error('数据查询助手流式查询处理错误:', error);
    res.write(`data: ${JSON.stringify({ type: 'error', error: error.message })}\n\n`);
    res.end();
  }
};
