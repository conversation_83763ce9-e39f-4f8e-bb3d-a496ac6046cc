# 1Panel配置指南 - 和富家族研究平台

本指南详细说明如何在1Panel中配置SSL证书和反向代理，以支持 `hefuf.com` 域名访问。

## 架构说明

```
用户 → 1Panel (80/443端口) → Nginx (99端口) → 前端(3000端口) / 后端(5001端口)
```

- **1Panel**: 处理SSL证书、域名解析和外部访问
- **Nginx**: 内部反向代理，将请求分发到前端和后端服务
- **前端**: Next.js应用 (端口3000)
- **后端**: Express API服务 (端口5001)

## 1Panel配置步骤

### 第一步：添加网站

1. 登录1Panel管理界面
2. 进入 **网站** → **网站** → **创建网站**
3. 填写网站信息：
   - **域名**: `hefuf.com`
   - **别名**: `www.hefuf.com`
   - **网站类型**: 反向代理
   - **代理地址**: `http://127.0.0.1:99`

### 第二步：配置SSL证书

1. 在网站列表中找到 `hefuf.com`
2. 点击 **SSL** 按钮
3. 选择证书获取方式：

#### 选项A：Let's Encrypt免费证书（推荐）
- 选择 **Let's Encrypt**
- 域名: `hefuf.com,www.hefuf.com`
- 邮箱: 填写您的邮箱地址
- 点击 **获取证书**

#### 选项B：上传自有证书
- 选择 **上传证书**
- 上传证书文件(.crt)和私钥文件(.key)

### 第三步：配置反向代理规则

1. 点击网站的 **配置** 按钮
2. 进入 **反向代理** 标签
3. 配置代理规则：

```nginx
# 主要代理规则
location / {
    proxy_pass http://127.0.0.1:99;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_cache_bypass $http_upgrade;
    
    # 超时设置
    proxy_connect_timeout 600s;
    proxy_send_timeout 600s;
    proxy_read_timeout 600s;
    
    # 文件上传大小限制
    client_max_body_size 1024M;
}
```

### 第四步：配置防火墙

1. 进入 **主机** → **防火墙**
2. 添加规则：
   - 端口 **80** (HTTP)
   - 端口 **443** (HTTPS)
   - 端口 **99** (内部nginx)
   - 端口 **3000** (前端服务)
   - 端口 **5001** (后端API)

### 第五步：配置域名解析

确保域名DNS记录正确指向服务器：
```
A记录: hefuf.com → *************
A记录: www.hefuf.com → *************
```

## 部署步骤

### 1. 部署nginx配置

```bash
# 复制nginx配置文件
sudo cp hefamily.conf /etc/nginx/conf.d/hefamily.conf

# 测试nginx配置
sudo nginx -t

# 重启nginx
sudo systemctl restart nginx
```

### 2. 启动应用服务

```bash
# 停止现有服务
pm2 stop all
pm2 delete all

# 启动后端服务
cd /path/to/your/project/backend
PORT=5001 pm2 start src/app.js --name "hefamily-backend"

# 启动前端服务
cd /path/to/your/project/frontend
pm2 start npm --name "hefamily-frontend" -- start

# 查看服务状态
pm2 status
```

### 3. 测试访问

```bash
# 测试内部nginx
curl -I http://localhost:99

# 测试1Panel代理
curl -I https://hefuf.com

# 测试API访问
curl https://hefuf.com/api/
```

## 故障排除

### 1. SSL证书问题
- 检查域名DNS解析是否正确
- 确保防火墙开放80和443端口
- 查看1Panel日志: `/opt/1panel/log/`

### 2. 反向代理问题
- 检查nginx是否正常运行: `systemctl status nginx`
- 查看nginx错误日志: `tail -f /var/log/nginx/error.log`
- 确保端口99没有被其他服务占用: `netstat -tlnp | grep :99`

### 3. 应用服务问题
- 检查pm2服务状态: `pm2 status`
- 查看应用日志: `pm2 logs`
- 确保前端和后端服务正常启动

### 4. CORS问题
- 检查后端CORS配置是否包含正确的域名
- 查看浏览器开发者工具的网络面板
- 确认请求头中包含正确的Origin

## 访问地址

配置完成后，您可以通过以下地址访问：

- **前端**: https://hefuf.com
- **后端API**: https://hefuf.com/api
- **文件上传**: https://hefuf.com/uploads

## 注意事项

1. **端口冲突**: 确保1Panel使用80/443端口，nginx使用99端口
2. **SSL证书**: 建议使用Let's Encrypt自动续期
3. **防火墙**: 只开放必要的端口，关闭不需要的端口
4. **日志监控**: 定期检查1Panel和nginx日志
5. **备份**: 定期备份SSL证书和配置文件
