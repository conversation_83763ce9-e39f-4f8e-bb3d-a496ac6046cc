/**
 * 知识库模型测试
 *
 * 测试知识库模型的字段、验证和关联
 */

const { KnowledgeBase, User, File } = require('../../src/models');

describe('知识库模型', () => {
  let testUser;
  let testKnowledgeBase;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试用户，使用时间戳确保用户名唯一
    const timestamp = Date.now();
    testUser = await User.create({
      username: `kbmodeluser_${timestamp}`,
      password: 'Password123!',
      email: `kbmodeluser_${timestamp}@example.com`,
      phone: `138${timestamp.toString().slice(-8)}`,
      role: 'basic_user',
      is_active: true
    });

    // 创建测试知识库，使用时间戳确保名称唯一
    testKnowledgeBase = await KnowledgeBase.create({
      name: `Test Model Knowledge Base ${Date.now()}`,
      description: 'Test knowledge base for model testing',
      type: 'user',
      creator_id: testUser.id
    });
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    try {
      if (testKnowledgeBase && testKnowledgeBase.id) {
        await KnowledgeBase.destroy({ where: { id: testKnowledgeBase.id } });
      }
      if (testUser && testUser.id) {
        await User.destroy({ where: { id: testUser.id } });
      }
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  });

  // 测试知识库创建
  describe('知识库创建', () => {
    test('应该成功创建知识库', async () => {
      const knowledgeBase = await KnowledgeBase.findByPk(testKnowledgeBase.id);

      expect(knowledgeBase).toBeDefined();
      expect(knowledgeBase.name).toContain('Test Model Knowledge Base');
      expect(knowledgeBase.description).toBe('Test knowledge base for model testing');
      expect(knowledgeBase.type).toBe('user');
      expect(knowledgeBase.creator_id).toBe(testUser.id);
    });

    test('不应该创建重复名称的知识库', async () => {
      try {
        await KnowledgeBase.create({
          name: testKnowledgeBase.name, // 重复的名称
          description: 'Another test knowledge base',
          type: 'user',
          creator_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建没有名称的知识库', async () => {
      try {
        await KnowledgeBase.create({
          // 缺少名称
          description: 'Knowledge base without name',
          type: 'user',
          creator_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建无效类型的知识库', async () => {
      try {
        await KnowledgeBase.create({
          name: 'Invalid Type Knowledge Base',
          description: 'Knowledge base with invalid type',
          type: 'invalid_type', // 无效的类型
          creator_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  // 测试知识库更新
  describe('知识库更新', () => {
    test('应该成功更新知识库信息', async () => {
      await testKnowledgeBase.update({
        name: 'Updated Knowledge Base',
        description: 'Updated description'
      });

      const updatedKnowledgeBase = await KnowledgeBase.findByPk(testKnowledgeBase.id);
      expect(updatedKnowledgeBase.name).toBe('Updated Knowledge Base');
      expect(updatedKnowledgeBase.description).toBe('Updated description');

      // 恢复原始数据
      await testKnowledgeBase.update({
        name: testKnowledgeBase.name,
        description: 'Test knowledge base for model testing'
      });
    });
  });

  // 测试知识库关联
  describe('知识库关联', () => {
    test('知识库应该关联到创建者', async () => {
      // 检查知识库模型是否有创建者关联
      expect(KnowledgeBase.associations).toHaveProperty('creator');

      // 获取知识库的创建者
      const knowledgeBase = await KnowledgeBase.findByPk(testKnowledgeBase.id, {
        include: ['creator']
      });

      expect(knowledgeBase.creator).toBeDefined();
      expect(knowledgeBase.creator.id).toBe(testUser.id);
      expect(knowledgeBase.creator.username).toContain('kbmodeluser');
    });

    test('知识库应该关联到文件', async () => {
      // 检查知识库模型是否有文件关联
      expect(KnowledgeBase.associations).toHaveProperty('files');

      // 创建测试文件
      const testFile = await File.create({
        name: 'test-model-file.pdf',
        original_name: 'test-model-file.pdf',
        path: 'uploads/test-model-file.pdf',
        type: 'pdf',
        mime_type: 'application/pdf',
        size: 1024,
        knowledge_base_id: testKnowledgeBase.id,
        uploader_id: testUser.id,
        status: 'approved',
        summary: 'Test file summary',
        detailed_description: 'Test file detailed description'
      });

      // 获取知识库的文件
      const knowledgeBase = await KnowledgeBase.findByPk(testKnowledgeBase.id, {
        include: ['files']
      });

      expect(knowledgeBase.files).toBeDefined();
      expect(knowledgeBase.files.length).toBeGreaterThan(0);
      expect(knowledgeBase.files[0].id).toBe(testFile.id);
      expect(knowledgeBase.files[0].name).toBe('test-model-file.pdf');

      // 清理测试文件
      await testFile.destroy();
    });
  });

  // 测试知识库实例方法
  describe('知识库实例方法', () => {
    test('getFileCount方法应该返回正确的文件数量', async () => {
      // 假设知识库模型有getFileCount方法
      if (typeof testKnowledgeBase.getFileCount === 'function') {
        // 创建多个测试文件
        await File.bulkCreate([
          {
            name: 'test-file-1.pdf',
            original_name: 'test-file-1.pdf',
            path: 'uploads/test-file-1.pdf',
            type: 'pdf',
            mime_type: 'application/pdf',
            size: 1024,
            knowledge_base_id: testKnowledgeBase.id,
            uploader_id: testUser.id,
            status: 'approved',
            summary: 'Test file 1 summary'
          },
          {
            name: 'test-file-2.pdf',
            original_name: 'test-file-2.pdf',
            path: 'uploads/test-file-2.pdf',
            type: 'pdf',
            mime_type: 'application/pdf',
            size: 1024,
            knowledge_base_id: testKnowledgeBase.id,
            uploader_id: testUser.id,
            status: 'approved',
            summary: 'Test file 2 summary'
          }
        ]);

        const fileCount = await testKnowledgeBase.getFileCount();
        expect(fileCount).toBeGreaterThanOrEqual(2);

        // 清理测试文件
        await File.destroy({
          where: {
            name: ['test-file-1.pdf', 'test-file-2.pdf']
          }
        });
      } else {
        // 如果没有该方法，跳过测试
        console.log('知识库模型没有getFileCount方法，跳过测试');
      }
    });
  });

  // 测试知识库类方法
  describe('知识库类方法', () => {
    test('findByType方法应该返回正确的知识库列表', async () => {
      // 假设知识库模型有findByType静态方法
      if (typeof KnowledgeBase.findByType === 'function') {
        const userKnowledgeBases = await KnowledgeBase.findByType('user');
        expect(Array.isArray(userKnowledgeBases)).toBe(true);
        expect(userKnowledgeBases.length).toBeGreaterThan(0);

        const systemKnowledgeBases = await KnowledgeBase.findByType('system');
        expect(Array.isArray(systemKnowledgeBases)).toBe(true);
      } else {
        // 如果没有该方法，跳过测试
        console.log('知识库模型没有findByType方法，跳过测试');
      }
    });
  });
});
