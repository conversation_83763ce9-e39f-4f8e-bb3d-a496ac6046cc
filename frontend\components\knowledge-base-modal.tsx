"use client"
import { createPortal } from "react-dom"
import { Check, Database, X, CheckSquare } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface KnowledgeBase {
  id: string
  name: string
  type: "系统" | "用户"
}

interface KnowledgeBaseModalProps {
  isOpen: boolean
  onClose: () => void
  knowledgeBases: KnowledgeBase[]
  selectedIds: string[]
  onChange: (selectedIds: string[]) => void
}

export function KnowledgeBaseModal({
  isOpen,
  onClose,
  knowledgeBases,
  selectedIds,
  onChange,
}: KnowledgeBaseModalProps) {
  // Group knowledge bases by type
  const systemKnowledgeBases = knowledgeBases.filter((kb) => kb.type === "系统")
  const userKnowledgeBases = knowledgeBases.filter((kb) => kb.type === "用户")

  // Toggle selection of a knowledge base
  const toggleSelection = (id: string) => {
    if (selectedIds.includes(id)) {
      onChange(selectedIds.filter((selectedId) => selectedId !== id))
    } else {
      onChange([...selectedIds, id])
    }
  }

  // Function to select all system knowledge bases
  const selectAllSystem = () => {
    const systemIds = systemKnowledgeBases.map((kb) => kb.id)
    const currentUserSelected = selectedIds.filter((id) => userKnowledgeBases.some((kb) => kb.id === id))
    onChange([...currentUserSelected, ...systemIds])
  }

  // Function to select all user knowledge bases
  const selectAllUser = () => {
    const userIds = userKnowledgeBases.map((kb) => kb.id)
    const currentSystemSelected = selectedIds.filter((id) => systemKnowledgeBases.some((kb) => kb.id === id))
    onChange([...currentSystemSelected, ...userIds])
  }

  if (!isOpen) return null

  // 使用Portal渲染到body
  return typeof window === "object"
    ? createPortal(
        <div className="fixed inset-0 z-[99999] flex items-center justify-center">
          <div className="fixed inset-0 bg-black/70" onClick={onClose} style={{ backdropFilter: "blur(4px)" }}></div>
          <div className="bg-white rounded-lg w-full max-w-md p-4 relative max-h-[80vh] overflow-y-auto mx-4 z-[100000]">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium">选择知识库</h2>
              <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="mb-4">
              <div className="text-sm text-gray-500 mb-2">已选择 {selectedIds.length} 个知识库</div>
            </div>

            <div className="space-y-4">
              {/* System knowledge bases */}
              {systemKnowledgeBases.length > 0 && (
                <div>
                  <div className="flex justify-between items-center px-2 py-1 bg-gray-50 rounded-sm mb-2">
                    <div className="text-xs font-semibold text-gray-500">系统知识库</div>
                    <button
                      onClick={selectAllSystem}
                      className="flex items-center text-xs text-blue-600 hover:text-blue-800"
                    >
                      <CheckSquare className="h-3 w-3 mr-1" />
                      全选
                    </button>
                  </div>
                  <div className="space-y-1">
                    {systemKnowledgeBases.map((kb) => (
                      <div
                        key={kb.id}
                        className={cn(
                          "flex items-center px-2 py-2 text-sm rounded-sm cursor-pointer",
                          selectedIds.includes(kb.id) ? "bg-emerald-50" : "hover:bg-gray-50",
                        )}
                        onClick={() => toggleSelection(kb.id)}
                      >
                        <div
                          className={cn(
                            "mr-2 h-4 w-4 rounded-sm border flex items-center justify-center",
                            selectedIds.includes(kb.id)
                              ? "border-emerald-500 bg-emerald-500 text-white"
                              : "border-gray-300",
                          )}
                        >
                          {selectedIds.includes(kb.id) && <Check className="h-3 w-3" />}
                        </div>
                        <div className="flex items-center">
                          <Database className="h-3 w-3 mr-2 text-blue-500" />
                          <span className="text-sm">{kb.name}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* User knowledge bases */}
              {userKnowledgeBases.length > 0 && (
                <div>
                  <div className="flex justify-between items-center px-2 py-1 bg-gray-50 rounded-sm mb-2">
                    <div className="text-xs font-semibold text-gray-500">用户知识库</div>
                    <button
                      onClick={selectAllUser}
                      className="flex items-center text-xs text-blue-600 hover:text-blue-800"
                    >
                      <CheckSquare className="h-3 w-3 mr-1" />
                      全选
                    </button>
                  </div>
                  <div className="space-y-1">
                    {userKnowledgeBases.map((kb) => (
                      <div
                        key={kb.id}
                        className={cn(
                          "flex items-center px-2 py-2 text-sm rounded-sm cursor-pointer",
                          selectedIds.includes(kb.id) ? "bg-emerald-50" : "hover:bg-gray-50",
                        )}
                        onClick={() => toggleSelection(kb.id)}
                      >
                        <div
                          className={cn(
                            "mr-2 h-4 w-4 rounded-sm border flex items-center justify-center",
                            selectedIds.includes(kb.id)
                              ? "border-emerald-500 bg-emerald-500 text-white"
                              : "border-gray-300",
                          )}
                        >
                          {selectedIds.includes(kb.id) && <Check className="h-3 w-3" />}
                        </div>
                        <div className="flex items-center">
                          <Database className="h-3 w-3 mr-2 text-green-500" />
                          <span className="text-sm">{kb.name}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end mt-6 space-x-2">
              <Button variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button onClick={onClose} className="bg-[#1e7a43] hover:bg-[#1e7a43]/90">
                确定
              </Button>
            </div>
          </div>
        </div>,
        document.body,
      )
    : null
}
