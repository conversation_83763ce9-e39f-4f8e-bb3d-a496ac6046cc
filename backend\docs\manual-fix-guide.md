# 角色问题手动修复指南

由于我们在执行自动修复脚本时遇到了一些技术问题，本文档提供了手动修复系统中角色相关问题的详细步骤。

## 问题概述

系统中存在以下角色相关问题：

1. **多余的测试角色**：系统中存在多个测试角色（test_admin_role_*），这些角色无法删除。
2. **多个管理员角色**：系统中存在多个管理员角色，权限不一致。
3. **权限检查机制混乱**：admin账号的权限来源是双重机制（硬编码和数据库角色关联）。

## 手动修复步骤

我们提供了SQL脚本 `fix-roles.sql` 来帮助您手动修复这些问题。请按照以下步骤操作：

### 1. 备份数据库

在执行任何修改之前，请务必备份您的数据库。

### 2. 查看当前角色情况

执行SQL脚本中的查询语句，了解当前角色情况：

```sql
-- 查看所有角色
SELECT id, name, description, is_system, created_at, updated_at FROM roles;

-- 查看admin用户使用的角色
SELECT u.id, u.username, u.role, u.role_id, r.name as role_name, r.is_system
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
WHERE u.username = 'admin';
```

### 3. 确保系统角色被正确标记

执行以下SQL语句，确保管理员和访问者角色被标记为系统角色：

```sql
-- 确保管理员角色被标记为系统角色
UPDATE roles
SET is_system = 1
WHERE name = '管理员' OR name = 'admin';

-- 确保访问者角色被标记为系统角色
UPDATE roles
SET is_system = 1
WHERE name = '访问者' OR name = '初级访问者' OR name = 'basic_user';
```

### 4. 处理测试角色

1. 查看使用测试角色的用户：
   ```sql
   SELECT u.id, u.username, u.role, u.role_id, r.name as role_name
   FROM users u
   JOIN roles r ON u.role_id = r.id
   WHERE r.name LIKE 'test_%' OR r.name LIKE '%_test' OR r.name LIKE '%_role_%';
   ```

2. 查找访问者角色ID：
   ```sql
   SELECT id FROM roles WHERE name = '访问者' OR name = '初级访问者' OR name = 'basic_user' LIMIT 1;
   ```

3. 将使用测试角色的用户迁移到访问者角色（替换 {访问者角色ID} 为实际ID）：
   ```sql
   UPDATE users
   SET role_id = {访问者角色ID}
   WHERE role_id IN (SELECT id FROM roles WHERE name LIKE 'test_%' OR name LIKE '%_test' OR name LIKE '%_role_%');
   ```

4. 删除测试角色：
   ```sql
   DELETE FROM roles
   WHERE name LIKE 'test_%' OR name LIKE '%_test' OR name LIKE '%_role_%';
   ```

### 5. 确保admin用户使用正确的管理员角色

1. 查找管理员角色ID：
   ```sql
   SELECT id FROM roles WHERE name = '管理员' OR name = 'admin' LIMIT 1;
   ```

2. 更新admin用户的角色ID（替换 {管理员角色ID} 为实际ID）：
   ```sql
   UPDATE users
   SET role_id = {管理员角色ID}
   WHERE username = 'admin';
   ```

### 6. 验证修复结果

执行以下SQL语句，确认修复结果：

```sql
-- 查看所有角色
SELECT id, name, description, is_system, created_at, updated_at FROM roles;

-- 查看admin用户使用的角色
SELECT u.id, u.username, u.role, u.role_id, r.name as role_name, r.is_system
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
WHERE u.username = 'admin';
```

## 改进权限检查机制

为了解决权限检查机制混乱的问题，我们提供了改进的权限中间件 `improvedPermissionMiddleware.js`。您需要手动将其集成到您的应用程序中：

1. 查看 `src/app.js` 或您的主应用程序文件，找到权限中间件的导入部分。
2. 将原有的权限中间件导入替换为改进的权限中间件：
   ```javascript
   // 替换这一行
   const { checkPermission, isAdmin } = require('./middlewares/authMiddleware');
   
   // 使用这一行
   const { checkPermission, isAdmin, hasPermission } = require('./middlewares/improvedPermissionMiddleware');
   ```

3. 在所有使用硬编码权限检查的地方，替换为基于角色的权限检查。

## 注意事项

- 在执行SQL语句之前，请务必备份数据库。
- 请仔细检查每个SQL语句，确保它们适用于您的系统。
- 如果您的系统有自定义的权限检查逻辑，可能需要额外的修改。

## 故障排除

如果您在执行SQL语句时遇到问题，请检查以下内容：

1. 确保数据库连接正常。
2. 检查SQL语句的语法是否正确。
3. 确保您有足够的权限执行数据库操作。

如果问题仍然存在，请联系系统管理员或开发团队获取帮助。
