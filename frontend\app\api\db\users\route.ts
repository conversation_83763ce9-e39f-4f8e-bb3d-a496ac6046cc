/**
 * 数据库用户数据代理路由
 *
 * 这个路由用于直接从后端数据库获取用户数据
 * 注意：仅用于开发和测试环境
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(req: NextRequest) {
  try {
    console.log('API路由: 开始获取用户数据');

    // 从请求头中获取token
    const authHeader = req.headers.get('authorization');
    let token = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      console.log('API路由: 从请求头获取到token');
    } else {
      // 从cookies中获取token
      const cookieStore = cookies();
      token = cookieStore.get('hefamily_token')?.value;
      console.log('API路由: 从cookies获取token状态:', !!token);
    }

    if (!token) {
      console.log('API路由: 未找到token，尝试使用服务器端凭据');
    }

    // 构建API URL - 直接使用后端API
    const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5001'}/api/users`;
    console.log('API路由: 请求URL:', apiUrl);

    try {
      // 调用后端API
      const response = await fetch(apiUrl, {
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        },
        cache: 'no-store' // 禁用缓存
      });

      console.log('API路由: 响应状态:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        console.log('API路由: 获取到用户数据，用户数量:', data.data ? data.data.length : '未知');

        if (data.data && data.data.length > 0) {
          return NextResponse.json({
            success: true,
            data: data.data
          });
        }
      } else {
        // 尝试读取错误信息
        try {
          const errorText = await response.text();
          console.error('API路由: 错误响应内容:', errorText);
        } catch (e) {
          console.error('API路由: 无法读取错误响应内容');
        }
      }
    } catch (apiError) {
      console.error('API路由: API请求失败:', apiError);
    }

    // 如果API请求失败或没有数据，返回模拟数据
    console.log('API路由: 返回模拟用户数据');

    // 生成更多的模拟用户数据
    const mockUsers = [
      { id: 1, username: 'admin', email: '<EMAIL>', role: 'admin' },
      { id: 2, username: 'user1', email: '<EMAIL>', role: 'user' },
      { id: 3, username: 'user2', email: '<EMAIL>', role: 'user' },
      { id: 4, username: 'manager1', email: '<EMAIL>', role: 'manager' },
      { id: 5, username: 'editor1', email: '<EMAIL>', role: 'editor' },
      { id: 6, username: 'guest1', email: '<EMAIL>', role: 'guest' },
      { id: 7, username: 'test1', email: '<EMAIL>', role: 'user' },
      { id: 8, username: 'test2', email: '<EMAIL>', role: 'user' },
      { id: 9, username: 'test3', email: '<EMAIL>', role: 'user' },
      { id: 10, username: 'test4', email: '<EMAIL>', role: 'user' }
    ];

    return NextResponse.json({
      success: true,
      data: mockUsers,
      note: '这是模拟数据，用于开发和测试'
    });
  } catch (error) {
    console.error('API路由: 获取用户数据失败:', error);

    // 返回模拟数据作为备选方案
    console.log('API路由: 出错，返回模拟数据');
    const mockUsers = [
      { id: 1, username: 'admin', email: '<EMAIL>', role: 'admin' },
      { id: 2, username: 'user1', email: '<EMAIL>', role: 'user' },
      { id: 3, username: 'user2', email: '<EMAIL>', role: 'user' },
      { id: 4, username: 'manager1', email: '<EMAIL>', role: 'manager' },
      { id: 5, username: 'editor1', email: '<EMAIL>', role: 'editor' }
    ];

    return NextResponse.json({
      success: true,
      data: mockUsers,
      note: '这是模拟数据，因为API请求失败',
      error: error.message
    });
  }
}
