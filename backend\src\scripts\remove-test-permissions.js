/**
 * 删除测试权限
 * 
 * 这个脚本用于删除数据库中的测试权限，如"新权限9487"
 */

require('dotenv').config();
const { Permission } = require('../models');
const { Op } = require('sequelize');

(async () => {
  try {
    console.log('开始删除测试权限...');

    // 要删除的测试权限代码列表
    const testPermissionCodes = [
      'new:permission:9487',
      'test:permission:',
      'unauthorized:permission:',
      'permission:to:delete:'
    ];

    // 查找匹配的权限
    const permissionsToDelete = await Permission.findAll({
      where: {
        [Op.or]: [
          { code: 'new:permission:9487' },
          { code: { [Op.like]: 'test:permission:%' } },
          { code: { [Op.like]: 'unauthorized:permission:%' } },
          { code: { [Op.like]: 'permission:to:delete:%' } }
        ]
      }
    });

    if (permissionsToDelete.length === 0) {
      console.log('未找到需要删除的测试权限');
      process.exit(0);
    }

    console.log(`找到 ${permissionsToDelete.length} 个测试权限需要删除:`);
    permissionsToDelete.forEach(p => {
      console.log(`- ${p.name} (${p.code})`);
    });

    // 删除这些权限
    const deleteCount = await Permission.destroy({
      where: {
        [Op.or]: [
          { code: 'new:permission:9487' },
          { code: { [Op.like]: 'test:permission:%' } },
          { code: { [Op.like]: 'unauthorized:permission:%' } },
          { code: { [Op.like]: 'permission:to:delete:%' } }
        ]
      }
    });

    console.log(`成功删除 ${deleteCount} 个测试权限`);
    console.log('脚本执行完成');
    process.exit(0);
  } catch (error) {
    console.error('执行脚本时发生错误:', error);
    process.exit(1);
  }
})();
