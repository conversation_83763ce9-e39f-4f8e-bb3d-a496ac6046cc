/**
 * 检查文件数量脚本
 * 
 * 用于查询数据库中的文件数量和状态
 */

const { sequelize, File, KnowledgeBase } = require('./src/models');

async function checkFiles() {
  try {
    console.log('开始检查文件数量...');
    console.log('='.repeat(80));
    
    // 查询所有文件的总数
    const totalFiles = await File.count();
    console.log(`总文件数量: ${totalFiles}`);
    
    // 查询不同状态的文件数量
    const approvedFiles = await File.count({ where: { status: 'approved' } });
    const pendingFiles = await File.count({ where: { status: 'pending' } });
    const rejectedFiles = await File.count({ where: { status: 'rejected' } });
    
    console.log(`已批准文件数量: ${approvedFiles}`);
    console.log(`待审核文件数量: ${pendingFiles}`);
    console.log(`已拒绝文件数量: ${rejectedFiles}`);
    
    // 检查总数是否一致
    const sumByStatus = approvedFiles + pendingFiles + rejectedFiles;
    if (totalFiles !== sumByStatus) {
      console.log(`[警告] 文件总数(${totalFiles})与各状态文件数量之和(${sumByStatus})不一致`);
    } else {
      console.log(`[正常] 文件总数(${totalFiles})与各状态文件数量之和(${sumByStatus})一致`);
    }
    
    console.log('='.repeat(80));
    
    // 查询所有知识库
    const knowledgeBases = await KnowledgeBase.findAll({
      order: [['id', 'ASC']]
    });
    
    console.log(`找到 ${knowledgeBases.length} 个知识库`);
    console.log('='.repeat(80));
    
    // 检查每个知识库的文件数量
    for (const kb of knowledgeBases) {
      // 获取所有状态的文件数量
      const allFilesCount = await File.count({
        where: {
          knowledge_base_id: kb.id
        }
      });
      
      // 获取已批准的文件数量
      const approvedFilesCount = await File.count({
        where: {
          knowledge_base_id: kb.id,
          status: 'approved'
        }
      });
      
      // 获取待审核的文件数量
      const pendingFilesCount = await File.count({
        where: {
          knowledge_base_id: kb.id,
          status: 'pending'
        }
      });
      
      // 获取已拒绝的文件数量
      const rejectedFilesCount = await File.count({
        where: {
          knowledge_base_id: kb.id,
          status: 'rejected'
        }
      });
      
      // 输出知识库信息
      console.log(`知识库 #${kb.id}: ${kb.name} (${kb.type})`);
      console.log(`  - 知识库记录的文件数量: ${kb.file_count}`);
      console.log(`  - 实际文件数量统计:`);
      console.log(`    - 所有状态文件: ${allFilesCount}`);
      console.log(`    - 已批准文件: ${approvedFilesCount}`);
      console.log(`    - 待审核文件: ${pendingFilesCount}`);
      console.log(`    - 已拒绝文件: ${rejectedFilesCount}`);
      
      // 检查是否有不一致
      if (kb.file_count !== approvedFilesCount) {
        console.log(`  - [警告] 文件数量不一致: 知识库记录数量=${kb.file_count}, 实际已批准文件数量=${approvedFilesCount}`);
      }
      
      console.log('-'.repeat(80));
    }
    
    // 查询文件详情
    console.log('查询文件详情:');
    console.log('-'.repeat(80));
    
    const files = await File.findAll({
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name', 'type']
        }
      ],
      order: [['id', 'ASC']]
    });
    
    console.log('ID | 文件名 | 知识库 | 状态 | 上传者ID');
    console.log('-'.repeat(80));
    
    for (const file of files) {
      console.log(`${file.id} | ${file.original_name} | ${file.knowledgeBase ? file.knowledgeBase.name : '未知'} | ${file.status} | ${file.uploader_id}`);
    }
    
    console.log('='.repeat(80));
    
    // 关闭数据库连接
    await sequelize.close();
    console.log('检查完成');
  } catch (error) {
    console.error('检查文件数量失败:', error);
    process.exit(1);
  }
}

// 执行检查
checkFiles();
