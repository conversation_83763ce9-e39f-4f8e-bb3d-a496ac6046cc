/**
 * 知识库访问申请控制器
 *
 * 处理知识库访问申请的业务逻辑，如创建、查询、审核等
 */

const { KnowledgeBase, User, KnowledgeBaseAccess, KnowledgeBaseAccessRequest } = require('../models');
const { Op } = require('sequelize');

/**
 * 创建知识库访问申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createAccessRequest = async (req, res) => {
  try {
    const { knowledge_base_id, reason } = req.body;

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(knowledge_base_id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    if (!knowledgeBase) {
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    // 检查是否已有访问权限
    const existingAccess = await KnowledgeBaseAccess.findOne({
      where: {
        knowledge_base_id,
        user_id: req.user.id
      }
    });

    if (existingAccess) {
      return res.status(400).json({
        success: false,
        message: '您已有该知识库的访问权限'
      });
    }

    // 检查是否已有待处理的申请
    const existingRequest = await KnowledgeBaseAccessRequest.findOne({
      where: {
        knowledge_base_id,
        user_id: req.user.id,
        status: 'pending'
      }
    });

    if (existingRequest) {
      return res.status(400).json({
        success: false,
        message: '您已有待处理的访问申请'
      });
    }

    // 创建访问申请
    const accessRequest = await KnowledgeBaseAccessRequest.create({
      knowledge_base_id,
      user_id: req.user.id,
      reason,
      status: 'pending'
    });

    res.status(201).json({
      success: true,
      message: '访问申请已提交，等待审核',
      data: accessRequest
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建访问申请失败',
      error: error.message
    });
  }
};

/**
 * 获取知识库访问申请列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAccessRequests = async (req, res) => {
  try {
    const { knowledge_base_id, status } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereConditions = {};

    if (knowledge_base_id) {
      whereConditions.knowledge_base_id = knowledge_base_id;
    }

    if (status) {
      whereConditions.status = status;
    }

    // 非管理员只能查看自己创建的知识库的申请
    if (req.user.role !== 'admin') {
      // 获取用户创建的知识库ID
      const userKnowledgeBases = await KnowledgeBase.findAll({
        where: { creator_id: req.user.id },
        attributes: ['id']
      });

      const userKnowledgeBaseIds = userKnowledgeBases.map(kb => kb.id);

      // 如果用户没有创建任何知识库，返回空结果
      if (userKnowledgeBaseIds.length === 0) {
        return res.status(200).json({
          success: true,
          data: {
            accessRequests: [],
            pagination: {
              total: 0,
              page,
              limit,
              totalPages: 0
            }
          }
        });
      }

      // 添加知识库ID条件
      whereConditions.knowledge_base_id = { [Op.in]: userKnowledgeBaseIds };
    }

    // 查询访问申请
    const { count, rows: accessRequests } = await KnowledgeBaseAccessRequest.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        },
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name', 'type'],
          include: [
            {
              model: User,
              as: 'creator',
              attributes: ['id', 'username', 'email']
            }
          ]
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'username', 'email']
        }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    // 计算总页数
    const totalPages = Math.ceil(count / limit);

    res.status(200).json({
      success: true,
      data: {
        accessRequests,
        pagination: {
          total: count,
          page,
          limit,
          totalPages
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取访问申请列表失败',
      error: error.message
    });
  }
};

/**
 * 获取知识库的访问申请列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getKnowledgeBaseAccessRequests = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(id);

    if (!knowledgeBase) {
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    // 检查查看权限
    console.log('知识库创建者ID:', knowledgeBase.creator_id);
    console.log('当前用户ID:', req.user.id);
    console.log('当前用户角色:', req.user.role);

    if (req.user.role !== 'admin' && knowledgeBase.creator_id !== req.user.id) {
      console.log('用户没有权限查看该知识库的访问申请');
      return res.status(403).json({
        success: false,
        message: '您没有权限查看该知识库的访问申请'
      });
    }

    // 构建查询条件
    const whereConditions = {
      knowledge_base_id: id
    };

    if (status) {
      whereConditions.status = status;
    }

    // 查询访问申请
    const { count, rows: accessRequests } = await KnowledgeBaseAccessRequest.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        },
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name', 'type'],
          include: [
            {
              model: User,
              as: 'creator',
              attributes: ['id', 'username', 'email']
            }
          ]
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'username', 'email']
        }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    // 计算总页数
    const totalPages = Math.ceil(count / limit);

    res.status(200).json({
      success: true,
      data: {
        accessRequests,
        pagination: {
          total: count,
          page,
          limit,
          totalPages
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取知识库访问申请列表失败',
      error: error.message
    });
  }
};

/**
 * 审核知识库访问申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewAccessRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, access_type } = req.body;

    // 验证状态
    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态，必须是 approved 或 rejected'
      });
    }

    // 如果批准，验证访问类型
    if (status === 'approved' && !['read', 'write', 'admin'].includes(access_type)) {
      return res.status(400).json({
        success: false,
        message: '无效的访问类型，必须是 read、write 或 admin'
      });
    }

    // 查询访问申请
    const accessRequest = await KnowledgeBaseAccessRequest.findByPk(id, {
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          include: [
            {
              model: User,
              as: 'creator',
              attributes: ['id', 'username', 'email']
            }
          ]
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    if (!accessRequest) {
      return res.status(404).json({
        success: false,
        message: '访问申请不存在'
      });
    }

    // 检查审核权限
    if (req.user.role !== 'admin' && accessRequest.knowledgeBase.creator_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您没有权限审核该访问申请'
      });
    }

    // 检查申请状态
    if (accessRequest.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: '该申请已被处理'
      });
    }

    // 更新申请状态
    await accessRequest.update({
      status,
      reviewed_by: req.user.id,
      reviewed_at: new Date()
    });

    // 如果批准，添加访问权限
    if (status === 'approved') {
      await KnowledgeBaseAccess.create({
        knowledge_base_id: accessRequest.knowledge_base_id,
        user_id: accessRequest.user_id,
        access_type: access_type || 'read',
        granted_by: req.user.id
      });
    }

    res.status(200).json({
      success: true,
      message: `访问申请已${status === 'approved' ? '批准' : '拒绝'}`,
      data: accessRequest
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '审核访问申请失败',
      error: error.message
    });
  }
};

/**
 * 获取我的访问申请列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getMyAccessRequests = async (req, res) => {
  try {
    const { status } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereConditions = {
      user_id: req.user.id
    };

    if (status) {
      whereConditions.status = status;
    }

    // 查询访问申请
    const { count, rows: accessRequests } = await KnowledgeBaseAccessRequest.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name', 'type'],
          include: [
            {
              model: User,
              as: 'creator',
              attributes: ['id', 'username', 'email']
            }
          ]
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'username', 'email']
        }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    // 计算总页数
    const totalPages = Math.ceil(count / limit);

    res.status(200).json({
      success: true,
      data: {
        accessRequests,
        pagination: {
          total: count,
          page,
          limit,
          totalPages
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取我的访问申请列表失败',
      error: error.message
    });
  }
};
