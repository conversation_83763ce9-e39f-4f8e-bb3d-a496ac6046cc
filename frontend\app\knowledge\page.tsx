"use client"

import React from "react"

import { useState, useEffect, useMemo } from "react"
import dynamic from 'next/dynamic'
import { useAuth } from "@/contexts/auth-context"
import { toast } from "@/components/ui/use-toast"
import {
  getKnowledgeBaseList,
  getMyKnowledgeBaseList,
  createKnowledgeBase,
  deleteKnowledgeBase
} from "@/services/knowledge-service"
import {
  getAllFiles,
  uploadFile,
  getPendingFiles,
  deleteFile,
  getKnowledgeBaseFiles
} from "@/services/knowledge-file-service"
import { Navbar } from "@/components/navbar"
import { KnowledgeFileUploadDialog, KnowledgeFileDetailDialog, KnowledgeFileReviewDialog } from "@/components/knowledge-file"
import { FileList } from "@/components/file-list"

// 动态导入操作按钮组件，禁用SSR
const KnowledgeActionButtons = dynamic(
  () => import('@/components/knowledge/action-buttons'),
  { ssr: false, loading: () => <div className="h-10"></div> }
)
import {
  Search,
  Upload,
  Database,
  FileText,
  X,
  Check,
  ChevronLeft,
  ChevronRight,
  Calendar,
  User,
  Filter,
  RefreshCw,
  Plus,
  Clock,
  Tag,
  Layers,
  Download,
  Eye,
  Settings,
  Info,
  Phone,
  AlertTriangle,
  MoreVertical,
  Trash2,
  AlertCircle,
  Lock,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import { useRouter } from "next/navigation"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { logger, sanitizeData } from "@/utils/logger"

// 前端显示用的知识库类型
interface KnowledgeBaseDisplay {
  id: string
  name: string
  creator: string
  createdAt: string
  storageSize: string
  documentsCount: number
  type: "系统" | "用户"
  description?: string
  contactPerson?: string
  contactPhone?: string
  status?: string
  updatedAt?: string
  hasAccess?: boolean // 添加访问权限标记
}

// 前端显示用的文件类型
interface KnowledgeFileDisplay {
  id: string
  name: string
  knowledgeBaseId: string
  knowledgeBaseName: string
  size: string
  type: string
  uploadedBy: string
  uploadedAt: string
  knowledgeBaseType: "系统" | "用户"
}

interface UploadFile {
  id: string
  name: string
  size: number
  type: string
  progress: number
  status: "uploading" | "completed" | "error"
  error?: string
}

// Modal type
type ModalContentProps = {
  title: string
  children: React.ReactNode
  footer?: React.ReactNode
}

const ModalContent: React.FC<ModalContentProps> = ({ title, children, footer }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-500 bg-opacity-75">
      <div className="relative bg-white rounded-lg shadow-lg max-w-2xl w-full mx-4">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold">{title}</h2>
        </div>
        <div className="p-4">{children}</div>
        {footer && <div className="p-4 border-t">{footer}</div>}
      </div>
    </div>
  )
}

// Memoized KnowledgeBaseCard component to improve performance
const KnowledgeBaseCardComponent = React.memo(
  ({
    knowledgeBase,
    onView,
    onSettings,
    onDelete,
  }: {
    knowledgeBase: KnowledgeBase
    onView: (id: string) => void
    onSettings: (id: string) => void
    onDelete: (id: string) => void
  }) => {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span className="truncate">{knowledgeBase.name}</span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onView(knowledgeBase.id)}>查看</DropdownMenuItem>
                <DropdownMenuItem onClick={() => onSettings(knowledgeBase.id)}>设置</DropdownMenuItem>
                <DropdownMenuItem className="text-red-600" onClick={() => onDelete(knowledgeBase.id)}>
                  删除
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardTitle>
          <CardDescription>{knowledgeBase.description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <p className="text-muted-foreground">类型</p>
              <p>{knowledgeBase.type}</p>
            </div>
            <div>
              <p className="text-muted-foreground">状态</p>
              <p>{knowledgeBase.status}</p>
            </div>
            <div>
              <p className="text-muted-foreground">文档数量</p>
              <p>{knowledgeBase.documentsCount}</p>
            </div>
            <div>
              <p className="text-muted-foreground">大小</p>
              <p>{knowledgeBase.storageSize}</p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" size="sm" onClick={() => onView(knowledgeBase.id)}>
            查看
          </Button>
          <Button variant="default" size="sm" onClick={() => onSettings(knowledgeBase.id)}>
            设置
          </Button>
        </CardFooter>
      </Card>
    )
  },
)

KnowledgeBaseCardComponent.displayName = "KnowledgeBaseCard"

export default function KnowledgePage() {
  const router = useRouter()
  const { isLoggedIn, hasPermission } = useAuth()

  // 状态
  const [kbSearchQuery, setKbSearchQuery] = useState("")
  const [fileSearchQuery, setFileSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("knowledge-bases")
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [showCreateKnowledgeBaseDialog, setShowCreateKnowledgeBaseDialog] = useState(false)
  const [uploadToSystemKb, setUploadToSystemKb] = useState(false)
  const [selectedPrivateKnowledgeBase, setSelectedPrivateKnowledgeBase] = useState("")
  const [createNewKnowledgeBase, setCreateNewKnowledgeBase] = useState(false)
  const [newKnowledgeBaseName, setNewKnowledgeBaseName] = useState("")
  const [newKnowledgeBaseDescription, setNewKnowledgeBaseDescription] = useState("")
  const [newKnowledgeBaseType, setNewKnowledgeBaseType] = useState<"系统" | "用户">("用户")
  const [isDragging, setIsDragging] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({})
  const [uploadStatus, setUploadStatus] = useState<{ [key: string]: "uploading" | "success" | "error" }>({})
  const [isUploading, setIsUploading] = useState(false)
  const [overallProgress, setOverallProgress] = useState(0)
  const [uploadingFiles, setUploadingFiles] = useState<File[]>([])
  const [uploadComplete, setUploadComplete] = useState(false)
  const [showSuccessNotification, setShowSuccessNotification] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")
  const [showFilters, setShowFilters] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [showLoginPrompt, setShowLoginPrompt] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [refreshTrigger, setRefreshTrigger] = useState(0) // 添加刷新触发器状态

  // 知识库和文件数据
  const [systemKnowledgeBases, setSystemKnowledgeBases] = useState<KnowledgeBaseDisplay[]>([])
  const [userKnowledgeBases, setUserKnowledgeBases] = useState<KnowledgeBaseDisplay[]>([])
  const [files, setFiles] = useState<KnowledgeFileDisplay[]>([])
  const [pendingFiles, setPendingFiles] = useState<KnowledgeFileDisplay[]>([])

  // 检查用户是否已登录
  useEffect(() => {
    // 如果未登录，显示登录提示
    if (!isLoggedIn) {
      setShowLoginPrompt(true)
    } else {
      setShowLoginPrompt(false)
      // 加载数据
      loadData()
    }
  }, [isLoggedIn])

  // 加载知识库和文件数据
  const loadData = async () => {
    setIsLoading(true)

    // 初始化空数据
    let kbResponse = { knowledgeBases: [], pagination: { total: 0, page: 1, limit: 10, totalPages: 0 } };
    let myKbResponse = { knowledgeBases: [], pagination: { total: 0, page: 1, limit: 10, totalPages: 0 } };
    let filesResponse = { files: [], pagination: { total: 0, page: 1, limit: 10, totalPages: 0 } };
    let pendingFilesResponse = { files: [], pagination: { total: 0, page: 1, limit: 10, totalPages: 0 } };

    try {
      logger.debug("开始加载知识库和文件数据");

      try {
        // 获取系统知识库列表
        kbResponse = await getKnowledgeBaseList({ type: 'system' });
        logger.debug("系统知识库列表:", sanitizeData(kbResponse));
      } catch (error) {
        logger.error("获取系统知识库列表失败:", sanitizeData(error));
        toast({
          title: "获取系统知识库列表失败",
          description: "请稍后再试",
          variant: "destructive"
        });
      }

      try {
        // 获取用户知识库列表
        myKbResponse = await getMyKnowledgeBaseList();
        logger.debug("用户知识库列表:", sanitizeData(myKbResponse));
      } catch (error) {
        logger.error("获取用户知识库列表失败:", sanitizeData(error));
        toast({
          title: "获取用户知识库列表失败",
          description: "请稍后再试",
          variant: "destructive"
        });
      }

      try {
        // 获取文件列表，使用当前页码和筛选条件
        const fileParams: any = {
          page: fileListPage,
          limit: 10
        };

        // 添加搜索条件
        if (fileSearchQuery) {
          fileParams.search = fileSearchQuery;
        }

        // 添加文件类型筛选
        if (fileTypeFilter && fileTypeFilter !== 'all') {
          fileParams.type = fileTypeFilter;
        }

        // 添加知识库ID筛选
        if (fileKnowledgeBaseFilter && fileKnowledgeBaseFilter !== 'all') {
          fileParams.knowledge_base_id = fileKnowledgeBaseFilter;
        }

        // 添加知识库类型筛选
        if (fileKnowledgeBaseTypeFilter && fileKnowledgeBaseTypeFilter !== 'all') {
          fileParams.knowledge_base_type = fileKnowledgeBaseTypeFilter === '系统' ? 'system' : 'user';
        }

        // 添加文件大小筛选
        if (fileSizeFilter !== 'all') {
          if (fileSizeFilter === 'small') {
            fileParams.size_max = 2 * 1024 * 1024; // 2MB
          } else if (fileSizeFilter === 'medium') {
            fileParams.size_min = 2 * 1024 * 1024; // 2MB
            fileParams.size_max = 10 * 1024 * 1024; // 10MB
          } else if (fileSizeFilter === 'large') {
            fileParams.size_min = 10 * 1024 * 1024; // 10MB
          }
        }

        logger.debug("文件列表请求参数:", sanitizeData(fileParams));
        filesResponse = await getAllFiles(fileParams);
        logger.debug("文件列表:", sanitizeData(filesResponse));
      } catch (error) {
        logger.error("获取文件列表失败:", sanitizeData(error));
        // 不显示错误提示，因为这个错误不影响主要功能
      }

      try {
        // 获取待审核文件列表
        pendingFilesResponse = await getPendingFiles();
        logger.debug("待审核文件列表:", sanitizeData(pendingFilesResponse));
      } catch (error) {
        logger.error("获取待审核文件列表失败:", sanitizeData(error));
        // 不显示错误提示，因为这个错误不影响主要功能
      }

      // 转换知识库数据格式
      const systemKbs = kbResponse.knowledgeBases.map(kb => ({
        id: kb.id,
        name: kb.name,
        creator: kb.creator?.username || '系统管理员',
        createdAt: new Date(kb.created_at).toLocaleString(),
        storageSize: formatFileSize(kb.storage_size),
        documentsCount: kb.file_count,
        type: '系统' as const,
        description: kb.description,
        updatedAt: new Date(kb.updated_at).toLocaleString(),
        hasAccess: kb.hasAccess // 添加访问权限标记
      }))

      const userKbs = myKbResponse.knowledgeBases.map(kb => ({
        id: kb.id,
        name: kb.name,
        creator: kb.creator?.username || '未知用户',
        createdAt: new Date(kb.created_at).toLocaleString(),
        storageSize: formatFileSize(kb.storage_size),
        documentsCount: kb.file_count,
        type: '用户' as const,
        description: kb.description,
        updatedAt: new Date(kb.updated_at).toLocaleString(),
        hasAccess: kb.hasAccess // 添加访问权限标记
      }))

      // 转换文件数据格式
      const filesList = filesResponse.files.map(file => {
        const kb = [...kbResponse.knowledgeBases, ...myKbResponse.knowledgeBases].find(kb => kb.id === file.knowledge_base_id)
        return {
          id: file.id,
          name: file.original_name,
          knowledgeBaseId: file.knowledge_base_id,
          knowledgeBaseName: kb?.name || '未知知识库',
          size: formatFileSize(file.size),
          type: file.type.split('/')[1] || file.type,
          uploadedBy: file.uploader?.username || file.creator?.username || '未知用户',
          uploadedAt: new Date(file.created_at).toLocaleString(),
          knowledgeBaseType: kb?.type === 'system' ? '系统' : '用户' as const
        }
      })

      // 转换待审核文件数据格式
      const pendingFilesList = pendingFilesResponse.files.map(file => {
        const kb = [...kbResponse.knowledgeBases, ...myKbResponse.knowledgeBases].find(kb => kb.id === file.knowledge_base_id)
        return {
          id: file.id,
          name: file.original_name,
          knowledgeBaseId: file.knowledge_base_id,
          knowledgeBaseName: kb?.name || '未知知识库',
          size: formatFileSize(file.size),
          type: file.type.split('/')[1] || file.type,
          uploadedBy: file.uploader?.username || file.creator?.username || '未知用户',
          uploadedAt: new Date(file.created_at).toLocaleString(),
          knowledgeBaseType: kb?.type === 'system' ? '系统' : '用户' as const
        }
      })

      setSystemKnowledgeBases(systemKbs)
      setUserKnowledgeBases(userKbs)
      setFiles(filesList)
      setPendingFiles(pendingFilesList)

      // 保存文件分页信息
      setFilePagination(filesResponse.pagination)
    } catch (error) {
      logger.error('加载数据失败:', sanitizeData(error))
      toast({
        title: "加载失败",
        description: "无法加载知识库数据，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 在客户端环境中导出格式化文件大小函数，供其他组件使用
  if (typeof window !== 'undefined') {
    (window as any).formatFileSize = formatFileSize
  }

  // 联系人相关状态
  const [contactPerson, setContactPerson] = useState("")
  const [contactPhone, setContactPhone] = useState("")

  // 在状态部分添加以下状态
  const [showAccessRequestDialog, setShowAccessRequestDialog] = useState(false)
  const [accessRequestReason, setAccessRequestReason] = useState("")
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<KnowledgeBaseDisplay | null>(null)
  const [showDeleteConfirmDialog, setShowDeleteConfirmDialog] = useState(false)
  const [accessRequestSubmitted, setAccessRequestSubmitted] = useState(false)

  // 文件详情对话框状态
  const [showFileDetailDialog, setShowFileDetailDialog] = useState(false)
  const [selectedFile, setSelectedFile] = useState<KnowledgeFileDisplay | null>(null)

  // 文件审核对话框状态
  const [showFileReviewDialog, setShowFileReviewDialog] = useState(false)
  const [reviewLoading, setReviewLoading] = useState(false)

  // 检查用户是否为管理员
  const isAdmin = hasPermission('knowledge:manage')

  // 文件筛选状态
  const [fileTypeFilter, setFileTypeFilter] = useState<string>("all")
  const [fileSizeFilter, setFileSizeFilter] = useState<string>("all")
  const [fileKnowledgeBaseFilter, setFileKnowledgeBaseFilter] = useState<string>("all")
  const [fileKnowledgeBaseTypeFilter, setFileKnowledgeBaseTypeFilter] = useState<string>("all")

  // 文件列表分页状态
  const [fileListPage, setFileListPage] = useState(1)
  const [filePagination, setFilePagination] = useState({ total: 0, page: 1, limit: 10, totalPages: 0 })

  // 知识库筛选状态
  const [kbCreatorFilter, setKbCreatorFilter] = useState<string>("all")
  const [kbTimeFilter, setKbTimeFilter] = useState<string>("all")
  const [kbTypeFilter, setKbTypeFilter] = useState<string>("all")

  // Modal state
  const [modalContent, setModalContent] = useState<React.ReactNode>(null)

  // Function to open the modal
  const openModal = (content: React.ReactNode) => {
    setModalContent(content)
  }

  // Function to close the modal
  const closeModal = () => {
    setModalContent(null)
  }

  // 所有知识库数据现在从API获取

  const allKnowledgeBases = useMemo(() => {
    return [...systemKnowledgeBases, ...userKnowledgeBases]
  }, [systemKnowledgeBases, userKnowledgeBases])

  // 获取所有创建者列表（用于筛选）
  const allCreators = Array.from(new Set(allKnowledgeBases.map((kb) => kb.creator)))

  // 获取活跃筛选器数量
  const getActiveKbFiltersCount = () => {
    let count = 0
    if (kbCreatorFilter !== "all") count++
    if (kbTimeFilter !== "all") count++
    if (kbTypeFilter !== "all") count++
    return count
  }

  const getActiveFileFiltersCount = () => {
    let count = 0
    if (fileTypeFilter !== "all") count++
    if (fileSizeFilter !== "all") count++
    if (fileKnowledgeBaseFilter !== "all") count++
    if (fileKnowledgeBaseTypeFilter !== "all") count++
    return count
  }

  // 过滤知识库
  const filteredKnowledgeBases = useMemo(() => {
    return allKnowledgeBases.filter((kb) => {
      // Search filter
      const matchesSearch =
        !kbSearchQuery ||
        kb.name.toLowerCase().includes(kbSearchQuery.toLowerCase()) ||
        kb.id.toLowerCase().includes(kbSearchQuery.toLowerCase())

      // Creator filter
      const matchesCreator = kbCreatorFilter === "all" || kb.creator === kbCreatorFilter

      // Type filter
      const matchesType = kbTypeFilter === "all" || kb.type === kbTypeFilter

      // Time filter (simplified)
      const createdDate = new Date(kb.createdAt)
      const now = new Date()
      const oneMonthAgo = new Date()
      oneMonthAgo.setMonth(now.getMonth() - 1)
      const threeMonthsAgo = new Date()
      threeMonthsAgo.setMonth(now.getMonth() - 3)
      const oneYearAgo = new Date()
      oneYearAgo.setFullYear(now.getFullYear() - 1)

      const matchesTime =
        kbTimeFilter === "all" ||
        (kbTimeFilter === "month" && createdDate >= oneMonthAgo) ||
        (kbTimeFilter === "quarter" && createdDate >= threeMonthsAgo) ||
        (kbTimeFilter === "year" && createdDate >= oneYearAgo)

      return matchesSearch && matchesCreator && matchesType && matchesTime
    })
  }, [allKnowledgeBases, kbSearchQuery, kbCreatorFilter, kbTypeFilter, kbTimeFilter])

  // 分离系统和用户知识库
  const filteredSystemKnowledgeBases = useMemo(() => {
    return filteredKnowledgeBases.filter((kb) => kb.type === "系统")
  }, [filteredKnowledgeBases])

  const filteredUserKnowledgeBases = useMemo(() => {
    return filteredKnowledgeBases.filter((kb) => kb.type === "用户")
  }, [filteredKnowledgeBases])

  // 过滤文件
  const filteredFiles = useMemo(() => {
    return files.filter((file) => {
      // Search filter
      const matchesSearch = !fileSearchQuery || file.name.toLowerCase().includes(fileSearchQuery.toLowerCase())

      // File type filter
      const matchesType = fileTypeFilter === "all" || file.type === fileTypeFilter

      // Knowledge base filter
      const matchesKnowledgeBase = fileKnowledgeBaseFilter === "all" || file.knowledgeBaseId === fileKnowledgeBaseFilter

      // Knowledge base type filter
      const matchesKnowledgeBaseType =
        fileKnowledgeBaseTypeFilter === "all" || file.knowledgeBaseType === fileKnowledgeBaseTypeFilter

      // File size filter (simplified)
      const matchesSize =
        fileSizeFilter === "all" ||
        (fileSizeFilter === "small" && Number.parseFloat(file.size) < 2) ||
        (fileSizeFilter === "medium" && Number.parseFloat(file.size) >= 2 && Number.parseFloat(file.size) < 10) ||
        (fileSizeFilter === "large" && Number.parseFloat(file.size) >= 10)

      return matchesSearch && matchesType && matchesKnowledgeBase && matchesKnowledgeBaseType && matchesSize
    })
  }, [files, fileSearchQuery, fileTypeFilter, fileKnowledgeBaseFilter, fileKnowledgeBaseTypeFilter, fileSizeFilter])

  // 当过滤条件变化时，重置分页到第一页
  useEffect(() => {
    setFileListPage(1)
  }, [fileSearchQuery, fileTypeFilter, fileKnowledgeBaseFilter, fileKnowledgeBaseTypeFilter, fileSizeFilter])

  // 当页码或筛选条件变化时，重新加载数据
  useEffect(() => {
    if (isLoggedIn) {
      loadData()
    }
  }, [
    fileListPage,
    isLoggedIn,
    fileSearchQuery,
    fileTypeFilter,
    fileKnowledgeBaseFilter,
    fileKnowledgeBaseTypeFilter,
    fileSizeFilter
  ])

  // 处理文件查看
  const handleViewFile = (file: KnowledgeFileDisplay) => {
    setSelectedFile(file)
    setShowFileDetailDialog(true)
  }

  // 处理文件下载
  const handleDownloadFile = (fileId: string) => {
    // 在实际应用中，这里应该调用API下载文件
    toast({
      title: "开始下载",
      description: "文件下载已开始，请稍候",
    })
  }

  // 处理文件审核
  const handleReviewFile = (file: KnowledgeFileDisplay) => {
    setSelectedFile(file)
    setShowFileReviewDialog(true)
  }

  // 处理文件审核通过
  const handleApproveFile = async (fileId: string) => {
    setReviewLoading(true)
    try {
      // 在实际应用中，这里应该调用API审核文件
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新文件状态
      const updatedPendingFiles = pendingFiles.filter(file => file.id !== fileId)
      setPendingFiles(updatedPendingFiles)

      // 增加刷新触发器的值，触发文件列表刷新
      setRefreshTrigger(prev => prev + 1)

      setSuccessMessage("文件已成功通过审核")
      setShowSuccessNotification(true)
      setShowFileReviewDialog(false)
    } catch (error) {
      logger.error("审核文件失败:", sanitizeData(error))
      toast({
        title: "审核失败",
        description: "无法完成文件审核，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setReviewLoading(false)
    }
  }

  // 处理文件审核驳回
  const handleRejectFile = async (fileId: string, reason: string) => {
    setReviewLoading(true)
    try {
      // 在实际应用中，这里应该调用API驳回文件
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新文件状态
      const updatedPendingFiles = pendingFiles.filter(file => file.id !== fileId)
      setPendingFiles(updatedPendingFiles)

      // 增加刷新触发器的值，触发文件列表刷新
      setRefreshTrigger(prev => prev + 1)

      setSuccessMessage("文件已驳回")
      setShowSuccessNotification(true)
      setShowFileReviewDialog(false)
    } catch (error) {
      logger.error("驳回文件失败:", sanitizeData(error))
      toast({
        title: "操作失败",
        description: "无法完成文件驳回，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setReviewLoading(false)
    }
  }

  // 处理上传完成
  const handleUploadComplete = (message: string) => {
    setSuccessMessage(message)
    setShowSuccessNotification(true)
    setShowUploadDialog(false)

    // 增加刷新触发器的值，触发文件列表刷新
    setRefreshTrigger(prev => prev + 1)

    // 刷新文件列表
    loadData()
  }

  // 开始上传过程
  const startUploadProcess = () => {
    // 重置错误状态
    setUploadError(null)

    if (selectedFiles.length === 0) {
      setUploadError("请选择要上传的文件")
      return
    }

    // 验证知识库选择
    if (!uploadToSystemKb && !selectedPrivateKnowledgeBase && !createNewKnowledgeBase) {
      setUploadError("请选择一个知识库或创建新的知识库")
      return
    }

    if (createNewKnowledgeBase && !newKnowledgeBaseName) {
      setUploadError("请输入新知识库名称")
      return
    }

    // 如果创建新知识库，先创建知识库
    if (createNewKnowledgeBase && newKnowledgeBaseName) {
      const newKnowledgeBase: KnowledgeBase = {
        id: `KB${Math.floor(Math.random() * 1000)
          .toString()
          .padStart(3, "0")}`,
        name: newKnowledgeBaseName,
        creator: "张文浩",
        createdAt: new Date().toLocaleString(),
        storageSize: "0 KB",
        documentsCount: 0,
        type: "用户",
        description: newKnowledgeBaseDescription,
        contactPerson: contactPerson,
        contactPhone: contactPhone,
      }

      userKnowledgeBases.push(newKnowledgeBase)
      setSelectedPrivateKnowledgeBase(newKnowledgeBase.id)
    }

    // Start upload
    setIsUploading(true)
    setUploadingFiles([...selectedFiles])
    setOverallProgress(0)
    setUploadComplete(false)

    // Reset each file's upload status
    const initialProgress = {}
    const initialStatus = {}

    selectedFiles.forEach((file, index) => {
      const fileId = `file-${index}-${file.name.replace(/\s+/g, "-")}`
      initialProgress[fileId] = 0
      initialStatus[fileId] = "uploading"
    })

    setUploadProgress(initialProgress)
    setUploadStatus(initialStatus)

    // Track overall progress
    let completedFiles = 0
    const totalFiles = selectedFiles.length

    // Process each file with individual progress tracking
    selectedFiles.forEach((file, index) => {
      const fileId = `file-${index}-${file.name.replace(/\s+/g, "-")}`

      // Simulate file upload with varying speeds based on file size
      const totalSteps = 20 // Number of progress updates per file
      const stepSize = 100 / totalSteps
      const baseDelay = 100 // Base delay between progress updates
      const fileSizeFactor = file.size / (1024 * 1024) // Size in MB affects upload speed

      let currentProgress = 0
      let step = 0

      const uploadInterval = setInterval(
        () => {
          step++

          // Calculate progress with some randomness and size factor
          const increment = stepSize * (0.5 + Math.random())
          currentProgress = Math.min(currentProgress + increment, step < totalSteps ? 95 : 100)

          // Update this file's progress
          setUploadProgress((prev) => ({
            ...prev,
            [fileId]: currentProgress,
          }))

          // If file is complete
          if (currentProgress >= 100) {
            clearInterval(uploadInterval)

            setUploadStatus((prev) => ({
              ...prev,
              [fileId]: "success",
            }))

            completedFiles++

            // Update overall progress
            const newOverallProgress = (completedFiles / totalFiles) * 100
            setOverallProgress(newOverallProgress)

            // Check if all files are complete
            if (completedFiles === totalFiles) {
              setTimeout(() => {
                setUploadComplete(true)
                setTimeout(() => {
                  setSuccessMessage(`已成功上传 ${selectedFiles.length} 个文件到知识库。`)
                  setShowSuccessNotification(true)
                }, 500)
              }, 500)
            }
          }
        },
        baseDelay + fileSizeFactor * 10,
      ) // Larger files upload slower
    })
  }

  // 处理上传对话框提交
  const handleUploadSubmit = () => {
    startUploadProcess()
  }

  // 处理创建知识库对话框提交
  const handleCreateKnowledgeBaseSubmit = async () => {
    if (!newKnowledgeBaseName) {
      toast({
        title: "错误",
        description: "请输入知识库名称",
        variant: "destructive"
      })
      return
    }

    try {
      // 调用API创建知识库
      const createParams = {
        name: newKnowledgeBaseName,
        description: newKnowledgeBaseDescription,
        type: newKnowledgeBaseType === "系统" ? "system" : "user" as "system" | "user"
      }

      console.log("创建知识库参数:", createParams);

      // 调用API创建知识库
      const newKnowledgeBase = await createKnowledgeBase(createParams);
      console.log("创建知识库成功:", newKnowledgeBase);

      // 显示成功消息
      setSuccessMessage(`已成功创建知识库 "${newKnowledgeBaseName}"。`)
      setShowSuccessNotification(true)

      // 重置状态并关闭对话框
      setNewKnowledgeBaseName("")
      setNewKnowledgeBaseDescription("")
      setNewKnowledgeBaseType("用户")
      setContactPerson("")
      setContactPhone("")
      setShowCreateKnowledgeBaseDialog(false)

      // 重新加载数据
      await loadData()
    } catch (error) {
      console.error("创建知识库失败:", error)
      toast({
        title: "创建失败",
        description: "无法创建知识库，请稍后再试",
        variant: "destructive"
      })
    }
  }

  // 关闭成功通知并重置状态
  const handleCloseSuccessNotification = () => {
    setShowSuccessNotification(false)

    // 关闭所有对话框并重置状态
    setIsUploading(false)
    setShowUploadDialog(false)
    setSelectedFiles([])
    setUploadProgress({})
    setUploadStatus({})
    setCreateNewKnowledgeBase(false)
    setNewKnowledgeBaseName("")
    setUploadingFiles([])
    setUploadComplete(false)
  }

  // 重置知识库筛选
  const resetKbFilters = () => {
    setKbCreatorFilter("all")
    setKbTimeFilter("all")
    setKbTypeFilter("all")
  }

  // 重置文件筛选
  const resetFileFilters = () => {
    setFileTypeFilter("all")
    setFileSizeFilter("all")
    setFileKnowledgeBaseFilter("all")
    setFileKnowledgeBaseTypeFilter("all")
  }

  // 获取文件图标
  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case "pdf":
        return <FileText className="text-red-500" />
      case "docx":
      case "doc":
        return <FileText className="text-blue-500" />
      case "xlsx":
      case "xls":
        return <FileText className="text-green-500" />
      case "pptx":
      case "ppt":
        return <FileText className="text-orange-500" />
      case "png":
      case "jpg":
      case "jpeg":
        return <FileText className="text-purple-500" />
      case "mp4":
      case "mov":
        return <FileText className="text-indigo-500" />
      case "zip":
      case "rar":
        return <FileText className="text-yellow-500" />
      default:
        return <FileText className="text-gray-500" />
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(false)

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      handleFileUpload(event.dataTransfer.files)
    }
  }

  // 获取知识库类型标签颜色
  const getKnowledgeBaseTypeColor = (type: "系统" | "用户") => {
    return type === "系统" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800"
  }

  // 获取时间筛选器显示文本
  const getTimeFilterText = (filter: string) => {
    switch (filter) {
      case "month":
        return "最近一个月"
      case "quarter":
        return "最近三个月"
      case "year":
        return "最近一年"
      default:
        return "所有时间"
    }
  }

  // 获取文件大小筛选器显示文本
  const getFileSizeFilterText = (filter: string) => {
    switch (filter) {
      case "small":
        return "小 (<2MB)"
      case "medium":
        return "中 (2-10MB)"
      case "large":
        return "大 (>10MB)"
      default:
        return "所有大小"
    }
  }

  // 解析文件大小字符串
  const parseFileSize = (sizeStr: string) => {
    const size = Number.parseFloat(sizeStr)
    const unit = sizeStr.replace(/[0-9.]/g, "").trim()
    return { size, unit }
  }

  // 获取文件大小进度条颜色
  const getFileSizeProgressColor = (size: string) => {
    const { size: sizeNum } = parseFileSize(size)
    if (sizeNum < 2) return "bg-green-500"
    if (sizeNum < 10) return "bg-yellow-500"
    return "bg-red-500"
  }

  // 获取文件大小百分比（相对于最大文件）
  const getFileSizePercentage = (size: string) => {
    const { size: sizeNum } = parseFileSize(size)
    const maxSize = 128.5 // 假设最大文件为128.5MB
    return Math.min((sizeNum / maxSize) * 100, 100)
  }

  // 待审核文件列表现在从API获取

  const canEditKnowledgeBase = (kb: KnowledgeBaseDisplay) => {
    // 管理员可以编辑所有知识库
    if (isAdmin) return true

    // 普通用户只能编辑自己创建的用户知识库
    return kb.type === "用户" && hasPermission('knowledge:edit')
  }

  const handleViewKnowledgeBase = (kb: KnowledgeBaseDisplay) => {
    // 如果是系统知识库或者用户有权限，直接查看
    if (kb.type === "系统" || kb.hasAccess || hasPermission('knowledge:view')) {
      router.push(`/knowledge/view/${kb.id}`)
    } else {
      // 如果是用户知识库且用户没有权限，显示访问请求对话框
      setSelectedKnowledgeBase(kb)
      setShowAccessRequestDialog(true)
    }
  }

  const getDocumentsCountPercentage = (count: number) => {
    const maxCount = 215 // Assume max document count is 215
    return Math.min((count / maxCount) * 100, 100)
  }

  const KnowledgeBaseCard = ({ kb }: { kb: KnowledgeBaseDisplay }) => {
    const router = useRouter()

    return (
      <Card
        key={kb.id}
        className="overflow-hidden border-gray-200 hover:border-emerald-300 transition-all hover:shadow-md"
      >
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div className="flex gap-2">
              <Badge
                className={cn("mb-2", kb.type === "系统" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800")}
              >
                {kb.type}
              </Badge>
              {kb.type === "用户" && !kb.hasAccess && (
                <Badge className="mb-2 bg-red-100 text-red-800">
                  <Lock className="h-3 w-3 mr-1" />
                  需申请
                </Badge>
              )}
            </div>
            <div className="text-xs text-gray-500 flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {kb.createdAt}
            </div>
          </div>
          <CardTitle className="text-lg font-bold text-gray-800 flex items-center">
            <Database className="h-5 w-5 mr-2 text-emerald-500" />
            {kb.name}
          </CardTitle>
          <CardDescription className="line-clamp-2 h-10 text-gray-600">{kb.description || "无描述"}</CardDescription>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-gray-500 mb-1 flex items-center">
                <User className="h-3 w-3 mr-1" />
                创建者
              </div>
              <div className="font-medium">{kb.creator}</div>
            </div>
            <div>
              <div className="text-gray-500 mb-1 flex items-center">
                <FileText className="h-3 w-3 mr-1" />
                文档数量
              </div>
              <div className="font-medium flex items-center">
                {kb.documentsCount}
                <div className="ml-2 w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-emerald-500"
                    style={{ width: `${getDocumentsCountPercentage(kb.documentsCount)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-3">
            <div className="text-gray-500 mb-1 text-sm flex items-center">
              <Layers className="h-3 w-3 mr-1" />
              存储大小
            </div>
            <div className="font-medium flex items-center text-sm">
              {kb.storageSize}
              <div className="ml-2 flex-grow h-1.5 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-500"
                  style={{ width: `${getFileSizePercentage(kb.storageSize)}%` }}
                ></div>
              </div>
            </div>
          </div>
          {kb.contactPerson && (
            <div className="mt-3">
              <div className="text-gray-500 mb-1 text-sm flex items-center">
                <User className="h-3 w-3 mr-1" />
                联系人
              </div>
              <div className="font-medium text-sm">{kb.contactPerson}</div>
            </div>
          )}
          {kb.contactPhone && (
            <div className="mt-2">
              <div className="text-gray-500 mb-1 text-sm flex items-center">
                <Phone className="h-3 w-3 mr-1" />
                联系电话
              </div>
              <div className="font-medium text-sm">{kb.contactPhone}</div>
            </div>
          )}
        </CardContent>
        <CardFooter className="pt-2 flex justify-between border-t border-gray-100 mt-2">
          <Button
            variant="ghost"
            size="sm"
            className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 flex items-center"
            onClick={() => handleViewKnowledgeBase(kb)}
          >
            <Eye className="h-3.5 w-3.5 mr-1" />
            查看
          </Button>

          {kb.type === "用户" && (
            <>
              {canEditKnowledgeBase(kb) && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-emerald-600 hover:text-emerald-800 hover:bg-emerald-50 flex items-center"
                  onClick={() => router.push(`/knowledge/settings/${kb.id}`)}
                >
                  编辑
                </Button>
              )}
              {canEditKnowledgeBase(kb) && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-red-600 hover:text-red-800 hover:bg-red-50 flex items-center"
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    setSelectedKnowledgeBase(kb)
                    setShowDeleteConfirmDialog(true)
                  }}
                >
                  删除
                </Button>
              )}
            </>
          )}
        </CardFooter>
      </Card>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        {showLoginPrompt ? (
          <div className="bg-white p-8 rounded-lg shadow-md max-w-md mx-auto mt-20">
            <div className="flex flex-col items-center text-center">
              <AlertTriangle className="h-16 w-16 text-yellow-500 mb-4" />
              <h2 className="text-2xl font-bold mb-4">需要登录</h2>
              <p className="text-gray-600 mb-6">
                您需要登录后才能访问知识库页面。请先登录或注册一个账号。
              </p>
              <div className="flex gap-4">
                <Button
                  onClick={() => {
                    // 触发登录弹窗
                    const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
                    window.dispatchEvent(event);
                  }}
                  className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                >
                  登录
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    // 触发注册弹窗
                    const event = new CustomEvent('open-login-modal', { detail: { isRegister: true } });
                    window.dispatchEvent(event);
                  }}
                >
                  注册
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* 页面标题和操作按钮 */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2">知识库管理</h1>
                <p className="text-gray-500">管理和组织您的知识资源，提高信息检索效率</p>
              </div>
              <KnowledgeActionButtons
                isAdmin={isAdmin}
                pendingFilesCount={pendingFiles.length}
                onCreateKnowledgeBase={() => setShowCreateKnowledgeBaseDialog(true)}
                onUploadFile={() => setShowUploadDialog(true)}
              />
            </div>

        {/* 成功通知 */}
        {showSuccessNotification && (
          <Alert className="mb-6 bg-green-50 border-green-200 shadow-sm animate-in slide-in-from-top duration-300">
            <Check className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800 font-medium">操作成功</AlertTitle>
            <AlertDescription className="text-green-700">{successMessage}</AlertDescription>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 border-green-300 text-green-700 hover:bg-green-100"
              onClick={handleCloseSuccessNotification}
            >
              确定
            </Button>
          </Alert>
        )}

        {/* 标签页切换 */}
        <Card className="mb-8 shadow-sm border-gray-200 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="w-full grid grid-cols-2 rounded-none bg-gray-100 p-0 h-12">
              <TabsTrigger
                value="knowledge-bases"
                className="rounded-none data-[state=active]:bg-white data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-emerald-500 h-full"
              >
                知识库列表
              </TabsTrigger>
              <TabsTrigger
                value="files"
                className="rounded-none data-[state=active]:bg-white data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-emerald-500 h-full"
              >
                文件列表
              </TabsTrigger>
            </TabsList>

            {/* 搜索和筛选区域 */}
            <div className="p-4 bg-white border-b border-gray-200">
              <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
                <div className="relative flex-grow">
                  <input
                    type="text"
                    placeholder={activeTab === "knowledge-bases" ? "搜索知识库名称" : "搜索文件名称"}
                    value={activeTab === "knowledge-bases" ? kbSearchQuery : fileSearchQuery}
                    onChange={(e) =>
                      activeTab === "knowledge-bases"
                        ? setKbSearchQuery(e.target.value)
                        : setFileSearchQuery(e.target.value)
                    }
                    className="w-full border border-gray-300 rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent bg-white shadow-sm"
                  />
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                    🔍
                  </span>
                </div>
                <Button
                  variant="outline"
                  className={cn(
                    "border-gray-300 text-gray-700 flex items-center gap-2 whitespace-nowrap",
                    showFilters && "bg-gray-100",
                  )}
                  onClick={() => setShowFilters(!showFilters)}
                >
                  筛选
                  {activeTab === "knowledge-bases" && getActiveKbFiltersCount() > 0 && (
                    <Badge className="ml-1 bg-emerald-500 hover:bg-emerald-600">{getActiveKbFiltersCount()}</Badge>
                  )}
                  {activeTab === "files" && getActiveFileFiltersCount() > 0 && (
                    <Badge className="ml-1 bg-emerald-500 hover:bg-emerald-600">{getActiveFileFiltersCount()}</Badge>
                  )}
                </Button>
              </div>

              {/* 活跃筛选器标签 */}
              {((activeTab === "knowledge-bases" && getActiveKbFiltersCount() > 0) ||
                (activeTab === "files" && getActiveFileFiltersCount() > 0)) && (
                <div className="flex flex-wrap items-center gap-2 mt-4">
                  {activeTab === "knowledge-bases" && (
                    <>
                      {kbCreatorFilter !== "all" && (
                        <Badge variant="outline" className="bg-gray-50 gap-1 flex items-center py-1 px-3">
                          <User className="h-3 w-3 mr-1" />
                          创建者: {kbCreatorFilter}
                          <X
                            className="h-3 w-3 ml-1 cursor-pointer hover:text-red-500"
                            onClick={() => setKbCreatorFilter("all")}
                          />
                        </Badge>
                      )}
                      {kbTimeFilter !== "all" && (
                        <Badge variant="outline" className="bg-gray-50 gap-1 flex items-center py-1 px-3">
                          <Calendar className="h-3 w-3 mr-1" />
                          时间: {getTimeFilterText(kbTimeFilter)}
                          <X
                            className="h-3 w-3 ml-1 cursor-pointer hover:text-red-500"
                            onClick={() => setKbTimeFilter("all")}
                          />
                        </Badge>
                      )}
                      {kbTypeFilter !== "all" && (
                        <Badge variant="outline" className="bg-gray-50 gap-1 flex items-center py-1 px-3">
                          <Database className="h-3 w-3 mr-1" />
                          类型: {kbTypeFilter === "系统" ? "系统知识库" : "用户知识库"}
                          <X
                            className="h-3 w-3 ml-1 cursor-pointer hover:text-red-500"
                            onClick={() => setKbTypeFilter("all")}
                          />
                        </Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-[#1e7a43] flex items-center h-7 hover:text-[#1e7a43]/90 hover:bg-[#1e7a43]/10"
                        onClick={resetKbFilters}
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        重置筛选
                      </Button>
                    </>
                  )}

                  {activeTab === "files" && (
                    <>
                      {fileTypeFilter !== "all" && (
                        <Badge variant="outline" className="bg-gray-50 gap-1 flex items-center py-1 px-3">
                          <FileText className="h-3 w-3 mr-1" />
                          类型: {fileTypeFilter.toUpperCase()}
                          <X
                            className="h-3 w-3 ml-1 cursor-pointer hover:text-red-500"
                            onClick={() => setFileTypeFilter("all")}
                          />
                        </Badge>
                      )}
                      {fileSizeFilter !== "all" && (
                        <Badge variant="outline" className="bg-gray-50 gap-1 flex items-center py-1 px-3">
                          <FileText className="h-3 w-3 mr-1" />
                          大小: {getFileSizeFilterText(fileSizeFilter)}
                          <X
                            className="h-3 w-3 ml-1 cursor-pointer hover:text-red-500"
                            onClick={() => setFileSizeFilter("all")}
                          />
                        </Badge>
                      )}
                      {fileKnowledgeBaseFilter !== "all" && (
                        <Badge variant="outline" className="bg-gray-50 gap-1 flex items-center py-1 px-3">
                          <Database className="h-3 w-3 mr-1" />
                          知识库:{" "}
                          {allKnowledgeBases.find((kb) => kb.id === fileKnowledgeBaseFilter)?.name ||
                            fileKnowledgeBaseFilter}
                          <X
                            className="h-3 w-3 ml-1 cursor-pointer hover:text-red-500"
                            onClick={() => setFileKnowledgeBaseFilter("all")}
                          />
                        </Badge>
                      )}
                      {fileKnowledgeBaseTypeFilter !== "all" && (
                        <Badge variant="outline" className="bg-gray-50 gap-1 flex items-center py-1 px-3">
                          <Tag className="h-3 w-3 mr-1" />
                          知识库类型: {fileKnowledgeBaseTypeFilter}
                          <X
                            className="h-3 w-3 ml-1 cursor-pointer hover:text-red-500"
                            onClick={() => setFileKnowledgeBaseTypeFilter("all")}
                          />
                        </Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-[#1e7a43] flex items-center h-7 hover:text-[#1e7a43]/90 hover:bg-[#1e7a43]/10"
                        onClick={resetFileFilters}
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        重置筛选
                      </Button>
                    </>
                  )}
                </div>
              )}

              {/* 筛选选项面板 */}
              {showFilters && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200 animate-in slide-in-from-top duration-300">
                  {activeTab === "knowledge-bases" ? (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-gray-700">
                          <User className="h-4 w-4" />
                          <label className="text-sm font-medium">创建者</label>
                        </div>
                        <Select value={kbCreatorFilter} onValueChange={setKbCreatorFilter}>
                          <SelectTrigger className="bg-white border-gray-300">
                            <SelectValue placeholder="选择创建者" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">所有创建者</SelectItem>
                            {allCreators.map((creator) => (
                              <SelectItem key={creator} value={creator}>
                                {creator}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-gray-700">
                          <Clock className="h-4 w-4" />
                          <label className="text-sm font-medium">创建时间</label>
                        </div>
                        <Select value={kbTimeFilter} onValueChange={setKbTimeFilter}>
                          <SelectTrigger className="bg-white border-gray-300">
                            <SelectValue placeholder="选择时间范围" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">所有时间</SelectItem>
                            <SelectItem value="month">最近一个月</SelectItem>
                            <SelectItem value="quarter">最近三个月</SelectItem>
                            <SelectItem value="year">最近一年</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-gray-700">
                          <Database className="h-4 w-4" />
                          <label className="text-sm font-medium">知识库类型</label>
                        </div>
                        <Select value={kbTypeFilter} onValueChange={setKbTypeFilter}>
                          <SelectTrigger className="bg-white border-gray-300">
                            <SelectValue placeholder="选择知识库类型" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">所有类型</SelectItem>
                            <SelectItem value="系统">系统知识库</SelectItem>
                            <SelectItem value="用户">用户知识库</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-gray-700">
                          <FileText className="h-4 w-4" />
                          <label className="text-sm font-medium">文件类型</label>
                        </div>
                        <Select value={fileTypeFilter} onValueChange={setFileTypeFilter}>
                          <SelectTrigger className="bg-white border-gray-300">
                            <SelectValue placeholder="选择文件类型" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">所有类型</SelectItem>
                            <SelectItem value="pdf">PDF文档</SelectItem>
                            <SelectItem value="doc">Word文档(.doc)</SelectItem>
                            <SelectItem value="docx">Word文档(.docx)</SelectItem>
                            <SelectItem value="word">Word文档(所有)</SelectItem>
                            <SelectItem value="xls">Excel表格(.xls)</SelectItem>
                            <SelectItem value="xlsx">Excel表格(.xlsx)</SelectItem>
                            <SelectItem value="excel">Excel表格(所有)</SelectItem>
                            <SelectItem value="ppt">PowerPoint(.ppt)</SelectItem>
                            <SelectItem value="pptx">PowerPoint(.pptx)</SelectItem>
                            <SelectItem value="powerpoint">PowerPoint(所有)</SelectItem>
                            <SelectItem value="txt">文本文件</SelectItem>
                            <SelectItem value="jpg">图片(JPG/JPEG)</SelectItem>
                            <SelectItem value="png">图片(PNG)</SelectItem>
                            <SelectItem value="image">图片(所有)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-gray-700">
                          <Layers className="h-4 w-4" />
                          <label className="text-sm font-medium">文件大小</label>
                        </div>
                        <Select value={fileSizeFilter} onValueChange={setFileSizeFilter}>
                          <SelectTrigger className="bg-white border-gray-300">
                            <SelectValue placeholder="选择文件大小" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">所有大小</SelectItem>
                            <SelectItem value="small">小 (&lt; 2MB)</SelectItem>
                            <SelectItem value="medium">中 (2-10MB)</SelectItem>
                            <SelectItem value="large">大 (&gt; 10MB)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-gray-700">
                          <Database className="h-4 w-4" />
                          <label className="text-sm font-medium">所属知识库</label>
                        </div>
                        <Select value={fileKnowledgeBaseFilter} onValueChange={setFileKnowledgeBaseFilter}>
                          <SelectTrigger className="bg-white border-gray-300">
                            <SelectValue placeholder="选择知识库" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">所有知识库</SelectItem>
                            {allKnowledgeBases.map((kb) => (
                              <SelectItem key={kb.id} value={kb.id}>
                                {kb.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-gray-700">
                          <Tag className="h-4 w-4" />
                          <label className="text-sm font-medium">知识库类型</label>
                        </div>
                        <Select value={fileKnowledgeBaseTypeFilter} onValueChange={setFileKnowledgeBaseTypeFilter}>
                          <SelectTrigger className="bg-white border-gray-300">
                            <SelectValue placeholder="选择知识库类型" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">所有类型</SelectItem>
                            <SelectItem value="系统">系统知识库</SelectItem>
                            <SelectItem value="用户">用户知识库</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 知识库标签内容 */}
            <TabsContent value="knowledge-bases" className="p-0 m-0">
              {filteredKnowledgeBases.length > 0 ? (
                <div className="p-4 space-y-8">
                  {/* 系统知识库区域 */}
                  {filteredSystemKnowledgeBases.length > 0 && (
                    <div>
                      <div className="flex items-center mb-4">
                        <h2 className="text-xl font-bold text-gray-800">系统知识库</h2>
                        <Badge className="ml-2 bg-blue-100 text-blue-800">{filteredSystemKnowledgeBases.length}</Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {filteredSystemKnowledgeBases.map((kb) => (
                          <KnowledgeBaseCard key={kb.id} kb={kb} />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 用户知识库区域 */}
                  {filteredUserKnowledgeBases.length > 0 && (
                    <div>
                      <div className="flex items-center mb-4">
                        <h2 className="text-xl font-bold text-gray-800">用户知识库</h2>
                        <Badge className="ml-2 bg-green-100 text-green-800">{filteredUserKnowledgeBases.length}</Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {filteredUserKnowledgeBases.map((kb) => (
                          <KnowledgeBaseCard key={kb.id} kb={kb} />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="py-12 text-center text-gray-500">
                  <Database className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium mb-2">未找到知识库</p>
                  <p className="text-sm">尝试调整搜索条件或创建新的知识库</p>
                </div>
              )}
            </TabsContent>

            {/* 文件标签内容 */}
            <TabsContent value="files" className="p-0 m-0">
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="text-lg font-medium">文件列表</div>
                  <div className="text-sm text-gray-500">
                    共 {files.length} 个文件
                  </div>
                </div>

                {/* 文件列表 - 使用FileList组件 */}
                <FileList
                  refreshTrigger={refreshTrigger}
                  onFileDeleted={() => {
                    setRefreshTrigger(prev => prev + 1)
                  }}
                  fileType={fileTypeFilter !== 'all' ? (
                    console.log('传递文件类型筛选:', fileTypeFilter),
                    fileTypeFilter
                  ) : undefined}
                  knowledgeBaseId={fileKnowledgeBaseFilter !== 'all' ? (
                    console.log('传递知识库ID筛选:', fileKnowledgeBaseFilter),
                    fileKnowledgeBaseFilter
                  ) : undefined}
                  knowledgeBaseType={fileKnowledgeBaseTypeFilter !== 'all' ? (
                    console.log('传递知识库类型筛选:', fileKnowledgeBaseTypeFilter === '系统' ? 'system' : 'user'),
                    fileKnowledgeBaseTypeFilter === '系统' ? 'system' : 'user'
                  ) : undefined}
                  sizeMin={fileSizeFilter === 'medium' ? (
                    console.log('传递最小文件大小筛选:', formatFileSize(2 * 1024 * 1024)),
                    2 * 1024 * 1024
                  ) : fileSizeFilter === 'large' ? (
                    console.log('传递最小文件大小筛选:', formatFileSize(10 * 1024 * 1024)),
                    10 * 1024 * 1024
                  ) : undefined}
                  sizeMax={fileSizeFilter === 'small' ? (
                    console.log('传递最大文件大小筛选:', formatFileSize(2 * 1024 * 1024)),
                    2 * 1024 * 1024
                  ) : fileSizeFilter === 'medium' ? (
                    console.log('传递最大文件大小筛选:', formatFileSize(10 * 1024 * 1024)),
                    10 * 1024 * 1024
                  ) : undefined}
                  searchQuery={fileSearchQuery}
                  hidePageControls={false}
                />

                {/* 分页控件已移除，使用文件列表组件中的分页控件 */}
              </div>
            </TabsContent>
          </Tabs>
        </Card>



        {/* 创建知识库对话框 */}
        {showCreateKnowledgeBaseDialog && (
          <Dialog open={showCreateKnowledgeBaseDialog} onOpenChange={setShowCreateKnowledgeBaseDialog}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="text-xl">创建知识库</DialogTitle>
                <DialogDescription>填写知识库信息，创建新的知识库</DialogDescription>
              </DialogHeader>

              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">知识库名称</label>
                  <input
                    type="text"
                    value={newKnowledgeBaseName}
                    onChange={(e) => setNewKnowledgeBaseName(e.target.value)}
                    placeholder="输入知识库名称"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">知识库描述</label>
                  <textarea
                    value={newKnowledgeBaseDescription}
                    onChange={(e) => setNewKnowledgeBaseDescription(e.target.value)}
                    placeholder="输入知识库描述（可选）"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent h-24 resize-none"
                  ></textarea>
                </div>

                {/* Add the new contact person field */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">联系人</label>
                  <input
                    type="text"
                    value={contactPerson}
                    onChange={(e) => setContactPerson(e.target.value)}
                    placeholder="输入联系人姓名"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>

                {/* Add the new contact phone field */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">联系电话</label>
                  <input
                    type="tel"
                    value={contactPhone}
                    onChange={(e) => setContactPhone(e.target.value)}
                    placeholder="输入联系电话"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">知识库类型</label>
                  <RadioGroup
                    value={newKnowledgeBaseType}
                    onValueChange={(value) => setNewKnowledgeBaseType(value as "系统" | "用户")}
                  >
                    <div className="flex items-start space-x-2 p-3 rounded-md border border-gray-200 bg-gray-50 mb-2">
                      <RadioGroupItem value="用户" id="private-kb" />
                      <div className="grid gap-1.5">
                        <Label htmlFor="private-kb" className="font-medium">
                          用户知识库
                        </Label>
                        <p className="text-sm text-muted-foreground">仅您和授权用户可访问</p>
                      </div>
                    </div>
                    {isAdmin && (
                      <div className="flex items-start space-x-2 p-3 rounded-md border border-gray-200 bg-gray-50">
                        <RadioGroupItem value="系统" id="public-kb" />
                        <div className="grid gap-1.5">
                          <Label htmlFor="public-kb" className="font-medium">
                            系统知识库
                          </Label>
                          <p className="text-sm text-muted-foreground">所有用户可访问（仅管理员可创建）</p>
                        </div>
                      </div>
                    )}
                  </RadioGroup>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateKnowledgeBaseDialog(false)}>
                  取消
                </Button>
                <Button
                  className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                  onClick={handleCreateKnowledgeBaseSubmit}
                  disabled={!newKnowledgeBaseName}
                >
                  创建
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* 文件上传对话框 */}
        <KnowledgeFileUploadDialog
          isOpen={showUploadDialog}
          onClose={() => setShowUploadDialog(false)}
          systemKnowledgeBases={systemKnowledgeBases}
          userKnowledgeBases={userKnowledgeBases}
          onUploadComplete={handleUploadComplete}
          isAdmin={isAdmin}
        />

        {/* 文件详情对话框 */}
        {selectedFile && (
          <KnowledgeFileDetailDialog
            isOpen={showFileDetailDialog}
            onClose={() => setShowFileDetailDialog(false)}
            file={{
              id: selectedFile.id,
              name: selectedFile.name,
              knowledge_base_id: selectedFile.knowledgeBaseId,
              knowledge_base_name: selectedFile.knowledgeBaseName,
              file_size: selectedFile.size,
              file_type: selectedFile.type,
              creator_name: selectedFile.uploadedBy,
              created_at: selectedFile.uploadedAt,
              summary: "这是文件的摘要信息。",
              detailed_description: "这是文件的详细描述内容。此处应显示文件的详细内容或摘要信息。",
              status: "正常"
            }}
            onDownload={handleDownloadFile}
          />
        )}

        {/* 文件审核对话框 */}
        {selectedFile && (
          <KnowledgeFileReviewDialog
            isOpen={showFileReviewDialog}
            onClose={() => setShowFileReviewDialog(false)}
            file={{
              id: selectedFile.id,
              name: selectedFile.name,
              knowledge_base_id: selectedFile.knowledgeBaseId,
              knowledge_base_name: selectedFile.knowledgeBaseName,
              file_size: selectedFile.size,
              file_type: selectedFile.type,
              creator_name: selectedFile.uploadedBy,
              created_at: selectedFile.uploadedAt,
              summary: "这是文件的摘要信息。",
              detailed_description: "这是文件的详细描述内容。此处应显示文件的详细内容或摘要信息。",
              status: "待审核"
            }}
            onApprove={handleApproveFile}
            onReject={handleRejectFile}
            loading={reviewLoading}
          />
        )}

        {/* 访问请求对话框 */}
        {showAccessRequestDialog && selectedKnowledgeBase && (
          <Dialog open={showAccessRequestDialog} onOpenChange={setShowAccessRequestDialog}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="text-xl">请求访问权限</DialogTitle>
                <DialogDescription>
                  您正在请求访问知识库 "{selectedKnowledgeBase.name}"。请说明您的理由。
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">请求理由</label>
                  <textarea
                    value={accessRequestReason}
                    onChange={(e) => setAccessRequestReason(e.target.value)}
                    placeholder="请详细说明您需要访问该知识库的理由"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent h-24 resize-none"
                  ></textarea>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setShowAccessRequestDialog(false)}>
                  取消
                </Button>
                <Button
                  className="bg-blue-500 hover:bg-blue-600"
                  onClick={() => {
                    // 提交访问请求
                    if (!accessRequestReason.trim()) return;

                    console.log('开始提交访问申请:', { id: selectedKnowledgeBase?.id, reason: accessRequestReason });
                    setAccessRequestSubmitted(true);

                    // 获取token
                    const token = localStorage.getItem('hefamily_token');

                    // 直接调用API
                    fetch('/api/knowledge-access-requests', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token ? `Bearer ${token}` : ''
                      },
                      body: JSON.stringify({
                        knowledge_base_id: selectedKnowledgeBase?.id,
                        reason: accessRequestReason
                      })
                    })
                    .then(response => response.json())
                    .then(data => {
                      console.log('API响应:', data);

                      if (data.success) {
                        toast({
                          title: "申请已提交",
                          description: "您的访问申请已提交，等待知识库创建者审核",
                        });

                        setShowAccessRequestDialog(false);
                        setTimeout(() => {
                          setAccessRequestSubmitted(false);
                          setAccessRequestReason("");
                        }, 500);
                      } else {
                        setAccessRequestSubmitted(false);
                        toast({
                          variant: "destructive",
                          title: "申请失败",
                          description: data.message || "提交访问申请失败，请稍后再试",
                        });
                      }
                    })
                    .catch(error => {
                      console.error('API错误:', error);
                      setAccessRequestSubmitted(false);
                      toast({
                        variant: "destructive",
                        title: "申请失败",
                        description: "提交访问申请失败，请稍后再试",
                      });
                    });
                  }}
                  disabled={!accessRequestReason || accessRequestSubmitted}
                >
                  {accessRequestSubmitted ? "提交中..." : "提交请求"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* 删除确认对话框 */}
        {showDeleteConfirmDialog && selectedKnowledgeBase && (
          <Dialog open={showDeleteConfirmDialog} onOpenChange={setShowDeleteConfirmDialog}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="text-xl">确认删除</DialogTitle>
                <DialogDescription>
                  您确定要删除知识库 "{selectedKnowledgeBase.name}" 吗？此操作无法撤销。
                </DialogDescription>
              </DialogHeader>

              <DialogFooter>
                <Button variant="outline" onClick={() => setShowDeleteConfirmDialog(false)}>
                  取消
                </Button>
                <Button
                  className="bg-red-500 hover:bg-red-600"
                  onClick={async () => {
                    try {
                      // 检查知识库是否有文件
                      const files = await getKnowledgeBaseFiles(selectedKnowledgeBase.id);
                      if (files && files.files && files.files.length > 0) {
                        toast({
                          variant: "destructive",
                          title: "无法删除",
                          description: "知识库中还有文件，请先删除所有文件后再尝试删除知识库",
                        });
                        setShowDeleteConfirmDialog(false);
                        return;
                      }

                      // 调用API删除知识库
                      console.log('开始删除知识库, ID:', selectedKnowledgeBase.id);
                      const response = await deleteKnowledgeBase(selectedKnowledgeBase.id);
                      console.log('删除知识库响应:', response);

                      // 更新前端状态
                      if (selectedKnowledgeBase.type === "系统") {
                        setSystemKnowledgeBases(systemKnowledgeBases.filter(
                          (kb) => kb.id !== selectedKnowledgeBase.id
                        ));
                      } else {
                        setUserKnowledgeBases(userKnowledgeBases.filter(
                          (kb) => kb.id !== selectedKnowledgeBase.id
                        ));
                      }

                      // 显示成功消息
                      setSuccessMessage(`已成功删除知识库 "${selectedKnowledgeBase.name}"。`);
                      setShowSuccessNotification(true);

                      // 重新加载数据
                      loadData();
                    } catch (error: any) {
                      console.error('删除知识库失败:', error);

                      // 显示具体的错误信息
                      let errorMessage = "删除知识库失败，请稍后再试";

                      if (error.message) {
                        errorMessage = error.message;
                      } else if (error.response && error.response.data && error.response.data.message) {
                        errorMessage = error.response.data.message;
                      }

                      toast({
                        variant: "destructive",
                        title: "删除失败",
                        description: errorMessage,
                      });
                    } finally {
                      // 重置状态并关闭对话框
                      setShowDeleteConfirmDialog(false);
                    }
                  }}
                >
                  确认删除
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
          </>
        )}
      </div>
    </div>
  )
}
