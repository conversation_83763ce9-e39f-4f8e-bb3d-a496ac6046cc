"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { User, Mail, Lock, ArrowLeft, Smartphone, Bell } from "lucide-react"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function AccountSettings() {
  const [userData, setUserData] = useState({
    username: "张三",
    email: "<EMAIL>",
    phone: "138****8000",
    avatar: "/vibrant-street-market.png",
  })

  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    push: true,
    system: true,
    activity: false,
  })

  // 对话框状态
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false)
  const [isPhoneDialogOpen, setIsPhoneDialogOpen] = useState(false)
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false)

  // 表单状态
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [newPhone, setNewPhone] = useState("")
  const [phoneVerifyCode, setPhoneVerifyCode] = useState("")
  const [newEmail, setNewEmail] = useState("")
  const [emailVerifyCode, setEmailVerifyCode] = useState("")

  const handleNotificationChange = (key: keyof typeof notifications) => {
    setNotifications((prev) => ({
      ...prev,
      [key]: !prev[key],
    }))
  }

  // 处理密码修改
  const handlePasswordSubmit = () => {
    // 这里添加密码修改逻辑
    alert("密码修改成功！")
    setIsPasswordDialogOpen(false)
    setCurrentPassword("")
    setNewPassword("")
    setConfirmPassword("")
  }

  // 处理手机绑定修改
  const handlePhoneSubmit = () => {
    // 这里添加手机绑定修改逻辑
    alert("手机绑定已更新！")
    setIsPhoneDialogOpen(false)
    setNewPhone("")
    setPhoneVerifyCode("")
  }

  // 处理邮箱绑定修改
  const handleEmailSubmit = () => {
    // 这里添加邮箱绑定修改逻辑
    alert("邮箱绑定已更新！")
    setIsEmailDialogOpen(false)
    setNewEmail("")
    setEmailVerifyCode("")
  }

  // 发送验证码
  const sendVerifyCode = (type: string) => {
    alert(`验证码已发送到您${type === "phone" ? "的手机" : "的邮箱"}！`)
  }

  return (
    <div className="min-h-screen bg-[#fdf9f1]">
      <Navbar />
      <main className="pt-20 pb-10">
        <div className="container mx-auto px-4">
          <div className="flex items-center mb-6">
            <Link href="/personal-center" className="mr-4">
              <Button variant="outline" size="sm" className="gap-1">
                <ArrowLeft className="h-4 w-4" />
                返回
              </Button>
            </Link>
            <h1 className="text-2xl font-bold text-[#1e7a43]">账号设置</h1>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* 侧边栏 */}
            <div className="md:col-span-1">
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col items-center">
                    <div className="w-24 h-24 rounded-full overflow-hidden mb-4">
                      <Image
                        src={userData.avatar || "/placeholder.svg"}
                        alt="用户头像"
                        width={96}
                        height={96}
                        className="object-cover"
                      />
                    </div>
                    <h2 className="text-xl font-bold mb-1">{userData.username}</h2>
                    <p className="text-gray-500 text-sm mb-4">普通用户</p>
                    <div className="w-full space-y-2">
                      <Link href="/personal-center">
                        <Button variant="outline" className="w-full justify-start">
                          <User className="mr-2 h-4 w-4" />
                          个人资料
                        </Button>
                      </Link>
                      <Link href="/personal-center/settings">
                        <Button variant="outline" className="w-full justify-start bg-gray-100">
                          <Mail className="mr-2 h-4 w-4" />
                          账号设置
                        </Button>
                      </Link>
                      <Link href="/personal-center/collections">
                        <Button variant="outline" className="w-full justify-start">
                          <Bell className="mr-2 h-4 w-4" />
                          我的收藏
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 主内容区 */}
            <div className="md:col-span-3">
              <div className="space-y-6">
                {/* 安全设置 */}
                <Card>
                  <CardHeader>
                    <CardTitle>安全设置</CardTitle>
                    <CardDescription>管理您的账号安全和登录方式</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between py-3 border-b">
                        <div className="flex items-start">
                          <Lock className="h-5 w-5 text-[#1e7a43] mr-3 mt-0.5" />
                          <div>
                            <p className="font-medium">登录密码</p>
                            <p className="text-sm text-gray-500">定期更改密码可以提高账号安全性</p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          className="text-[#1e7a43] border-[#1e7a43]"
                          onClick={() => setIsPasswordDialogOpen(true)}
                        >
                          修改
                        </Button>
                      </div>

                      <div className="flex items-center justify-between py-3 border-b">
                        <div className="flex items-start">
                          <Smartphone className="h-5 w-5 text-[#1e7a43] mr-3 mt-0.5" />
                          <div>
                            <p className="font-medium">手机绑定</p>
                            <p className="text-sm text-gray-500">已绑定：{userData.phone}</p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          className="text-[#1e7a43] border-[#1e7a43]"
                          onClick={() => setIsPhoneDialogOpen(true)}
                        >
                          更换
                        </Button>
                      </div>

                      <div className="flex items-center justify-between py-3">
                        <div className="flex items-start">
                          <Mail className="h-5 w-5 text-[#1e7a43] mr-3 mt-0.5" />
                          <div>
                            <p className="font-medium">邮箱绑定</p>
                            <p className="text-sm text-gray-500">已绑定：{userData.email}</p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          className="text-[#1e7a43] border-[#1e7a43]"
                          onClick={() => setIsEmailDialogOpen(true)}
                        >
                          更换
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 通知设置 */}
                <Card>
                  <CardHeader>
                    <CardTitle>通知设置</CardTitle>
                    <CardDescription>管理您接收的通知类型和方式</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between py-3 border-b">
                        <div className="flex items-start">
                          <Bell className="h-5 w-5 text-[#1e7a43] mr-3 mt-0.5" />
                          <div>
                            <p className="font-medium">电子邮件通知</p>
                            <p className="text-sm text-gray-500">接收重要更新和活动通知</p>
                          </div>
                        </div>
                        <Switch
                          checked={notifications.email}
                          onCheckedChange={() => handleNotificationChange("email")}
                          aria-label="电子邮件通知开关"
                        />
                      </div>

                      <div className="flex items-center justify-between py-3 border-b">
                        <div className="flex items-start">
                          <Bell className="h-5 w-5 text-[#1e7a43] mr-3 mt-0.5" />
                          <div>
                            <p className="font-medium">短信通知</p>
                            <p className="text-sm text-gray-500">接收账号安全和重要提醒</p>
                          </div>
                        </div>
                        <Switch
                          checked={notifications.sms}
                          onCheckedChange={() => handleNotificationChange("sms")}
                          aria-label="短信通知开关"
                        />
                      </div>

                      <div className="flex items-center justify-between py-3 border-b">
                        <div className="flex items-start">
                          <Bell className="h-5 w-5 text-[#1e7a43] mr-3 mt-0.5" />
                          <div>
                            <p className="font-medium">系统消息</p>
                            <p className="text-sm text-gray-500">接收平台公告和系统更新</p>
                          </div>
                        </div>
                        <Switch
                          checked={notifications.system}
                          onCheckedChange={() => handleNotificationChange("system")}
                          aria-label="系统消息开关"
                        />
                      </div>

                      <div className="flex items-center justify-between py-3">
                        <div className="flex items-start">
                          <Bell className="h-5 w-5 text-[#1e7a43] mr-3 mt-0.5" />
                          <div>
                            <p className="font-medium">活动提醒</p>
                            <p className="text-sm text-gray-500">接收研究活动和新内容提醒</p>
                          </div>
                        </div>
                        <Switch
                          checked={notifications.activity}
                          onCheckedChange={() => handleNotificationChange("activity")}
                          aria-label="活动提醒开关"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />

      {/* 修改密码对话框 */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>修改登录密码</DialogTitle>
            <DialogDescription>请输入您的当前密码和新密码，新密码需包含字母、数字和特殊字符。</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="current-password">当前密码</Label>
              <Input
                id="current-password"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="new-password">新密码</Label>
              <Input
                id="new-password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="confirm-password">确认新密码</Label>
              <Input
                id="confirm-password"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPasswordDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handlePasswordSubmit}>确认修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 更换手机绑定对话框 */}
      <Dialog open={isPhoneDialogOpen} onOpenChange={setIsPhoneDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>更换手机绑定</DialogTitle>
            <DialogDescription>请输入新的手机号码并验证。</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="new-phone">新手机号码</Label>
              <Input id="new-phone" type="tel" value={newPhone} onChange={(e) => setNewPhone(e.target.value)} />
            </div>
            <div className="grid grid-cols-3 gap-2">
              <div className="col-span-2">
                <Label htmlFor="phone-verify-code">验证码</Label>
                <Input
                  id="phone-verify-code"
                  value={phoneVerifyCode}
                  onChange={(e) => setPhoneVerifyCode(e.target.value)}
                />
              </div>
              <div className="flex items-end">
                <Button variant="outline" className="w-full" onClick={() => sendVerifyCode("phone")}>
                  获取验证码
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPhoneDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handlePhoneSubmit}>确认更换</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 更换邮箱绑定对话框 */}
      <Dialog open={isEmailDialogOpen} onOpenChange={setIsEmailDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>更换邮箱绑定</DialogTitle>
            <DialogDescription>请输入新的邮箱地址并验证。</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="new-email">新邮箱地址</Label>
              <Input id="new-email" type="email" value={newEmail} onChange={(e) => setNewEmail(e.target.value)} />
            </div>
            <div className="grid grid-cols-3 gap-2">
              <div className="col-span-2">
                <Label htmlFor="email-verify-code">验证码</Label>
                <Input
                  id="email-verify-code"
                  value={emailVerifyCode}
                  onChange={(e) => setEmailVerifyCode(e.target.value)}
                />
              </div>
              <div className="flex items-end">
                <Button variant="outline" className="w-full" onClick={() => sendVerifyCode("email")}>
                  获取验证码
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEmailDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEmailSubmit}>确认更换</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
