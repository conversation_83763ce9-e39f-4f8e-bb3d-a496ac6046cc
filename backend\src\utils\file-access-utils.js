const { Role, KnowledgeBaseAccess } = require('../models');

/**
 * 过滤用户有权限访问的文件
 * @param {Array} files - 文件列表
 * @param {Object} user - 用户信息
 * @returns {Array} 用户有权限访问的文件列表
 */
async function filterAccessibleFiles(files, user) {
  // 获取用户角色
  const userRole = await Role.findByPk(user.role_id);
  
  // 管理员可以访问所有文件
  if (userRole.name === '管理员') {
    return files;
  }
  
  // 获取用户有权限访问的知识库ID列表
  const accessibleKnowledgeBases = await KnowledgeBaseAccess.findAll({
    where: { user_id: user.id },
    attributes: ['knowledge_base_id']
  });
  
  const accessibleKnowledgeBaseIds = accessibleKnowledgeBases.map(access => access.knowledge_base_id);
  
  // 过滤用户有权限访问的文件
  return files.filter(file => {
    // 系统知识库的文件所有用户都可以访问
    if (file.KnowledgeBase.type === 'system') {
      return true;
    }
    
    // 用户知识库的文件只有创建者和被授权用户可以访问
    if (file.KnowledgeBase.type === 'user') {
      // 用户是知识库创建者
      if (file.KnowledgeBase.creator_id === user.id) {
        return true;
      }
      
      // 用户被授权访问该知识库
      if (accessibleKnowledgeBaseIds.includes(file.knowledge_base_id)) {
        return true;
      }
    }
    
    return false;
  });
}

module.exports = {
  filterAccessibleFiles
};
