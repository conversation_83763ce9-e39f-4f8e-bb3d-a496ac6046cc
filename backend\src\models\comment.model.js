/**
 * 评论模型
 * 
 * 定义评论数据结构，包括评论内容、状态等信息
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} Comment模型
 */
module.exports = (sequelize, DataTypes) => {
  const Comment = sequelize.define('Comment', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '评论者ID'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    topic_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '评论主题ID'
    },
    topic_type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '评论主题类型，如personal_topic, activity等'
    },
    topic_title: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '评论主题标题'
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected'),
      allowNull: false,
      defaultValue: 'pending',
      comment: '评论状态：待审核、已批准、已拒绝'
    },
    reviewer_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '审核人ID'
    },
    review_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '审核时间'
    },
    reject_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '拒绝原因'
    }
  }, {
    tableName: 'comments',
    timestamps: true
  });

  // 关联关系
  Comment.associate = (models) => {
    // 评论与用户的多对一关系
    Comment.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });

    // 评论与审核者的多对一关系
    Comment.belongsTo(models.User, {
      foreignKey: 'reviewer_id',
      as: 'reviewer'
    });
  };

  return Comment;
};
