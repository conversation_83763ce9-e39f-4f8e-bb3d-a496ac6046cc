/**
 * 展板图片模型
 * 
 * 用于存储首页、家族专题和个人专题的展板图片信息
 */
module.exports = (sequelize, DataTypes) => {
  const ExhibitionBoard = sequelize.define('ExhibitionBoard', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '展板ID'
    },
    type: {
      type: DataTypes.ENUM('home', 'family', 'personal'),
      allowNull: false,
      comment: '展板类型：home-首页, family-家族专题, personal-个人专题'
    },
    sub_type: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '子类型，如个人专题中的具体人物ID'
    },
    image_url: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '图片URL'
    },
    title: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '标题'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '描述'
    },
    button_text: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '按钮文本'
    },
    button_link: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '按钮链接'
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '排序顺序'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      allowNull: false,
      defaultValue: 'active',
      comment: '状态：active-启用, inactive-禁用'
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '创建者ID'
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '更新者ID'
    }
  }, {
    tableName: 'exhibition_boards',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  // 关联
  ExhibitionBoard.associate = (models) => {
    // 创建者关联
    ExhibitionBoard.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator'
    });

    // 更新者关联
    ExhibitionBoard.belongsTo(models.User, {
      foreignKey: 'updated_by',
      as: 'updater'
    });
  };

  return ExhibitionBoard;
};
