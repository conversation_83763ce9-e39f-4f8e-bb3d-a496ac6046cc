/**
 * 活动详情组件
 * 
 * 用于展示活动详情
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from '@/components/ui/use-toast'
import { Activity } from './ActivityList'

// 活动详情组件属性
interface ActivityDetailProps {
  activity: Activity
  onBack: () => void
}

// 活动详情组件
export const ActivityDetail: React.FC<ActivityDetailProps> = ({ activity, onBack }) => {
  const [isJoining, setIsJoining] = useState(false)
  const [hasJoined, setHasJoined] = useState(false)

  // 获取状态标签
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'upcoming':
        return '即将开始'
      case 'ongoing':
        return '进行中'
      case 'completed':
        return '已结束'
      case 'cancelled':
        return '已取消'
      default:
        return status
    }
  }

  // 获取状态类名
  const getStatusClassName = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800'
      case 'ongoing':
        return 'bg-green-100 text-green-800'
      case 'completed':
        return 'bg-gray-100 text-gray-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 处理参加活动
  const handleJoinActivity = async () => {
    setIsJoining(true)
    
    try {
      // 模拟API调用
      const response = await fetch(`/api/activities/${activity.id}/join`, {
        method: 'POST'
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.message || '参加活动失败')
      }
      
      setHasJoined(true)
      
      toast({
        title: '参加成功',
        description: '您已成功参加活动'
      })
    } catch (err) {
      toast({
        title: '参加失败',
        description: err instanceof Error ? err.message : '参加活动失败',
        variant: 'destructive'
      })
    } finally {
      setIsJoining(false)
    }
  }

  // 判断是否可以参加活动
  const canJoinActivity = activity.status === 'upcoming' || activity.status === 'ongoing'

  return (
    <div>
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={onBack} className="mr-2">返回</Button>
        <h1 className="text-3xl font-bold">{activity.title}</h1>
      </div>
      
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{activity.title}</CardTitle>
              <CardDescription>
                创建者: {activity.created_by_name} | 创建时间: {new Date(activity.created_at).toLocaleString()}
              </CardDescription>
            </div>
            <div className={`px-3 py-1 rounded ${getStatusClassName(activity.status)}`}>
              {getStatusLabel(activity.status)}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">活动详情</h3>
            <div className="whitespace-pre-wrap">{activity.content}</div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-lg font-medium mb-2">活动地点</h3>
              <p>{activity.location}</p>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">参与人数</h3>
              <p>{activity.participants_count} 人</p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-lg font-medium mb-2">开始时间</h3>
              <p>{new Date(activity.start_time).toLocaleString()}</p>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">结束时间</h3>
              <p>{new Date(activity.end_time).toLocaleString()}</p>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {canJoinActivity && !hasJoined ? (
            <Button 
              onClick={handleJoinActivity} 
              disabled={isJoining}
            >
              {isJoining ? '参加中...' : '参加活动'}
            </Button>
          ) : hasJoined ? (
            <Button variant="outline" disabled>已参加</Button>
          ) : (
            <Button variant="outline" disabled>活动{activity.status === 'completed' ? '已结束' : '已取消'}</Button>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}

export default ActivityDetail
