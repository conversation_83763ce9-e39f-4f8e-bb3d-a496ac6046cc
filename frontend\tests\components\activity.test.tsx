/**
 * 活动组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { ActivityList } from '@/components/activity/ActivityList'
import { ActivityDetail } from '@/components/activity/ActivityDetail'
import { ActivityForm } from '@/components/activity/ActivityForm'
import { mockFetch, resetMockFetch } from '../test-utils'

// 模拟活动数据
const mockActivities = [
  {
    id: 1,
    title: '家族历史研讨会',
    date: '2023-12-01',
    description: '探讨家族历史研究方法和成果',
    status: 'published',
    creator: {
      id: 1,
      username: 'admin'
    },
    created_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 2,
    title: '文物保护讲座',
    date: '2023-12-15',
    description: '家族文物的保护和修复技术介绍',
    status: 'published',
    creator: {
      id: 1,
      username: 'admin'
    },
    created_at: '2023-01-02T00:00:00Z'
  },
  {
    id: 3,
    title: '未发布的活动',
    date: '2023-12-31',
    description: '这是一个未发布的活动',
    status: 'draft',
    creator: {
      id: 1,
      username: 'admin'
    },
    created_at: '2023-01-03T00:00:00Z'
  }
]

// 模拟活动详情
const mockActivityDetail = {
  id: 1,
  title: '家族历史研讨会',
  date: '2023-12-01',
  description: '探讨家族历史研究方法和成果',
  content: '这是活动的详细内容，包括活动安排、地点、参与方式等信息。\n\n活动将邀请多位家族史研究专家进行分享，并设有互动环节。',
  status: 'published',
  creator: {
    id: 1,
    username: 'admin'
  },
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
}

describe('活动组件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch()
    jest.clearAllMocks()
  })

  // 测试活动列表组件
  describe('ActivityList组件', () => {
    test('应该正确渲染活动列表', () => {
      render(
        <ActivityList
          activities={mockActivities}
          isLoading={false}
          isAdmin={false}
          onView={jest.fn()}
          onCreate={jest.fn()}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      // 验证活动标题
      expect(screen.getByText('家族历史研讨会')).toBeInTheDocument()
      expect(screen.getByText('文物保护讲座')).toBeInTheDocument()
      
      // 普通用户不应该看到未发布的活动
      expect(screen.queryByText('未发布的活动')).not.toBeInTheDocument()
      
      // 验证活动描述
      expect(screen.getByText('探讨家族历史研究方法和成果')).toBeInTheDocument()
      expect(screen.getByText('家族文物的保护和修复技术介绍')).toBeInTheDocument()
      
      // 验证活动日期
      expect(screen.getByText('2023-12-01')).toBeInTheDocument()
      expect(screen.getByText('2023-12-15')).toBeInTheDocument()
      
      // 验证查看按钮
      expect(screen.getAllByText('查看详情').length).toBe(2)
    })

    test('管理员应该能看到所有活动和管理按钮', () => {
      render(
        <ActivityList
          activities={mockActivities}
          isLoading={false}
          isAdmin={true}
          onView={jest.fn()}
          onCreate={jest.fn()}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      // 验证所有活动都可见
      expect(screen.getByText('家族历史研讨会')).toBeInTheDocument()
      expect(screen.getByText('文物保护讲座')).toBeInTheDocument()
      expect(screen.getByText('未发布的活动')).toBeInTheDocument()
      
      // 验证管理按钮
      expect(screen.getByText('创建活动')).toBeInTheDocument()
      expect(screen.getAllByText('编辑').length).toBe(3)
      expect(screen.getAllByText('删除').length).toBe(3)
    })

    test('应该显示加载状态', () => {
      render(
        <ActivityList
          activities={[]}
          isLoading={true}
          isAdmin={false}
          onView={jest.fn()}
          onCreate={jest.fn()}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      expect(screen.getByText('加载中...')).toBeInTheDocument()
    })

    test('应该显示空活动提示', () => {
      render(
        <ActivityList
          activities={[]}
          isLoading={false}
          isAdmin={false}
          onView={jest.fn()}
          onCreate={jest.fn()}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      expect(screen.getByText('暂无活动')).toBeInTheDocument()
    })

    test('应该调用查看函数', () => {
      const handleView = jest.fn()
      
      render(
        <ActivityList
          activities={mockActivities}
          isLoading={false}
          isAdmin={false}
          onView={handleView}
          onCreate={jest.fn()}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      // 点击查看按钮
      const viewButtons = screen.getAllByText('查看详情')
      fireEvent.click(viewButtons[0])
      
      // 验证查看函数被调用
      expect(handleView).toHaveBeenCalledWith(mockActivities[0].id)
    })

    test('应该调用创建函数', () => {
      const handleCreate = jest.fn()
      
      render(
        <ActivityList
          activities={mockActivities}
          isLoading={false}
          isAdmin={true}
          onView={jest.fn()}
          onCreate={handleCreate}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      // 点击创建按钮
      const createButton = screen.getByText('创建活动')
      fireEvent.click(createButton)
      
      // 验证创建函数被调用
      expect(handleCreate).toHaveBeenCalled()
    })

    test('应该调用编辑函数', () => {
      const handleEdit = jest.fn()
      
      render(
        <ActivityList
          activities={mockActivities}
          isLoading={false}
          isAdmin={true}
          onView={jest.fn()}
          onCreate={jest.fn()}
          onEdit={handleEdit}
          onDelete={jest.fn()}
        />
      )
      
      // 点击编辑按钮
      const editButtons = screen.getAllByText('编辑')
      fireEvent.click(editButtons[0])
      
      // 验证编辑函数被调用
      expect(handleEdit).toHaveBeenCalledWith(mockActivities[0].id)
    })

    test('应该调用删除函数', () => {
      const handleDelete = jest.fn()
      
      render(
        <ActivityList
          activities={mockActivities}
          isLoading={false}
          isAdmin={true}
          onView={jest.fn()}
          onCreate={jest.fn()}
          onEdit={jest.fn()}
          onDelete={handleDelete}
        />
      )
      
      // 点击删除按钮
      const deleteButtons = screen.getAllByText('删除')
      fireEvent.click(deleteButtons[0])
      
      // 确认删除
      const confirmButton = screen.getByText('确认')
      fireEvent.click(confirmButton)
      
      // 验证删除函数被调用
      expect(handleDelete).toHaveBeenCalledWith(mockActivities[0].id)
    })
  })

  // 测试活动详情组件
  describe('ActivityDetail组件', () => {
    test('应该正确渲染活动详情', () => {
      render(
        <ActivityDetail
          activity={mockActivityDetail}
          isLoading={false}
          isAdmin={false}
          onBack={jest.fn()}
          onEdit={jest.fn()}
        />
      )
      
      // 验证活动标题
      expect(screen.getByText('家族历史研讨会')).toBeInTheDocument()
      
      // 验证活动日期
      expect(screen.getByText('2023-12-01')).toBeInTheDocument()
      
      // 验证活动描述
      expect(screen.getByText('探讨家族历史研究方法和成果')).toBeInTheDocument()
      
      // 验证活动内容
      expect(screen.getByText(/这是活动的详细内容/)).toBeInTheDocument()
      expect(screen.getByText(/活动将邀请多位家族史研究专家进行分享/)).toBeInTheDocument()
      
      // 验证返回按钮
      expect(screen.getByText('返回列表')).toBeInTheDocument()
    })

    test('管理员应该能看到编辑按钮', () => {
      render(
        <ActivityDetail
          activity={mockActivityDetail}
          isLoading={false}
          isAdmin={true}
          onBack={jest.fn()}
          onEdit={jest.fn()}
        />
      )
      
      // 验证编辑按钮
      expect(screen.getByText('编辑活动')).toBeInTheDocument()
    })

    test('应该显示加载状态', () => {
      render(
        <ActivityDetail
          activity={null}
          isLoading={true}
          isAdmin={false}
          onBack={jest.fn()}
          onEdit={jest.fn()}
        />
      )
      
      expect(screen.getByText('加载中...')).toBeInTheDocument()
    })

    test('应该调用返回函数', () => {
      const handleBack = jest.fn()
      
      render(
        <ActivityDetail
          activity={mockActivityDetail}
          isLoading={false}
          isAdmin={false}
          onBack={handleBack}
          onEdit={jest.fn()}
        />
      )
      
      // 点击返回按钮
      const backButton = screen.getByText('返回列表')
      fireEvent.click(backButton)
      
      // 验证返回函数被调用
      expect(handleBack).toHaveBeenCalled()
    })

    test('应该调用编辑函数', () => {
      const handleEdit = jest.fn()
      
      render(
        <ActivityDetail
          activity={mockActivityDetail}
          isLoading={false}
          isAdmin={true}
          onBack={jest.fn()}
          onEdit={handleEdit}
        />
      )
      
      // 点击编辑按钮
      const editButton = screen.getByText('编辑活动')
      fireEvent.click(editButton)
      
      // 验证编辑函数被调用
      expect(handleEdit).toHaveBeenCalledWith(mockActivityDetail.id)
    })
  })

  // 测试活动表单组件
  describe('ActivityForm组件', () => {
    test('应该正确渲染创建活动表单', () => {
      render(
        <ActivityForm
          activity={null}
          isLoading={false}
          onSubmit={jest.fn()}
          onCancel={jest.fn()}
        />
      )
      
      // 验证表单标题
      expect(screen.getByText('创建活动')).toBeInTheDocument()
      
      // 验证表单字段
      expect(screen.getByLabelText('活动标题')).toBeInTheDocument()
      expect(screen.getByLabelText('活动日期')).toBeInTheDocument()
      expect(screen.getByLabelText('活动描述')).toBeInTheDocument()
      expect(screen.getByLabelText('活动内容')).toBeInTheDocument()
      expect(screen.getByLabelText('状态')).toBeInTheDocument()
      
      // 验证按钮
      expect(screen.getByRole('button', { name: '保存' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument()
    })

    test('应该正确渲染编辑活动表单', () => {
      render(
        <ActivityForm
          activity={mockActivityDetail}
          isLoading={false}
          onSubmit={jest.fn()}
          onCancel={jest.fn()}
        />
      )
      
      // 验证表单标题
      expect(screen.getByText('编辑活动')).toBeInTheDocument()
      
      // 验证表单字段值
      expect(screen.getByLabelText('活动标题')).toHaveValue('家族历史研讨会')
      expect(screen.getByLabelText('活动日期')).toHaveValue('2023-12-01')
      expect(screen.getByLabelText('活动描述')).toHaveValue('探讨家族历史研究方法和成果')
      expect(screen.getByLabelText('活动内容')).toHaveValue('这是活动的详细内容，包括活动安排、地点、参与方式等信息。\n\n活动将邀请多位家族史研究专家进行分享，并设有互动环节。')
    })

    test('应该显示加载状态', () => {
      render(
        <ActivityForm
          activity={null}
          isLoading={true}
          onSubmit={jest.fn()}
          onCancel={jest.fn()}
        />
      )
      
      expect(screen.getByText('加载中...')).toBeInTheDocument()
    })

    test('应该调用提交函数', async () => {
      const handleSubmit = jest.fn().mockResolvedValue({
        id: 4,
        title: '新活动',
        date: '2023-12-31',
        description: '新活动描述',
        content: '新活动内容',
        status: 'draft',
        created_at: new Date().toISOString()
      })
      
      render(
        <ActivityForm
          activity={null}
          isLoading={false}
          onSubmit={handleSubmit}
          onCancel={jest.fn()}
        />
      )
      
      // 输入活动信息
      const titleInput = screen.getByLabelText('活动标题')
      fireEvent.change(titleInput, { target: { value: '新活动' } })
      
      const dateInput = screen.getByLabelText('活动日期')
      fireEvent.change(dateInput, { target: { value: '2023-12-31' } })
      
      const descriptionInput = screen.getByLabelText('活动描述')
      fireEvent.change(descriptionInput, { target: { value: '新活动描述' } })
      
      const contentInput = screen.getByLabelText('活动内容')
      fireEvent.change(contentInput, { target: { value: '新活动内容' } })
      
      // 点击保存按钮
      const saveButton = screen.getByRole('button', { name: '保存' })
      fireEvent.click(saveButton)
      
      // 验证提交函数被调用
      expect(handleSubmit).toHaveBeenCalledWith({
        title: '新活动',
        date: '2023-12-31',
        description: '新活动描述',
        content: '新活动内容',
        status: 'draft'
      })
      
      // 等待提交完成
      await waitFor(() => {
        expect(screen.getByText('活动保存成功')).toBeInTheDocument()
      })
    })

    test('应该调用取消函数', () => {
      const handleCancel = jest.fn()
      
      render(
        <ActivityForm
          activity={null}
          isLoading={false}
          onSubmit={jest.fn()}
          onCancel={handleCancel}
        />
      )
      
      // 点击取消按钮
      const cancelButton = screen.getByRole('button', { name: '取消' })
      fireEvent.click(cancelButton)
      
      // 验证取消函数被调用
      expect(handleCancel).toHaveBeenCalled()
    })

    test('应该验证表单输入', () => {
      render(
        <ActivityForm
          activity={null}
          isLoading={false}
          onSubmit={jest.fn()}
          onCancel={jest.fn()}
        />
      )
      
      // 不输入任何内容，直接点击保存按钮
      const saveButton = screen.getByRole('button', { name: '保存' })
      fireEvent.click(saveButton)
      
      // 验证错误提示
      expect(screen.getByText('活动标题不能为空')).toBeInTheDocument()
      expect(screen.getByText('活动日期不能为空')).toBeInTheDocument()
      expect(screen.getByText('活动描述不能为空')).toBeInTheDocument()
    })
  })
});
