/**
 * 表单验证工具函数
 *
 * 提供表单验证相关的工具函数
 */

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否有效
 */
export function validateEmail(email: string): boolean {
  if (!email) return false;

  // 更严格的邮箱验证正则表达式，不允许连续的点
  const emailRegex = /^[a-zA-Z0-9._%+-]+@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/;

  // 额外检查，确保@前后没有点，域名中没有连续的点
  if (email.includes('..') || email.startsWith('.') || email.endsWith('.') ||
      email.indexOf('@.') !== -1 || email.indexOf('.@') !== -1) {
    return false;
  }

  return emailRegex.test(email);
}

/**
 * 密码验证选项
 */
interface PasswordOptions {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumber: boolean;
  requireSpecial: boolean;
}

/**
 * 验证密码强度
 * @param password 密码
 * @param options 验证选项
 * @returns 是否有效
 */
export function validatePassword(
  password: string,
  options: PasswordOptions = {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumber: true,
    requireSpecial: true
  }
): boolean {
  if (!password) return false;

  // 检查长度
  if (password.length < options.minLength) return false;

  // 检查大写字母
  if (options.requireUppercase && !/[A-Z]/.test(password)) return false;

  // 检查小写字母
  if (options.requireLowercase && !/[a-z]/.test(password)) return false;

  // 检查数字
  if (options.requireNumber && !/[0-9]/.test(password)) return false;

  // 检查特殊字符
  if (options.requireSpecial && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) return false;

  return true;
}

/**
 * 验证中国手机号
 * @param phone 手机号
 * @returns 是否有效
 */
export function validatePhone(phone: string): boolean {
  if (!phone) return false;

  // 中国手机号格式：1开头，第二位是3-9，后面9位数字
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 用户名验证选项
 */
interface UsernameOptions {
  minLength: number;
  maxLength: number;
  allowUnderscore: boolean;
  allowDash: boolean;
}

/**
 * 验证用户名
 * @param username 用户名
 * @param options 验证选项
 * @returns 是否有效
 */
export function validateUsername(
  username: string,
  options: UsernameOptions = {
    minLength: 3,
    maxLength: 20,
    allowUnderscore: true,
    allowDash: true
  }
): boolean {
  if (!username) return false;

  // 检查长度
  if (username.length < options.minLength || username.length > options.maxLength) return false;

  // 构建正则表达式
  let pattern = '^[a-zA-Z0-9';
  if (options.allowUnderscore) pattern += '_';
  if (options.allowDash) pattern += '-';
  pattern += ']+$';

  const regex = new RegExp(pattern);
  return regex.test(username);
}

/**
 * 验证必填字段
 * @param value 字段值
 * @param errorMessage 错误消息
 * @returns 是否有效
 */
export function validateRequired(value: any, errorMessage = '此字段为必填项'): boolean {
  if (value === null || value === undefined || value === '') {
    return false;
  }
  return true;
}

/**
 * 验证字段长度
 * @param value 字段值
 * @param min 最小长度
 * @param max 最大长度
 * @param errorMessage 错误消息
 * @returns 是否有效
 */
export function validateLength(
  value: string,
  min: number,
  max: number,
  errorMessage = `长度必须在${min}-${max}个字符之间`
): boolean {
  if (!value) return min === 0;
  return value.length >= min && value.length <= max;
}

/**
 * 验证两个值是否匹配
 * @param value 当前值
 * @param compareValue 比较值
 * @param errorMessage 错误消息
 * @returns 是否匹配
 */
export function validateMatch(
  value: any,
  compareValue: any,
  errorMessage = '两次输入不一致'
): boolean {
  return value === compareValue;
}
