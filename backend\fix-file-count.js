const { sequelize, KnowledgeBase, File } = require('./src/models');

async function fixFileCount() {
  try {
    const knowledgeBases = await KnowledgeBase.findAll();
    console.log('开始修复知识库文件计数...');
    
    for (const kb of knowledgeBases) {
      const approvedFilesCount = await File.count({
        where: {
          knowledge_base_id: kb.id,
          status: 'approved'
        }
      });
      
      console.log(`知识库 ${kb.id} (${kb.name}): 当前文件计数=${kb.file_count}, 实际已批准文件数=${approvedFilesCount}`);
      
      if (kb.file_count !== approvedFilesCount) {
        await kb.update({
          file_count: approvedFilesCount
        });
        console.log(`已更新知识库 ${kb.id} 的文件计数为 ${approvedFilesCount}`);
      }
    }
    
    console.log('知识库文件计数修复完成');
    process.exit(0);
  } catch (error) {
    console.error('修复知识库文件计数失败:', error);
    process.exit(1);
  }
}

fixFileCount();
