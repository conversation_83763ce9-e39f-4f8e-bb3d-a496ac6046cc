/**
 * 创建AI助手表迁移文件
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('ai_assistants', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      type: {
        type: Sequelize.ENUM('personal', 'data-query', 'assistant', 'knowledge-file'),
        allowNull: false,
        comment: '助手类型：个人专题助手、数据查询助手、AI研究助手、知识库文件分析助手'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      api_key: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Dify API密钥'
      },
      api_endpoint: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Dify API端点'
      },
      app_id: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Dify应用ID'
      },
      app_code: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Dify应用代码'
      },
      upload_api_path: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '文件上传API路径（仅知识库文件分析助手使用）'
      },
      analysis_api_path: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '文件分析API路径（仅知识库文件分析助手使用）'
      },
      tags: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '助手标签，用于分类和筛选（仅AI研究助手使用）'
      },
      initial_message: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '初始对话消息'
      },
      is_system: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: '是否为系统预设助手，系统预设助手不可删除'
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive'),
        allowNull: false,
        defaultValue: 'active',
        comment: '助手状态：活跃、非活跃'
      },
      creator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      last_updated_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('ai_assistants');
  }
};
