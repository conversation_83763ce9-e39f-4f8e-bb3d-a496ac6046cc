'use strict';

/**
 * 角色权限初始化种子文件
 * 为管理员和初级访问者角色分配权限
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 获取所有权限
    const permissions = await queryInterface.sequelize.query(
      'SELECT id, code FROM permissions',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );
    
    // 创建权限映射
    const permissionMap = {};
    permissions.forEach(permission => {
      permissionMap[permission.code] = permission.id;
    });
    
    // 管理员角色权限（所有权限）
    const adminPermissions = permissions.map(permission => ({
      role_id: 1, // 管理员角色ID
      permission_id: permission.id,
      created_at: new Date(),
      updated_at: new Date()
    }));
    
    // 初级访问者角色权限
    const basicPermissions = [
      'content:view',
      'knowledge:view'
    ].map(code => ({
      role_id: 4, // 初级访问者角色ID
      permission_id: permissionMap[code],
      created_at: new Date(),
      updated_at: new Date()
    }));
    
    return queryInterface.bulkInsert('role_permissions', [
      ...adminPermissions,
      ...basicPermissions
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete('role_permissions', null, {});
  }
};
