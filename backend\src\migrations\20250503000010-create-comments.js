/**
 * 创建评论表迁移文件
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('comments', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: '评论者ID'
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      topic_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '评论主题ID'
      },
      topic_type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '评论主题类型，如personal_topic, activity等'
      },
      topic_title: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '评论主题标题'
      },
      status: {
        type: Sequelize.ENUM('pending', 'approved', 'rejected'),
        allowNull: false,
        defaultValue: 'pending',
        comment: '评论状态：待审核、已批准、已拒绝'
      },
      reviewer_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '审核人ID'
      },
      review_time: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '审核时间'
      },
      reject_reason: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '拒绝原因'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('comments');
  }
};
