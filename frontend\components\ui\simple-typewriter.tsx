"use client"

import React, { useState, useEffect, useRef } from 'react'

/**
 * 简单打字机效果组件
 *
 * 用于实现打字机效果的React组件
 * @param text 要显示的文本
 * @param className 自定义CSS类
 * @param speed 打字速度（毫秒/字符）
 * @param showCursor 是否显示光标
 */
export function SimpleTypewriter({
  text,
  className = "",
  speed = 30,
  showCursor = true
}: {
  text: string,
  className?: string,
  speed?: number,
  showCursor?: boolean
}) {
  const [displayText, setDisplayText] = useState("")
  const [isComplete, setIsComplete] = useState(false)
  const textRef = useRef(text)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // 当文本变化时重置状态
  useEffect(() => {
    if (text !== textRef.current) {
      textRef.current = text
      setDisplayText("")
      setIsComplete(false)

      // 清除现有定时器
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }

      // 开始打字效果
      let index = 0
      const typeNextChar = () => {
        if (index < text.length) {
          setDisplayText(prev => prev + text[index])
          index++
          timerRef.current = setTimeout(typeNextChar, speed)
        } else {
          setIsComplete(true)
        }
      }

      // 开始打字
      timerRef.current = setTimeout(typeNextChar, speed)
    }

    // 清理函数
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
    }
  }, [text, speed])

  // 将文本中的换行符转换为<br>标签
  const formattedText = displayText.split('\n').map((line, i, arr) => (
    <React.Fragment key={i}>
      {line}
      {i < arr.length - 1 && <br />}
    </React.Fragment>
  ))

  return (
    <div className={className}>
      {formattedText}
      {showCursor && !isComplete && (
        <span className="inline-block w-2 h-4 bg-gray-500 ml-1 animate-pulse"></span>
      )}
    </div>
  )
}
