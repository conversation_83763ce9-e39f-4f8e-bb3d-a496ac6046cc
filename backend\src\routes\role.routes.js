/**
 * 角色相关路由
 *
 * 处理角色的创建、查询、更新、删除等请求
 */

const express = require('express');
const router = express.Router();
const roleController = require('../controllers/role.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');

// 获取角色列表 (只需要认证，不需要特定权限)
router.get('/',
  authMiddleware,
  roleController.getRoles
);

// 获取角色详情 (需要认证和权限)
router.get('/:id',
  authMiddleware,
  checkPermission('role:manage'),
  roleController.getRoleById
);

// 创建角色 (需要认证和权限)
router.post('/',
  authMiddleware,
  checkPermission('role:manage'),
  roleController.createRole
);

// 更新角色 (需要认证和权限)
router.put('/:id',
  authMiddleware,
  checkPermission('role:manage'),
  roleController.updateRole
);

// 删除角色 (需要认证和权限)
router.delete('/:id',
  authMiddleware,
  checkPermission('role:manage'),
  roleController.deleteRole
);

// 获取角色权限 (需要认证和权限)
router.get('/:id/permissions',
  authMiddleware,
  checkPermission('role:manage'),
  roleController.getRolePermissions
);

// 更新角色权限 (需要认证和权限)
router.put('/:id/permissions',
  authMiddleware,
  checkPermission('role:manage'),
  roleController.updateRolePermissions
);

module.exports = router;
