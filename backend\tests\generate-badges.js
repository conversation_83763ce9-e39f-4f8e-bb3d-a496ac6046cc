/**
 * 测试覆盖率徽章生成脚本
 * 
 * 用于生成测试覆盖率徽章，可以嵌入到README.md中
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 徽章目录
const BADGES_DIR = path.join(__dirname, '..', 'badges');
const COVERAGE_SUMMARY_PATH = path.join(__dirname, '..', 'coverage', 'coverage-summary.json');

// 确保徽章目录存在
if (!fs.existsSync(BADGES_DIR)) {
  fs.mkdirSync(BADGES_DIR, { recursive: true });
}

// 运行测试并生成覆盖率报告
console.log('运行测试并生成覆盖率报告...');
try {
  execSync('npx jest --coverage', { stdio: 'inherit' });
} catch (error) {
  console.error('测试失败:', error.message);
  process.exit(1);
}

// 检查覆盖率报告是否存在
if (!fs.existsSync(COVERAGE_SUMMARY_PATH)) {
  console.error('覆盖率报告不存在');
  process.exit(1);
}

// 读取覆盖率报告
const coverageSummary = JSON.parse(fs.readFileSync(COVERAGE_SUMMARY_PATH, 'utf8'));
const total = coverageSummary.total;

// 生成徽章
console.log('\n生成覆盖率徽章...');

// 生成徽章颜色
function getBadgeColor(coverage) {
  if (coverage >= 90) return 'brightgreen';
  if (coverage >= 80) return 'green';
  if (coverage >= 70) return 'yellowgreen';
  if (coverage >= 60) return 'yellow';
  if (coverage >= 50) return 'orange';
  return 'red';
}

// 生成徽章URL
function generateBadgeUrl(label, coverage) {
  const color = getBadgeColor(coverage);
  return `https://img.shields.io/badge/${encodeURIComponent(label)}-${coverage}%25-${color}`;
}

// 生成徽章Markdown
function generateBadgeMarkdown(label, coverage) {
  const url = generateBadgeUrl(label, coverage);
  return `![${label}](${url})`;
}

// 生成徽章HTML
function generateBadgeHtml(label, coverage) {
  const url = generateBadgeUrl(label, coverage);
  return `<img src="${url}" alt="${label}">`;
}

// 生成徽章JSON
const badges = {
  lines: {
    label: 'Lines',
    coverage: total.lines.pct.toFixed(2),
    color: getBadgeColor(total.lines.pct)
  },
  functions: {
    label: 'Functions',
    coverage: total.functions.pct.toFixed(2),
    color: getBadgeColor(total.functions.pct)
  },
  branches: {
    label: 'Branches',
    coverage: total.branches.pct.toFixed(2),
    color: getBadgeColor(total.branches.pct)
  },
  statements: {
    label: 'Statements',
    coverage: total.statements.pct.toFixed(2),
    color: getBadgeColor(total.statements.pct)
  }
};

// 写入徽章JSON文件
fs.writeFileSync(path.join(BADGES_DIR, 'coverage-badges.json'), JSON.stringify(badges, null, 2));
console.log(`徽章JSON已生成: ${path.join(BADGES_DIR, 'coverage-badges.json')}`);

// 生成徽章Markdown
const badgeMarkdown = `# 测试覆盖率徽章

${generateBadgeMarkdown('Lines', total.lines.pct.toFixed(2))}
${generateBadgeMarkdown('Functions', total.functions.pct.toFixed(2))}
${generateBadgeMarkdown('Branches', total.branches.pct.toFixed(2))}
${generateBadgeMarkdown('Statements', total.statements.pct.toFixed(2))}
`;

// 写入徽章Markdown文件
fs.writeFileSync(path.join(BADGES_DIR, 'coverage-badges.md'), badgeMarkdown);
console.log(`徽章Markdown已生成: ${path.join(BADGES_DIR, 'coverage-badges.md')}`);

// 生成徽章HTML
const badgeHtml = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>测试覆盖率徽章</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    h1 {
      margin-bottom: 20px;
    }
    .badge {
      margin-right: 10px;
    }
  </style>
</head>
<body>
  <h1>测试覆盖率徽章</h1>
  <div>
    <span class="badge">${generateBadgeHtml('Lines', total.lines.pct.toFixed(2))}</span>
    <span class="badge">${generateBadgeHtml('Functions', total.functions.pct.toFixed(2))}</span>
    <span class="badge">${generateBadgeHtml('Branches', total.branches.pct.toFixed(2))}</span>
    <span class="badge">${generateBadgeHtml('Statements', total.statements.pct.toFixed(2))}</span>
  </div>
</body>
</html>
`;

// 写入徽章HTML文件
fs.writeFileSync(path.join(BADGES_DIR, 'coverage-badges.html'), badgeHtml);
console.log(`徽章HTML已生成: ${path.join(BADGES_DIR, 'coverage-badges.html')}`);

console.log('\n徽章生成完成');
