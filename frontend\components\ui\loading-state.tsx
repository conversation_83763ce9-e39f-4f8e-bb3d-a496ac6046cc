"use client"

import { Loader2, AlertCircle, CheckCircle } from "lucide-react"
import { cn } from "@/lib/utils"

/**
 * 加载状态组件
 * 
 * 用于显示加载中、成功和错误状态
 */
interface LoadingStateProps {
  status: "loading" | "success" | "error" | "idle"
  loadingText?: string
  successText?: string
  errorText?: string
  className?: string
  size?: "sm" | "md" | "lg"
  fullPage?: boolean
  onRetry?: () => void
}

export function LoadingState({
  status,
  loadingText = "加载中...",
  successText = "操作成功",
  errorText = "发生错误",
  className,
  size = "md",
  fullPage = false,
  onRetry
}: LoadingStateProps) {
  // 根据尺寸设置图标大小
  const iconSize = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  }

  // 根据尺寸设置文本大小
  const textSize = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg"
  }

  // 如果状态为idle，不显示任何内容
  if (status === "idle") return null

  // 全页面加载状态
  if (fullPage) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 z-50">
        <div className={cn("flex flex-col items-center justify-center p-6", className)}>
          {status === "loading" && (
            <>
              <Loader2 className={cn(iconSize[size], "animate-spin text-primary")} />
              <p className={cn(textSize[size], "mt-4 font-medium text-gray-700 dark:text-gray-300")}>{loadingText}</p>
            </>
          )}

          {status === "success" && (
            <>
              <CheckCircle className={cn(iconSize[size], "text-green-500")} />
              <p className={cn(textSize[size], "mt-4 font-medium text-gray-700 dark:text-gray-300")}>{successText}</p>
            </>
          )}

          {status === "error" && (
            <>
              <AlertCircle className={cn(iconSize[size], "text-red-500")} />
              <p className={cn(textSize[size], "mt-4 font-medium text-gray-700 dark:text-gray-300")}>{errorText}</p>
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
                >
                  重试
                </button>
              )}
            </>
          )}
        </div>
      </div>
    )
  }

  // 内联加载状态
  return (
    <div className={cn("flex items-center", className)}>
      {status === "loading" && (
        <>
          <Loader2 className={cn(iconSize[size], "animate-spin text-primary mr-2")} />
          <span className={cn(textSize[size], "text-gray-700 dark:text-gray-300")}>{loadingText}</span>
        </>
      )}

      {status === "success" && (
        <>
          <CheckCircle className={cn(iconSize[size], "text-green-500 mr-2")} />
          <span className={cn(textSize[size], "text-gray-700 dark:text-gray-300")}>{successText}</span>
        </>
      )}

      {status === "error" && (
        <>
          <AlertCircle className={cn(iconSize[size], "text-red-500 mr-2")} />
          <span className={cn(textSize[size], "text-gray-700 dark:text-gray-300")}>{errorText}</span>
          {onRetry && (
            <button
              onClick={onRetry}
              className="ml-2 text-primary hover:text-primary/90 text-sm font-medium"
            >
              重试
            </button>
          )}
        </>
      )}
    </div>
  )
}
