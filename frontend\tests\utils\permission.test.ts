/**
 * 权限检查函数测试
 */

import {
  hasPermission,
  isAdmin,
  canAccessPage,
  canPerformAction
} from '@/utils/permission';

// 模拟用户数据
const mockBasicUser = {
  id: 1,
  username: 'basicuser',
  role: 'basic_user',
  permissions: [
    'content:view',
    'comment:create',
    'knowledge_base:view'
  ]
};

const mockAdvancedUser = {
  id: 2,
  username: 'advanceduser',
  role: 'advanced_user',
  permissions: [
    'content:view',
    'content:create',
    'comment:create',
    'comment:edit',
    'knowledge_base:view',
    'knowledge_base:create'
  ]
};

const mockAdminUser = {
  id: 3,
  username: 'admin',
  role: 'admin',
  permissions: ['*'] // 管理员拥有所有权限
};

describe('权限检查函数', () => {
  // 测试hasPermission函数
  describe('hasPermission', () => {
    test('应该正确检查用户是否有指定权限', () => {
      // 基础用户
      expect(hasPermission(mockBasicUser, 'content:view')).toBe(true);
      expect(hasPermission(mockBasicUser, 'comment:create')).toBe(true);
      expect(hasPermission(mockBasicUser, 'content:create')).toBe(false);
      
      // 高级用户
      expect(hasPermission(mockAdvancedUser, 'content:create')).toBe(true);
      expect(hasPermission(mockAdvancedUser, 'comment:edit')).toBe(true);
      expect(hasPermission(mockAdvancedUser, 'content:delete')).toBe(false);
      
      // 管理员
      expect(hasPermission(mockAdminUser, 'content:view')).toBe(true);
      expect(hasPermission(mockAdminUser, 'content:create')).toBe(true);
      expect(hasPermission(mockAdminUser, 'content:edit')).toBe(true);
      expect(hasPermission(mockAdminUser, 'content:delete')).toBe(true);
      expect(hasPermission(mockAdminUser, 'system:manage')).toBe(true);
    });

    test('应该处理未登录用户', () => {
      expect(hasPermission(null, 'content:view')).toBe(false);
      expect(hasPermission(undefined, 'content:view')).toBe(false);
    });

    test('应该处理没有权限属性的用户', () => {
      const userWithoutPermissions = {
        id: 4,
        username: 'nopermissions',
        role: 'basic_user'
      };
      
      expect(hasPermission(userWithoutPermissions, 'content:view')).toBe(false);
    });

    test('应该支持检查多个权限', () => {
      // 基础用户
      expect(hasPermission(mockBasicUser, ['content:view', 'comment:create'])).toBe(true);
      expect(hasPermission(mockBasicUser, ['content:view', 'content:create'])).toBe(false);
      
      // 高级用户
      expect(hasPermission(mockAdvancedUser, ['content:view', 'content:create', 'comment:edit'])).toBe(true);
      expect(hasPermission(mockAdvancedUser, ['content:view', 'content:delete'])).toBe(false);
      
      // 管理员
      expect(hasPermission(mockAdminUser, ['content:view', 'content:create', 'system:manage'])).toBe(true);
    });

    test('应该支持requireAll参数', () => {
      // 基础用户
      expect(hasPermission(mockBasicUser, ['content:view', 'comment:create'], true)).toBe(true);
      expect(hasPermission(mockBasicUser, ['content:view', 'content:create'], true)).toBe(false);
      
      // 高级用户
      expect(hasPermission(mockAdvancedUser, ['content:view', 'content:create'], true)).toBe(true);
      expect(hasPermission(mockAdvancedUser, ['content:view', 'content:delete'], true)).toBe(false);
      
      // 默认requireAll为false，只需要满足其中一个权限
      expect(hasPermission(mockBasicUser, ['content:view', 'content:create'])).toBe(true);
      expect(hasPermission(mockBasicUser, ['content:edit', 'content:delete'])).toBe(false);
    });
  });

  // 测试isAdmin函数
  describe('isAdmin', () => {
    test('应该正确识别管理员用户', () => {
      expect(isAdmin(mockAdminUser)).toBe(true);
      expect(isAdmin(mockBasicUser)).toBe(false);
      expect(isAdmin(mockAdvancedUser)).toBe(false);
    });

    test('应该处理未登录用户', () => {
      expect(isAdmin(null)).toBe(false);
      expect(isAdmin(undefined)).toBe(false);
    });

    test('应该处理没有角色属性的用户', () => {
      const userWithoutRole = {
        id: 4,
        username: 'norole'
      };
      
      expect(isAdmin(userWithoutRole)).toBe(false);
    });
  });

  // 测试canAccessPage函数
  describe('canAccessPage', () => {
    // 模拟页面权限配置
    const pagePermissions = {
      'home': null, // 公开页面
      'personal-topic': 'content:view',
      'knowledge-base': 'knowledge_base:view',
      'data-query': 'data:query',
      'system-management': 'system:manage'
    };

    test('应该正确检查用户是否可以访问页面', () => {
      // 公开页面
      expect(canAccessPage(mockBasicUser, 'home', pagePermissions)).toBe(true);
      expect(canAccessPage(null, 'home', pagePermissions)).toBe(true);
      
      // 需要权限的页面
      expect(canAccessPage(mockBasicUser, 'personal-topic', pagePermissions)).toBe(true);
      expect(canAccessPage(mockBasicUser, 'knowledge-base', pagePermissions)).toBe(true);
      expect(canAccessPage(mockBasicUser, 'data-query', pagePermissions)).toBe(false);
      expect(canAccessPage(mockBasicUser, 'system-management', pagePermissions)).toBe(false);
      
      // 高级用户
      expect(canAccessPage(mockAdvancedUser, 'personal-topic', pagePermissions)).toBe(true);
      expect(canAccessPage(mockAdvancedUser, 'knowledge-base', pagePermissions)).toBe(true);
      expect(canAccessPage(mockAdvancedUser, 'data-query', pagePermissions)).toBe(false);
      expect(canAccessPage(mockAdvancedUser, 'system-management', pagePermissions)).toBe(false);
      
      // 管理员
      expect(canAccessPage(mockAdminUser, 'personal-topic', pagePermissions)).toBe(true);
      expect(canAccessPage(mockAdminUser, 'knowledge-base', pagePermissions)).toBe(true);
      expect(canAccessPage(mockAdminUser, 'data-query', pagePermissions)).toBe(true);
      expect(canAccessPage(mockAdminUser, 'system-management', pagePermissions)).toBe(true);
    });

    test('应该处理未配置的页面', () => {
      expect(canAccessPage(mockBasicUser, 'unknown-page', pagePermissions)).toBe(false);
      expect(canAccessPage(mockAdminUser, 'unknown-page', pagePermissions)).toBe(true); // 管理员可以访问所有页面
    });

    test('应该处理未登录用户', () => {
      expect(canAccessPage(null, 'personal-topic', pagePermissions)).toBe(false);
      expect(canAccessPage(undefined, 'knowledge-base', pagePermissions)).toBe(false);
    });
  });

  // 测试canPerformAction函数
  describe('canPerformAction', () => {
    // 模拟操作权限配置
    const actionPermissions = {
      'create-knowledge-base': 'knowledge_base:create',
      'edit-knowledge-base': 'knowledge_base:edit',
      'delete-knowledge-base': 'knowledge_base:delete',
      'create-comment': 'comment:create',
      'edit-comment': 'comment:edit',
      'delete-comment': 'comment:delete',
      'manage-users': 'user:manage'
    };

    test('应该正确检查用户是否可以执行操作', () => {
      // 基础用户
      expect(canPerformAction(mockBasicUser, 'create-comment', actionPermissions)).toBe(true);
      expect(canPerformAction(mockBasicUser, 'create-knowledge-base', actionPermissions)).toBe(false);
      expect(canPerformAction(mockBasicUser, 'edit-comment', actionPermissions)).toBe(false);
      expect(canPerformAction(mockBasicUser, 'manage-users', actionPermissions)).toBe(false);
      
      // 高级用户
      expect(canPerformAction(mockAdvancedUser, 'create-comment', actionPermissions)).toBe(true);
      expect(canPerformAction(mockAdvancedUser, 'edit-comment', actionPermissions)).toBe(true);
      expect(canPerformAction(mockAdvancedUser, 'create-knowledge-base', actionPermissions)).toBe(true);
      expect(canPerformAction(mockAdvancedUser, 'delete-knowledge-base', actionPermissions)).toBe(false);
      expect(canPerformAction(mockAdvancedUser, 'manage-users', actionPermissions)).toBe(false);
      
      // 管理员
      expect(canPerformAction(mockAdminUser, 'create-comment', actionPermissions)).toBe(true);
      expect(canPerformAction(mockAdminUser, 'edit-comment', actionPermissions)).toBe(true);
      expect(canPerformAction(mockAdminUser, 'delete-comment', actionPermissions)).toBe(true);
      expect(canPerformAction(mockAdminUser, 'create-knowledge-base', actionPermissions)).toBe(true);
      expect(canPerformAction(mockAdminUser, 'edit-knowledge-base', actionPermissions)).toBe(true);
      expect(canPerformAction(mockAdminUser, 'delete-knowledge-base', actionPermissions)).toBe(true);
      expect(canPerformAction(mockAdminUser, 'manage-users', actionPermissions)).toBe(true);
    });

    test('应该处理未配置的操作', () => {
      expect(canPerformAction(mockBasicUser, 'unknown-action', actionPermissions)).toBe(false);
      expect(canPerformAction(mockAdminUser, 'unknown-action', actionPermissions)).toBe(true); // 管理员可以执行所有操作
    });

    test('应该处理未登录用户', () => {
      expect(canPerformAction(null, 'create-comment', actionPermissions)).toBe(false);
      expect(canPerformAction(undefined, 'create-knowledge-base', actionPermissions)).toBe(false);
    });

    test('应该支持自定义检查函数', () => {
      // 自定义检查函数：只有创建者可以编辑自己的评论
      const customCheck = (user, action, resource) => {
        if (action === 'edit-comment' && resource && user) {
          return user.id === resource.creator_id;
        }
        return false;
      };
      
      const userComment = { id: 1, content: 'Test comment', creator_id: 1 };
      const otherUserComment = { id: 2, content: 'Other comment', creator_id: 2 };
      
      expect(canPerformAction(mockBasicUser, 'edit-comment', actionPermissions, userComment, customCheck)).toBe(true);
      expect(canPerformAction(mockBasicUser, 'edit-comment', actionPermissions, otherUserComment, customCheck)).toBe(false);
    });
  });
});
