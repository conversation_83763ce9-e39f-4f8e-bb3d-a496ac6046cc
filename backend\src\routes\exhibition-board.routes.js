/**
 * 展板图片相关路由
 *
 * 处理展板图片的CRUD操作
 */
const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const exhibitionBoardController = require('../controllers/exhibition-board.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../public/exhibition');
try {
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
    console.log(`创建上传目录成功: ${uploadDir}`);
  } else {
    console.log(`上传目录已存在: ${uploadDir}`);
  }

  // 测试目录写入权限
  const testFile = path.join(uploadDir, '.test_write_permission');
  fs.writeFileSync(testFile, 'test');
  fs.unlinkSync(testFile);
  console.log('上传目录写入权限测试成功');
} catch (error) {
  console.error('上传目录设置失败:', error);
}

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'exhibition-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的图片类型
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/svg+xml'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型，只允许上传JPG、PNG、GIF和SVG图片'), false);
  }
};

// 配置multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// 测试路由
router.get('/test', (req, res) => {
  console.log('测试路由被访问');
  res.status(200).json({
    success: true,
    message: '测试路由正常工作'
  });
});

// 获取展板图片列表 (所有人可访问)
router.get('/',
  exhibitionBoardController.getExhibitionBoards
);

// 获取展板图片详情 (所有人可访问)
router.get('/:id',
  exhibitionBoardController.getExhibitionBoardById
);

// 创建展板图片 (需要认证和权限)
router.post('/',
  authMiddleware,
  checkPermission(['system:manage', 'exhibition:manage']),
  exhibitionBoardController.createExhibitionBoard
);

// 上传展板图片 (需要认证和权限)
router.post('/upload',
  authMiddleware,
  checkPermission(['system:manage', 'exhibition:manage']),
  upload.single('image'),
  exhibitionBoardController.uploadExhibitionImage
);

// 更新展板图片 (需要认证和权限)
router.put('/:id',
  authMiddleware,
  checkPermission(['system:manage', 'exhibition:manage']),
  exhibitionBoardController.updateExhibitionBoard
);

// 删除展板图片 (需要认证和权限)
router.delete('/:id',
  authMiddleware,
  checkPermission(['system:manage', 'exhibition:manage']),
  exhibitionBoardController.deleteExhibitionBoard
);

// 更新展板图片顺序 (需要认证和权限)
router.put('/order/batch',
  authMiddleware,
  checkPermission(['system:manage', 'exhibition:manage']),
  exhibitionBoardController.updateExhibitionBoardOrder
);

module.exports = router;
