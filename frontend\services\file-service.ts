/**
 * 文件服务
 *
 * 处理文件的上传、下载、查询、审核等
 */

import apiService from './api-service'

// 文件类型
export interface File {
  id: string
  name: string
  original_name: string
  path: string
  type: string
  mime_type: string
  size: number
  knowledge_base_id: string
  knowledgeBase?: {
    id: string
    name: string
    type: string
  }
  uploader_id: string
  uploader?: {
    id: string
    username: string
    email: string
  }
  status: 'pending' | 'approved' | 'rejected'
  reviewer_id?: string
  reviewer?: {
    id: string
    username: string
    email: string
  }
  review_time?: string
  reject_reason?: string
  summary?: string
  detailed_description?: string
  created_at: string
  updated_at: string
}

// 文件查询参数
export interface FileQueryParams {
  page?: number
  limit?: number
  search?: string
  status?: 'pending' | 'approved' | 'rejected'
  type?: string
  knowledge_base_id?: string
  size_min?: number
  size_max?: number
  knowledge_base_type?: string
}

// 文件审核参数
export interface FileReviewParams {
  status: 'approved' | 'rejected'
  reject_reason?: string
}

/**
 * 获取文件列表
 * @param params 查询参数
 */
export const getFileList = async (params?: FileQueryParams): Promise<{
  files: File[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}> => {
  const response = await apiService.get<{
    files: File[]
    pagination: {
      total: number
      page: number
      limit: number
      totalPages: number
    }
  }>('/files', params)

  return response
}

/**
 * 获取知识库文件列表
 * @param knowledgeBaseId 知识库ID
 * @param params 查询参数
 */
export const getKnowledgeBaseFileList = async (knowledgeBaseId: string, params?: FileQueryParams): Promise<{
  knowledgeBase: {
    id: string
    name: string
    type: string
  }
  files: File[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}> => {
  console.log(`开始获取知识库文件列表，知识库ID: ${knowledgeBaseId}`, params);

  try {
    // 修正API路径，确保与后端路由匹配
    const response = await apiService.get<{
      knowledgeBase: {
        id: string
        name: string
        type: string
      }
      files: File[]
      pagination: {
        total: number
        page: number
        limit: number
        totalPages: number
      }
    }>(`/files/knowledge-base/${knowledgeBaseId}`, params);

    console.log(`获取知识库文件列表成功，知识库ID: ${knowledgeBaseId}`, {
      knowledgeBase: response.knowledgeBase,
      filesCount: response.files?.length || 0,
      pagination: response.pagination
    });

    // 检查文件列表中的上传者信息
    if (response.files && response.files.length > 0) {
      console.log(`文件列表中的第一个文件:`, {
        id: response.files[0].id,
        name: response.files[0].original_name,
        uploader_id: response.files[0].uploader_id,
        uploader: response.files[0].uploader
      });

      // 处理上传者信息
      response.files = response.files.map(file => {
        // 如果上传者信息不是对象或为null，但有上传者ID，尝试创建上传者对象
        if ((!file.uploader || typeof file.uploader !== 'object') && file.uploader_id) {
          console.log(`文件 ${file.id} 的上传者信息无效但有上传者ID，尝试创建上传者对象`);
          return {
            ...file,
            uploader: {
              id: file.uploader_id,
              username: `用户ID: ${file.uploader_id}`,
              email: ''
            }
          };
        }
        return file;
      });
    }

    return response;
  } catch (error) {
    console.error(`获取知识库文件列表失败，知识库ID: ${knowledgeBaseId}`, error);
    throw error;
  }
}

/**
 * 获取文件详情
 * @param id 文件ID
 */
export const getFileById = async (id: string): Promise<File> => {
  return await apiService.get<File>(`/files/${id}`)
}

/**
 * 上传文件
 * @param knowledgeBaseId 知识库ID
 * @param file 文件对象
 * @param onProgress 上传进度回调
 * @param useDify 是否使用Dify分析文件
 */
export const uploadFile = async (
  knowledgeBaseId: string,
  file: Blob,
  onProgress?: (percent: number) => void,
  useDify: boolean = true // 默认使用Dify分析
): Promise<File> => {
  const formData = new FormData()
  formData.append('file', file)

  // 添加是否使用Dify分析的参数
  formData.append('use_dify', useDify ? 'true' : 'false')

  return await apiService.upload<File>(
    `/files/upload/${knowledgeBaseId}`,
    formData,
    onProgress ? (progressEvent: any) => {
      const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      onProgress(percent)
    } : undefined
  )
}

/**
 * 下载文件
 * @param id 文件ID
 * @param filename 文件名（可选）
 */
export const downloadFile = async (id: string, filename?: string): Promise<void> => {
  await apiService.download(`/files/${id}/download`, filename)
}

/**
 * 审核文件
 * @param id 文件ID
 * @param params 审核参数
 */
export const reviewFile = async (id: string, params: FileReviewParams): Promise<File> => {
  return await apiService.put<File>(`/files/${id}/review`, params)
}

/**
 * 上传文件到Dify知识库
 * @param id 文件ID
 */
export const uploadFileToDify = async (id: string): Promise<File> => {
  return await apiService.post<File>(`/files/${id}/upload-to-dify`)
}

/**
 * 删除文件
 * @param id 文件ID
 */
export const deleteFile = async (id: string): Promise<{ message: string }> => {
  return await apiService.del<{ message: string }>(`/files/${id}`)
}

export default {
  getFileList,
  getKnowledgeBaseFileList,
  getFileById,
  uploadFile,
  downloadFile,
  reviewFile,
  uploadFileToDify,
  deleteFile
}
