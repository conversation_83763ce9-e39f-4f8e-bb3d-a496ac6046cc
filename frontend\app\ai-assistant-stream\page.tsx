"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { Navbar } from "@/components/navbar"
import { Search, X, MessageSquare, AlertTriangle, Send } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import type { ResearchAssistant } from "@/types/ai-assistants"
import { getResearchAssistants } from "@/services/ai-assistant-service"
import { isLoggedIn as checkIsLoggedIn } from "@/services/auth-service"
import { toast } from "@/components/ui/use-toast"
import { StreamTypewriter } from "@/components/ui/typewriter"

export default function AIAssistantStreamPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [showAssistantModal, setShowAssistantModal] = useState(false)
  const [selectedAssistant, setSelectedAssistant] = useState<ResearchAssistant | null>(null)
  const [chatInput, setChatInput] = useState("")
  const [chatMessages, setChatMessages] = useState<{ role: "user" | "assistant"; content: string }[]>([])
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [assistants, setAssistants] = useState<ResearchAssistant[]>([])
  const [loading, setLoading] = useState(true)
  const [isStreaming, setIsStreaming] = useState(false)
  const chatEndRef = useRef<HTMLDivElement>(null)
  
  // 创建流式打字机实例
  const {
    textComponent: streamingText,
    text: streamingContent,
    isTyping,
    conversationId,
    startStreaming
  } = StreamTypewriter({ className: "text-gray-800 text-lg whitespace-pre-line" })
  
  // 滚动到底部
  const scrollToBottom = () => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }
  
  // 当消息更新时滚动到底部
  useEffect(() => {
    scrollToBottom()
  }, [chatMessages, streamingContent])

  // 检查用户是否已登录并获取助手数据
  useEffect(() => {
    const initialize = async () => {
      try {
        // 检查登录状态
        const loggedIn = checkIsLoggedIn()
        setIsLoggedIn(loggedIn)

        // 只有在用户已登录时才获取研究助手列表
        if (loggedIn) {
          setLoading(true)
          const assistantList = await getResearchAssistants()
          console.log('获取到的研究助手列表:', assistantList)
          setAssistants(assistantList)
          setLoading(false) // 无论成功与否，都设置loading为false
        } else {
          // 用户未登录，不加载数据
          setLoading(false)
        }
      } catch (error) {
        console.error('初始化失败:', error)
        toast({
          title: '加载失败',
          description: '无法获取AI助手列表，请稍后再试',
          variant: 'destructive'
        })
        setLoading(false)
      }
    }

    initialize()
  }, [])

  // 根据搜索过滤助手
  const filteredAssistants = assistants.filter((assistant) => {
    return (
      searchQuery === "" ||
      assistant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      assistant.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })

  // 打开助手对话窗口
  const openAssistantModal = (assistant: ResearchAssistant) => {
    // 检查用户是否登录
    if (!isLoggedIn) {
      toast({
        title: '需要登录',
        description: '请先登录后再使用AI助手',
        variant: 'destructive'
      })
      return
    }

    setSelectedAssistant(assistant)
    setShowAssistantModal(true)
    // 初始化对话
    setChatMessages([
      {
        role: "assistant",
        content: `您好！我是${assistant.name}，${assistant.description}。请问有什么可以帮助您的吗？`,
      },
    ])
  }

  // 关闭助手对话窗口
  const closeAssistantModal = () => {
    setShowAssistantModal(false)
    setSelectedAssistant(null)
    setChatInput("")
    setChatMessages([])
  }

  // 发送消息
  const sendMessage = async () => {
    if (!chatInput.trim() || !selectedAssistant || isStreaming) return

    // 检查用户是否登录
    if (!isLoggedIn) {
      toast({
        title: '需要登录',
        description: '请先登录后再使用AI助手',
        variant: 'destructive'
      })
      return
    }

    const userMessage = chatInput.trim()
    setChatInput("")

    // 添加用户消息到聊天记录
    setChatMessages(prev => [...prev, { role: "user", content: userMessage }])
    
    // 添加空的助手消息，用于显示流式响应
    setChatMessages(prev => [...prev, { role: "assistant", content: "" }])
    
    // 设置流式状态
    setIsStreaming(true)
    
    try {
      // 开始流式打字
      const { cancel } = await startStreaming(selectedAssistant.id, userMessage, conversationId)
      
      // 流式响应结束后
      setIsStreaming(false)
      
      // 更新聊天记录
      setChatMessages(prev => {
        const newMessages = [...prev]
        // 找到最后一条助手消息并替换
        const lastIndex = newMessages.length - 1
        if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
          newMessages[lastIndex] = {
            role: "assistant",
            content: streamingContent
          }
        }
        return newMessages
      })
    } catch (error: any) {
      console.error('流式查询AI助手失败:', error)
      setIsStreaming(false)
      
      // 获取详细错误信息
      let errorMessage = "抱歉，发生了错误，无法获取回复。请稍后再试。"
      
      if (error.message) {
        errorMessage = `错误: ${error.message}`
      }
      
      // 更新聊天记录
      setChatMessages(prev => {
        const newMessages = [...prev]
        // 找到最后一条助手消息并替换
        const lastIndex = newMessages.length - 1
        if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
          newMessages[lastIndex] = {
            role: "assistant",
            content: errorMessage
          }
        }
        return newMessages
      })
      
      toast({
        title: '查询失败',
        description: '无法获取AI助手回复，请稍后再试',
        variant: 'destructive'
      })
    }
  }

  // 处理输入框回车事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    try {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault()
        void sendMessage() // 使用void操作符处理Promise
      }
    } catch (error) {
      console.error("处理键盘事件时出错:", error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">AI研究助手 (流式响应)</h1>
          <p className="text-gray-600">选择适合您研究需求的智能助手，获取专业的研究支持和知识服务</p>
        </div>

        {!isLoggedIn ? (
          <div className="bg-white p-8 rounded-lg shadow-md max-w-md mx-auto mt-20">
            <div className="flex flex-col items-center text-center">
              <AlertTriangle className="h-16 w-16 text-yellow-500 mb-4" />
              <h2 className="text-2xl font-bold mb-4">需要登录</h2>
              <p className="text-gray-600 mb-6">
                您需要登录后才能使用AI研究助手。请先登录或注册一个账号。
              </p>
              <div className="flex gap-4">
                <Button
                  onClick={(e) => {
                    try {
                      e.preventDefault();
                      // 触发登录弹窗
                      const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
                      window.dispatchEvent(event);
                    } catch (error) {
                      console.error("触发登录弹窗时出错:", error);
                    }
                  }}
                  className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                >
                  登录
                </Button>
                <Button
                  variant="outline"
                  onClick={(e) => {
                    try {
                      e.preventDefault();
                      // 触发注册弹窗
                      const event = new CustomEvent('open-login-modal', { detail: { isRegister: true } });
                      window.dispatchEvent(event);
                    } catch (error) {
                      console.error("触发注册弹窗时出错:", error);
                    }
                  }}
                >
                  注册
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* 搜索 */}
            <div className="mb-8 flex flex-col md:flex-row gap-4">
              <div className="relative md:w-1/2">
                <input
                  type="text"
                  placeholder="搜索助手..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              </div>
            </div>

            {/* 加载状态 */}
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1e7a43] mx-auto mb-4"></div>
                <p className="text-gray-500">正在加载AI助手...</p>
              </div>
            ) : (
              <>
                {/* 助手卡片网格 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredAssistants.map((assistant) => (
                    <div
                      key={assistant.id}
                      className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={(e) => {
                        try {
                          e.preventDefault();
                          openAssistantModal(assistant);
                        } catch (error) {
                          console.error("打开助手对话窗口时出错:", error);
                        }
                      }}
                    >
                      <div className="flex items-start mb-4">
                        <div className="p-2 rounded-lg bg-gray-50 mr-4">
                          <MessageSquare className="h-5 w-5 text-[#1e7a43]" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold">{assistant.name}</h3>
                          <p className="text-sm text-gray-500 mt-1">{assistant.description}</p>
                        </div>
                      </div>
                      {/* 标签 */}
                      {assistant.tags && assistant.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {assistant.tags.map((tag, index) => (
                            <span
                              key={`assistant-tag-${assistant.id}-${index}`}
                              className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* 没有结果时显示 */}
                {filteredAssistants.length === 0 && (
                  <div className="text-center py-12">
                    <div className="mb-4">
                      <Search className="h-12 w-12 text-gray-300 mx-auto" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-700 mb-2">未找到匹配的助手</h3>
                    <p className="text-gray-500">请尝试使用其他关键词或清除筛选条件</p>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>

      {/* 助手对话模态窗口 - 只有在登录状态下才显示 */}
      {isLoggedIn && showAssistantModal && selectedAssistant && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-4xl h-[80vh] flex flex-col relative">
            {/* 模态窗口头部 */}
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-gray-50 mr-3">
                  <MessageSquare className="h-5 w-5 text-[#1e7a43]" />
                </div>
                <div>
                  <h2 className="text-xl font-bold">{selectedAssistant.name}</h2>
                  <p className="text-sm text-gray-500">{selectedAssistant.description}</p>
                </div>
              </div>
              <button
                onClick={(e) => {
                  try {
                    e.preventDefault();
                    closeAssistantModal();
                  } catch (error) {
                    console.error("关闭助手对话窗口时出错:", error);
                  }
                }}
                className="text-gray-500 hover:text-gray-700">
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* 聊天内容区域 */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {chatMessages.map((message, index) => (
                <div key={index} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                  {message.role === "user" ? (
                    // 用户消息
                    <div className="max-w-[80%] rounded-lg p-4 bg-[#1e7a43] text-white">
                      <div className="whitespace-pre-line">{message.content}</div>
                    </div>
                  ) : (
                    // AI助手消息
                    <div className="max-w-[80%] rounded-lg overflow-hidden">
                      {/* 如果是最后一条消息且正在流式响应，显示打字机效果 */}
                      {index === chatMessages.length - 1 && isStreaming ? (
                        <div className="bg-white border border-[#1e7a43]/20 p-3 rounded-lg">
                          {streamingText}
                        </div>
                      ) : (
                        // 普通消息
                        <div className="bg-white border border-[#1e7a43]/20 p-3 rounded-lg">
                          <div className="text-gray-800 text-lg whitespace-pre-line">{message.content}</div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
              <div ref={chatEndRef} />
            </div>

            {/* 输入区域 */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center space-x-2">
                <textarea
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="输入您的问题..."
                  className="flex-1 border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent resize-none"
                  rows={2}
                  disabled={isStreaming}
                />
                <Button
                  onClick={(e) => {
                    try {
                      e.preventDefault();
                      void sendMessage();
                    } catch (error) {
                      console.error("发送消息时出错:", error);
                    }
                  }}
                  className="bg-[#1e7a43] hover:bg-[#1e7a43]/90 h-full"
                  disabled={!chatInput.trim() || isStreaming}
                >
                  <Send className="h-5 w-5" />
                </Button>
              </div>
              {isStreaming && (
                <div className="text-xs text-gray-500 mt-2">
                  AI正在生成回复，请稍候...
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
