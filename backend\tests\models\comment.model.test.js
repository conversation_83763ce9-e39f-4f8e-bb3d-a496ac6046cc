/**
 * 评论模型测试
 *
 * 测试评论模型的字段、验证和关联
 */

const { Comment, User } = require('../../src/models');

describe('评论模型', () => {
  let testUser;
  let reviewerUser;
  let testComment;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试用户，使用时间戳确保用户名唯一
    const timestamp = Date.now();
    testUser = await User.create({
      username: `commentmodeluser_${timestamp}`,
      password: 'Password123!',
      email: `commentmodeluser_${timestamp}@example.com`,
      phone: `138${timestamp.toString().slice(-8)}`,
      role: 'basic_user',
      is_active: true
    });

    // 创建审核者用户，使用时间戳确保用户名唯一
    reviewerUser = await User.create({
      username: `commentreviewer_${timestamp}`,
      password: 'Password123!',
      email: `commentreviewer_${timestamp}@example.com`,
      phone: `139${timestamp.toString().slice(-8)}`,
      role: 'admin',
      is_active: true
    });

    // 创建测试评论
    testComment = await Comment.create({
      content: '这是一条测试评论',
      topic_type: 'personal',
      topic_id: 1,
      status: 'pending',
      user_id: testUser.id
    });
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await Comment.destroy({ where: { id: testComment.id } });
    await User.destroy({ where: { id: testUser.id } });
    await User.destroy({ where: { id: reviewerUser.id } });
  });

  // 测试评论创建
  describe('评论创建', () => {
    test('应该成功创建评论', async () => {
      const comment = await Comment.findByPk(testComment.id);

      expect(comment).toBeDefined();
      expect(comment.content).toBe('这是一条测试评论');
      expect(comment.topic_type).toBe('personal');
      expect(comment.topic_id).toBe(1);
      expect(comment.status).toBe('pending');
      expect(comment.user_id).toBe(testUser.id);
    });

    test('不应该创建没有内容的评论', async () => {
      try {
        await Comment.create({
          // 缺少内容
          topic_type: 'personal',
          topic_id: 1,
          status: 'pending',
          user_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建没有主题类型的评论', async () => {
      try {
        await Comment.create({
          content: '缺少主题类型的评论',
          // 缺少主题类型
          topic_id: 1,
          status: 'pending',
          user_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建没有主题ID的评论', async () => {
      try {
        await Comment.create({
          content: '缺少主题ID的评论',
          topic_type: 'personal',
          // 缺少主题ID
          status: 'pending',
          user_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建无效状态的评论', async () => {
      try {
        await Comment.create({
          content: '无效状态的评论',
          topic_type: 'personal',
          topic_id: 1,
          status: 'invalid_status', // 无效的状态
          user_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  // 测试评论更新
  describe('评论更新', () => {
    test('应该成功更新评论状态', async () => {
      await testComment.update({
        status: 'approved',
        reject_reason: '评论已审核通过',
        reviewer_id: reviewerUser.id,
        review_time: new Date()
      });

      const updatedComment = await Comment.findByPk(testComment.id);
      expect(updatedComment.status).toBe('approved');
      expect(updatedComment.reject_reason).toBe('评论已审核通过');
      expect(updatedComment.reviewer_id).toBe(reviewerUser.id);
      expect(updatedComment.review_time).not.toBeNull();

      // 恢复原始数据
      await testComment.update({
        status: 'pending',
        reject_reason: null,
        reviewer_id: null,
        review_time: null
      });
    });
  });

  // 测试评论关联
  describe('评论关联', () => {
    test('评论应该关联到用户', async () => {
      // 检查评论模型是否有用户关联
      expect(Comment.associations).toHaveProperty('user');

      // 获取评论的用户
      const comment = await Comment.findByPk(testComment.id, {
        include: ['user']
      });

      expect(comment.user).toBeDefined();
      expect(comment.user.id).toBe(testUser.id);
      expect(comment.user.username).toContain('commentmodeluser');
    });

    test('评论应该关联到审核者', async () => {
      // 检查评论模型是否有审核者关联
      expect(Comment.associations).toHaveProperty('reviewer');

      // 更新评论添加审核者
      await testComment.update({
        status: 'approved',
        reject_reason: '评论已审核通过',
        reviewer_id: reviewerUser.id,
        review_time: new Date()
      });

      // 获取评论的审核者
      const comment = await Comment.findByPk(testComment.id, {
        include: ['reviewer']
      });

      expect(comment.reviewer).toBeDefined();
      expect(comment.reviewer.id).toBe(reviewerUser.id);
      expect(comment.reviewer.username).toContain('commentreviewer');

      // 恢复原始数据
      await testComment.update({
        status: 'pending',
        reject_reason: null,
        reviewer_id: null,
        review_time: null
      });
    });
  });

  // 测试评论实例方法
  describe('评论实例方法', () => {
    test('isApproved方法应该正确判断评论是否已审核通过', async () => {
      // 假设评论模型有isApproved方法
      if (typeof testComment.isApproved === 'function') {
        // 初始状态为pending
        expect(testComment.isApproved()).toBe(false);

        // 更新为approved状态
        await testComment.update({ status: 'approved' });
        expect(testComment.isApproved()).toBe(true);

        // 恢复原始状态
        await testComment.update({ status: 'pending' });
      } else {
        // 如果没有该方法，跳过测试
        console.log('评论模型没有isApproved方法，跳过测试');
      }
    });

    test('isRejected方法应该正确判断评论是否已被拒绝', async () => {
      // 假设评论模型有isRejected方法
      if (typeof testComment.isRejected === 'function') {
        // 初始状态为pending
        expect(testComment.isRejected()).toBe(false);

        // 更新为rejected状态
        await testComment.update({ status: 'rejected' });
        expect(testComment.isRejected()).toBe(true);

        // 恢复原始状态
        await testComment.update({ status: 'pending' });
      } else {
        // 如果没有该方法，跳过测试
        console.log('评论模型没有isRejected方法，跳过测试');
      }
    });
  });

  // 测试评论类方法
  describe('评论类方法', () => {
    test('findByStatus方法应该返回指定状态的评论列表', async () => {
      // 假设评论模型有findByStatus静态方法
      if (typeof Comment.findByStatus === 'function') {
        const pendingComments = await Comment.findByStatus('pending');
        expect(Array.isArray(pendingComments)).toBe(true);

        // 所有返回的评论都应该是pending状态
        pendingComments.forEach(comment => {
          expect(comment.status).toBe('pending');
        });
      } else {
        // 如果没有该方法，跳过测试
        console.log('评论模型没有findByStatus方法，跳过测试');
      }
    });

    test('findByTopic方法应该返回指定主题的评论列表', async () => {
      // 假设评论模型有findByTopic静态方法
      if (typeof Comment.findByTopic === 'function') {
        const topicComments = await Comment.findByTopic('personal', 1);
        expect(Array.isArray(topicComments)).toBe(true);

        // 所有返回的评论都应该属于指定主题
        topicComments.forEach(comment => {
          expect(comment.topic_type).toBe('personal');
          expect(comment.topic_id).toBe(1);
        });
      } else {
        // 如果没有该方法，跳过测试
        console.log('评论模型没有findByTopic方法，跳过测试');
      }
    });
  });
});
