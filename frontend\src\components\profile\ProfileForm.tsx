"use client"

import { useState, useRef } from 'react'
import { User, Upload, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ProfileFormProps, ProfileFormFields } from './types'

/**
 * 个人信息表单组件
 * 
 * 用于编辑个人信息
 */
export function ProfileForm({ user, loading, onSubmit }: ProfileFormProps) {
  const [formData, setFormData] = useState<ProfileFormFields>({
    name: user.name || '',
    email: user.email || '',
    phone: user.phone || '',
    avatar: undefined
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [avatarPreview, setAvatarPreview] = useState<string | undefined>(user.avatar)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })
    
    // 清除错误
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' })
    }
  }

  // 处理头像选择
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setFormData({ ...formData, avatar: file })
      
      // 预览头像
      const reader = new FileReader()
      reader.onload = (event) => {
        setAvatarPreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    
    // 验证邮箱
    if (!formData.email) {
      newErrors.email = '请输入邮箱'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱'
    }
    
    // 验证手机号
    if (!formData.phone) {
      newErrors.phone = '请输入手机号'
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入有效的手机号'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      onSubmit(formData)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <div className="h-24 w-24 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
            {avatarPreview ? (
              <img src={avatarPreview} alt="Avatar" className="h-full w-full object-cover" />
            ) : (
              <User className="h-12 w-12 text-gray-400" />
            )}
          </div>
          <button
            type="button"
            className="absolute bottom-0 right-0 bg-primary text-white rounded-full p-1.5 shadow-md"
            onClick={() => fileInputRef.current?.click()}
            disabled={loading}
          >
            <Upload className="h-4 w-4" />
          </button>
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleAvatarChange}
            disabled={loading}
          />
        </div>
        <p className="text-sm text-gray-500">点击图标上传头像</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
            用户名
          </Label>
          <Input
            id="username"
            value={user.username}
            disabled
            className="bg-gray-100"
          />
          <p className="mt-1 text-xs text-gray-500">用户名不可修改</p>
        </div>

        <div>
          <Label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            姓名
          </Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            disabled={loading}
          />
        </div>

        <div>
          <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            邮箱
          </Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            className={errors.email ? 'border-red-500' : ''}
            disabled={loading}
          />
          {errors.email && <p className="mt-1 text-xs text-red-500">{errors.email}</p>}
        </div>

        <div>
          <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
            手机号
          </Label>
          <Input
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            className={errors.phone ? 'border-red-500' : ''}
            disabled={loading}
          />
          {errors.phone && <p className="mt-1 text-xs text-red-500">{errors.phone}</p>}
        </div>

        <div>
          <Label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
            角色
          </Label>
          <Input
            id="role"
            value={user.role}
            disabled
            className="bg-gray-100"
          />
          <p className="mt-1 text-xs text-gray-500">角色不可修改</p>
        </div>
      </div>

      <div className="flex justify-end">
        <Button type="submit" disabled={loading}>
          {loading ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              保存中...
            </>
          ) : (
            '保存修改'
          )}
        </Button>
      </div>
    </form>
  )
}
