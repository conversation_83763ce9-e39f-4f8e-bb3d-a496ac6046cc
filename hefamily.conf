server {
    listen 99;
    server_name *************;  # 您的公网IP

    # 全局设置
    client_max_body_size 1024M;  # 允许上传最大1GB的文件
    
    # 超时设置
    proxy_connect_timeout 600;    # 连接后端服务器的超时时间（秒）
    proxy_send_timeout 600;       # 发送请求到后端服务器的超时时间（秒）
    proxy_read_timeout 600;       # 从后端服务器读取响应的超时时间（秒）
    send_timeout 600;             # 客户端与服务器连接的超时时间（秒）

    # 前端应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 后端API
    location /api {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 上传文件的静态服务
    location /uploads {
        alias /soft/hefu/20250513/backend/uploads;
        try_files $uri =404;
    }
}
