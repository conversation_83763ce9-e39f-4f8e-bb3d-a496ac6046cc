/**
 * 添加AI研究助手权限脚本
 * 
 * 运行方法：
 * node add-assistant-permission.js
 */

const db = require('./src/models');

(async () => {
  try {
    console.log('开始添加AI研究助手权限...');

    // 检查assistant:use权限是否已存在
    let permission = await db.Permission.findOne({
      where: { code: 'assistant:use' }
    });

    // 如果权限不存在，创建它
    if (!permission) {
      console.log('创建assistant:use权限...');
      permission = await db.Permission.create({
        name: '使用AI研究助手',
        code: 'assistant:use',
        description: '使用AI研究助手功能',
        module: 'assistant'
      });
      console.log('创建成功，权限ID:', permission.id);
    } else {
      console.log('assistant:use权限已存在，ID:', permission.id);
    }

    // 获取访问者角色
    const visitorRole = await db.Role.findOne({
      where: { name: '访问者' }
    });

    if (!visitorRole) {
      console.error('未找到访问者角色');
      process.exit(1);
    }

    console.log('找到访问者角色，ID:', visitorRole.id);

    // 检查角色是否已有该权限
    const hasPermission = await db.RolePermission.findOne({
      where: {
        role_id: visitorRole.id,
        permission_id: permission.id
      }
    });

    if (!hasPermission) {
      console.log('为访问者角色分配assistant:use权限...');
      await db.RolePermission.create({
        role_id: visitorRole.id,
        permission_id: permission.id
      });
      console.log('权限分配成功');
    } else {
      console.log('访问者角色已有assistant:use权限');
    }

    console.log('AI研究助手权限添加完成');
  } catch (error) {
    console.error('添加AI研究助手权限失败:', error);
  } finally {
    process.exit();
  }
})();
