/**
 * 用户管理组件类型定义
 */

// 用户类型定义
export interface UserType {
  id: string
  name: string
  phone: string
  email: string
  roleId: string // 角色ID，一个用户只能有一个角色
  roleName: string // 角色名称，用于显示
  status: "正常" | "待审核" | "已禁用" | "待激活"
  createdAt: string
}

// 角色类型定义
export interface Role {
  id: string
  name: string
  description: string
  userCount: number
  createdAt: string
  isPreset?: boolean // 是否为预设角色，预设角色不可删除
}

// 用户表格属性
export interface UserTableProps {
  users: UserType[]
  selectedUsers: string[]
  onSelectUser: (userId: string) => void
  onSelectAll: (userIds: string[]) => void
  onEdit: (user: UserType) => void
  onDelete: (user: UserType) => void
  onResetPassword: (user: UserType) => void
}

// 用户模态框属性
export interface UserModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (userData: UserType) => void
  user: UserType | null
  roles: Role[]
}

// 删除用户模态框属性
export interface DeleteUserModalProps {
  isOpen: boolean
  onClose: () => void
  onDelete: () => void
  user: UserType | null
}
