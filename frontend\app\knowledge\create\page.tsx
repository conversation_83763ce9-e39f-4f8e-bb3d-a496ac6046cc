// 重新设计新建知识库界面

"use client"

import { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { Book, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "@/components/ui/use-toast"
import { createKnowledgeBase } from "@/services/knowledge-service"

export default function CreateKnowledgePage() {
  const router = useRouter()
  const { isLoggedIn, hasPermission } = useAuth()
  const [knowledgeBaseName, setKnowledgeBaseName] = useState("")
  const [knowledgeBaseDescription, setKnowledgeBaseDescription] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 检查用户是否已登录
  useEffect(() => {
    if (!isLoggedIn) {
      toast({
        title: "需要登录",
        description: "请先登录后再创建知识库",
        variant: "destructive"
      })
      router.push("/knowledge")
    }

    // 检查用户是否有创建知识库的权限
    if (!hasPermission('knowledge:create')) {
      toast({
        title: "权限不足",
        description: "您没有创建知识库的权限",
        variant: "destructive"
      })
      router.push("/knowledge")
    }
  }, [isLoggedIn, hasPermission, router])

  const handleSubmit = async () => {
    if (!knowledgeBaseName.trim()) {
      toast({
        title: "输入错误",
        description: "请输入知识库名称",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 调用API创建知识库
      const newKnowledgeBase = await createKnowledgeBase({
        name: knowledgeBaseName,
        description: knowledgeBaseDescription,
        type: 'user'
      })

      toast({
        title: "创建成功",
        description: `知识库 "${knowledgeBaseName}" 创建成功！`,
      })

      // 跳转到知识库详情页
      router.push(`/knowledge/view/${newKnowledgeBase.id}`)
    } catch (error: any) {
      console.error('创建知识库失败:', error)
      toast({
        title: "创建失败",
        description: error.response?.data?.message || "无法创建知识库，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center text-sm text-gray-500 mb-6">
            <span className="cursor-pointer hover:text-[#1e7a43]" onClick={() => router.push("/knowledge")}>
              知识库
            </span>
            <ChevronRight className="h-4 w-4 mx-1" />
            <span>新建知识库</span>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-8">
            <div className="flex items-center justify-center mb-8">
              <div className="h-16 w-16 rounded-full bg-[#1e7a43]/10 flex items-center justify-center">
                <Book className="h-8 w-8 text-[#1e7a43]" />
              </div>
            </div>

            <h1 className="text-2xl font-bold text-center mb-8">新建知识库</h1>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  知识库名称 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={knowledgeBaseName}
                  onChange={(e) => setKnowledgeBaseName(e.target.value)}
                  placeholder="请输入知识库名称"
                  className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                />
                <p className="mt-1 text-xs text-gray-500">知识库名称将作为标识，建议使用简洁明了的名称</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  知识库描述
                </label>
                <textarea
                  value={knowledgeBaseDescription}
                  onChange={(e) => setKnowledgeBaseDescription(e.target.value)}
                  placeholder="请输入知识库描述"
                  className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                  rows={3}
                />
                <p className="mt-1 text-xs text-gray-500">简要描述知识库的内容和用途</p>
              </div>

              <div>
                <div className="p-3 border rounded-md bg-gray-50">
                  <div className="flex items-center">
                    <div className="ml-3">
                      <span className="font-medium">用户知识库访问权限说明</span>
                      <p className="text-xs text-gray-500 mt-1">创建的用户知识库默认仅您可访问，其他用户需要向您申请访问权限</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8 flex justify-center">
              <Button
                className="bg-[#f5a623] hover:bg-[#f5a623]/90 w-full md:w-64"
                onClick={handleSubmit}
                disabled={isSubmitting || !knowledgeBaseName.trim()}
              >
                {isSubmitting ? "创建中..." : "创建知识库"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
