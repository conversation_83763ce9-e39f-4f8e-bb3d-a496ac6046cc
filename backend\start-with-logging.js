/**
 * 启动后端服务器并将日志记录到文件
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 创建日志目录
const logDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
  console.log(`已创建日志目录: ${logDir}`);
}

// 创建日志文件
const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
const logFile = path.join(logDir, `server-${timestamp}.log`);
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

console.log(`日志将记录到: ${logFile}`);

// 启动后端服务器
console.log(`当前工作目录: ${__dirname}`);
console.log(`启动命令: node src/app.js`);

const server = spawn('node', ['src/app.js'], {
  stdio: ['ignore', 'pipe', 'pipe'],
  cwd: __dirname,
  env: {
    ...process.env,
    DIFY_API_KEY: 'dataset-DLFJlUe25VUHOwMO4HbO4hQk',
    DIFY_DATASET_ID: 'bc99508b-c53b-41d8-bbc1-180683fc9f8c'
  }
});

// 将标准输出和标准错误重定向到日志文件
server.stdout.on('data', (data) => {
  // 写入日志文件
  logStream.write(data);

  // 同时在控制台显示
  process.stdout.write(data);

  // 强制刷新日志流
  logStream.flush();
});

server.stderr.on('data', (data) => {
  // 写入日志文件
  logStream.write(data);

  // 同时在控制台显示
  process.stderr.write(data);

  // 强制刷新日志流
  logStream.flush();
});

// 监听服务器退出事件
server.on('exit', (code) => {
  console.log(`服务器进程已退出，退出码: ${code}`);
  logStream.end();
});

// 监听进程信号，优雅地关闭服务器
process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  server.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.kill('SIGTERM');
});

console.log(`后端服务器已启动，进程ID: ${server.pid}`);
console.log('按Ctrl+C停止服务器');
