"use client"

import { useState } from "react"
import { Navbar } from "@/components/navbar"
import { MultiSelectKnowledgeBase } from "@/components/multi-select-knowledge-base"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, FileText, BarChart, Calendar, Clock } from "lucide-react"

// 示例知识库数据
const knowledgeBases = [
  {
    id: "SYS001",
    name: "革命先辈资料库",
    type: "系统" as const,
    description: "包含革命先辈的历史资料、照片和文献",
  },
  {
    id: "SYS002",
    name: "家族历史档案",
    type: "系统" as const,
    description: "记录家族历史发展的重要文献和资料",
  },
  {
    id: "SYS003",
    name: "学术研究资料",
    type: "系统" as const,
    description: "与家族相关的学术研究论文和资料",
  },
  {
    id: "SYS004",
    name: "媒体资源库",
    type: "系统" as const,
    description: "包含视频、音频和图片等多媒体资源",
  },
  {
    id: "KB001",
    name: "个人研究资料",
    type: "用户" as const,
    description: "个人研究相关的资料和笔记",
  },
  {
    id: "KB002",
    name: "项目文档",
    type: "用户" as const,
    description: "工作项目相关的文档和资料",
  },
]

export default function FamilyResearchDatabasePage() {
  const [selectedKnowledgeBaseIds, setSelectedKnowledgeBaseIds] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState("search")
  const [searchQuery, setSearchQuery] = useState("")

  const handleSearch = () => {
    // 在实际应用中，这里会处理搜索请求
    alert(`正在搜索 ${selectedKnowledgeBaseIds.length} 个知识库中的 "${searchQuery}"`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">家族研究平台数据库</h1>
            <p className="text-gray-500">查询、分析和可视化家族研究相关的数据</p>
          </div>
        </div>

        <Card className="shadow-sm mb-8">
          <CardHeader className="pb-2">
            <CardTitle>数据源选择</CardTitle>
            <CardDescription>选择您要查询的知识库，可以选择多个知识库进行跨库检索</CardDescription>
          </CardHeader>
          <CardContent>
            <MultiSelectKnowledgeBase
              knowledgeBases={knowledgeBases}
              selectedIds={selectedKnowledgeBaseIds}
              onChange={setSelectedKnowledgeBaseIds}
              placeholder="请选择一个或多个知识库..."
            />
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full grid grid-cols-3 rounded-none bg-gray-100 p-0 h-12">
              <TabsTrigger
                value="search"
                className="rounded-none data-[state=active]:bg-white data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-emerald-500 h-full"
              >
                <Search className="h-4 w-4 mr-2" />
                全文检索
              </TabsTrigger>
              <TabsTrigger
                value="advanced"
                className="rounded-none data-[state=active]:bg-white data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-emerald-500 h-full"
              >
                <FileText className="h-4 w-4 mr-2" />
                高级查询
              </TabsTrigger>
              <TabsTrigger
                value="analytics"
                className="rounded-none data-[state=active]:bg-white data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-emerald-500 h-full"
              >
                <BarChart className="h-4 w-4 mr-2" />
                数据分析
              </TabsTrigger>
            </TabsList>

            <TabsContent value="search" className="p-6">
              <div className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">关键词搜索</label>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="输入搜索关键词..."
                      className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">时间范围</label>
                    <div className="flex items-center space-x-2">
                      <div className="relative flex-1">
                        <input
                          type="text"
                          placeholder="开始日期"
                          className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                        />
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      </div>
                      <span className="text-gray-500">至</span>
                      <div className="relative flex-1">
                        <input
                          type="text"
                          placeholder="结束日期"
                          className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                        />
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">排序方式</label>
                    <div className="relative">
                      <select className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent appearance-none bg-white">
                        <option value="relevance">相关性排序</option>
                        <option value="time_desc">时间降序（新→旧）</option>
                        <option value="time_asc">时间升序（旧→新）</option>
                        <option value="importance">重要性排序</option>
                      </select>
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    className="bg-emerald-600 hover:bg-emerald-700"
                    onClick={handleSearch}
                    disabled={selectedKnowledgeBaseIds.length === 0 || !searchQuery.trim()}
                  >
                    <Search className="h-4 w-4 mr-2" />
                    搜索
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="p-6">
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium mb-2">高级查询功能</p>
                <p className="text-sm">此功能允许您构建复杂的查询条件，精确定位所需数据</p>
              </div>
            </TabsContent>

            <TabsContent value="analytics" className="p-6">
              <div className="text-center py-8 text-gray-500">
                <BarChart className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium mb-2">数据分析功能</p>
                <p className="text-sm">此功能允许您对数据进行统计分析和可视化展示</p>
              </div>
            </TabsContent>
          </Tabs>
        </Card>
      </div>
    </div>
  )
}
