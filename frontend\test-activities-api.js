// 测试前端活动API调用
// 在浏览器控制台中运行此代码

// 检查用户认证状态
function checkAuthStatus() {
  const token = localStorage.getItem('hefamily_token');
  const userData = localStorage.getItem('hefamily_user_data');
  const isLoggedIn = localStorage.getItem('isLoggedIn');
  
  console.log('认证状态检查:');
  console.log('Token:', token ? '存在' : '不存在');
  console.log('用户数据:', userData ? JSON.parse(userData) : '不存在');
  console.log('登录状态:', isLoggedIn);
  
  return {
    isAuthenticated: !!token && isLoggedIn === 'true',
    userData: userData ? JSON.parse(userData) : null
  };
}

// 测试活动API调用
async function testActivitiesAPI() {
  try {
    const authStatus = checkAuthStatus();
    console.log('用户认证状态:', authStatus.isAuthenticated ? '已登录' : '未登录');
    if (authStatus.userData) {
      console.log('用户角色:', authStatus.userData.role);
    }
    
    // 测试普通模式API调用
    console.log('\n测试普通模式API调用 (只获取已发布活动):');
    const normalResponse = await fetch('/api/activities?status=published');
    const normalData = await normalResponse.json();
    console.log('状态码:', normalResponse.status);
    console.log('活动数量:', normalData.data.activities.length);
    
    // 测试管理模式API调用
    console.log('\n测试管理模式API调用 (获取所有状态活动):');
    const adminResponse = await fetch('/api/activities', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('hefamily_token')}`
      }
    });
    const adminData = await adminResponse.json();
    console.log('状态码:', adminResponse.status);
    console.log('活动数量:', adminData.data.activities.length);
    
    if (adminData.data.activities.length > 0) {
      // 统计各状态活动数量
      const statusCount = {
        published: 0,
        draft: 0,
        archived: 0
      };
      
      adminData.data.activities.forEach(activity => {
        if (activity.status === 'published') statusCount.published++;
        else if (activity.status === 'draft') statusCount.draft++;
        else if (activity.status === 'archived') statusCount.archived++;
      });
      
      console.log('活动状态统计:');
      console.log(`已发布: ${statusCount.published}`);
      console.log(`草稿: ${statusCount.draft}`);
      console.log(`已归档: ${statusCount.archived}`);
    }
    
    return {
      normalMode: normalData,
      adminMode: adminData
    };
  } catch (error) {
    console.error('API测试失败:', error);
    return null;
  }
}

// 执行测试
testActivitiesAPI();
