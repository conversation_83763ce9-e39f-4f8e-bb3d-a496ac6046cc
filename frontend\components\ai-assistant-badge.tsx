import React from 'react';

type AIAssistantType = 'personal-topic' | 'data-query' | 'research' | 'knowledge-file';

interface AIAssistantBadgeProps {
  type: AIAssistantType;
}

/**
 * AI助手类型标签组件
 * 根据助手类型显示不同颜色的标签
 * @param type - AI助手类型
 * @returns 带有颜色的标签组件
 */
export function AIAssistantBadge({ type }: AIAssistantBadgeProps) {
  // 根据助手类型定义颜色和文本
  const getBadgeConfig = (type: AIAssistantType) => {
    switch (type) {
      case 'personal-topic':
        return {
          color: 'bg-blue-100 text-blue-800',
          text: '个人专题助手'
        };
      case 'data-query':
        return {
          color: 'bg-green-100 text-green-800',
          text: '数据查询助手'
        };
      case 'research':
        return {
          color: 'bg-purple-100 text-purple-800',
          text: 'AI研究助手'
        };
      case 'knowledge-file':
        return {
          color: 'bg-orange-100 text-orange-800',
          text: '知识库文件分析助手'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          text: '未知类型'
        };
    }
  };

  const { color, text } = getBadgeConfig(type);

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}>
      {text}
    </span>
  );
}
