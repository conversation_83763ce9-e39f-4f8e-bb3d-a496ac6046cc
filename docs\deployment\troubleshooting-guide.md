# 和富家族研究平台故障排除指南

本文档提供了和富家族研究平台常见问题的故障排除步骤和解决方案，帮助管理员和开发人员快速解决系统运行中可能遇到的问题。

## 1. 系统启动问题

### 1.1 后端服务无法启动

**症状**: 运行`npm run start`或`pm2 start`命令后，后端服务无法正常启动。

**可能原因**:
- 端口被占用
- 环境变量配置错误
- 数据库连接失败
- 依赖包缺失或版本不兼容

**解决方案**:

1. **检查端口占用**:
   ```bash
   # Linux/macOS
   lsof -i :5000
   
   # Windows
   netstat -ano | findstr :5000
   ```
   如果端口被占用，可以修改`.env`文件中的`PORT`值或终止占用该端口的进程。

2. **验证环境变量**:
   确保`.env`文件存在且包含所有必需的配置项。可以对照`.env.example`文件检查。

3. **检查数据库连接**:
   ```bash
   # 测试数据库连接
   mysql -u your_username -p -h your_host your_database
   ```
   确保数据库服务正在运行，并且配置的用户名、密码、主机和数据库名称正确。

4. **检查依赖包**:
   ```bash
   # 重新安装依赖
   rm -rf node_modules
   npm install
   ```

5. **检查日志**:
   ```bash
   # 如果使用PM2
   pm2 logs hefamily-backend
   
   # 如果直接运行
   npm run start
   ```
   查看详细的错误信息，根据错误信息进行针对性排查。

### 1.2 前端服务无法启动

**症状**: 运行`npm run start`或`pm2 start`命令后，前端服务无法正常启动。

**可能原因**:
- 端口被占用
- 构建文件缺失
- 环境变量配置错误
- 依赖包缺失或版本不兼容

**解决方案**:

1. **检查端口占用**:
   ```bash
   # Linux/macOS
   lsof -i :3000
   
   # Windows
   netstat -ano | findstr :3000
   ```
   如果端口被占用，可以修改`package.json`中的启动脚本，添加`--port`参数。

2. **重新构建前端**:
   ```bash
   npm run build
   ```
   确保构建过程没有错误，并且生成了`.next`目录。

3. **验证环境变量**:
   确保`.env.local`文件存在且包含所有必需的配置项。

4. **检查依赖包**:
   ```bash
   # 重新安装依赖
   rm -rf node_modules
   npm install
   ```

5. **检查日志**:
   ```bash
   # 如果使用PM2
   pm2 logs hefamily-frontend
   
   # 如果直接运行
   npm run start
   ```

## 2. 数据库问题

### 2.1 数据库连接失败

**症状**: 应用启动时报数据库连接错误，或者运行时数据库操作失败。

**可能原因**:
- 数据库服务未运行
- 数据库凭据错误
- 数据库网络连接问题
- 数据库权限问题

**解决方案**:

1. **检查数据库服务**:
   ```bash
   # MySQL服务状态 (Linux)
   sudo systemctl status mysql
   
   # 启动MySQL服务 (Linux)
   sudo systemctl start mysql
   
   # Windows服务管理器
   services.msc
   ```
   确保MySQL服务正在运行。

2. **验证数据库凭据**:
   检查`.env`文件中的数据库配置是否正确：
   ```
   DB_HOST=localhost
   DB_PORT=3306
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   DB_NAME=your_database
   ```
   尝试使用这些凭据手动连接数据库。

3. **检查网络连接**:
   如果数据库在远程服务器上，确保网络连接正常，并且防火墙允许连接。
   ```bash
   # 测试连接
   telnet your_db_host 3306
   ```

4. **检查数据库权限**:
   确保配置的数据库用户有足够的权限访问指定的数据库。
   ```sql
   -- 在MySQL中检查用户权限
   SHOW GRANTS FOR 'your_username'@'localhost';
   
   -- 如需要，授予权限
   GRANT ALL PRIVILEGES ON your_database.* TO 'your_username'@'localhost';
   FLUSH PRIVILEGES;
   ```

### 2.2 数据库迁移失败

**症状**: 运行数据库迁移命令时失败。

**可能原因**:
- 数据库连接问题
- 迁移文件错误
- 数据库已存在冲突的表或数据

**解决方案**:

1. **检查数据库连接**:
   确保数据库连接正常，参考上一节的解决方案。

2. **检查迁移文件**:
   查看`migrations`目录中的迁移文件，确保语法正确。

3. **重置迁移状态**:
   ```bash
   # 撤销所有迁移
   npx sequelize-cli db:migrate:undo:all
   
   # 重新运行迁移
   npx sequelize-cli db:migrate
   ```

4. **检查Sequelize版本**:
   确保使用的Sequelize版本与迁移文件兼容。

5. **手动检查数据库**:
   登录数据库，检查表结构和数据，查找可能的冲突。
   ```sql
   SHOW TABLES;
   DESCRIBE table_name;
   ```

## 3. API问题

### 3.1 API请求返回401未授权

**症状**: 前端调用API时收到401 Unauthorized响应。

**可能原因**:
- JWT令牌缺失或无效
- JWT密钥配置错误
- 用户会话已过期
- 认证中间件配置错误

**解决方案**:

1. **检查JWT令牌**:
   在浏览器开发者工具中检查请求头，确保包含有效的Authorization头。
   ```
   Authorization: Bearer your_jwt_token
   ```

2. **验证JWT配置**:
   检查后端`.env`文件中的JWT配置：
   ```
   JWT_SECRET=your_jwt_secret
   JWT_EXPIRES_IN=7d
   ```
   确保前后端使用相同的JWT密钥。

3. **检查用户登录状态**:
   尝试重新登录，获取新的JWT令牌。

4. **检查认证中间件**:
   查看`middlewares/authMiddleware.js`文件，确保认证逻辑正确。

5. **检查后端日志**:
   查看后端日志，寻找与认证相关的错误信息。

### 3.2 API请求返回500内部服务器错误

**症状**: 前端调用API时收到500 Internal Server Error响应。

**可能原因**:
- 后端代码错误
- 数据库操作失败
- 外部服务调用失败
- 服务器资源不足

**解决方案**:

1. **检查后端日志**:
   ```bash
   # 如果使用PM2
   pm2 logs hefamily-backend
   
   # 如果直接运行
   npm run start
   ```
   查看详细的错误堆栈信息。

2. **检查数据库操作**:
   确保数据库连接正常，并且相关的数据库操作没有错误。

3. **检查外部服务**:
   如果API依赖外部服务（如Dify API），确保这些服务可用且配置正确。

4. **检查服务器资源**:
   ```bash
   # 检查内存和CPU使用情况
   free -m
   top
   ```
   确保服务器有足够的资源运行应用。

5. **启用详细日志**:
   在开发环境中，设置`NODE_ENV=development`以获取更详细的错误信息。

## 4. 文件上传问题

### 4.1 文件上传失败

**症状**: 尝试上传文件时失败，可能收到错误消息或无响应。

**可能原因**:
- 文件大小超过限制
- 文件类型不被允许
- 上传目录权限问题
- 磁盘空间不足

**解决方案**:

1. **检查文件大小限制**:
   查看后端配置中的文件大小限制：
   ```
   MAX_FILE_SIZE=10485760 # 10MB
   ```
   确保上传的文件不超过此限制。

2. **检查文件类型**:
   查看允许的文件类型列表：
   ```
   ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif
   ```
   确保上传的文件类型在此列表中。

3. **检查上传目录权限**:
   ```bash
   # 检查目录权限
   ls -la uploads/
   
   # 如需要，修改权限
   chmod 755 uploads/
   chown your_user:your_group uploads/
   ```
   确保应用有权限写入上传目录。

4. **检查磁盘空间**:
   ```bash
   # 检查磁盘空间
   df -h
   ```
   确保有足够的磁盘空间存储上传的文件。

5. **检查Multer配置**:
   查看`middlewares/uploadMiddleware.js`文件，确保Multer配置正确。

### 4.2 无法访问上传的文件

**症状**: 文件上传成功，但无法通过URL访问。

**可能原因**:
- 静态文件服务配置错误
- 文件路径错误
- 文件权限问题
- Nginx配置错误（如果使用）

**解决方案**:

1. **检查静态文件服务配置**:
   查看后端代码中的静态文件服务配置：
   ```javascript
   app.use('/uploads', express.static('uploads'));
   ```
   确保配置正确。

2. **验证文件路径**:
   检查数据库中存储的文件路径是否正确，并且文件实际存在于该路径。

3. **检查文件权限**:
   确保文件有适当的读取权限。

4. **检查Nginx配置**:
   如果使用Nginx作为反向代理，检查Nginx配置中的静态文件路径：
   ```nginx
   location /uploads {
       alias /path/to/uploads;
   }
   ```

## 5. 前端问题

### 5.1 页面加载白屏

**症状**: 访问前端页面时显示白屏，没有内容或错误信息。

**可能原因**:
- JavaScript错误
- 资源加载失败
- 路由配置错误
- 构建文件缺失

**解决方案**:

1. **检查浏览器控制台**:
   打开浏览器开发者工具，查看控制台中的错误信息。

2. **检查网络请求**:
   在浏览器开发者工具的Network标签中，查看资源加载情况。

3. **验证构建文件**:
   确保`.next`目录中包含所有必要的构建文件。

4. **检查路由配置**:
   查看`app`目录中的路由配置，确保没有错误。

5. **尝试清除缓存**:
   清除浏览器缓存，或使用隐私模式访问页面。

### 5.2 组件渲染错误

**症状**: 页面部分内容无法正常显示，或显示错误信息。

**可能原因**:
- 组件代码错误
- 数据格式不匹配
- 状态管理问题
- 样式冲突

**解决方案**:

1. **检查组件代码**:
   查看相关组件的代码，寻找可能的错误。

2. **检查API响应数据**:
   确保API返回的数据格式符合组件的预期。

3. **检查状态管理**:
   如果使用Context或其他状态管理工具，确保状态正确传递。

4. **检查样式**:
   查看是否有样式冲突导致组件显示异常。

5. **使用React开发者工具**:
   安装React Developer Tools浏览器扩展，检查组件树和状态。

## 6. 外部服务集成问题

### 6.1 Dify API集成失败

**症状**: 调用Dify API时失败，AI助手功能无法正常工作。

**可能原因**:
- API密钥或端点配置错误
- 请求格式错误
- 网络连接问题
- Dify服务不可用

**解决方案**:

1. **验证API配置**:
   检查`.env`文件中的Dify API配置：
   ```
   DIFY_API_KEY=your_dify_api_key
   DIFY_API_ENDPOINT=https://api.dify.ai
   DIFY_APP_ID=your_dify_app_id
   ```
   确保这些值正确。

2. **检查请求格式**:
   查看发送到Dify API的请求格式，确保符合API文档要求。

3. **测试API连接**:
   使用curl或Postman等工具直接测试Dify API：
   ```bash
   curl -X POST "https://api.dify.ai/v1/chat-messages" \
     -H "Authorization: Bearer your_dify_api_key" \
     -H "Content-Type: application/json" \
     -d '{"inputs":{},"query":"test","user":"test_user"}'
   ```

4. **检查Dify服务状态**:
   访问Dify官方网站或状态页面，确认服务是否正常运行。

5. **检查网络连接**:
   确保服务器可以访问Dify API端点。

### 6.2 文件分析服务集成失败

**症状**: 文件上传后无法进行分析，或分析结果不正确。

**可能原因**:
- 文件格式不支持
- 分析服务配置错误
- 文件内容问题
- 外部服务限制

**解决方案**:

1. **检查文件格式**:
   确保上传的文件格式受支持，并且文件内容有效。

2. **验证分析服务配置**:
   检查与文件分析相关的配置，确保正确。

3. **检查分析日志**:
   查看后端日志中与文件分析相关的信息，寻找可能的错误。

4. **测试分析服务**:
   使用已知可以成功分析的文件进行测试，验证服务是否正常工作。

5. **检查服务限制**:
   确认是否达到了外部服务的使用限制或配额。

## 7. 性能问题

### 7.1 系统响应缓慢

**症状**: 页面加载或API响应时间过长。

**可能原因**:
- 数据库查询效率低
- 服务器资源不足
- 代码优化不足
- 网络延迟
- 缓存配置不当

**解决方案**:

1. **优化数据库查询**:
   检查慢查询日志，优化数据库索引和查询语句。
   ```sql
   -- 启用慢查询日志
   SET GLOBAL slow_query_log = 'ON';
   SET GLOBAL long_query_time = 1;
   ```

2. **增加服务器资源**:
   如果服务器资源不足，考虑增加CPU、内存或磁盘空间。

3. **代码性能优化**:
   使用性能分析工具找出代码中的性能瓶颈，进行优化。
   ```bash
   # 使用Node.js内置的性能分析工具
   node --prof app.js
   ```

4. **启用缓存**:
   对频繁访问的数据启用缓存，减少数据库查询。
   ```javascript
   // 使用内存缓存
   const cache = new Map();
   
   // 使用Redis缓存
   const redis = require('redis');
   const client = redis.createClient();
   ```

5. **使用CDN**:
   对静态资源使用CDN，减少服务器负载。

### 7.2 内存泄漏

**症状**: 服务器内存使用量持续增长，最终可能导致应用崩溃。

**可能原因**:
- 代码中的内存泄漏
- 未释放的资源
- 大量长时间运行的异步操作
- 缓存过大

**解决方案**:

1. **监控内存使用**:
   ```bash
   # 使用PM2监控内存
   pm2 monit
   
   # 或使用Node.js内置的内存分析
   node --inspect app.js
   ```
   然后使用Chrome DevTools连接到Node.js进程，分析内存使用情况。

2. **检查代码中的内存泄漏**:
   查找可能导致内存泄漏的代码模式，如未清理的事件监听器、闭包等。

3. **定期重启服务**:
   如果无法立即解决内存泄漏问题，可以设置PM2定期重启服务：
   ```bash
   pm2 start app.js --name hefamily-backend --max-memory-restart 500M
   ```

4. **优化缓存策略**:
   设置合理的缓存大小和过期时间，避免缓存过大。

5. **使用内存分析工具**:
   使用专业的内存分析工具，如heapdump、memwatch等。

## 8. 安全问题

### 8.1 未授权访问

**症状**: 未登录用户能够访问需要认证的资源。

**可能原因**:
- 认证中间件配置错误
- 权限检查逻辑错误
- 前端路由保护不完善
- JWT验证失败

**解决方案**:

1. **检查认证中间件**:
   查看`middlewares/authMiddleware.js`文件，确保所有需要认证的路由都正确应用了认证中间件。

2. **验证权限检查**:
   确保权限检查逻辑正确，只有具有适当权限的用户才能访问受限资源。

3. **加强前端路由保护**:
   在前端实现路由守卫，阻止未认证用户访问需要认证的页面。

4. **检查JWT验证**:
   确保JWT验证逻辑正确，包括签名验证和过期检查。

5. **实施RBAC**:
   实施基于角色的访问控制，明确定义不同角色的权限。

### 8.2 跨站脚本攻击 (XSS)

**症状**: 网站可能受到XSS攻击，用户输入的脚本被执行。

**可能原因**:
- 输入验证不足
- 输出编码不足
- 内容安全策略 (CSP) 缺失
- 第三方库漏洞

**解决方案**:

1. **实施输入验证**:
   对所有用户输入进行严格验证，只接受预期的格式和内容。

2. **实施输出编码**:
   在显示用户输入时进行适当的HTML编码，防止脚本执行。

3. **配置内容安全策略**:
   在HTTP响应头中添加Content-Security-Policy头，限制脚本来源。
   ```
   Content-Security-Policy: default-src 'self'; script-src 'self' trusted-scripts.com;
   ```

4. **使用安全的第三方库**:
   定期更新第三方库，修复已知的安全漏洞。

5. **使用XSS过滤器**:
   使用专门的XSS过滤库，如DOMPurify，过滤用户输入。

## 9. 部署问题

### 9.1 部署失败

**症状**: 应用部署过程失败，无法完成部署。

**可能原因**:
- 构建错误
- 依赖问题
- 环境配置错误
- 权限问题
- 磁盘空间不足

**解决方案**:

1. **检查构建日志**:
   查看构建过程的日志，找出失败的具体原因。

2. **验证依赖**:
   确保所有依赖都正确安装，并且版本兼容。
   ```bash
   npm ci
   ```

3. **检查环境配置**:
   确保所有必需的环境变量都已正确设置。

4. **检查权限**:
   确保部署用户有足够的权限执行所有必要的操作。

5. **检查磁盘空间**:
   确保有足够的磁盘空间进行构建和部署。

### 9.2 部署后应用无法访问

**症状**: 部署完成后，无法通过URL访问应用。

**可能原因**:
- 服务未启动
- 端口配置错误
- 防火墙阻止
- DNS配置错误
- 反向代理配置错误

**解决方案**:

1. **检查服务状态**:
   ```bash
   # 如果使用PM2
   pm2 status
   
   # 如果使用systemd
   sudo systemctl status your-service
   ```
   确保服务正在运行。

2. **检查端口配置**:
   确保应用监听在正确的端口上，并且该端口可以被外部访问。

3. **检查防火墙**:
   ```bash
   # 检查防火墙规则
   sudo iptables -L
   
   # 如需要，允许端口通过
   sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
   sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
   ```

4. **检查DNS配置**:
   确保域名正确解析到服务器IP。
   ```bash
   nslookup your-domain.com
   ```

5. **检查反向代理配置**:
   如果使用Nginx或Apache作为反向代理，检查配置是否正确。

## 10. 联系支持

如果按照本指南无法解决问题，请联系技术支持团队：

- 邮箱: <EMAIL>
- 电话: +86 123 4567 8910

在联系支持时，请提供以下信息：
- 详细的错误信息和日志
- 问题发生的时间和频率
- 已尝试的解决方案
- 系统环境信息（操作系统、Node.js版本、数据库版本等）
