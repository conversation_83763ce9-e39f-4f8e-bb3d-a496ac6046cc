/**
 * 通知服务
 *
 * 处理通知的查询、标记已读等
 */

import apiService from './api-service'

// 通知类型
export interface Notification {
  id: number
  title: string
  content: string
  type: 'system' | 'comment' | 'file' | 'file_review' | 'file_review_result'
  is_read: boolean
  created_at: string
  updated_at?: string
  related_id?: number
  related_type?: string
  read?: boolean // 兼容旧版属性
}

// 通知查询参数
export interface NotificationQueryParams {
  page?: number
  limit?: number
  type?: string
  is_read?: boolean
}

// 已被 getNotifications 替代，保留此注释以便于理解代码历史

/**
 * 获取通知详情
 * @param id 通知ID
 * @returns 通知详情
 */
export async function getNotification(id: number) {
  try {
    const response = await apiService.get(`/notifications/${id}`);
    return response;
  } catch (error) {
    throw error;
  }
}

// 已被 markNotificationAsRead 和 markAllNotificationsAsRead 替代，保留此注释以便于理解代码历史

/**
 * 删除通知
 * @param id 通知ID
 * @returns 删除结果
 */
export async function deleteNotification(id: number) {
  try {
    const response = await apiService.del(`/notifications/${id}`);
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * 获取通知列表
 * @param params 查询参数
 * @returns 通知列表和分页信息
 */
export const getNotifications = async (params?: NotificationQueryParams): Promise<{
  notifications: Notification[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}> => {
  try {
    // 确保请求头中包含认证信息
    const token = localStorage.getItem('hefamily_token');
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    // 构建查询参数
    const queryParams = {
      page: params?.page || 1,
      limit: params?.limit || 10,
      status: params?.is_read === false ? 'unread' : params?.is_read === true ? 'read' : 'all'
    };

    // 调用API服务
    const response = await apiService.get('/notifications', queryParams, { headers });

    // 如果响应中没有通知数据，返回空数组
    if (!response || !response.notifications) {
      return {
        notifications: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 10,
          totalPages: 0
        }
      };
    }

    return response;
  } catch (error) {
    console.error('获取通知列表失败:', error);
    // 出错时返回空数据
    return {
      notifications: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      }
    };
  }
}

/**
 * 获取未读通知数量
 * @returns 未读通知数量，如果API不可用则返回0
 */
export const getUnreadNotificationCount = async (): Promise<number> => {
  try {
    // 确保请求头中包含认证信息
    const token = localStorage.getItem('hefamily_token');
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    const response = await apiService.get<{ unread_count: number }>('/notifications/unread-count', {}, { headers });
    return response.unread_count || 0;
  } catch (error) {
    console.warn('获取未读通知数量失败:', error);
    return 0;
  }
}

/**
 * 标记通知为已读
 * @param id 通知ID
 * @returns 标记结果
 */
export const markNotificationAsRead = async (id: string): Promise<{ message: string }> => {
  try {
    const response = await apiService.put(`/notifications/${id}/read`);
    return response;
  } catch (error) {
    console.error('标记通知为已读失败:', error);
    throw error;
  }
}

/**
 * 标记所有通知为已读
 * @returns 标记结果
 */
export const markAllNotificationsAsRead = async (): Promise<{ message: string }> => {
  try {
    // 确保请求头中包含认证信息
    const token = localStorage.getItem('hefamily_token');
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    const response = await apiService.put('/notifications/read-all', null, { headers });
    return response;
  } catch (error) {
    console.error('标记所有通知为已读失败:', error);
    throw error;
  }
}

/**
 * 删除所有通知
 */
export const deleteAllNotifications = async (): Promise<{ message: string }> => {
  return await apiService.del<{ message: string }>('/notifications')
}

export default {
  getNotifications,
  getUnreadNotificationCount,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  deleteAllNotifications
}
