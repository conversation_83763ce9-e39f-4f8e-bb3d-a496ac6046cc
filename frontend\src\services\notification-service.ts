/**
 * 通知服务
 * 
 * 提供与通知相关的API调用
 */

import axios from 'axios';
import { 
  Notification, 
  NotificationListParams, 
  NotificationListData,
  NotificationType,
  NotificationStatus
} from '@/components/notification/types';

// 获取API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5001/api';

// 通知服务
const notificationService = {
  /**
   * 获取通知列表
   * @param params 查询参数
   * @returns 通知列表数据
   */
  async getNotifications(params?: NotificationListParams): Promise<NotificationListData> {
    try {
      const response = await axios.get(`${API_BASE_URL}/notifications`, { params });
      return response.data.data;
    } catch (error) {
      console.error('获取通知列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取未读通知数量
   * @returns 未读通知数量
   */
  async getUnreadCount(): Promise<number> {
    try {
      const response = await axios.get(`${API_BASE_URL}/notifications/unread-count`);
      return response.data.data.count;
    } catch (error) {
      console.error('获取未读通知数量失败:', error);
      throw error;
    }
  },

  /**
   * 获取通知详情
   * @param id 通知ID
   * @returns 通知详情
   */
  async getNotificationById(id: string): Promise<Notification> {
    try {
      const response = await axios.get(`${API_BASE_URL}/notifications/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('获取通知详情失败:', error);
      throw error;
    }
  },

  /**
   * 标记通知为已读
   * @param id 通知ID
   * @returns 操作结果
   */
  async markAsRead(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.put(`${API_BASE_URL}/notifications/${id}/read`);
      return response.data;
    } catch (error) {
      console.error('标记通知为已读失败:', error);
      throw error;
    }
  },

  /**
   * 标记所有通知为已读
   * @returns 操作结果
   */
  async markAllAsRead(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.put(`${API_BASE_URL}/notifications/read-all`);
      return response.data;
    } catch (error) {
      console.error('标记所有通知为已读失败:', error);
      throw error;
    }
  },

  /**
   * 删除通知
   * @param id 通知ID
   * @returns 操作结果
   */
  async deleteNotification(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.delete(`${API_BASE_URL}/notifications/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除通知失败:', error);
      throw error;
    }
  },

  /**
   * 删除所有通知
   * @returns 操作结果
   */
  async deleteAllNotifications(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.delete(`${API_BASE_URL}/notifications/delete-all`);
      return response.data;
    } catch (error) {
      console.error('删除所有通知失败:', error);
      throw error;
    }
  },

  /**
   * 创建通知（仅用于测试）
   * @param data 通知数据
   * @returns 创建的通知
   */
  async createNotification(data: {
    title: string;
    content: string;
    type: NotificationType;
    related_id?: string;
    related_type?: string;
  }): Promise<Notification> {
    try {
      const response = await axios.post(`${API_BASE_URL}/notifications`, data);
      return response.data.data;
    } catch (error) {
      console.error('创建通知失败:', error);
      throw error;
    }
  }
};

export default notificationService;
