"use client"

import { useState, useEffect } from "react"
import { Upload, FileText, RefreshCw, CheckCircle, AlertCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { uploadFileToDify } from "@/services/file-service"
import { useAuth } from "@/contexts/auth-context"

// 组件属性
interface FileAnalysisAssistantProps {
  fileId: string
  onAnalysisComplete: (summary: string, detailedDescription: string) => void
  initialSummary?: string
  initialDetailedDescription?: string
}

/**
 * 知识库文件分析助手组件
 *
 * 用于分析文件内容，生成摘要和详细描述
 * 使用Dify API进行文件分析
 */
export function FileAnalysisAssistant({
  fileId,
  onAnalysisComplete,
  initialSummary = "",
  initialDetailedDescription = ""
}: FileAnalysisAssistantProps) {
  const { isLoggedIn } = useAuth()
  const [summary, setSummary] = useState(initialSummary)
  const [detailedDescription, setDetailedDescription] = useState(initialDetailedDescription)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [error, setError] = useState("")

  // 当初始值变化时更新状态
  useEffect(() => {
    setSummary(initialSummary)
    setDetailedDescription(initialDetailedDescription)
  }, [initialSummary, initialDetailedDescription])

  // 分析文件
  const handleAnalyzeFile = async () => {
    if (!fileId) {
      setError("文件ID不能为空")
      return
    }

    if (!isLoggedIn) {
      // 触发登录弹窗
      const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } })
      window.dispatchEvent(event)
      return
    }

    setIsAnalyzing(true)
    setError("")

    try {
      // 调用文件上传到Dify知识库API
      const result = await uploadFileToDify(fileId)

      // 更新摘要和详细描述
      setSummary(result.summary || "")
      setDetailedDescription(result.detailed_description || "")

      // 回调通知父组件
      onAnalysisComplete(result.summary || "", result.detailed_description || "")

      // 显示成功提示
      toast({
        title: "分析成功",
        description: "文件已成功分析",
      })
    } catch (error: any) {
      console.error("分析文件失败:", error)
      setError(error.response?.data?.message || "分析文件失败，请稍后再试")

      // 显示错误提示
      toast({
        title: "分析失败",
        description: error.response?.data?.message || "分析文件失败，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsAnalyzing(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader className="px-4 py-3 border-b">
        <CardTitle className="text-lg">知识库文件分析</CardTitle>
      </CardHeader>
      <CardContent className="p-4 space-y-4">
        {error && (
          <div className="bg-destructive/10 text-destructive p-3 rounded-md flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 shrink-0 mt-0.5" />
            <p>{error}</p>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="summary">文件摘要</Label>
          <Textarea
            id="summary"
            value={summary}
            onChange={(e) => setSummary(e.target.value)}
            className="min-h-[100px]"
            placeholder="文件摘要将在这里显示"
            readOnly={isAnalyzing}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="detailed-description">详细描述</Label>
          <Textarea
            id="detailed-description"
            value={detailedDescription}
            onChange={(e) => setDetailedDescription(e.target.value)}
            className="min-h-[200px]"
            placeholder="文件详细描述将在这里显示"
            readOnly={isAnalyzing}
          />
        </div>

        <Button
          onClick={handleAnalyzeFile}
          disabled={isAnalyzing || !fileId}
          className="w-full bg-[#1e7a43] hover:bg-[#1e7a43]/90"
        >
          {isAnalyzing ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              分析中...
            </>
          ) : (
            "开始分析"
          )}
        </Button>
      </CardContent>
    </Card>
  )
}
