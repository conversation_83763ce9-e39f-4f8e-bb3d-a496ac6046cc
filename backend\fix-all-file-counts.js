/**
 * 修复知识库文件数量脚本
 * 
 * 用于修复知识库文件数量统计，确保知识库的file_count字段与实际已批准文件数量一致
 */

const { sequelize, KnowledgeBase, File } = require('./src/models');

async function fixAllFileCounts() {
  try {
    console.log('开始修复知识库文件数量...');
    console.log('='.repeat(80));
    
    // 获取所有知识库
    const knowledgeBases = await KnowledgeBase.findAll({
      order: [['id', 'ASC']]
    });
    
    console.log(`找到 ${knowledgeBases.length} 个知识库`);
    console.log('='.repeat(80));
    
    let fixedCount = 0;
    
    // 修复每个知识库的文件数量
    for (const kb of knowledgeBases) {
      // 获取已批准的文件数量
      const approvedFilesCount = await File.count({
        where: {
          knowledge_base_id: kb.id,
          status: 'approved'
        }
      });
      
      // 获取已批准的文件总大小
      const approvedFilesResult = await File.findAll({
        where: {
          knowledge_base_id: kb.id,
          status: 'approved'
        },
        attributes: [
          [sequelize.fn('SUM', sequelize.col('size')), 'total_size']
        ],
        raw: true
      });
      
      const approvedFilesSize = approvedFilesResult[0].total_size || 0;
      
      // 输出知识库信息
      console.log(`知识库 #${kb.id}: ${kb.name} (${kb.type})`);
      console.log(`  - 当前记录的文件数量: ${kb.file_count}`);
      console.log(`  - 实际已批准文件数量: ${approvedFilesCount}`);
      console.log(`  - 当前记录的存储大小: ${kb.storage_size}`);
      console.log(`  - 实际已批准文件存储大小: ${approvedFilesSize}`);
      
      // 检查是否需要修复
      if (kb.file_count !== approvedFilesCount || kb.storage_size !== approvedFilesSize) {
        console.log(`  - [需要修复] 更新知识库文件数量和存储大小`);
        
        // 更新知识库的文件计数和存储大小
        await kb.update({
          file_count: approvedFilesCount,
          storage_size: approvedFilesSize
        });
        
        console.log(`  - [已修复] 知识库 ${kb.id} 的文件数量已更新为 ${approvedFilesCount}，存储大小已更新为 ${approvedFilesSize} 字节`);
        fixedCount++;
      } else {
        console.log(`  - [无需修复] 文件数量和存储大小已正确`);
      }
      
      console.log('-'.repeat(80));
    }
    
    // 输出总计
    console.log('='.repeat(80));
    console.log(`修复完成: 共修复了 ${fixedCount} 个知识库的文件数量`);
    console.log('='.repeat(80));
    
    // 关闭数据库连接
    await sequelize.close();
    console.log('修复完成');
  } catch (error) {
    console.error('修复知识库文件数量失败:', error);
    process.exit(1);
  }
}

// 执行修复
fixAllFileCounts();
