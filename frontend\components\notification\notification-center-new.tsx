"use client"

import { useState, useEffect } from "react"
import { Bell } from "lucide-react"
import { useRouter } from "next/navigation"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useAuth } from "@/contexts/auth-context"
import {
  getUnreadNotificationCount,
  markAllNotificationsAsRead,
  getNotifications,
  Notification
} from "@/services/notification-service"
import { toast } from "@/components/ui/use-toast"

/**
 * 通知中心组件
 *
 * 显示用户的通知信息，包括未读通知数量
 */
export function NotificationCenterNew() {
  const [unreadCount, setUnreadCount] = useState(0)
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([])
  const { isLoggedIn } = useAuth()
  const router = useRouter()

  /**
   * 获取未读通知数量
   * 从API获取未读通知数量并更新状态
   */
  const fetchUnreadCount = async () => {
    try {
      const count = await getUnreadNotificationCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('获取未读通知数量失败:', error);
      // 出错时不显示未读数量
      setUnreadCount(0);
    }
  }

  /**
   * 标记所有通知为已读
   * 调用API将所有通知标记为已读并更新本地状态
   * @param {React.MouseEvent} e - 点击事件
   */
  const handleMarkAllAsRead = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (unreadCount === 0) return;

    try {
      setIsLoading(true);

      // 调用API标记所有通知为已读
      await markAllNotificationsAsRead();

      // 更新未读数量
      setUnreadCount(0);

      // 更新通知列表中的已读状态
      setNotifications(prev => prev.map(notification => ({
        ...notification,
        read: true,
        is_read: true
      })));

      toast({
        title: "标记成功",
        description: "所有通知已标记为已读"
      });

      // 重新获取通知列表，而不是刷新整个页面
      fetchNotifications();
    } catch (error) {
      console.error("标记所有通知为已读失败:", error);
      toast({
        title: "操作失败",
        description: "无法标记所有通知为已读，请稍后重试",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }

  /**
   * 获取通知列表
   * 从API获取通知列表并更新状态
   */
  const fetchNotifications = async () => {
    if (!isLoggedIn) return;

    try {
      setIsLoading(true);

      const response = await getNotifications({ limit: 5 });

      if (response && response.notifications) {
        setNotifications(response.notifications);
      } else {
        setNotifications([]);
      }
    } catch (error) {
      console.error('获取通知列表失败:', error);
      setNotifications([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取未读通知数量
  useEffect(() => {
    if (isLoggedIn) {
      fetchUnreadCount();

      // 每分钟刷新一次未读数量
      const interval = setInterval(fetchUnreadCount, 60000);

      return () => clearInterval(interval);
    }
  }, [isLoggedIn]);

  // 当下拉菜单打开时获取通知列表
  useEffect(() => {
    if (isOpen) {
      fetchNotifications();
    }
  }, [isOpen, isLoggedIn]);

  if (!isLoggedIn) return null;

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <button className="relative p-1 rounded-full hover:bg-gray-100 transition-colors">
          <Bell className="h-5 w-5 text-gray-600" />
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 inline-flex items-center justify-center w-4 h-4 text-xs font-bold text-white bg-red-500 rounded-full">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80 rounded-md shadow-lg">
        <div className="p-3 border-b flex justify-between items-center">
          <p className="font-medium">通知中心</p>
          {unreadCount > 0 && (
            <button
              className="text-xs text-[#1e8e3e] hover:underline"
              onClick={handleMarkAllAsRead}
              disabled={isLoading}
            >
              {isLoading ? '处理中...' : '全部标为已读'}
            </button>
          )}
        </div>
        <div className="max-h-80 overflow-y-auto">
          {isLoading ? (
            <div className="flex justify-center items-center py-4">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900"></div>
            </div>
          ) : notifications.length > 0 ? (
            notifications.map(notification => (
              <DropdownMenuItem
                key={notification.id}
                className="p-3 hover:bg-gray-50 cursor-pointer"
                onSelect={(e) => {
                  // 阻止默认行为，防止关闭下拉菜单
                  e.preventDefault();
                }}
              >
                <div className={notification.is_read ? '' : 'font-semibold'}>
                  <p className="font-medium">{notification.title}</p>
                  <p className="text-sm text-gray-500">{notification.content}</p>
                  <p className="text-xs text-gray-400 mt-1">
                    {new Date(notification.created_at).toLocaleString()}
                  </p>
                </div>
              </DropdownMenuItem>
            ))
          ) : (
            <div className="p-3 text-center text-gray-500 text-sm">
              暂无通知
            </div>
          )}
        </div>
        <div className="p-2 border-t text-center">
          <button
            className="text-sm text-[#1e8e3e] hover:underline"
            onClick={() => {
              setIsOpen(false);
              router.push('/personal-center/notifications');
            }}
          >
            查看全部通知
          </button>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
