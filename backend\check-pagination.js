/**
 * 检查分页逻辑脚本
 * 
 * 用于模拟文件列表API的分页逻辑，检查是否有问题
 */

const { sequelize, File, KnowledgeBase, User } = require('./src/models');
const { Op } = require('sequelize');

async function checkPagination() {
  try {
    console.log('开始检查分页逻辑...');
    console.log('='.repeat(80));
    
    // 模拟管理员用户
    const adminUser = { role: 'admin', id: 1 };
    
    // 模拟API请求参数
    const page = 1;
    const limit = 10;
    const offset = (page - 1) * limit;
    
    console.log(`模拟请求参数: page=${page}, limit=${limit}, offset=${offset}`);
    console.log('='.repeat(80));
    
    // 构建查询条件 - 不添加状态过滤
    let whereConditions = {};
    
    console.log('查询条件:', JSON.stringify(whereConditions));
    
    // 执行查询
    const { count, rows: files } = await File.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name', 'type']
        },
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'email']
        }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });
    
    // 计算总页数
    const totalPages = Math.ceil(count / limit);
    
    console.log(`查询结果: 总数=${count}, 当前页数据=${files.length}, 总页数=${totalPages}`);
    console.log('='.repeat(80));
    
    // 输出文件列表
    console.log('当前页文件列表:');
    console.log('-'.repeat(80));
    console.log('ID | 文件名 | 知识库 | 状态 | 上传者');
    console.log('-'.repeat(80));
    
    for (const file of files) {
      console.log(`${file.id} | ${file.original_name} | ${file.knowledgeBase ? file.knowledgeBase.name : '未知'} | ${file.status} | ${file.uploader ? file.uploader.username : '未知'}`);
    }
    
    console.log('='.repeat(80));
    
    // 检查第二页
    if (totalPages > 1) {
      const page2 = 2;
      const offset2 = (page2 - 1) * limit;
      
      console.log(`检查第二页: page=${page2}, limit=${limit}, offset=${offset2}`);
      
      const { count: count2, rows: files2 } = await File.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: KnowledgeBase,
            as: 'knowledgeBase',
            attributes: ['id', 'name', 'type']
          },
          {
            model: User,
            as: 'uploader',
            attributes: ['id', 'username', 'email']
          }
        ],
        limit,
        offset: offset2,
        order: [['created_at', 'DESC']]
      });
      
      console.log(`第二页查询结果: 总数=${count2}, 当前页数据=${files2.length}`);
      console.log('-'.repeat(80));
      console.log('第二页文件列表:');
      console.log('-'.repeat(80));
      console.log('ID | 文件名 | 知识库 | 状态 | 上传者');
      console.log('-'.repeat(80));
      
      for (const file of files2) {
        console.log(`${file.id} | ${file.original_name} | ${file.knowledgeBase ? file.knowledgeBase.name : '未知'} | ${file.status} | ${file.uploader ? file.uploader.username : '未知'}`);
      }
    } else {
      console.log('没有第二页数据');
    }
    
    console.log('='.repeat(80));
    
    // 检查只查询已批准的文件
    console.log('检查只查询已批准的文件:');
    
    const approvedWhereConditions = { status: 'approved' };
    
    const { count: approvedCount, rows: approvedFiles } = await File.findAndCountAll({
      where: approvedWhereConditions,
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name', 'type']
        },
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'email']
        }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });
    
    // 计算已批准文件的总页数
    const approvedTotalPages = Math.ceil(approvedCount / limit);
    
    console.log(`已批准文件查询结果: 总数=${approvedCount}, 当前页数据=${approvedFiles.length}, 总页数=${approvedTotalPages}`);
    console.log('-'.repeat(80));
    console.log('已批准文件列表:');
    console.log('-'.repeat(80));
    console.log('ID | 文件名 | 知识库 | 状态 | 上传者');
    console.log('-'.repeat(80));
    
    for (const file of approvedFiles) {
      console.log(`${file.id} | ${file.original_name} | ${file.knowledgeBase ? file.knowledgeBase.name : '未知'} | ${file.status} | ${file.uploader ? file.uploader.username : '未知'}`);
    }
    
    console.log('='.repeat(80));
    
    // 关闭数据库连接
    await sequelize.close();
    console.log('检查完成');
  } catch (error) {
    console.error('检查分页逻辑失败:', error);
    process.exit(1);
  }
}

// 执行检查
checkPagination();
