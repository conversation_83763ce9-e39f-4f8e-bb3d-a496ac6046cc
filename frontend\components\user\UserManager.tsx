/**
 * 用户管理组件
 *
 * 用于系统管理页面中的用户管理功能，包括用户列表、搜索、筛选、编辑等功能
 */

import React, { useState, useEffect } from "react"
import { Search, Edit, Trash, X, CheckCircle, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { UserType, Role } from "./types"
import { UserTable } from "./UserTable"
import { UserModal } from "./UserModal"
import { DeleteUserModal } from "./DeleteUserModal"
import { ResetPasswordModal } from "./ResetPasswordModal"
import apiService from "@/services/api-service"
import { logger, sanitizeData } from "@/utils/logger"

// 组件属性定义
interface UserManagerProps {
  // 不再从外部接收角色列表，而是在组件内部从API获取
}

/**
 * 用户管理组件
 * @param props 组件属性
 */
const UserManager: React.FC<UserManagerProps> = () => {
  // 用户管理状态
  const [userSearchQuery, setUserSearchQuery] = useState("")
  const [userStatusFilter, setUserStatusFilter] = useState("全部状态")
  const [userRoleFilter, setUserRoleFilter] = useState("全部角色")
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [showUserModal, setShowUserModal] = useState(false)
  const [currentUser, setCurrentUser] = useState<UserType | null>(null)
  const [showDeleteUserModal, setShowDeleteUserModal] = useState(false)
  const [userToDelete, setUserToDelete] = useState<UserType | null>(null)
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false)
  const [userToResetPassword, setUserToResetPassword] = useState<UserType | null>(null)
  const [users, setUsers] = useState<UserType[]>([])
  const [loading, setLoading] = useState(false)

  // 不再使用模拟用户数据，而是从API获取真实数据

  // 角色数据从API获取，不再使用模拟数据
  const [roles, setRoles] = useState<Role[]>([])

  // 从API获取角色数据
  const fetchRoles = async () => {
    try {
      const token = localStorage.getItem('hefamily_token')
      if (!token) {
        logger.warn('未找到认证token，无法获取角色数据')
        setRoles([])
        return
      }

      // 尝试从标准API获取角色数据
      try {
        logger.debug('尝试从标准API获取角色数据，API基础URL:', apiService.getBaseUrl())
        const response = await apiService.get('/roles', null, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        logger.debug('角色数据API响应:', sanitizeData(response))

        // 处理不同的响应格式
        let roleData = []

        // 格式1: { success: true, data: [...] }
        if (response && response.success === true && Array.isArray(response.data)) {
          logger.debug('响应格式1: { success: true, data: [...] }')
          roleData = response.data
        }
        // 格式2: { data: [...] }
        else if (response && response.data && Array.isArray(response.data)) {
          logger.debug('响应格式2: { data: [...] }')
          roleData = response.data
        }
        // 格式3: [...]
        else if (response && Array.isArray(response)) {
          logger.debug('响应格式3: [...]')
          roleData = response
        }
        // 格式4: { success: true, data: [...] } 但在response.data中
        else if (response && response.data && response.data.success === true && Array.isArray(response.data.data)) {
          logger.debug('响应格式4: response.data = { success: true, data: [...] }')
          roleData = response.data.data
        }

        if (roleData && roleData.length > 0) {
          logger.debug(`找到${roleData.length}个角色`)

          const formattedRoles = roleData.map(role => {
            // 为特殊角色提供友好的名称
            let displayName = role.name;

            // 为系统预设角色提供友好的名称
            if (role.name === 'admin') {
              displayName = '管理员';
            } else if (role.name === 'basic_user' || role.name === '初级访问者' || role.name === '访问者') {
              displayName = '访问者';
            }

            return {
              id: role.id.toString(),
              name: displayName,
              description: role.description || '',
              userCount: role.user_count || 0,
              createdAt: new Date(role.created_at).toLocaleString('zh-CN'),
              isPreset: role.is_system || false
            };
          })
          logger.debug('格式化后的角色数据:', sanitizeData(formattedRoles))
          setRoles(formattedRoles)
          return
        } else {
          logger.warn('API响应中没有找到角色数据')
        }
      } catch (apiError) {
        logger.error('标准API获取角色数据失败:', sanitizeData(apiError))
        // 继续尝试其他方法
      }

      // 如果无法获取角色数据，至少提供一些基本角色
      logger.warn('无法从API获取角色数据，使用基本角色')
      setRoles([
        {
          id: "admin_default",
          name: "管理员",
          description: "可进行系统管理，用户管理等操作",
          userCount: 0,
          createdAt: new Date().toLocaleString('zh-CN'),
          isPreset: true
        },
        {
          id: "basic_default",
          name: "访问者",
          description: "新注册用户默认角色，仅具有基本查看权限",
          userCount: 0,
          createdAt: new Date().toLocaleString('zh-CN'),
          isPreset: true
        }
      ])
    } catch (error) {
      logger.error('获取角色数据失败:', sanitizeData(error))
      // 提供基本角色作为后备
      setRoles([
        {
          id: "admin_fallback",
          name: "管理员",
          description: "可进行系统管理，用户管理等操作",
          userCount: 0,
          createdAt: new Date().toLocaleString('zh-CN'),
          isPreset: true
        },
        {
          id: "basic_fallback",
          name: "访问者",
          description: "新注册用户默认角色，仅具有基本查看权限",
          userCount: 0,
          createdAt: new Date().toLocaleString('zh-CN'),
          isPreset: true
        }
      ])
    }
  }

  // 从API获取用户数据
  const fetchUsers = async () => {
    try {
      setLoading(true)

      // 检查是否有认证token
      const token = localStorage.getItem('hefamily_token')
      if (!token) {
        logger.warn('未找到认证token，无法获取用户数据')
        toast({
          title: "认证失败",
          description: "请先登录以查看用户列表",
          variant: "destructive"
        })
        setUsers([])
        return
      }

      // 尝试从标准API获取用户数据
      try {
        logger.debug('尝试从标准API获取用户数据')
        const response = await apiService.get('/users', null, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        logger.debug('API响应:', sanitizeData(response))

        // 处理不同的响应格式
        let userData = []

        // 格式1: { success: true, count: N, data: [...] }
        if (response && response.success === true && Array.isArray(response.data)) {
          logger.debug('响应格式1: { success: true, count: N, data: [...] }')
          userData = response.data
        }
        // 格式2: { data: [...] }
        else if (response && response.data && Array.isArray(response.data)) {
          logger.debug('响应格式2: { data: [...] }')
          userData = response.data
        }
        // 格式3: [...]
        else if (response && Array.isArray(response)) {
          logger.debug('响应格式3: [...]')
          userData = response
        }
        // 格式4: { success: true, count: N, data: [...] } 但在response.data中
        else if (response && response.data && response.data.success === true && Array.isArray(response.data.data)) {
          logger.debug('响应格式4: response.data = { success: true, count: N, data: [...] }')
          userData = response.data.data
        }

        if (userData && userData.length > 0) {
          logger.debug(`找到${userData.length}个用户`)

          // 将API返回的用户数据格式转换为组件使用的格式
          const formattedUsers = userData.map(user => ({
            id: user.id.toString(),
            name: user.username,
            phone: user.phone || '',
            email: user.email || '',
            roleId: user.role_id ? user.role_id.toString() : '',
            roleName: user.userRole ?
              (user.userRole.name === 'basic_user' ? '访问者' : user.userRole.name) :
              (user.role === 'admin' ? '管理员' : '访问者'),
            status: user.is_active ? '正常' : '已禁用',
            createdAt: new Date(user.created_at).toLocaleString('zh-CN')
          }))

          logger.debug(`格式化后有${formattedUsers.length}个用户`)
          setUsers(formattedUsers)
          return
        } else {
          logger.warn('API响应中没有找到用户数据')
        }
      } catch (apiError) {
        logger.error('标准API获取用户数据失败:', sanitizeData(apiError))
        // 继续尝试其他方法
      }

      // 不再尝试从数据库直接访问路由获取，直接使用基本用户数据
      logger.debug('标准API获取用户数据失败，使用基本用户数据')

      // 使用基本用户数据
      const basicUsers = [
        {
          id: "1",
          name: "管理员",
          phone: "13800000000",
          email: "<EMAIL>",
          roleId: "1",
          roleName: "管理员",
          status: "正常",
          createdAt: new Date().toLocaleString('zh-CN')
        },
        {
          id: "2",
          name: "测试用户",
          phone: "13900000000",
          email: "<EMAIL>",
          roleId: "4",
          roleName: "访问者",
          status: "正常",
          createdAt: new Date().toLocaleString('zh-CN')
        }
      ]

      setUsers(basicUsers)

      // 显示提示
      toast({
        title: "使用基本用户数据",
        description: "无法从API获取用户数据，使用基本用户数据代替",
        variant: "default"
      })

      return
    } catch (error) {
      logger.error('获取用户数据失败:', sanitizeData(error))

      // 如果是403错误，显示权限不足提示
      if (error.response && error.response.status === 403) {
        toast({
          title: "权限不足",
          description: "您没有权限查看用户列表，请使用管理员账号登录",
          variant: "destructive"
        })
      } else if (error.response && error.response.status === 401) {
        toast({
          title: "认证失败",
          description: "您的登录已过期，请重新登录",
          variant: "destructive"
        })
        // 清除过期的token
        localStorage.removeItem('hefamily_token')
        localStorage.removeItem('hefamily_user_info')
      } else {
        toast({
          title: "获取用户数据失败",
          description: "无法从服务器获取用户数据，请稍后重试",
          variant: "destructive"
        })
      }

      // 返回空数组，不使用模拟数据
      setUsers([])
    } finally {
      setLoading(false)
    }
  }

  // 初始化用户和角色数据
  useEffect(() => {
    fetchUsers()
    fetchRoles()
  }, [])

  // 使用从API获取的角色列表
  const rolesList = roles

  // 过滤用户
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
      user.phone.toLowerCase().includes(userSearchQuery.toLowerCase())

    const matchesStatus = userStatusFilter === "全部状态" || user.status === userStatusFilter
    const matchesRole = userRoleFilter === "全部角色" || user.roleName === userRoleFilter

    return matchesSearch && matchesStatus && matchesRole
  })

  // 处理用户选择
  const handleUserSelect = (userId: string) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter((id) => id !== userId))
    } else {
      setSelectedUsers([...selectedUsers, userId])
    }
  }

  // 打开用户编辑模态框
  const openUserModal = (user: UserType | null = null) => {
    setCurrentUser(user)
    setShowUserModal(true)
  }

  // 关闭用户编辑模态框
  const closeUserModal = () => {
    setCurrentUser(null)
    setShowUserModal(false)
  }

  // 打开删除用户确认模态框
  const openDeleteUserModal = (user: UserType) => {
    setUserToDelete(user)
    setShowDeleteUserModal(true)
  }

  // 关闭删除用户确认模态框
  const closeDeleteUserModal = () => {
    setUserToDelete(null)
    setShowDeleteUserModal(false)
  }

  // 打开重置密码确认模态框
  const openResetPasswordModal = (user: UserType) => {
    setUserToResetPassword(user)
    setShowResetPasswordModal(true)
  }

  // 关闭重置密码确认模态框
  const closeResetPasswordModal = () => {
    setUserToResetPassword(null)
    setShowResetPasswordModal(false)
  }

  // 保存用户
  const saveUser = async (userData: UserType) => {
    try {
      // 检查是否是admin或ceshi账号
      if (currentUser && (currentUser.name === 'admin' || currentUser.name === 'ceshi')) {
        // 检查是否尝试修改用户名
        if (userData.name !== currentUser.name) {
          toast({
            title: "无法修改",
            description: `${currentUser.name}账号的用户名是系统保留的，不能修改。`,
            variant: "destructive",
          })
          return
        }
      }

      // 获取认证token
      const token = localStorage.getItem('hefamily_token')
      if (!token) {
        toast({
          title: "认证失败",
          description: "请先登录以执行此操作",
          variant: "destructive"
        })
        return
      }

      if (currentUser) {
        // 更新现有用户
        // 准备API请求数据
        const apiData = {
          username: userData.name,
          email: userData.email,
          phone: userData.phone,
          role_id: userData.roleId ? parseInt(userData.roleId) : null,
          is_active: userData.status === '正常'
        }

        // 显示加载状态
        toast({
          title: "正在更新",
          description: `正在更新用户 ${userData.name} 的信息...`,
          variant: "default",
        })

        // 调用API更新用户
        await apiService.put(`/users/${userData.id}`, apiData, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        // 更新本地状态
        setUsers(users.map((user) => (user.id === userData.id ? userData : user)))

        toast({
          title: "用户已更新",
          description: `用户 ${userData.name} 的信息已成功更新。`,
          variant: "default",
        })
      } else {
        // 添加新用户
        // 准备API请求数据
        const apiData = {
          username: userData.name,
          password: userData.password || '123456', // 默认密码
          email: userData.email,
          phone: userData.phone,
          role_id: userData.roleId ? parseInt(userData.roleId) : null
        }

        // 显示加载状态
        toast({
          title: "正在添加",
          description: `正在添加用户 ${userData.name}...`,
          variant: "default",
        })

        // 调用API添加用户
        const response = await apiService.post('/register', apiData)

        // 获取新用户ID
        const newUserId = response.data?.id || Math.random().toString(36).substring(2, 11)

        // 构建新用户对象
        const newUser = {
          ...userData,
          id: newUserId.toString(),
          createdAt: new Date().toLocaleString("zh-CN"),
        }

        // 更新本地状态
        setUsers([...users, newUser])

        toast({
          title: "用户已添加",
          description: `用户 ${userData.name} 已成功添加到系统。`,
          variant: "default",
        })
      }
    } catch (error) {
      logger.error('保存用户失败:', sanitizeData(error))

      toast({
        title: "保存失败",
        description: error.response?.data?.message || "无法保存用户信息，请稍后重试。",
        variant: "destructive",
      })

      // 不关闭模态框，让用户可以修改后重试
      return
    }

    // 成功后关闭模态框
    closeUserModal()
  }

  // 删除用户
  const deleteUser = async () => {
    if (!userToDelete) return

    try {
      // 检查是否是admin账号
      if (userToDelete.name === 'admin') {
        toast({
          title: "无法删除",
          description: `admin账号是系统保留账号，不能删除。`,
          variant: "destructive",
        })
        closeDeleteUserModal()
        return
      }

      // 调用API删除用户
      const token = localStorage.getItem('hefamily_token')
      if (!token) {
        toast({
          title: "认证失败",
          description: "请先登录以执行此操作",
          variant: "destructive"
        })
        return
      }

      // 显示加载状态
      toast({
        title: "正在删除",
        description: `正在删除用户 ${userToDelete.name}...`,
        variant: "default",
      })

      // 调用删除API
      await apiService.del(`/users/${userToDelete.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      // 更新本地状态
      setUsers(users.filter((user) => user.id !== userToDelete.id))
      setSelectedUsers(selectedUsers.filter((id) => id !== userToDelete.id))

      toast({
        title: "用户已删除",
        description: `用户 ${userToDelete.name} 已成功从系统中删除。`,
        variant: "default",
      })
    } catch (error) {
      console.error('删除用户失败:', error)

      toast({
        title: "删除失败",
        description: error.response?.data?.message || "无法删除用户，请稍后重试。",
        variant: "destructive",
      })
    } finally {
      closeDeleteUserModal()
    }
  }

  // 批量删除用户
  const deleteSelectedUsers = async () => {
    if (selectedUsers.length === 0) return

    try {
      // 获取选中的用户
      const selectedUserObjects = users.filter(user => selectedUsers.includes(user.id))

      // 检查是否包含admin账号
      const adminUsers = selectedUserObjects.filter(user =>
        user.name === 'admin'
      )

      if (adminUsers.length > 0) {
        toast({
          title: "无法删除admin账号",
          description: `admin是系统保留账号，不能删除。请取消选择此账号后重试。`,
          variant: "destructive",
        })
        return
      }

      // 调用API删除用户
      const token = localStorage.getItem('hefamily_token')
      if (!token) {
        toast({
          title: "认证失败",
          description: "请先登录以执行此操作",
          variant: "destructive"
        })
        return
      }

      // 显示加载状态
      toast({
        title: "正在删除",
        description: `正在删除 ${selectedUsers.length} 个用户...`,
        variant: "default",
      })

      // 由于后端没有批量删除API，我们需要逐个删除
      const deletePromises = selectedUsers.map(userId =>
        apiService.del(`/users/${userId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
      )

      // 等待所有删除操作完成
      await Promise.all(deletePromises)

      // 更新本地状态
      setUsers(users.filter((user) => !selectedUsers.includes(user.id)))

      toast({
        title: "用户已删除",
        description: `已成功删除 ${selectedUsers.length} 个用户。`,
        variant: "default",
      })
      setSelectedUsers([])
    } catch (error) {
      console.error('批量删除用户失败:', error)

      toast({
        title: "删除失败",
        description: error.response?.data?.message || "无法删除用户，请稍后重试。",
        variant: "destructive",
      })
    }
  }

  // 批量启用用户
  const enableSelectedUsers = () => {
    if (selectedUsers.length === 0) return

    // 在实际应用中，这里应该调用API批量启用用户
    setUsers(
      users.map((user) =>
        selectedUsers.includes(user.id) ? { ...user, status: "正常" } : user
      )
    )
    toast({
      title: "用户已启用",
      description: `已成功启用 ${selectedUsers.length} 个用户。`,
      variant: "default",
    })
    setSelectedUsers([])
  }

  // 批量禁用用户
  const disableSelectedUsers = () => {
    if (selectedUsers.length === 0) return

    // 在实际应用中，这里应该调用API批量禁用用户
    setUsers(
      users.map((user) =>
        selectedUsers.includes(user.id) ? { ...user, status: "已禁用" } : user
      )
    )
    toast({
      title: "用户已禁用",
      description: `已成功禁用 ${selectedUsers.length} 个用户。`,
      variant: "default",
    })
    setSelectedUsers([])
  }

  // 重置用户密码
  const resetUserPassword = async () => {
    if (!userToResetPassword) return

    try {
      // 获取认证token
      const token = localStorage.getItem('hefamily_token')
      if (!token) {
        toast({
          title: "认证失败",
          description: "请先登录以执行此操作",
          variant: "destructive"
        })
        return
      }

      // 显示加载状态
      toast({
        title: "正在重置密码",
        description: `正在重置用户 ${userToResetPassword.name} 的密码...`,
        variant: "default",
      })

      // 调用API重置密码
      const response = await apiService.post(`/users/${userToResetPassword.id}/reset-password`, null, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      toast({
        title: "密码已重置",
        description: response.data?.message || `用户 ${userToResetPassword.name} 的密码已重置为默认密码：123456`,
        variant: "default",
      })
    } catch (error) {
      console.error('重置密码失败:', error)

      toast({
        title: "重置密码失败",
        description: error.response?.data?.message || "无法重置用户密码，请稍后重试。",
        variant: "destructive",
      })
    } finally {
      closeResetPasswordModal()
    }
  }

  // 渲染用户管理界面
  return (
    <div className="space-y-6">
      {/* 搜索和筛选 */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative md:w-1/3">
          <input
            type="text"
            placeholder="搜索用户..."
            value={userSearchQuery}
            onChange={(e) => setUserSearchQuery(e.target.value)}
            className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        </div>

        <select
          value={userStatusFilter}
          onChange={(e) => setUserStatusFilter(e.target.value)}
          className="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
        >
          <option value="全部状态">全部状态</option>
          <option value="正常">正常</option>
          <option value="待审核">待审核</option>
          <option value="已禁用">已禁用</option>
          <option value="待激活">待激活</option>
        </select>

        <select
          value={userRoleFilter}
          onChange={(e) => setUserRoleFilter(e.target.value)}
          className="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
        >
          <option value="全部角色">全部角色</option>
          {rolesList.map((role) => (
            <option key={role.id} value={role.name}>
              {role.name}
            </option>
          ))}
        </select>

        <div className="flex-grow"></div>

        <Button
          onClick={() => openUserModal()}
          className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
        >
          添加用户
        </Button>
      </div>

      {/* 批量操作 */}
      {selectedUsers.length > 0 && (
        <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-md">
          <span className="text-sm text-gray-500">已选择 {selectedUsers.length} 个用户</span>
          <Button
            variant="outline"
            size="sm"
            onClick={enableSelectedUsers}
            className="text-green-600 border-green-600 hover:bg-green-50"
          >
            <CheckCircle className="h-4 w-4 mr-1" />
            启用
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={disableSelectedUsers}
            className="text-orange-600 border-orange-600 hover:bg-orange-50"
          >
            <AlertCircle className="h-4 w-4 mr-1" />
            禁用
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={deleteSelectedUsers}
            className="text-red-600 border-red-600 hover:bg-red-50"
          >
            <Trash className="h-4 w-4 mr-1" />
            删除
          </Button>
        </div>
      )}

      {/* 用户表格 */}
      <UserTable
        users={filteredUsers}
        selectedUsers={selectedUsers}
        onSelectUser={handleUserSelect}
        onSelectAll={setSelectedUsers}
        onEdit={openUserModal}
        onDelete={openDeleteUserModal}
        onResetPassword={openResetPasswordModal}
      />

      {/* 用户编辑模态框 */}
      <UserModal
        isOpen={showUserModal}
        onClose={closeUserModal}
        onSave={saveUser}
        user={currentUser}
        roles={rolesList}
      />

      {/* 删除用户确认模态框 */}
      <DeleteUserModal
        isOpen={showDeleteUserModal}
        onClose={closeDeleteUserModal}
        onDelete={deleteUser}
        user={userToDelete}
      />

      {/* 重置密码确认模态框 */}
      <ResetPasswordModal
        isOpen={showResetPasswordModal}
        onClose={closeResetPasswordModal}
        onConfirm={resetUserPassword}
        user={userToResetPassword}
      />
    </div>
  )
}

export default UserManager
