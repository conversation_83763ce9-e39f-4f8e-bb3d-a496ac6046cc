/**
 * 用户管理组件
 * 
 * 用于系统管理页面中的用户管理功能，包括用户列表、搜索、筛选、编辑等功能
 */

import React, { useState, useEffect } from "react"
import { Search, Edit, Trash, X, CheckCircle, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { UserType, Role } from "./types"
import { UserTable } from "./UserTable"
import { UserModal } from "./UserModal"
import { DeleteUserModal } from "./DeleteUserModal"

// 组件属性定义
interface UserManagerProps {
  roles?: Role[] // 可选的角色列表，如果不提供则使用模拟数据
}

/**
 * 用户管理组件
 * @param props 组件属性
 */
const UserManager: React.FC<UserManagerProps> = ({ roles = [] }) => {
  // 用户管理状态
  const [userSearchQuery, setUserSearchQuery] = useState("")
  const [userStatusFilter, setUserStatusFilter] = useState("全部状态")
  const [userRoleFilter, setUserRoleFilter] = useState("全部角色")
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [showUserModal, setShowUserModal] = useState(false)
  const [currentUser, setCurrentUser] = useState<UserType | null>(null)
  const [showDeleteUserModal, setShowDeleteUserModal] = useState(false)
  const [userToDelete, setUserToDelete] = useState<UserType | null>(null)
  const [users, setUsers] = useState<UserType[]>([])
  const [loading, setLoading] = useState(false)

  // 模拟用户数据
  const mockUsers: UserType[] = [
    {
      id: "1",
      name: "张志明",
      phone: "13812345678",
      email: "<EMAIL>",
      roleId: "2",
      roleName: "管理员",
      status: "正常",
      createdAt: "2024-01-15 14:30",
    },
    {
      id: "2",
      name: "李秀英",
      phone: "13987654321",
      email: "<EMAIL>",
      roleId: "3",
      roleName: "运营人员",
      status: "正常",
      createdAt: "2024-01-14 09:15",
    },
    {
      id: "3",
      name: "王建国",
      phone: "13765432198",
      email: "<EMAIL>",
      roleId: "2",
      roleName: "管理员",
      status: "正常",
      createdAt: "2024-01-13 16:45",
    },
    {
      id: "4",
      name: "陈晓华",
      phone: "13598765432",
      email: "<EMAIL>",
      roleId: "3",
      roleName: "运营人员",
      status: "正常",
      createdAt: "2024-01-13 11:20",
    },
    {
      id: "5",
      name: "刘德华",
      phone: "13876543210",
      email: "<EMAIL>",
      roleId: "3",
      roleName: "运营人员",
      status: "待激活",
      createdAt: "2024-01-13 10:15",
    },
  ]

  // 模拟角色数据
  const mockRoles: Role[] = [
    {
      id: "1",
      name: "超级管理员",
      description: "系统最高权限，可以管理所有功能",
      userCount: 2,
      createdAt: "2024-01-15 14:30",
      isPreset: true,
    },
    {
      id: "2",
      name: "管理员",
      description: "可进行系统管理，用户管理等操作",
      userCount: 5,
      createdAt: "2024-01-14 09:15",
      isPreset: true,
    },
    {
      id: "3",
      name: "运营人员",
      description: "负责日常运营、数据维护等工作",
      userCount: 8,
      createdAt: "2024-01-13 16:45",
      isPreset: false,
    },
  ]

  // 初始化用户数据
  useEffect(() => {
    // 在实际应用中，这里应该从API获取用户数据
    setUsers(mockUsers)
  }, [])

  // 使用传入的角色列表或模拟数据
  const rolesList = roles.length > 0 ? roles : mockRoles

  // 过滤用户
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
      user.phone.toLowerCase().includes(userSearchQuery.toLowerCase())

    const matchesStatus = userStatusFilter === "全部状态" || user.status === userStatusFilter
    const matchesRole = userRoleFilter === "全部角色" || user.roleName === userRoleFilter

    return matchesSearch && matchesStatus && matchesRole
  })

  // 处理用户选择
  const handleUserSelect = (userId: string) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter((id) => id !== userId))
    } else {
      setSelectedUsers([...selectedUsers, userId])
    }
  }

  // 打开用户编辑模态框
  const openUserModal = (user: UserType | null = null) => {
    setCurrentUser(user)
    setShowUserModal(true)
  }

  // 关闭用户编辑模态框
  const closeUserModal = () => {
    setCurrentUser(null)
    setShowUserModal(false)
  }

  // 打开删除用户确认模态框
  const openDeleteUserModal = (user: UserType) => {
    setUserToDelete(user)
    setShowDeleteUserModal(true)
  }

  // 关闭删除用户确认模态框
  const closeDeleteUserModal = () => {
    setUserToDelete(null)
    setShowDeleteUserModal(false)
  }

  // 保存用户
  const saveUser = (userData: UserType) => {
    // 在实际应用中，这里应该调用API保存用户数据
    if (currentUser) {
      // 更新现有用户
      setUsers(users.map((user) => (user.id === userData.id ? userData : user)))
      toast({
        title: "用户已更新",
        description: `用户 ${userData.name} 的信息已成功更新。`,
        variant: "default",
      })
    } else {
      // 添加新用户
      const newUser = {
        ...userData,
        id: Math.random().toString(36).substring(2, 11), // 生成随机ID
        createdAt: new Date().toLocaleString("zh-CN"),
      }
      setUsers([...users, newUser])
      toast({
        title: "用户已添加",
        description: `用户 ${userData.name} 已成功添加到系统。`,
        variant: "default",
      })
    }
    closeUserModal()
  }

  // 删除用户
  const deleteUser = () => {
    if (!userToDelete) return

    // 在实际应用中，这里应该调用API删除用户
    setUsers(users.filter((user) => user.id !== userToDelete.id))
    setSelectedUsers(selectedUsers.filter((id) => id !== userToDelete.id))
    toast({
      title: "用户已删除",
      description: `用户 ${userToDelete.name} 已成功从系统中删除。`,
      variant: "default",
    })
    closeDeleteUserModal()
  }

  // 批量删除用户
  const deleteSelectedUsers = () => {
    if (selectedUsers.length === 0) return

    // 在实际应用中，这里应该调用API批量删除用户
    setUsers(users.filter((user) => !selectedUsers.includes(user.id)))
    toast({
      title: "用户已删除",
      description: `已成功删除 ${selectedUsers.length} 个用户。`,
      variant: "default",
    })
    setSelectedUsers([])
  }

  // 批量启用用户
  const enableSelectedUsers = () => {
    if (selectedUsers.length === 0) return

    // 在实际应用中，这里应该调用API批量启用用户
    setUsers(
      users.map((user) =>
        selectedUsers.includes(user.id) ? { ...user, status: "正常" } : user
      )
    )
    toast({
      title: "用户已启用",
      description: `已成功启用 ${selectedUsers.length} 个用户。`,
      variant: "default",
    })
    setSelectedUsers([])
  }

  // 批量禁用用户
  const disableSelectedUsers = () => {
    if (selectedUsers.length === 0) return

    // 在实际应用中，这里应该调用API批量禁用用户
    setUsers(
      users.map((user) =>
        selectedUsers.includes(user.id) ? { ...user, status: "已禁用" } : user
      )
    )
    toast({
      title: "用户已禁用",
      description: `已成功禁用 ${selectedUsers.length} 个用户。`,
      variant: "default",
    })
    setSelectedUsers([])
  }

  // 渲染用户管理界面
  return (
    <div className="space-y-6">
      {/* 搜索和筛选 */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative md:w-1/3">
          <input
            type="text"
            placeholder="搜索用户..."
            value={userSearchQuery}
            onChange={(e) => setUserSearchQuery(e.target.value)}
            className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        </div>

        <select
          value={userStatusFilter}
          onChange={(e) => setUserStatusFilter(e.target.value)}
          className="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
        >
          <option value="全部状态">全部状态</option>
          <option value="正常">正常</option>
          <option value="待审核">待审核</option>
          <option value="已禁用">已禁用</option>
          <option value="待激活">待激活</option>
        </select>

        <select
          value={userRoleFilter}
          onChange={(e) => setUserRoleFilter(e.target.value)}
          className="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
        >
          <option value="全部角色">全部角色</option>
          {rolesList.map((role) => (
            <option key={role.id} value={role.name}>
              {role.name}
            </option>
          ))}
        </select>

        <div className="flex-grow"></div>

        <Button
          onClick={() => openUserModal()}
          className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
        >
          添加用户
        </Button>
      </div>

      {/* 批量操作 */}
      {selectedUsers.length > 0 && (
        <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-md">
          <span className="text-sm text-gray-500">已选择 {selectedUsers.length} 个用户</span>
          <Button
            variant="outline"
            size="sm"
            onClick={enableSelectedUsers}
            className="text-green-600 border-green-600 hover:bg-green-50"
          >
            <CheckCircle className="h-4 w-4 mr-1" />
            启用
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={disableSelectedUsers}
            className="text-orange-600 border-orange-600 hover:bg-orange-50"
          >
            <AlertCircle className="h-4 w-4 mr-1" />
            禁用
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={deleteSelectedUsers}
            className="text-red-600 border-red-600 hover:bg-red-50"
          >
            <Trash className="h-4 w-4 mr-1" />
            删除
          </Button>
        </div>
      )}

      {/* 用户表格 */}
      <UserTable
        users={filteredUsers}
        selectedUsers={selectedUsers}
        onSelectUser={handleUserSelect}
        onSelectAll={setSelectedUsers}
        onEdit={openUserModal}
        onDelete={openDeleteUserModal}
      />

      {/* 用户编辑模态框 */}
      <UserModal
        isOpen={showUserModal}
        onClose={closeUserModal}
        onSave={saveUser}
        user={currentUser}
        roles={rolesList}
      />

      {/* 删除用户确认模态框 */}
      <DeleteUserModal
        isOpen={showDeleteUserModal}
        onClose={closeDeleteUserModal}
        onDelete={deleteUser}
        user={userToDelete}
      />
    </div>
  )
}

export default UserManager
