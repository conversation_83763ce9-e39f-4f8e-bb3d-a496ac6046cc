/**
 * 清理函数测试
 */

import { sanitizeInput } from '@/lib/utils'

describe('清理函数', () => {
  describe('sanitizeInput', () => {
    test('应该清理HTML标签', () => {
      const input = '<script>alert("XSS")</script>Hello<b>World</b>';
      const sanitized = sanitizeInput(input);
      expect(sanitized).toBe('HelloWorld');
    });

    test('应该处理空值', () => {
      expect(sanitizeInput('')).toBe('');
      expect(sanitizeInput(null as any)).toBe('');
      expect(sanitizeInput(undefined as any)).toBe('');
    });

    test('应该保留纯文本', () => {
      const input = '这是一段纯文本，没有HTML标签';
      const sanitized = sanitizeInput(input);
      expect(sanitized).toBe(input);
    });

    test('应该清理复杂的HTML结构', () => {
      const input = `
        <div class="container">
          <h1>标题</h1>
          <p>这是<strong>一段</strong>文本</p>
          <script>
            document.cookie = "session=stolen";
          </script>
          <img src="x" onerror="alert('XSS')">
        </div>
      `;
      const sanitized = sanitizeInput(input);
      expect(sanitized).not.toContain('<');
      expect(sanitized).not.toContain('>');
      expect(sanitized).toContain('标题');
      expect(sanitized).toContain('这是一段文本');
    });
  });
});
