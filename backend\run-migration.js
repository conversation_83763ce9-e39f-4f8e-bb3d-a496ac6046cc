/**
 * 运行数据库迁移
 */

const { Sequelize } = require('sequelize');
const { Umzug, SequelizeStorage } = require('umzug');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

// 创建Sequelize实例
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USERNAME,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: 'mysql',
    logging: console.log
  }
);

// 创建Umzug实例
const umzug = new Umzug({
  migrations: {
    path: path.join(__dirname, './migrations'),
    pattern: /\.js$/,
    params: [
      sequelize.getQueryInterface(),
      Sequelize
    ]
  },
  storage: new SequelizeStorage({ sequelize }),
  logger: console
});

// 运行迁移
(async () => {
  try {
    console.log('开始运行数据库迁移...');
    const migrations = await umzug.up();
    console.log('数据库迁移完成！');
    console.log('执行的迁移:', migrations.map(m => m.name));
    process.exit(0);
  } catch (error) {
    console.error('数据库迁移失败:', error);
    process.exit(1);
  }
})();
