// 将活动状态更新为已发布
require('dotenv').config();
const { sequelize, Activity } = require('./src/models');

async function publishActivities() {
  try {
    console.log('尝试连接到数据库...');
    await sequelize.authenticate();
    console.log('数据库连接成功！');

    // 获取最新的几个活动
    const recentActivities = await Activity.findAll({
      order: [['id', 'DESC']],
      limit: 5
    });

    console.log(`找到 ${recentActivities.length} 条最新活动记录`);

    // 更新这些活动的状态为已发布
    for (const activity of recentActivities) {
      console.log(`更新活动 ID: ${activity.id}, 标题: ${activity.title}, 当前状态: ${activity.status}`);
      
      await activity.update({
        status: 'published'
      });
      
      console.log(`活动 ID: ${activity.id} 已更新为已发布状态`);
    }

    console.log('\n活动状态更新完成！');

  } catch (error) {
    console.error('更新失败:', error);
  } finally {
    await sequelize.close();
    console.log('数据库连接已关闭');
  }
}

publishActivities();
