/**
 * 默认角色权限关联种子数据
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 获取角色ID
    const [roles] = await queryInterface.sequelize.query(
      `SELECT id, name FROM roles WHERE name IN ('管理员', '访问者')`
    );

    const adminRole = roles.find(role => role.name === '管理员');
    const basicRole = roles.find(role => role.name === '访问者');

    if (!adminRole || !basicRole) {
      console.error('未找到所需角色，请先运行角色种子数据');
      return;
    }

    // 获取所有权限ID
    const [permissions] = await queryInterface.sequelize.query(
      `SELECT id, code FROM permissions`
    );

    // 管理员拥有所有权限
    const adminPermissions = permissions.map(permission => ({
      role_id: adminRole.id,
      permission_id: permission.id,
      created_at: new Date(),
      updated_at: new Date()
    }));

    // 初级访问者拥有的权限代码
    const basicUserPermissionCodes = [
      'knowledge:access',
      'knowledge:create_user',
      'file:upload',
      'data:access',
      'data:ai_query',
      'activity:view',
      'personal:access',
      'personal:ai_use'
    ];

    // 初级访问者权限
    const basicPermissions = permissions
      .filter(permission => basicUserPermissionCodes.includes(permission.code))
      .map(permission => ({
        role_id: basicRole.id,
        permission_id: permission.id,
        created_at: new Date(),
        updated_at: new Date()
      }));

    // 合并所有角色权限关联
    const allRolePermissions = [
      ...adminPermissions,
      ...basicPermissions
    ];

    await queryInterface.bulkInsert('role_permissions', allRolePermissions, {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('role_permissions', null, {});
  }
};
