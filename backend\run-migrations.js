/**
 * 运行数据库迁移脚本
 * 
 * 此脚本用于手动运行数据库迁移，确保数据库结构和数据与最新代码保持一致
 */

const { Sequelize } = require('sequelize');
const { Umzug, SequelizeStorage } = require('umzug');
const path = require('path');
require('dotenv').config();

// 获取数据库配置
const dbConfig = {
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'hefamily_dev',
  host: process.env.DB_HOST || 'localhost',
  dialect: 'mysql',
  logging: console.log
};

console.log('数据库配置:', {
  username: dbConfig.username,
  database: dbConfig.database,
  host: dbConfig.host,
  dialect: dbConfig.dialect
});

// 创建Sequelize实例
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    dialect: dbConfig.dialect,
    logging: dbConfig.logging
  }
);

// 创建Umzug实例
const umzug = new Umzug({
  migrations: {
    glob: 'src/migrations/*.js',
    resolve: ({ name, path, context }) => {
      const migration = require(path);
      return {
        name,
        up: async () => migration.up(context.queryInterface, context.sequelize),
        down: async () => migration.down(context.queryInterface, context.sequelize)
      };
    }
  },
  context: sequelize.getQueryInterface(),
  storage: new SequelizeStorage({ sequelize }),
  logger: console
});

// 运行迁移
(async () => {
  try {
    console.log('开始运行数据库迁移...');
    
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 获取待执行的迁移
    const pending = await umzug.pending();
    console.log(`发现 ${pending.length} 个待执行的迁移:`);
    pending.forEach(m => console.log(`- ${m.name}`));
    
    // 执行迁移
    const migrations = await umzug.up();
    console.log(`成功执行 ${migrations.length} 个迁移:`);
    migrations.forEach(m => console.log(`- ${m.name}`));
    
    console.log('数据库迁移完成');
  } catch (error) {
    console.error('数据库迁移失败:', error);
  } finally {
    await sequelize.close();
    process.exit();
  }
})();
