const db = require('./src/models');

console.log('开始创建系统知识库和用户知识库文件分析助手...');

(async () => {
  try {
    // 查找管理员用户
    const adminUser = await db.User.findOne({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.error('未找到管理员用户，无法创建知识库文件分析助手');
      process.exit(1);
      return;
    }

    console.log('找到管理员用户，ID:', adminUser.id);

    // 1. 创建系统知识库文件分析助手
    console.log('\n创建系统知识库文件分析助手...');
    
    // 检查是否已存在
    let systemAssistant = await db.AIAssistant.findOne({
      where: { type: 'system-knowledge-file' }
    });
    
    if (systemAssistant) {
      console.log('系统知识库文件分析助手已存在，ID:', systemAssistant.id);
      console.log('更新配置...');
      
      await systemAssistant.update({
        name: '系统知识库文件分析助手',
        description: '用于分析上传到系统知识库的文件',
        api_key: 'dataset-DLFJlUe25VUHOwMO4HbO4hQk',
        api_endpoint: 'https://ai.glab.vip',
        app_id: '77199451-730a-4d79-a1c9-9b9e6bfcd747', // 系统知识库数据集ID
        app_code: '77199451-730a-4d79-a1c9-9b9e6bfcd747', // 保持一致
        upload_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
        analysis_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
        tags: '系统知识库,文件分析,Dify',
        initial_message: '我是系统知识库文件分析助手，可以帮助您分析上传的文件。',
        is_system: true,
        status: 'active',
        last_updated_by: adminUser.id
      });
    } else {
      systemAssistant = await db.AIAssistant.create({
        name: '系统知识库文件分析助手',
        type: 'system-knowledge-file',
        description: '用于分析上传到系统知识库的文件',
        api_key: 'dataset-DLFJlUe25VUHOwMO4HbO4hQk',
        api_endpoint: 'https://ai.glab.vip',
        app_id: '77199451-730a-4d79-a1c9-9b9e6bfcd747', // 系统知识库数据集ID
        app_code: '77199451-730a-4d79-a1c9-9b9e6bfcd747', // 保持一致
        upload_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
        analysis_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
        tags: '系统知识库,文件分析,Dify',
        initial_message: '我是系统知识库文件分析助手，可以帮助您分析上传的文件。',
        is_system: true,
        status: 'active',
        creator_id: adminUser.id,
        last_updated_by: adminUser.id
      });
    }
    
    console.log('系统知识库文件分析助手配置完成，ID:', systemAssistant.id);
    
    // 2. 创建用户知识库文件分析助手
    console.log('\n创建用户知识库文件分析助手...');
    
    // 检查是否已存在
    let userAssistant = await db.AIAssistant.findOne({
      where: { type: 'user-knowledge-file' }
    });
    
    if (userAssistant) {
      console.log('用户知识库文件分析助手已存在，ID:', userAssistant.id);
      console.log('更新配置...');
      
      await userAssistant.update({
        name: '用户知识库文件分析助手',
        description: '用于分析上传到用户知识库的文件',
        api_key: 'dataset-DLFJlUe25VUHOwMO4HbO4hQk',
        api_endpoint: 'https://ai.glab.vip',
        app_id: '602d59cf-3384-4105-bf91-e1481b30b6b2', // 用户知识库数据集ID
        app_code: '602d59cf-3384-4105-bf91-e1481b30b6b2', // 保持一致
        upload_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
        analysis_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
        tags: '用户知识库,文件分析,Dify',
        initial_message: '我是用户知识库文件分析助手，可以帮助您分析上传的文件。',
        is_system: true,
        status: 'active',
        last_updated_by: adminUser.id
      });
    } else {
      userAssistant = await db.AIAssistant.create({
        name: '用户知识库文件分析助手',
        type: 'user-knowledge-file',
        description: '用于分析上传到用户知识库的文件',
        api_key: 'dataset-DLFJlUe25VUHOwMO4HbO4hQk',
        api_endpoint: 'https://ai.glab.vip',
        app_id: '602d59cf-3384-4105-bf91-e1481b30b6b2', // 用户知识库数据集ID
        app_code: '602d59cf-3384-4105-bf91-e1481b30b6b2', // 保持一致
        upload_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
        analysis_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
        tags: '用户知识库,文件分析,Dify',
        initial_message: '我是用户知识库文件分析助手，可以帮助您分析上传的文件。',
        is_system: true,
        status: 'active',
        creator_id: adminUser.id,
        last_updated_by: adminUser.id
      });
    }
    
    console.log('用户知识库文件分析助手配置完成，ID:', userAssistant.id);
    
    // 3. 检查原有的知识库文件分析助手，如果存在则停用
    const oldAssistant = await db.AIAssistant.findOne({
      where: { type: 'knowledge-file' }
    });
    
    if (oldAssistant) {
      console.log('\n找到原有的知识库文件分析助手，ID:', oldAssistant.id);
      console.log('停用原有助手...');
      
      await oldAssistant.update({
        status: 'inactive',
        description: '已停用，请使用系统知识库文件分析助手或用户知识库文件分析助手',
        last_updated_by: adminUser.id
      });
      
      console.log('原有知识库文件分析助手已停用');
    }
    
    console.log('\n所有知识库文件分析助手配置完成！');
  } catch (e) {
    console.error('创建知识库文件分析助手失败:', e);
  } finally {
    process.exit();
  }
})();
