/**
 * 活动表单组件
 * 
 * 用于创建和编辑活动
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from '@/components/ui/use-toast'
import { Activity } from './ActivityList'

// 活动表单组件属性
interface ActivityFormProps {
  activity?: Activity
  onSubmit: (activityData: Omit<Activity, 'id' | 'created_by' | 'created_by_name' | 'created_at' | 'updated_at' | 'participants_count'>) => void
}

// 活动表单组件
export const ActivityForm: React.FC<ActivityFormProps> = ({ activity, onSubmit }) => {
  const [title, setTitle] = useState(activity?.title || '')
  const [content, setContent] = useState(activity?.content || '')
  const [location, setLocation] = useState(activity?.location || '')
  const [startTime, setStartTime] = useState(activity?.start_time ? new Date(activity.start_time).toISOString().slice(0, 16) : '')
  const [endTime, setEndTime] = useState(activity?.end_time ? new Date(activity.end_time).toISOString().slice(0, 16) : '')
  const [status, setStatus] = useState(activity?.status || 'upcoming')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 处理提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // 表单验证
    if (!title.trim()) {
      toast({
        title: '请输入活动标题',
        variant: 'destructive'
      })
      return
    }
    
    if (!content.trim()) {
      toast({
        title: '请输入活动内容',
        variant: 'destructive'
      })
      return
    }
    
    if (!location.trim()) {
      toast({
        title: '请输入活动地点',
        variant: 'destructive'
      })
      return
    }
    
    if (!startTime) {
      toast({
        title: '请选择开始时间',
        variant: 'destructive'
      })
      return
    }
    
    if (!endTime) {
      toast({
        title: '请选择结束时间',
        variant: 'destructive'
      })
      return
    }
    
    if (new Date(startTime) >= new Date(endTime)) {
      toast({
        title: '结束时间必须晚于开始时间',
        variant: 'destructive'
      })
      return
    }
    
    setIsSubmitting(true)
    
    try {
      // 提交表单
      await onSubmit({
        title,
        content,
        location,
        start_time: new Date(startTime).toISOString(),
        end_time: new Date(endTime).toISOString(),
        status
      })
    } catch (error) {
      console.error('提交活动失败', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{activity ? '编辑活动' : '创建活动'}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">活动标题</label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="请输入活动标题"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">活动内容</label>
            <Textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="请输入活动内容"
              rows={5}
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">活动地点</label>
            <Input
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              placeholder="请输入活动地点"
              required
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">开始时间</label>
              <Input
                type="datetime-local"
                value={startTime}
                onChange={(e) => setStartTime(e.target.value)}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">结束时间</label>
              <Input
                type="datetime-local"
                value={endTime}
                onChange={(e) => setEndTime(e.target.value)}
                required
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">活动状态</label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger>
                <SelectValue placeholder="选择活动状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="upcoming">即将开始</SelectItem>
                <SelectItem value="ongoing">进行中</SelectItem>
                <SelectItem value="completed">已结束</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline">取消</Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? '提交中...' : activity ? '保存修改' : '创建活动'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}

export default ActivityForm
