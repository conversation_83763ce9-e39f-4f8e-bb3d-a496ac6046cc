"use client"

import { useState, useEffect } from 'react'
import { Upload, FileText, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import axios from 'axios'
import aiAssistantService from '@/services/ai-assistant-service'

interface FileAnalysisAssistantProps {
  onAnalysisComplete?: (summary: string, detailedDescription: string) => void
  apiKey?: string
  apiEndpoint?: string
  uploadApiPath?: string
  analysisApiPath?: string
}

/**
 * 知识库文件分析助手组件
 *
 * 用于上传和分析文件，生成摘要和详细描述
 * 通过系统管理的AI管理界面中配置的知识库文件分析助手调用Dify API
 */
export function FileAnalysisAssistant({
  onAnalysisComplete,
  apiKey,
  apiEndpoint,
  uploadApiPath,
  analysisApiPath
}: FileAnalysisAssistantProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [summary, setSummary] = useState('')
  const [detailedDescription, setDetailedDescription] = useState('')
  const [error, setError] = useState('')
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const [analysisSuccess, setAnalysisSuccess] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [aiConfig, setAiConfig] = useState<{
    apiKey: string;
    apiEndpoint: string;
    uploadApiPath: string;
    analysisApiPath: string;
  } | null>(null)

  // 加载AI助手配置
  useEffect(() => {
    const loadAiConfig = async () => {
      try {
        setIsLoading(true)
        // 获取知识库文件分析助手配置
        const assistants = await aiAssistantService.getAIAssistants('knowledge-file')

        if (assistants && assistants.length > 0) {
          const assistant = assistants[0]
          setAiConfig({
            apiKey: assistant.apiKey,
            apiEndpoint: assistant.apiEndpoint,
            uploadApiPath: assistant.uploadApiPath || '/files/upload',
            analysisApiPath: assistant.analysisApiPath || '/files/analyze'
          })
        } else {
          setError('未找到知识库文件分析助手配置，请在系统管理-AI管理中配置')
        }
      } catch (err) {
        console.error('加载AI助手配置失败:', err)
        setError('加载AI助手配置失败，请稍后再试')
      } finally {
        setIsLoading(false)
      }
    }

    // 如果没有传入配置，则从系统获取
    if (!apiKey || !apiEndpoint) {
      loadAiConfig()
    } else {
      // 使用传入的配置
      setAiConfig({
        apiKey,
        apiEndpoint,
        uploadApiPath: uploadApiPath || '/files/upload',
        analysisApiPath: analysisApiPath || '/files/analyze'
      })
    }
  }, [apiKey, apiEndpoint, uploadApiPath, analysisApiPath])

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0])
      setError('')
      setUploadSuccess(false)
      setAnalysisSuccess(false)
      setSummary('')
      setDetailedDescription('')
    }
  }

  // 上传文件
  const handleUpload = async () => {
    if (!file) {
      setError('请先选择文件')
      return
    }

    if (!aiConfig) {
      setError('AI助手配置未加载，请稍后再试')
      return
    }

    setIsUploading(true)
    setError('')

    try {
      // 创建表单数据
      const formData = new FormData()
      formData.append('file', file)

      // 上传文件
      const response = await axios.post(
        `${aiConfig.apiEndpoint}${aiConfig.uploadApiPath}`,
        formData,
        {
          headers: {
            'Authorization': `Bearer ${aiConfig.apiKey}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      )

      setUploadSuccess(true)

      // 检查响应格式
      const fileId = response.data.id || response.data.file_id

      if (!fileId) {
        throw new Error('上传响应中未找到文件ID')
      }

      // 上传文件到Dify知识库
      await uploadFileToDify(fileId)
    } catch (error: any) {
      console.error('文件上传失败:', error)
      setError(`文件上传失败: ${error.message || '请重试'}`)
    } finally {
      setIsUploading(false)
    }
  }

  // 上传文件到Dify知识库
  const uploadFileToDify = async (fileId: string) => {
    if (!aiConfig) {
      setError('AI助手配置未加载，请稍后再试')
      return
    }

    setIsAnalyzing(true)
    setError('')

    try {
      // 准备请求数据
      const requestData: any = {
        file_id: fileId
      }

      // 如果有文件名，添加到请求中
      if (file) {
        requestData.file_name = file.name
        requestData.file_type = file.type
      }

      // 调用分析API
      const response = await axios.post(
        `${aiConfig.apiEndpoint}${aiConfig.analysisApiPath}`,
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${aiConfig.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      )

      // 设置分析结果
      const summary = response.data.summary || ''
      const detailed_description = response.data.detailed_description || ''

      setSummary(summary)
      setDetailedDescription(detailed_description)
      setAnalysisSuccess(true)

      // 回调函数
      if (onAnalysisComplete) {
        onAnalysisComplete(summary, detailed_description)
      }
    } catch (error: any) {
      console.error('文件分析失败:', error)
      setError(`文件分析失败: ${error.message || '请重试'}`)
    } finally {
      setIsAnalyzing(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader className="px-4 py-3 border-b">
        <CardTitle className="text-lg">知识库文件分析助手</CardTitle>
      </CardHeader>
      <CardContent className="p-4 space-y-4">
        {/* 加载状态 */}
        {isLoading && (
          <div className="bg-muted p-3 rounded-md flex items-center">
            <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
            <p>正在加载AI助手配置...</p>
          </div>
        )}

        {/* 错误提示 */}
        {error && (
          <div className="bg-destructive/10 text-destructive p-3 rounded-md flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 shrink-0 mt-0.5" />
            <p>{error}</p>
          </div>
        )}

        {/* 文件上传区域 */}
        {!isLoading && (
          <>
            <div className="space-y-2">
              <Label htmlFor="file-upload">选择文件</Label>
              <Input
                id="file-upload"
                type="file"
                onChange={handleFileChange}
                accept=".pdf,.doc,.docx,.txt,.md"
                disabled={isUploading || isAnalyzing || !aiConfig}
              />
              {file && (
                <div className="text-sm text-muted-foreground flex items-center">
                  <FileText className="h-4 w-4 mr-1" />
                  {file.name} ({(file.size / 1024).toFixed(2)} KB)
                </div>
              )}
            </div>

            <Button
              onClick={handleUpload}
              disabled={!file || isUploading || isAnalyzing || !aiConfig}
              className="w-full"
            >
              {isUploading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  上传中...
                </>
              ) : uploadSuccess ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  上传成功
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  上传并分析
                </>
              )}
            </Button>

            {/* 分析状态 */}
            {isAnalyzing && (
              <div className="bg-muted p-3 rounded-md flex items-center">
                <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                <p>正在分析文件，这可能需要几分钟时间...</p>
              </div>
            )}

            {/* 分析结果 */}
            {analysisSuccess && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="summary">文件摘要</Label>
                  <Textarea
                    id="summary"
                    value={summary}
                    onChange={(e) => setSummary(e.target.value)}
                    className="min-h-[100px]"
                    placeholder="文件摘要将在这里显示"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="detailed-description">详细描述</Label>
                  <Textarea
                    id="detailed-description"
                    value={detailedDescription}
                    onChange={(e) => setDetailedDescription(e.target.value)}
                    className="min-h-[200px]"
                    placeholder="文件详细描述将在这里显示"
                  />
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
