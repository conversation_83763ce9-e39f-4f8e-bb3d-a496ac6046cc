"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Navbar } from "@/components/navbar"
import { Search, User, Check, X, Plus, Clock, ShieldCheck, ShieldX } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"
import knowledgeService from "@/services/knowledge-service"

interface Member {
  id: number
  name: string
  email: string
  status: "正常" | "待审核"
  role: "管理员" | "访客"
  addedAt: string
}

// 新增访问申请接口
interface AccessRequest {
  id: number
  userId: number
  userName: string
  userEmail: string
  requestDate: string
  reason: string
  status: "待审核" | "已通过" | "已拒绝"
}

interface KnowledgeSettingsProps {
  params: {
    id: string
  }
}

export default function KnowledgeSettingsPage({ params }: KnowledgeSettingsProps) {
  const router = useRouter()
  // 使用React.use()解包params
  const unwrappedParams = React.use(params)
  const id = unwrappedParams.id
  const [knowledgeBase, setKnowledgeBase] = useState<any>(null)
  const [knowledgeBaseName, setKnowledgeBaseName] = useState("")
  const [knowledgeBaseDescription, setKnowledgeBaseDescription] = useState("")
  const [searchMember, setSearchMember] = useState("")
  const [showAddMemberModal, setShowAddMemberModal] = useState(false)
  const [selectedMember, setSelectedMember] = useState<number | null>(null)
  // 所有用户都设置为访客权限，不再需要选择
  const selectedRole = "访客"
  const [showSuccessToast, setShowSuccessToast] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")
  const [activeTab, setActiveTab] = useState("general")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 成员数据
  const [members, setMembers] = useState<Member[]>([])

  // 可添加的用户列表
  const [availableUsers, setAvailableUsers] = useState<{id: number, name: string, email: string}[]>([])

  // 访问申请数据
  const [accessRequests, setAccessRequests] = useState<AccessRequest[]>([])

  // 加载知识库数据
  useEffect(() => {
    const loadKnowledgeBase = async () => {
      setIsLoading(true)
      setError(null)

      try {
        console.log('开始加载知识库数据, ID:', id)

        // 获取知识库详情
        const kb = await knowledgeService.getKnowledgeBaseById(id)
        console.log('获取到知识库详情:', kb)

        setKnowledgeBase(kb)
        setKnowledgeBaseName(kb.name)
        setKnowledgeBaseDescription(kb.description || "")

        // 如果是系统知识库且当前页签不是基本设置，则重置为基本设置页签
        if (kb.type === 'system' && activeTab !== 'general') {
          setActiveTab('general')
        }

        try {
          // 获取知识库成员
          const accessList = await knowledgeService.getKnowledgeBaseAccess(id)
          console.log('获取到知识库成员:', accessList)

          // 转换成员数据格式
          const membersList = accessList.map((access: any) => ({
            id: access.user_id,
            name: access.user?.username || '未知用户',
            email: access.user?.email || '',
            status: '正常',
            role: access.access_type === 'admin' ? '管理员' : '访客',
            addedAt: new Date(access.created_at).toLocaleString()
          }))

          setMembers(membersList)

          // 获取可添加的用户列表
          try {
            console.log('开始获取可添加的用户列表')
            console.log('当前成员列表:', membersList)

            // 直接使用模拟数据
            const mockUsers = [
              { id: 1, username: 'admin', email: '<EMAIL>', role: 'admin' },
              { id: 2, username: 'user1', email: '<EMAIL>', role: 'user' },
              { id: 3, username: 'user2', email: '<EMAIL>', role: 'user' },
              { id: 4, username: 'manager1', email: '<EMAIL>', role: 'manager' },
              { id: 5, username: 'editor1', email: '<EMAIL>', role: 'editor' },
              { id: 6, username: 'guest1', email: '<EMAIL>', role: 'guest' },
              { id: 7, username: 'test1', email: '<EMAIL>', role: 'user' },
              { id: 8, username: 'test2', email: '<EMAIL>', role: 'user' },
              { id: 9, username: 'test3', email: '<EMAIL>', role: 'user' },
              { id: 10, username: 'test4', email: '<EMAIL>', role: 'user' }
            ]

            // 过滤掉已经是成员的用户
            const memberIds = membersList.map(m => m.id)
            console.log('当前成员ID列表:', memberIds)

            const availableUsersList = mockUsers
              .filter(user => !memberIds.includes(user.id))
              .map(user => ({
                id: user.id,
                name: user.username,
                email: user.email || ''
              }))

            console.log('可添加的用户列表 (模拟数据):', availableUsersList)
            setAvailableUsers(availableUsersList)

            // 同时尝试从API获取真实数据
            try {
              // 添加认证头
              const token = localStorage.getItem('hefamily_token')
              const headers: HeadersInit = {
                'Content-Type': 'application/json'
              }

              if (token) {
                headers['Authorization'] = `Bearer ${token}`
                console.log('已添加认证头到用户列表请求')
              } else {
                console.warn('未找到认证令牌，可能导致权限问题')
              }

              // 发送请求
              console.log('尝试从API获取用户数据')
              const dbResponse = await fetch('/api/db/users', { headers })
              console.log('用户列表API响应状态:', dbResponse.status, dbResponse.statusText)

              if (dbResponse.ok) {
                const userData = await dbResponse.json()
                console.log('从API获取到用户数据:', userData)

                if (userData && userData.success && userData.data && userData.data.length > 0) {
                  // 过滤掉已经是成员的用户
                  const apiUsersList = userData.data
                    .filter((user: any) => !memberIds.includes(user.id))
                    .map((user: any) => ({
                      id: user.id,
                      name: user.username,
                      email: user.email || ''
                    }))

                  console.log('从API获取的可添加用户列表:', apiUsersList)

                  // 如果API返回的用户列表不为空，则使用API返回的数据
                  if (apiUsersList.length > 0) {
                    setAvailableUsers(apiUsersList)
                  }
                }
              }
            } catch (apiError) {
              console.error('从API获取用户列表失败:', apiError)
              // 已经设置了模拟数据，所以这里不需要再设置
            }
          } catch (error) {
            console.error('获取用户列表失败:', error)

            // 使用备用模拟数据
            const fallbackUsers = [
              { id: 100, name: '备用用户1', email: '<EMAIL>' },
              { id: 101, name: '备用用户2', email: '<EMAIL>' },
              { id: 102, name: '备用用户3', email: '<EMAIL>' }
            ]
            console.log('使用备用模拟数据:', fallbackUsers)
            setAvailableUsers(fallbackUsers)
          }
        } catch (accessError: any) {
          console.error('获取知识库成员失败:', accessError)
          // 不阻止整个页面加载
        }

        try {
          // 获取访问申请列表
          console.log('开始获取知识库访问申请列表, ID:', id);
          const requestsData = await knowledgeService.getKnowledgeBaseAccessRequests(id)
          console.log('访问申请API响应:', requestsData);

          if (requestsData && requestsData.accessRequests) {
            console.log('获取到访问申请列表:', requestsData)

            // 转换访问申请数据格式
            const requestsList = requestsData.accessRequests?.map((request: any) => ({
              id: request.id,
              userId: request.user_id,
              userName: request.user?.username || '未知用户',
              userEmail: request.user?.email || '',
              requestDate: new Date(request.created_at).toLocaleString(),
              reason: request.reason || '',
              status: request.status === 'pending' ? '待审核' :
                     request.status === 'approved' ? '已通过' : '已拒绝'
            })) || []

            setAccessRequests(requestsList)
          } else {
            console.warn('获取访问申请列表失败:', requestsData.message)
            // 不阻止整个页面加载，使用空数组
            setAccessRequests([])
          }
        } catch (requestsError: any) {
          console.error('获取访问申请列表失败:', requestsError)
          // 不阻止整个页面加载，使用空数组
          setAccessRequests([])
        }
      } catch (error: any) {
        console.error('加载知识库数据失败:', error)
        setError(error.message || '加载知识库数据失败')
      } finally {
        setIsLoading(false)
      }
    }

    loadKnowledgeBase()
  }, [id])

  // 监听知识库类型变化，如果是系统知识库且当前页签不是基本设置，则重置为基本设置页签
  useEffect(() => {
    if (knowledgeBase?.type === 'system' && activeTab !== 'general') {
      setActiveTab('general')
    }
  }, [knowledgeBase?.type, activeTab])

  // 过滤可添加的用户
  const filteredUsers = searchMember
    ? availableUsers.filter(
        (user) =>
          user.name.toLowerCase().includes(searchMember.toLowerCase()) ||
          user.email.toLowerCase().includes(searchMember.toLowerCase()),
      )
    : availableUsers

  const handleAddMember = () => {
    setShowAddMemberModal(true)
  }

  const handleSelectMember = (userId: number) => {
    setSelectedMember(userId)
  }

  const handleConfirmAddMember = async () => {
    if (selectedMember) {
      const userToAdd = availableUsers.find((user) => user.id === selectedMember)
      if (userToAdd) {
        try {
          // 添加知识库成员，始终设置为访客权限（read）
          const response = await knowledgeService.addKnowledgeBaseAccess(id, {
            user_id: userToAdd.id.toString(),
            access_type: 'read'
          })

          if (response) {
            const newMember: Member = {
              id: userToAdd.id,
              name: userToAdd.name,
              email: userToAdd.email,
              status: "正常",
              role: "访客",
              addedAt: new Date().toLocaleString("zh-CN", {
                year: "numeric",
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
              }).replace(/\//g, "-"),
            }

            setMembers([...members, newMember])
            setShowAddMemberModal(false)
            setSelectedMember(null)

            setSearchMember("")

            setSuccessMessage(`已成功添加成员 ${userToAdd.name}`)
            setShowSuccessToast(true)
            setTimeout(() => {
              setShowSuccessToast(false)
            }, 2000)
          } else {
            toast({
              variant: "destructive",
              title: "添加失败",
              description: "添加知识库成员失败，请稍后再试",
            })
          }
        } catch (error: any) {
          console.error('添加知识库成员失败:', error)
          toast({
            variant: "destructive",
            title: "添加失败",
            description: error.message || "添加知识库成员失败，请稍后再试",
          })
        }
      }
    }
  }

  const handleRemoveMember = async (memberId: number) => {
    try {
      // 移除知识库成员
      const response = await knowledgeService.deleteKnowledgeBaseAccess(id, memberId.toString())

      if (response) {
        // 更新成员列表
        setMembers(members.filter((member) => member.id !== memberId))

        const removedMember = members.find(m => m.id === memberId)
        setSuccessMessage(`已成功移除成员 ${removedMember?.name || ''}`)
        setShowSuccessToast(true)
        setTimeout(() => {
          setShowSuccessToast(false)
        }, 2000)
      } else {
        toast({
          variant: "destructive",
          title: "移除失败",
          description: "移除知识库成员失败，请稍后再试",
        })
      }
    } catch (error: any) {
      console.error('移除知识库成员失败:', error)
      toast({
        variant: "destructive",
        title: "移除失败",
        description: error.message || "移除知识库成员失败，请稍后再试",
      })
    }
  }

  const handleSaveSettings = async () => {
    try {
      // 保存知识库设置
      const response = await knowledgeService.updateKnowledgeBase(id, {
        name: knowledgeBaseName,
        description: knowledgeBaseDescription
      })

      if (response) {
        setSuccessMessage(`知识库 "${knowledgeBaseName}" 设置已保存成功！`)
        setShowSuccessToast(true)
        setTimeout(() => {
          setShowSuccessToast(false)
          router.push("/knowledge")
        }, 2000)
      } else {
        toast({
          variant: "destructive",
          title: "保存失败",
          description: "保存知识库设置失败，请稍后再试",
        })
      }
    } catch (error: any) {
      console.error('保存知识库设置失败:', error)
      toast({
        variant: "destructive",
        title: "保存失败",
        description: error.message || "保存知识库设置失败，请稍后再试",
      })
    }
  }

  // 处理访问申请审批
  const handleApproveRequest = async (requestId: number) => {
    try {
      console.log('开始批准访问申请, ID:', requestId);

      // 调用API批准访问申请
      const response = await knowledgeService.reviewAccessRequest(
        requestId.toString(),
        'approved',
        'read'
      )

      console.log('批准访问申请响应:', response);

      if (response && (response.success || response.data)) {
        console.log('批准访问申请成功');

        // 更新访问申请状态
        setAccessRequests(
          accessRequests.map((request) => (request.id === requestId ? { ...request, status: "已通过" } : request)),
        )

        // 将申请用户添加为成员
        const approvedRequest = accessRequests.find((request) => request.id === requestId)
        if (approvedRequest) {
          console.log('找到批准的申请:', approvedRequest);

          const newMember: Member = {
            id: approvedRequest.userId,
            name: approvedRequest.userName,
            email: approvedRequest.userEmail,
            status: "正常",
            role: "访客",
            addedAt: new Date()
              .toLocaleString("zh-CN", {
                year: "numeric",
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
              })
              .replace(/\//g, "-"),
          }

          console.log('添加新成员:', newMember);
          setMembers([...members, newMember])
        }

        setSuccessMessage("已批准访问申请")
        setShowSuccessToast(true)
        setTimeout(() => {
          setShowSuccessToast(false)
        }, 2000)

        // 重新加载知识库成员列表
        try {
          const accessList = await knowledgeService.getKnowledgeBaseAccess(id)
          console.log('重新获取到知识库成员:', accessList)

          // 转换成员数据格式
          const membersList = accessList.map((access: any) => ({
            id: access.user_id,
            name: access.user?.username || '未知用户',
            email: access.user?.email || '',
            status: '正常',
            role: access.access_type === 'admin' ? '管理员' : '访客',
            addedAt: new Date(access.created_at).toLocaleString()
          }))

          setMembers(membersList)
        } catch (accessError: any) {
          console.error('重新获取知识库成员失败:', accessError)
        }
      } else {
        console.error('批准访问申请失败, 响应:', response);

        toast({
          variant: "destructive",
          title: "操作失败",
          description: (response && response.message) || "批准访问申请失败，请稍后再试",
        })
      }
    } catch (error: any) {
      console.error('批准访问申请失败:', error)

      let errorMessage = "批准访问申请失败，请稍后再试";
      if (error.message) {
        errorMessage = error.message;
      } else if (error.error) {
        errorMessage = error.error;
      }

      toast({
        variant: "destructive",
        title: "操作失败",
        description: errorMessage,
      })
    }
  }

  const handleRejectRequest = async (requestId: number) => {
    try {
      console.log('开始拒绝访问申请, ID:', requestId);

      // 调用API拒绝访问申请
      const response = await knowledgeService.reviewAccessRequest(
        requestId.toString(),
        'rejected'
      )

      console.log('拒绝访问申请响应:', response);

      if (response && (response.success || response.data)) {
        console.log('拒绝访问申请成功');

        // 更新访问申请状态
        setAccessRequests(
          accessRequests.map((request) => (request.id === requestId ? { ...request, status: "已拒绝" } : request)),
        )

        setSuccessMessage("已拒绝访问申请")
        setShowSuccessToast(true)
        setTimeout(() => {
          setShowSuccessToast(false)
        }, 2000)
      } else {
        console.error('拒绝访问申请失败, 响应:', response);

        toast({
          variant: "destructive",
          title: "操作失败",
          description: (response && response.message) || "拒绝访问申请失败，请稍后再试",
        })
      }
    } catch (error: any) {
      console.error('拒绝访问申请失败:', error)

      let errorMessage = "拒绝访问申请失败，请稍后再试";
      if (error.message) {
        errorMessage = error.message;
      } else if (error.error) {
        errorMessage = error.error;
      }

      toast({
        variant: "destructive",
        title: "操作失败",
        description: errorMessage,
      })
    }
  }

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="container mx-auto px-4 py-8 pt-24">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1e7a43]"></div>
            <span className="ml-2">加载中...</span>
          </div>
        </div>
      </div>
    )
  }

  // 显示错误信息
  if (error || !knowledgeBase) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="container mx-auto px-4 py-8 pt-24">
          <div className="bg-red-50 border border-red-200 rounded-lg p-8 text-center">
            <div className="text-red-500 text-4xl mb-4">!</div>
            <h2 className="text-xl font-bold mb-2">加载失败</h2>
            <p className="text-gray-600 mb-6">{error || "无法加载知识库数据，请稍后再试"}</p>
            <Button onClick={() => router.push("/knowledge")}>
              返回知识库列表
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            className="mr-4"
            onClick={() => router.push("/knowledge")}
          >
            ← 返回
          </Button>
          <h1 className="text-2xl font-bold">知识库设置</h1>
        </div>

        <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="general">基本设置</TabsTrigger>
            {/* 只在用户知识库中显示成员管理和访问申请页签 */}
            {knowledgeBase?.type === 'user' && (
              <>
                <TabsTrigger value="members">成员管理</TabsTrigger>
                <TabsTrigger value="access-requests">
                  访问申请
                  {accessRequests.filter((req) => req.status === "待审核").length > 0 && (
                    <span className="ml-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 inline-flex items-center justify-center">
                      {accessRequests.filter((req) => req.status === "待审核").length}
                    </span>
                  )}
                </TabsTrigger>
              </>
            )}
          </TabsList>

          <TabsContent value="general">
            <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">知识库名称</label>
                  <input
                    type="text"
                    value={knowledgeBaseName}
                    onChange={(e) => setKnowledgeBaseName(e.target.value)}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">知识库 ID</label>
                  <input
                    type="text"
                    value={id}
                    disabled
                    className="w-full border border-gray-300 rounded-md py-2 px-3 bg-gray-50 text-gray-500"
                  />
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">知识库描述</label>
                <textarea
                  value={knowledgeBaseDescription}
                  onChange={(e) => setKnowledgeBaseDescription(e.target.value)}
                  placeholder="请输入知识库描述"
                  className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent min-h-[100px]"
                ></textarea>
                <p className="mt-1 text-xs text-gray-500">简要描述知识库的内容和用途</p>
              </div>



              <div className="mt-6 flex justify-end">
                <Button className="bg-[#f5a623] hover:bg-[#f5a623]/90" onClick={handleSaveSettings}>
                  保存设置
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="members">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-medium mb-4">访问权限</h2>

              {/* 管理员权限提示 */}
              <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                <h3 className="text-sm font-medium text-blue-800 mb-2">管理员权限说明</h3>
                <p className="text-sm text-blue-700">
                  系统管理员默认拥有对所有知识库的完全访问权限，但不会显示在成员列表中。如需将管理员显式添加为成员，请使用下方的添加成员功能。
                </p>
              </div>

              {/* 添加成员按钮 */}
              <div className="mb-4">
                <Button onClick={handleAddMember} className="bg-[#f5a623] hover:bg-[#f5a623]/90 flex items-center">
                  <Plus className="h-4 w-4 mr-2" />
                  添加成员
                </Button>
                <div className="mt-2 text-xs text-gray-500">
                  管理员和知识库创建者可以添加或移除成员
                </div>

              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">成员</th>
                      <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">状态</th>
                      <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">权限角色</th>
                      <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">添加时间</th>
                      <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {members.map((member) => (
                      <tr key={member.id} className="border-b border-gray-200">
                        <td className="py-4 px-4">
                          <div className="flex items-center">
                            <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                              <User className="h-4 w-4 text-gray-500" />
                            </div>
                            <div>
                              <div className="font-medium">{member.name}</div>
                              <div className="text-xs text-gray-500">{member.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${member.status === "正常" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}`}
                          >
                            {member.status}
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${member.role === "管理员" ? "bg-blue-100 text-blue-800" : "bg-gray-100 text-gray-800"}`}
                          >
                            {member.role}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-sm">{member.addedAt}</td>
                        <td className="py-4 px-4">
                          <button
                            className="text-red-600 hover:text-red-800 text-sm"
                            onClick={() => handleRemoveMember(member.id)}
                          >
                            移除
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="mt-2 md:hidden text-sm text-gray-500 text-center px-4 py-2">向左右滑动可查看更多内容</div>
            </div>
          </TabsContent>

          <TabsContent value="access-requests">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-medium mb-4">访问申请管理</h2>

              {accessRequests.length === 0 ? (
                <div className="text-center py-12">
                  <Clock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">暂无访问申请</p>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-md font-medium">
                      待审核申请
                      <span className="ml-2 text-sm text-gray-500">
                        ({accessRequests.filter((req) => req.status === "待审核").length}条)
                      </span>
                    </h3>
                  </div>

                  {accessRequests.filter((req) => req.status === "待审核").length === 0 ? (
                    <div className="bg-gray-50 rounded-md p-4 text-center">
                      <p className="text-gray-500">暂无待审核的申请</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {accessRequests
                        .filter((req) => req.status === "待审核")
                        .map((request) => (
                          <div key={request.id} className="border rounded-lg p-4 bg-gray-50">
                            <div className="flex justify-between items-start mb-3">
                              <div className="flex items-center">
                                <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                                  <User className="h-5 w-5 text-gray-500" />
                                </div>
                                <div>
                                  <div className="font-medium">{request.userName}</div>
                                  <div className="text-xs text-gray-500">{request.userEmail}</div>
                                </div>
                              </div>
                              <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800 flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
                                待审核
                              </span>
                            </div>

                            <div className="mb-3">
                              <p className="text-sm text-gray-500 mb-1">申请时间：{request.requestDate}</p>
                              <div className="bg-white p-3 rounded border border-gray-200">
                                <p className="text-sm">{request.reason}</p>
                              </div>
                            </div>

                            <div className="flex justify-end space-x-3">
                              <Button
                                variant="outline"
                                className="text-red-600 border-red-600 hover:bg-red-50"
                                onClick={() => handleRejectRequest(request.id)}
                              >
                                <ShieldX className="h-4 w-4 mr-1" />
                                拒绝
                              </Button>
                              <Button
                                className="bg-[#f5a623] hover:bg-[#f5a623]/90"
                                onClick={() => handleApproveRequest(request.id)}
                              >
                                <ShieldCheck className="h-4 w-4 mr-1" />
                                批准
                              </Button>
                            </div>
                          </div>
                        ))}
                    </div>
                  )}

                  <div className="mt-8">
                    <h3 className="text-md font-medium mb-4">已处理申请</h3>

                    {accessRequests.filter((req) => req.status !== "待审核").length === 0 ? (
                      <div className="bg-gray-50 rounded-md p-4 text-center">
                        <p className="text-gray-500">暂无已处理的申请</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="min-w-full">
                          <thead>
                            <tr className="border-b border-gray-200">
                              <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">申请人</th>
                              <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">申请时间</th>
                              <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">申请理由</th>
                              <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">状态</th>
                            </tr>
                          </thead>
                          <tbody>
                            {accessRequests
                              .filter((req) => req.status !== "待审核")
                              .map((request) => (
                                <tr key={request.id} className="border-b border-gray-200">
                                  <td className="py-4 px-4">
                                    <div className="flex items-center">
                                      <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                                        <User className="h-4 w-4 text-gray-500" />
                                      </div>
                                      <div>
                                        <div className="font-medium">{request.userName}</div>
                                        <div className="text-xs text-gray-500">{request.userEmail}</div>
                                      </div>
                                    </div>
                                  </td>
                                  <td className="py-4 px-4 text-sm">{request.requestDate}</td>
                                  <td className="py-4 px-4 text-sm">
                                    <div className="max-w-xs truncate">{request.reason}</div>
                                  </td>
                                  <td className="py-4 px-4">
                                    <span
                                      className={`px-2 py-1 rounded-full text-xs ${
                                        request.status === "已通过"
                                          ? "bg-green-100 text-green-800"
                                          : "bg-red-100 text-red-800"
                                      }`}
                                    >
                                      {request.status}
                                    </span>
                                  </td>
                                </tr>
                              ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* 添加成员弹窗 */}
      {showAddMemberModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-md p-6 relative">
            <button
              onClick={() => setShowAddMemberModal(false)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </button>

            <h2 className="text-xl font-bold mb-4">添加成员</h2>

            <div className="mb-4 relative">
              <input
                type="text"
                placeholder="搜索用户"
                value={searchMember}
                onChange={(e) => setSearchMember(e.target.value)}
                className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            </div>



            <div className="max-h-60 overflow-y-auto mb-4">
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <div
                    key={user.id}
                    className={`flex items-center p-3 rounded-md cursor-pointer ${selectedMember === user.id ? "bg-[#1e7a43]/10 border border-[#1e7a43]" : "hover:bg-gray-100"}`}
                    onClick={() => handleSelectMember(user.id)}
                  >
                    <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                      <User className="h-4 w-4 text-gray-500" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">{user.name}</div>
                      <div className="text-xs text-gray-500">{user.email}</div>
                    </div>
                    {selectedMember === user.id && <Check className="h-5 w-5 text-[#1e7a43]" />}
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <div>未找到匹配的用户</div>
                  <div className="mt-2 text-xs">
                    请尝试其他搜索关键词
                  </div>
                </div>
              )}
            </div>

            {selectedMember && (
              <div className="mb-4">
                <div className="p-3 bg-gray-50 rounded-md">
                  <p className="text-sm text-gray-600">所有添加的用户将被设置为<span className="font-medium">访客</span>权限</p>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={() => setShowAddMemberModal(false)}>
                取消
              </Button>
              <Button
                className="bg-[#f5a623] hover:bg-[#f5a623]/90"
                onClick={handleConfirmAddMember}
                disabled={!selectedMember}
              >
                确认添加
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 成功提示弹窗 */}
      {showSuccessToast && (
        <div className="fixed bottom-8 right-8 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md z-50 animate-fade-in-up">
          <div className="flex items-center">
            <svg className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
            <p>{successMessage}</p>
          </div>
        </div>
      )}
    </div>
  )
}
