"use client"

import { useState, useEffect, useRef } from 'react'
import { Send, Trash2, RefreshCw, Search, Filter, Download } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AIQueryRequest, ConversationMessage, ResearchResult } from '@/types/ai-assistants'
import aiAssistantService from '@/services/ai-assistant-service'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { HtmlTypewriter } from '@/components/ui/html-typewriter'

interface ResearchAssistantProps {
  title?: string
  assistantId?: string
}

/**
 * AI研究助手组件
 *
 * 用于提供研究问题的AI问答功能，支持引用文献和研究资料
 */
export function ResearchAssistant({ title = "AI研究助手", assistantId }: ResearchAssistantProps) {
  const [query, setQuery] = useState('')
  const [messages, setMessages] = useState<ConversationMessage[]>([])
  const [conversationId, setConversationId] = useState<string | undefined>()
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('chat')
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<ResearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [filter, setFilter] = useState('all')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 打字机效果相关状态
  const [showTypewriter, setShowTypewriter] = useState(false)
  const [typewriterText, setTypewriterText] = useState('')
  const [lastMessageId, setLastMessageId] = useState<string | null>(null)

  // 初始化助手
  useEffect(() => {
    setMessages([
      {
        id: 'initial',
        role: 'assistant',
        content: `您好，我是AI研究助手，可以帮助您查找资料、分析研究问题和提供学术建议。请问有什么可以帮助您的吗？`,
        created_at: new Date().toISOString()
      }
    ])
  }, [])

  // 滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // 打字机效果完成后的回调函数
  const handleTypewriterComplete = () => {
    console.log('打字机效果完成')
    setShowTypewriter(false)
    setIsLoading(false)
  }

  // 发送查询
  const handleSendQuery = async () => {
    if (!query.trim() || isLoading) return

    // 重置打字机状态
    setShowTypewriter(false)
    setTypewriterText('')

    // 添加用户消息
    const userMessage: ConversationMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: query,
      created_at: new Date().toISOString()
    }
    setMessages(prev => [...prev, userMessage])

    // 清空输入框
    setQuery('')
    setIsLoading(true)

    try {
      // 准备请求数据
      const requestData: AIQueryRequest = {
        query: userMessage.content,
        conversation_id: conversationId
      }

      // 调用API
      const response = await aiAssistantService.queryResearchAssistant(requestData, assistantId)

      // 保存会话ID
      if (response.conversation_id) {
        setConversationId(response.conversation_id)
      }

      // 处理响应内容
      if (response.answer && response.answer.trim() !== "") {
        // 检查是否包含思考过程（details标签）
        let cleanedAnswer = response.answer;

        // 如果包含思考过程，提取出正式回复部分
        if (response.answer.includes('<details') && response.answer.includes('</details>')) {
          const detailsEndIndex = response.answer.indexOf('</details>') + 10;
          cleanedAnswer = response.answer.substring(detailsEndIndex).trim();
          console.log('提取出正式回复部分，长度:', cleanedAnswer.length);
        }

        // 移除所有HTML标签，只保留纯文本
        const textOnly = cleanedAnswer.replace(/<[^>]*>/g, '');
        console.log('移除HTML标签后的纯文本，长度:', textOnly.length);

        // 设置打字机文本内容
        setTypewriterText(textOnly)

        // 添加助手回复
        const assistantMessage: ConversationMessage = {
          id: response.id || `assistant-${Date.now()}`,
          role: 'assistant',
          content: textOnly, // 使用纯文本内容
          created_at: response.created_at || new Date().toISOString(),
          references: response.references || []
        }

        // 保存最后一条消息ID，用于打字机效果
        setLastMessageId(assistantMessage.id)

        // 添加消息到列表
        setMessages(prev => [...prev, assistantMessage])

        // 显示打字机效果
        setShowTypewriter(true)
      } else {
        // 如果响应为空，添加默认消息
        const assistantMessage: ConversationMessage = {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: '抱歉，我无法处理您的请求，请稍后再试。',
          created_at: new Date().toISOString()
        }
        setMessages(prev => [...prev, assistantMessage])
        setIsLoading(false)
      }
    } catch (error) {
      console.error('查询失败:', error)

      // 添加错误消息
      const errorMessage: ConversationMessage = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: '抱歉，我遇到了一些问题，无法回答您的问题。请稍后再试。',
        created_at: new Date().toISOString()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  // 清空对话
  const handleClearConversation = async () => {
    if (conversationId) {
      try {
        await aiAssistantService.clearConversationHistory('assistant', conversationId)
      } catch (error) {
        console.error('清除对话历史失败:', error)
      }
    }

    // 重置状态
    setMessages([
      {
        id: 'initial',
        role: 'assistant',
        content: `您好，我是AI研究助手，可以帮助您查找资料、分析研究问题和提供学术建议。请问有什么可以帮助您的吗？`,
        created_at: new Date().toISOString()
      }
    ])
    setConversationId(undefined)
  }

  // 搜索资料
  const handleSearch = async () => {
    if (!searchQuery.trim() || isSearching) return

    setIsSearching(true)

    try {
      // 调用搜索API
      const results = await aiAssistantService.searchResearchMaterials(searchQuery, filter)
      setSearchResults(results)
    } catch (error) {
      console.error('搜索失败:', error)
    } finally {
      setIsSearching(false)
    }
  }

  // 下载资料
  const handleDownload = (result: ResearchResult) => {
    if (result.downloadUrl) {
      window.open(result.downloadUrl, '_blank')
    }
  }

  // 引用资料到对话
  const handleCiteToChat = (result: ResearchResult) => {
    setQuery(prev => {
      const citation = `\n\n参考资料：《${result.title}》(${result.author}, ${result.year})`
      return prev + citation
    })
    setActiveTab('chat')
  }

  return (
    <Card className="w-full h-full flex flex-col">
      <CardHeader className="px-4 py-3 border-b">
        <CardTitle className="text-lg flex justify-between items-center">
          <span>{title}</span>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClearConversation}
              title="清空对话"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden p-0 flex flex-col">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
          <TabsList className="grid grid-cols-2 mx-4 mt-2">
            <TabsTrigger value="chat">对话</TabsTrigger>
            <TabsTrigger value="search">资料搜索</TabsTrigger>
          </TabsList>

          <TabsContent value="chat" className="flex-1 flex flex-col overflow-hidden">
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg px-4 py-2 ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}
                  >
                    {showTypewriter && message.id === lastMessageId ? (
                      <HtmlTypewriter
                        html={typewriterText}
                        className="text-lg whitespace-pre-line"
                        speed={30}
                        onComplete={handleTypewriterComplete}
                      />
                    ) : (
                      <div className="whitespace-pre-line">{message.content}</div>
                    )}

                    {/* 引用资料 */}
                    {message.references && message.references.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <div className="text-xs font-medium mb-1">参考资料：</div>
                        <div className="space-y-2">
                          {message.references.map((ref, index) => (
                            <div key={index} className="text-xs">
                              <div className="font-medium">{ref.title}</div>
                              <div className="text-gray-500">{ref.author}, {ref.year}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {isLoading && !showTypewriter && (
                <div className="flex justify-start">
                  <div className="max-w-[80%] rounded-lg px-4 py-2 bg-muted flex items-center">
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    思考中...
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
            <div className="p-4 border-t">
              <div className="flex gap-2">
                <Textarea
                  placeholder="请输入您的问题..."
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      handleSendQuery()
                    }
                  }}
                  className="min-h-[60px] resize-none"
                />
                <Button
                  onClick={handleSendQuery}
                  disabled={!query.trim() || isLoading}
                  className="shrink-0"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="search" className="flex-1 flex flex-col overflow-hidden">
            <div className="p-4 border-b">
              <div className="flex gap-2 mb-3">
                <div className="flex-1">
                  <Input
                    placeholder="搜索研究资料..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleSearch()
                      }
                    }}
                  />
                </div>
                <Select value={filter} onValueChange={setFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="全部类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="article">学术文章</SelectItem>
                    <SelectItem value="book">图书</SelectItem>
                    <SelectItem value="thesis">论文</SelectItem>
                    <SelectItem value="report">报告</SelectItem>
                  </SelectContent>
                </Select>
                <Button onClick={handleSearch} disabled={isSearching}>
                  {isSearching ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              {searchResults.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  {isSearching ? '正在搜索...' : '暂无搜索结果'}
                </div>
              ) : (
                <div className="space-y-4">
                  {searchResults.map((result) => (
                    <Card key={result.id} className="overflow-hidden">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{result.title}</h3>
                            <div className="text-sm text-gray-500 mt-1">
                              {result.author} · {result.year} · {result.source}
                            </div>
                          </div>
                          <Badge variant={result.type === 'article' ? 'default' : 'secondary'}>
                            {result.type === 'article' ? '文章' :
                             result.type === 'book' ? '图书' :
                             result.type === 'thesis' ? '论文' : '报告'}
                          </Badge>
                        </div>
                        <p className="text-sm mt-2 line-clamp-2">{result.abstract}</p>
                        <div className="flex justify-end gap-2 mt-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCiteToChat(result)}
                          >
                            引用到对话
                          </Button>
                          {result.downloadUrl && (
                            <Button
                              size="sm"
                              onClick={() => handleDownload(result)}
                            >
                              <Download className="h-3 w-3 mr-1" />
                              下载
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
