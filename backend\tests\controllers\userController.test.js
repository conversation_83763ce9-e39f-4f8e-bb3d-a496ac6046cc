/**
 * 用户控制器测试
 */

const request = require('supertest');
const app = require('../../src/app');
const { User, Role, sequelize } = require('../../src/models');
const bcrypt = require('bcryptjs');
const {
  createTestUser,
  createTestAdmin,
  createTestRole,
  generateTestToken
} = require('../utils/testHelpers');

describe('用户控制器测试', () => {
  let testUser;
  let testAdmin;
  let userToken;
  let adminToken;
  let userRole;
  let adminRole;

  beforeAll(async () => {
    try {
      // 生成随机后缀，避免数据冲突
      const randomSuffix = Math.floor(Math.random() * 10000);

      // 创建测试角色
      userRole = await Role.create({
        name: `user_test_${randomSuffix}`,
        description: 'Regular user role',
        is_system: false
      });

      adminRole = await Role.create({
        name: `admin_test_${randomSuffix}`,
        description: 'Administrator role',
        is_system: true
      });

      // 创建测试用户
      testUser = await User.create({
        username: `testuser_${randomSuffix}`,
        email: `testuser_${randomSuffix}@example.com`,
        password: 'password123', // 使用模型的beforeCreate钩子自动加密
        phone: `138${randomSuffix.toString().padStart(8, '0')}`,
        is_active: true,
        role: 'basic_user',
        role_id: userRole.id
      });

      // 创建测试管理员
      testAdmin = await User.create({
        username: `testadmin_${randomSuffix}`,
        email: `testadmin_${randomSuffix}@example.com`,
        password: 'admin123', // 使用模型的beforeCreate钩子自动加密
        phone: `139${randomSuffix.toString().padStart(8, '0')}`,
        is_active: true,
        role: 'admin',
        role_id: adminRole.id
      });

      // 确保管理员角色设置正确
      await testAdmin.update({ role: 'admin' });

      // 重新获取管理员用户，确保角色已更新
      testAdmin = await User.findByPk(testAdmin.id);

      // 生成测试令牌
      userToken = generateTestToken(testUser);

      // 确保管理员令牌中的角色设置正确
      adminToken = jwt.sign(
        { id: testAdmin.id, username: testAdmin.username, role_id: testAdmin.role_id, role: 'admin' },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );
    } catch (error) {
      console.error('创建测试数据失败:', error);
      throw error;
    }
  });

  afterAll(async () => {
    // 清理测试数据
    try {
      await User.destroy({ where: { id: testUser.id } });
      await User.destroy({ where: { id: testAdmin.id } });
      await Role.destroy({ where: { id: userRole.id } });
      await Role.destroy({ where: { id: adminRole.id } });
      console.log('测试数据已清理');
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  });

  describe('注册用户 - POST /api/users/register', () => {
    it('应该成功注册新用户', async () => {
      const newUser = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: '13811112222'
      };

      const response = await request(app)
        .post('/api/users/register')
        .send(newUser);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('注册成功');
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.username).toBe(newUser.username);
      expect(response.body.data.email).toBe(newUser.email);
      expect(response.body.data).not.toHaveProperty('password');
    });

    it('当用户名已存在时应该返回错误', async () => {
      const duplicateUser = {
        username: testUser.username,
        email: '<EMAIL>',
        password: 'password123',
        phone: '13833334444'
      };

      const response = await request(app)
        .post('/api/users/register')
        .send(duplicateUser);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('已存在');
    });

    it('当邮箱已存在时应该返回错误', async () => {
      const duplicateUser = {
        username: 'uniqueuser',
        email: testUser.email,
        password: 'password123',
        phone: '13833334444'
      };

      const response = await request(app)
        .post('/api/users/register')
        .send(duplicateUser);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('已存在');
    });

    it('当缺少必要字段时应该返回错误', async () => {
      const incompleteUser = {
        username: 'incompleteuser',
        // 缺少email
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/users/register')
        .send(incompleteUser);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('必填字段');
    });
  });

  describe('用户登录 - POST /api/users/login', () => {
    it('应该成功登录并返回令牌', async () => {
      const loginCredentials = {
        username: testUser.username,
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/users/login')
        .send(loginCredentials);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('登录成功');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user.username).toBe(testUser.username);
    });

    it('当用户名不存在时应该返回错误', async () => {
      const invalidCredentials = {
        username: 'nonexistentuser',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/users/login')
        .send(invalidCredentials);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('用户不存在');
    });

    it('当密码错误时应该返回错误', async () => {
      const invalidCredentials = {
        username: testUser.username,
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/users/login')
        .send(invalidCredentials);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('密码错误');
    });
  });

  describe('获取当前用户信息 - GET /api/users/me', () => {
    it('应该返回已认证用户的信息', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.username).toBe(testUser.username);
      expect(response.body.data.email).toBe(testUser.email);
      expect(response.body.data).not.toHaveProperty('password');
    });

    it('当未提供令牌时应该返回错误', async () => {
      const response = await request(app)
        .get('/api/users/me');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('未授权');
    });

    it('当提供无效令牌时应该返回错误', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', 'Bearer invalidtoken');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('无效的令牌');
    });
  });

  describe('更新当前用户信息 - PUT /api/users/me', () => {
    it('应该成功更新用户信息', async () => {
      const updatedInfo = {
        phone: '13977778888',
        email: '<EMAIL>'
      };

      const response = await request(app)
        .put('/api/users/me')
        .set('Authorization', `Bearer ${userToken}`)
        .send(updatedInfo);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('更新成功');
      expect(response.body.data.phone).toBe(updatedInfo.phone);
      expect(response.body.data.email).toBe(updatedInfo.email);
    });

    it('当未认证时应该返回错误', async () => {
      const updatedInfo = {
        phone: '13966667777'
      };

      const response = await request(app)
        .put('/api/users/me')
        .send(updatedInfo);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('未授权');
    });
  });

  describe('修改密码 - PUT /api/users/change-password', () => {
    it('应该成功修改密码', async () => {
      const passwordData = {
        currentPassword: 'password123',
        newPassword: 'newpassword123',
        confirmPassword: 'newpassword123'
      };

      const response = await request(app)
        .put('/api/users/change-password')
        .set('Authorization', `Bearer ${userToken}`)
        .send(passwordData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('密码修改成功');
    });

    it('当当前密码错误时应该返回错误', async () => {
      const passwordData = {
        currentPassword: 'wrongpassword',
        newPassword: 'newpassword123',
        confirmPassword: 'newpassword123'
      };

      const response = await request(app)
        .put('/api/users/change-password')
        .set('Authorization', `Bearer ${userToken}`)
        .send(passwordData);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('当前密码错误');
    });

    it('当新密码和确认密码不匹配时应该返回错误', async () => {
      const passwordData = {
        currentPassword: 'newpassword123', // 上一个测试已经修改为这个密码
        newPassword: 'anothernewpassword',
        confirmPassword: 'differentpassword'
      };

      const response = await request(app)
        .put('/api/users/change-password')
        .set('Authorization', `Bearer ${userToken}`)
        .send(passwordData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('新密码和确认密码不匹配');
    });
  });

  describe('管理员获取所有用户 - GET /api/users', () => {
    it('管理员应该能获取所有用户列表', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('非管理员用户应该无法获取所有用户列表', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });
  });

  describe('管理员获取特定用户 - GET /api/users/:id', () => {
    it('管理员应该能获取特定用户信息', async () => {
      const response = await request(app)
        .get(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.id).toBe(testUser.id);
      expect(response.body.data.username).toBe(testUser.username);
    });

    it('当用户不存在时应该返回错误', async () => {
      const nonExistentId = 9999;
      const response = await request(app)
        .get(`/api/users/${nonExistentId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('用户不存在');
    });

    it('非管理员用户应该无法获取特定用户信息', async () => {
      const response = await request(app)
        .get(`/api/users/${testAdmin.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });
  });

  describe('管理员更新用户 - PUT /api/users/:id', () => {
    it('管理员应该能更新用户信息', async () => {
      const updatedInfo = {
        is_active: false,
        role_id: userRole.id
      };

      const response = await request(app)
        .put(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updatedInfo);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('更新成功');
      expect(response.body.data.is_active).toBe(updatedInfo.is_active);
      expect(response.body.data.role_id).toBe(updatedInfo.role_id);
    });

    it('非管理员用户应该无法更新用户信息', async () => {
      const updatedInfo = {
        is_active: true
      };

      const response = await request(app)
        .put(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updatedInfo);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });
  });

  describe('管理员删除用户 - DELETE /api/users/:id', () => {
    it('管理员应该能删除用户', async () => {
      // 生成随机后缀，避免数据冲突
      const randomSuffix = Math.floor(Math.random() * 10000);

      // 先创建一个要删除的测试用户
      const userToDelete = await User.create({
        username: `userToDelete_${randomSuffix}`,
        email: `delete_${randomSuffix}@example.com`,
        password: await bcrypt.hash('password123', 10),
        phone: `137${randomSuffix.toString().padStart(8, '0')}`,
        is_active: true,
        role: 'basic_user',
        role_id: userRole.id
      });

      const response = await request(app)
        .delete(`/api/users/${userToDelete.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('删除成功');

      // 验证用户已被删除
      const deletedUser = await User.findByPk(userToDelete.id);
      expect(deletedUser).toBeNull();
    });

    it('非管理员用户应该无法删除用户', async () => {
      const response = await request(app)
        .delete(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });

    it('当用户不存在时应该返回错误', async () => {
      const nonExistentId = 9999;
      const response = await request(app)
        .delete(`/api/users/${nonExistentId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('用户不存在');
    });
  });
});
