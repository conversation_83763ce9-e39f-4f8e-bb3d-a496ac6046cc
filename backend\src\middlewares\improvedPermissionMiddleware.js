/**
 * 改进的权限中间件
 * 
 * 统一使用基于角色的权限检查，而不是硬编码检查
 */

const { User, Role, Permission } = require('../models');

/**
 * 检查特定权限的中间件
 * @param {String|Array} permissionCode - 权限代码或权限代码数组
 * @param {Object} options - 配置选项
 * @param {Boolean} options.requireAll - 是否需要满足所有权限（默认为false，即满足任一权限即可）
 * @returns {Function} 中间件函数
 */
const checkPermission = (permissionCode, options = {}) => {
  return async (req, res, next) => {
    try {
      // 确保用户已认证
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '未授权，请先登录'
        });
      }

      // 获取用户角色
      const user = await User.findByPk(req.user.id, {
        include: [{
          model: Role,
          as: 'userRole',
          include: [{
            model: Permission,
            as: 'permissions'
          }]
        }]
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      if (!user.userRole) {
        return res.status(403).json({
          success: false,
          message: '用户没有关联角色'
        });
      }

      // 检查用户角色是否是管理员
      if (user.userRole.name === '管理员' || user.userRole.name === 'admin') {
        // 管理员拥有所有权限
        return next();
      }

      // 如果未提供权限代码，拒绝访问
      if (!permissionCode) {
        return res.status(403).json({
          success: false,
          message: '权限不足'
        });
      }

      // 检查用户是否有指定权限
      const permissions = user.userRole.permissions || [];
      const permissionCodes = permissions.map(p => p.code);

      // 将单个权限代码转换为数组
      const requiredPermissions = Array.isArray(permissionCode) ? permissionCode : [permissionCode];
      
      // 检查是否拥有所需权限
      const hasPermission = options.requireAll
        ? requiredPermissions.every(code => permissionCodes.includes(code))
        : requiredPermissions.some(code => permissionCodes.includes(code));

      if (hasPermission) {
        return next();
      }

      // 没有权限
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    } catch (error) {
      console.error('权限检查错误:', error);
      return res.status(500).json({
        success: false,
        message: '服务器错误',
        error: error.message
      });
    }
  };
};

/**
 * 检查用户是否是管理员
 * @returns {Function} 中间件函数
 */
const isAdmin = async (req, res, next) => {
  try {
    // 确保用户已认证
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未授权，请先登录'
      });
    }

    // 获取用户角色
    const user = await User.findByPk(req.user.id, {
      include: [{
        model: Role,
        as: 'userRole'
      }]
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查用户是否是管理员
    if (user.userRole && (user.userRole.name === '管理员' || user.userRole.name === 'admin')) {
      return next();
    }

    // 用户不是管理员
    return res.status(403).json({
      success: false,
      message: '需要管理员权限'
    });
  } catch (error) {
    console.error('管理员检查错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

/**
 * 检查用户是否有权限访问资源
 * 用于控制器内部的权限检查
 *
 * @param {Object} user - 用户对象
 * @param {String|Array} permissionCode - 权限代码或权限代码数组
 * @param {Object} options - 配置选项
 * @param {Boolean} options.requireAll - 是否需要满足所有权限
 * @param {Boolean} options.allowOwner - 是否允许资源所有者访问
 * @param {Number|String} options.ownerId - 资源所有者ID
 * @returns {Boolean} 是否有权限
 */
const hasPermission = async (user, permissionCode, options = {}) => {
  try {
    // 如果允许资源所有者访问，且用户是资源所有者
    if (options.allowOwner && options.ownerId && user.id === parseInt(options.ownerId)) {
      return true;
    }

    // 获取用户角色
    const userWithRole = await User.findByPk(user.id, {
      include: [{
        model: Role,
        as: 'userRole',
        include: [{
          model: Permission,
          as: 'permissions'
        }]
      }]
    });

    if (!userWithRole || !userWithRole.userRole) {
      return false;
    }

    // 检查用户角色是否是管理员
    if (userWithRole.userRole.name === '管理员' || userWithRole.userRole.name === 'admin') {
      return true;
    }

    // 如果未提供权限代码，拒绝访问
    if (!permissionCode) {
      return false;
    }

    // 检查用户是否有指定权限
    const permissions = userWithRole.userRole.permissions || [];
    const permissionCodes = permissions.map(p => p.code);

    // 将单个权限代码转换为数组
    const requiredPermissions = Array.isArray(permissionCode) ? permissionCode : [permissionCode];
    
    // 检查是否拥有所需权限
    return options.requireAll
      ? requiredPermissions.every(code => permissionCodes.includes(code))
      : requiredPermissions.some(code => permissionCodes.includes(code));
  } catch (error) {
    console.error('权限检查错误:', error);
    return false;
  }
};

module.exports = {
  checkPermission,
  isAdmin,
  hasPermission
};
