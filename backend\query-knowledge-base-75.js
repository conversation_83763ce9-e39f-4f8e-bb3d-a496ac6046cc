/**
 * 查询ID为75的知识库的详细信息及其文件
 */

// 加载环境变量
require('dotenv').config();

// 导入模型
const db = require('./src/models');

// 查询知识库及其文件
async function queryKnowledgeBase75() {
  try {
    // 连接数据库
    await db.sequelize.authenticate();
    console.log('数据库连接成功');

    // 查询ID为75的知识库记录
    const knowledgeBase = await db.KnowledgeBase.findByPk(75, {
      include: [
        {
          model: db.User,
          as: 'creator',
          attributes: ['id', 'username', 'email', 'role']
        }
      ]
    });

    if (knowledgeBase) {
      console.log('找到知识库记录:');
      console.log('知识库ID:', knowledgeBase.id);
      console.log('知识库名称:', knowledgeBase.name);
      console.log('知识库类型:', knowledgeBase.type);
      console.log('知识库描述:', knowledgeBase.description);
      console.log('创建时间:', knowledgeBase.created_at);
      console.log('文件数量:', knowledgeBase.file_count);
      console.log('存储大小:', knowledgeBase.storage_size, '字节');
      
      if (knowledgeBase.creator) {
        console.log('\n创建者信息:');
        console.log('创建者ID:', knowledgeBase.creator.id);
        console.log('创建者用户名:', knowledgeBase.creator.username);
        console.log('创建者邮箱:', knowledgeBase.creator.email);
        console.log('创建者角色:', knowledgeBase.creator.role);
      } else {
        console.log('\n未找到创建者信息');
      }
      
      // 查询该知识库下的所有文件
      const files = await db.File.findAll({
        where: {
          knowledge_base_id: 75
        },
        include: [
          {
            model: db.User,
            as: 'uploader',
            attributes: ['id', 'username', 'email']
          }
        ]
      });
      
      if (files.length > 0) {
        console.log('\n该知识库下的文件列表:');
        files.forEach((file, index) => {
          console.log(`\n文件 ${index + 1}:`);
          console.log('文件ID:', file.id);
          console.log('文件名:', file.original_name);
          console.log('文件类型:', file.type);
          console.log('文件大小:', file.size, '字节');
          console.log('上传时间:', file.created_at);
          console.log('上传者:', file.uploader ? file.uploader.username : '未知');
        });
      } else {
        console.log('\n该知识库下没有文件');
      }
    } else {
      console.log('未找到ID为75的知识库记录');
    }

    // 关闭数据库连接
    await db.sequelize.close();
  } catch (error) {
    console.error('查询失败:', error);
  }
}

// 执行查询
queryKnowledgeBase75();
