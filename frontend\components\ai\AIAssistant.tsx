/**
 * AI助手组件
 *
 * 用于与AI助手交互
 */

import React, { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from '@/components/ui/use-toast'
import { LoadingState } from '@/components/ui/loading-state'

// AI助手类型
export interface AIAssistant {
  id: number
  name: string
  description: string
  type: string
  api_key?: string
  model?: string
  created_at: string
  updated_at: string
}

// 消息类型
export interface Message {
  id: number
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
}

// 对话类型
export interface Conversation {
  id: number
  title: string
  messages: Message[]
  assistant_id: number
  created_at: string
  updated_at: string
}

// AI助手组件
export const AIAssistant = () => {
  const [assistants, setAssistants] = useState<AIAssistant[]>([])
  const [selectedAssistant, setSelectedAssistant] = useState<AIAssistant | null>(null)
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null)
  const [message, setMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 加载AI助手列表
  useEffect(() => {
    const fetchAssistants = async () => {
      setIsLoading(true)
      setError(null)
      try {
        // 模拟数据
        const mockData = {
          data: [
            {
              id: 1,
              name: '个人专题助手',
              description: '帮助用户处理个人专题相关问题',
              type: 'personal',
              api_key: 'mock_api_key_1',
              model: 'gpt-3.5-turbo',
              created_at: '2023-01-01T00:00:00Z',
              updated_at: '2023-01-01T00:00:00Z'
            },
            {
              id: 2,
              name: '数据查询助手',
              description: '帮助用户查询和分析数据',
              type: 'data',
              api_key: 'mock_api_key_2',
              model: 'gpt-4',
              created_at: '2023-01-02T00:00:00Z',
              updated_at: '2023-01-02T00:00:00Z'
            }
          ]
        }

        // 模拟网络延迟
        setTimeout(() => {
          setAssistants(mockData.data || [])

          // 如果有助手，默认选择第一个
          if (mockData.data && mockData.data.length > 0) {
            setSelectedAssistant(mockData.data[0])
          }

          setIsLoading(false)
        }, 500)
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载AI助手失败')
        toast({
          title: '加载失败',
          description: err instanceof Error ? err.message : '加载AI助手失败',
          variant: 'destructive'
        })
        setIsLoading(false)
      }
    }

    fetchAssistants()
  }, [])

  // 当选择助手变化时，加载对话列表
  useEffect(() => {
    if (selectedAssistant) {
      const fetchConversations = async () => {
        setIsLoading(true)
        setError(null)
        try {
          // 模拟数据
          const mockData = {
            data: [
              {
                id: 1,
                title: `与${selectedAssistant.name}的对话`,
                messages: [
                  {
                    id: 101,
                    role: 'user',
                    content: '你好，我需要一些帮助。',
                    timestamp: '2023-01-03T10:00:00Z'
                  },
                  {
                    id: 102,
                    role: 'assistant',
                    content: `你好！我是${selectedAssistant.name}，很高兴能帮助你。请告诉我你需要什么帮助？`,
                    timestamp: '2023-01-03T10:00:05Z'
                  }
                ],
                assistant_id: selectedAssistant.id,
                created_at: '2023-01-03T10:00:00Z',
                updated_at: '2023-01-03T10:00:05Z'
              }
            ]
          }

          // 模拟网络延迟
          setTimeout(() => {
            setConversations(mockData.data || [])

            // 如果有对话，默认选择第一个
            if (mockData.data && mockData.data.length > 0) {
              setCurrentConversation(mockData.data[0])
            } else {
              // 如果没有对话，创建一个新对话
              createNewConversation()
            }

            setIsLoading(false)
          }, 500)
        } catch (err) {
          setError(err instanceof Error ? err.message : '加载对话失败')
          toast({
            title: '加载失败',
            description: err instanceof Error ? err.message : '加载对话失败',
            variant: 'destructive'
          })
          setIsLoading(false)
        }
      }

      fetchConversations()
    }
  }, [selectedAssistant])

  // 滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [currentConversation?.messages])

  // 创建新对话
  const createNewConversation = async () => {
    if (!selectedAssistant) return

    setIsLoading(true)
    setError(null)

    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 创建新对话
      const newConversation: Conversation = {
        id: Date.now(),
        title: `与${selectedAssistant.name}的新对话`,
        messages: [],
        assistant_id: selectedAssistant.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      setConversations([newConversation, ...conversations])
      setCurrentConversation(newConversation)

      toast({
        title: '创建成功',
        description: '新对话已创建'
      })
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建对话失败')
      toast({
        title: '创建失败',
        description: err instanceof Error ? err.message : '创建对话失败',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 发送消息
  const sendMessage = async () => {
    if (!currentConversation || !message.trim() || !selectedAssistant) return

    setIsLoading(true)

    // 添加用户消息
    const userMessage: Message = {
      id: Date.now(),
      role: 'user',
      content: message,
      timestamp: new Date().toISOString()
    }

    const updatedMessages = [...(currentConversation.messages || []), userMessage]

    // 更新当前对话
    setCurrentConversation({
      ...currentConversation,
      messages: updatedMessages
    })

    // 清空输入框
    setMessage('')

    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟助手回复
      const assistantMessage: Message = {
        id: Date.now() + 1,
        role: 'assistant',
        content: `这是${selectedAssistant.name}的回复。\n\n您的消息是: "${userMessage.content}"\n\n我可以帮您解答这个问题或提供相关信息。`,
        timestamp: new Date().toISOString()
      }

      // 更新当前对话
      const finalMessages = [...updatedMessages, assistantMessage]
      const updatedConversation = {
        ...currentConversation,
        messages: finalMessages,
        updated_at: new Date().toISOString()
      }

      setCurrentConversation(updatedConversation)

      // 更新对话列表
      setConversations(conversations.map(conv =>
        conv.id === currentConversation.id ? updatedConversation : conv
      ))
    } catch (err) {
      setError(err instanceof Error ? err.message : '发送消息失败')
      toast({
        title: '发送失败',
        description: err instanceof Error ? err.message : '发送消息失败',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 选择对话
  const selectConversation = (conversation: Conversation) => {
    setCurrentConversation(conversation)
  }

  // 选择助手
  const handleSelectAssistant = (assistantId: string) => {
    const assistant = assistants.find(a => a.id.toString() === assistantId)
    if (assistant) {
      setSelectedAssistant(assistant)
    }
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">AI助手</h1>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* 侧边栏 */}
        <div className="md:col-span-1 space-y-6">
          {/* 助手选择 */}
          <Card>
            <CardHeader>
              <CardTitle>选择助手</CardTitle>
            </CardHeader>
            <CardContent>
              <Select
                value={selectedAssistant?.id.toString()}
                onValueChange={handleSelectAssistant}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择AI助手" />
                </SelectTrigger>
                <SelectContent>
                  {assistants.map(assistant => (
                    <SelectItem key={assistant.id} value={assistant.id.toString()}>
                      {assistant.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* 对话列表 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>对话列表</CardTitle>
              <Button variant="outline" size="sm" onClick={createNewConversation}>
                新对话
              </Button>
            </CardHeader>
            <CardContent>
              {conversations.length === 0 ? (
                <div className="text-center p-4 text-gray-500">暂无对话</div>
              ) : (
                <div className="space-y-2">
                  {conversations.map(conversation => (
                    <div
                      key={conversation.id}
                      className={`p-2 rounded cursor-pointer hover:bg-gray-100 ${
                        currentConversation?.id === conversation.id ? 'bg-gray-100' : ''
                      }`}
                      onClick={() => selectConversation(conversation)}
                    >
                      <div className="font-medium truncate">{conversation.title}</div>
                      <div className="text-xs text-gray-500">
                        {new Date(conversation.updated_at).toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 主内容区 */}
        <div className="md:col-span-3">
          <Card className="h-full flex flex-col">
            <CardHeader>
              <CardTitle>
                {currentConversation?.title || '新对话'}
              </CardTitle>
              {selectedAssistant && (
                <CardDescription>
                  {selectedAssistant.description}
                </CardDescription>
              )}
            </CardHeader>

            <CardContent className="flex-grow overflow-y-auto max-h-[60vh]">
              {isLoading && !currentConversation?.messages?.length && (
                <LoadingState status="loading" loadingText="正在加载对话..." />
              )}

              {error && !currentConversation?.messages?.length && (
                <LoadingState status="error" errorText={error} />
              )}

              {currentConversation?.messages?.length === 0 && (
                <div className="text-center p-4 text-gray-500">
                  开始与AI助手对话吧！
                </div>
              )}

              {currentConversation?.messages?.length > 0 && (
                <div className="space-y-4">
                  {currentConversation.messages.map(msg => (
                    <div
                      key={msg.id}
                      className={`p-3 rounded-lg ${
                        msg.role === 'user'
                          ? 'bg-blue-100 ml-12'
                          : 'bg-gray-100 mr-12'
                      }`}
                    >
                      <div className="font-medium mb-1">
                        {msg.role === 'user' ? '你' : selectedAssistant?.name}
                      </div>
                      <div className="whitespace-pre-wrap">{msg.content}</div>
                      <div className="text-xs text-gray-500 text-right mt-1">
                        {new Date(msg.timestamp).toLocaleString()}
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </CardContent>

            <CardFooter className="border-t p-4">
              <div className="flex w-full space-x-2">
                <Textarea
                  placeholder="输入消息..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="flex-grow"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      sendMessage()
                    }
                  }}
                />
                <Button
                  onClick={sendMessage}
                  disabled={isLoading || !message.trim()}
                >
                  {isLoading ? '发送中...' : '发送'}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default AIAssistant
