/**
 * 测试数据库连接
 */

// 加载环境变量
require('dotenv').config();

// 导入Sequelize
const { Sequelize } = require('sequelize');

// 创建Sequelize实例
const sequelize = new Sequelize(
  process.env.DB_NAME || 'hefamily_dev',
  process.env.DB_USERNAME || 'root',
  process.env.DB_PASSWORD || 'Misaya9987.',
  {
    host: process.env.DB_HOST || 'localhost',
    dialect: 'mysql',
    logging: console.log
  }
);

// 测试连接
async function testConnection() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功！');
    
    // 执行简单查询
    const [results] = await sequelize.query('SELECT 1+1 AS result');
    console.log('查询结果:', results);
    
    // 关闭连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('数据库连接失败:', error);
  }
}

// 执行测试
testConnection();
