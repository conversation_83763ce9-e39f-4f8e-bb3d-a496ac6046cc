/**
 * 查询文件列表
 */

// 加载环境变量
require('dotenv').config();

// 导入模型
const db = require('./src/models');

// 查询文件列表
async function queryFiles() {
  try {
    // 连接数据库
    await db.sequelize.authenticate();
    console.log('数据库连接成功');

    // 查询文件列表
    const files = await db.File.findAll({
      include: [
        {
          model: db.User,
          as: 'uploader',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    console.log('文件列表:', JSON.stringify(files, null, 2));

    // 关闭数据库连接
    await db.sequelize.close();
  } catch (error) {
    console.error('查询失败:', error);
  }
}

// 执行查询
queryFiles();
