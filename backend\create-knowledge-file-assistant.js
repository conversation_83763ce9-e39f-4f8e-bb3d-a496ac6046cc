const db = require('./src/models');

console.log('开始创建知识库文件分析助手...');
console.log('数据库模型:', Object.keys(db));

(async () => {
  try {
    console.log('查找是否已存在知识库文件分析助手...');
    // 查找是否已存在知识库文件分析助手
    const existingAssistant = await db.AIAssistant.findOne({
      where: { type: 'knowledge-file' }
    });

    if (existingAssistant) {
      console.log('知识库文件分析助手已存在，ID:', existingAssistant.id);
      console.log(JSON.stringify(existingAssistant, null, 2));
      process.exit();
      return;
    }

    console.log('未找到现有的知识库文件分析助手，将创建新的助手...');

    // 查找管理员用户
    const adminUser = await db.User.findOne({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.error('未找到管理员用户，无法创建知识库文件分析助手');
      process.exit(1);
      return;
    }

    // 创建知识库文件分析助手
    const assistant = await db.AIAssistant.create({
      name: '知识库文件分析助手',
      type: 'knowledge-file',
      description: '用于分析上传到知识库的文件，支持系统知识库和用户知识库',
      api_key: 'dataset-DLFJlUe25VUHOwMO4HbO4hQk',
      api_endpoint: 'https://ai.glab.vip',
      app_id: '77199451-730a-4d79-a1c9-9b9e6bfcd747', // 系统知识库数据集ID
      app_code: '602d59cf-3384-4105-bf91-e1481b30b6b2', // 用户知识库数据集ID
      upload_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
      analysis_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
      tags: '知识库,文件分析,Dify',
      initial_message: '我是知识库文件分析助手，可以帮助您分析上传的文件。',
      is_system: true,
      status: 'active',
      creator_id: adminUser.id,
      last_updated_by: adminUser.id
    });

    console.log('知识库文件分析助手创建成功，ID:', assistant.id);
    console.log(JSON.stringify(assistant, null, 2));
  } catch (e) {
    console.error('创建知识库文件分析助手失败:', e);
  } finally {
    process.exit();
  }
})();
