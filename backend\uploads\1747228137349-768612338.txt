开始上传文件: /activities/upload-image {formDataEntries: Array(1)}
api-service.ts:487 总文件大小: 1.56MB
api-service.ts:29 已添加认证头: Bearer eyJhbGciOi...
api-service.ts:38 API请求: POST /activities/upload-image {baseURL: 'http://*************/api', headers: AxiosHeaders, params: undefined, data: FormData}
memorial-activities.tsx:783 当前模式: 管理模式
memorial-activities.tsx:784 活动总数: 0
memorial-activities.tsx:785 显示的活动数: 0
memorial-activities.tsx:786 活动状态统计: {draft: 0, published: 0, archived: 0}
memorial-activities.tsx:799 用户登录状态: 已登录
memorial-activities.tsx:800 用户权限: {manage_activities: true, activity_manage: true}
memorial-activities.tsx:816 活动列表为空，可能是API调用问题或数据库中没有活动。
MemorialActivities @ memorial-activities.tsx:816
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16275
performWorkUntilDeadline @ scheduler.development.js:45
<MemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
ClientMemorialActivities @ client-memorial-activities.tsx:42
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<ClientMemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
Home @ page.tsx:16
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<Home>
exports.jsx @ react-jsx-runtime.development.js:319
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9894
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2334
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1011
resolveModel @ react-server-dom-webpack-client.browser.development.js:1579
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2268
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1567
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2376
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2696
eval @ app-index.js:133
(app-pages-browser)/../node_modules/next/dist/client/app-index.js @ main-app.js?v=1747210485723:160
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
fn @ webpack.js?v=1747210485723:369
eval @ app-next-dev.js:10
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:9
(app-pages-browser)/../node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1747210485723:182
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
__webpack_exec__ @ main-app.js?v=1747210485723:2792
(anonymous) @ main-app.js?v=1747210485723:2793
webpackJsonpCallback @ webpack.js?v=1747210485723:1388
(anonymous) @ main-app.js?v=1747210485723:9
Show 70 more frames
Show less
api-service.ts:504 文件上传进度: 100%
api-service.ts:496 
        
        
       POST http://*************/api/activities/upload-image 413 (Request Entity Too Large)
dispatchXhrRequest @ xhr.js:209
xhr @ xhr.js:29
dispatchRequest @ dispatchRequest.js:61
Promise.then
_request @ Axios.js:175
request @ Axios.js:52
httpMethod @ Axios.js:238
wrap @ bind.js:9
upload @ api-service.ts:496
uploadActivityImage @ activity-service.ts:181
handleImageUpload @ memorial-activities.tsx:507
executeDispatch @ react-dom-client.development.js:16427
runWithFiberInDEV @ react-dom-client.development.js:1511
processDispatchQueue @ react-dom-client.development.js:16477
eval @ react-dom-client.development.js:17075
batchedUpdates$1 @ react-dom-client.development.js:3254
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16631
dispatchEvent @ react-dom-client.development.js:20717
dispatchDiscreteEvent @ react-dom-client.development.js:20685
<input>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
EditActivityModal @ memorial-activities.tsx:1232
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<EditActivityModal>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
MemorialActivities @ memorial-activities.tsx:874
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<MemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
ClientMemorialActivities @ client-memorial-activities.tsx:42
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<ClientMemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
Home @ page.tsx:16
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<Home>
exports.jsx @ react-jsx-runtime.development.js:319
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9894
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2334
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1011
resolveModel @ react-server-dom-webpack-client.browser.development.js:1579
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2268
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1567
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2376
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2696
eval @ app-index.js:133
(app-pages-browser)/../node_modules/next/dist/client/app-index.js @ main-app.js?v=1747210485723:160
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
fn @ webpack.js?v=1747210485723:369
eval @ app-next-dev.js:10
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:9
(app-pages-browser)/../node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1747210485723:182
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
__webpack_exec__ @ main-app.js?v=1747210485723:2792
(anonymous) @ main-app.js?v=1747210485723:2793
webpackJsonpCallback @ webpack.js?v=1747210485723:1388
(anonymous) @ main-app.js?v=1747210485723:9
Show 102 more frames
Show less
api-service.ts:79 API请求错误: {url: '/activities/upload-image', method: 'post', status: 413, statusText: 'Request Entity Too Large', data: '<html>\r\n<head><title>413 Request Entity Too Large<…disable MSIE and Chrome friendly error page -->\r\n', …}
error @ intercept-console-error.js:50
eval @ api-service.ts:79
Promise.then
_request @ Axios.js:175
request @ Axios.js:52
httpMethod @ Axios.js:238
wrap @ bind.js:9
upload @ api-service.ts:496
uploadActivityImage @ activity-service.ts:181
handleImageUpload @ memorial-activities.tsx:507
executeDispatch @ react-dom-client.development.js:16427
runWithFiberInDEV @ react-dom-client.development.js:1511
processDispatchQueue @ react-dom-client.development.js:16477
eval @ react-dom-client.development.js:17075
batchedUpdates$1 @ react-dom-client.development.js:3254
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16631
dispatchEvent @ react-dom-client.development.js:20717
dispatchDiscreteEvent @ react-dom-client.development.js:20685
<input>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
EditActivityModal @ memorial-activities.tsx:1232
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<EditActivityModal>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
MemorialActivities @ memorial-activities.tsx:874
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<MemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
ClientMemorialActivities @ client-memorial-activities.tsx:42
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<ClientMemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
Home @ page.tsx:16
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<Home>
exports.jsx @ react-jsx-runtime.development.js:319
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9894
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2334
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1011
resolveModel @ react-server-dom-webpack-client.browser.development.js:1579
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2268
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1567
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2376
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2696
eval @ app-index.js:133
(app-pages-browser)/../node_modules/next/dist/client/app-index.js @ main-app.js?v=1747210485723:160
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
fn @ webpack.js?v=1747210485723:369
eval @ app-next-dev.js:10
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:9
(app-pages-browser)/../node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1747210485723:182
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
__webpack_exec__ @ main-app.js?v=1747210485723:2792
(anonymous) @ main-app.js?v=1747210485723:2793
webpackJsonpCallback @ webpack.js?v=1747210485723:1388
(anonymous) @ main-app.js?v=1747210485723:9
Show 100 more frames
Show less
api-service.ts:526 文件上传失败: /activities/upload-image {message: 'Request failed with status code 413', code: 'ERR_BAD_REQUEST', response: '<html>\r\n<head><title>413 Request Entity Too Large<…disable MSIE and Chrome friendly error page -->\r\n'}
error @ intercept-console-error.js:50
upload @ api-service.ts:526
await in upload
uploadActivityImage @ activity-service.ts:181
handleImageUpload @ memorial-activities.tsx:507
executeDispatch @ react-dom-client.development.js:16427
runWithFiberInDEV @ react-dom-client.development.js:1511
processDispatchQueue @ react-dom-client.development.js:16477
eval @ react-dom-client.development.js:17075
batchedUpdates$1 @ react-dom-client.development.js:3254
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16631
dispatchEvent @ react-dom-client.development.js:20717
dispatchDiscreteEvent @ react-dom-client.development.js:20685
<input>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
EditActivityModal @ memorial-activities.tsx:1232
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<EditActivityModal>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
MemorialActivities @ memorial-activities.tsx:874
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<MemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
ClientMemorialActivities @ client-memorial-activities.tsx:42
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<ClientMemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
Home @ page.tsx:16
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<Home>
exports.jsx @ react-jsx-runtime.development.js:319
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9894
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2334
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1011
resolveModel @ react-server-dom-webpack-client.browser.development.js:1579
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2268
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1567
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2376
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2696
eval @ app-index.js:133
(app-pages-browser)/../node_modules/next/dist/client/app-index.js @ main-app.js?v=1747210485723:160
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
fn @ webpack.js?v=1747210485723:369
eval @ app-next-dev.js:10
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:9
(app-pages-browser)/../node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1747210485723:182
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
__webpack_exec__ @ main-app.js?v=1747210485723:2792
(anonymous) @ main-app.js?v=1747210485723:2793
webpackJsonpCallback @ webpack.js?v=1747210485723:1388
(anonymous) @ main-app.js?v=1747210485723:9
Show 96 more frames
Show less
api-service.ts:539 文件太大，超出服务器允许的最大大小
error @ intercept-console-error.js:50
upload @ api-service.ts:539
await in upload
uploadActivityImage @ activity-service.ts:181
handleImageUpload @ memorial-activities.tsx:507
executeDispatch @ react-dom-client.development.js:16427
runWithFiberInDEV @ react-dom-client.development.js:1511
processDispatchQueue @ react-dom-client.development.js:16477
eval @ react-dom-client.development.js:17075
batchedUpdates$1 @ react-dom-client.development.js:3254
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16631
dispatchEvent @ react-dom-client.development.js:20717
dispatchDiscreteEvent @ react-dom-client.development.js:20685
<input>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
EditActivityModal @ memorial-activities.tsx:1232
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<EditActivityModal>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
MemorialActivities @ memorial-activities.tsx:874
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<MemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
ClientMemorialActivities @ client-memorial-activities.tsx:42
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<ClientMemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
Home @ page.tsx:16
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<Home>
exports.jsx @ react-jsx-runtime.development.js:319
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9894
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2334
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1011
resolveModel @ react-server-dom-webpack-client.browser.development.js:1579
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2268
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1567
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2376
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2696
eval @ app-index.js:133
(app-pages-browser)/../node_modules/next/dist/client/app-index.js @ main-app.js?v=1747210485723:160
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
fn @ webpack.js?v=1747210485723:369
eval @ app-next-dev.js:10
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:9
(app-pages-browser)/../node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1747210485723:182
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
__webpack_exec__ @ main-app.js?v=1747210485723:2792
(anonymous) @ main-app.js?v=1747210485723:2793
webpackJsonpCallback @ webpack.js?v=1747210485723:1388
(anonymous) @ main-app.js?v=1747210485723:9
Show 96 more frames
Show less
memorial-activities.tsx:526 上传图片失败: AxiosError {message: 'Request failed with status code 413', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}
error @ intercept-console-error.js:50
handleImageUpload @ memorial-activities.tsx:526
await in handleImageUpload
executeDispatch @ react-dom-client.development.js:16427
runWithFiberInDEV @ react-dom-client.development.js:1511
processDispatchQueue @ react-dom-client.development.js:16477
eval @ react-dom-client.development.js:17075
batchedUpdates$1 @ react-dom-client.development.js:3254
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16631
dispatchEvent @ react-dom-client.development.js:20717
dispatchDiscreteEvent @ react-dom-client.development.js:20685
<input>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
EditActivityModal @ memorial-activities.tsx:1232
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<EditActivityModal>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
MemorialActivities @ memorial-activities.tsx:874
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<MemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
ClientMemorialActivities @ client-memorial-activities.tsx:42
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<ClientMemorialActivities>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
Home @ page.tsx:16
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<Home>
exports.jsx @ react-jsx-runtime.development.js:319
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9894
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2334
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1011
resolveModel @ react-server-dom-webpack-client.browser.development.js:1579
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2268
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1567
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2376
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2696
eval @ app-index.js:133
(app-pages-browser)/../node_modules/next/dist/client/app-index.js @ main-app.js?v=1747210485723:160
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
fn @ webpack.js?v=1747210485723:369
eval @ app-next-dev.js:10
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:9
(app-pages-browser)/../node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1747210485723:182
options.factory @ webpack.js?v=1747210485723:712
__webpack_require__ @ webpack.js?v=1747210485723:37
__webpack_exec__ @ main-app.js?v=1747210485723:2792
(anonymous) @ main-app.js?v=1747210485723:2793
webpackJsonpCallback @ webpack.js?v=1747210485723:1388
(anonymous) @ main-app.js?v=1747210485723:9
Show 96 more frames
Show less
*************/:1 Unchecked runtime.lastError: can not use with devtools