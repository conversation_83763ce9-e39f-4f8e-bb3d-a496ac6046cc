/**
 * 简单组件测试
 */

import React from 'react'
import { render, screen } from '@testing-library/react'

// 简单组件
const SimpleComponent = ({ text }: { text: string }) => {
  return <div data-testid="simple-component">{text}</div>
}

describe('SimpleComponent', () => {
  test('renders the text', () => {
    render(<SimpleComponent text="Hello, world!" />)

    const element = screen.getByTestId('simple-component')
    expect(element).toBeInTheDocument()
    expect(element).toHaveTextContent('Hello, world!')
  })
})
