/**
 * 工具函数单元测试
 */

const {
  formatDate,
  generateRandomString,
  validateEmail,
  validatePhone,
  sanitizeInput,
  paginateResults
} = require('../../src/utils/helpers');

describe('工具函数单元测试', () => {
  describe('formatDate', () => {
    it('应该正确格式化日期', () => {
      const date = new Date('2023-01-15T12:30:45');
      const formatted = formatDate(date);
      expect(formatted).toBe('2023-01-15 12:30:45');
    });

    it('当传入null时应该返回空字符串', () => {
      const formatted = formatDate(null);
      expect(formatted).toBe('');
    });

    it('当传入无效日期时应该返回空字符串', () => {
      const formatted = formatDate('invalid-date');
      expect(formatted).toBe('');
    });
  });

  describe('generateRandomString', () => {
    it('应该生成指定长度的随机字符串', () => {
      const length = 10;
      const randomString = generateRandomString(length);
      expect(randomString.length).toBe(length);
    });

    it('应该生成不同的随机字符串', () => {
      const length = 10;
      const randomString1 = generateRandomString(length);
      const randomString2 = generateRandomString(length);
      expect(randomString1).not.toBe(randomString2);
    });

    it('当长度为0时应该返回空字符串', () => {
      const randomString = generateRandomString(0);
      expect(randomString).toBe('');
    });
  });

  describe('validateEmail', () => {
    it('应该验证有效的邮箱地址', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('应该拒绝无效的邮箱地址', () => {
      expect(validateEmail('invalid')).toBe(false);
      expect(validateEmail('invalid@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
      expect(validateEmail('user@.com')).toBe(false);
      expect(validateEmail('user@domain')).toBe(false);
    });

    it('应该拒绝空值', () => {
      expect(validateEmail('')).toBe(false);
      expect(validateEmail(null)).toBe(false);
      expect(validateEmail(undefined)).toBe(false);
    });
  });

  describe('validatePhone', () => {
    it('应该验证有效的中国手机号', () => {
      expect(validatePhone('13800138000')).toBe(true);
      expect(validatePhone('15912345678')).toBe(true);
      expect(validatePhone('18612345678')).toBe(true);
      expect(validatePhone('19912345678')).toBe(true);
    });

    it('应该拒绝无效的中国手机号', () => {
      expect(validatePhone('12345678901')).toBe(false); // 不是1开头
      expect(validatePhone('1234567890')).toBe(false);  // 长度不对
      expect(validatePhone('123456789012')).toBe(false); // 长度不对
      expect(validatePhone('abcdefghijk')).toBe(false); // 非数字
    });

    it('应该拒绝空值', () => {
      expect(validatePhone('')).toBe(false);
      expect(validatePhone(null)).toBe(false);
      expect(validatePhone(undefined)).toBe(false);
    });
  });

  describe('sanitizeInput', () => {
    it('应该清理HTML标签', () => {
      const input = '<script>alert("XSS")</script>Hello<b>World</b>';
      const sanitized = sanitizeInput(input);
      expect(sanitized).toBe('Hello World');
      // 确保没有HTML标签
      expect(sanitized).not.toContain('<b>');
      expect(sanitized).not.toContain('</b>');
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('</script>');
      expect(sanitized).not.toContain('alert');
      expect(sanitized).not.toContain('XSS');
    });

    it('应该处理空值', () => {
      expect(sanitizeInput('')).toBe('');
      expect(sanitizeInput(null)).toBe('');
      expect(sanitizeInput(undefined)).toBe('');
    });

    it('应该保留普通文本', () => {
      const input = 'Hello World';
      const sanitized = sanitizeInput(input);
      expect(sanitized).toBe(input);
    });
  });

  describe('paginateResults', () => {
    it('应该正确分页结果', () => {
      const items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      const page = 2;
      const pageSize = 3;

      const result = paginateResults(items, page, pageSize);

      expect(result.items).toEqual([4, 5, 6]);
      expect(result.total).toBe(10);
      expect(result.page).toBe(2);
      expect(result.pageSize).toBe(3);
      expect(result.totalPages).toBe(4);
    });

    it('当页码超出范围时应该返回空数组', () => {
      const items = [1, 2, 3, 4, 5];
      const page = 10;
      const pageSize = 2;

      const result = paginateResults(items, page, pageSize);

      expect(result.items).toEqual([]);
      expect(result.total).toBe(5);
      expect(result.page).toBe(10);
      expect(result.pageSize).toBe(2);
      expect(result.totalPages).toBe(3);
    });

    it('当项目为空时应该返回空数组', () => {
      const items = [];
      const page = 1;
      const pageSize = 10;

      const result = paginateResults(items, page, pageSize);

      expect(result.items).toEqual([]);
      expect(result.total).toBe(0);
      expect(result.page).toBe(1);
      expect(result.pageSize).toBe(10);
      expect(result.totalPages).toBe(0);
    });

    it('当页码为负数时应该使用第一页', () => {
      const items = [1, 2, 3, 4, 5];
      const page = -1;
      const pageSize = 2;

      const result = paginateResults(items, page, pageSize);

      expect(result.items).toEqual([1, 2]);
      expect(result.page).toBe(1);
    });

    it('当页大小为负数时应该使用默认页大小', () => {
      const items = [1, 2, 3, 4, 5];
      const page = 1;
      const pageSize = -1;

      const result = paginateResults(items, page, pageSize);

      expect(result.items).toEqual([1, 2, 3, 4, 5]);
      expect(result.pageSize).toBe(10); // 默认页大小
    });
  });
});
