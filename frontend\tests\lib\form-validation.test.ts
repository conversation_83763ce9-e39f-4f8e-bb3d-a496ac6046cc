/**
 * 表单验证工具函数测试
 */

import {
  validateRequired,
  validateMinLength,
  validateMaxLength,
  validateEmail,
  validatePhone,
  validatePassword,
  validateMatch,
  validateUrl,
  validateAll
} from '@/lib/form-validation'

describe('表单验证工具函数', () => {
  // 测试必填验证
  describe('validateRequired', () => {
    test('应该验证空值', () => {
      expect(validateRequired('').valid).toBe(false)
      expect(validateRequired('  ').valid).toBe(false)
      expect(validateRequired(null as any).valid).toBe(false)
      expect(validateRequired(undefined as any).valid).toBe(false)
    })

    test('应该验证非空值', () => {
      expect(validateRequired('test').valid).toBe(true)
      expect(validateRequired('0').valid).toBe(true)
    })

    test('应该返回自定义字段名称的错误消息', () => {
      const result = validateRequired('', '用户名')
      expect(result.valid).toBe(false)
      expect(result.message).toBe('用户名不能为空')
    })
  })

  // 测试最小长度验证
  describe('validateMinLength', () => {
    test('应该验证最小长度', () => {
      expect(validateMinLength('abc', 4).valid).toBe(false)
      expect(validateMinLength('abcd', 4).valid).toBe(true)
      expect(validateMinLength('abcde', 4).valid).toBe(true)
    })

    test('应该返回自定义字段名称的错误消息', () => {
      const result = validateMinLength('abc', 4, '密码')
      expect(result.valid).toBe(false)
      expect(result.message).toBe('密码长度不能少于4个字符')
    })
  })

  // 测试最大长度验证
  describe('validateMaxLength', () => {
    test('应该验证最大长度', () => {
      expect(validateMaxLength('abc', 2).valid).toBe(false)
      expect(validateMaxLength('ab', 2).valid).toBe(true)
      expect(validateMaxLength('a', 2).valid).toBe(true)
    })

    test('应该返回自定义字段名称的错误消息', () => {
      const result = validateMaxLength('abc', 2, '用户名')
      expect(result.valid).toBe(false)
      expect(result.message).toBe('用户名长度不能超过2个字符')
    })
  })

  // 测试邮箱验证
  describe('validateEmail', () => {
    test('应该验证有效的邮箱地址', () => {
      expect(validateEmail('<EMAIL>').valid).toBe(true)
      expect(validateEmail('<EMAIL>').valid).toBe(true)
      expect(validateEmail('<EMAIL>').valid).toBe(true)
    })

    test('应该验证无效的邮箱地址', () => {
      expect(validateEmail('test').valid).toBe(false)
      expect(validateEmail('test@').valid).toBe(false)
      expect(validateEmail('test@example').valid).toBe(false)
      expect(validateEmail('@example.com').valid).toBe(false)
    })

    test('应该允许空值（由validateRequired处理）', () => {
      expect(validateEmail('').valid).toBe(true)
    })
  })

  // 测试手机号验证
  describe('validatePhone', () => {
    test('应该验证有效的中国手机号', () => {
      expect(validatePhone('13812345678').valid).toBe(true)
      expect(validatePhone('15912345678').valid).toBe(true)
      expect(validatePhone('18812345678').valid).toBe(true)
    })

    test('应该验证无效的手机号', () => {
      expect(validatePhone('1381234567').valid).toBe(false) // 少一位
      expect(validatePhone('138123456789').valid).toBe(false) // 多一位
      expect(validatePhone('12812345678').valid).toBe(false) // 不是1[3-9]开头
      expect(validatePhone('abcdefghijk').valid).toBe(false) // 非数字
    })

    test('应该允许空值（由validateRequired处理）', () => {
      expect(validatePhone('').valid).toBe(true)
    })
  })

  // 测试密码验证
  describe('validatePassword', () => {
    test('应该验证密码最小长度', () => {
      expect(validatePassword('1234567').valid).toBe(false)
      expect(validatePassword('12345678').valid).toBe(true)
    })

    test('应该验证密码包含大写字母', () => {
      expect(validatePassword('12345678', { requireUppercase: true }).valid).toBe(false)
      expect(validatePassword('12345678A', { requireUppercase: true }).valid).toBe(true)
    })

    test('应该验证密码包含小写字母', () => {
      expect(validatePassword('12345678A', { requireLowercase: true }).valid).toBe(false)
      expect(validatePassword('12345678Aa', { requireLowercase: true }).valid).toBe(true)
    })

    test('应该验证密码包含数字', () => {
      expect(validatePassword('abcdefgH', { requireNumbers: true }).valid).toBe(false)
      expect(validatePassword('abcdefgH1', { requireNumbers: true }).valid).toBe(true)
    })

    test('应该验证密码包含特殊字符', () => {
      expect(validatePassword('abcdefgH1', { requireSpecialChars: true }).valid).toBe(false)
      expect(validatePassword('abcdefgH1!', { requireSpecialChars: true }).valid).toBe(true)
    })

    test('应该支持自定义验证选项', () => {
      // 只验证长度，不验证其他条件
      expect(validatePassword('12345678', {
        requireUppercase: false,
        requireLowercase: false,
        requireNumbers: false,
        requireSpecialChars: false
      }).valid).toBe(true)
    })
  })

  // 测试匹配验证
  describe('validateMatch', () => {
    test('应该验证两个值是否匹配', () => {
      expect(validateMatch('password', 'password').valid).toBe(true)
      expect(validateMatch('password', 'different').valid).toBe(false)
    })

    test('应该返回自定义字段名称的错误消息', () => {
      const result = validateMatch('password', 'different', '密码')
      expect(result.valid).toBe(false)
      expect(result.message).toBe('密码不匹配')
    })
  })

  // 测试URL验证
  describe('validateUrl', () => {
    test('应该验证有效的URL', () => {
      expect(validateUrl('https://example.com').valid).toBe(true)
      expect(validateUrl('http://example.com/path').valid).toBe(true)
      expect(validateUrl('https://sub.example.co.uk/path?query=1').valid).toBe(true)
    })

    test('应该验证无效的URL', () => {
      expect(validateUrl('example.com').valid).toBe(false) // 缺少协议
      expect(validateUrl('htp:/example').valid).toBe(false) // 无效协议
    })

    test('应该允许空值（由validateRequired处理）', () => {
      expect(validateUrl('').valid).toBe(true)
    })
  })

  // 测试组合验证
  describe('validateAll', () => {
    test('应该组合多个验证函数', () => {
      const validators = [
        (value: string) => validateRequired(value, '邮箱'),
        validateEmail
      ]

      expect(validateAll('', validators).valid).toBe(false)
      expect(validateAll('invalid', validators).valid).toBe(false)
      expect(validateAll('<EMAIL>', validators).valid).toBe(true)
    })

    test('应该在第一个验证失败时停止', () => {
      const validators = [
        (value: string) => validateRequired(value, '邮箱'),
        validateEmail
      ]

      const result = validateAll('', validators)
      expect(result.valid).toBe(false)
      expect(result.message).toBe('邮箱不能为空')
      // 不会继续验证邮箱格式
    })
  })
})
