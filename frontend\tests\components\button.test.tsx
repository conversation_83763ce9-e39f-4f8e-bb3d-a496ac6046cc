/**
 * 按钮组件测试
 */

import React from 'react'
import { render, screen, fireEvent } from '../test-utils'
import { Button } from '@/components/ui/button'

describe('Button组件', () => {
  // 测试基本渲染
  test('应该正确渲染按钮', () => {
    render(<Button>测试按钮</Button>)
    
    const button = screen.getByRole('button', { name: '测试按钮' })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('inline-flex')
  })

  // 测试不同变体
  test('应该支持不同变体', () => {
    const { rerender } = render(<Button variant="default">默认按钮</Button>)
    
    let button = screen.getByRole('button', { name: '默认按钮' })
    expect(button).toHaveClass('bg-primary')
    
    rerender(<Button variant="destructive">危险按钮</Button>)
    button = screen.getByRole('button', { name: '危险按钮' })
    expect(button).toHaveClass('bg-destructive')
    
    rerender(<Button variant="outline">轮廓按钮</Button>)
    button = screen.getByRole('button', { name: '轮廓按钮' })
    expect(button).toHaveClass('border')
    
    rerender(<Button variant="ghost">幽灵按钮</Button>)
    button = screen.getByRole('button', { name: '幽灵按钮' })
    expect(button).toHaveClass('hover:bg-accent')
  })

  // 测试不同尺寸
  test('应该支持不同尺寸', () => {
    const { rerender } = render(<Button size="default">默认尺寸</Button>)
    
    let button = screen.getByRole('button', { name: '默认尺寸' })
    expect(button).toHaveClass('h-10')
    
    rerender(<Button size="sm">小尺寸</Button>)
    button = screen.getByRole('button', { name: '小尺寸' })
    expect(button).toHaveClass('h-9')
    
    rerender(<Button size="lg">大尺寸</Button>)
    button = screen.getByRole('button', { name: '大尺寸' })
    expect(button).toHaveClass('h-11')
  })

  // 测试禁用状态
  test('应该支持禁用状态', () => {
    render(<Button disabled>禁用按钮</Button>)
    
    const button = screen.getByRole('button', { name: '禁用按钮' })
    expect(button).toBeDisabled()
    expect(button).toHaveClass('opacity-50')
  })

  // 测试加载状态
  test('应该支持加载状态', () => {
    render(<Button isLoading>加载按钮</Button>)
    
    const button = screen.getByRole('button', { name: '加载按钮' })
    expect(button).toHaveClass('cursor-not-allowed')
    expect(screen.getByRole('status')).toBeInTheDocument()
  })

  // 测试点击事件
  test('应该触发点击事件', () => {
    const handleClick = jest.fn()
    
    render(<Button onClick={handleClick}>点击按钮</Button>)
    
    const button = screen.getByRole('button', { name: '点击按钮' })
    fireEvent.click(button)
    
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  // 测试禁用状态下不触发点击事件
  test('禁用状态下不应该触发点击事件', () => {
    const handleClick = jest.fn()
    
    render(<Button disabled onClick={handleClick}>禁用按钮</Button>)
    
    const button = screen.getByRole('button', { name: '禁用按钮' })
    fireEvent.click(button)
    
    expect(handleClick).not.toHaveBeenCalled()
  })

  // 测试加载状态下不触发点击事件
  test('加载状态下不应该触发点击事件', () => {
    const handleClick = jest.fn()
    
    render(<Button isLoading onClick={handleClick}>加载按钮</Button>)
    
    const button = screen.getByRole('button', { name: '加载按钮' })
    fireEvent.click(button)
    
    expect(handleClick).not.toHaveBeenCalled()
  })

  // 测试自定义类名
  test('应该支持自定义类名', () => {
    render(<Button className="custom-class">自定义按钮</Button>)
    
    const button = screen.getByRole('button', { name: '自定义按钮' })
    expect(button).toHaveClass('custom-class')
  })
})
