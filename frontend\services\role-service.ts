/**
 * 角色服务
 *
 * 提供与角色相关的API调用
 */

import apiService from './api-service';
import { Permission } from './permission-service';

// 角色类型
export interface Role {
  id: number;
  name: string;
  description: string;
  is_preset: boolean;
  created_at: string;
  updated_at: string;
  user_count?: number;
  permissions?: Permission[];
}

// 创建角色请求
export interface CreateRoleRequest {
  name: string;
  description: string;
  permissions?: number[];
}

// 更新角色请求
export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: number[];
}

// 角色服务
const roleService = {
  /**
   * 获取所有角色
   * @param search 搜索关键词
   * @returns 角色列表
   */
  async getRoles(search?: string): Promise<Role[]> {
    try {
      const params: any = {};
      if (search) {
        params.search = search;
      }

      const response = await apiService.get('/roles', params);

      // 处理不同的响应格式
      if (response && response.data) {
        return response.data;
      } else {
        return [];
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
      // 出错时返回空数组，避免前端崩溃
      return [];
    }
  },

  /**
   * 获取角色详情
   * @param id 角色ID
   * @returns 角色详情
   */
  async getRoleById(id: number): Promise<Role> {
    try {
      const response = await apiService.get(`/roles/${id}`);
      return response.data || {};
    } catch (error) {
      console.error('获取角色详情失败:', error);
      return {} as Role;
    }
  },

  /**
   * 创建角色
   * @param data 创建角色请求数据
   * @returns 创建的角色
   */
  async createRole(data: CreateRoleRequest): Promise<Role> {
    try {
      const response = await apiService.post('/roles', data);
      return response.data || {};
    } catch (error) {
      console.error('创建角色失败:', error);
      throw error;
    }
  },

  /**
   * 更新角色
   * @param id 角色ID
   * @param data 更新角色请求数据
   * @returns 更新后的角色
   */
  async updateRole(id: number, data: UpdateRoleRequest): Promise<Role> {
    try {
      const response = await apiService.put(`/roles/${id}`, data);
      return response.data || {};
    } catch (error) {
      console.error('更新角色失败:', error);
      throw error;
    }
  },

  /**
   * 删除角色
   * @param id 角色ID
   * @returns 操作结果
   */
  async deleteRole(id: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiService.del(`/roles/${id}`);
      return response.data || { success: false, message: '删除失败' };
    } catch (error) {
      console.error('删除角色失败:', error);
      return { success: false, message: '删除失败: ' + (error.message || '未知错误') };
    }
  }
};

export default roleService;
