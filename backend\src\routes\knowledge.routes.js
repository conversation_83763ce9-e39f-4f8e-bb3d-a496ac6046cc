/**
 * 知识库相关路由
 *
 * 处理知识库的创建、查询、更新、删除等请求
 */

const express = require('express');
const router = express.Router();
const knowledgeController = require('../controllers/knowledge.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');

// 创建知识库 (需要认证)
router.post('/',
  authMiddleware,
  knowledgeController.createKnowledgeBase
);

// 获取知识库列表 (需要认证)
router.get('/',
  authMiddleware,
  knowledgeController.getKnowledgeBases
);

// 获取知识库详情 (需要认证)
router.get('/:id',
  authMiddleware,
  knowledgeController.getKnowledgeBaseById
);

// 更新知识库 (需要认证和权限)
router.put('/:id',
  authMiddleware,
  knowledgeController.updateKnowledgeBase
);

// 删除知识库 (需要认证和权限)
router.delete('/:id',
  authMiddleware,
  knowledgeController.deleteKnowledgeBase
);

// 获取知识库访问权限列表 (需要认证和权限)
router.get('/:id/access',
  authMiddleware,
  knowledgeController.getKnowledgeBaseAccess
);

// 添加知识库访问权限 (需要认证和权限)
router.post('/:id/access',
  authMiddleware,
  knowledgeController.addKnowledgeBaseAccess
);

// 更新知识库访问权限 (需要认证和权限)
router.put('/:id/access/:userId',
  authMiddleware,
  knowledgeController.updateKnowledgeBaseAccess
);

// 删除知识库访问权限 (需要认证和权限)
router.delete('/:id/access/:userId',
  authMiddleware,
  knowledgeController.removeKnowledgeBaseAccess
);

// 获取知识库访问申请列表 (需要认证和权限)
router.get('/:id/access-requests',
  authMiddleware,
  (req, res, next) => {
    // 转发到知识库访问申请控制器
    req.url = `/knowledge-access-requests/knowledge-base/${req.params.id}`;
    req.baseUrl = '';
    next();
  }
);

module.exports = router;
