/**
 * 查询数据库表信息
 */

// 加载环境变量
require('dotenv').config();

// 导入Sequelize
const { Sequelize } = require('sequelize');

// 创建Sequelize实例
const sequelize = new Sequelize(
  process.env.DB_NAME || 'hefamily_dev',
  process.env.DB_USERNAME || 'root',
  process.env.DB_PASSWORD || 'Misaya9987.',
  {
    host: process.env.DB_HOST || 'localhost',
    dialect: 'mysql',
    logging: console.log
  }
);

// 查询数据库表
async function queryTables() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功！');
    
    // 查询所有表
    const [tables] = await sequelize.query('SHOW TABLES');
    console.log('数据库表列表:');
    console.log(tables);
    
    // 关闭连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('查询失败:', error);
  }
}

// 执行查询
queryTables();
