/**
 * 认证中间件测试
 *
 * 测试认证中间件的功能
 */

const jwt = require('jsonwebtoken');
const { User } = require('../../src/models');
const authMiddleware = require('../../src/middlewares/authMiddleware');

describe('认证中间件', () => {
  let testUser;
  let validToken;
  let expiredToken;
  let invalidToken;

  // 模拟请求、响应和下一个中间件
  const mockRequest = (token) => ({
    headers: {
      authorization: token ? `Bearer ${token}` : undefined
    }
  });

  const mockResponse = () => {
    const res = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    return res;
  };

  const mockNext = jest.fn();

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试用户
    testUser = await User.create({
      username: 'authmiddlewareuser',
      password: 'Password123!',
      email: '<EMAIL>',
      phone: '13800000014',
      role: 'basic_user',
      is_active: true
    });

    // 生成有效的JWT令牌
    validToken = jwt.sign(
      { id: testUser.id, role: testUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );

    // 生成已过期的JWT令牌
    expiredToken = jwt.sign(
      { id: testUser.id, role: testUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '0s' }
    );

    // 生成无效的JWT令牌
    invalidToken = 'invalid.token.string';
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await User.destroy({ where: { id: testUser.id } });
  });

  // 在每个测试后重置模拟函数
  afterEach(() => {
    mockNext.mockReset();
  });

  // 测试认证中间件
  describe('认证中间件', () => {
    test('应该在有效令牌时通过认证', async () => {
      const req = mockRequest(validToken);
      const res = mockResponse();

      await authMiddleware(req, res, mockNext);

      // 验证用户信息被添加到请求对象
      expect(req.user).toBeDefined();
      expect(req.user.id).toBe(testUser.id);
      expect(req.user.role).toBe(testUser.role);

      // 验证next被调用
      expect(mockNext).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('应该在没有令牌时拒绝认证', async () => {
      const req = mockRequest();
      const res = mockResponse();

      await authMiddleware(req, res, mockNext);

      // 验证响应状态和消息
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('未提供认证令牌')
        })
      );

      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该在令牌格式错误时拒绝认证', async () => {
      const req = mockRequest('invalid-format');
      const res = mockResponse();

      await authMiddleware(req, res, mockNext);

      // 验证响应状态和消息
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('无效的令牌')
        })
      );

      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该在令牌无效时拒绝认证', async () => {
      const req = mockRequest(invalidToken);
      const res = mockResponse();

      await authMiddleware(req, res, mockNext);

      // 验证响应状态和消息
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('无效')
        })
      );

      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该在令牌过期时拒绝认证', async () => {
      // 等待令牌过期
      await new Promise(resolve => setTimeout(resolve, 1000));

      const req = mockRequest(expiredToken);
      const res = mockResponse();

      await authMiddleware(req, res, mockNext);

      // 验证响应状态和消息
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('过期')
        })
      );

      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该在用户不存在时拒绝认证', async () => {
      // 生成包含不存在用户ID的令牌
      const nonExistentToken = jwt.sign(
        { id: 9999, role: 'basic_user' },
        process.env.JWT_SECRET || 'test_secret',
        { expiresIn: '1h' }
      );

      const req = mockRequest(nonExistentToken);
      const res = mockResponse();

      await authMiddleware(req, res, mockNext);

      // 验证响应状态和消息
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('不存在')
        })
      );

      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该在用户被禁用时拒绝认证', async () => {
      // 禁用测试用户
      await testUser.update({ is_active: false });

      const req = mockRequest(validToken);
      const res = mockResponse();

      await authMiddleware(req, res, mockNext);

      // 验证响应状态和消息
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: '用户账号已被禁用'
      });

      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();

      // 恢复用户状态
      await testUser.update({ is_active: true });
    });
  });
});
