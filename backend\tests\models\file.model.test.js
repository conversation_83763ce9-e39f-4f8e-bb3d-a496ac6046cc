/**
 * 文件模型测试
 *
 * 测试文件模型的字段、验证和关联
 */

const { File, User, KnowledgeBase } = require('../../src/models');
const fs = require('fs');
const path = require('path');

describe('文件模型', () => {
  let testUser;
  let testKnowledgeBase;
  let testFile;
  let testFilePath;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    try {
      // 生成随机用户名和邮箱，避免冲突
      const randomSuffix = Math.floor(Math.random() * 10000);
      const username = `fileuser_${randomSuffix}`;
      const email = `fileuser_${randomSuffix}@example.com`;

      // 创建测试用户
      testUser = await User.create({
        username: username,
        password: 'Password123!',
        email: email,
        phone: '13800000016',
        role: 'basic_user',
        is_active: true
      }, { transaction: global.testTransaction });

      // 创建测试知识库
      testKnowledgeBase = await KnowledgeBase.create({
        name: 'Test File Model Knowledge Base',
        description: 'Test knowledge base for file model testing',
        type: 'user',
        creator_id: testUser.id
      }, { transaction: global.testTransaction });

      // 创建测试文件目录
      const uploadsDir = path.join(__dirname, '../../uploads');
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      // 创建测试文件
      testFilePath = path.join(uploadsDir, 'test-model-file.pdf');
      fs.writeFileSync(testFilePath, 'Test file content for model testing');

      // 创建测试文件记录
      testFile = await File.create({
        name: 'test-model-file.pdf',
        original_name: 'test-model-file.pdf',
        path: 'uploads/test-model-file.pdf',
        type: 'pdf',
        mime_type: 'application/pdf',
        size: 1024,
        knowledge_base_id: testKnowledgeBase.id,
        uploader_id: testUser.id,
        status: 'pending',
        summary: 'Test file summary',
        detailed_description: 'Test file content'
      }, { transaction: global.testTransaction });
    } catch (error) {
      console.error('创建测试数据失败:', error);
      throw error;
    }
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    try {
      // 删除测试文件
      if (fs.existsSync(testFilePath)) {
        fs.unlinkSync(testFilePath);
      }

      // 测试事务会自动回滚，不需要手动删除数据
      // 但如果需要确保数据被删除，可以在这里添加删除逻辑
      console.log('测试文件已清理');
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  });

  // 测试文件创建
  describe('文件创建', () => {
    test('应该成功创建文件', async () => {
      const file = await File.findByPk(testFile.id);

      expect(file).toBeDefined();
      expect(file.name).toBe('test-model-file.pdf');
      expect(file.original_name).toBe('test-model-file.pdf');
      expect(file.path).toBe('uploads/test-model-file.pdf');
      expect(file.type).toBe('pdf');
      expect(file.mime_type).toBe('application/pdf');
      expect(file.size).toBe(1024);
      expect(file.knowledge_base_id).toBe(testKnowledgeBase.id);
      expect(file.uploader_id).toBe(testUser.id);
      expect(file.status).toBe('pending');
      expect(file.summary).toBe('Test file summary');
      expect(file.detailed_description).toBe('Test file content');
    });

    test('不应该创建没有名称的文件', async () => {
      try {
        await File.create({
          // 缺少名称
          original_name: 'missing-name.pdf',
          path: 'uploads/missing-name.pdf',
          type: 'pdf',
          mime_type: 'application/pdf',
          size: 1024,
          knowledge_base_id: testKnowledgeBase.id,
          uploader_id: testUser.id,
          status: 'pending'
        }, { transaction: global.testTransaction });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建没有路径的文件', async () => {
      try {
        await File.create({
          name: 'missing-path.pdf',
          original_name: 'missing-path.pdf',
          // 缺少路径
          type: 'pdf',
          mime_type: 'application/pdf',
          size: 1024,
          knowledge_base_id: testKnowledgeBase.id,
          uploader_id: testUser.id,
          status: 'pending'
        }, { transaction: global.testTransaction });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建无效状态的文件', async () => {
      try {
        await File.create({
          name: 'invalid-status.pdf',
          original_name: 'invalid-status.pdf',
          path: 'uploads/invalid-status.pdf',
          type: 'pdf',
          mime_type: 'application/pdf',
          size: 1024,
          knowledge_base_id: testKnowledgeBase.id,
          uploader_id: testUser.id,
          status: 'invalid_status' // 无效的状态
        }, { transaction: global.testTransaction });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  // 测试文件更新
  describe('文件更新', () => {
    test('应该成功更新文件信息', async () => {
      // 直接更新数据库中的记录
      await File.update({
        summary: 'Updated summary',
        detailed_description: 'Updated content',
        status: 'approved'
      }, {
        where: { id: testFile.id },
        transaction: global.testTransaction
      });

      // 重新获取文件记录
      const updatedFile = await File.findByPk(testFile.id, {
        transaction: global.testTransaction
      });

      expect(updatedFile.summary).toBe('Updated summary');
      expect(updatedFile.detailed_description).toBe('Updated content');
      expect(updatedFile.status).toBe('approved');

      // 恢复原始数据
      await File.update({
        summary: 'Test file summary',
        detailed_description: 'Test file content',
        status: 'pending'
      }, {
        where: { id: testFile.id },
        transaction: global.testTransaction
      });
    });
  });

  // 测试文件关联
  describe('文件关联', () => {
    test('文件应该关联到上传者', async () => {
      // 检查文件模型是否有上传者关联
      expect(File.associations).toHaveProperty('uploader');

      // 获取文件的上传者
      const file = await File.findByPk(testFile.id, {
        include: ['uploader']
      });

      expect(file.uploader).toBeDefined();
      expect(file.uploader.id).toBe(testUser.id);
      expect(file.uploader.username).toBe(testUser.username);
    });

    test('文件应该关联到知识库', async () => {
      // 检查文件模型是否有知识库关联
      expect(File.associations).toHaveProperty('knowledgeBase');

      // 获取文件的知识库
      const file = await File.findByPk(testFile.id, {
        include: ['knowledgeBase']
      });

      expect(file.knowledgeBase).toBeDefined();
      expect(file.knowledgeBase.id).toBe(testKnowledgeBase.id);
      expect(file.knowledgeBase.name).toBe('Test File Model Knowledge Base');
    });
  });

  // 测试文件实例方法
  describe('文件实例方法', () => {
    test('getFullPath方法应该返回完整的文件路径', async () => {
      // 假设文件模型有getFullPath方法
      if (typeof testFile.getFullPath === 'function') {
        const fullPath = testFile.getFullPath();
        expect(fullPath).toBe(path.join(process.cwd(), 'uploads/test-model-file.pdf'));
      } else {
        // 如果没有该方法，跳过测试
        console.log('文件模型没有getFullPath方法，跳过测试');
      }
    });

    test('isImage方法应该正确判断文件是否为图片', async () => {
      // 假设文件模型有isImage方法
      if (typeof testFile.isImage === 'function') {
        // PDF不是图片
        expect(testFile.isImage()).toBe(false);

        // 创建图片文件记录
        const imageFile = await File.create({
          name: 'test-image.jpg',
          original_name: 'test-image.jpg',
          path: 'uploads/test-image.jpg',
          type: 'jpg',
          mime_type: 'image/jpeg',
          size: 2048,
          knowledge_base_id: testKnowledgeBase.id,
          uploader_id: testUser.id,
          status: 'pending'
        }, { transaction: global.testTransaction });

        // 图片应该返回true
        expect(imageFile.isImage()).toBe(true);

        // 事务会自动回滚，不需要手动删除
      } else {
        // 如果没有该方法，跳过测试
        console.log('文件模型没有isImage方法，跳过测试');
      }
    });
  });

  // 测试文件类方法
  describe('文件类方法', () => {
    test('findByType方法应该返回指定类型的文件列表', async () => {
      // 假设文件模型有findByType静态方法
      if (typeof File.findByType === 'function') {
        const pdfFiles = await File.findByType('pdf');
        expect(Array.isArray(pdfFiles)).toBe(true);
        expect(pdfFiles.length).toBeGreaterThan(0);

        // 所有返回的文件都应该是PDF类型
        pdfFiles.forEach(file => {
          expect(file.type).toBe('pdf');
        });
      } else {
        // 如果没有该方法，跳过测试
        console.log('文件模型没有findByType方法，跳过测试');
      }
    });

    test('findByStatus方法应该返回指定状态的文件列表', async () => {
      // 假设文件模型有findByStatus静态方法
      if (typeof File.findByStatus === 'function') {
        const pendingFiles = await File.findByStatus('pending');
        expect(Array.isArray(pendingFiles)).toBe(true);

        // 所有返回的文件都应该是pending状态
        pendingFiles.forEach(file => {
          expect(file.status).toBe('pending');
        });
      } else {
        // 如果没有该方法，跳过测试
        console.log('文件模型没有findByStatus方法，跳过测试');
      }
    });
  });
});
