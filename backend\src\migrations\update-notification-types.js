/**
 * 更新通知类型枚举
 * 
 * 添加文件审核相关的通知类型
 */

const { sequelize } = require('../models');
const { QueryTypes } = require('sequelize');

async function updateNotificationTypes() {
  try {
    console.log('开始更新通知类型枚举...');
    
    // 检查数据库类型，不同数据库有不同的修改枚举类型的语法
    const dbType = sequelize.getDialect();
    console.log(`数据库类型: ${dbType}`);
    
    if (dbType === 'mysql' || dbType === 'mariadb') {
      // MySQL/MariaDB 修改枚举类型的SQL
      await sequelize.query(`
        ALTER TABLE notifications 
        MODIFY COLUMN type ENUM('system', 'activity', 'file', 'comment', 'file_review', 'file_review_result') 
        NOT NULL COMMENT '通知类型：系统通知、活动通知、文件通知、评论通知、文件审核通知、文件审核结果通知'
      `);
      console.log('MySQL/MariaDB: 通知类型枚举更新成功');
    } else if (dbType === 'postgres') {
      // PostgreSQL 修改枚举类型的SQL
      await sequelize.query(`
        ALTER TABLE notifications 
        DROP CONSTRAINT IF EXISTS "notifications_type_check";
        
        ALTER TABLE notifications 
        ADD CONSTRAINT "notifications_type_check" 
        CHECK (type IN ('system', 'activity', 'file', 'comment', 'file_review', 'file_review_result'));
      `);
      console.log('PostgreSQL: 通知类型枚举更新成功');
    } else if (dbType === 'sqlite') {
      // SQLite 不支持直接修改列类型，需要创建新表并迁移数据
      console.log('SQLite: 开始重建通知表...');
      
      // 1. 创建临时表
      await sequelize.query(`
        CREATE TABLE notifications_new (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          title VARCHAR(100) NOT NULL,
          content TEXT NOT NULL,
          type VARCHAR(50) NOT NULL CHECK (type IN ('system', 'activity', 'file', 'comment', 'file_review', 'file_review_result')),
          is_read BOOLEAN DEFAULT 0,
          read_time DATETIME,
          related_id INTEGER,
          related_type VARCHAR(50),
          createdAt DATETIME NOT NULL,
          updatedAt DATETIME NOT NULL,
          FOREIGN KEY (user_id) REFERENCES users (id)
        );
      `);
      
      // 2. 复制数据
      await sequelize.query(`
        INSERT INTO notifications_new 
        SELECT * FROM notifications;
      `);
      
      // 3. 删除旧表
      await sequelize.query(`
        DROP TABLE notifications;
      `);
      
      // 4. 重命名新表
      await sequelize.query(`
        ALTER TABLE notifications_new RENAME TO notifications;
      `);
      
      console.log('SQLite: 通知表重建成功');
    } else {
      console.error(`不支持的数据库类型: ${dbType}`);
      return false;
    }
    
    console.log('通知类型枚举更新完成');
    return true;
  } catch (error) {
    console.error('更新通知类型枚举失败:', error);
    return false;
  }
}

// 执行迁移
updateNotificationTypes()
  .then(success => {
    if (success) {
      console.log('迁移成功');
      process.exit(0);
    } else {
      console.error('迁移失败');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('迁移出错:', error);
    process.exit(1);
  });
