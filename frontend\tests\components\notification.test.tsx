/**
 * 通知组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { NotificationList } from '@/components/notification/NotificationList'
import { NotificationItem } from '@/components/notification/NotificationItem'
import { mockFetch, resetMockFetch } from '../test-utils'

// 模拟通知数据
const mockNotifications = [
  {
    id: 1,
    title: '系统通知',
    content: '欢迎使用和富家族研究平台',
    type: 'system',
    status: 'unread',
    created_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 2,
    title: '评论通知',
    content: '您的评论已被审核通过',
    type: 'comment',
    status: 'read',
    created_at: '2023-01-02T00:00:00Z'
  },
  {
    id: 3,
    title: '文件通知',
    content: '您上传的文件已审核通过',
    type: 'file',
    status: 'unread',
    created_at: '2023-01-03T00:00:00Z'
  }
]

describe('通知组件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch()
    jest.clearAllMocks()
  })

  // 测试通知列表组件
  describe('NotificationList组件', () => {
    test('应该正确渲染通知列表', () => {
      render(
        <NotificationList
          notifications={mockNotifications}
          isLoading={false}
          onRead={jest.fn()}
          onDelete={jest.fn()}
          onReadAll={jest.fn()}
        />
      )
      
      // 验证通知标题
      expect(screen.getByText('系统通知')).toBeInTheDocument()
      expect(screen.getByText('评论通知')).toBeInTheDocument()
      expect(screen.getByText('文件通知')).toBeInTheDocument()
      
      // 验证通知内容
      expect(screen.getByText('欢迎使用和富家族研究平台')).toBeInTheDocument()
      expect(screen.getByText('您的评论已被审核通过')).toBeInTheDocument()
      expect(screen.getByText('您上传的文件已审核通过')).toBeInTheDocument()
      
      // 验证全部已读按钮
      expect(screen.getByText('全部已读')).toBeInTheDocument()
    })

    test('应该显示加载状态', () => {
      render(
        <NotificationList
          notifications={[]}
          isLoading={true}
          onRead={jest.fn()}
          onDelete={jest.fn()}
          onReadAll={jest.fn()}
        />
      )
      
      expect(screen.getByText('加载中...')).toBeInTheDocument()
    })

    test('应该显示空通知提示', () => {
      render(
        <NotificationList
          notifications={[]}
          isLoading={false}
          onRead={jest.fn()}
          onDelete={jest.fn()}
          onReadAll={jest.fn()}
        />
      )
      
      expect(screen.getByText('暂无通知')).toBeInTheDocument()
    })

    test('应该调用全部已读函数', () => {
      const handleReadAll = jest.fn()
      
      render(
        <NotificationList
          notifications={mockNotifications}
          isLoading={false}
          onRead={jest.fn()}
          onDelete={jest.fn()}
          onReadAll={handleReadAll}
        />
      )
      
      // 点击全部已读按钮
      const readAllButton = screen.getByText('全部已读')
      fireEvent.click(readAllButton)
      
      // 验证全部已读函数被调用
      expect(handleReadAll).toHaveBeenCalled()
    })

    test('应该根据状态筛选通知', () => {
      render(
        <NotificationList
          notifications={mockNotifications}
          isLoading={false}
          onRead={jest.fn()}
          onDelete={jest.fn()}
          onReadAll={jest.fn()}
        />
      )
      
      // 默认应该显示所有通知
      expect(screen.getByText('系统通知')).toBeInTheDocument()
      expect(screen.getByText('评论通知')).toBeInTheDocument()
      expect(screen.getByText('文件通知')).toBeInTheDocument()
      
      // 切换到未读通知
      const unreadTab = screen.getByText('未读')
      fireEvent.click(unreadTab)
      
      // 应该只显示未读通知
      expect(screen.getByText('系统通知')).toBeInTheDocument()
      expect(screen.getByText('文件通知')).toBeInTheDocument()
      expect(screen.queryByText('评论通知')).not.toBeInTheDocument()
      
      // 切换到已读通知
      const readTab = screen.getByText('已读')
      fireEvent.click(readTab)
      
      // 应该只显示已读通知
      expect(screen.queryByText('系统通知')).not.toBeInTheDocument()
      expect(screen.queryByText('文件通知')).not.toBeInTheDocument()
      expect(screen.getByText('评论通知')).toBeInTheDocument()
    })
  })

  // 测试通知项组件
  describe('NotificationItem组件', () => {
    test('应该正确渲染未读通知', () => {
      render(
        <NotificationItem
          notification={mockNotifications[0]}
          onRead={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      // 验证通知内容
      expect(screen.getByText('系统通知')).toBeInTheDocument()
      expect(screen.getByText('欢迎使用和富家族研究平台')).toBeInTheDocument()
      
      // 验证未读标记
      const unreadIndicator = screen.getByTestId('unread-indicator')
      expect(unreadIndicator).toBeInTheDocument()
      
      // 验证操作按钮
      expect(screen.getByText('标记为已读')).toBeInTheDocument()
      expect(screen.getByText('删除')).toBeInTheDocument()
    })

    test('应该正确渲染已读通知', () => {
      render(
        <NotificationItem
          notification={mockNotifications[1]}
          onRead={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      // 验证通知内容
      expect(screen.getByText('评论通知')).toBeInTheDocument()
      expect(screen.getByText('您的评论已被审核通过')).toBeInTheDocument()
      
      // 验证没有未读标记
      expect(screen.queryByTestId('unread-indicator')).not.toBeInTheDocument()
      
      // 验证操作按钮
      expect(screen.queryByText('标记为已读')).not.toBeInTheDocument() // 已读通知没有此按钮
      expect(screen.getByText('删除')).toBeInTheDocument()
    })

    test('应该调用标记为已读函数', () => {
      const handleRead = jest.fn()
      
      render(
        <NotificationItem
          notification={mockNotifications[0]}
          onRead={handleRead}
          onDelete={jest.fn()}
        />
      )
      
      // 点击标记为已读按钮
      const readButton = screen.getByText('标记为已读')
      fireEvent.click(readButton)
      
      // 验证标记为已读函数被调用
      expect(handleRead).toHaveBeenCalledWith(mockNotifications[0].id)
    })

    test('应该调用删除函数', () => {
      const handleDelete = jest.fn()
      
      render(
        <NotificationItem
          notification={mockNotifications[0]}
          onRead={jest.fn()}
          onDelete={handleDelete}
        />
      )
      
      // 点击删除按钮
      const deleteButton = screen.getByText('删除')
      fireEvent.click(deleteButton)
      
      // 验证删除函数被调用
      expect(handleDelete).toHaveBeenCalledWith(mockNotifications[0].id)
    })

    test('应该显示通知时间', () => {
      render(
        <NotificationItem
          notification={mockNotifications[0]}
          onRead={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      // 验证时间显示
      const timeElement = screen.getByText((content) => {
        return content.includes('2023') && content.includes('01') && content.includes('01');
      });
      expect(timeElement).toBeInTheDocument()
    })

    test('应该根据通知类型显示不同图标', () => {
      const { rerender } = render(
        <NotificationItem
          notification={mockNotifications[0]} // 系统通知
          onRead={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      // 验证系统通知图标
      expect(screen.getByTestId('system-icon')).toBeInTheDocument()
      
      // 重新渲染评论通知
      rerender(
        <NotificationItem
          notification={mockNotifications[1]} // 评论通知
          onRead={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      // 验证评论通知图标
      expect(screen.getByTestId('comment-icon')).toBeInTheDocument()
      
      // 重新渲染文件通知
      rerender(
        <NotificationItem
          notification={mockNotifications[2]} // 文件通知
          onRead={jest.fn()}
          onDelete={jest.fn()}
        />
      )
      
      // 验证文件通知图标
      expect(screen.getByTestId('file-icon')).toBeInTheDocument()
    })
  })
});
