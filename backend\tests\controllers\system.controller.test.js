/**
 * 系统控制器测试
 * 
 * 测试系统配置相关的API端点和业务逻辑
 */

const request = require('supertest');
const app = require('../../src/app');
const { User, SystemConfig } = require('../../src/models');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

describe('系统控制器', () => {
  let testUser;
  let adminUser;
  let systemConfig;
  let userToken;
  let adminToken;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试用户
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash('testpassword', salt);
    
    testUser = await User.create({
      username: 'systemuser',
      password: passwordHash,
      email: '<EMAIL>',
      phone: '13800000006',
      role: 'basic_user',
      is_active: true
    });

    // 创建管理员用户
    const adminPasswordHash = await bcrypt.hash('adminpassword', salt);
    
    adminUser = await User.create({
      username: 'systemadmin',
      password: adminPasswordHash,
      email: '<EMAIL>',
      phone: '13900000006',
      role: 'admin',
      is_active: true
    });

    // 确保系统配置存在
    systemConfig = await SystemConfig.findOne({ where: { id: 1 } });
    if (!systemConfig) {
      systemConfig = await SystemConfig.create({
        id: 1,
        password_min_length: 8,
        password_require_uppercase: true,
        password_require_lowercase: true,
        password_require_number: true,
        password_require_special: true,
        max_login_attempts: 5,
        lockout_duration: 30,
        session_timeout: 60,
        file_upload_max_size: 10,
        allowed_file_types: 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif',
        system_name: '和富家族研究平台',
        system_logo: '/logo.png',
        system_description: '和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。'
      });
    }

    // 生成测试用的JWT令牌
    userToken = jwt.sign(
      { id: testUser.id, role: testUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );

    adminToken = jwt.sign(
      { id: adminUser.id, role: adminUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await User.destroy({ where: { id: testUser.id } });
    await User.destroy({ where: { id: adminUser.id } });
  });

  // 测试获取系统配置
  describe('获取系统配置', () => {
    test('管理员应该能获取系统配置', async () => {
      const response = await request(app)
        .get('/api/system/config')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', 1);
      expect(response.body.data).toHaveProperty('system_name', '和富家族研究平台');
      expect(response.body.data).toHaveProperty('password_min_length');
      expect(response.body.data).toHaveProperty('allowed_file_types');
    });

    test('普通用户不应该能获取系统配置', async () => {
      const response = await request(app)
        .get('/api/system/config')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/system/config');
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('未授权');
    });
  });

  // 测试更新系统配置
  describe('更新系统配置', () => {
    test('管理员应该能更新系统配置', async () => {
      const response = await request(app)
        .put('/api/system/config')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          system_name: '更新后的系统名称',
          system_description: '更新后的系统描述',
          password_min_length: 10
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '系统配置更新成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('system_name', '更新后的系统名称');
      expect(response.body.data).toHaveProperty('system_description', '更新后的系统描述');
      expect(response.body.data).toHaveProperty('password_min_length', 10);
      
      // 恢复原始数据
      await systemConfig.update({
        system_name: '和富家族研究平台',
        system_description: '和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。',
        password_min_length: 8
      });
    });

    test('普通用户不应该能更新系统配置', async () => {
      const response = await request(app)
        .put('/api/system/config')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          system_name: '未授权的更新',
          system_description: '这不应该生效'
        });
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test('应该验证系统配置数据', async () => {
      const response = await request(app)
        .put('/api/system/config')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          password_min_length: 0, // 无效的密码长度
          password_require_uppercase: 'invalid' // 应该是布尔值
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('验证失败');
    });
  });

  // 测试获取系统统计信息
  describe('获取系统统计信息', () => {
    test('管理员应该能获取系统统计信息', async () => {
      const response = await request(app)
        .get('/api/system/stats')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('user_count');
      expect(response.body.data).toHaveProperty('knowledge_base_count');
      expect(response.body.data).toHaveProperty('file_count');
      expect(response.body.data).toHaveProperty('activity_count');
    });

    test('普通用户不应该能获取系统统计信息', async () => {
      const response = await request(app)
        .get('/api/system/stats')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });
  });
});
