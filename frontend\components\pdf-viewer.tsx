"use client"

import { useState } from "react"
import { pdfjs } from "react-pdf"
import { FileDown, ExternalLink } from "lucide-react"
import { Button } from "@/components/ui/button"

// 使用 cdnjs 而不是 unpkg，cdnjs 通常更稳定
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.8.69/pdf.worker.min.js`

interface PDFViewerProps {
  pdfUrl: string
  materialTitle: string
  onError?: () => void
}

export default function PDFViewer({ pdfUrl, materialTitle, onError }: PDFViewerProps) {
  const [numPages, setNumPages] = useState<number | null>(null)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [scale, setScale] = useState<number>(1.0)
  const [rotation, setRotation] = useState<number>(0)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [loadError, setLoadError] = useState<boolean>(false)

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages)
    setIsLoading(false)
    setLoadError(false)
  }

  function onDocumentLoadError(error: Error) {
    console.error("PDF loading error:", error)
    setIsLoading(false)
    setLoadError(true)
    if (onError) {
      onError()
    }
  }

  function changePage(offset: number) {
    setPageNumber((prevPageNumber) => {
      const newPageNumber = prevPageNumber + offset
      return newPageNumber >= 1 && newPageNumber <= (numPages || 1) ? newPageNumber : prevPageNumber
    })
  }

  function previousPage() {
    changePage(-1)
  }

  function nextPage() {
    changePage(1)
  }

  function zoomIn() {
    setScale((prevScale) => Math.min(prevScale + 0.2, 2.0))
  }

  function zoomOut() {
    setScale((prevScale) => Math.max(prevScale - 0.2, 0.6))
  }

  function rotate() {
    setRotation((prevRotation) => (prevRotation + 90) % 360)
  }

  // 直接显示下载和在新窗口打开选项，避免PDF.js加载问题
  return (
    <div className="flex flex-col items-center">
      <div className="bg-gray-800 text-white w-full p-3 flex justify-between items-center rounded-t-lg">
        <div className="text-sm font-medium truncate max-w-[200px]">{materialTitle}</div>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.open(pdfUrl, "_blank")}
            className="text-white hover:bg-gray-700"
          >
            <ExternalLink className="h-4 w-4 mr-1" />
            在新窗口打开
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              const link = document.createElement("a")
              link.href = pdfUrl
              link.download = materialTitle || "document.pdf"
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
            }}
            className="text-white hover:bg-gray-700"
          >
            <FileDown className="h-4 w-4 mr-1" />
            下载
          </Button>
        </div>
      </div>

      <div className="border border-gray-200 p-8 w-full flex flex-col items-center justify-center bg-gray-100 min-h-[300px]">
        <div className="text-amber-500 mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="48"
            height="48"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">PDF查看器无法加载</h3>
        <p className="text-gray-600 mb-6 text-center max-w-md">
          由于网络或浏览器限制，我们无法在浏览器中直接显示此PDF文件。您可以使用以下选项查看文档：
        </p>
        <div className="flex gap-4">
          <Button onClick={() => window.open(pdfUrl, "_blank")} className="flex items-center gap-2">
            <ExternalLink className="h-4 w-4" />
            在新窗口打开
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              const link = document.createElement("a")
              link.href = pdfUrl
              link.download = materialTitle || "document.pdf"
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
            }}
            className="flex items-center gap-2"
          >
            <FileDown className="h-4 w-4" />
            下载PDF
          </Button>
        </div>
      </div>
    </div>
  )
}
