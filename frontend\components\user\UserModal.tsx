/**
 * 用户编辑模态框组件
 * 
 * 用于添加或编辑用户信息
 */

import React from "react"
import { X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { UserModalProps, UserType } from "./types"

/**
 * 用户编辑模态框组件
 * @param props 组件属性
 */
export const UserModal: React.FC<UserModalProps> = ({
  isOpen,
  onClose,
  onSave,
  user,
  roles
}) => {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg w-full max-w-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">
            {user ? "编辑用户" : "添加用户"}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="h-6 w-6" />
          </button>
        </div>

        <form
          onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)
            const userData: UserType = {
              id: user?.id || "",
              name: formData.get("name") as string,
              email: formData.get("email") as string,
              phone: formData.get("phone") as string,
              roleId: formData.get("roleId") as string,
              roleName: roles.find((role) => role.id === formData.get("roleId"))?.name || "",
              status: formData.get("status") as "正常" | "待审核" | "已禁用" | "待激活",
              createdAt: user?.createdAt || "",
            }
            onSave(userData)
          }}
          className="space-y-4"
        >
          <div>
            <Label htmlFor="name">用户名</Label>
            <Input
              id="name"
              name="name"
              defaultValue={user?.name || ""}
              required
            />
          </div>

          <div>
            <Label htmlFor="email">电子邮件</Label>
            <Input
              id="email"
              name="email"
              type="email"
              defaultValue={user?.email || ""}
              required
            />
          </div>

          <div>
            <Label htmlFor="phone">手机号码</Label>
            <Input
              id="phone"
              name="phone"
              defaultValue={user?.phone || ""}
              required
            />
          </div>

          <div>
            <Label htmlFor="roleId">角色</Label>
            <select
              id="roleId"
              name="roleId"
              defaultValue={user?.roleId || ""}
              className="w-full border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
              required
            >
              <option value="" disabled>
                选择角色
              </option>
              {roles.map((role) => (
                <option key={role.id} value={role.id}>
                  {role.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <Label htmlFor="status">状态</Label>
            <select
              id="status"
              name="status"
              defaultValue={user?.status || "正常"}
              className="w-full border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
              required
            >
              <option value="正常">正常</option>
              <option value="待审核">待审核</option>
              <option value="已禁用">已禁用</option>
              <option value="待激活">待激活</option>
            </select>
          </div>

          <div className="flex justify-end gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              取消
            </Button>
            <Button
              type="submit"
              className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
            >
              保存
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
