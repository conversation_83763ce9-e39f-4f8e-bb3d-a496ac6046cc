/**
 * 知识库控制器
 *
 * 处理知识库相关的业务逻辑，如创建、查询、更新、删除知识库等
 */

const { KnowledgeBase, User, KnowledgeBaseAccess, File } = require('../models');
const { Op } = require('sequelize');

/**
 * 创建知识库
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createKnowledgeBase = async (req, res) => {
  try {
    const { name, description, type, contact_name, contact_phone } = req.body;

    // 验证知识库类型
    if (type === 'system' && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '只有管理员可以创建系统知识库'
      });
    }

    // 创建知识库
    const knowledgeBase = await KnowledgeBase.create({
      name,
      description,
      type: type || 'user', // 默认为用户知识库
      creator_id: req.user.id,
      contact_name,
      contact_phone,
      storage_size: 0,
      file_count: 0
    });

    // 如果是用户知识库，自动为创建者添加管理权限
    if (type !== 'system') {
      await KnowledgeBaseAccess.create({
        knowledge_base_id: knowledgeBase.id,
        user_id: req.user.id,
        access_type: 'admin',
        granted_by: req.user.id
      });
    }

    res.status(201).json({
      success: true,
      message: '知识库创建成功',
      data: knowledgeBase
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建知识库失败',
      error: error.message
    });
  }
};

/**
 * 获取知识库列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getKnowledgeBases = async (req, res) => {
  try {
    const { type, search } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereConditions = {};

    // 根据类型筛选
    if (type) {
      whereConditions.type = type;
    }

    // 根据名称或描述搜索
    if (search) {
      whereConditions[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    // 所有用户都能看到所有知识库，但需要标记是否有访问权限
    // 获取用户有权限的知识库ID
    let accessibleIds = [];
    if (req.user.role !== 'admin') {
      const userAccess = await KnowledgeBaseAccess.findAll({
        where: { user_id: req.user.id },
        attributes: ['knowledge_base_id']
      });

      accessibleIds = userAccess.map(access => access.knowledge_base_id);
    }

    // 查询知识库
    const { count, rows: knowledgeBases } = await KnowledgeBase.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    // 计算总页数
    const totalPages = Math.ceil(count / limit);

    // 为每个知识库添加权限标记和获取已批准的文件数量
    const knowledgeBasesWithAccess = await Promise.all(knowledgeBases.map(async kb => {
      const hasAccess = req.user.role === 'admin' ||
                        kb.type === 'system' ||
                        kb.creator_id === req.user.id ||
                        accessibleIds.includes(kb.id);

      // 获取已批准的文件数量
      const approvedFileCount = await File.count({
        where: {
          knowledge_base_id: kb.id,
          status: 'approved'
        }
      });

      return {
        ...kb.toJSON(),
        hasAccess,
        file_count: approvedFileCount // 覆盖原有的file_count，只包含已批准的文件
      };
    }));

    res.status(200).json({
      success: true,
      data: {
        knowledgeBases: knowledgeBasesWithAccess,
        pagination: {
          total: count,
          page,
          limit,
          totalPages
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取知识库列表失败',
      error: error.message
    });
  }
};

/**
 * 根据ID获取知识库详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getKnowledgeBaseById = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    if (!knowledgeBase) {
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    // 检查访问权限
    let hasAccess = false;

    // 管理员可以访问任何知识库
    if (req.user.role === 'admin') {
      hasAccess = true;
    }
    // 系统知识库所有人可以访问
    else if (knowledgeBase.type === 'system') {
      hasAccess = true;
    }
    // 创建者可以访问自己的知识库
    else if (knowledgeBase.creator_id === req.user.id) {
      hasAccess = true;
    }
    // 检查是否有访问权限
    else {
      const access = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id: id,
          user_id: req.user.id
        }
      });

      if (access) {
        hasAccess = true;
      }
    }

    // 获取已批准的文件数量
    const fileCount = await File.count({
      where: {
        knowledge_base_id: id,
        status: 'approved'
      }
    });

    // 获取知识库访问权限列表
    const accessList = await KnowledgeBaseAccess.findAll({
      where: { knowledge_base_id: id },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.status(200).json({
      success: true,
      data: {
        ...knowledgeBase.toJSON(),
        file_count: fileCount,
        access_list: accessList,
        hasAccess: hasAccess
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取知识库详情失败',
      error: error.message
    });
  }
};

/**
 * 更新知识库
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateKnowledgeBase = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, contact_name, contact_phone } = req.body;

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(id);

    if (!knowledgeBase) {
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    // 检查更新权限
    if (req.user.role !== 'admin') {
      // 系统知识库只有管理员可以更新
      if (knowledgeBase.type === 'system') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以更新系统知识库'
        });
      }

      // 用户知识库需要检查是否有管理权限
      const hasAccess = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id: id,
          user_id: req.user.id,
          access_type: 'admin'
        }
      });

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: '您没有权限更新该知识库'
        });
      }
    }

    // 更新知识库
    await knowledgeBase.update({
      name: name || knowledgeBase.name,
      description: description !== undefined ? description : knowledgeBase.description,
      contact_name: contact_name !== undefined ? contact_name : knowledgeBase.contact_name,
      contact_phone: contact_phone !== undefined ? contact_phone : knowledgeBase.contact_phone
    });

    res.status(200).json({
      success: true,
      message: '知识库更新成功',
      data: knowledgeBase
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新知识库失败',
      error: error.message
    });
  }
};

/**
 * 删除知识库
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteKnowledgeBase = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(id);

    if (!knowledgeBase) {
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    // 检查删除权限
    if (req.user.role !== 'admin') {
      // 系统知识库只有管理员可以删除
      if (knowledgeBase.type === 'system') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以删除系统知识库'
        });
      }

      // 用户知识库需要检查是否有管理权限
      const hasAccess = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id: id,
          user_id: req.user.id,
          access_type: 'admin'
        }
      });

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: '您没有权限删除该知识库'
        });
      }
    }

    // 检查知识库是否有文件
    const fileCount = await File.count({
      where: { knowledge_base_id: id }
    });

    if (fileCount > 0) {
      return res.status(400).json({
        success: false,
        message: '知识库中还有文件，请先删除所有文件'
      });
    }

    // 删除知识库访问权限
    await KnowledgeBaseAccess.destroy({
      where: { knowledge_base_id: id }
    });

    // 删除知识库
    await knowledgeBase.destroy();

    res.status(200).json({
      success: true,
      message: '知识库删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除知识库失败',
      error: error.message
    });
  }
};

/**
 * 获取知识库访问权限列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getKnowledgeBaseAccess = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(id);

    if (!knowledgeBase) {
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    // 检查访问权限
    if (req.user.role !== 'admin') {
      // 系统知识库只有管理员可以查看访问权限
      if (knowledgeBase.type === 'system') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以查看系统知识库的访问权限'
        });
      }

      // 用户知识库需要检查是否有管理权限
      const hasAccess = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id: id,
          user_id: req.user.id,
          access_type: 'admin'
        }
      });

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: '您没有权限查看该知识库的访问权限'
        });
      }
    }

    // 获取知识库访问权限列表
    const accessList = await KnowledgeBaseAccess.findAll({
      where: { knowledge_base_id: id },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'grantor',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.status(200).json({
      success: true,
      data: accessList
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取知识库访问权限列表失败',
      error: error.message
    });
  }
};

/**
 * 添加知识库访问权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.addKnowledgeBaseAccess = async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id, access_type } = req.body;

    // 验证访问类型
    if (!['read', 'write', 'admin'].includes(access_type)) {
      return res.status(400).json({
        success: false,
        message: '无效的访问类型，必须是 read、write 或 admin'
      });
    }

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(id);

    if (!knowledgeBase) {
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    // 检查添加权限的权限
    if (req.user.role !== 'admin') {
      // 系统知识库只有管理员可以添加访问权限
      if (knowledgeBase.type === 'system') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以添加系统知识库的访问权限'
        });
      }

      // 用户知识库需要检查是否有管理权限
      const hasAccess = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id: id,
          user_id: req.user.id,
          access_type: 'admin'
        }
      });

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: '您没有权限添加该知识库的访问权限'
        });
      }
    }

    // 检查用户是否存在
    const user = await User.findByPk(user_id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查是否已有访问权限
    const existingAccess = await KnowledgeBaseAccess.findOne({
      where: {
        knowledge_base_id: id,
        user_id
      }
    });

    if (existingAccess) {
      return res.status(400).json({
        success: false,
        message: '该用户已有访问权限'
      });
    }

    // 添加访问权限
    const access = await KnowledgeBaseAccess.create({
      knowledge_base_id: id,
      user_id,
      access_type,
      granted_by: req.user.id
    });

    // 获取完整的访问权限信息
    const fullAccess = await KnowledgeBaseAccess.findByPk(access.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'grantor',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: '知识库访问权限添加成功',
      data: fullAccess
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '添加知识库访问权限失败',
      error: error.message
    });
  }
};

/**
 * 更新知识库访问权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateKnowledgeBaseAccess = async (req, res) => {
  try {
    const { id, userId } = req.params;
    const { access_type } = req.body;

    // 验证访问类型
    if (!['read', 'write', 'admin'].includes(access_type)) {
      return res.status(400).json({
        success: false,
        message: '无效的访问类型，必须是 read、write 或 admin'
      });
    }

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(id);

    if (!knowledgeBase) {
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    // 检查更新权限的权限
    if (req.user.role !== 'admin') {
      // 系统知识库只有管理员可以更新访问权限
      if (knowledgeBase.type === 'system') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以更新系统知识库的访问权限'
        });
      }

      // 用户知识库需要检查是否有管理权限
      const hasAccess = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id: id,
          user_id: req.user.id,
          access_type: 'admin'
        }
      });

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: '您没有权限更新该知识库的访问权限'
        });
      }
    }

    // 查找访问权限
    const access = await KnowledgeBaseAccess.findOne({
      where: {
        knowledge_base_id: id,
        user_id: userId
      }
    });

    if (!access) {
      return res.status(404).json({
        success: false,
        message: '访问权限不存在'
      });
    }

    // 不允许更新创建者的权限
    if (knowledgeBase.creator_id.toString() === userId.toString()) {
      return res.status(403).json({
        success: false,
        message: '不能更新知识库创建者的权限'
      });
    }

    // 更新访问权限
    await access.update({
      access_type,
      granted_by: req.user.id
    });

    // 获取完整的访问权限信息
    const fullAccess = await KnowledgeBaseAccess.findByPk(access.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'grantor',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: '知识库访问权限更新成功',
      data: fullAccess
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新知识库访问权限失败',
      error: error.message
    });
  }
};

/**
 * 删除知识库访问权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.removeKnowledgeBaseAccess = async (req, res) => {
  try {
    const { id, userId } = req.params;

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(id);

    if (!knowledgeBase) {
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    // 检查删除权限的权限
    if (req.user.role !== 'admin') {
      // 系统知识库只有管理员可以删除访问权限
      if (knowledgeBase.type === 'system') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以删除系统知识库的访问权限'
        });
      }

      // 用户知识库需要检查是否有管理权限
      const hasAccess = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id: id,
          user_id: req.user.id,
          access_type: 'admin'
        }
      });

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: '您没有权限删除该知识库的访问权限'
        });
      }
    }

    // 查找访问权限
    const access = await KnowledgeBaseAccess.findOne({
      where: {
        knowledge_base_id: id,
        user_id: userId
      }
    });

    if (!access) {
      return res.status(404).json({
        success: false,
        message: '访问权限不存在'
      });
    }

    // 不允许删除创建者的权限
    if (knowledgeBase.creator_id.toString() === userId.toString()) {
      return res.status(403).json({
        success: false,
        message: '不能删除知识库创建者的权限'
      });
    }

    // 删除访问权限
    await access.destroy();

    res.status(200).json({
      success: true,
      message: '知识库访问权限删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除知识库访问权限失败',
      error: error.message
    });
  }
};
