"use client"

import { useState, useRef, useEffect } from "react"
import { Send, RefreshCw, Database, FileText, ExternalLink, Check, Download } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DataQueryAssistantConfig } from "@/types/ai-assistants"
import { useModal } from "@/components/modal-provider"
import { cn } from "@/lib/utils"
import { SimpleTypewriter } from "@/components/ui/simple-typewriter"
import { HtmlTypewriter } from "@/components/ui/html-typewriter"
import { DataQueryStreamTypewriter } from "./DataQueryStreamTypewriter"

// 消息类型
interface Message {
  role: "user" | "assistant"
  content: string
  materials?: Material[]
}

// 材料类型
interface Material {
  id: number
  title: string
  description: string
  date: string
  source: string
  sourceType: "internal" | "external"
  sourceOrg: string
  contentType: "text" | "image" | "pdf"
  url?: string
  uploadTime?: string
  pageInfo?: string
  externalUrl?: string
  imageUrls?: string[]
  knowledgeBaseId?: string
  fileSize?: string
  fileType?: string
}

// 组件属性
interface DataQueryAssistantProps extends DataQueryAssistantConfig {
  // dataSourceId 已经在 DataQueryAssistantConfig 中定义
}

/**
 * 数据查询助手组件
 *
 * 用于数据查询页面，帮助用户查询和分析数据
 * 在系统管理的AI管理界面中，type="data-query"类型的AI助手会显示在此组件中
 *
 * @param name - 助手名称
 * @param dataSourceId - 关联的数据源ID
 * @param assistantId - 关联到系统中的AI助手ID（在实际应用中使用）
 * @param apiKey - API密钥（在实际应用中使用）
 * @param apiEndpoint - API端点（在实际应用中使用）
 */
export function DataQueryAssistant({
  name,
  description,
  dataSourceId,
  apiKey,
  apiEndpoint,
  appId,
  appCode,
  initialMessage
}: DataQueryAssistantProps & { initialMessage?: string, appCode?: string }) {
  // 检查是否有初始对话内容
  const defaultMessage = "您好，我是您的智能学术助手。我可以帮您检索相关资料，提供专业的研究支持和学术建议。请问您需要查找什么资料？"

  const [messages, setMessages] = useState<Message[]>(
    initialMessage || defaultMessage ? [
      {
        role: "assistant",
        content: initialMessage || defaultMessage,
      },
    ] : []
  )
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [isActive, setIsActive] = useState(false) // 初始状态设为非活跃
  // 不再需要模态框显示状态，使用统一的模态框系统
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState<string[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { openModal, closeModal } = useModal()

  // 打字机效果相关状态
  // 不再需要传统打字机相关的状态
  const [conversationId, setConversationId] = useState<string | null>(null)

  // 流式打字机
  const {
    textComponent: streamingText,
    text: streamingContent,
    isTyping: isStreaming,
    conversationId: streamConversationId,
    startStreaming
  } = DataQueryStreamTypewriter({
    className: "prose prose-sm max-w-none",
    onFirstToken: () => {
      // 收到第一个token时，停止显示思考中动画，开始显示打字机效果
      console.log('收到第一个token，停止显示思考中动画')
      // 设置isLoading为false，停止显示思考中动画
      setIsLoading(false)
      // 确保isStreaming状态正确，这样打字机效果才会显示
      console.log('开始显示打字机效果')
    },
    onComplete: (finalText, finalConversationId) => {
      console.log('流式打字机完成，更新聊天记录', { finalText, finalConversationId })

      // 如果有会话ID，保存它
      if (finalConversationId) {
        setConversationId(finalConversationId)
      }

      // 提取纯文本内容，移除HTML标签
      let cleanedText = finalText;

      // 如果包含思考过程，提取出正式回复部分
      if (finalText.includes('<details') && finalText.includes('</details>')) {
        const detailsEndIndex = finalText.indexOf('</details>') + 10;
        cleanedText = finalText.substring(detailsEndIndex).trim();
        console.log('提取出正式回复部分，长度:', cleanedText.length);
      }

      // 移除所有HTML标签，只保留纯文本
      const textOnly = cleanedText.replace(/<[^>]*>/g, '');
      console.log('移除HTML标签后的纯文本，长度:', textOnly.length);

      // 更新聊天记录，确保不会重复显示消息
      setMessages(prev => {
        // 检查是否已经有一个空的助手消息（由handleSendMessage添加）
        const lastIndex = prev.length - 1
        const hasEmptyAssistantMessage = lastIndex >= 0 &&
                                        prev[lastIndex].role === "assistant" &&
                                        prev[lastIndex].content === ""

        if (hasEmptyAssistantMessage) {
          // 如果有空的助手消息，替换它
          const newMessages = [...prev]
          newMessages[lastIndex] = {
            role: "assistant",
            content: textOnly
          }
          return newMessages
        } else {
          // 如果没有空的助手消息，检查最后一条消息是否是助手消息
          if (lastIndex >= 0 && prev[lastIndex].role === "assistant") {
            // 如果最后一条是助手消息，替换它
            const newMessages = [...prev]
            newMessages[lastIndex] = {
              role: "assistant",
              content: textOnly
            }
            return newMessages
          } else {
            // 如果最后一条不是助手消息，添加一条新的
            return [...prev, {
              role: "assistant",
              content: textOnly
            }]
          }
        }
      })

      // 延迟一段时间后再设置isLoading和isActive为false，确保用户能看到完整的打字机效果
      setTimeout(() => {
        setIsLoading(false) // 确保加载状态被重置
        setIsActive(false) // 重置活跃状态，启用输入框
        console.log('流式响应状态结束')
      }, 1000)
    }
  })

  // 检查登录状态
  useEffect(() => {
    const checkLoginStatus = () => {
      const loggedIn = localStorage.getItem("isLoggedIn") === "true"
      setIsLoggedIn(loggedIn)
    }

    checkLoginStatus()
    // 监听登录状态变化
    window.addEventListener("storage", checkLoginStatus)

    return () => {
      window.removeEventListener("storage", checkLoginStatus)
    }
  }, [])

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  // 模拟数据库
  const materialDatabase = [
    {
      id: 1,
      title: "蔡和森自传及生平研究",
      description:
        "本资料收集了蔡和森的自传文稿及相关研究，详细记录了其从湖南求学到法国勤工俭学，再到参与创建中国共产党的完整生平。",
      date: "2023-05-20",
      source: "《蔡和森传记资料汇编》",
      sourceType: "internal",
      sourceOrg: "中央档案馆",
      contentType: "text",
      url: "#document-1",
      uploadTime: "2023-06-15",
      pageInfo: "第23-45页",
      knowledgeBaseId: "kb-1",
      fileSize: "4.2MB",
      fileType: "PDF",
    },
    {
      id: 2,
      title: "李富春早期革命活动研究",
      description:
        "本研究详细记录了李富春从青年时期参加革命到成为国家领导人的完整历程，特别关注其在经济建设方面的贡献和思想。",
      date: "2023-03-15",
      source: "《中共党史人物研究》第三卷",
      sourceType: "internal",
      sourceOrg: "党史研究中心",
      contentType: "text",
      url: "#document-2",
      uploadTime: "2023-04-10",
      pageInfo: "第127-156页",
      knowledgeBaseId: "kb-2",
      fileSize: "3.8MB",
      fileType: "PDF",
    },
    {
      id: 3,
      title: "蔡和森在法国的革命活动档案",
      description:
        "本档案收集了蔡和森在法国勤工俭学期间的相关史料，包括其创办《少年》杂志、组织留法学生和参与建党等重要活动的第一手资料。",
      date: "2022-11-30",
      source: "《中国留法勤工俭学运动史料》",
      sourceType: "internal",
      sourceOrg: "国际关系研究所",
      contentType: "text",
      url: "#document-3",
      uploadTime: "2023-01-05",
      pageInfo: "第78-92页",
      knowledgeBaseId: "kb-1",
      fileSize: "2.7MB",
      fileType: "PDF",
    }
  ]

  // 从API获取知识库数据
  const [knowledgeBases, setKnowledgeBases] = useState<{id: string, name: string, type: "系统" | "用户"}[]>([])
  const [isLoadingKnowledgeBases, setIsLoadingKnowledgeBases] = useState(false)

  // 获取知识库数据
  useEffect(() => {
    const fetchKnowledgeBases = async () => {
      setIsLoadingKnowledgeBases(true)
      try {
        // 获取认证token
        const token = localStorage.getItem('hefamily_token')
        if (!token) {
          console.warn('未找到认证token，无法获取知识库列表')
          // 不抛出错误，而是使用默认数据
          setKnowledgeBases([
            { id: "kb-1", name: "中共党史资料库", type: "系统" as const },
            { id: "kb-2", name: "革命人物研究库", type: "系统" as const },
            { id: "kb-3", name: "个人研究资料", type: "用户" as const }
          ])
          setIsLoadingKnowledgeBases(false)
          return
        }

        // 使用API服务获取知识库列表，并添加认证头
        const response = await fetch('/api/knowledge', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (!response.ok) {
          throw new Error('获取知识库列表失败')
        }

        const data = await response.json()
        if (data.success && data.data && data.data.knowledgeBases) {
          // 转换API返回的数据格式为组件需要的格式
          const formattedKnowledgeBases = data.data.knowledgeBases.map((kb: any) => ({
            id: kb.id.toString(),
            name: kb.name,
            type: kb.type === 'system' ? '系统' : '用户' as const
          }))
          setKnowledgeBases(formattedKnowledgeBases)
        }
      } catch (error) {
        console.error('获取知识库列表失败:', error)
        // 如果API调用失败，使用一些默认数据以确保UI不会崩溃
        setKnowledgeBases([
          { id: "kb-1", name: "中共党史资料库", type: "系统" as const },
          { id: "kb-2", name: "革命人物研究库", type: "系统" as const },
          { id: "kb-3", name: "个人研究资料", type: "用户" as const }
        ])
      } finally {
        setIsLoadingKnowledgeBases(false)
      }
    }

    fetchKnowledgeBases()
  }, [])

  // 查找相关材料
  const findRelevantMaterials = async (query: string) => {
    try {
      // 构建查询参数
      const params = new URLSearchParams()
      if (query) {
        params.append('search', query)
      }

      // 如果有选择知识库，添加知识库ID参数
      if (selectedKnowledgeBases && selectedKnowledgeBases.length > 0) {
        selectedKnowledgeBases.forEach(kb => params.append('knowledge_base_ids[]', kb))
      } else {
        // 如果没有选择知识库，使用所有知识库
        knowledgeBases.forEach(kb => params.append('knowledge_base_ids[]', kb.id))
      }

      // 记录查询参数
      console.log('AI助手文件搜索参数:', {
        query,
        selectedKnowledgeBases,
        params: params.toString()
      })

      // 获取认证token
      const token = localStorage.getItem('hefamily_token')
      if (!token) {
        console.warn('未找到认证token，无法获取文件列表')
        // 不抛出错误，而是返回空结果
        return []
      }

      // 调用API获取文件列表，并添加认证头
      console.log(`AI助手发送API请求: /api/files?${params.toString()}`)
      const response = await fetch(`/api/files?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('获取文件列表失败')
      }

      const data = await response.json()
      if (data.success && data.data && data.data.files) {
        // 转换API返回的数据格式为组件需要的格式
        return data.data.files.map((file: any) => ({
          id: file.id,
          title: file.original_name,
          description: file.description || '无描述',
          date: new Date(file.created_at).toLocaleDateString(),
          source: file.knowledge_base?.name || '未知知识库',
          sourceType: "internal" as const,
          sourceOrg: "和富家族研究平台",
          contentType: "text" as const,
          url: `/api/files/${file.id}/download`,
          uploadTime: new Date(file.created_at).toLocaleDateString(),
          pageInfo: file.page_count ? `共${file.page_count}页` : undefined,
          knowledgeBaseId: file.knowledge_base_id?.toString(),
          fileSize: formatFileSize(file.file_size),
          fileType: file.file_type?.toUpperCase() || 'UNKNOWN'
        }))
      }
      return []
    } catch (error) {
      console.error('获取文件列表失败:', error)
      // 如果API调用失败，返回模拟数据
      return materialDatabase.filter(material =>
        selectedKnowledgeBases.length === 0 ||
        (material.knowledgeBaseId && selectedKnowledgeBases.includes(material.knowledgeBaseId))
      )
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '未知'
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`
  }

  // 不再需要传统打字机效果完成后的回调函数

  // 处理下载材料
  const handleDownloadMaterial = (material: any) => {
    if (material.url) {
      // 创建一个隐藏的a标签来触发下载
      const a = document.createElement('a')
      a.href = material.url
      a.download = material.fileName || 'download'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    } else {
      console.error('材料没有下载URL:', material)
      // 显示错误提示
      setError('无法下载材料，URL不存在')
      // 3秒后自动清除错误提示
      setTimeout(() => setError(null), 3000)
    }
  }

  // 不再需要单独的知识库选择变化处理函数，已整合到模态框确认按钮的点击事件中

  // 打开知识库选择模态框
  const handleOpenKnowledgeBaseModal = () => {
    // 使用统一的模态框系统，而不是直接渲染KnowledgeBaseModal组件
    const modalContent = (
      <div className="p-4">
        <div className="mb-4">
          <div className="text-sm text-gray-500 mb-2">已选择 {selectedKnowledgeBases.length} 个知识库</div>
        </div>

        {isLoadingKnowledgeBases ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1e7a43]"></div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* 系统知识库 */}
            {knowledgeBases.filter((kb) => kb.type === "系统").length > 0 && (
              <div>
                <div className="flex justify-between items-center px-2 py-1 bg-gray-50 rounded-sm mb-2">
                  <div className="text-xs font-semibold text-gray-500">系统知识库</div>
                  <button
                    onClick={() => {
                      const systemIds = knowledgeBases.filter((kb) => kb.type === "系统").map((kb) => kb.id);
                      const allSelected = systemIds.every((id) => selectedKnowledgeBases.includes(id));

                      if (allSelected) {
                        // 如果全部已选，则取消全选
                        setSelectedKnowledgeBases((prev) => prev.filter((id) => !systemIds.includes(id)));
                      } else {
                        // 如果未全选，则全选
                        const newSelectedIds = [...selectedKnowledgeBases];
                        systemIds.forEach((id) => {
                          if (!newSelectedIds.includes(id)) {
                            newSelectedIds.push(id);
                          }
                        });
                        setSelectedKnowledgeBases(newSelectedIds);
                      }
                    }}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    {knowledgeBases.filter((kb) => kb.type === "系统").every((kb) => selectedKnowledgeBases.includes(kb.id))
                      ? "取消全选"
                      : "全选"}
                  </button>
                </div>
                <div className="space-y-1">
                  {knowledgeBases
                    .filter((kb) => kb.type === "系统")
                    .map((kb) => (
                      <div
                        key={kb.id}
                        className={cn(
                          "flex items-center px-2 py-2 text-sm rounded-sm cursor-pointer",
                          selectedKnowledgeBases.includes(kb.id) ? "bg-emerald-50" : "hover:bg-gray-50",
                        )}
                        onClick={() => {
                          if (selectedKnowledgeBases.includes(kb.id)) {
                            setSelectedKnowledgeBases(selectedKnowledgeBases.filter((id) => id !== kb.id));
                          } else {
                            setSelectedKnowledgeBases([...selectedKnowledgeBases, kb.id]);
                          }
                        }}
                      >
                        <div
                          className={cn(
                            "mr-2 h-4 w-4 rounded-sm border flex items-center justify-center",
                            selectedKnowledgeBases.includes(kb.id)
                              ? "border-emerald-500 bg-emerald-500 text-white"
                              : "border-gray-300",
                          )}
                        >
                          {selectedKnowledgeBases.includes(kb.id) && <Check className="h-3 w-3" />}
                        </div>
                        <div className="flex items-center">
                          <Database className="h-3 w-3 mr-2 text-blue-500" />
                          <span className="text-sm">{kb.name}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* 用户知识库 */}
            {knowledgeBases.filter((kb) => kb.type === "用户").length > 0 && (
              <div>
                <div className="flex justify-between items-center px-2 py-1 bg-gray-50 rounded-sm mb-2">
                  <div className="text-xs font-semibold text-gray-500">用户知识库</div>
                  <button
                    onClick={() => {
                      const userIds = knowledgeBases.filter((kb) => kb.type === "用户").map((kb) => kb.id);
                      const allSelected = userIds.every((id) => selectedKnowledgeBases.includes(id));

                      if (allSelected) {
                        // 如果全部已选，则取消全选
                        setSelectedKnowledgeBases((prev) => prev.filter((id) => !userIds.includes(id)));
                      } else {
                        // 如果未全选，则全选
                        const newSelectedIds = [...selectedKnowledgeBases];
                        userIds.forEach((id) => {
                          if (!newSelectedIds.includes(id)) {
                            newSelectedIds.push(id);
                          }
                        });
                        setSelectedKnowledgeBases(newSelectedIds);
                      }
                    }}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    {knowledgeBases.filter((kb) => kb.type === "用户").every((kb) => selectedKnowledgeBases.includes(kb.id))
                      ? "取消全选"
                      : "全选"}
                  </button>
                </div>
                <div className="space-y-1">
                  {knowledgeBases
                    .filter((kb) => kb.type === "用户")
                    .map((kb) => (
                      <div
                        key={kb.id}
                        className={cn(
                          "flex items-center px-2 py-2 text-sm rounded-sm cursor-pointer",
                          selectedKnowledgeBases.includes(kb.id) ? "bg-emerald-50" : "hover:bg-gray-50",
                        )}
                        onClick={() => {
                          if (selectedKnowledgeBases.includes(kb.id)) {
                            setSelectedKnowledgeBases(selectedKnowledgeBases.filter((id) => id !== kb.id));
                          } else {
                            setSelectedKnowledgeBases([...selectedKnowledgeBases, kb.id]);
                          }
                        }}
                      >
                        <div
                          className={cn(
                            "mr-2 h-4 w-4 rounded-sm border flex items-center justify-center",
                            selectedKnowledgeBases.includes(kb.id)
                              ? "border-emerald-500 bg-emerald-500 text-white"
                              : "border-gray-300",
                          )}
                        >
                          {selectedKnowledgeBases.includes(kb.id) && <Check className="h-3 w-3" />}
                        </div>
                        <div className="flex items-center">
                          <Database className="h-3 w-3 mr-2 text-green-500" />
                          <span className="text-sm">{kb.name}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );

    const modalFooter = (
      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={closeModal}>
          取消
        </Button>
        <Button
          className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
          onClick={() => {
            // 如果有选择知识库，则更新提示信息
            if (selectedKnowledgeBases.length > 0) {
              const selectedNames = knowledgeBases
                .filter((kb) => selectedKnowledgeBases.includes(kb.id))
                .map((kb) => kb.name)
                .join(", ");

              setMessages([
                {
                  role: "assistant",
                  content: `已选择知识库: ${selectedNames}。请输入您的问题，我将在这些知识库中查找相关资料。`,
                },
              ]);
            }
            closeModal();
          }}
        >
          确定
        </Button>
      </div>
    );

    openModal({
      title: "选择知识库",
      content: modalContent,
      footer: modalFooter,
    });
  }

  // 查看文件详情
  const handleViewFileDetail = (material: Material) => {
    openModal({
      title: material.title,
      content: (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FileText className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-sm font-medium">{material.fileType}</span>
            </div>
            <span className="text-xs text-gray-500">{material.fileSize}</span>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">描述</h3>
            <p className="text-sm text-gray-600">{material.description}</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">来源</h3>
              <p className="text-sm text-gray-600">{material.source}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">日期</h3>
              <p className="text-sm text-gray-600">{material.date}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">上传时间</h3>
              <p className="text-sm text-gray-600">{material.uploadTime}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">位置</h3>
              <p className="text-sm text-gray-600">{material.pageInfo}</p>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            {material.sourceType === "external" && material.externalUrl && (
              <Button
                variant="outline"
                className="flex items-center"
                onClick={() => window.open(material.externalUrl, "_blank")}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                访问外部链接
              </Button>
            )}
            <Button
              className="bg-[#f5a623] hover:bg-[#f5a623]/90 flex items-center"
              onClick={() => alert(`下载文件: ${material.title}`)}
            >
              <Download className="h-4 w-4 mr-2" />
              下载文件
            </Button>
          </div>
        </div>
      ),
    })
  }

  /**
   * 发送消息
   * 在实际应用中，这里应该根据assistantId调用对应的AI API
   */
  const handleSendMessage = async () => {
    // 如果输入为空、正在加载中、正在活跃状态或未登录，不处理
    if (!input.trim() || isLoading || isActive || !isLoggedIn) return

    // 检查用户是否登录（额外检查，虽然上面已经检查过了）
    if (!isLoggedIn) {
      setError("请先登录后再使用AI助手")
      return
    }

    // 清除错误
    setError(null)

    // 保存用户输入
    const userMessage = input.trim()

    // 添加用户消息和空的助手消息（一次性更新，避免多次渲染）
    setMessages([...messages,
      { role: "user", content: userMessage },
      { role: "assistant", content: "" }
    ])

    // 设置加载状态和活跃状态
    setIsLoading(true)
    setIsActive(true) // 设置为活跃状态，禁用输入框

    // 清空输入框
    setInput("")

    console.log('发送消息:', {
      userMessage,
      selectedKnowledgeBases,
      conversationId
    })

    try {
      // 使用流式打字机组件发送请求
      if (apiKey && apiEndpoint && appId) {
        console.log(`使用流式打字机调用Dify API: ${apiEndpoint}, appId: ${appId}, appCode: ${appCode || '未设置'}`)

        try {
          console.log('开始调用流式打字机，参数:', {
            userMessage,
            conversationId,
            apiKey: apiKey ? apiKey.substring(0, 5) + '...' : undefined,
            apiEndpoint,
            appId,
            appCode
          })

          // 开始流式打字
          // 注意：不要在这里设置isLoading为false，让思考中动画显示直到收到第一个token
          const { cancel } = await startStreaming(
            userMessage,
            conversationId,
            apiKey,
            apiEndpoint,
            appId,
            appCode
          )

          console.log('流式打字机启动成功')

          // 如果有会话ID，保存它
          if (streamConversationId) {
            setConversationId(streamConversationId)
            console.log('保存新的会话ID:', streamConversationId)
          }

          // 注意：不在这里更新聊天记录，而是在流式响应结束后更新
          // 流式响应过程中，通过isStreaming状态和streamingText组件显示内容

          return
        } catch (streamError) {
          console.error('流式打字机错误:', streamError)
          setError(`流式响应错误: ${streamError.message || '未知错误'}`)

          // 如果流式响应失败，尝试使用普通API
          console.log('流式响应失败，尝试使用普通API')

          // 获取认证token
          const token = localStorage.getItem('hefamily_token')
          if (!token) {
            console.warn('未找到认证token，无法调用AI API')
            throw new Error('未找到认证token')
          }

          // 准备请求体
          const requestBody = {
            query: userMessage,
            conversation_id: conversationId,
            // 传递AI助手配置信息
            api_key: apiKey,
            api_endpoint: apiEndpoint,
            app_id: appId,
            app_code: appCode
          };

          // 尝试调用后端API，并添加认证头
          const response = await fetch('/api/ai/data-query/query', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(requestBody),
          })

          if (response.ok) {
            const data = await response.json()
            if (data.success && data.data) {
              console.log('AI数据查询助手响应:', data.data)

              // 保存会话ID
              if (data.data.conversation_id) {
                setConversationId(data.data.conversation_id)
              }

              // 处理响应内容
              if (data.data.answer && data.data.answer.trim() !== "") {
                // 检查是否包含思考过程（details标签）
                let cleanedAnswer = data.data.answer

                // 如果包含思考过程，提取出正式回复部分
                if (data.data.answer.includes('<details') && data.data.answer.includes('</details>')) {
                  const detailsEndIndex = data.data.answer.indexOf('</details>') + 10
                  cleanedAnswer = data.data.answer.substring(detailsEndIndex).trim()
                  console.log('提取出正式回复部分，长度:', cleanedAnswer.length)
                }

                // 移除所有HTML标签，只保留纯文本
                const textOnly = cleanedAnswer.replace(/<[^>]*>/g, '')
                console.log('移除HTML标签后的纯文本，长度:', textOnly.length)

                // 设置打字机文本内容
                setTypewriterText(textOnly)

                // 更新最后一条消息的索引
                setLastMessageIndex(messages.length)

                // 显示打字机效果
                setShowTypewriter(true)
                return
              }
            }
          }

          throw new Error('API响应处理失败')
        }
      }

      // 如果API配置不完整，显示错误消息
      setError("AI助手配置不完整，请联系管理员")
      setMessages((prev) => {
        const newMessages = [...prev]
        const lastIndex = newMessages.length - 1
        if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
          newMessages[lastIndex] = {
            role: "assistant",
            content: "抱歉，AI助手配置不完整，请联系管理员。",
          }
        }
        return newMessages
      })
    } catch (err) {
      console.error("获取AI回复失败:", err)
      setError(`获取回复失败: ${err.message || '未知错误'}`)

      // 更新最后一条消息
      setMessages((prev) => {
        const newMessages = [...prev]
        const lastIndex = newMessages.length - 1
        if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
          newMessages[lastIndex] = {
            role: "assistant",
            content: "抱歉，获取回复失败，请重试。",
          }
        }
        return newMessages
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 如果正在加载中或正在活跃状态，不处理Enter键
    if (isLoading || isActive) return

    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* 顶部标题 */}
      <div className="flex items-center p-4 border-b">
        <div className="w-8 h-8 rounded-full bg-[#1e7a43] flex items-center justify-center text-white mr-2">AI</div>
        <div className="text-sm font-medium">AI 数据查询助手</div>
        <div className="ml-auto flex items-center space-x-2">
          <div className="flex items-center">
            <span
              className={`inline-block w-2 h-2 rounded-full ${isActive ? "bg-green-500" : "bg-gray-400"} mr-1`}
            ></span>
            <span className="text-xs text-gray-500">{isActive ? "运行中" : "空闲"}</span>
          </div>
        </div>
      </div>

      {/* 对话内容区 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 pb-20">
        {messages.map((message, index) => {
          // 如果是最后一条助手消息且正在流式输出或加载中，则显示流式打字机效果
          if (index === messages.length - 1 && message.role === "assistant" && (isStreaming || (isLoading && message.content === ""))) {
            return (
              <div key={index} className="flex justify-start">
                <div className="max-w-[80%] rounded-lg p-3 bg-white border border-[#1e7a43]/20">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mr-3">
                      <div className="w-8 h-8 rounded-full bg-[#1e7a43] flex items-center justify-center text-white">
                        AI
                      </div>
                    </div>
                    <div className="flex-1 overflow-hidden">
                      {streamingContent ? (
                        <div className="prose prose-sm max-w-none whitespace-pre-line">
                          {streamingContent.includes('<details') && streamingContent.includes('</details>')
                            ? streamingContent.substring(streamingContent.indexOf('</details>') + 10).trim()
                            : streamingContent}
                          <span className="inline-block w-2 h-4 bg-gray-500 ml-1 animate-pulse"></span>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"></div>
                          <span>正在生成回复...</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          }

          // 正常显示其他消息
          return (
            <div key={index} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.role === "user" ? "bg-[#1e7a43] text-white" : "bg-white border border-gray-200"
                }`}
              >
                {message.role === "assistant" && (
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mr-3">
                      <div className="w-8 h-8 rounded-full bg-[#1e7a43] flex items-center justify-center text-white">
                        AI
                      </div>
                    </div>
                    <div className="flex-1 overflow-hidden">
                      <div className="prose prose-sm max-w-none whitespace-pre-line">
                        {message.content}
                      </div>
                    </div>
                  </div>
                )}

                {message.role === "user" && (
                  <div>{message.content}</div>
                )}

                {/* 材料显示部分 */}
                {message.materials && message.materials.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {message.materials.map((material) => (
                      <div key={material.id} className="bg-gray-50 p-3 rounded border border-gray-200">
                        <h4 className="font-medium text-[#1e7a43]">{material.title}</h4>
                        <p className="text-xs text-gray-600 mt-1">{material.description.substring(0, 100)}...</p>
                        <div className="flex flex-wrap justify-between items-center mt-2 text-xs text-gray-500">
                          <div>
                            <span className="mr-3">来源: {material.source}</span>
                            <span className="mr-3">上传时间: {material.uploadTime || material.date}</span>
                            {material.sourceType === "internal" && material.pageInfo && (
                              <span>位置: {material.pageInfo}</span>
                            )}
                          </div>
                          <button
                            className="text-xs text-[#f5a623] hover:underline flex items-center"
                            onClick={() => handleViewFileDetail(material)}
                          >
                            查看详情
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          );
        })}
        {/* 不再需要传统打字机效果，统一使用流式打字机 */}

        {/* 不再需要单独的流式打字机效果，我们将在messages.map中处理 */}

        {/* 思考中动画 - 与课题分析助手保持一致 */}
        {isLoading && !isStreaming && (
          <div className="flex justify-start">
            <div className="max-w-[80%] rounded-lg p-3 bg-white border border-[#1e7a43]/20">
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  <div className="w-8 h-8 rounded-full bg-[#1e7a43] flex items-center justify-center text-white">
                    AI
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex flex-col space-y-2">
                    <div className="flex space-x-2">
                      <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"></div>
                      <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce [animation-delay:0.2s]"></div>
                      <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce [animation-delay:0.4s]"></div>
                    </div>
                    <div className="text-sm text-gray-500">
                      正在思考中...
                    </div>
                    <div className="text-xs text-gray-400">
                      复杂问题可能需要较长时间，请耐心等待
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}


        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 底部输入框 */}
      <div className="p-4 border-t absolute bottom-0 left-0 right-0 bg-gray-50">
        {!isLoggedIn ? (
          <div className="text-center p-2 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-700">请先登录后再使用AI助手</p>
            <Button
              className="mt-2 bg-[#1e7a43] hover:bg-[#165a32]"
              onClick={() => {
                // 触发登录弹窗
                const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
                window.dispatchEvent(event);
              }}
            >
              登录
            </Button>
          </div>
        ) : (
          <div className="flex items-center">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="请输入您的问题..."
              className="flex-1 border rounded-l-md p-2 focus:outline-none focus:ring-1 focus:ring-[#1e7a43] resize-none h-10 max-h-32 overflow-y-auto"
              rows={1}
              disabled={isLoading || isActive || !isLoggedIn}
            />
            <Button
              onClick={handleSendMessage}
              className="rounded-l-none bg-[#f5a623] hover:bg-[#f5a623]/90 h-10"
              disabled={isLoading || isActive || !isLoggedIn || !input.trim()}
            >
              {isLoading ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        )}
      </div>

      {/* 不再需要单独的知识库选择模态框，使用统一的模态框系统 */}
    </div>
  )
}
