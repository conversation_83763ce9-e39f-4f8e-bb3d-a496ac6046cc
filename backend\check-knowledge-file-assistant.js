const db = require('./src/models');

console.log('检查知识库文件分析助手配置...');

(async () => {
  try {
    console.log('查找知识库文件分析助手...');
    // 查找知识库文件分析助手
    const assistant = await db.AIAssistant.findOne({
      where: { type: 'knowledge-file' }
    });

    if (!assistant) {
      console.error('未找到知识库文件分析助手');
      process.exit(1);
      return;
    }

    console.log('找到知识库文件分析助手，ID:', assistant.id);
    console.log('当前配置:', JSON.stringify(assistant, null, 2));
    
    // 打印关键字段
    console.log('\n关键字段:');
    console.log('系统知识库数据集ID (app_id):', assistant.app_id);
    console.log('用户知识库数据集ID (app_code):', assistant.app_code);
    console.log('API密钥:', assistant.api_key);
    console.log('API端点:', assistant.api_endpoint);
    console.log('上传API路径:', assistant.upload_api_path);
    console.log('分析API路径:', assistant.analysis_api_path);
  } catch (e) {
    console.error('检查知识库文件分析助手失败:', e);
  } finally {
    process.exit();
  }
})();
