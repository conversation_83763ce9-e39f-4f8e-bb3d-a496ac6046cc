"use client"

import React, { useState, useEffect } from "react"
import {
  File,
  Download,
  Trash2,
  Search,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  MoreVertical
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { useAuth } from "@/contexts/auth-context"
import {
  getFileList,
  getKnowledgeBaseFileList,
  downloadFile,
  deleteFile,
  reviewFile,
  type File as FileType
} from "@/services/file-service"

/**
 * 文件列表组件属性
 */
interface FileListProps {
  knowledgeBaseId?: string | number // 修改为可以接受字符串或数字
  showActions?: boolean
  showReview?: boolean
  onFileDeleted?: () => void
  onFileReviewed?: () => void
  refreshTrigger?: number // 添加刷新触发器
  status?: 'pending' | 'approved' | 'rejected' // 添加状态过滤参数
  fileType?: string // 文件类型筛选
  sizeMin?: number // 文件大小最小值 (字节)
  sizeMax?: number // 文件大小最大值 (字节)
  knowledgeBaseType?: string // 知识库类型筛选
  searchQuery?: string // 外部传入的搜索查询
}

/**
 * 文件列表组件
 *
 * 显示文件列表，支持搜索、下载、删除、审核等操作
 */
export function FileList({
  knowledgeBaseId,
  showActions = true,
  showReview = false,
  onFileDeleted,
  onFileReviewed,
  refreshTrigger = 0,
  status,
  fileType,
  sizeMin,
  sizeMax,
  knowledgeBaseType,
  searchQuery = ""
}: FileListProps) {
  // 状态
  const [allFiles, setAllFiles] = useState<FileType[]>([]) // 存储所有文件
  const [filteredFiles, setFilteredFiles] = useState<FileType[]>([]) // 存储筛选后的文件
  const [isLoading, setIsLoading] = useState(true)
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10, // 每页显示10个文件
    totalPages: 0
  })
  const [selectedFile, setSelectedFile] = useState<FileType | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showReviewDialog, setShowReviewDialog] = useState(false)
  const [reviewStatus, setReviewStatus] = useState<"approved" | "rejected">("approved")
  const [rejectReason, setRejectReason] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  // 权限检查
  const { hasPermission } = useAuth()
  const canDelete = hasPermission("file:delete")
  const canReview = hasPermission("file:review")

  /**
   * 加载文件列表
   */
  const loadFiles = async (page = 1) => {
    setIsLoading(true)
    try {
      const params: any = {
        page,
        limit: 10, // 每页显示10个文件
        search: searchQuery || undefined
      }

      // 添加状态过滤参数
      if (status) {
        params.status = status
      } else if (showReview) {
        // 如果是审核页面但没有明确指定状态，默认显示待审核文件
        params.status = 'pending'
      }

      // 添加文件类型筛选参数
      if (fileType && fileType !== 'all') {
        // 根据文件类型添加适当的MIME类型前缀
        if (fileType === 'pdf') {
          params.type = 'application/pdf'
        } else if (fileType === 'doc' || fileType === 'docx') {
          params.type = 'application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        } else if (fileType === 'xls' || fileType === 'xlsx') {
          params.type = 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        } else if (fileType === 'ppt' || fileType === 'pptx') {
          params.type = 'application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation'
        } else if (fileType === 'txt') {
          params.type = 'text/plain'
        } else if (fileType === 'jpg' || fileType === 'jpeg' || fileType === 'png' || fileType === 'gif') {
          params.type = 'image/jpeg,image/png,image/gif'
        } else {
          // 如果是其他类型，直接使用
          params.type = fileType
        }

        console.log('添加文件类型筛选:', params.type)
      }

      // 添加文件大小筛选参数
      if (sizeMin !== undefined) {
        params.size_min = sizeMin
        console.log('添加最小文件大小筛选:', formatFileSize(sizeMin))
      }

      if (sizeMax !== undefined) {
        params.size_max = sizeMax
        console.log('添加最大文件大小筛选:', formatFileSize(sizeMax))
      }

      // 添加知识库类型筛选参数
      if (knowledgeBaseType && knowledgeBaseType !== 'all') {
        params.knowledge_base_type = knowledgeBaseType
      }

      // 如果指定了知识库ID，则获取该知识库的文件
      let files = []
      let pagination = { total: 0, page: 1, limit: 10, totalPages: 1 }

      try {
        if (knowledgeBaseId) {
          // 确保知识库ID是有效的
          const knowledgeBaseIdStr = String(knowledgeBaseId); // 转换为字符串
          if (!knowledgeBaseIdStr || knowledgeBaseIdStr.trim() === '') {
            console.warn('知识库ID无效或为空，将使用普通文件列表API', knowledgeBaseId)
          } else {
            // 使用专门的知识库文件列表API
            console.log('调用知识库文件列表API，知识库ID:', knowledgeBaseId)
            // 移除params中的knowledge_base_id，因为已经在URL中指定了
            const { knowledge_base_id, ...restParams } = params
            // 确保传递字符串类型的知识库ID
            const response = await getKnowledgeBaseFileList(String(knowledgeBaseId), restParams)
            console.log('知识库文件列表API响应:', response)
            console.log('知识库文件列表API响应类型:', typeof response)
            console.log('知识库文件列表API响应是否有files字段:', response && 'files' in response)

            if (response && 'files' in response) {
              // 确保我们有正确的文件列表和分页信息
              files = response.files || []
              pagination = response.pagination || pagination

              // 检查文件列表中的上传者信息
              console.log('文件列表长度:', files.length)
              files.forEach((file, index) => {
                // 只在开发环境记录文件信息
                if (process.env.NODE_ENV !== 'production') {
                  console.log(`文件 ${index + 1}:`, file.id, file.original_name)
                  // 不记录上传者详细信息，只记录是否存在
                  console.log(`文件 ${index + 1} 上传者类型:`, typeof file.uploader)
                  console.log(`文件 ${index + 1} 上传者ID存在:`, !!file.uploader_id)
                }
              })
            } else {
              console.error('API响应中没有files字段:', response)
            }
          }
        } else {
          // 否则获取所有文件
          console.log('调用文件列表API')
          const response = await getFileList(params)
          console.log('文件列表API响应:', response)
          console.log('文件列表API响应类型:', typeof response)
          console.log('文件列表API响应是否有files字段:', response && 'files' in response)

          if (response && 'files' in response) {
            // 确保我们有正确的文件列表和分页信息
            files = response.files || []
            pagination = response.pagination || pagination

            // 检查文件列表中的上传者信息
            console.log('文件列表长度:', files.length)
            files.forEach((file, index) => {
              // 只在开发环境记录文件信息
              if (process.env.NODE_ENV !== 'production') {
                console.log(`文件 ${index + 1}:`, file.id, file.original_name)
                // 不记录上传者详细信息，只记录是否存在
                console.log(`文件 ${index + 1} 上传者类型:`, typeof file.uploader)
                console.log(`文件 ${index + 1} 上传者ID存在:`, !!file.uploader_id)
              }
            })

            // 更新所有文件列表，前端筛选逻辑会自动应用
            setAllFiles(files)
          } else {
            console.error('API响应中没有files字段:', response)
          }
        }
      } catch (error) {
        console.error("API调用失败:", error)
        throw error
      }

      // 确保上传者信息正确处理
      const processedFiles = files.map(file => {
        // 只在开发环境记录处理文件信息
        if (process.env.NODE_ENV !== 'production') {
          console.log('处理文件:', file.id, file.original_name, '上传者ID存在:', !!file.uploader_id)
        }

        // 检查上传者信息是否存在且有效
        if (file.uploader && typeof file.uploader === 'object' && file.uploader.username) {
          // 只在开发环境记录上传者信息有效
          if (process.env.NODE_ENV !== 'production') {
            console.log('文件上传者信息有效')
          }
          return file
        }

        // 如果上传者信息不是对象或为null，但有上传者ID，尝试创建上传者对象
        if (file.uploader_id) {
          // 只在开发环境记录上传者信息处理
          if (process.env.NODE_ENV !== 'production') {
            console.log('文件上传者信息无效但有上传者ID，尝试创建上传者对象')
          }
          return {
            ...file,
            uploader: {
              id: file.uploader_id,
              username: `用户ID: ${file.uploader_id}`,
              email: ''
            }
          }
        }

        // 如果没有上传者ID，设置为默认对象
        // 只在开发环境记录上传者信息处理
        if (process.env.NODE_ENV !== 'production') {
          console.log('文件上传者信息无效，设置为默认值')
        }
        return {
          ...file,
          uploader: { id: null, username: '未知用户', email: '' }
        }
      })

      // 更新所有文件列表
      setAllFiles(processedFiles)
      // 设置分页信息
      setPagination(pagination)
    } catch (error) {
      console.error("加载文件列表失败:", error)
      toast({
        title: "加载失败",
        description: "无法加载文件列表，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 初始加载和刷新
  useEffect(() => {
    console.log('FileList组件刷新触发器变化:', refreshTrigger)
    console.log('FileList组件筛选条件:', {
      knowledgeBaseId,
      status,
      fileType,
      sizeMin,
      sizeMax,
      knowledgeBaseType,
      searchQuery
    })
    loadFiles()
  }, [knowledgeBaseId, refreshTrigger, status, knowledgeBaseType])

  // 前端筛选逻辑
  useEffect(() => {
    if (allFiles.length === 0) return

    console.log('应用前端筛选，文件总数:', allFiles.length)
    let filtered = [...allFiles]

    // 文件类型筛选
    if (fileType && fileType !== 'all') {
      console.log('应用文件类型筛选:', fileType)

      // 定义文件类型映射
      const fileExtensions: { [key: string]: string[] } = {
        'pdf': ['.pdf'],
        'doc': ['.doc'],
        'docx': ['.docx'],
        'xls': ['.xls'],
        'xlsx': ['.xlsx'],
        'ppt': ['.ppt'],
        'pptx': ['.pptx'],
        'txt': ['.txt'],
        'jpg': ['.jpg', '.jpeg', '.png', '.gif']
      }

      // 处理组合类型
      if (fileType === 'word') {
        fileExtensions[fileType] = ['.doc', '.docx']
      } else if (fileType === 'excel') {
        fileExtensions[fileType] = ['.xls', '.xlsx']
      } else if (fileType === 'powerpoint') {
        fileExtensions[fileType] = ['.ppt', '.pptx']
      } else if (fileType === 'image') {
        fileExtensions[fileType] = ['.jpg', '.jpeg', '.png', '.gif']
      }

      if (fileExtensions[fileType]) {
        filtered = filtered.filter(file => {
          // 获取文件名并转为小写
          const fileName = file.original_name.toLowerCase()

          // 检查文件是否以任何指定的扩展名结尾
          const matchesExtension = fileExtensions[fileType].some(ext => {
            // 确保扩展名前有点号，避免匹配文件名中的其他部分
            return fileName.endsWith(ext)
          })

          // 记录每个文件的筛选结果，便于调试
          console.log(`文件 "${fileName}" 是否匹配 ${fileType} 类型: ${matchesExtension}`)

          return matchesExtension
        })
      }
      console.log('文件类型筛选后剩余:', filtered.length)
    }

    // 文件大小筛选
    if (sizeMin !== undefined) {
      console.log('应用最小文件大小筛选:', sizeMin)
      filtered = filtered.filter(file => file.size >= sizeMin)
      console.log('最小文件大小筛选后剩余:', filtered.length)
    }

    if (sizeMax !== undefined) {
      console.log('应用最大文件大小筛选:', sizeMax)
      filtered = filtered.filter(file => file.size <= sizeMax)
      console.log('最大文件大小筛选后剩余:', filtered.length)
    }

    // 搜索查询筛选
    if (searchQuery) {
      console.log('应用搜索查询筛选:', searchQuery)
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(file =>
        file.original_name.toLowerCase().includes(query) ||
        (file.description && file.description.toLowerCase().includes(query))
      )
      console.log('搜索查询筛选后剩余:', filtered.length)
    }

    setFilteredFiles(filtered)
  }, [allFiles, fileType, sizeMin, sizeMax, searchQuery])

  /**
   * 处理页码变化
   */
  const handlePageChange = (page: number) => {
    loadFiles(page)
  }

  /**
   * 处理文件下载
   */
  const handleDownload = async (file: FileType) => {
    try {
      await downloadFile(file.id, file.original_name)

      toast({
        title: "下载开始",
        description: "文件开始下载，请稍候",
      })
    } catch (error) {
      console.error("下载文件失败:", error)
      toast({
        title: "下载失败",
        description: "无法下载文件，请稍后再试",
        variant: "destructive"
      })
    }
  }

  /**
   * 处理文件删除
   */
  const handleDelete = async () => {
    if (!selectedFile) return

    setIsProcessing(true)
    try {
      await deleteFile(selectedFile.id)

      toast({
        title: "删除成功",
        description: "文件已成功删除",
      })

      // 关闭对话框
      setShowDeleteDialog(false)

      // 重新加载文件列表
      loadFiles(pagination.page)

      // 调用回调
      if (onFileDeleted) {
        onFileDeleted()
      }
    } catch (error) {
      console.error("删除文件失败:", error)
      toast({
        title: "删除失败",
        description: "无法删除文件，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
    }
  }

  /**
   * 处理文件审核
   */
  const handleReview = async () => {
    if (!selectedFile) return

    setIsProcessing(true)
    try {
      await reviewFile(selectedFile.id, {
        status: reviewStatus,
        reject_reason: reviewStatus === "rejected" ? rejectReason : undefined
      })

      toast({
        title: "审核完成",
        description: `文件已${reviewStatus === "approved" ? "批准" : "拒绝"}`,
      })

      // 关闭对话框
      setShowReviewDialog(false)

      // 重新加载文件列表
      loadFiles(pagination.page)

      // 调用回调
      if (onFileReviewed) {
        onFileReviewed()
      }
    } catch (error) {
      console.error("审核文件失败:", error)
      toast({
        title: "审核失败",
        description: "无法完成审核，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
    }
  }



  /**
   * 获取文件图标
   */
  const getFileIcon = (fileType: string) => {
    const type = fileType.split('/')[1] || fileType

    // 根据文件类型返回不同图标
    switch (type) {
      case 'pdf':
        return <File className="h-5 w-5 text-red-500" />
      case 'doc':
      case 'docx':
      case 'msword':
      case 'vnd.openxmlformats-officedocument.wordprocessingml.document':
        return <File className="h-5 w-5 text-blue-500" />
      case 'xls':
      case 'xlsx':
      case 'vnd.ms-excel':
      case 'vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return <File className="h-5 w-5 text-green-500" />
      case 'ppt':
      case 'pptx':
      case 'vnd.ms-powerpoint':
      case 'vnd.openxmlformats-officedocument.presentationml.presentation':
        return <File className="h-5 w-5 text-orange-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <File className="h-5 w-5 text-purple-500" />
      default:
        return <File className="h-5 w-5 text-gray-500" />
    }
  }

  /**
   * 获取状态标签
   */
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">已批准</Badge>
      case 'rejected':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">已拒绝</Badge>
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">待审核</Badge>
      default:
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">{status}</Badge>
    }
  }

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="w-full">
      {/* 文件列表 */}
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]"></TableHead>
              <TableHead>文件名</TableHead>
              <TableHead>大小</TableHead>
              <TableHead>上传者</TableHead>
              <TableHead>上传时间</TableHead>
              {showReview && <TableHead>状态</TableHead>}
              {showActions && <TableHead className="text-right">操作</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={showReview ? 7 : 6} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#1e7a43]"></div>
                    <span className="ml-2">加载中...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredFiles.length === 0 ? (
              <TableRow>
                <TableCell colSpan={showReview ? 7 : 6} className="text-center py-8">
                  <p className="text-gray-500">暂无符合条件的文件</p>
                </TableCell>
              </TableRow>
            ) : (
              filteredFiles.map((file) => (
                <TableRow key={file.id}>
                  <TableCell>
                    {getFileIcon(file.type)}
                  </TableCell>
                  <TableCell className="font-medium">
                    {file.original_name}
                    {file.summary && (
                      <p className="text-xs text-gray-500 mt-1 truncate max-w-[300px]">
                        {file.summary}
                      </p>
                    )}
                  </TableCell>
                  <TableCell>{formatFileSize(file.size)}</TableCell>
                  <TableCell>
                    {/* 只在开发环境记录渲染上传者信息 */}
                    {process.env.NODE_ENV !== 'production' && console.log('渲染文件:', file.id, file.original_name)}
                    {/* 显示上传者用户名 */}
                    {file.uploader?.username || '未知用户'}
                  </TableCell>
                  <TableCell>{new Date(file.created_at).toLocaleString()}</TableCell>
                  {showReview && (
                    <TableCell>{getStatusBadge(file.status)}</TableCell>
                  )}
                  {showActions && (
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                          onClick={() => handleDownload(file)}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          下载
                        </Button>

                        {canReview && file.status === "pending" && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-green-600 hover:text-green-800 hover:bg-green-50"
                            onClick={() => {
                              setSelectedFile(file)
                              setShowReviewDialog(true)
                            }}
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            审核
                          </Button>
                        )}

                        {canDelete && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-800 hover:bg-red-50"
                            onClick={() => {
                              setSelectedFile(file)
                              setShowDeleteDialog(true)
                            }}
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            删除
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 分页 */}
      <div className="flex items-center justify-between mt-4">
        <div className="text-sm text-gray-500">
          {fileType || sizeMin || sizeMax || searchQuery ? (
            <>
              筛选结果: {filteredFiles.length} 个文件 (总共 {allFiles.length} 个文件)
              {fileType && <span className="ml-2">类型: {fileType}</span>}
              {sizeMin && <span className="ml-2">最小: {typeof window !== 'undefined' && (window as any).formatFileSize ? (window as any).formatFileSize(sizeMin) : `${sizeMin} 字节`}</span>}
              {sizeMax && <span className="ml-2">最大: {typeof window !== 'undefined' && (window as any).formatFileSize ? (window as any).formatFileSize(sizeMax) : `${sizeMax} 字节`}</span>}
              {searchQuery && <span className="ml-2">搜索: "{searchQuery}"</span>}
            </>
          ) : (
            <>
              共 {pagination.total} 个文件
              {pagination.totalPages > 1 && (
                <span className="ml-1 font-medium">
                  ，当前显示第 {pagination.page} / {pagination.totalPages} 页
                  {pagination.page < pagination.totalPages && (
                    <span className="text-blue-600 ml-1">
                      (还有 {pagination.total - pagination.page * pagination.limit > 0 ? pagination.total - pagination.page * pagination.limit : pagination.total - (pagination.page - 1) * pagination.limit} 个文件在下一页)
                    </span>
                  )}
                </span>
              )}
            </>
          )}
        </div>
        {pagination.totalPages > 1 && (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              上一页
            </Button>
            {/* 显示页码按钮 - 改进版本，支持显示当前页附近的页码 */}
            {Array.from({ length: Math.min(5, pagination.totalPages) }).map((_, i) => {
              // 计算显示的页码，确保当前页在中间
              let startPage = Math.max(1, pagination.page - 2);
              let endPage = Math.min(pagination.totalPages, startPage + 4);

              // 如果结束页已经达到最大值，调整起始页
              if (endPage === pagination.totalPages) {
                startPage = Math.max(1, endPage - 4);
              }

              const pageNum = startPage + i;

              // 确保页码在有效范围内
              if (pageNum <= pagination.totalPages) {
                return (
                  <Button
                    key={pageNum}
                    variant={pagination.page === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className={pagination.page === pageNum ? "bg-blue-600 hover:bg-blue-700" : ""}
                  >
                    {pageNum}
                  </Button>
                );
              }
              return null;
            })}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
            >
              下一页
            </Button>
          </div>
        )}
      </div>

      {/* 分页提示 */}
      {pagination.totalPages > 1 && (
        <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md text-blue-700 text-sm">
          <p className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            提示：文件总数超过每页显示数量，请使用分页控件查看更多文件。
          </p>
        </div>
      )}

      {/* 删除确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除文件 "{selectedFile?.original_name}" 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isProcessing}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  处理中...
                </>
              ) : (
                "确认删除"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 审核对话框 */}
      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>审核文件</DialogTitle>
            <DialogDescription>
              请审核文件 "{selectedFile?.original_name}"
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex items-center space-x-4">
              <Button
                variant={reviewStatus === "approved" ? "default" : "outline"}
                className={reviewStatus === "approved" ? "bg-green-600 hover:bg-green-700" : ""}
                onClick={() => setReviewStatus("approved")}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                批准
              </Button>

              <Button
                variant={reviewStatus === "rejected" ? "default" : "outline"}
                className={reviewStatus === "rejected" ? "bg-red-600 hover:bg-red-700" : ""}
                onClick={() => setReviewStatus("rejected")}
              >
                <XCircle className="h-4 w-4 mr-2" />
                拒绝
              </Button>
            </div>

            {reviewStatus === "rejected" && (
              <div>
                <label className="block text-sm font-medium mb-1">拒绝原因</label>
                <textarea
                  value={rejectReason}
                  onChange={(e) => setRejectReason(e.target.value)}
                  className="w-full border rounded-md p-2 h-24"
                  placeholder="请输入拒绝原因..."
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowReviewDialog(false)}
              disabled={isProcessing}
            >
              取消
            </Button>
            <Button
              onClick={handleReview}
              disabled={isProcessing || (reviewStatus === "rejected" && !rejectReason)}
              className={reviewStatus === "approved" ? "bg-green-600 hover:bg-green-700" : "bg-red-600 hover:bg-red-700"}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  处理中...
                </>
              ) : (
                reviewStatus === "approved" ? "批准" : "拒绝"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default FileList
