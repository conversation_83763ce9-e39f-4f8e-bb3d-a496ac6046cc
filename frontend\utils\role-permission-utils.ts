/**
 * 角色权限工具函数
 *
 * 提供角色权限相关的工具函数
 */

/**
 * 设置角色权限
 *
 * @param roleId 角色ID
 * @param permissionIds 权限ID数组
 * @returns 设置结果
 */
export async function setRolePermissions(roleId: number, permissionIds: number[]) {
  try {
    // 获取token
    const token = localStorage.getItem('hefamily_token');
    if (!token) {
      return { success: false, message: '未登录或登录已过期' };
    }

    // 构建API URL - 使用相对路径
    const url = `/api/roles/${roleId}/permissions`;

    // 打印调试信息
    console.log('调用 setRolePermissions API:', `roleId=${roleId}, permissionIds=`, permissionIds);

    // 发送请求
    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ permissions: permissionIds })
    });

    // 解析响应
    const data = await response.json();
    console.log('设置角色权限API响应:', data);

    // 返回结果
    return {
      success: data.success || response.ok,
      message: data.message || (response.ok ? '角色权限更新成功' : '角色权限更新失败'),
      data: data.data
    };
  } catch (error) {
    console.error('设置角色权限失败:', error);
    return { success: false, message: '设置角色权限失败，请稍后重试' };
  }
}

/**
 * 获取角色权限
 *
 * @param roleId 角色ID
 * @returns 角色权限
 */
export async function getRolePermissions(roleId: number) {
  try {
    // 获取token
    const token = localStorage.getItem('hefamily_token');
    if (!token) {
      return [];
    }

    // 构建API URL - 使用相对路径
    const url = `/api/roles/${roleId}/permissions`;

    // 打印调试信息
    console.log('获取角色权限:', `roleId=${roleId}`);

    // 发送请求
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // 解析响应
    const data = await response.json();
    console.log('获取角色权限响应:', data);

    // 返回结果
    return data.success ? data.data : [];
  } catch (error) {
    console.error('获取角色权限失败:', error);
    return [];
  }
}
