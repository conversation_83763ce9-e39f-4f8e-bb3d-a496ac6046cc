<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>和富家族研究平台 - 测试覆盖率报告</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      background-color: #f8f9fa;
      padding: 20px;
      margin-bottom: 30px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #2c3e50;
      margin-top: 0;
    }
    
    h2 {
      color: #3498db;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
      margin-top: 30px;
    }
    
    h3 {
      color: #2c3e50;
    }
    
    .summary {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .summary-item {
      flex: 1;
      min-width: 200px;
      background-color: #fff;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .summary-item h3 {
      margin-top: 0;
      margin-bottom: 10px;
    }
    
    .progress {
      height: 20px;
      background-color: #ecf0f1;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 10px;
    }
    
    .progress-bar {
      height: 100%;
      border-radius: 10px;
      transition: width 0.3s ease;
    }
    
    .progress-bar.green {
      background-color: #2ecc71;
    }
    
    .progress-bar.yellow {
      background-color: #f1c40f;
    }
    
    .progress-bar.red {
      background-color: #e74c3c;
    }
    
    .percentage {
      font-weight: bold;
      font-size: 18px;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 30px;
    }
    
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    
    th {
      background-color: #f8f9fa;
      font-weight: bold;
    }
    
    tr:hover {
      background-color: #f5f5f5;
    }
    
    .file-path {
      font-family: monospace;
      word-break: break-all;
    }
    
    .badge {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 15px;
      color: white;
      font-weight: bold;
      font-size: 12px;
    }
    
    .badge.green {
      background-color: #2ecc71;
    }
    
    .badge.yellow {
      background-color: #f1c40f;
    }
    
    .badge.red {
      background-color: #e74c3c;
    }
    
    footer {
      margin-top: 50px;
      text-align: center;
      color: #7f8c8d;
      font-size: 14px;
    }
    
    .timestamp {
      font-style: italic;
    }
  </style>
</head>
<body>
  <header>
    <h1>和富家族研究平台 - 测试覆盖率报告</h1>
    <p class="timestamp">生成时间: {{timestamp}}</p>
  </header>
  
  <h2>覆盖率摘要</h2>
  
  <div class="summary">
    <div class="summary-item">
      <h3>行覆盖率</h3>
      <div class="progress">
        <div class="progress-bar {{linesCoverageClass}}" style="width: {{linesCoverage}}%"></div>
      </div>
      <p class="percentage">{{linesCoverage}}%</p>
    </div>
    
    <div class="summary-item">
      <h3>函数覆盖率</h3>
      <div class="progress">
        <div class="progress-bar {{functionsCoverageClass}}" style="width: {{functionsCoverage}}%"></div>
      </div>
      <p class="percentage">{{functionsCoverage}}%</p>
    </div>
    
    <div class="summary-item">
      <h3>分支覆盖率</h3>
      <div class="progress">
        <div class="progress-bar {{branchesCoverageClass}}" style="width: {{branchesCoverage}}%"></div>
      </div>
      <p class="percentage">{{branchesCoverage}}%</p>
    </div>
    
    <div class="summary-item">
      <h3>语句覆盖率</h3>
      <div class="progress">
        <div class="progress-bar {{statementsCoverageClass}}" style="width: {{statementsCoverage}}%"></div>
      </div>
      <p class="percentage">{{statementsCoverage}}%</p>
    </div>
  </div>
  
  <h2>文件覆盖率详情</h2>
  
  <table>
    <thead>
      <tr>
        <th>文件</th>
        <th>行覆盖率</th>
        <th>函数覆盖率</th>
        <th>分支覆盖率</th>
        <th>语句覆盖率</th>
      </tr>
    </thead>
    <tbody>
      {{#each files}}
      <tr>
        <td class="file-path">{{path}}</td>
        <td><span class="badge {{linesCoverageClass}}">{{linesCoverage}}%</span></td>
        <td><span class="badge {{functionsCoverageClass}}">{{functionsCoverage}}%</span></td>
        <td><span class="badge {{branchesCoverageClass}}">{{branchesCoverage}}%</span></td>
        <td><span class="badge {{statementsCoverageClass}}">{{statementsCoverage}}%</span></td>
      </tr>
      {{/each}}
    </tbody>
  </table>
  
  <h2>覆盖率最低的文件</h2>
  
  <h3>行覆盖率最低的文件</h3>
  <table>
    <thead>
      <tr>
        <th>文件</th>
        <th>行覆盖率</th>
      </tr>
    </thead>
    <tbody>
      {{#each lowestLinesCoverage}}
      <tr>
        <td class="file-path">{{path}}</td>
        <td><span class="badge {{coverageClass}}">{{coverage}}%</span></td>
      </tr>
      {{/each}}
    </tbody>
  </table>
  
  <h3>函数覆盖率最低的文件</h3>
  <table>
    <thead>
      <tr>
        <th>文件</th>
        <th>函数覆盖率</th>
      </tr>
    </thead>
    <tbody>
      {{#each lowestFunctionsCoverage}}
      <tr>
        <td class="file-path">{{path}}</td>
        <td><span class="badge {{coverageClass}}">{{coverage}}%</span></td>
      </tr>
      {{/each}}
    </tbody>
  </table>
  
  <h3>分支覆盖率最低的文件</h3>
  <table>
    <thead>
      <tr>
        <th>文件</th>
        <th>分支覆盖率</th>
      </tr>
    </thead>
    <tbody>
      {{#each lowestBranchesCoverage}}
      <tr>
        <td class="file-path">{{path}}</td>
        <td><span class="badge {{coverageClass}}">{{coverage}}%</span></td>
      </tr>
      {{/each}}
    </tbody>
  </table>
  
  <footer>
    <p>和富家族研究平台 &copy; 2023</p>
  </footer>
</body>
</html>
