/**
 * 基本测试
 * 
 * 用于验证Jest测试框架是否正常工作
 */

describe('基本测试', () => {
  test('1 + 1 应该等于 2', () => {
    expect(1 + 1).toBe(2);
  });

  test('字符串连接', () => {
    expect('hello' + ' ' + 'world').toBe('hello world');
  });

  test('布尔值测试', () => {
    expect(true).toBeTruthy();
    expect(false).toBeFalsy();
  });

  test('对象测试', () => {
    const obj = { name: 'test', value: 123 };
    expect(obj).toHaveProperty('name', 'test');
    expect(obj).toHaveProperty('value', 123);
  });

  test('数组测试', () => {
    const arr = [1, 2, 3, 4, 5];
    expect(arr).toHaveLength(5);
    expect(arr).toContain(3);
    expect(arr).not.toContain(6);
  });
});
