/**
 * 头像服务
 *
 * 处理头像上传和获取的API请求
 */

import apiService from './api-service';

// 存储用户头像的缓存
const avatarCache: { [userId: number]: string } = {};

/**
 * 上传头像
 * @param avatarData 头像数据（base64格式）
 */
export const uploadAvatar = async (avatarData: string): Promise<any> => {
  console.log('调用头像上传API，数据长度:', avatarData.length);

  try {
    const response = await apiService.post('/avatar/upload', {
      avatar_data: avatarData
    });

    console.log('头像上传API响应:', response);

    // 如果上传成功，将头像数据缓存起来
    if (response.success && response.data && response.data.id) {
      avatarCache[response.data.id] = avatarData;
    }

    return response;
  } catch (error) {
    console.error('头像上传失败:', error);
    throw error;
  }
};

/**
 * 获取头像URL
 * @param userId 用户ID
 */
export const getAvatarUrl = (userId: number): string => {
  // 硬编码API基础URL，确保使用正确的URL
  const apiBaseUrl = 'http://localhost:5001/api';

  // 添加随机参数以避免缓存
  const timestamp = new Date().getTime();

  // 直接返回完整的URL
  const url = `${apiBaseUrl}/avatar/${userId}?t=${timestamp}`;
  console.log(`构建头像URL: ${url}`);
  return url;
};

/**
 * 获取用户头像数据
 * @param userId 用户ID
 * @returns 头像数据的Promise
 */
export const getAvatarData = async (userId: number): Promise<string> => {
  // 如果缓存中有头像数据，直接返回
  if (avatarCache[userId]) {
    return avatarCache[userId];
  }

  try {
    // 从localStorage中获取用户信息
    const userInfoStr = localStorage.getItem('hefamily_user_info');
    if (userInfoStr) {
      const userInfo = JSON.parse(userInfoStr);
      if (userInfo.avatar) {
        // 缓存头像数据
        avatarCache[userId] = userInfo.avatar;
        return userInfo.avatar;
      }
    }

    // 如果localStorage中没有头像数据，则使用默认头像
    return '';
  } catch (error) {
    console.error('获取头像数据失败:', error);
    return '';
  }
};

export default {
  uploadAvatar,
  getAvatarUrl,
  getAvatarData
};
