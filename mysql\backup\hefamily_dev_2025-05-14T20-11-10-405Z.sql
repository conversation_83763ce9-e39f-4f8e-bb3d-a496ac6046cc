-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: localhost    Database: hefamily_dev
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `activities`
--

DROP TABLE IF EXISTS `activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activities` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `date` datetime NOT NULL,
  `description` text NOT NULL,
  `image` varchar(255) DEFAULT NULL COMMENT '活动图片路径',
  `status` enum('published','draft','archived') NOT NULL DEFAULT 'draft' COMMENT '活动状态：已发布、草稿、已归档',
  `creator_id` int NOT NULL,
  `last_updated_by` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `creator_id` (`creator_id`),
  KEY `last_updated_by` (`last_updated_by`),
  CONSTRAINT `activities_ibfk_1` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `activities_ibfk_2` FOREIGN KEY (`last_updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `activities`
--

LOCK TABLES `activities` WRITE;
/*!40000 ALTER TABLE `activities` DISABLE KEYS */;
INSERT INTO `activities` VALUES (48,'2414124','2025-05-14 00:00:00','12412','/placeholder.svg?height=200&width=300','published',1,1,'2025-05-14 17:47:02','2025-05-14 17:47:03');
/*!40000 ALTER TABLE `activities` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `activity_attachments`
--

DROP TABLE IF EXISTS `activity_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activity_attachments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `activity_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `path` varchar(255) NOT NULL COMMENT '文件存储路径',
  `type` varchar(50) NOT NULL COMMENT '文件类型，如pdf, doc, jpg等',
  `mime_type` varchar(100) NOT NULL COMMENT '文件MIME类型',
  `size` bigint NOT NULL COMMENT '文件大小（字节）',
  `uploader_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `activity_id` (`activity_id`),
  KEY `uploader_id` (`uploader_id`),
  CONSTRAINT `activity_attachments_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `activity_attachments_ibfk_2` FOREIGN KEY (`uploader_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `activity_attachments`
--

LOCK TABLES `activity_attachments` WRITE;
/*!40000 ALTER TABLE `activity_attachments` DISABLE KEYS */;
/*!40000 ALTER TABLE `activity_attachments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ai_assistants`
--

DROP TABLE IF EXISTS `ai_assistants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_assistants` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` enum('personal','data-query','assistant','knowledge-file') NOT NULL COMMENT '助手类型：个人专题助手、数据查询助手、AI研究助手、知识库文件分析助手',
  `description` text,
  `api_key` text NOT NULL,
  `api_endpoint` varchar(255) NOT NULL COMMENT 'Dify API端点',
  `app_id` varchar(100) NOT NULL COMMENT 'Dify应用ID',
  `app_code` varchar(100) NOT NULL COMMENT 'Dify应用代码',
  `upload_api_path` varchar(255) DEFAULT NULL COMMENT '文件上传API路径（仅知识库文件分析助手使用）',
  `analysis_api_path` varchar(255) DEFAULT NULL COMMENT '文件分析API路径（仅知识库文件分析助手使用）',
  `tags` varchar(255) DEFAULT NULL COMMENT '助手标签，用于分类和筛选（仅AI研究助手使用）',
  `initial_message` text COMMENT '初始对话消息',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否为系统预设助手，系统预设助手不可删除',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '助手状态：活跃、非活跃',
  `creator_id` int NOT NULL,
  `last_updated_by` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `creator_id` (`creator_id`),
  KEY `last_updated_by` (`last_updated_by`),
  CONSTRAINT `ai_assistants_ibfk_1` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `ai_assistants_ibfk_2` FOREIGN KEY (`last_updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ai_assistants`
--

LOCK TABLES `ai_assistants` WRITE;
/*!40000 ALTER TABLE `ai_assistants` DISABLE KEYS */;
INSERT INTO `ai_assistants` VALUES (2,'数据查询助手','data-query','用于数据查询页面的AI助手，可以帮助用户查询和分析数据','eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2MGYwMzUxNy1jYjVkLTRiYmUtOTRiMy02YTc1ZWU3OGIxMzYiLCJzdWIiOiJXZWIgQVBJIFBhc3Nwb3J0IiwiYXBwX2lkIjoiNjBmMDM1MTctY2I1ZC00YmJlLTk0YjMtNmE3NWVlNzhiMTM2IiwiYXBwX2NvZGUiOiJjTTZ3eFFwWHVQSmRya1Y5IiwiZW5kX3VzZXJfaWQiOiI0OGQ3MDBjNi01YWE2LTQ4ODItYjIzYi1jYWE2Yjc5OGM2MTgifQ.lkkCIz6gocVnKbutEBZqrZPZBYQZwsyRXczaGTaYCiU','https://ai.glab.vip/api/chat-messages','app_data_query','data_query_assistant',NULL,NULL,'','您好，我是数据查询助手，请问您想查询什么数据？',1,'active',1,1,'2025-05-05 03:09:26','2025-05-12 18:48:57'),(4,'知识库文件同步助手','knowledge-file','用于分析上传到知识库的文件，支持系统知识库和用户知识库','dataset-DLFJlUe25VUHOwMO4HbO4hQk','https://ai.glab.vip','77199451-730a-4d79-a1c9-9b9e6bfcd747','602d59cf-3384-4105-bf91-e1481b30b6b2','/v1/datasets/{datasetId}/document/create-by-file','/v1/datasets/{datasetId}/document/create-by-file','知识库,  文件分析','我是知识库文件分析助手，可以帮助您分析上传的文件。',1,'active',1,1,'2025-05-05 03:09:26','2025-05-14 13:44:49'),(5,'蔡和森专题助手','personal','蔡和森专题研究助手，可以回答关于蔡和森生平、思想和贡献的问题','eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI1OWJiNTUyNi03M2RjLTQzOTQtOWM2Ny0wNjZlYTc4MzExZTUiLCJzdWIiOiJXZWIgQVBJIFBhc3Nwb3J0IiwiYXBwX2lkIjoiNTliYjU1MjYtNzNkYy00Mzk0LTljNjctMDY2ZWE3ODMxMWU1IiwiYXBwX2NvZGUiOiJKRHVRczdiMUVDb0ptd1JQIiwiZW5kX3VzZXJfaWQiOiIzZjIwMDUzMy05ODZjLTQ0YzUtYTY5YS0wODliMGE0NjIzZTIifQ.AmYM4HSpWVujTQKNRUY4Ux9tDJMbkHr-tWjMDpz5ONo','http://ai.glab.vip/v1/chat-messages','','',NULL,NULL,'个人专题,蔡和森,中共创建','您好！我是蔡和森研究助手，很高兴为您提供关于蔡和森生平、思想和贡献的信息。请问有什么可以帮助您的吗？',1,'active',1,1,'2025-05-08 11:58:19','2025-05-08 12:29:28'),(6,'蔡畅专题助手','personal','蔡畅专题研究助手，可以回答关于蔡畅生平、思想和贡献的问题','eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2OGI4MzdmNi1kNTNlLTQxMDEtODhjYi1hM2NmNWM0Y2FhMzAiLCJzdWIiOiJXZWIgQVBJIFBhc3Nwb3J0IiwiYXBwX2lkIjoiNjhiODM3ZjYtZDUzZS00MTAxLTg4Y2ItYTNjZjVjNGNhYTMwIiwiYXBwX2NvZGUiOiJvZTlNeUd1ZTZGWjJsSFBRIiwiZW5kX3VzZXJfaWQiOiIyMzExZTU3Mi1iNzIxLTQ4MmQtYmU1Ny0yYjE3YjQwNTI0NDUifQ.4JM7KatYR0laDUxi98o0Jwh0oGYumcoB5yDm591EVqA','http://ai.glab.vip/v1/chat-messages','','',NULL,NULL,'个人专题,蔡畅,妇女运动','您好！我是蔡畅研究助手，很高兴为您提供关于蔡畅生平、思想和贡献的信息。请问有什么可以帮助您的吗？',1,'active',1,1,'2025-05-08 11:58:19','2025-05-08 05:44:28'),(7,'李富春专题助手','personal','李富春专题研究助手，可以回答关于李富春生平、思想和贡献的问题','eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI4NzBlOTc5YS1kZDEyLTRlMzktOTI1MC1mNWNiZjFiYjE1NWYiLCJzdWIiOiJXZWIgQVBJIFBhc3Nwb3J0IiwiYXBwX2lkIjoiODcwZTk3OWEtZGQxMi00ZTM5LTkyNTAtZjVjYmYxYmIxNTVmIiwiYXBwX2NvZGUiOiJOZnJnVUgzZVdFeVRHZ1R2IiwiZW5kX3VzZXJfaWQiOiJmMGM3M2NiOS05OWIwLTRiZDYtOTYxYy1lZDc1Njk4ZGNlYTEifQ.ul4wwvds_zOlsGCZ-G2FcrbJB7gUEEB5-GMQpgl7Gig','http://ai.glab.vip/v1/chat-messages','','',NULL,NULL,'个人专题,李富春,经济建设','您好！我是李富春研究助手，很高兴为您提供关于李富春生平、思想和贡献的信息。请问有什么可以帮助您的吗？',1,'active',1,1,'2025-05-08 11:58:19','2025-05-08 05:34:54'),(8,'葛健豪专题助手','personal','葛健豪专题研究助手，可以回答关于葛健豪生平、思想和贡献的问题','eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI1MjU0MGE3NC0wNWE2LTQxZjYtOGQwMi01MTQyODM5YTEyYmMiLCJzdWIiOiJXZWIgQVBJIFBhc3Nwb3J0IiwiYXBwX2lkIjoiNTI1NDBhNzQtMDVhNi00MWY2LThkMDItNTE0MjgzOWExMmJjIiwiYXBwX2NvZGUiOiJ5MTRzejE0bjJGeWlYVnZqIiwiZW5kX3VzZXJfaWQiOiI1YzA3YjJiYS01MjczLTQwZDEtODQxNi05N2FiNTYxMmI3NWIifQ.FcB_Kg5W1o3F4Ty38HYrvaTHdfudBjYMP9J8EP0ENGE','http://ai.glab.vip/v1/chat-messages','','',NULL,NULL,'个人专题,葛健豪,革命斗争','您好！我是葛健豪研究助手，很高兴为您提供关于葛健豪生平、思想和贡献的信息。请问有什么可以帮助您的吗？',1,'active',1,1,'2025-05-08 11:58:19','2025-05-08 12:48:12'),(9,'向警予专题助手','personal','向警予专题研究助手，可以回答关于向警予生平、思想和贡献的问题','eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiIxMTZkNzkwYi1mZWQxLTRkZjUtYmJlNi04MGYzODdiZGJmZjQiLCJzdWIiOiJXZWIgQVBJIFBhc3Nwb3J0IiwiYXBwX2lkIjoiMTE2ZDc5MGItZmVkMS00ZGY1LWJiZTYtODBmMzg3YmRiZmY0IiwiYXBwX2NvZGUiOiJ2Skl1S3c5SktLb0JiV29GIiwiZW5kX3VzZXJfaWQiOiI2OTI2MmY3OC1kNTgzLTQyMDMtYmY1Yi05ZTI2NTU2M2UzMDIifQ.iSrQs3MBtgb1wEmciAgYbcNEIQo0JrFDOxnt7YVJOjc','http://ai.glab.vip/v1/chat-messages','','',NULL,NULL,'个人专题,向警予,妇女运动','您好！我是向警予研究助手，很高兴为您提供关于向警予生平、思想和贡献的信息。请问有什么可以帮助您的吗？',1,'active',1,1,'2025-05-08 11:58:19','2025-05-08 12:48:02'),(10,'论文标题','assistant','论文标题','eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2NTM2YjI2MS0yMzQwLTQxMmYtOGZjYS0yMzA3NTE4N2YxOGYiLCJzdWIiOiJXZWIgQVBJIFBhc3Nwb3J0IiwiYXBwX2lkIjoiNjUzNmIyNjEtMjM0MC00MTJmLThmY2EtMjMwNzUxODdmMThmIiwiYXBwX2NvZGUiOiJFQjh2QzZOekJBajhTSkw4IiwiZW5kX3VzZXJfaWQiOiI4MjQ3ZWQyYy05ZmIwLTQxY2EtOTE0ZS0xZWY0MDE4ZjVlNWIifQ.eNifi182ttsoNRJaOropCal7M0jMVAtW8IEn_Yu0_9o','https://ai.glab.vip/api/chat-messages','','',NULL,NULL,'学术研究',NULL,0,'active',1,1,'2025-05-08 22:39:58','2025-05-08 22:39:58'),(11,'课题分析','assistant','课题分析课题分析','eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI0ZTIxMGRmNi02ZTYwLTQ2NWUtOGZlZC00ZWRjZTM3YTRjY2IiLCJzdWIiOiJXZWIgQVBJIFBhc3Nwb3J0IiwiYXBwX2lkIjoiNGUyMTBkZjYtNmU2MC00NjVlLThmZWQtNGVkY2UzN2E0Y2NiIiwiYXBwX2NvZGUiOiJ5NWdTRVBmbnJjdkd6ek9zIiwiZW5kX3VzZXJfaWQiOiIzOTUzMWM5YS1kMTcxLTQzYTAtOGIyNi02ODZiOGU2OTFkYmQifQ.Jto93j6MakmOGx4KquqWl8mw_QOHkLHQg_1gdD0oyZc','https://ai.glab.vip/api/chat-messages','','',NULL,NULL,'全能助手,  研究辅助',NULL,0,'active',1,1,'2025-05-09 01:43:21','2025-05-09 01:43:45');
/*!40000 ALTER TABLE `ai_assistants` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `comments`
--

DROP TABLE IF EXISTS `comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '评论者ID',
  `content` text NOT NULL,
  `topic_id` int NOT NULL COMMENT '评论主题ID',
  `topic_type` varchar(50) NOT NULL COMMENT '评论主题类型，如personal_topic, activity等',
  `topic_title` varchar(255) DEFAULT NULL COMMENT '评论主题标题',
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending' COMMENT '评论状态：待审核、已批准、已拒绝',
  `reviewer_id` int DEFAULT NULL COMMENT '审核人ID',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reject_reason` text COMMENT '拒绝原因',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `reviewer_id` (`reviewer_id`),
  CONSTRAINT `comments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `comments_ibfk_2` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `comments`
--

LOCK TABLES `comments` WRITE;
/*!40000 ALTER TABLE `comments` DISABLE KEYS */;
INSERT INTO `comments` VALUES (12,833,'测试',1,'personal_topic','蔡和森','pending',NULL,NULL,NULL,'2025-05-07 06:15:59','2025-05-07 06:15:59'),(13,833,'测试',1,'personal_topic','蔡和森','pending',NULL,NULL,NULL,'2025-05-07 06:20:59','2025-05-07 06:20:59'),(14,833,'测试',1,'personal_topic','蔡和森','pending',NULL,NULL,NULL,'2025-05-07 06:21:05','2025-05-07 06:21:05'),(15,833,'ceshi',1,'personal_topic','蔡和森','rejected',1,NULL,NULL,'2025-05-07 06:33:33','2025-05-14 15:02:03'),(16,833,'ceshi',1,'personal_topic','蔡和森','rejected',1,NULL,NULL,'2025-05-07 06:34:31','2025-05-14 15:02:02'),(17,833,'ceshi',1,'personal_topic','蔡和森','rejected',1,NULL,NULL,'2025-05-07 06:42:59','2025-05-14 15:02:02'),(18,833,'ceshi',1,'personal_topic','蔡和森','rejected',1,NULL,NULL,'2025-05-07 06:49:07','2025-05-14 15:02:01'),(19,833,'ceshi',1,'personal_topic','蔡和森','rejected',1,NULL,NULL,'2025-05-07 06:49:17','2025-05-14 15:02:00'),(20,833,'ceshi',1,'personal_topic','蔡和森','rejected',1,NULL,NULL,'2025-05-07 06:50:31','2025-05-14 15:01:58'),(21,833,'测试',1,'personal_topic','蔡和森','rejected',1,NULL,NULL,'2025-05-07 06:54:54','2025-05-14 15:01:57'),(28,833,'111',4,'personal_topic','葛健豪','pending',NULL,NULL,NULL,'2025-05-14 16:34:10','2025-05-14 16:34:10');
/*!40000 ALTER TABLE `comments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `files`
--

DROP TABLE IF EXISTS `files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `files` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `path` varchar(255) NOT NULL COMMENT '文件存储路径',
  `type` varchar(50) NOT NULL COMMENT '文件类型，如pdf, doc, jpg等',
  `mime_type` varchar(100) NOT NULL COMMENT '文件MIME类型',
  `size` bigint NOT NULL COMMENT '文件大小（字节）',
  `knowledge_base_id` int NOT NULL,
  `uploader_id` int NOT NULL,
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending' COMMENT '文件状态：待审核、已批准、已拒绝',
  `reviewer_id` int DEFAULT NULL,
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reject_reason` text COMMENT '拒绝原因',
  `summary` text COMMENT '文件内容摘要（由AI生成）',
  `detailed_description` text COMMENT '文件详细描述（由AI生成）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `dify_task_id` varchar(100) DEFAULT NULL COMMENT 'Dify分析任务ID',
  `analysis_status` enum('pending','processing','completed','failed') DEFAULT 'pending' COMMENT '分析状态：待分析、处理中、已完成、失败',
  `analysis_started_at` datetime DEFAULT NULL COMMENT '分析开始时间',
  `analysis_completed_at` datetime DEFAULT NULL COMMENT '分析完成时间',
  `analysis_error` text COMMENT '分析错误信息',
  PRIMARY KEY (`id`),
  KEY `knowledge_base_id` (`knowledge_base_id`),
  KEY `uploader_id` (`uploader_id`),
  KEY `reviewer_id` (`reviewer_id`),
  CONSTRAINT `files_ibfk_1` FOREIGN KEY (`knowledge_base_id`) REFERENCES `knowledge_bases` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `files_ibfk_2` FOREIGN KEY (`uploader_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `files_ibfk_3` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=121 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `files`
--

LOCK TABLES `files` WRITE;
/*!40000 ALTER TABLE `files` DISABLE KEYS */;
INSERT INTO `files` VALUES (66,'1746785690094-584167651.doc','3.向警予 (1).doc','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1746785690094-584167651.doc','doc','application/msword',44032,75,1,'approved',1,'2025-05-09 10:14:50',NULL,'','','2025-05-09 10:14:50','2025-05-09 10:14:50',NULL,'pending',NULL,NULL,NULL),(67,'1746786441619-254033510.txt','API文档.txt','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1746786441619-254033510.txt','txt','text/plain',33475,75,1,'approved',1,'2025-05-09 10:27:21',NULL,'','','2025-05-09 10:27:21','2025-05-09 10:27:21',NULL,'pending',NULL,NULL,NULL),(69,'1746966989340-512214193.docx','李富春年谱(1)2021年2月.docx','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1746966989340-512214193.docx','docx','application/vnd.openxmlformats-officedocument.wordprocessingml.document',2044600,75,833,'rejected',1,'2025-05-11 13:27:28',NULL,NULL,NULL,'2025-05-11 12:36:29','2025-05-11 13:27:28',NULL,'pending',NULL,NULL,NULL),(70,'1746969542320-212527429.doc','1111111.doc','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1746969542320-212527429.doc','doc','application/msword',9216,75,833,'approved',1,'2025-05-11 13:22:54',NULL,'分析结果为空','分析结果为空','2025-05-11 13:19:02','2025-05-11 14:03:11',NULL,'failed','2025-05-11 13:22:54','2025-05-11 13:22:54','Request failed with status code 401'),(71,'1746970108354-25637249.docx','李富春年谱(1)2021年2月.docx','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1746970108354-25637249.docx','docx','application/vnd.openxmlformats-officedocument.wordprocessingml.document',2044600,75,833,'approved',1,'2025-05-11 13:29:04',NULL,'分析结果为空','分析结果为空','2025-05-11 13:28:28','2025-05-11 14:01:39',NULL,'failed','2025-05-11 13:29:04','2025-05-11 13:29:04','Request failed with status code 401'),(98,'1746980962819-82101509.png','微信图片_20250508154340.png','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1746980962819-82101509.png','png','image/png',242487,75,1,'approved',1,'2025-05-11 16:29:22',NULL,NULL,NULL,'2025-05-11 16:29:22','2025-05-11 16:29:23','ba04409c-81ae-4f59-83bc-e4db0bb6c1a7','completed',NULL,'2025-05-11 16:29:23',NULL),(102,'1747059354702-73173090.docx','内部汇报.docx','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747059354702-73173090.docx','docx','application/vnd.openxmlformats-officedocument.wordprocessingml.document',10055,76,833,'approved',1,'2025-05-12 14:24:20',NULL,NULL,NULL,'2025-05-12 14:15:54','2025-05-12 14:24:21','338bf459-8965-4521-9aaa-7eabff9f7cd3','completed',NULL,'2025-05-12 14:24:21',NULL),(103,'1747060560655-372760094.docx','内部汇报 - 副本.docx','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747060560655-372760094.docx','docx','application/vnd.openxmlformats-officedocument.wordprocessingml.document',10184,76,1,'approved',1,'2025-05-12 14:36:00',NULL,NULL,NULL,'2025-05-12 14:36:00','2025-05-12 14:36:00','d698915e-edd0-46b6-8acc-c0f60244b270','completed',NULL,'2025-05-12 14:36:00',NULL),(104,'1747210886779-990346174.doc','3.向警予.doc','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747210886779-990346174.doc','doc','application/msword',44032,75,833,'pending',NULL,NULL,NULL,NULL,NULL,'2025-05-14 08:21:26','2025-05-14 08:21:26',NULL,'pending',NULL,NULL,NULL),(105,'1747210910884-680291552.doc','3.向警予.doc','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747210910884-680291552.doc','doc','application/msword',44032,76,833,'pending',NULL,NULL,NULL,NULL,NULL,'2025-05-14 08:21:50','2025-05-14 08:21:50',NULL,'pending',NULL,NULL,NULL),(106,'1747227146154-799563707.doc','3.向警予.doc','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747227146154-799563707.doc','doc','application/msword',44032,77,1,'approved',1,'2025-05-14 12:52:26',NULL,NULL,NULL,'2025-05-14 12:52:26','2025-05-14 12:52:26',NULL,'failed',NULL,NULL,'Request failed with status code 404 (状态码: 404)'),(107,'1747227201900-603414681.doc','3.向警予.doc','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747227201900-603414681.doc','doc','application/msword',44032,76,1,'approved',1,'2025-05-14 12:53:21',NULL,NULL,NULL,'2025-05-14 12:53:21','2025-05-14 12:53:22',NULL,'failed',NULL,NULL,'Request failed with status code 404 (状态码: 404)'),(108,'1747227681676-517771627.doc','3.向警予.doc','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747227681676-517771627.doc','doc','application/msword',44032,75,1,'approved',1,'2025-05-14 13:01:21',NULL,NULL,NULL,'2025-05-14 13:01:21','2025-05-14 13:01:22','1b868daa-49f2-416c-b763-cd9296b4e628','completed',NULL,'2025-05-14 13:01:22',NULL),(109,'1747227696207-106005736.doc','3.向警予.doc','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747227696207-106005736.doc','doc','application/msword',44032,77,1,'approved',1,'2025-05-14 13:01:36',NULL,NULL,NULL,'2025-05-14 13:01:36','2025-05-14 13:01:36','7f429825-91fa-43ce-ae40-5cca0398bec8','completed',NULL,'2025-05-14 13:01:36',NULL),(110,'1747228137349-768612338.txt','上传图片报错信息.txt','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747228137349-768612338.txt','txt','text/plain',41294,76,1,'approved',1,'2025-05-14 13:08:57',NULL,NULL,NULL,'2025-05-14 13:08:57','2025-05-14 13:08:57','a5162f49-ba36-4a3c-b814-063db7c57e5a','completed',NULL,'2025-05-14 13:08:57',NULL),(111,'1747228157970-17338165.txt','上传图片报错信息.txt','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747228157970-17338165.txt','txt','text/plain',41294,77,1,'approved',1,'2025-05-14 13:09:17',NULL,NULL,NULL,'2025-05-14 13:09:17','2025-05-14 13:09:18','256f0dac-cde6-4bb4-8aa0-e2e9e1387722','completed',NULL,'2025-05-14 13:09:18',NULL),(112,'1747234650581-812961172.txt','<EMAIL>','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747234650581-812961172.txt','txt','text/plain',137,77,1,'approved',1,'2025-05-14 14:57:30',NULL,NULL,NULL,'2025-05-14 14:57:30','2025-05-14 14:57:30','336b0a0a-0d74-477d-ac7f-08de7937d0a4','completed',NULL,'2025-05-14 14:57:30',NULL),(113,'1747234665360-997730791.txt','<EMAIL>','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747234665360-997730791.txt','txt','text/plain',137,76,1,'approved',1,'2025-05-14 14:57:45',NULL,NULL,NULL,'2025-05-14 14:57:45','2025-05-14 14:57:45','a655514f-02a7-4c81-a5c3-461cc11c1b90','completed',NULL,'2025-05-14 14:57:45',NULL),(114,'1747240637906-83946007.txt','API文档.txt','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747240637906-83946007.txt','txt','text/plain',33475,78,833,'approved',833,'2025-05-14 16:37:17',NULL,NULL,NULL,'2025-05-14 16:37:17','2025-05-14 16:37:18','d08d282c-79ff-48ac-bb31-ffda1131e446','completed',NULL,'2025-05-14 16:37:18',NULL),(115,'1747240651593-155445399.txt','API文档.txt','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747240651593-155445399.txt','txt','text/plain',33475,76,833,'pending',NULL,NULL,NULL,NULL,NULL,'2025-05-14 16:37:31','2025-05-14 16:37:31',NULL,'pending',NULL,NULL,NULL),(116,'1747240686735-832960335.txt','API文档.txt','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747240686735-832960335.txt','txt','text/plain',33475,76,833,'pending',NULL,NULL,NULL,NULL,NULL,'2025-05-14 16:38:06','2025-05-14 16:38:06',NULL,'pending',NULL,NULL,NULL),(117,'1747247553865-29526783.txt','API文档.txt','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747247553865-29526783.txt','txt','text/plain',33475,78,833,'approved',833,'2025-05-14 18:32:33',NULL,NULL,NULL,'2025-05-14 18:32:33','2025-05-14 18:32:34','79ef13dd-c0a8-4fb9-bcb3-b7bcc5816a3c','completed',NULL,'2025-05-14 18:32:34',NULL),(118,'1747247570957-323265917.txt','<EMAIL>','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747247570957-323265917.txt','txt','text/plain',137,78,833,'approved',833,'2025-05-14 18:32:50',NULL,NULL,NULL,'2025-05-14 18:32:50','2025-05-14 18:32:51','273887ac-a867-4dae-9b1f-2e1a4dccca9b','completed',NULL,'2025-05-14 18:32:51',NULL),(119,'1747247608055-538855153.txt','<EMAIL>','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747247608055-538855153.txt','txt','text/plain',137,76,833,'pending',NULL,NULL,NULL,NULL,NULL,'2025-05-14 18:33:28','2025-05-14 18:33:28',NULL,'pending',NULL,NULL,NULL),(120,'1747247620053-223855759.doc','和富家族研究平台项目汇报文档.doc','C:\\Users\\<USER>\\Desktop\\P\\backend\\uploads\\1747247620053-223855759.doc','doc','application/msword',17408,76,833,'rejected',1,'2025-05-14 18:34:08',NULL,NULL,NULL,'2025-05-14 18:33:40','2025-05-14 18:34:08',NULL,'pending',NULL,NULL,NULL);
/*!40000 ALTER TABLE `files` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `knowledge_base_access`
--

DROP TABLE IF EXISTS `knowledge_base_access`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `knowledge_base_access` (
  `id` int NOT NULL AUTO_INCREMENT,
  `knowledge_base_id` int NOT NULL,
  `user_id` int NOT NULL,
  `access_type` enum('read','write','admin') NOT NULL DEFAULT 'read' COMMENT '访问权限类型：read-只读, write-读写, admin-管理',
  `granted_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `knowledge_base_access_kb_id_user_id_unique` (`knowledge_base_id`,`user_id`),
  KEY `user_id` (`user_id`),
  KEY `granted_by` (`granted_by`),
  CONSTRAINT `knowledge_base_access_ibfk_1` FOREIGN KEY (`knowledge_base_id`) REFERENCES `knowledge_bases` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `knowledge_base_access_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `knowledge_base_access_ibfk_3` FOREIGN KEY (`granted_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `knowledge_base_access`
--

LOCK TABLES `knowledge_base_access` WRITE;
/*!40000 ALTER TABLE `knowledge_base_access` DISABLE KEYS */;
INSERT INTO `knowledge_base_access` VALUES (4,77,1,'admin',1,'2025-05-12 14:25:40','2025-05-12 14:25:40'),(6,78,833,'admin',833,'2025-05-14 14:39:23','2025-05-14 14:39:23');
/*!40000 ALTER TABLE `knowledge_base_access` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `knowledge_base_access_requests`
--

DROP TABLE IF EXISTS `knowledge_base_access_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `knowledge_base_access_requests` (
  `id` int NOT NULL AUTO_INCREMENT,
  `knowledge_base_id` int NOT NULL,
  `user_id` int NOT NULL,
  `reason` text,
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
  `reviewed_by` int DEFAULT NULL,
  `reviewed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `knowledge_base_id` (`knowledge_base_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `knowledge_base_access_requests_ibfk_3` (`reviewed_by`),
  CONSTRAINT `knowledge_base_access_requests_ibfk_1` FOREIGN KEY (`knowledge_base_id`) REFERENCES `knowledge_bases` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `knowledge_base_access_requests_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `knowledge_base_access_requests_ibfk_3` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `knowledge_base_access_requests`
--

LOCK TABLES `knowledge_base_access_requests` WRITE;
/*!40000 ALTER TABLE `knowledge_base_access_requests` DISABLE KEYS */;
INSERT INTO `knowledge_base_access_requests` VALUES (2,77,833,'woxiangfangwen','approved',1,'2025-05-12 14:26:47','2025-05-12 14:26:23','2025-05-12 14:26:47');
/*!40000 ALTER TABLE `knowledge_base_access_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `knowledge_bases`
--

DROP TABLE IF EXISTS `knowledge_bases`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `knowledge_bases` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `type` enum('system','user') NOT NULL DEFAULT 'user' COMMENT '知识库类型：系统知识库或用户知识库',
  `creator_id` int NOT NULL,
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系人电话',
  `storage_size` bigint DEFAULT '0' COMMENT '知识库存储大小（字节）',
  `file_count` int DEFAULT '0' COMMENT '知识库文件数量',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `creator_id` (`creator_id`),
  CONSTRAINT `knowledge_bases_ibfk_1` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `knowledge_bases`
--

LOCK TABLES `knowledge_bases` WRITE;
/*!40000 ALTER TABLE `knowledge_bases` DISABLE KEYS */;
INSERT INTO `knowledge_bases` VALUES (75,'测试系统知识库','测试14212412341','system',1,NULL,NULL,2417842,6,'2025-05-09 10:04:13','2025-05-14 14:18:42'),(76,'111','111','system',1,NULL,NULL,105702,5,'2025-05-12 14:14:19','2025-05-14 18:34:08'),(77,'测试访问','111','user',1,NULL,NULL,129495,4,'2025-05-12 14:25:40','2025-05-14 14:57:30'),(78,'12412412','3123123','user',833,NULL,NULL,67087,3,'2025-05-14 14:39:23','2025-05-14 18:32:50');
/*!40000 ALTER TABLE `knowledge_bases` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '通知接收者ID',
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `type` enum('system','activity','file','comment','file_review','file_review_result') NOT NULL COMMENT '通知类型：系统通知、活动通知、文件通知、评论通知、文件审核通知、文件审核结果通知',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `related_id` int DEFAULT NULL COMMENT '相关对象ID',
  `related_type` varchar(50) DEFAULT NULL COMMENT '相关对象类型',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=434 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
INSERT INTO `notifications` VALUES (402,1,'评论已批准','您在\"蔡和森\"下的评论已被批准','comment',1,NULL,23,'comment','2025-05-07 13:53:39','2025-05-09 17:46:57'),(403,1,'评论已批准','您在\"蔡和森\"下的评论已被批准','comment',1,NULL,22,'comment','2025-05-08 04:47:11','2025-05-09 17:46:57'),(404,1,'新评论待审核','用户 admin 在\"向警予\"下发表了评论，请审核。','comment',1,NULL,24,'comment','2025-05-08 04:47:30','2025-05-09 17:46:57'),(405,1,'评论已批准','您在\"向警予\"下的评论已被批准','comment',1,NULL,24,'comment','2025-05-08 04:47:35','2025-05-09 17:46:57'),(406,1,'新评论待审核','用户 0508 在\"李富春\"下发表了评论，请审核。','comment',1,NULL,25,'comment','2025-05-08 06:33:53','2025-05-09 17:46:57'),(407,835,'评论已批准','您在\"李富春\"下的评论已被批准','comment',0,NULL,25,'comment','2025-05-08 06:34:22','2025-05-08 06:34:22'),(408,1,'新文件等待审核','用户 ceshi 上传了文件 \"1111111.doc\" 到知识库 \"测试系统知识库\"，请审核。','file_review',1,NULL,70,'file','2025-05-11 13:19:02','2025-05-11 14:19:46'),(409,833,'文件审核已通过','您上传到知识库 \"测试系统知识库\" 的文件 \"1111111.doc\" 已通过审核。','file_review_result',1,NULL,70,'file','2025-05-11 13:22:54','2025-05-11 13:28:35'),(410,833,'文件审核被拒绝','您上传到知识库 \"测试系统知识库\" 的文件 \"李富春年谱(1)2021年2月.docx\" 被拒绝。','file_review_result',1,NULL,69,'file','2025-05-11 13:27:28','2025-05-11 13:28:35'),(411,1,'新文件等待审核','用户 ceshi 上传了文件 \"李富春年谱(1)2021年2月.docx\" 到知识库 \"测试系统知识库\"，请审核。','file_review',1,NULL,71,'file','2025-05-11 13:28:28','2025-05-11 14:19:46'),(412,833,'文件审核已通过','您上传到知识库 \"测试系统知识库\" 的文件 \"李富春年谱(1)2021年2月.docx\" 已通过审核。','file_review_result',1,NULL,71,'file','2025-05-11 13:29:04','2025-05-12 13:55:30'),(413,1,'新评论待审核','用户 ceshi 在\"李富春\"下发表了评论，请审核。','comment',1,NULL,26,'comment','2025-05-12 13:56:07','2025-05-12 17:08:26'),(414,833,'评论已批准','您在\"李富春\"下的评论已被批准','comment',0,NULL,26,'comment','2025-05-12 13:56:27','2025-05-12 13:56:27'),(415,1,'新文件等待审核','用户 ceshi 上传了文件 \"内部汇报.docx\" 到知识库 \"111\"，请审核。','file_review',1,NULL,102,'file','2025-05-12 14:15:54','2025-05-12 17:08:26'),(416,833,'文件审核已通过','您上传到知识库 \"111\" 的文件 \"内部汇报.docx\" 已通过审核。','file_review_result',0,NULL,102,'file','2025-05-12 14:24:21','2025-05-12 14:24:21'),(417,1,'新文件等待审核','用户 ceshi 上传了文件 \"3.向警予.doc\" 到知识库 \"测试系统知识库\"，请审核。','file_review',0,NULL,104,'file','2025-05-14 08:21:26','2025-05-14 08:21:26'),(418,1,'新文件等待审核','用户 ceshi 上传了文件 \"3.向警予.doc\" 到知识库 \"111\"，请审核。','file_review',0,NULL,105,'file','2025-05-14 08:21:50','2025-05-14 08:21:50'),(419,1,'新评论待审核','用户 admin 在\"蔡和森\"下发表了评论，请审核。','comment',0,NULL,27,'comment','2025-05-14 12:35:27','2025-05-14 12:35:27'),(420,1,'评论未通过审核','您在\"蔡和森\"下的评论未通过审核','comment',0,NULL,27,'comment','2025-05-14 14:59:28','2025-05-14 14:59:28'),(421,833,'评论未通过审核','您在\"蔡和森\"下的评论未通过审核','comment',0,NULL,21,'comment','2025-05-14 15:01:57','2025-05-14 15:01:57'),(422,833,'评论未通过审核','您在\"蔡和森\"下的评论未通过审核','comment',0,NULL,20,'comment','2025-05-14 15:01:58','2025-05-14 15:01:58'),(423,833,'评论未通过审核','您在\"蔡和森\"下的评论未通过审核','comment',0,NULL,19,'comment','2025-05-14 15:02:00','2025-05-14 15:02:00'),(424,833,'评论未通过审核','您在\"蔡和森\"下的评论未通过审核','comment',0,NULL,18,'comment','2025-05-14 15:02:01','2025-05-14 15:02:01'),(425,833,'评论未通过审核','您在\"蔡和森\"下的评论未通过审核','comment',0,NULL,17,'comment','2025-05-14 15:02:02','2025-05-14 15:02:02'),(426,833,'评论未通过审核','您在\"蔡和森\"下的评论未通过审核','comment',0,NULL,16,'comment','2025-05-14 15:02:02','2025-05-14 15:02:02'),(427,833,'评论未通过审核','您在\"蔡和森\"下的评论未通过审核','comment',0,NULL,15,'comment','2025-05-14 15:02:03','2025-05-14 15:02:03'),(428,1,'新评论待审核','用户 ceshi 在\"葛健豪\"下发表了评论，请审核。','comment',0,NULL,28,'comment','2025-05-14 16:34:10','2025-05-14 16:34:10'),(429,1,'新文件等待审核','用户 ceshi 上传了文件 \"API文档.txt\" 到知识库 \"111\"，请审核。','file_review',0,NULL,115,'file','2025-05-14 16:37:31','2025-05-14 16:37:31'),(430,1,'新文件等待审核','用户 ceshi 上传了文件 \"API文档.txt\" 到知识库 \"111\"，请审核。','file_review',0,NULL,116,'file','2025-05-14 16:38:06','2025-05-14 16:38:06'),(431,1,'新文件等待审核','用户 ceshi 上传了文件 \"<EMAIL>\" 到知识库 \"111\"，请审核。','file_review',0,NULL,119,'file','2025-05-14 18:33:28','2025-05-14 18:33:28'),(432,1,'新文件等待审核','用户 ceshi 上传了文件 \"和富家族研究平台项目汇报文档.doc\" 到知识库 \"111\"，请审核。','file_review',0,NULL,120,'file','2025-05-14 18:33:40','2025-05-14 18:33:40'),(433,833,'文件审核被拒绝','您上传到知识库 \"111\" 的文件 \"和富家族研究平台项目汇报文档.doc\" 被拒绝。','file_review_result',0,NULL,120,'file','2025-05-14 18:34:08','2025-05-14 18:34:08');
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `code` varchar(50) NOT NULL COMMENT '权限代码，如content:view, content:edit等',
  `description` varchar(255) DEFAULT NULL,
  `module` varchar(50) NOT NULL COMMENT '权限所属模块，如content, user, system等',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=107 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'访问系统管理','system:access','访问系统管理页面','system','2025-05-05 03:08:24','2025-05-05 03:08:24'),(2,'用户管理','user:manage','管理用户（创建、编辑、删除）','system','2025-05-05 03:08:24','2025-05-05 03:08:24'),(3,'角色管理','role:manage','管理角色（创建、编辑、删除）','system','2025-05-05 03:08:24','2025-05-05 03:08:24'),(4,'权限管理','permission:manage','管理权限（分配权限）','system','2025-05-05 03:08:24','2025-05-05 03:08:24'),(5,'AI管理','ai:manage','管理AI助手（编辑配置）','system','2025-05-05 03:08:24','2025-05-05 03:08:24'),(6,'安全配置','security:manage','管理安全配置（密码策略等）','system','2025-05-05 03:08:24','2025-05-05 03:08:24'),(7,'访问知识库','knowledge:access','访问知识库页面','knowledge','2025-05-05 03:08:24','2025-05-05 03:08:24'),(8,'创建系统知识库','knowledge:create_system','创建系统知识库','knowledge','2025-05-05 03:08:24','2025-05-05 03:08:24'),(9,'创建用户知识库','knowledge:create_user','创建用户知识库','knowledge','2025-05-05 03:08:24','2025-05-05 03:08:24'),(10,'上传文件','file:upload','上传文件到知识库','knowledge','2025-05-05 03:08:24','2025-05-05 03:08:24'),(11,'审核文件','file:review','审核知识库文件','knowledge','2025-05-05 03:08:24','2025-05-05 03:08:24'),(12,'访问数据查询','data:access','访问数据查询页面','data','2025-05-05 03:08:24','2025-05-05 03:08:24'),(13,'使用AI查询','data:ai_query','使用AI助手进行数据查询','data','2025-05-05 03:08:24','2025-05-05 03:08:24'),(14,'管理活动','activity:manage','管理活动（创建、编辑、删除）','activity','2025-05-05 03:08:24','2025-05-05 03:08:24'),(15,'查看活动','activity:view','查看活动详情','activity','2025-05-05 03:08:24','2025-05-05 03:08:24'),(16,'访问个人专题','personal:access','访问个人专题页面','personal','2025-05-05 03:08:24','2025-05-05 03:08:24'),(17,'使用个人专题AI助手','personal:ai_use','使用个人专题AI助手','personal','2025-05-05 03:08:24','2025-05-05 03:08:24'),(18,'管理相关资料','personal:manage_materials','管理个人专题相关资料','personal','2025-05-05 03:08:24','2025-05-05 03:08:24'),(35,'test_permission_1114','test:permission:1114','测试权限','test','2025-05-06 05:42:13','2025-05-06 05:42:13'),(37,'test:permission:7218','test:permission:3465','Test permission','test','2025-05-06 05:43:34','2025-05-06 05:43:34'),(38,'test:permission:5131','test:permission:5131','Test permission','test','2025-05-06 05:44:26','2025-05-06 05:44:26'),(39,'test:permission:5645','test:permission:5645','Test permission','test','2025-05-06 05:44:57','2025-05-06 05:44:57'),(40,'test:permission','test:permission:9976','Test permission for testing','test','2025-05-06 05:45:31','2025-05-06 05:45:31'),(41,'user:read','test:permission:7370','Read user permission','user','2025-05-06 05:45:31','2025-05-06 05:45:31'),(42,'user:write','test:permission:5163','Write user permission','user','2025-05-06 05:45:31','2025-05-06 05:45:31'),(43,'knowledge:read','test:permission:6807','Read knowledge permission','knowledge','2025-05-06 05:45:31','2025-05-06 05:45:31'),(45,'test:permission:684','test:permission:684','Test permission','test','2025-05-06 05:45:32','2025-05-06 05:45:32'),(46,'test_permission_9855','test:permission:9855','测试权限','test','2025-05-06 05:45:32','2025-05-06 05:45:32'),(50,'test_permission_4394','test:permission:4394','测试权限','test','2025-05-06 05:46:08','2025-05-06 05:46:08'),(51,'test:permission:47','test:permission:47','Test permission','test','2025-05-06 05:46:08','2025-05-06 05:46:08'),(54,'test:permission:9931','test:permission:9931','Test permission','test','2025-05-06 06:01:48','2025-05-06 06:01:48'),(55,'test_permission_9709','test:permission:9709','测试权限','test','2025-05-06 06:01:49','2025-05-06 06:01:49'),(59,'test:permission:8252','test:permission:8252','Test permission for testing','test','2025-05-06 06:09:08','2025-05-06 06:09:08'),(60,'user:read:8252','user:read:8252','Read user permission','user','2025-05-06 06:09:08','2025-05-06 06:09:08'),(61,'user:write:8252','user:write:8252','Write user permission','user','2025-05-06 06:09:08','2025-05-06 06:09:08'),(62,'knowledge:read:8252','knowledge:read:8252','Read knowledge permission','knowledge','2025-05-06 06:09:08','2025-05-06 06:09:08'),(63,'permission:to:delete:2249','permission:to:delete:2249','Permission to delete','test','2025-05-06 06:09:08','2025-05-06 06:09:08'),(65,'user:read:9920','user:read:9920','Read user permission','user','2025-05-06 06:11:14','2025-05-06 06:11:14'),(66,'user:write:9920','user:write:9920','Write user permission','user','2025-05-06 06:11:14','2025-05-06 06:11:14'),(67,'knowledge:read:9920','knowledge:read:9920','Read knowledge permission','knowledge','2025-05-06 06:11:14','2025-05-06 06:11:14'),(68,'new:permission:9487','new:permission:9487','New permission for testing','new','2025-05-06 06:11:14','2025-05-06 06:11:14'),(69,'unauthorized:permission:1066','unauthorized:permission:1066','This should fail','test','2025-05-06 06:11:14','2025-05-06 06:11:14'),(72,'user:read:7024','user:read:7024','Read user permission','user','2025-05-06 06:12:12','2025-05-06 06:12:12'),(73,'user:write:7024','user:write:7024','Write user permission','user','2025-05-06 06:12:12','2025-05-06 06:12:12'),(74,'knowledge:read:7024','knowledge:read:7024','Read knowledge permission','knowledge','2025-05-06 06:12:12','2025-05-06 06:12:12'),(75,'unauthorized:permission:6351','unauthorized:permission:6351','This should fail','test','2025-05-06 06:12:13','2025-05-06 06:12:13'),(76,'permission:to:delete:8771','permission:to:delete:8771','Permission to delete','test','2025-05-06 06:12:13','2025-05-06 06:12:13'),(78,'user:read:3181','user:read:3181','Read user permission','user','2025-05-06 06:13:09','2025-05-06 06:13:09'),(79,'user:write:3181','user:write:3181','Write user permission','user','2025-05-06 06:13:09','2025-05-06 06:13:09'),(80,'knowledge:read:3181','knowledge:read:3181','Read knowledge permission','knowledge','2025-05-06 06:13:09','2025-05-06 06:13:09'),(81,'unauthorized:permission:2788','unauthorized:permission:2788','This should fail','test','2025-05-06 06:13:09','2025-05-06 06:13:09'),(82,'permission:to:delete:119','permission:to:delete:119','Permission to delete','test','2025-05-06 06:13:09','2025-05-06 06:13:09'),(83,'test:permission:5046','test:permission:5046','Test permission','test','2025-05-06 06:18:52','2025-05-06 06:18:52'),(86,'test_permission_2597','test:permission:2597','测试权限','test','2025-05-06 06:18:53','2025-05-06 06:18:53'),(89,'test:permission:6215','test:permission:6215','Test permission','test','2025-05-06 06:51:09','2025-05-06 06:51:09'),(91,'test_permission_3120','test:permission:3120','测试权限','test','2025-05-06 06:51:09','2025-05-06 06:51:09'),(93,'test:permission:8979','test:permission:8979','Unauthorized update','test','2025-05-06 10:33:15','2025-05-06 10:33:15'),(94,'user:read:8979','user:read:8979','Read user permission','user','2025-05-06 10:33:15','2025-05-06 10:33:15'),(95,'user:write:8979','user:write:8979','Write user permission','user','2025-05-06 10:33:15','2025-05-06 10:33:15'),(96,'knowledge:read:8979','knowledge:read:8979','Read knowledge permission','knowledge','2025-05-06 10:33:15','2025-05-06 10:33:15'),(97,'test:permission:3695','test:permission:3695','Test permission','test','2025-05-06 10:33:15','2025-05-06 10:33:15'),(98,'unauthorized:permission:8719','unauthorized:permission:8719','This should fail','test','2025-05-06 10:33:15','2025-05-06 10:33:15'),(99,'permission:to:delete:14','permission:to:delete:14','Permission to delete','test','2025-05-06 10:33:15','2025-05-06 10:33:15'),(102,'test_permission_5797','test:permission:5797','测试权限','test','2025-05-06 10:33:17','2025-05-06 10:33:17'),(104,'访问首页','home:access','访问系统首页','basic','2025-05-14 17:41:28','2025-05-14 17:41:28'),(105,'访问家族专题','family:access','访问家族专题页面','basic','2025-05-14 17:46:22','2025-05-14 17:46:22'),(106,'使用AI研究助手','assistant:use','使用AI研究助手功能','assistant','2025-05-14 17:46:22','2025-05-14 17:46:22');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` int NOT NULL,
  `permission_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_permissions_role_id_permission_id_unique` (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=122 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (51,111,35,'2025-05-06 05:42:13','2025-05-06 05:42:13'),(52,112,35,'2025-05-06 05:42:13','2025-05-06 05:42:13'),(53,117,38,'2025-05-06 05:44:26','2025-05-06 05:44:26'),(54,119,39,'2025-05-06 05:44:57','2025-05-06 05:44:57'),(55,123,45,'2025-05-06 05:45:32','2025-05-06 05:45:32'),(56,124,46,'2025-05-06 05:45:32','2025-05-06 05:45:32'),(57,125,46,'2025-05-06 05:45:32','2025-05-06 05:45:32'),(61,129,50,'2025-05-06 05:46:08','2025-05-06 05:46:08'),(62,130,50,'2025-05-06 05:46:08','2025-05-06 05:46:08'),(63,132,51,'2025-05-06 05:46:08','2025-05-06 05:46:08'),(64,145,54,'2025-05-06 06:01:48','2025-05-06 06:01:48'),(65,146,55,'2025-05-06 06:01:49','2025-05-06 06:01:49'),(66,147,55,'2025-05-06 06:01:49','2025-05-06 06:01:49'),(70,155,83,'2025-05-06 06:18:52','2025-05-06 06:18:52'),(74,158,86,'2025-05-06 06:18:53','2025-05-06 06:18:53'),(75,159,86,'2025-05-06 06:18:53','2025-05-06 06:18:53'),(78,167,89,'2025-05-06 06:51:09','2025-05-06 06:51:09'),(80,168,91,'2025-05-06 06:51:09','2025-05-06 06:51:09'),(81,169,91,'2025-05-06 06:51:09','2025-05-06 06:51:09'),(82,187,97,'2025-05-06 10:33:15','2025-05-06 10:33:15'),(86,190,102,'2025-05-06 10:33:17','2025-05-06 10:33:17'),(87,191,102,'2025-05-06 10:33:17','2025-05-06 10:33:17'),(92,12,1,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(93,12,2,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(94,12,3,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(95,12,4,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(96,12,5,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(97,12,6,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(98,12,7,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(99,12,8,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(100,12,9,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(101,12,10,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(102,12,11,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(103,12,12,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(104,12,13,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(105,12,14,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(106,12,15,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(107,12,16,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(108,12,17,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(109,12,18,'2025-05-14 16:46:50','2025-05-14 16:46:50'),(110,4,7,'2025-05-14 16:56:53','2025-05-14 16:56:53'),(111,4,16,'2025-05-14 16:56:53','2025-05-14 16:56:53'),(112,4,12,'2025-05-14 17:28:29','2025-05-14 17:28:29'),(114,4,104,'2025-05-14 17:43:01','2025-05-14 17:43:01'),(115,4,105,'2025-05-14 17:46:32','2025-05-14 17:46:32'),(117,4,106,'2025-05-14 17:46:43','2025-05-14 17:46:43'),(118,4,9,'2025-05-14 18:02:14','2025-05-14 18:02:14'),(119,4,10,'2025-05-14 18:02:14','2025-05-14 18:02:14'),(120,4,13,'2025-05-14 18:02:14','2025-05-14 18:02:14'),(121,4,17,'2025-05-14 18:02:14','2025-05-14 18:02:14');
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否为系统预设角色，系统预设角色不可删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=193 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'管理员','拥有全部权限',0,'2025-05-05 03:08:07','2025-05-05 03:08:07'),(4,'初级访问者','新注册用户的默认角色',0,'2025-05-05 03:08:07','2025-05-05 03:08:07'),(7,'test_role','测试角色',0,'2025-05-05 09:02:25','2025-05-05 09:02:25'),(8,'test_admin_role','测试管理员角色',1,'2025-05-05 09:02:26','2025-05-05 09:02:26'),(11,'user','Regular user role',0,'2025-05-05 11:53:40','2025-05-05 11:53:40'),(12,'admin','Administrator role',0,'2025-05-05 11:53:40','2025-05-05 11:53:40'),(67,'test_permission_role','测试权限角色',0,'2025-05-06 02:56:34','2025-05-06 02:56:34'),(68,'admin_permission_role','管理员权限角色',1,'2025-05-06 02:56:34','2025-05-06 02:56:34'),(105,'user_8215','Regular user role',0,'2025-05-06 05:22:18','2025-05-06 05:22:18'),(106,'admin_8215','Administrator role',0,'2025-05-06 05:22:18','2025-05-06 05:22:18'),(109,'test_role_3986','测试角色',0,'2025-05-06 05:36:27','2025-05-06 05:36:27'),(110,'test_admin_role_3986','测试管理员角色',1,'2025-05-06 05:36:27','2025-05-06 05:36:27'),(111,'test_role_1114','测试角色',0,'2025-05-06 05:42:13','2025-05-06 05:42:13'),(112,'test_admin_role_1114','测试管理员角色',1,'2025-05-06 05:42:13','2025-05-06 05:42:13'),(114,'user_7218','Regular user role',0,'2025-05-06 05:43:34','2025-05-06 05:43:34'),(115,'admin_7218','Administrator role',0,'2025-05-06 05:43:34','2025-05-06 05:43:34'),(116,'user_5131','Regular user role',0,'2025-05-06 05:44:26','2025-05-06 05:44:26'),(117,'admin_5131','Administrator role',0,'2025-05-06 05:44:26','2025-05-06 05:44:26'),(118,'user_5645','Regular user role',0,'2025-05-06 05:44:57','2025-05-06 05:44:57'),(119,'admin_5645','Administrator role',0,'2025-05-06 05:44:57','2025-05-06 05:44:57'),(122,'user_684','Regular user role',0,'2025-05-06 05:45:32','2025-05-06 05:45:32'),(123,'admin_684','Administrator role',0,'2025-05-06 05:45:32','2025-05-06 05:45:32'),(124,'test_role_9855','测试角色',0,'2025-05-06 05:45:32','2025-05-06 05:45:32'),(125,'test_admin_role_9855','测试管理员角色',1,'2025-05-06 05:45:32','2025-05-06 05:45:32'),(129,'test_role_4394','测试角色',0,'2025-05-06 05:46:08','2025-05-06 05:46:08'),(130,'test_admin_role_4394','测试管理员角色',1,'2025-05-06 05:46:08','2025-05-06 05:46:08'),(131,'user_47','Regular user role',0,'2025-05-06 05:46:08','2025-05-06 05:46:08'),(132,'admin_47','Administrator role',0,'2025-05-06 05:46:08','2025-05-06 05:46:08'),(137,'user_test','Regular user role',0,'2025-05-06 05:59:45','2025-05-06 05:59:45'),(138,'admin_test','Administrator role',1,'2025-05-06 05:59:45','2025-05-06 05:59:45'),(144,'user_9931','Regular user role',0,'2025-05-06 06:01:48','2025-05-06 06:01:48'),(145,'admin_9931','Administrator role',0,'2025-05-06 06:01:48','2025-05-06 06:01:48'),(146,'test_role_9709','测试角色',0,'2025-05-06 06:01:48','2025-05-06 06:01:48'),(147,'test_admin_role_9709','测试管理员角色',1,'2025-05-06 06:01:48','2025-05-06 06:01:48'),(154,'user_5046','Regular user role',0,'2025-05-06 06:18:52','2025-05-06 06:18:52'),(155,'admin_5046','Administrator role',0,'2025-05-06 06:18:52','2025-05-06 06:18:52'),(158,'test_role_2597','测试角色',0,'2025-05-06 06:18:53','2025-05-06 06:18:53'),(159,'test_admin_role_2597','测试管理员角色',1,'2025-05-06 06:18:53','2025-05-06 06:18:53'),(166,'user_6215','Regular user role',0,'2025-05-06 06:51:09','2025-05-06 06:51:09'),(167,'admin_6215','Administrator role',0,'2025-05-06 06:51:09','2025-05-06 06:51:09'),(168,'test_role_3120','测试角色',0,'2025-05-06 06:51:09','2025-05-06 06:51:09'),(169,'test_admin_role_3120','测试管理员角色',1,'2025-05-06 06:51:09','2025-05-06 06:51:09'),(186,'user_3695','Regular user role',0,'2025-05-06 10:33:15','2025-05-06 10:33:15'),(187,'admin_3695','Administrator role',0,'2025-05-06 10:33:15','2025-05-06 10:33:15'),(190,'test_role_5797','测试角色',0,'2025-05-06 10:33:17','2025-05-06 10:33:17'),(191,'test_admin_role_5797','测试管理员角色',1,'2025-05-06 10:33:17','2025-05-06 10:33:17');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sequelizemeta`
--

DROP TABLE IF EXISTS `sequelizemeta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sequelizemeta` (
  `name` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`name`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sequelizemeta`
--

LOCK TABLES `sequelizemeta` WRITE;
/*!40000 ALTER TABLE `sequelizemeta` DISABLE KEYS */;
INSERT INTO `sequelizemeta` VALUES ('20240506000000-create-timeline-events.js'),('20240520_add_dify_fields_to_files.js'),('20250503000001-create-users.js'),('20250503000002-create-roles.js'),('20250503000003-create-permissions.js'),('20250503000004-create-role-permissions.js'),('20250503000005-create-knowledge-bases.js'),('20250503000006-create-knowledge-base-access.js'),('20250503000007-create-files.js'),('20250503000008-create-ai-assistants.js'),('20250503000009-create-notifications.js'),('20250503000010-create-comments.js'),('20250503000011-create-activities.js'),('20250503000012-create-activity-attachments.js'),('20250503000013-create-system-config.js'),('20250510-modify-avatar-field.js');
/*!40000 ALTER TABLE `sequelizemeta` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_config`
--

DROP TABLE IF EXISTS `system_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_config` (
  `id` int NOT NULL DEFAULT '1',
  `password_min_length` int NOT NULL DEFAULT '8' COMMENT '密码最小长度',
  `password_require_uppercase` tinyint(1) NOT NULL DEFAULT '1' COMMENT '密码是否需要包含大写字母',
  `password_require_lowercase` tinyint(1) NOT NULL DEFAULT '1' COMMENT '密码是否需要包含小写字母',
  `password_require_number` tinyint(1) NOT NULL DEFAULT '1' COMMENT '密码是否需要包含数字',
  `password_require_special` tinyint(1) NOT NULL DEFAULT '1' COMMENT '密码是否需要包含特殊字符',
  `max_login_attempts` int NOT NULL DEFAULT '5' COMMENT '最大登录尝试次数',
  `lockout_duration` int NOT NULL DEFAULT '30' COMMENT '锁定时长（分钟）',
  `session_timeout` int NOT NULL DEFAULT '60' COMMENT '会话超时时间（分钟）',
  `file_upload_max_size` int NOT NULL DEFAULT '10' COMMENT '文件上传最大大小（MB）',
  `allowed_file_types` varchar(255) NOT NULL DEFAULT 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif' COMMENT '允许上传的文件类型，逗号分隔',
  `system_name` varchar(100) NOT NULL DEFAULT '和富家族研究平台' COMMENT '系统名称',
  `system_logo` varchar(255) NOT NULL DEFAULT '/logo.png' COMMENT '系统Logo路径',
  `system_description` text COMMENT '系统描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_config`
--

LOCK TABLES `system_config` WRITE;
/*!40000 ALTER TABLE `system_config` DISABLE KEYS */;
INSERT INTO `system_config` VALUES (1,0,1,1,1,1,-1,30,60,10,'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif','和富家族研究平台','/logo.png','和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。','2025-05-06 05:45:31','2025-05-06 10:33:18');
/*!40000 ALTER TABLE `system_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `timeline_events`
--

DROP TABLE IF EXISTS `timeline_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `timeline_events` (
  `id` int NOT NULL AUTO_INCREMENT,
  `year` varchar(255) NOT NULL COMMENT '事件年份',
  `title` varchar(255) NOT NULL COMMENT '事件标题',
  `description` varchar(255) NOT NULL COMMENT '事件简短描述',
  `content` text NOT NULL COMMENT '事件详细内容',
  `level` enum('national','family','personal') NOT NULL DEFAULT 'personal' COMMENT '事件级别：国家级、家族级、个人级',
  `icon` varchar(255) DEFAULT NULL COMMENT '事件图标URL',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `timeline_events`
--

LOCK TABLES `timeline_events` WRITE;
/*!40000 ALTER TABLE `timeline_events` DISABLE KEYS */;
INSERT INTO `timeline_events` VALUES (1,'1892年','蔡和森出生','蔡和森出生于湖南宝庆（今湖南邵阳）。','1893年，蔡和森出生于湖南宝庆（今湖南邵阳）。他的出生是革命家族历史的起点之一，后来的蔡和森将成为中国共产党的重要创始人之一。','personal',NULL,'2025-05-06 12:31:38','2025-05-08 07:31:30'),(2,'1913年','接触进步思想','蔡和森考入湖南省立第一中学，开始接触进步思想。','1913年，蔡和森考入湖南省立第一中学，开始接触进步思想。这段时期是他思想启蒙的关键阶段，为他后来成为革命者奠定了思想基础。在这所学校里，他接触了许多新思想、新文化，开始关注社会问题和民族危机。','personal','/public/uploads/timeline-icons/icon-1746702429965-940912525.png','2025-05-06 12:31:38','2025-05-08 12:23:06'),(3,'1918年','赴法勤工俭学','蔡和森与毛泽东等人一起赴法国勤工俭学。','1918年，蔡和森与毛泽东等人一起赴法国勤工俭学。在法国期间，他系统学习了马克思主义理论，并与其他留学生一起创办了旅欧中国少年共产党，这是中国共产党的雏形之一。','family',NULL,'2025-05-06 12:31:38','2025-05-06 12:31:38'),(4,'1921年','中国共产党成立','中国共产党在上海成立，蔡和森是重要创始人之一。','1921年，中国共产党在上海成立。虽然蔡和森当时仍在法国，但他是党的重要创始人之一，他早在1920年就在给毛泽东的信中提出了建立共产党的构想。','national',NULL,'2025-05-06 12:31:38','2025-05-06 12:31:38'),(5,'1922年','回国参加革命工作','蔡和森回国后积极投身革命工作。','1922年，蔡和森回国后积极投身革命工作，担任中共中央秘书，参与领导工人运动和青年运动。他在这一时期撰写了大量宣传马克思主义的文章，为中国共产党的思想建设做出了重要贡献。','family',NULL,'2025-05-06 12:31:38','2025-05-06 12:31:38'),(6,'1927年','蔡和森牺牲','蔡和森在长沙被反动军阀杀害，年仅34岁。','1927年8月4日，蔡和森在长沙被反动军阀杀害，年仅34岁。他的牺牲是中国革命的重大损失，但他的革命精神和理论贡献对中国共产党的发展产生了深远影响。','national',NULL,'2025-05-06 12:31:38','2025-05-06 12:31:38'),(7,'1929年','向警予牺牲','蔡和森的妻子向警予在长沙被杀害。','1929年5月1日，蔡和森的妻子向警予在长沙被杀害。向警予是中国共产党早期的重要女性领导人，也是中国妇女运动的先驱。她与蔡和森的革命伴侣关系是中国革命家庭的典范。','family',NULL,'2025-05-06 12:31:38','2025-05-06 12:31:38'),(8,'1949年','中华人民共和国成立','中华人民共和国成立，蔡和森等革命先烈的理想得以实现。','1949年10月1日，中华人民共和国成立。这标志着蔡和森等革命先烈为之奋斗的理想得以实现。在新中国成立后，蔡和森被追认为革命烈士，他的革命事迹和理论贡献得到了广泛的纪念和研究。','national','/public/uploads/timeline-icons/icon-1746707033658-751518534.png','2025-05-06 12:31:38','2025-05-08 12:23:55'),(9,'1983年','蔡和森故居修复','蔡和森故居在湖南邵阳修复并对外开放。','1983年，蔡和森故居在湖南邵阳修复并对外开放，成为重要的革命传统教育基地。故居的修复和开放，使更多人能够了解蔡和森的生平事迹和革命贡献。','family',NULL,'2025-05-06 12:31:38','2025-05-06 12:31:38'),(10,'2013年','纪念蔡和森诞辰120周年','全国各地举行活动纪念蔡和森诞辰120周年。','2013年，适逢蔡和森诞辰120周年，全国各地举行了一系列纪念活动。这些活动包括学术研讨会、展览和出版物，旨在弘扬蔡和森的革命精神和理论贡献，激励新一代继承和发扬革命传统。','national','/public/uploads/timeline-icons/icon-1746705625736-814516524.png','2025-05-06 12:31:38','2025-05-08 12:23:06'),(11,'2024年','测试','测试','测试','personal','/public/uploads/timeline-icons/icon-1746690230888-522954614.png','2025-05-08 07:43:54','2025-05-08 12:23:06'),(13,'2025','测试','21312','12312312','personal','/public/uploads/timeline-icons/icon-1747226069239-695693677.png','2025-05-14 08:48:22','2025-05-14 12:34:30'),(17,'1412412','124124','124123123','3123123','personal','/public/uploads/timeline-icons/icon-1747239161636-667145737.png','2025-05-14 16:12:44','2025-05-14 16:12:44');
/*!40000 ALTER TABLE `timeline_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `avatar` text,
  `role` enum('admin','basic_user') DEFAULT 'basic_user',
  `role_id` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=836 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'admin','$2a$10$ErG3FpwIO.yBu60IDwCwwuggROJPU7TosZhMER70K161YOMnG6oX6','<EMAIL>','13044444444',NULL,'admin',1,1,'2025-05-14 18:31:58','2025-05-05 03:09:10','2025-05-14 18:31:58'),(833,'ceshi','$2a$10$9FQaJTu.Y7zgwg4ThcFmu.Eyf2tzNE83qJ2mZ/yBhJYipsa7J7aBK','<EMAIL>','13044444444','data:image/jpeg;base64,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','basic_user',4,1,'2025-05-14 19:26:55','2025-05-07 03:28:24','2025-05-14 19:26:55'),(835,'0508','$2a$10$Q.sN.4hrokN3Hw0tgyUQ1eme2XZioeHlZxwGWRbdy6LONDHqVcg56','<EMAIL>','1247861287',NULL,'basic_user',4,1,'2025-05-14 14:40:21','2025-05-08 06:29:11','2025-05-14 14:40:21');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-15  4:11:10
