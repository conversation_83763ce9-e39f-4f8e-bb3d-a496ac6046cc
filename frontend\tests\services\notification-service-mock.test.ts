/**
 * 通知服务模拟测试
 */

import {
  getNotificationList,
  getNotification,
  markAsRead,
  markAllAsRead,
  deleteNotification
} from '@/services/notification-service';
import apiService from '@/services/api-service';

// 模拟API服务
jest.mock('@/services/api-service', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  del: jest.fn()
}));

describe('通知服务', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    jest.clearAllMocks();
  });

  // 测试获取通知列表
  describe('getNotificationList', () => {
    test('应该返回通知列表', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        data: {
          notifications: [
            {
              id: 1,
              title: '系统通知',
              content: '欢迎使用和富家族研究平台',
              type: 'system',
              status: 'unread',
              created_at: '2023-01-01T00:00:00Z'
            },
            {
              id: 2,
              title: '评论通知',
              content: '您的评论已被审核通过',
              type: 'comment',
              status: 'read',
              created_at: '2023-01-02T00:00:00Z'
            }
          ],
          pagination: {
            total: 2,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        }
      };

      // 设置模拟返回值
      (apiService.get as jest.Mock).mockResolvedValue(mockResponse.data);

      // 调用获取通知列表函数
      const result = await getNotificationList({ status: 'all' });

      // 验证API服务被调用
      expect(apiService.get).toHaveBeenCalledWith('/notifications', { params: { status: 'all' } });

      // 验证结果
      expect(result).toEqual(mockResponse.data);
      expect(result.notifications).toHaveLength(2);
      expect(result.notifications[0].title).toBe('系统通知');
      expect(result.notifications[1].title).toBe('评论通知');
    });

    test('获取通知列表失败应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '获取通知列表失败'
      };

      // 设置模拟返回值
      (apiService.get as jest.Mock).mockRejectedValue(new Error('获取通知列表失败'));

      // 调用获取通知列表函数并捕获错误
      await expect(getNotificationList({ status: 'all' })).rejects.toThrow('获取通知列表失败');

      // 验证API服务被调用
      expect(apiService.get).toHaveBeenCalledWith('/notifications', { params: { status: 'all' } });
    });
  });

  // 测试获取通知详情
  describe('getNotification', () => {
    test('应该返回通知详情', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        data: {
          id: 1,
          title: '系统通知',
          content: '欢迎使用和富家族研究平台',
          type: 'system',
          status: 'unread',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      };

      // 设置模拟返回值
      (apiService.get as jest.Mock).mockResolvedValue(mockResponse.data);

      // 调用获取通知详情函数
      const result = await getNotification(1);

      // 验证API服务被调用
      expect(apiService.get).toHaveBeenCalledWith('/notifications/1');

      // 验证结果
      expect(result).toEqual(mockResponse.data);
      expect(result.id).toBe(1);
      expect(result.title).toBe('系统通知');
      expect(result.content).toBe('欢迎使用和富家族研究平台');
    });

    test('获取不存在的通知应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '通知不存在'
      };

      // 设置模拟返回值
      (apiService.get as jest.Mock).mockRejectedValue(new Error('通知不存在'));

      // 调用获取通知详情函数并捕获错误
      await expect(getNotification(999)).rejects.toThrow('通知不存在');

      // 验证API服务被调用
      expect(apiService.get).toHaveBeenCalledWith('/notifications/999');
    });
  });

  // 测试标记通知为已读
  describe('markAsRead', () => {
    test('应该成功标记通知为已读', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        message: '通知已标记为已读',
        data: {
          id: 1,
          status: 'read',
          updated_at: '2023-01-03T00:00:00Z'
        }
      };

      // 设置模拟返回值
      (apiService.put as jest.Mock).mockResolvedValue(mockResponse.data);

      // 调用标记通知为已读函数
      const result = await markAsRead(1);

      // 验证API服务被调用
      expect(apiService.put).toHaveBeenCalledWith('/notifications/1/read');

      // 验证结果
      expect(result).toEqual(mockResponse.data);
      expect(result.status).toBe('read');
    });

    test('标记不存在的通知应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '通知不存在'
      };

      // 设置模拟返回值
      (apiService.put as jest.Mock).mockRejectedValue(new Error('通知不存在'));

      // 调用标记通知为已读函数并捕获错误
      await expect(markAsRead(999)).rejects.toThrow('通知不存在');

      // 验证API服务被调用
      expect(apiService.put).toHaveBeenCalledWith('/notifications/999/read');
    });
  });

  // 测试标记所有通知为已读
  describe('markAllAsRead', () => {
    test('应该成功标记所有通知为已读', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        message: '所有通知已标记为已读',
        data: {
          count: 3
        }
      };

      // 设置模拟返回值
      (apiService.put as jest.Mock).mockResolvedValue(mockResponse);

      // 调用标记所有通知为已读函数
      const result = await markAllAsRead();

      // 验证API服务被调用
      expect(apiService.put).toHaveBeenCalledWith('/notifications/read-all');

      // 验证结果
      expect(result).toEqual(mockResponse);
      expect(result.success).toBe(true);
      expect(result.message).toBe('所有通知已标记为已读');
      expect(result.data.count).toBe(3);
    });

    test('标记所有通知失败应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '操作失败'
      };

      // 设置模拟返回值
      (apiService.put as jest.Mock).mockRejectedValue(new Error('操作失败'));

      // 调用标记所有通知为已读函数并捕获错误
      await expect(markAllAsRead()).rejects.toThrow('操作失败');

      // 验证API服务被调用
      expect(apiService.put).toHaveBeenCalledWith('/notifications/read-all');
    });
  });

  // 测试删除通知
  describe('deleteNotification', () => {
    test('应该成功删除通知', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        message: '通知删除成功'
      };

      // 设置模拟返回值
      (apiService.del as jest.Mock).mockResolvedValue(mockResponse);

      // 调用删除通知函数
      const result = await deleteNotification(1);

      // 验证API服务被调用
      expect(apiService.del).toHaveBeenCalledWith('/notifications/1');

      // 验证结果
      expect(result).toEqual(mockResponse);
      expect(result.success).toBe(true);
      expect(result.message).toBe('通知删除成功');
    });

    test('删除不存在的通知应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '通知不存在'
      };

      // 设置模拟返回值
      (apiService.del as jest.Mock).mockRejectedValue(new Error('通知不存在'));

      // 调用删除通知函数并捕获错误
      await expect(deleteNotification(999)).rejects.toThrow('通知不存在');

      // 验证API服务被调用
      expect(apiService.del).toHaveBeenCalledWith('/notifications/999');
    });
  });
});
