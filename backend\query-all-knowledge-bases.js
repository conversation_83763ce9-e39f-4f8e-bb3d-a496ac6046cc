/**
 * 查询所有知识库的信息
 */

// 加载环境变量
require('dotenv').config();

// 导入模型
const db = require('./src/models');

// 查询所有知识库
async function queryAllKnowledgeBases() {
  try {
    // 连接数据库
    await db.sequelize.authenticate();
    console.log('数据库连接成功');

    // 查询所有知识库记录
    const knowledgeBases = await db.KnowledgeBase.findAll({
      include: [
        {
          model: db.User,
          as: 'creator',
          attributes: ['id', 'username', 'email', 'role']
        }
      ]
    });

    if (knowledgeBases.length > 0) {
      console.log(`找到 ${knowledgeBases.length} 个知识库记录:`);
      
      knowledgeBases.forEach((kb, index) => {
        console.log(`\n知识库 ${index + 1}:`);
        console.log('知识库ID:', kb.id);
        console.log('知识库名称:', kb.name);
        console.log('知识库类型:', kb.type);
        console.log('创建时间:', kb.created_at);
        console.log('创建者:', kb.creator ? kb.creator.username : '未知');
      });
    } else {
      console.log('未找到任何知识库记录');
    }

    // 关闭数据库连接
    await db.sequelize.close();
  } catch (error) {
    console.error('查询失败:', error);
  }
}

// 执行查询
queryAllKnowledgeBases();
