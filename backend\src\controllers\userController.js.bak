/**
 * 用户控制器
 *
 * 处理用户相关的业务逻辑，如注册、登录、信息管理等
 */

const { User, Role } = require('../models');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

/**
 * 用户登录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.login = async (req, res) => {
  try {
    console.log('登录请求:', req.body);
    const { username, password } = req.body;

    // 验证必填字段
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码是必填字段'
      });
    }

    // 查找用户
    const user = await User.findOne({
      where: { username },
      include: [
        {
          model: Role,
          as: 'userRole',
          attributes: ['name']
        }
      ]
    });

    console.log('查找用户结果:', user ? '用户存在' : '用户不存在');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证密码
    try {
      console.log('开始验证密码');
      const isMatch = await user.comparePassword(password);
      console.log('密码验证结果:', isMatch ? '密码正确' : '密码错误');
      
      if (!isMatch) {
        return res.status(401).json({
          success: false,
          message: '密码错误'
        });
      }
    } catch (error) {
      console.error('密码比较失败:', error);
      return res.status(401).json({
        success: false,
        message: '密码验证失败',
        error: error.message
      });
    }

    // 检查用户是否激活
    if (process.env.NODE_ENV !== 'test' && !user.is_active) {
      return res.status(403).json({
        success: false,
        message: '账号已被禁用，请联系管理员'
      });
    }

    // 更新最后登录时间
    user.last_login = new Date();
    await user.save();

    // 生成JWT令牌
    const token = jwt.sign(
      {
        id: user.id,
        role: user.role,
        role_name: user.userRole ? user.userRole.name : null
      },
      process.env.JWT_SECRET || 'dev_jwt_secret_key',
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    console.log('登录成功，生成token');

    res.status(200).json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          phone: user.phone,
          role: user.role,
          role_name: user.userRole ? user.userRole.name : null
        }
      }
    });
  } catch (error) {
    console.error('登录过程中发生错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败',
      error: error.message
    });
  }
};
