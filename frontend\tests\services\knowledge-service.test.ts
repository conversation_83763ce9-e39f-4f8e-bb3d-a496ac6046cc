/**
 * 知识库服务测试
 */

import {
  getKnowledgeBaseList,
  getMyKnowledgeBaseList,
  getKnowledgeBaseById,
  createKnowledgeBase,
  updateKnowledgeBase,
  deleteKnowledgeBase
} from '@/services/knowledge-service'
import apiService from '@/services/api-service'

// 模拟API服务
jest.mock('@/services/api-service')

describe('知识库服务', () => {
  // 在每个测试前后重置模拟
  beforeEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  afterEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  // 测试获取知识库列表
  describe('getKnowledgeBaseList', () => {
    test('应该返回知识库列表', async () => {
      // 模拟成功的响应
      const mockResponse = {
        knowledgeBases: [
          {
            id: '1',
            name: '系统知识库1',
            description: '系统知识库描述',
            type: 'system',
            creator_id: '1',
            creator: {
              id: '1',
              username: 'admin',
              email: '<EMAIL>'
            },
            file_count: 10,
            storage_size: 1048576,
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
          },
          {
            id: '2',
            name: '系统知识库2',
            description: '另一个系统知识库',
            type: 'system',
            creator_id: '1',
            creator: {
              id: '1',
              username: 'admin',
              email: '<EMAIL>'
            },
            file_count: 5,
            storage_size: 524288,
            created_at: '2023-01-02T00:00:00Z',
            updated_at: '2023-01-02T00:00:00Z'
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取知识库列表函数
      const result = await getKnowledgeBaseList({ type: 'system' })

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.knowledgeBases).toHaveLength(2)
      expect(result.knowledgeBases[0].name).toBe('系统知识库1')
      expect(result.knowledgeBases[1].name).toBe('系统知识库2')
    })

    test('获取知识库列表失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('获取知识库列表失败')

      // 模拟API服务的get方法抛出错误
      apiService.get = jest.fn().mockRejectedValue(error)

      // 调用获取知识库列表函数并捕获错误
      await expect(getKnowledgeBaseList({ type: 'system' })).rejects.toThrow('获取知识库列表失败')
    })
  })

  // 测试获取我的知识库列表
  describe('getMyKnowledgeBaseList', () => {
    test('应该返回我的知识库列表', async () => {
      // 模拟成功的响应
      const mockResponse = {
        knowledgeBases: [
          {
            id: '3',
            name: '我的知识库1',
            description: '我的知识库描述',
            type: 'user',
            creator_id: '2',
            creator: {
              id: '2',
              username: 'testuser',
              email: '<EMAIL>'
            },
            file_count: 3,
            storage_size: 307200,
            created_at: '2023-01-03T00:00:00Z',
            updated_at: '2023-01-03T00:00:00Z'
          }
        ],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取我的知识库列表函数
      const result = await getMyKnowledgeBaseList()

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.knowledgeBases).toHaveLength(1)
      expect(result.knowledgeBases[0].name).toBe('我的知识库1')
      expect(result.knowledgeBases[0].type).toBe('user')
    })
  })

  // 测试获取知识库详情
  describe('getKnowledgeBaseById', () => {
    test('应该返回知识库详情', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '1',
        name: '系统知识库1',
        description: '系统知识库描述',
        type: 'system',
        creator_id: '1',
        creator: {
          id: '1',
          username: 'admin',
          email: '<EMAIL>'
        },
        file_count: 10,
        storage_size: 1048576,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取知识库详情函数
      const result = await getKnowledgeBaseById('1')

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.id).toBe('1')
      expect(result.name).toBe('系统知识库1')
    })

    test('获取不存在的知识库应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('知识库不存在')

      // 模拟API服务的get方法抛出错误
      apiService.get = jest.fn().mockRejectedValue(error)

      // 调用获取知识库详情函数并捕获错误
      await expect(getKnowledgeBaseById('999')).rejects.toThrow('知识库不存在')
    })
  })

  // 测试创建知识库
  describe('createKnowledgeBase', () => {
    test('应该成功创建知识库', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '4',
        name: '新知识库',
        description: '新知识库描述',
        type: 'user',
        creator_id: '2',
        creator: {
          id: '2',
          username: 'testuser',
          email: '<EMAIL>'
        },
        file_count: 0,
        storage_size: 0,
        created_at: '2023-01-10T00:00:00Z',
        updated_at: '2023-01-10T00:00:00Z'
      }

      // 模拟API服务的post方法
      apiService.post = jest.fn().mockResolvedValue(mockResponse)

      // 调用创建知识库函数
      const result = await createKnowledgeBase({
        name: '新知识库',
        description: '新知识库描述',
        type: 'user'
      })

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.id).toBe('4')
      expect(result.name).toBe('新知识库')
    })

    test('创建知识库失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('知识库名称已存在')

      // 模拟API服务的post方法抛出错误
      apiService.post = jest.fn().mockRejectedValue(error)

      // 调用创建知识库函数并捕获错误
      await expect(createKnowledgeBase({
        name: '已存在的知识库',
        description: '测试描述',
        type: 'user'
      })).rejects.toThrow('知识库名称已存在')
    })
  })

  // 测试更新知识库
  describe('updateKnowledgeBase', () => {
    test('应该成功更新知识库', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '3',
        name: '更新后的知识库',
        description: '更新后的描述',
        type: 'user',
        creator_id: '2',
        creator: {
          id: '2',
          username: 'testuser',
          email: '<EMAIL>'
        },
        file_count: 3,
        storage_size: 307200,
        created_at: '2023-01-03T00:00:00Z',
        updated_at: '2023-01-11T00:00:00Z'
      }

      // 模拟API服务的put方法
      apiService.put = jest.fn().mockResolvedValue(mockResponse)

      // 调用更新知识库函数
      const result = await updateKnowledgeBase('3', {
        name: '更新后的知识库',
        description: '更新后的描述'
      })

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.name).toBe('更新后的知识库')
      expect(result.description).toBe('更新后的描述')
    })
  })

  // 测试删除知识库
  describe('deleteKnowledgeBase', () => {
    test('应该成功删除知识库', async () => {
      // 模拟成功的响应
      const mockResponse = {
        message: '知识库删除成功'
      }

      // 模拟API服务的del方法
      apiService.del = jest.fn().mockResolvedValue(mockResponse)

      // 调用删除知识库函数
      const result = await deleteKnowledgeBase('3')

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.message).toBe('知识库删除成功')
    })

    test('删除不存在的知识库应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('知识库不存在')

      // 模拟API服务的del方法抛出错误
      apiService.del = jest.fn().mockRejectedValue(error)

      // 调用删除知识库函数并捕获错误
      await expect(deleteKnowledgeBase('999')).rejects.toThrow('知识库不存在')
    })
  })
});
