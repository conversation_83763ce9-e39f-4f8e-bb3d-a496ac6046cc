/**
 * 知识库访问申请相关路由
 * 
 * 处理知识库访问申请的创建、查询、审核等请求
 */

const express = require('express');
const router = express.Router();
const knowledgeAccessRequestController = require('../controllers/knowledge-access-request.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');

// 创建知识库访问申请 (需要认证)
router.post('/', 
  authMiddleware, 
  knowledgeAccessRequestController.createAccessRequest
);

// 获取知识库访问申请列表 (需要认证)
router.get('/', 
  authMiddleware, 
  knowledgeAccessRequestController.getAccessRequests
);

// 获取知识库的访问申请列表 (需要认证)
router.get('/knowledge-base/:id', 
  authMiddleware, 
  knowledgeAccessRequestController.getKnowledgeBaseAccessRequests
);

// 获取我的访问申请列表 (需要认证)
router.get('/my', 
  authMiddleware, 
  knowledgeAccessRequestController.getMyAccessRequests
);

// 审核知识库访问申请 (需要认证)
router.put('/:id/review', 
  authMiddleware, 
  knowledgeAccessRequestController.reviewAccessRequest
);

module.exports = router;
