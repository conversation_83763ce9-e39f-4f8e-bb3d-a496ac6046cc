"use client"

import { useState, useEffect, useRef } from 'react'
import { Send, Trash2, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AIQueryRequest, ConversationMessage } from '@/types/ai-assistants'
import aiAssistantService from '@/services/ai-assistant-service'
import { HtmlTypewriter } from '@/components/ui/html-typewriter'

interface PersonalAssistantProps {
  title?: string
  personName?: string
}

/**
 * 个人专题AI助手组件
 *
 * 用于在个人专题页面中提供AI问答功能
 */
export function PersonalAssistant({ title = "AI助手", personName }: PersonalAssistantProps) {
  const [query, setQuery] = useState('')
  const [messages, setMessages] = useState<ConversationMessage[]>([])
  const [conversationId, setConversationId] = useState<string | undefined>()
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 打字机效果相关状态
  const [showTypewriter, setShowTypewriter] = useState(false)
  const [typewriterText, setTypewriterText] = useState('')
  const [lastMessageId, setLastMessageId] = useState<string | null>(null)

  // 初始化助手
  useEffect(() => {
    // 如果有人物名称，添加初始消息
    if (personName) {
      setMessages([
        {
          id: 'initial',
          role: 'assistant',
          content: `您好，我是${personName}专题AI助手，有什么可以帮助您的吗？`,
          created_at: new Date().toISOString()
        }
      ])
    }
  }, [personName])

  // 滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // 打字机效果完成后的回调函数
  const handleTypewriterComplete = () => {
    console.log('打字机效果完成')
    setShowTypewriter(false)
    setIsLoading(false)
  }

  // 发送查询
  const handleSendQuery = async () => {
    if (!query.trim() || isLoading) return

    // 重置打字机状态
    setShowTypewriter(false)
    setTypewriterText('')

    // 添加用户消息
    const userMessage: ConversationMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: query,
      created_at: new Date().toISOString()
    }
    setMessages(prev => [...prev, userMessage])

    // 清空输入框
    setQuery('')
    setIsLoading(true)

    try {
      // 准备请求数据
      const requestData: AIQueryRequest = {
        query: userMessage.content,
        conversation_id: conversationId
      }

      // 调用API
      const response = await aiAssistantService.queryPersonalAssistant(requestData)

      // 保存会话ID
      if (response.conversation_id) {
        setConversationId(response.conversation_id)
      }

      // 处理响应内容
      if (response.answer && response.answer.trim() !== "") {
        // 检查是否包含思考过程（details标签）
        let cleanedAnswer = response.answer;

        // 如果包含思考过程，提取出正式回复部分
        if (response.answer.includes('<details') && response.answer.includes('</details>')) {
          const detailsEndIndex = response.answer.indexOf('</details>') + 10;
          cleanedAnswer = response.answer.substring(detailsEndIndex).trim();
          console.log('提取出正式回复部分，长度:', cleanedAnswer.length);
        }

        // 移除所有HTML标签，只保留纯文本
        const textOnly = cleanedAnswer.replace(/<[^>]*>/g, '');
        console.log('移除HTML标签后的纯文本，长度:', textOnly.length);

        // 设置打字机文本内容
        setTypewriterText(textOnly)

        // 添加空的助手消息占位符
        const assistantMessage: ConversationMessage = {
          id: response.id || `assistant-${Date.now()}`,
          role: 'assistant',
          content: textOnly, // 使用纯文本内容
          created_at: response.created_at || new Date().toISOString()
        }

        // 保存最后一条消息ID，用于打字机效果
        setLastMessageId(assistantMessage.id)

        // 添加消息到列表
        setMessages(prev => [...prev, assistantMessage])

        // 显示打字机效果
        setShowTypewriter(true)
      } else {
        // 如果响应为空，添加默认消息
        const errorMessage: ConversationMessage = {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: '抱歉，我无法处理您的请求，请稍后再试。',
          created_at: new Date().toISOString()
        }
        setMessages(prev => [...prev, errorMessage])
        setIsLoading(false)
      }
    } catch (error) {
      console.error('查询失败:', error)

      // 添加错误消息
      const errorMessage: ConversationMessage = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: '抱歉，我遇到了一些问题，无法回答您的问题。请稍后再试。',
        created_at: new Date().toISOString()
      }
      setMessages(prev => [...prev, errorMessage])
      setIsLoading(false)
    }
  }

  // 清空对话
  const handleClearConversation = async () => {
    if (conversationId) {
      try {
        await aiAssistantService.clearConversationHistory('personal', conversationId)
      } catch (error) {
        console.error('清除对话历史失败:', error)
      }
    }

    // 重置状态
    setMessages(personName ? [
      {
        id: 'initial',
        role: 'assistant',
        content: `您好，我是${personName}专题AI助手，有什么可以帮助您的吗？`,
        created_at: new Date().toISOString()
      }
    ] : [])
    setConversationId(undefined)
  }

  return (
    <Card className="w-full h-full flex flex-col">
      <CardHeader className="px-4 py-3 border-b">
        <CardTitle className="text-lg flex justify-between items-center">
          <span>{title}</span>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClearConversation}
              title="清空对话"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden p-0 flex flex-col">
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] rounded-lg px-4 py-2 ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                {showTypewriter && message.id === lastMessageId ? (
                  <HtmlTypewriter
                    html={typewriterText}
                    className="text-lg whitespace-pre-line"
                    speed={30}
                    onComplete={handleTypewriterComplete}
                  />
                ) : (
                  message.content
                )}
              </div>
            </div>
          ))}
          {isLoading && !showTypewriter && (
            <div className="flex justify-start">
              <div className="max-w-[80%] rounded-lg px-4 py-2 bg-muted flex items-center">
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                思考中...
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
        <div className="p-4 border-t">
          <div className="flex gap-2">
            <Textarea
              placeholder="请输入您的问题..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  handleSendQuery()
                }
              }}
              className="min-h-[60px] resize-none"
            />
            <Button
              onClick={handleSendQuery}
              disabled={!query.trim() || isLoading}
              className="shrink-0"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
