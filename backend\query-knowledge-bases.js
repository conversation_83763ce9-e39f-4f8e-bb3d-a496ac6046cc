/**
 * 查询所有知识库及其文件
 */

// 加载环境变量
require('dotenv').config();

// 导入模型
const db = require('./src/models');

// 查询知识库
async function queryKnowledgeBases() {
  try {
    // 连接数据库
    await db.sequelize.authenticate();
    console.log('数据库连接成功');

    // 查询所有知识库
    const knowledgeBases = await db.KnowledgeBase.findAll({
      include: [
        {
          model: db.User,
          as: 'creator',
          attributes: ['id', 'username', 'email', 'role']
        }
      ]
    });

    console.log('知识库列表:');
    for (const kb of knowledgeBases) {
      console.log(`ID: ${kb.id}, 名称: ${kb.name}, 类型: ${kb.type}, 创建者: ${kb.creator ? kb.creator.username : '未知'}`);
      
      // 查询该知识库的文件
      const files = await db.File.findAll({
        where: { knowledge_base_id: kb.id },
        include: [
          {
            model: db.User,
            as: 'uploader',
            attributes: ['id', 'username', 'email']
          }
        ]
      });
      
      console.log(`  文件数量: ${files.length}`);
      files.forEach(file => {
        console.log(`  - ID: ${file.id}, 名称: ${file.original_name}, 上传者: ${file.uploader ? file.uploader.username : '未知'}`);
      });
      
      console.log('---');
    }

  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    // 关闭数据库连接
    await db.sequelize.close();
    console.log('数据库连接已关闭');
  }
}

// 执行查询
queryKnowledgeBases();
