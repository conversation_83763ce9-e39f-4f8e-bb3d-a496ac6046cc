"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Bell, Info, Calendar, MessageSquare } from "lucide-react"
import { Notification } from "@/services/notification-service"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

interface NotificationDetailModalProps {
  isOpen: boolean
  onClose: () => void
  notification: Notification | null
}

/**
 * 通知详情模态框组件
 *
 * 显示通知的详细内容
 */
export function NotificationDetailModal({ isOpen, onClose, notification }: NotificationDetailModalProps) {
  if (!notification) return null

  // 获取通知类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "system":
        return <Info className="h-5 w-5 text-blue-500" />
      case "activity":
        return <Calendar className="h-5 w-5 text-green-500" />
      case "file":
        return <Bell className="h-5 w-5 text-orange-500" />
      case "comment":
        return <MessageSquare className="h-5 w-5 text-purple-500" />
      default:
        return <Bell className="h-5 w-5 text-gray-500" />
    }
  }

  // 获取通知类型标签
  const getTypeLabel = (type: string) => {
    switch (type) {
      case "system":
        return "系统通知"
      case "activity":
        return "活动通知"
      case "file":
        return "文件通知"
      case "comment":
        return "评论通知"
      default:
        return "通知"
    }
  }

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      return format(new Date(dateString), "yyyy-MM-dd HH:mm", { locale: zhCN })
    } catch (error) {
      return dateString
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="flex flex-row items-center gap-2">
          {getTypeIcon(notification.type)}
          <DialogTitle>{notification.title}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="flex justify-between items-center text-sm text-gray-500">
            <span>{getTypeLabel(notification.type)}</span>
            <span>{formatTime(notification.created_at)}</span>
          </div>
          <div className="border-t pt-4">
            <p className="text-base">{notification.content}</p>
            {notification.related_id && notification.related_type && (
              <div className="mt-4 p-4 bg-gray-50 rounded-md text-sm">
                <p>相关信息：{notification.related_type} (ID: {notification.related_id})</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
