/**
 * 文件服务测试
 */

import {
  getFileList,
  getFileById,
  uploadFile,
  downloadFile,
  uploadFileToDify,
  reviewFile,
  deleteFile
} from '@/services/file-service'
import apiService from '@/services/api-service'

// 模拟API服务
jest.mock('@/services/api-service')

describe('文件服务', () => {
  // 在每个测试前后重置模拟
  beforeEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  afterEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  // 测试获取文件列表
  describe('getFileList', () => {
    test('应该返回文件列表', async () => {
      // 模拟成功的响应
      const mockResponse = {
        files: [
          {
            id: '1',
            name: 'test-file-1.pdf',
            original_name: 'test-file-1.pdf',
            type: 'pdf',
            size: 1024,
            knowledge_base_id: '1',
            uploader: {
              id: '1',
              username: 'admin'
            },
            status: 'approved',
            created_at: '2023-01-01T00:00:00Z'
          },
          {
            id: '2',
            name: 'test-file-2.docx',
            original_name: 'test-file-2.docx',
            type: 'docx',
            size: 2048,
            knowledge_base_id: '1',
            uploader: {
              id: '1',
              username: 'admin'
            },
            status: 'approved',
            created_at: '2023-01-02T00:00:00Z'
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取文件列表函数
      const result = await getFileList({ knowledge_base_id: '1' })

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.files).toHaveLength(2)
      expect(result.files[0].name).toBe('test-file-1.pdf')
      expect(result.files[1].name).toBe('test-file-2.docx')
    })

    test('获取文件列表失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('获取文件列表失败')

      // 模拟API服务的get方法抛出错误
      apiService.get = jest.fn().mockRejectedValue(error)

      // 调用获取文件列表函数并捕获错误
      await expect(getFileList({ knowledge_base_id: '1' })).rejects.toThrow('获取文件列表失败')
    })
  })

  // 测试获取文件详情
  describe('getFileById', () => {
    test('应该返回文件详情', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '1',
        name: 'test-file.pdf',
        original_name: 'test-file.pdf',
        path: 'uploads/test-file.pdf',
        type: 'pdf',
        mime_type: 'application/pdf',
        size: 1024,
        knowledge_base_id: '1',
        uploader: {
          id: '1',
          username: 'admin'
        },
        status: 'approved',
        summary: 'This is a test file summary',
        detailed_description: 'This is the content of the test file',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取文件详情函数
      const result = await getFileById('1')

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.id).toBe('1')
      expect(result.name).toBe('test-file.pdf')
      expect(result.summary).toBe('This is a test file summary')
    })

    test('获取不存在的文件应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('文件不存在')

      // 模拟API服务的get方法抛出错误
      apiService.get = jest.fn().mockRejectedValue(error)

      // 调用获取文件详情函数并捕获错误
      await expect(getFileById('999')).rejects.toThrow('文件不存在')
    })
  })

  // 测试上传文件
  describe('uploadFile', () => {
    test('应该成功上传文件', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '3',
        name: 'new-file.pdf',
        original_name: 'new-file.pdf',
        type: 'pdf',
        size: 3072,
        knowledge_base_id: '1',
        uploader_id: '1',
        status: 'pending',
        created_at: '2023-01-03T00:00:00Z'
      }

      // 模拟API服务的upload方法
      apiService.upload = jest.fn().mockResolvedValue(mockResponse)

      // 创建测试文件
      const file = new File(['file content'], 'new-file.pdf', { type: 'application/pdf' })

      // 调用上传文件函数
      const result = await uploadFile('1', file)

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.id).toBe('3')
      expect(result.name).toBe('new-file.pdf')
      expect(result.status).toBe('pending')
    })

    test('上传文件失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('文件大小超过限制')

      // 模拟API服务的upload方法抛出错误
      apiService.upload = jest.fn().mockRejectedValue(error)

      // 创建测试文件
      const file = new File(['file content'], 'large-file.pdf', { type: 'application/pdf' })

      // 调用上传文件函数并捕获错误
      await expect(uploadFile('1', file)).rejects.toThrow('文件大小超过限制')
    })
  })

  // 测试下载文件
  describe('downloadFile', () => {
    test('应该成功下载文件', async () => {
      // 模拟API服务的download方法
      apiService.download = jest.fn().mockResolvedValue(undefined)

      // 调用下载文件函数
      await downloadFile('1')

      // 验证API服务被调用
      expect(apiService.download).toHaveBeenCalledWith('/files/1/download', undefined)
    })

    test('下载文件失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('文件不存在')

      // 模拟API服务的download方法抛出错误
      apiService.download = jest.fn().mockRejectedValue(error)

      // 调用下载文件函数并捕获错误
      await expect(downloadFile('999')).rejects.toThrow('文件不存在')
    })
  })

  // 测试上传文件到Dify知识库
  describe('uploadFileToDify', () => {
    test('应该成功上传文件到Dify知识库', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '1',
        dify_task_id: 'dify-123456',
        status: 'approved',
        updated_at: '2023-01-04T00:00:00Z'
      }

      // 模拟API服务的post方法
      apiService.post = jest.fn().mockResolvedValue(mockResponse)

      // 调用上传文件到Dify知识库函数
      const result = await uploadFileToDify('1')

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.dify_task_id).toBe('dify-123456')
      expect(result.status).toBe('approved')
    })

    test('上传文件到Dify知识库失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('上传文件到Dify知识库失败')

      // 模拟API服务的post方法抛出错误
      apiService.post = jest.fn().mockRejectedValue(error)

      // 调用上传文件到Dify知识库函数并捕获错误
      await expect(uploadFileToDify('1')).rejects.toThrow('上传文件到Dify知识库失败')
    })
  })

  // 测试审核文件
  describe('reviewFile', () => {
    test('应该成功审核文件', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '1',
        status: 'approved',
        reject_reason: '',
        reviewer_id: '1',
        updated_at: '2023-01-05T00:00:00Z'
      }

      // 模拟API服务的put方法
      apiService.put = jest.fn().mockResolvedValue(mockResponse)

      // 调用审核文件函数
      const result = await reviewFile('1', { status: 'approved' })

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.status).toBe('approved')
    })

    test('审核文件失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('权限不足')

      // 模拟API服务的put方法抛出错误
      apiService.put = jest.fn().mockRejectedValue(error)

      // 调用审核文件函数并捕获错误
      await expect(reviewFile('1', { status: 'approved' })).rejects.toThrow('权限不足')
    })
  })

  // 测试删除文件
  describe('deleteFile', () => {
    test('应该成功删除文件', async () => {
      // 模拟成功的响应
      const mockResponse = {
        message: '文件删除成功'
      }

      // 模拟API服务的del方法
      apiService.del = jest.fn().mockResolvedValue(mockResponse)

      // 调用删除文件函数
      const result = await deleteFile('1')

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.message).toBe('文件删除成功')
    })

    test('删除文件失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('文件不存在')

      // 模拟API服务的del方法抛出错误
      apiService.del = jest.fn().mockRejectedValue(error)

      // 调用删除文件函数并捕获错误
      await expect(deleteFile('999')).rejects.toThrow('文件不存在')
    })
  })
});
