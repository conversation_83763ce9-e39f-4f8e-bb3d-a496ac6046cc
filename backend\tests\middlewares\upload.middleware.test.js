/**
 * 文件上传中间件测试
 * 
 * 测试文件上传中间件的功能
 */

const path = require('path');
const fs = require('fs');
const uploadMiddleware = require('../../src/middlewares/uploadMiddleware');
const { SystemConfig } = require('../../src/models');

describe('文件上传中间件', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;
  let testFile;
  let testFilePath;
  let systemConfig;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试文件目录
    const uploadsDir = path.join(__dirname, '../../uploads/test');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // 创建测试文件
    testFilePath = path.join(uploadsDir, 'test-upload.txt');
    fs.writeFileSync(testFilePath, 'Test file content for upload middleware testing');

    // 获取系统配置
    systemConfig = await SystemConfig.findOne({ where: { id: 1 } });
    if (!systemConfig) {
      systemConfig = await SystemConfig.create({
        id: 1,
        file_upload_max_size: 10, // 10MB
        allowed_file_types: 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif'
      });
    }

    // 创建测试文件对象
    testFile = {
      fieldname: 'file',
      originalname: 'test-upload.txt',
      encoding: '7bit',
      mimetype: 'text/plain',
      destination: uploadsDir,
      filename: 'test-upload.txt',
      path: testFilePath,
      size: fs.statSync(testFilePath).size
    };
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    // 删除测试文件
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }

    // 删除测试目录
    const uploadsDir = path.join(__dirname, '../../uploads/test');
    if (fs.existsSync(uploadsDir)) {
      fs.rmdirSync(uploadsDir);
    }

    // 如果是新创建的配置，则删除
    if (systemConfig && !systemConfig.system_name) {
      await systemConfig.destroy();
    }
  });

  // 在每个测试前初始化模拟对象
  beforeEach(() => {
    mockRequest = {
      file: testFile
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();
  });

  // 测试文件上传中间件
  describe('文件上传中间件', () => {
    test('应该在没有文件时返回错误', () => {
      // 模拟没有文件的请求
      const reqWithoutFile = { ...mockRequest, file: undefined };
      
      // 调用中间件
      uploadMiddleware(reqWithoutFile, mockResponse, mockNext);
      
      // 验证响应
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('没有文件')
        })
      );
      
      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该在文件类型不允许时返回错误', () => {
      // 模拟不允许的文件类型
      const reqWithInvalidType = {
        ...mockRequest,
        file: {
          ...testFile,
          originalname: 'test.exe',
          mimetype: 'application/x-msdownload'
        }
      };
      
      // 调用中间件
      uploadMiddleware(reqWithInvalidType, mockResponse, mockNext);
      
      // 验证响应
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('文件类型不允许')
        })
      );
      
      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该在文件大小超过限制时返回错误', () => {
      // 模拟超过大小限制的文件
      const maxSizeBytes = systemConfig.file_upload_max_size * 1024 * 1024; // 转换为字节
      const reqWithOversizedFile = {
        ...mockRequest,
        file: {
          ...testFile,
          size: maxSizeBytes + 1
        }
      };
      
      // 调用中间件
      uploadMiddleware(reqWithOversizedFile, mockResponse, mockNext);
      
      // 验证响应
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('文件大小超过限制')
        })
      );
      
      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该在文件有效时调用next', () => {
      // 调用中间件
      uploadMiddleware(mockRequest, mockResponse, mockNext);
      
      // 验证next被调用
      expect(mockNext).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith();
      
      // 验证响应方法没有被调用
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
    });

    test('应该处理文件名和类型', () => {
      // 调用中间件
      uploadMiddleware(mockRequest, mockResponse, mockNext);
      
      // 验证请求对象被正确修改
      expect(mockRequest.fileInfo).toBeDefined();
      expect(mockRequest.fileInfo.originalName).toBe('test-upload.txt');
      expect(mockRequest.fileInfo.fileName).toBe('test-upload.txt');
      expect(mockRequest.fileInfo.fileType).toBe('txt');
      expect(mockRequest.fileInfo.mimeType).toBe('text/plain');
      expect(mockRequest.fileInfo.filePath).toBe(testFilePath);
      expect(mockRequest.fileInfo.fileSize).toBe(fs.statSync(testFilePath).size);
    });
  });
});
