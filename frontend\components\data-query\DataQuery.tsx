/**
 * 数据查询组件
 *
 * 用于查询和展示数据
 */

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from '@/components/ui/use-toast'
import { LoadingState } from '@/components/ui/loading-state'

// 知识库类型
export interface KnowledgeBase {
  id: number
  name: string
  description: string
  type: string
}

// 数据源类型
export interface DataSource {
  id: number
  name: string
  description: string
  type: string
}

// 查询结果类型
export interface QueryResult {
  id: number
  query: string
  result: string
  source: string
  timestamp: string
}

// 数据查询组件
export const DataQuery = () => {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([])
  const [dataSources, setDataSources] = useState<DataSource[]>([])
  const [selectedSource, setSelectedSource] = useState<string>('')
  const [query, setQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [results, setResults] = useState<QueryResult[]>([])
  const [queryHistory, setQueryHistory] = useState<QueryResult[]>([])

  // 加载知识库和数据源
  useEffect(() => {
    const fetchSources = async () => {
      setIsLoading(true)
      setError(null)
      try {
        // 模拟数据
        const kbData = {
          data: [
            {
              id: 1,
              name: '测试知识库',
              description: '这是一个测试用的知识库',
              type: '系统知识库'
            },
            {
              id: 2,
              name: '个人知识库',
              description: '用户个人知识库',
              type: '个人知识库'
            }
          ]
        }

        const dsData = {
          data: [
            {
              id: 1,
              name: '系统数据源',
              description: '系统默认数据源',
              type: 'system'
            },
            {
              id: 2,
              name: '外部数据源',
              description: '外部API数据源',
              type: 'external'
            }
          ]
        }

        // 模拟网络延迟
        setTimeout(() => {
          setKnowledgeBases(kbData.data || [])
          setDataSources(dsData.data || [])
          setIsLoading(false)
        }, 500)
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载数据源失败')
        toast({
          title: '加载失败',
          description: err instanceof Error ? err.message : '加载数据源失败',
          variant: 'destructive'
        })
        setIsLoading(false)
      }
    }

    fetchSources()

    // 加载查询历史
    const fetchQueryHistory = async () => {
      try {
        // 模拟数据
        const historyData = {
          data: [
            {
              id: 1,
              query: '什么是人工智能?',
              result: '人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。',
              source: '知识库',
              timestamp: new Date().toISOString()
            },
            {
              id: 2,
              query: '中国的GDP是多少?',
              result: '根据最新数据，中国2023年GDP约为121万亿元人民币。',
              source: '外部数据源',
              timestamp: new Date(Date.now() - 86400000).toISOString()
            }
          ]
        }

        setTimeout(() => {
          setQueryHistory(historyData.data || [])
        }, 300)
      } catch (error) {
        console.error('加载查询历史失败', error)
      }
    }

    fetchQueryHistory()
  }, [])

  // 处理查询
  const handleQuery = async () => {
    if (!query.trim()) {
      toast({
        title: '请输入查询内容',
        variant: 'destructive'
      })
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // 模拟API调用
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟响应数据
      const mockResult = {
        result: `这是对"${query}"的查询结果。\n\n查询来源: ${selectedSource || 'all'}\n\n查询时间: ${new Date().toLocaleString()}`
      }

      // 添加到结果和历史
      const newResult = {
        id: Date.now(),
        query,
        result: mockResult.result,
        source: selectedSource || 'all',
        timestamp: new Date().toISOString()
      }

      setResults([newResult, ...results])
      setQueryHistory([newResult, ...queryHistory])

      toast({
        title: '查询成功',
        description: '查询结果已更新'
      })
    } catch (err) {
      setError(err instanceof Error ? err.message : '查询失败')
      toast({
        title: '查询失败',
        description: err instanceof Error ? err.message : '查询失败',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理清除查询
  const handleClearQuery = () => {
    setQuery('')
  }

  // 处理选择历史查询
  const handleSelectHistory = (historyItem: QueryResult) => {
    setQuery(historyItem.query)
    setSelectedSource(historyItem.source === '全部' ? '' : historyItem.source)
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">数据查询</h1>

      <Tabs defaultValue="query">
        <TabsList>
          <TabsTrigger value="query">查询</TabsTrigger>
          <TabsTrigger value="history">历史记录</TabsTrigger>
        </TabsList>

        <TabsContent value="query" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>查询数据</CardTitle>
              <CardDescription>输入查询内容并选择数据源</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">数据源</label>
                  <Select value={selectedSource} onValueChange={setSelectedSource}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择数据源" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      <SelectItem value="knowledge_base">知识库</SelectItem>
                      <SelectItem value="external">外部数据源</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">查询内容</label>
                  <Textarea
                    placeholder="输入您的查询内容..."
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    rows={5}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={handleClearQuery}>清除</Button>
              <Button onClick={handleQuery} disabled={isLoading}>
                {isLoading ? '查询中...' : '查询'}
              </Button>
            </CardFooter>
          </Card>

          {isLoading && (
            <div className="mt-6">
              <LoadingState status="loading" loadingText="正在查询数据..." />
            </div>
          )}

          {error && (
            <div className="mt-6">
              <LoadingState status="error" errorText={error} />
            </div>
          )}

          {results.length > 0 && (
            <div className="mt-6">
              <h2 className="text-xl font-bold mb-4">查询结果</h2>
              <div className="space-y-4">
                {results.map((result) => (
                  <Card key={result.id}>
                    <CardHeader>
                      <CardTitle>查询: {result.query}</CardTitle>
                      <CardDescription>
                        数据源: {result.source} | 时间: {new Date(result.timestamp).toLocaleString()}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="whitespace-pre-wrap">{result.result}</div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="history" className="mt-4">
          <h2 className="text-xl font-bold mb-4">查询历史</h2>

          {queryHistory.length === 0 ? (
            <div className="text-center p-4">暂无查询历史</div>
          ) : (
            <div className="space-y-4">
              {queryHistory.map((history) => (
                <Card key={history.id} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleSelectHistory(history)}>
                  <CardHeader>
                    <CardTitle>{history.query}</CardTitle>
                    <CardDescription>
                      数据源: {history.source} | 时间: {new Date(history.timestamp).toLocaleString()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="whitespace-pre-wrap line-clamp-3">{history.result}</div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline">使用此查询</Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default DataQuery
