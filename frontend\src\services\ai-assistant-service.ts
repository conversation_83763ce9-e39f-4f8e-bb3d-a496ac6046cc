/**
 * AI助手服务
 *
 * 提供与AI助手相关的API调用
 */

import axios from 'axios';
import { AIAgent, AIQueryRequest, AIQueryResponse, SetKnowledgeBaseRequest, SetKnowledgeBaseResponse } from '@/types/ai-assistants';

// 获取API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';

// AI助手服务
const aiAssistantService = {
  /**
   * 获取AI助手列表
   * @param type 可选的助手类型过滤
   * @returns AI助手列表
   */
  async getAIAssistants(type?: string): Promise<AIAgent[]> {
    try {
      const params = type ? { type } : {};
      const response = await axios.get(`${API_BASE_URL}/ai`, { params });
      return response.data.data;
    } catch (error) {
      console.error('获取AI助手列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取AI助手详情
   * @param id AI助手ID
   * @returns AI助手详情
   */
  async getAIAssistantById(id: string): Promise<AIAgent> {
    try {
      console.log(`获取AI助手详情(ID: ${id})`);
      const response = await axios.get(`${API_BASE_URL}/ai/${id}`);

      const item = response.data.data;
      console.log(`获取到的AI助手详情:`, {
        id: item.id,
        name: item.name,
        type: item.type,
        api_endpoint: item.api_endpoint,
        app_id: item.app_id || '空',
        app_code: item.app_code || '空'
      });

      // 确保返回的数据符合AIAgent接口要求
      const agent: AIAgent = {
        id: item.id,
        name: item.name,
        type: item.type,
        description: item.description || '',
        tags: item.tags ? item.tags.split(',') : [],
        status: item.status === 'active' ? '正常' : '停用',
        lastUpdated: new Date(item.updated_at).toLocaleString(),
        apiKey: item.api_key || '',
        apiEndpoint: item.api_endpoint || '',
        appId: item.app_id || '',
        appCode: item.app_code || '',
        uploadApiPath: item.upload_api_path || '',
        analysisApiPath: item.analysis_api_path || ''
      };

      return agent;
    } catch (error) {
      console.error('获取AI助手详情失败:', error);
      throw error;
    }
  },

  /**
   * 更新AI助手
   * @param id AI助手ID
   * @param data 更新的数据
   * @returns 更新后的AI助手
   */
  async updateAIAssistant(id: string, data: Partial<AIAgent>): Promise<AIAgent> {
    try {
      console.log('更新AI助手 - 原始数据:', {
        ...data,
        apiKey: data.apiKey ? '已设置' : '未设置' // 不在日志中显示完整的API密钥
      });

      // 转换前端数据格式为后端所需格式
      const backendData = {
        name: data.name,
        description: data.description,
        api_key: data.apiKey,
        api_endpoint: data.apiEndpoint,
        // 确保即使是空字符串也发送
        app_id: data.appId !== undefined ? data.appId : '',
        app_code: data.appCode !== undefined ? data.appCode : '',
        upload_api_path: data.uploadApiPath,
        analysis_api_path: data.analysisApiPath,
        tags: Array.isArray(data.tags) ? data.tags.join(',') : data.tags,
        status: data.status === '正常' ? 'active' : 'inactive'
      };

      console.log('更新AI助手 - 发送到后端的数据:', {
        ...backendData,
        api_key: backendData.api_key ? '已设置' : '未设置'
      });

      const response = await axios.put(`${API_BASE_URL}/ai/${id}`, backendData);

      console.log('更新AI助手 - 后端响应:', {
        success: response.data.success,
        message: response.data.message
      });

      // 获取更新后的数据
      return this.getAIAssistantById(id);
    } catch (error) {
      console.error('更新AI助手失败:', error);
      throw error;
    }
  },

  /**
   * 个人专题助手查询
   * @param data 查询请求
   * @returns 查询响应
   */
  async queryPersonalAssistant(data: AIQueryRequest | string, conversationId?: string, personalId?: number, personalName?: string): Promise<AIQueryResponse> {
    try {
      // 处理不同的参数格式
      let requestData: any;

      if (typeof data === 'string') {
        // 如果传入的是字符串，则视为查询内容
        requestData = {
          query: data,
          conversation_id: conversationId,
          personal_id: personalId,
          personal_name: personalName
        };
      } else {
        // 如果传入的是对象，则直接使用
        requestData = data;
      }

      console.log('前端 - 查询个人专题助手:', requestData);

      const response = await axios.post(`${API_BASE_URL}/ai/personal/query`, requestData);

      console.log('前端 - 个人专题助手响应:', response.data);

      // 检查响应格式
      if (response.data && response.data.data) {
        // 处理Dify API返回的数据格式
        if (response.data.data.answer) {
          return {
            id: response.data.data.id || `ai-${Date.now()}`,
            answer: response.data.data.answer,
            conversation_id: response.data.data.conversation_id || conversationId || null,
            created_at: response.data.data.created_at || new Date().toISOString()
          };
        } else if (response.data.data.text) {
          // 某些Dify API版本可能返回text而不是answer
          return {
            id: response.data.data.id || `ai-${Date.now()}`,
            answer: response.data.data.text,
            conversation_id: response.data.data.conversation_id || conversationId || null,
            created_at: response.data.data.created_at || new Date().toISOString()
          };
        } else if (typeof response.data.data === 'string') {
          // 如果返回的是纯文本
          return {
            id: `ai-${Date.now()}`,
            answer: response.data.data,
            conversation_id: conversationId || null,
            created_at: new Date().toISOString()
          };
        } else {
          // 尝试从响应中提取有用信息
          console.warn('前端 - 个人专题助手响应格式不标准:', response.data);
          return {
            id: `ai-${Date.now()}`,
            answer: JSON.stringify(response.data.data),
            conversation_id: conversationId || null,
            created_at: new Date().toISOString()
          };
        }
      } else {
        throw new Error('API返回的响应格式不正确');
      }
    } catch (error) {
      console.error('个人专题助手查询失败:', error);
      // 添加更多错误信息
      if (error.response) {
        console.error('错误响应:', {
          status: error.response.status,
          data: error.response.data
        });
      }
      throw error;
    }
  },

  /**
   * 数据查询助手查询
   * @param data 查询请求
   * @returns 查询响应
   */
  async queryDataAssistant(data: AIQueryRequest): Promise<AIQueryResponse> {
    try {
      const response = await axios.post(`${API_BASE_URL}/ai/data-query/query`, data);
      return response.data.data;
    } catch (error) {
      console.error('数据查询助手查询失败:', error);
      throw error;
    }
  },

  /**
   * 设置数据查询助手知识库
   * @param data 知识库设置请求
   * @returns 知识库设置响应
   */
  async setDataAssistantKnowledgeBase(data: SetKnowledgeBaseRequest): Promise<SetKnowledgeBaseResponse> {
    try {
      const response = await axios.post(`${API_BASE_URL}/ai/data-query/set-knowledge-base`, data);
      return response.data.data;
    } catch (error) {
      console.error('设置数据查询助手知识库失败:', error);
      throw error;
    }
  },

  /**
   * 获取AI助手对话历史
   * @param assistantType 助手类型
   * @param conversationId 可选的会话ID
   * @returns 对话历史
   */
  async getConversationHistory(assistantType: string, conversationId?: string): Promise<any> {
    try {
      const params = conversationId ? { conversation_id: conversationId } : {};
      const response = await axios.get(`${API_BASE_URL}/ai/conversations/${assistantType}`, { params });
      return response.data.data;
    } catch (error) {
      console.error('获取AI助手对话历史失败:', error);
      throw error;
    }
  },

  /**
   * 清除AI助手对话历史
   * @param assistantType 助手类型
   * @param conversationId 可选的会话ID
   * @returns 操作结果
   */
  async clearConversationHistory(assistantType: string, conversationId?: string): Promise<any> {
    try {
      const data = conversationId ? { conversation_id: conversationId } : {};
      const response = await axios.delete(`${API_BASE_URL}/ai/conversations/${assistantType}`, { data });
      return response.data;
    } catch (error) {
      console.error('清除AI助手对话历史失败:', error);
      throw error;
    }
  },

  /**
   * 查询AI研究助手
   * @param data 查询请求或查询内容字符串
   * @param assistantId 可选的助手ID，用于指定特定的研究助手
   * @returns 查询响应
   */
  async queryResearchAssistant(data: AIQueryRequest | string, assistantId?: string): Promise<AIQueryResponse> {
    try {
      // 处理不同的参数格式
      let requestData: any;
      let url = `${API_BASE_URL}/ai/assistant/query`;

      if (typeof data === 'string') {
        // 如果传入的是字符串，则视为查询内容
        requestData = {
          query: data,
          conversation_id: undefined
        };
      } else {
        // 如果传入的是对象，则直接使用
        requestData = data;
      }

      // 如果提供了特定的助手ID，则使用特定助手的API端点
      if (assistantId) {
        url = `${API_BASE_URL}/ai/assistant/${assistantId}/query`;
      }

      console.log('前端 - 查询AI研究助手:', {
        url,
        requestData,
        assistantId: assistantId || '未指定'
      });

      const response = await axios.post(url, requestData);

      console.log('前端 - AI研究助手响应:', response.data);

      // 检查响应格式
      if (response.data && response.data.data) {
        // 处理API返回的数据格式
        if (response.data.data.answer) {
          return {
            id: response.data.data.id || `ai-${Date.now()}`,
            answer: response.data.data.answer,
            conversation_id: response.data.data.conversation_id || (typeof data === 'object' ? data.conversation_id : '') || '',
            created_at: response.data.data.created_at || new Date().toISOString()
          };
        } else if (response.data.data.text) {
          // 某些API版本可能返回text而不是answer
          return {
            id: response.data.data.id || `ai-${Date.now()}`,
            answer: response.data.data.text,
            conversation_id: response.data.data.conversation_id || (typeof data === 'object' ? data.conversation_id : '') || '',
            created_at: response.data.data.created_at || new Date().toISOString()
          };
        } else if (typeof response.data.data === 'string') {
          // 如果返回的是纯文本
          return {
            id: `ai-${Date.now()}`,
            answer: response.data.data,
            conversation_id: (typeof data === 'object' ? data.conversation_id : '') || '',
            created_at: new Date().toISOString()
          };
        } else {
          // 尝试从响应中提取有用信息
          console.warn('前端 - AI研究助手响应格式不标准:', response.data);
          return {
            id: `ai-${Date.now()}`,
            answer: JSON.stringify(response.data.data),
            conversation_id: (typeof data === 'object' ? data.conversation_id : '') || '',
            created_at: new Date().toISOString()
          };
        }
      } else {
        throw new Error('API返回的响应格式不正确');
      }
    } catch (error) {
      console.error('查询AI研究助手失败:', error);
      // 添加更多错误信息
      if (error.response) {
        console.error('错误响应:', {
          status: error.response.status,
          data: error.response.data
        });
      }
      throw error;
    }
  }
};

export default aiAssistantService;
