'use strict';

/**
 * 角色初始化种子文件
 * 初始化管理员和初级访问者两个角色
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.bulkInsert('roles', [
      {
        id: 1,
        name: '管理员',
        description: '拥有全部权限',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 4,
        name: '初级访问者',
        description: '新注册用户的默认角色',
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete('roles', null, {});
  }
};
