"use client"

import Link, { LinkProps } from 'next/link'
import { useRouter } from 'next/navigation'
import { forwardRef, ReactNode } from 'react'
import { triggerRouteChangeStart } from '@/lib/performance-utils'

interface OptimizedLinkProps extends LinkProps {
  children: ReactNode
  className?: string
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void
}

/**
 * 优化的Link组件
 * 
 * 提供更流畅的页面跳转体验，触发路由变化事件
 */
export const OptimizedLink = forwardRef<HTMLAnchorElement, OptimizedLinkProps>(
  ({ children, href, className, onClick, ...props }, ref) => {
    const router = useRouter()
    
    const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
      // 如果提供了onClick回调，则执行
      if (onClick) {
        onClick(e)
      }
      
      // 如果没有阻止默认行为，则触发路由变化开始事件
      if (!e.defaultPrevented) {
        triggerRouteChangeStart()
      }
    }
    
    return (
      <Link
        ref={ref}
        href={href}
        className={className}
        onClick={handleClick}
        {...props}
      >
        {children}
      </Link>
    )
  }
)

OptimizedLink.displayName = 'OptimizedLink'
