import React from 'react';
import { LucideIcon } from 'lucide-react';

/**
 * 图标包装器组件
 * 用于统一处理图标的渲染，确保服务器端和客户端渲染一致
 *
 * @param props 组件属性
 * @returns 包装后的图标组件
 */
interface IconWrapperProps {
  name: string; // 图标名称，用于确保服务器端和客户端渲染一致
  className?: string;
  children?: React.ReactNode;
}

export const IconWrapper: React.FC<IconWrapperProps> = ({
  name,
  className = '',
  children,
}) => {
  return (
    <span className={className} data-icon-name={name}>
      {children}
    </span>
  );
};
