/**
 * 全局错误处理工具
 *
 * 用于捕获和记录全局未处理的错误，以及处理特定类型的错误
 */

import { logger } from './logger';

/**
 * 自定义权限错误类
 */
export class PermissionError extends Error {
  response?: any;

  constructor(message: string = '您没有权限执行此操作', response?: any) {
    super(message);
    this.name = 'PermissionError';
    this.response = response;

    // 这一行是为了正确继承Error类
    Object.setPrototypeOf(this, PermissionError.prototype);
  }
}

/**
 * 格式化错误消息
 * @param error 错误对象
 * @returns 格式化后的错误消息
 */
export const formatErrorMessage = (error: any): string => {
  // 如果是自定义的PermissionError，直接返回错误消息
  if (error instanceof PermissionError) {
    return error.message;
  }

  // 如果是Axios错误，尝试从响应中获取错误消息
  if (error.response?.data?.message) {
    return error.response.data.message;
  }

  // 如果是HTTP 403错误，返回权限不足消息
  if (error.response?.status === 403) {
    return error.response.data?.message || '您没有权限执行此操作';
  }

  // 如果是HTTP 401错误，返回未授权消息
  if (error.response?.status === 401) {
    return '请先登录后再执行此操作';
  }

  // 如果是HTTP 404错误，返回资源不存在消息
  if (error.response?.status === 404) {
    return '请求的资源不存在';
  }

  // 如果是HTTP 500错误，返回服务器错误消息
  if (error.response?.status === 500) {
    return '服务器内部错误，请稍后再试';
  }

  // 如果是网络错误，返回网络错误消息
  if (error.message === 'Network Error') {
    return '网络错误，请检查您的网络连接';
  }

  // 其他错误，返回通用错误消息
  return error.message || '操作失败，请稍后再试';
};

/**
 * 处理错误
 * @param error 错误对象
 * @param silent 是否静默处理（不记录到控制台）
 * @returns 格式化后的错误消息
 */
export const handleError = (error: any, silent: boolean = false): string => {
  const errorMessage = formatErrorMessage(error);

  // 根据错误类型决定日志级别
  if (error instanceof PermissionError || error.response?.status === 403) {
    // 权限错误使用info级别
    if (!silent) {
      logger.info('权限错误:', errorMessage);
    }
  } else {
    // 其他错误使用error级别
    if (!silent) {
      logger.error('操作失败:', error);
    }
  }

  return errorMessage;
};

/**
 * 从Axios错误创建适当的错误对象
 * @param error Axios错误
 * @returns 格式化后的错误对象
 */
export const createErrorFromResponse = (error: any): Error => {
  // 处理403权限错误
  if (error.response?.status === 403) {
    return new PermissionError(
      error.response.data?.message || '您没有权限执行此操作',
      error.response
    );
  }

  // 其他错误，保留原始错误
  return error;
};

/**
 * 初始化全局错误处理
 */
export const initGlobalErrorHandlers = () => {
  if (typeof window !== 'undefined') {
    // 捕获未处理的Promise错误
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未处理的Promise错误:', {
        reason: event.reason,
        stack: event.reason?.stack,
        message: event.reason?.message
      })
    })

    // 捕获全局错误
    window.addEventListener('error', (event) => {
      console.error('全局错误:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        stack: event.error?.stack
      })
    })

    // 重写console.error，添加更多上下文信息
    const originalConsoleError = console.error
    console.error = (...args) => {
      // 添加时间戳
      const timestamp = new Date().toISOString()
      originalConsoleError(`[${timestamp}]`, ...args)

      // 在这里可以添加将错误发送到服务器的逻辑
    }
  }
}

/**
 * 包装异步函数，添加错误处理
 * @param fn 异步函数
 * @param errorHandler 错误处理函数
 */
export const withErrorHandling = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  errorHandler?: (error: any) => void
) => {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args)
    } catch (error) {
      console.error('函数执行错误:', {
        functionName: fn.name,
        arguments: args,
        error,
        stack: error?.stack
      })

      if (errorHandler) {
        errorHandler(error)
      }

      throw error
    }
  }
}

export default {
  PermissionError,
  formatErrorMessage,
  handleError,
  createErrorFromResponse,
  initGlobalErrorHandlers,
  withErrorHandling
}
