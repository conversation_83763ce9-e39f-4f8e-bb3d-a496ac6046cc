/**
 * 角色服务
 *
 * 提供与角色和权限相关的API调用
 */

import axios from 'axios';

// 获取API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5001/api';

// 角色类型
export interface Role {
  id: number;
  name: string;
  description: string;
  is_preset: boolean;
  created_at: string;
  updated_at: string;
  permissions?: Permission[];
  user_count?: number;
}

// 权限类型
export interface Permission {
  id: number;
  code: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
}

// 角色权限关联类型
export interface RolePermission {
  role_id: number;
  permission_id: number;
}

// 创建角色请求
export interface CreateRoleRequest {
  name: string;
  description: string;
  permission_ids?: number[];
}

// 更新角色请求
export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permission_ids?: number[];
}

// 角色服务
const roleService = {
  /**
   * 获取所有角色
   * @returns 角色列表
   */
  async getAllRoles(): Promise<Role[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/roles`);
      return response.data.data;
    } catch (error) {
      console.error('获取所有角色失败:', error);
      throw error;
    }
  },

  /**
   * 获取角色详情
   * @param id 角色ID
   * @returns 角色详情
   */
  async getRoleById(id: number): Promise<Role> {
    try {
      const response = await axios.get(`${API_BASE_URL}/roles/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('获取角色详情失败:', error);
      throw error;
    }
  },

  /**
   * 创建角色
   * @param data 创建角色请求数据
   * @returns 创建的角色
   */
  async createRole(data: CreateRoleRequest): Promise<Role> {
    try {
      const response = await axios.post(`${API_BASE_URL}/roles`, data);
      return response.data.data;
    } catch (error) {
      console.error('创建角色失败:', error);
      throw error;
    }
  },

  /**
   * 更新角色
   * @param id 角色ID
   * @param data 更新角色请求数据
   * @returns 更新后的角色
   */
  async updateRole(id: number, data: UpdateRoleRequest): Promise<Role> {
    try {
      const response = await axios.put(`${API_BASE_URL}/roles/${id}`, data);
      return response.data.data;
    } catch (error) {
      console.error('更新角色失败:', error);
      throw error;
    }
  },

  /**
   * 删除角色
   * @param id 角色ID
   * @returns 操作结果
   */
  async deleteRole(id: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.delete(`${API_BASE_URL}/roles/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除角色失败:', error);
      throw error;
    }
  },

  /**
   * 获取所有权限
   * @returns 权限列表
   */
  async getAllPermissions(): Promise<Permission[]> {
    try {
      const token = localStorage.getItem('hefamily_token');
      if (!token) {
        throw new Error('未找到认证token');
      }

      const response = await axios.get(`${API_BASE_URL}/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('获取所有权限响应:', response.data);
      return response.data.data || [];
    } catch (error) {
      console.error('获取所有权限失败:', error);
      throw error;
    }
  },

  /**
   * 获取角色的权限
   * @param roleId 角色ID
   * @returns 权限列表
   */
  async getRolePermissions(roleId: number): Promise<Permission[]> {
    try {
      const token = localStorage.getItem('hefamily_token');
      if (!token) {
        throw new Error('未找到认证token');
      }

      console.log(`获取角色权限: roleId=${roleId}`);
      const response = await axios.get(`${API_BASE_URL}/roles/${roleId}/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('获取角色权限响应:', response.data);
      return response.data.data || [];
    } catch (error) {
      console.error('获取角色权限失败:', error);
      throw error;
    }
  },

  /**
   * 设置角色权限
   * @param roleId 角色ID
   * @param permissionIds 权限ID列表
   * @returns 操作结果
   */
  async setRolePermissions(roleId: number, permissionIds: number[]): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`调用 setRolePermissions API: roleId=${roleId}, permissionIds=`, permissionIds);

      // 根据后端API路由定义，应该使用PUT方法
      const response = await axios.put(
        `${API_BASE_URL}/roles/${roleId}/permissions`,
        { permissions: permissionIds },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('hefamily_token')}`
          }
        }
      );

      console.log('设置角色权限API响应:', response.data);
      return response.data;
    } catch (error) {
      console.error('设置角色权限失败:', error);
      throw error;
    }
  }
};

export default roleService;
