/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '5001',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: '*************',
        port: '5001',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: '*************',
        pathname: '/**',
      },
    ],
  },
  // 禁用开发环境中的指示器（包括左下角的 Turbopack 按钮）
  devIndicators: false,
  // 添加以下配置以解决引用错误
  reactStrictMode: false,
  // 添加API代理配置，将/api开头的请求和静态文件请求转发到后端服务器
  async rewrites() {
    // 获取环境变量中的API URL，如果没有则使用默认值
    const apiUrl = process.env.API_URL || 'http://localhost:5001';

    console.log('API URL for rewrites:', apiUrl);

    return [
      {
        source: '/api/:path*',
        destination: `${apiUrl}/api/:path*`,
      },
      {
        source: '/public/:path*',
        destination: `${apiUrl}/public/:path*`,
      },
      {
        source: '/uploads/:path*',
        destination: `${apiUrl}/uploads/:path*`,
      },
    ];
  },
  // 添加以下配置以解决错误组件问题
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };

    return config;
  },
}

module.exports = nextConfig
