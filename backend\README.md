# 和富家族研究平台后端服务

这是和富家族研究平台的后端服务，提供API接口支持前端应用。

## 技术栈

- Node.js
- Express
- MySQL (Sequelize ORM)
- JWT认证
- 集成Dify API

## 目录结构

```
backend/
├── config/             # 配置文件
├── src/                # 源代码
│   ├── controllers/    # 控制器
│   ├── models/         # 数据模型
│   ├── routes/         # API路由
│   ├── services/       # 业务逻辑
│   ├── utils/          # 工具函数
│   ├── middlewares/    # 中间件
│   └── app.js          # 应用入口
├── tests/              # 测试文件
├── .env.example        # 环境变量示例
├── package.json        # 项目依赖
└── README.md           # 项目文档
```

## 安装与运行

1. 安装依赖：
   ```
   npm install
   ```

2. 创建环境变量文件：
   ```
   cp .env.example .env
   ```
   然后编辑 `.env` 文件，填入实际的配置值。

3. 启动开发服务器：
   ```
   npm run dev
   ```

4. 生产环境启动：
   ```
   npm start
   ```

## API文档

API文档将在开发完成后提供。

## 测试

运行测试：
```
npm test
```

## 许可证

[MIT](LICENSE)
