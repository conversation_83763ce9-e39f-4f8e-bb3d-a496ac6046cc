/**
 * 个人中心组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { PersonalCenter } from '@/components/user/PersonalCenter'
import { mockFetch, resetMockFetch } from '../test-utils'

// 模拟用户数据
const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  phone: '13800138000',
  role: 'basic_user',
  avatar: '/images/avatar.jpg',
  created_at: '2023-01-01T00:00:00Z'
}

// 模拟通知数据
const mockNotifications = [
  {
    id: 1,
    title: '系统通知',
    content: '欢迎使用和富家族研究平台',
    type: 'system',
    status: 'unread',
    created_at: '2023-01-02T00:00:00Z'
  },
  {
    id: 2,
    title: '评论通知',
    content: '您的评论已被审核通过',
    type: 'comment',
    status: 'read',
    created_at: '2023-01-03T00:00:00Z'
  }
]

// 模拟知识库数据
const mockKnowledgeBases = [
  {
    id: 1,
    name: '我的知识库1',
    description: '个人知识库1描述',
    type: 'user',
    file_count: 5,
    created_at: '2023-01-04T00:00:00Z'
  },
  {
    id: 2,
    name: '我的知识库2',
    description: '个人知识库2描述',
    type: 'user',
    file_count: 3,
    created_at: '2023-01-05T00:00:00Z'
  }
]

describe('PersonalCenter组件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch()
    jest.clearAllMocks()
  })

  // 测试基本渲染
  test('应该正确渲染个人中心组件', () => {
    render(
      <PersonalCenter
        user={mockUser}
        notifications={mockNotifications}
        knowledgeBases={mockKnowledgeBases}
        isLoading={false}
        onProfileUpdate={jest.fn()}
        onPasswordChange={jest.fn()}
        onNotificationRead={jest.fn()}
        onNotificationDelete={jest.fn()}
        onKnowledgeBaseCreate={jest.fn()}
        onKnowledgeBaseDelete={jest.fn()}
      />
    )
    
    // 验证个人信息
    expect(screen.getByText('个人信息')).toBeInTheDocument()
    expect(screen.getByText('testuser')).toBeInTheDocument()
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByDisplayValue('13800138000')).toBeInTheDocument()
    
    // 验证通知
    expect(screen.getByText('我的通知')).toBeInTheDocument()
    expect(screen.getByText('系统通知')).toBeInTheDocument()
    expect(screen.getByText('评论通知')).toBeInTheDocument()
    
    // 验证知识库
    expect(screen.getByText('我的知识库')).toBeInTheDocument()
    expect(screen.getByText('我的知识库1')).toBeInTheDocument()
    expect(screen.getByText('我的知识库2')).toBeInTheDocument()
  })

  // 测试加载状态
  test('应该显示加载状态', () => {
    render(
      <PersonalCenter
        user={null}
        notifications={[]}
        knowledgeBases={[]}
        isLoading={true}
        onProfileUpdate={jest.fn()}
        onPasswordChange={jest.fn()}
        onNotificationRead={jest.fn()}
        onNotificationDelete={jest.fn()}
        onKnowledgeBaseCreate={jest.fn()}
        onKnowledgeBaseDelete={jest.fn()}
      />
    )
    
    expect(screen.getByText('加载中...')).toBeInTheDocument()
  })

  // 测试个人信息更新
  test('应该调用个人信息更新函数', async () => {
    const handleProfileUpdate = jest.fn().mockResolvedValue({
      ...mockUser,
      email: '<EMAIL>',
      phone: '13900139000'
    })
    
    render(
      <PersonalCenter
        user={mockUser}
        notifications={mockNotifications}
        knowledgeBases={mockKnowledgeBases}
        isLoading={false}
        onProfileUpdate={handleProfileUpdate}
        onPasswordChange={jest.fn()}
        onNotificationRead={jest.fn()}
        onNotificationDelete={jest.fn()}
        onKnowledgeBaseCreate={jest.fn()}
        onKnowledgeBaseDelete={jest.fn()}
      />
    )
    
    // 修改邮箱和手机号
    const emailInput = screen.getByLabelText('邮箱')
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    
    const phoneInput = screen.getByLabelText('手机号')
    fireEvent.change(phoneInput, { target: { value: '13900139000' } })
    
    // 点击保存按钮
    const saveButton = screen.getByRole('button', { name: '保存信息' })
    fireEvent.click(saveButton)
    
    // 验证更新函数被调用
    expect(handleProfileUpdate).toHaveBeenCalledWith({
      email: '<EMAIL>',
      phone: '13900139000'
    })
    
    // 等待更新完成
    await waitFor(() => {
      expect(screen.getByText('个人信息已更新')).toBeInTheDocument()
    })
  })

  // 测试密码修改
  test('应该调用密码修改函数', async () => {
    const handlePasswordChange = jest.fn().mockResolvedValue({
      success: true,
      message: '密码修改成功'
    })
    
    render(
      <PersonalCenter
        user={mockUser}
        notifications={mockNotifications}
        knowledgeBases={mockKnowledgeBases}
        isLoading={false}
        onProfileUpdate={jest.fn()}
        onPasswordChange={handlePasswordChange}
        onNotificationRead={jest.fn()}
        onNotificationDelete={jest.fn()}
        onKnowledgeBaseCreate={jest.fn()}
        onKnowledgeBaseDelete={jest.fn()}
      />
    )
    
    // 切换到修改密码选项卡
    const passwordTab = screen.getByText('修改密码')
    fireEvent.click(passwordTab)
    
    // 输入密码
    const oldPasswordInput = screen.getByLabelText('当前密码')
    fireEvent.change(oldPasswordInput, { target: { value: 'oldpassword' } })
    
    const newPasswordInput = screen.getByLabelText('新密码')
    fireEvent.change(newPasswordInput, { target: { value: 'Newpassword123!' } })
    
    const confirmPasswordInput = screen.getByLabelText('确认新密码')
    fireEvent.change(confirmPasswordInput, { target: { value: 'Newpassword123!' } })
    
    // 点击修改按钮
    const changeButton = screen.getByRole('button', { name: '修改密码' })
    fireEvent.click(changeButton)
    
    // 验证修改函数被调用
    expect(handlePasswordChange).toHaveBeenCalledWith(
      'oldpassword',
      'Newpassword123!'
    )
    
    // 等待修改完成
    await waitFor(() => {
      expect(screen.getByText('密码修改成功')).toBeInTheDocument()
    })
  })

  // 测试通知标记为已读
  test('应该调用通知标记为已读函数', async () => {
    const handleNotificationRead = jest.fn().mockResolvedValue({
      id: 1,
      status: 'read'
    })
    
    render(
      <PersonalCenter
        user={mockUser}
        notifications={mockNotifications}
        knowledgeBases={mockKnowledgeBases}
        isLoading={false}
        onProfileUpdate={jest.fn()}
        onPasswordChange={jest.fn()}
        onNotificationRead={handleNotificationRead}
        onNotificationDelete={jest.fn()}
        onKnowledgeBaseCreate={jest.fn()}
        onKnowledgeBaseDelete={jest.fn()}
      />
    )
    
    // 切换到通知选项卡
    const notificationsTab = screen.getByText('我的通知')
    fireEvent.click(notificationsTab)
    
    // 点击未读通知
    const unreadNotification = screen.getByText('系统通知')
    fireEvent.click(unreadNotification)
    
    // 验证标记为已读函数被调用
    expect(handleNotificationRead).toHaveBeenCalledWith(1)
    
    // 等待标记完成
    await waitFor(() => {
      expect(screen.getByText('通知已标记为已读')).toBeInTheDocument()
    })
  })

  // 测试通知删除
  test('应该调用通知删除函数', async () => {
    const handleNotificationDelete = jest.fn().mockResolvedValue({
      success: true
    })
    
    render(
      <PersonalCenter
        user={mockUser}
        notifications={mockNotifications}
        knowledgeBases={mockKnowledgeBases}
        isLoading={false}
        onProfileUpdate={jest.fn()}
        onPasswordChange={jest.fn()}
        onNotificationRead={jest.fn()}
        onNotificationDelete={handleNotificationDelete}
        onKnowledgeBaseCreate={jest.fn()}
        onKnowledgeBaseDelete={jest.fn()}
      />
    )
    
    // 切换到通知选项卡
    const notificationsTab = screen.getByText('我的通知')
    fireEvent.click(notificationsTab)
    
    // 点击删除按钮
    const deleteButtons = screen.getAllByText('删除')
    fireEvent.click(deleteButtons[0])
    
    // 确认删除
    const confirmButton = screen.getByText('确认')
    fireEvent.click(confirmButton)
    
    // 验证删除函数被调用
    expect(handleNotificationDelete).toHaveBeenCalledWith(1)
    
    // 等待删除完成
    await waitFor(() => {
      expect(screen.getByText('通知已删除')).toBeInTheDocument()
    })
  })

  // 测试知识库创建
  test('应该调用知识库创建函数', async () => {
    const handleKnowledgeBaseCreate = jest.fn().mockResolvedValue({
      id: 3,
      name: '新知识库',
      description: '新知识库描述',
      type: 'user',
      file_count: 0,
      created_at: new Date().toISOString()
    })
    
    render(
      <PersonalCenter
        user={mockUser}
        notifications={mockNotifications}
        knowledgeBases={mockKnowledgeBases}
        isLoading={false}
        onProfileUpdate={jest.fn()}
        onPasswordChange={jest.fn()}
        onNotificationRead={jest.fn()}
        onNotificationDelete={jest.fn()}
        onKnowledgeBaseCreate={handleKnowledgeBaseCreate}
        onKnowledgeBaseDelete={jest.fn()}
      />
    )
    
    // 切换到知识库选项卡
    const knowledgeBasesTab = screen.getByText('我的知识库')
    fireEvent.click(knowledgeBasesTab)
    
    // 点击创建知识库按钮
    const createButton = screen.getByText('创建知识库')
    fireEvent.click(createButton)
    
    // 输入知识库信息
    const nameInput = screen.getByLabelText('知识库名称')
    fireEvent.change(nameInput, { target: { value: '新知识库' } })
    
    const descriptionInput = screen.getByLabelText('知识库描述')
    fireEvent.change(descriptionInput, { target: { value: '新知识库描述' } })
    
    // 点击保存按钮
    const saveButton = screen.getByRole('button', { name: '保存' })
    fireEvent.click(saveButton)
    
    // 验证创建函数被调用
    expect(handleKnowledgeBaseCreate).toHaveBeenCalledWith({
      name: '新知识库',
      description: '新知识库描述',
      type: 'user'
    })
    
    // 等待创建完成
    await waitFor(() => {
      expect(screen.getByText('知识库创建成功')).toBeInTheDocument()
    })
  })

  // 测试知识库删除
  test('应该调用知识库删除函数', async () => {
    const handleKnowledgeBaseDelete = jest.fn().mockResolvedValue({
      success: true
    })
    
    render(
      <PersonalCenter
        user={mockUser}
        notifications={mockNotifications}
        knowledgeBases={mockKnowledgeBases}
        isLoading={false}
        onProfileUpdate={jest.fn()}
        onPasswordChange={jest.fn()}
        onNotificationRead={jest.fn()}
        onNotificationDelete={jest.fn()}
        onKnowledgeBaseCreate={jest.fn()}
        onKnowledgeBaseDelete={handleKnowledgeBaseDelete}
      />
    )
    
    // 切换到知识库选项卡
    const knowledgeBasesTab = screen.getByText('我的知识库')
    fireEvent.click(knowledgeBasesTab)
    
    // 点击删除按钮
    const deleteButtons = screen.getAllByText('删除')
    fireEvent.click(deleteButtons[0])
    
    // 确认删除
    const confirmButton = screen.getByText('确认')
    fireEvent.click(confirmButton)
    
    // 验证删除函数被调用
    expect(handleKnowledgeBaseDelete).toHaveBeenCalledWith(1)
    
    // 等待删除完成
    await waitFor(() => {
      expect(screen.getByText('知识库已删除')).toBeInTheDocument()
    })
  })

  // 测试密码验证
  test('应该验证密码输入', async () => {
    render(
      <PersonalCenter
        user={mockUser}
        notifications={mockNotifications}
        knowledgeBases={mockKnowledgeBases}
        isLoading={false}
        onProfileUpdate={jest.fn()}
        onPasswordChange={jest.fn()}
        onNotificationRead={jest.fn()}
        onNotificationDelete={jest.fn()}
        onKnowledgeBaseCreate={jest.fn()}
        onKnowledgeBaseDelete={jest.fn()}
      />
    )
    
    // 切换到修改密码选项卡
    const passwordTab = screen.getByText('修改密码')
    fireEvent.click(passwordTab)
    
    // 输入不匹配的密码
    const oldPasswordInput = screen.getByLabelText('当前密码')
    fireEvent.change(oldPasswordInput, { target: { value: 'oldpassword' } })
    
    const newPasswordInput = screen.getByLabelText('新密码')
    fireEvent.change(newPasswordInput, { target: { value: 'Newpassword123!' } })
    
    const confirmPasswordInput = screen.getByLabelText('确认新密码')
    fireEvent.change(confirmPasswordInput, { target: { value: 'DifferentPassword123!' } })
    
    // 点击修改按钮
    const changeButton = screen.getByRole('button', { name: '修改密码' })
    fireEvent.click(changeButton)
    
    // 验证错误消息
    expect(screen.getByText('两次输入的密码不一致')).toBeInTheDocument()
  })

  // 测试空通知和知识库
  test('应该处理空通知和知识库', () => {
    render(
      <PersonalCenter
        user={mockUser}
        notifications={[]}
        knowledgeBases={[]}
        isLoading={false}
        onProfileUpdate={jest.fn()}
        onPasswordChange={jest.fn()}
        onNotificationRead={jest.fn()}
        onNotificationDelete={jest.fn()}
        onKnowledgeBaseCreate={jest.fn()}
        onKnowledgeBaseDelete={jest.fn()}
      />
    )
    
    // 切换到通知选项卡
    const notificationsTab = screen.getByText('我的通知')
    fireEvent.click(notificationsTab)
    
    // 验证空通知提示
    expect(screen.getByText('暂无通知')).toBeInTheDocument()
    
    // 切换到知识库选项卡
    const knowledgeBasesTab = screen.getByText('我的知识库')
    fireEvent.click(knowledgeBasesTab)
    
    // 验证空知识库提示
    expect(screen.getByText('暂无知识库')).toBeInTheDocument()
  })
});
