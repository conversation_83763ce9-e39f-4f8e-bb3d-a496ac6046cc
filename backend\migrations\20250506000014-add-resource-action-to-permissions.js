'use strict';

/**
 * 添加resource和action字段到permissions表
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('permissions', 'resource', {
      type: Sequelize.STRING(50),
      allowNull: true,
      after: 'module'
    });

    await queryInterface.addColumn('permissions', 'action', {
      type: Sequelize.STRING(50),
      allowNull: true,
      after: 'resource'
    });

    return Promise.resolve();
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('permissions', 'resource');
    await queryInterface.removeColumn('permissions', 'action');
    
    return Promise.resolve();
  }
};
