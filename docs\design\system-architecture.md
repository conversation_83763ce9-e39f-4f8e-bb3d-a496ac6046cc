# 和富家族研究平台系统架构文档

## 1. 概述

本文档描述了和富家族研究平台的系统架构设计，包括系统的整体架构、技术栈选择、组件划分、数据流向和部署方案等内容。系统采用简单高效的架构设计，满足当前用户规模（100-200人）和访问量（日访问几百次）的需求，同时为未来的扩展预留空间。

## 2. 架构目标与约束

### 2.1 架构目标

1. **简单高效**：采用简单易维护的架构，降低开发和运维成本
2. **可扩展性**：支持功能模块的扩展和用户规模的增长
3. **可维护性**：便于问题定位和系统升级
4. **安全可靠**：保护用户数据和系统资源，防止未授权访问
5. **性能适中**：满足当前用户规模和访问量的性能需求

### 2.2 架构约束

1. **技术栈约束**：
   - 前端：React.js + Next.js + Tailwind CSS
   - 后端：Node.js + Express.js
   - 数据库：MySQL
   - AI服务：Dify平台

2. **资源约束**：
   - 服务器资源有限，需要优化资源利用
   - 开发和运维团队规模小

3. **性能约束**：
   - 支持100-200名用户同时在线
   - 日访问量在几百次
   - 页面加载时间不超过3秒

## 3. 系统架构

### 3.1 整体架构

和富家族研究平台采用经典的三层架构，包括表示层、业务逻辑层和数据访问层，同时集成外部服务（如Dify AI服务）。

![系统架构图](../public/images/system-architecture.png)

#### 3.1.1 表示层（前端）

表示层负责用户界面的展示和用户交互，采用React.js框架和Next.js框架实现，使用Tailwind CSS进行样式设计。

主要组件：
- **页面组件**：实现各功能页面的UI展示
- **共享组件**：如导航栏、页脚、模态框等可复用组件
- **状态管理**：使用React的Context API和Hooks管理前端状态
- **路由管理**：使用Next.js的路由系统管理页面导航

#### 3.1.2 业务逻辑层（后端）

业务逻辑层负责处理业务逻辑和数据处理，采用Node.js和Express.js框架实现。

主要组件：
- **API控制器**：处理前端请求，实现业务逻辑
- **服务层**：封装核心业务逻辑，提供服务接口
- **中间件**：处理认证、日志、错误处理等横切关注点
- **外部服务集成**：与Dify AI服务、文件存储服务等外部服务集成

#### 3.1.3 数据访问层

数据访问层负责与数据库交互，实现数据的存储和检索，采用MySQL数据库。

主要组件：
- **数据访问对象（DAO）**：封装数据库操作
- **ORM框架**：使用Sequelize ORM框架简化数据库操作
- **数据模型**：定义系统中的数据实体和关系

#### 3.1.4 外部服务

系统集成了多个外部服务，扩展系统功能：
- **Dify AI服务**：提供AI助手功能
- **文件存储服务**：存储用户上传的文件
- **邮件服务**：发送系统通知和邮件

### 3.2 技术栈选择

#### 3.2.1 前端技术栈

- **React.js**：用于构建用户界面的JavaScript库
- **Next.js**：基于React的服务端渲染框架，提供路由、服务端渲染等功能
- **Tailwind CSS**：实用优先的CSS框架，用于快速构建自定义设计
- **Axios**：用于发送HTTP请求的库
- **React Hook Form**：用于表单处理和验证的库
- **ShadcnUI**：基于Tailwind CSS的UI组件库

#### 3.2.2 后端技术栈

- **Node.js**：JavaScript运行时环境
- **Express.js**：Node.js Web应用框架
- **Sequelize**：Node.js ORM框架，用于数据库操作
- **Passport.js**：用于用户认证的中间件
- **JWT**：用于生成和验证JSON Web Token
- **Multer**：用于处理文件上传的中间件

#### 3.2.3 数据库

- **MySQL**：关系型数据库，用于存储系统数据
- **Redis**（可选）：内存数据库，用于缓存和会话管理

#### 3.2.4 开发和部署工具

- **Git**：版本控制系统
- **npm/yarn**：包管理工具
- **ESLint**：JavaScript代码检查工具
- **Prettier**：代码格式化工具
- **Jest**：JavaScript测试框架
- **Docker**（可选）：容器化部署工具
- **PM2**：Node.js进程管理工具

### 3.3 组件划分

系统按功能模块划分为多个组件，每个组件负责特定的功能：

#### 3.3.1 前端组件

1. **核心组件**：
   - **Navbar**：导航栏组件
   - **Footer**：页脚组件
   - **LoginModal**：登录/注册弹窗组件
   - **NotificationSystem**：通知系统组件

2. **页面组件**：
   - **HomePage**：首页组件
   - **FamilyTopicPage**：家族专题页面组件
   - **PersonalTopicPage**：个人专题页面组件
   - **KnowledgeBasePage**：知识库页面组件
   - **DataQueryPage**：数据查询页面组件
   - **AIAssistantPage**：AI研究助手页面组件
   - **SystemManagementPage**：系统管理页面组件
   - **PersonalCenterPage**：个人中心页面组件

3. **功能组件**：
   - **MemorialActivities**：纪念活动组件
   - **FileUploader**：文件上传组件
   - **SearchComponent**：搜索组件
   - **AIChat**：AI对话组件
   - **DataTable**：数据表格组件

#### 3.3.2 后端组件

1. **API控制器**：
   - **UserController**：用户管理控制器
   - **ContentController**：内容管理控制器
   - **ActivityController**：活动管理控制器
   - **KnowledgeController**：知识库管理控制器
   - **DataQueryController**：数据查询控制器
   - **AIController**：AI助手控制器
   - **SystemController**：系统管理控制器
   - **NotificationController**：通知管理控制器

2. **服务层**：
   - **UserService**：用户服务
   - **ContentService**：内容服务
   - **ActivityService**：活动服务
   - **KnowledgeService**：知识库服务
   - **DataQueryService**：数据查询服务
   - **AIService**：AI服务
   - **SystemService**：系统服务
   - **NotificationService**：通知服务
   - **FileService**：文件服务

3. **中间件**：
   - **AuthMiddleware**：认证中间件
   - **LogMiddleware**：日志中间件
   - **ErrorMiddleware**：错误处理中间件
   - **ValidationMiddleware**：数据验证中间件

4. **数据访问层**：
   - **UserDAO**：用户数据访问对象
   - **ContentDAO**：内容数据访问对象
   - **ActivityDAO**：活动数据访问对象
   - **KnowledgeDAO**：知识库数据访问对象
   - **DataQueryDAO**：数据查询数据访问对象
   - **AIDAO**：AI助手数据访问对象
   - **SystemDAO**：系统数据访问对象
   - **NotificationDAO**：通知数据访问对象

### 3.4 数据流向

系统中的数据流向主要包括以下几种：

#### 3.4.1 用户请求流程

1. 用户通过浏览器访问系统
2. 前端组件渲染页面并处理用户交互
3. 前端通过API请求后端服务
4. 后端控制器接收请求并调用相应的服务
5. 服务层处理业务逻辑并调用数据访问层
6. 数据访问层与数据库交互，获取或修改数据
7. 数据沿原路径返回给用户

#### 3.4.2 AI助手交互流程

1. 用户在前端输入问题
2. 前端发送请求到后端AI控制器
3. AI控制器调用AI服务
4. AI服务调用Dify API获取回答
5. 回答返回给前端并显示给用户

#### 3.4.3 文件上传流程

1. 用户在前端选择文件并上传
2. 前端发送文件到后端文件控制器
3. 文件控制器调用文件服务
4. 文件服务将文件保存到文件存储服务
5. 文件服务调用知识库文件分析助手分析文件内容
6. 分析结果保存到数据库
7. 上传结果返回给前端并显示给用户

#### 3.4.4 通知流程

1. 系统事件触发通知（如文件审核结果、评论批准等）
2. 通知服务创建通知并保存到数据库
3. 用户登录或刷新页面时，前端请求通知数据
4. 后端返回通知数据
5. 前端显示通知给用户

### 3.5 安全架构

系统采用多层次的安全架构，保护用户数据和系统资源：

#### 3.5.1 认证与授权

- **用户认证**：使用JWT（JSON Web Token）进行用户认证
- **密码安全**：密码加密存储，使用bcrypt等算法
- **会话管理**：会话超时自动登出
- **权限控制**：基于角色的访问控制（RBAC）

#### 3.5.2 数据安全

- **数据传输**：使用HTTPS加密传输
- **数据存储**：敏感数据加密存储
- **数据备份**：定期备份数据库
- **输入验证**：防止SQL注入、XSS等攻击

#### 3.5.3 API安全

- **API认证**：API请求需要认证
- **请求限制**：限制API请求频率，防止DoS攻击
- **CORS配置**：合理配置跨域资源共享策略

## 4. 部署架构

### 4.1 部署方案

系统采用简单的单服务器部署方案，适合当前用户规模和访问量：

![部署架构图](../public/images/deployment-architecture.png)

#### 4.1.1 服务器配置

- **Web服务器**：Nginx，用于静态资源服务和反向代理
- **应用服务器**：Node.js应用服务器，运行Express.js应用
- **数据库服务器**：MySQL数据库服务器
- **缓存服务器**（可选）：Redis缓存服务器

#### 4.1.2 部署流程

1. 前端代码构建生成静态资源
2. 静态资源部署到Nginx服务器
3. 后端代码部署到Node.js应用服务器
4. 配置Nginx反向代理，将API请求转发到Node.js应用服务器
5. 配置数据库连接
6. 配置外部服务集成

### 4.2 扩展性考虑

虽然当前采用单服务器部署方案，但系统设计考虑了未来的扩展需求：

1. **水平扩展**：可以增加多个应用服务器实例，通过负载均衡分发请求
2. **垂直扩展**：可以增加服务器资源（CPU、内存等）
3. **数据库扩展**：可以实现数据库读写分离、分库分表等
4. **微服务架构**：可以将系统拆分为多个微服务，独立部署和扩展

### 4.3 监控与运维

系统部署后需要进行监控和运维，确保系统稳定运行：

1. **日志管理**：收集和分析系统日志
2. **性能监控**：监控系统性能指标（CPU、内存、响应时间等）
3. **异常监控**：监控系统异常和错误
4. **备份恢复**：定期备份数据，制定恢复方案
5. **安全更新**：及时更新系统和依赖库，修复安全漏洞

## 5. 接口设计

### 5.1 前后端接口

系统采用RESTful风格的API设计，主要接口包括：

#### 5.1.1 用户接口

- `POST /api/users/register`：用户注册
- `POST /api/users/login`：用户登录
- `GET /api/users/profile`：获取用户信息
- `PUT /api/users/profile`：更新用户信息
- `PUT /api/users/password`：修改密码
- `POST /api/users/logout`：用户退出登录

#### 5.1.2 内容接口

- `GET /api/content/home`：获取首页内容
- `GET /api/content/family-topic`：获取家族专题内容
- `GET /api/content/personal-topic/:id`：获取个人专题内容
- `POST /api/content/comments`：发表评论
- `GET /api/content/comments/:topicId`：获取评论列表

#### 5.1.3 活动接口

- `GET /api/activities`：获取活动列表
- `GET /api/activities/:id`：获取活动详情
- `POST /api/activities`：添加活动
- `PUT /api/activities/:id`：更新活动
- `DELETE /api/activities/:id`：删除活动

#### 5.1.4 知识库接口

- `GET /api/knowledge`：获取资料列表
- `GET /api/knowledge/:id`：获取资料详情
- `POST /api/knowledge`：上传资料
- `PUT /api/knowledge/:id`：更新资料
- `DELETE /api/knowledge/:id`：删除资料
- `GET /api/knowledge/search`：搜索资料

#### 5.1.5 数据查询接口

- `GET /api/data-sources`：获取数据源列表
- `POST /api/data-query`：执行数据查询
- `GET /api/data-query/export`：导出查询结果

#### 5.1.6 AI助手接口

- `GET /api/ai/assistants`：获取AI助手列表
- `GET /api/ai/assistants/:id`：获取AI助手详情
- `POST /api/ai/chat`：发送对话消息
- `GET /api/ai/chat/history`：获取对话历史

#### 5.1.7 系统管理接口

- `GET /api/admin/users`：获取用户列表
- `POST /api/admin/users`：添加用户
- `PUT /api/admin/users/:id`：更新用户
- `DELETE /api/admin/users/:id`：删除用户
- `GET /api/admin/roles`：获取角色列表
- `POST /api/admin/roles`：添加角色
- `PUT /api/admin/roles/:id`：更新角色
- `DELETE /api/admin/roles/:id`：删除角色
- `GET /api/admin/messages`：获取留言列表
- `PUT /api/admin/messages/:id`：审核留言
- `DELETE /api/admin/messages/:id`：删除留言
- `GET /api/admin/ai`：获取AI助手配置列表
- `PUT /api/admin/ai/:id`：更新AI助手配置
- `POST /api/admin/ai`：添加AI助手配置
- `DELETE /api/admin/ai/:id`：删除AI助手配置
- `GET /api/admin/config`：获取系统配置
- `PUT /api/admin/config`：更新系统配置

#### 5.1.8 通知接口

- `GET /api/notifications`：获取通知列表
- `GET /api/notifications/:id`：获取通知详情
- `PUT /api/notifications/:id/read`：标记通知为已读
- `PUT /api/notifications/read-all`：标记所有通知为已读

### 5.2 外部服务接口

系统集成了多个外部服务，主要接口包括：

#### 5.2.1 Dify AI服务接口

- `POST /api/dify/chat`：发送对话消息
- `POST /api/dify/knowledge-upload`：上传知识库文件
- `POST /api/dify/knowledge-analysis`：分析知识库文件

#### 5.2.2 文件存储服务接口

- `POST /api/files/upload`：上传文件
- `GET /api/files/:id`：获取文件
- `DELETE /api/files/:id`：删除文件

#### 5.2.3 邮件服务接口

- `POST /api/email/send`：发送邮件

## 6. 性能与可扩展性

### 6.1 性能优化策略

为满足系统性能需求，采用以下优化策略：

1. **前端优化**：
   - 代码分割，按需加载
   - 静态资源缓存
   - 图片懒加载
   - 减少HTTP请求

2. **后端优化**：
   - 数据库索引优化
   - 查询优化
   - 连接池管理
   - 异步处理

3. **缓存策略**：
   考虑到系统规模（100-200用户，日访问几百次），采用简单高效的缓存策略：
   - **前端缓存**：
     - 浏览器缓存：缓存静态资源（JS、CSS、图片）
     - localStorage：存储用户设置和登录状态
   - **后端缓存**：
     - 内存缓存：缓存频繁访问但很少变化的数据（如系统知识库列表）
     - 按需扩展：随着系统规模增长，可以考虑引入更复杂的缓存机制

### 6.2 可扩展性策略

为支持系统未来扩展，采用以下策略：

1. **模块化设计**：系统按功能模块划分，便于独立扩展
2. **松耦合架构**：组件间通过接口通信，降低耦合度
3. **可配置性**：系统参数可配置，无需修改代码
4. **API版本控制**：支持API版本控制，便于API升级
5. **数据库设计**：合理设计数据库结构，支持数据量增长

## 7. 风险与挑战

### 7.1 技术风险

1. **AI服务依赖风险**：依赖Dify平台提供AI服务，存在服务不稳定或API变更的风险
2. **性能风险**：随着用户和数据量增长，系统性能可能下降
3. **安全风险**：系统可能面临安全威胁，如数据泄露、未授权访问等

### 7.2 应对策略

1. **服务降级**：当外部服务不可用时，提供降级服务
2. **性能监控**：实时监控系统性能，及时发现和解决问题
3. **安全审计**：定期进行安全审计，修复安全漏洞
4. **容错设计**：系统设计考虑容错性，提高系统可靠性

## 8. 结论

和富家族研究平台采用简单高效的三层架构，使用React.js、Node.js和MySQL等技术栈，满足当前用户规模和访问量的需求。系统设计考虑了可扩展性和可维护性，为未来的功能扩展和用户增长预留空间。通过合理的组件划分、接口设计和部署方案，系统能够稳定高效地运行，为用户提供良好的使用体验。