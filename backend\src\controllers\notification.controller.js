const { Notification } = require('../models');

/**
 * 获取通知列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getNotifications = async (req, res) => {
  try {
    const { type, is_read } = req.query;
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;

    // 构建查询条件
    const where = { user_id: req.user.id };
    if (type) where.type = type;
    if (is_read !== undefined) where.is_read = is_read === 'true';

    // 查询通知
    const { count, rows: notifications } = await Notification.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      limit: pageSize,
      offset: (page - 1) * pageSize
    });

    // 获取未读通知数量
    const unreadCount = await Notification.count({
      where: {
        user_id: req.user.id,
        is_read: false
      }
    });

    res.status(200).json({
      success: true,
      data: {
        notifications,
        unreadCount,
        pagination: {
          total: count,
          page,
          pageSize,
          totalPages: Math.ceil(count / pageSize)
        }
      },
      message: '获取通知列表成功'
    });
  } catch (error) {
    console.error('获取通知列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，获取通知列表失败',
      error: error.message
    });
  }
};

/**
 * 标记通知为已读
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.markNotificationAsRead = async (req, res) => {
  try {
    const { id } = req.params;

    // 获取通知
    const notification = await Notification.findByPk(id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      });
    }

    // 检查权限
    if (notification.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足，无法标记他人的通知'
      });
    }

    // 标记为已读
    await notification.update({ is_read: true });

    res.status(200).json({
      success: true,
      data: {
        id: notification.id,
        status: 'read',
        is_read: notification.is_read
      },
      message: '通知已标记为已读'
    });
  } catch (error) {
    console.error('标记通知为已读失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，标记通知为已读失败',
      error: error.message
    });
  }
};

/**
 * 标记所有通知为已读
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.markAllNotificationsAsRead = async (req, res) => {
  try {
    // 标记所有通知为已读
    await Notification.update(
      { is_read: true },
      {
        where: {
          user_id: req.user.id,
          is_read: false
        }
      }
    );

    res.status(200).json({
      success: true,
      message: '所有通知已标记为已读'
    });
  } catch (error) {
    console.error('标记所有通知为已读失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，标记所有通知为已读失败',
      error: error.message
    });
  }
};

/**
 * 删除通知
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteNotification = async (req, res) => {
  try {
    const { id } = req.params;

    // 获取通知
    const notification = await Notification.findByPk(id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      });
    }

    // 检查权限
    if (notification.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足，无法删除他人的通知'
      });
    }

    // 删除通知
    await notification.destroy();

    res.status(200).json({
      success: true,
      message: '通知删除成功'
    });
  } catch (error) {
    console.error('删除通知失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，删除通知失败',
      error: error.message
    });
  }
};

/**
 * 获取通知详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getNotificationById = async (req, res) => {
  try {
    const { id } = req.params;

    // 获取通知
    const notification = await Notification.findByPk(id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      });
    }

    // 检查权限
    if (notification.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足，无法查看他人的通知'
      });
    }

    res.status(200).json({
      success: true,
      data: notification,
      message: '获取通知详情成功'
    });
  } catch (error) {
    console.error('获取通知详情失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，获取通知详情失败',
      error: error.message
    });
  }
};

/**
 * 获取未读通知数量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUnreadNotificationCount = async (req, res) => {
  try {
    // 获取未读通知数量
    const unreadCount = await Notification.count({
      where: {
        user_id: req.user.id,
        is_read: false
      }
    });

    res.status(200).json({
      success: true,
      data: {
        unread_count: unreadCount
      }
    });
  } catch (error) {
    console.error('获取未读通知数量失败:', error);
    res.status(500).json({
      success: false,
      message: '获取未读通知数量失败',
      error: error.message
    });
  }
};

/**
 * 创建通知
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createNotification = async (req, res) => {
  try {
    const { title, content, type, user_id } = req.body;

    // 验证必填字段
    if (!title) {
      return res.status(400).json({
        success: false,
        message: '标题不能为空'
      });
    }

    // 验证权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足，只有管理员可以创建系统通知'
      });
    }

    // 创建通知
    const notification = await Notification.create({
      title,
      content,
      type: type || 'system',
      status: 'unread',
      is_read: false,
      user_id
    });

    res.status(201).json({
      success: true,
      message: '通知创建成功',
      data: notification
    });
  } catch (error) {
    console.error('创建通知失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，创建通知失败',
      error: error.message
    });
  }
};

/**
 * 删除所有通知
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteAllNotifications = async (req, res) => {
  try {
    // 删除所有通知
    await Notification.destroy({
      where: {
        user_id: req.user.id
      }
    });

    res.status(200).json({
      success: true,
      message: '所有通知删除成功'
    });
  } catch (error) {
    console.error('删除所有通知失败:', error);
    res.status(500).json({
      success: false,
      message: '删除所有通知失败',
      error: error.message
    });
  }
};
