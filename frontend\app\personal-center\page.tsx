"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON>Left, User, Settings, Edit2, Save, X, Camera } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { updateUserInfo, changePassword } from "@/services/auth-service"
import apiService from "@/services/api-service"
import uploadService from "@/services/upload-service"
import avatarService from "@/services/avatar-service"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export default function PersonalCenter() {
  const { isLoggedIn, userData: authUserData, updateUserData } = useAuth()
  const router = useRouter()

  const [activeTab, setActiveTab] = useState("profile")
  const [userData, setUserData] = useState({
    username: "",
    email: "",
    phone: "",
    avatar: null,
    joinDate: "",
    lastLogin: "",
    role: "",
    notifications: {
      email: true,
      system: true,
      activity: true
    }
  })
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState(userData)
  const [isAvatarDialogOpen, setIsAvatarDialogOpen] = useState(false)
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false)
  const [isPhoneDialogOpen, setIsPhoneDialogOpen] = useState(false)
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 刷新用户信息
  const refreshUserData = async () => {
    try {
      console.log("开始刷新用户信息");

      // 使用auth-context中的updateUserData函数
      await updateUserData();

      console.log("用户信息刷新成功");

      // 刷新页面
      window.location.reload();
    } catch (error) {
      console.error("刷新用户信息失败:", error);
    }
  };

  // 存储用户头像数据
  const [avatarData, setAvatarData] = useState<string | null>(null);

  // 加载用户头像数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        // 使用/users/me端点获取当前用户信息，包含头像数据
        const response = await apiService.get('/users/me');
        console.log('获取到的用户信息:', response);

        // 打印response的结构，帮助调试
        console.log('response类型:', typeof response);

        // 检查response是否有avatar属性
        if (response) {
          console.log('response的属性:', Object.keys(response));
          console.log('response.avatar是否存在:', 'avatar' in response);
          console.log('response.avatar的值:', response.avatar);
        }

        // 如果用户信息中包含头像数据，直接使用
        if (response && response.avatar) {
          console.log('成功获取头像数据');
          setAvatarData(response.avatar);
        } else {
          console.log('用户信息中没有头像数据');

          // 如果authUserData中有头像数据，使用它
          if (authUserData && authUserData.avatar) {
            console.log('从authUserData中获取头像数据');
            setAvatarData(authUserData.avatar);
          }
        }
      } catch (error) {
        console.error('获取头像数据失败:', error);
      }
    };

    fetchUserData();
  }, []);

  // 如果未登录，重定向到首页
  useEffect(() => {
    if (!isLoggedIn) {
      router.push("/")
      toast({
        title: "需要登录",
        description: "请先登录后再访问个人中心",
        variant: "destructive"
      })
    } else {
      // 已登录，确保页面显示正确
      console.log("用户已登录，显示个人中心页面");
    }
  }, [isLoggedIn, router])

  // 初始化用户数据
  useEffect(() => {
    if (authUserData) {
      console.log("初始化用户数据:", authUserData);

      // 检查角色信息
      let roleDisplay = "";

      // 打印用户数据，帮助调试
      console.log("用户数据:", authUserData);

      // 根据系统设计文档和记忆，用户角色有两种存储方式：
      // 1. role字段：存储角色代码，如'basic_user'
      // 2. userRole关联：存储角色对象，包含name属性

      if (typeof authUserData.role === 'object' && authUserData.role !== null) {
        // 如果role是对象，直接使用其name属性
        console.log("角色是对象:", authUserData.role);
        roleDisplay = authUserData.role.name || "";
      } else if (typeof authUserData.role === 'string') {
        // 如果role是字符串，使用映射表转换为中文名称
        console.log("角色是字符串:", authUserData.role);

        // 角色映射表（根据系统设计文档和代码）
        // 系统说明文档明确指出：basic_user对应访问者
        const roleMap: {[key: string]: string} = {
          'admin': '管理员',
          'basic_user': '访问者'
        };

        // 使用映射表，如果没有对应的映射则使用原始值
        roleDisplay = roleMap[authUserData.role] || authUserData.role;

        // 特殊处理basic_user角色
        if (authUserData.role === 'basic_user' && (!roleDisplay || roleDisplay === 'basic_user')) {
          roleDisplay = '访问者';
        }
      } else {
        // 如果role格式未知，使用默认角色
        console.log("角色格式未知，使用默认角色");
        roleDisplay = "访问者";
      }

      const formattedUserData = {
        username: authUserData.username || "",
        email: authUserData.email || "",
        phone: authUserData.phone || "",
        avatar: authUserData.avatar || null,
        joinDate: authUserData.created_at ? new Date(authUserData.created_at).toLocaleDateString() : "",
        lastLogin: authUserData.last_login ? new Date(authUserData.last_login).toLocaleString() : "",
        role: roleDisplay,
        notifications: {
          email: true,
          system: true,
          activity: true
        }
      }

      console.log("格式化后的用户数据:", formattedUserData);
      setUserData(formattedUserData)
      setFormData(formattedUserData)
    } else {
      console.warn("没有用户数据可用");
    }
  }, [authUserData])

  // 密码修改表单状态
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")

  // 手机修改表单状态
  const [newPhone, setNewPhone] = useState("")
  const [phoneVerifyCode, setPhoneVerifyCode] = useState("")

  // 邮箱修改表单状态
  const [newEmail, setNewEmail] = useState("")
  const [emailVerifyCode, setEmailVerifyCode] = useState("")

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  // 处理编辑模式切换
  const handleEditToggle = () => {
    if (isEditing) {
      // 取消编辑，恢复原始数据
      setFormData(userData)
    }
    setIsEditing(!isEditing)
  }

  // 处理保存资料
  const handleSaveProfile = async () => {
    try {
      setIsLoading(true)

      // 调用API更新用户信息
      await updateUserInfo({
        email: formData.email,
        phone: formData.phone
      })

      // 更新本地状态
      setUserData(formData)
      setIsEditing(false)

      toast({
        title: "更新成功",
        description: "个人资料已更新！",
      })
    } catch (error: any) {
      console.error('更新个人资料失败:', error)
      toast({
        title: "更新失败",
        description: error.response?.data?.message || "无法更新个人资料，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理通知设置切换
  const handleNotificationToggle = async (type: 'email' | 'system' | 'activity') => {
    try {
      setIsLoading(true)

      // 获取当前状态的反向值
      const newValue = !userData.notifications[type]

      // 调用API更新通知设置
      await updateUserInfo({
        notifications: {
          ...userData.notifications,
          [type]: newValue
        }
      })

      // 更新本地状态
      setUserData(prev => ({
        ...prev,
        notifications: {
          ...prev.notifications,
          [type]: newValue
        }
      }))

      toast({
        title: "设置已更新",
        description: `${newValue ? '已开启' : '已关闭'}${type === 'email' ? '电子邮件' : type === 'system' ? '系统消息' : '活动提醒'}通知`,
      })
    } catch (error: any) {
      console.error('更新通知设置失败:', error)
      toast({
        title: "更新失败",
        description: error.response?.data?.message || "无法更新通知设置，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理头像上传
  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      try {
        setIsLoading(true)

        // 读取文件为DataURL
        const reader = new FileReader()
        reader.onload = async (event) => {
          if (event.target?.result) {
            let avatarDataUrl = event.target.result as string

            try {
              console.log('头像读取成功，准备更新用户信息')

              console.log('准备发送头像数据到后端，数据长度:', avatarDataUrl.length);

              // 无论图片大小如何，都进行压缩
              console.log('开始压缩头像数据...');
              // 创建一个HTMLImageElement对象来压缩图片
              const img = document.createElement('img');
              img.src = avatarDataUrl;
              await new Promise(resolve => { img.onload = resolve; });

              // 创建canvas来压缩图片
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');

              // 计算新的尺寸，保持宽高比
              let width = img.width;
              let height = img.height;
              const maxSize = 64; // 最大尺寸，减小到64px

              if (width > height && width > maxSize) {
                height = Math.round((height * maxSize) / width);
                width = maxSize;
              } else if (height > maxSize) {
                width = Math.round((width * maxSize) / height);
                height = maxSize;
              }

              // 设置canvas尺寸
              canvas.width = width;
              canvas.height = height;

              // 绘制图片
              ctx.drawImage(img, 0, 0, width, height);

              // 获取压缩后的DataURL，使用更低的质量
              avatarDataUrl = canvas.toDataURL('image/jpeg', 0.3); // 使用JPEG格式，质量0.3
              console.log('压缩后的头像数据长度:', avatarDataUrl.length);

              // 使用专门的头像上传API
              const response = await avatarService.uploadAvatar(avatarDataUrl);
              console.log('头像上传响应:', response);

              // 更新用户信息
              const updatedUser = await updateUserInfo({});
              console.log('更新用户信息响应:', updatedUser);

              // 重新获取用户信息，包含头像数据
              const userResponse = await apiService.get('/users/me');
              if (userResponse && userResponse.avatar) {
                console.log('成功获取更新后的头像数据');
                setAvatarData(userResponse.avatar);
              }

              console.log('用户信息更新成功:', updatedUser)

              // 头像已成功上传到后端
              console.log('头像已成功上传到后端');

              // 更新头像数据状态
              setAvatarData(avatarDataUrl);

              // 更新用户数据状态
              setUserData((prev) => ({ ...prev, avatar: avatarDataUrl }));
              setFormData((prev) => ({ ...prev, avatar: avatarDataUrl }));

              toast({
                title: "更新成功",
                description: "头像已成功更新！",
              })

              // 延迟1秒后刷新页面，确保显示最新头像
              setTimeout(() => {
                console.log('刷新页面以显示最新头像');
                window.location.reload();
              }, 1000);
            } catch (error: any) {
              console.error('更新头像失败:', error)
              toast({
                title: "更新失败",
                description: error.response?.data?.message || "无法更新头像，请稍后再试",
                variant: "destructive"
              })
            } finally {
              setIsLoading(false)
              setIsAvatarDialogOpen(false)
            }
          }
        }
        reader.readAsDataURL(file)
      } catch (error) {
        setIsLoading(false)
        console.error('读取文件失败:', error)
        toast({
          title: "文件读取失败",
          description: "无法读取所选文件，请重试",
          variant: "destructive"
        })
      }
    }
  }

  // 处理密码修改
  const handlePasswordSubmit = async () => {
    if (newPassword !== confirmPassword) {
      toast({
        title: "密码不匹配",
        description: "两次输入的新密码不一致！",
        variant: "destructive"
      })
      return
    }

    try {
      setIsLoading(true)
      console.log("开始提交密码修改请求");

      // 调用API修改密码
      const passwordData = {
        old_password: currentPassword,
        new_password: newPassword,
        confirm_password: confirmPassword
      };

      console.log("密码修改参数:", passwordData);

      // 调用修改密码API
      await changePassword(passwordData);

      toast({
        title: "修改成功",
        description: "密码已成功修改！",
      })

      // 关闭对话框并清空表单
      setIsPasswordDialogOpen(false)
      setCurrentPassword("")
      setNewPassword("")
      setConfirmPassword("")
    } catch (error: any) {
      console.error('修改密码失败:', error);

      let errorMessage = "密码修改失败，请检查当前密码是否正确";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "修改失败",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理手机绑定修改
  const handlePhoneSubmit = async () => {
    try {
      setIsLoading(true)

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(newPhone)) {
        toast({
          title: "格式错误",
          description: "请输入正确的手机号码格式",
          variant: "destructive"
        })
        return
      }

      // 调用API更新手机号
      await updateUserInfo({
        phone: newPhone
      })

      // 更新本地状态
      setUserData({ ...userData, phone: newPhone })
      setFormData({ ...formData, phone: newPhone })

      // 保存到localStorage，确保刷新页面后仍然显示
      const userInfo = localStorage.getItem('hefamily_user_info')
      if (userInfo) {
        const user = JSON.parse(userInfo)
        localStorage.setItem('hefamily_user_info', JSON.stringify({
          ...user,
          phone: newPhone
        }))
      }

      toast({
        title: "更新成功",
        description: "手机号绑定成功！",
      })

      // 关闭对话框并清空表单
      setIsPhoneDialogOpen(false)
      setNewPhone("")
      setPhoneVerifyCode("")
    } catch (error: any) {
      console.error('更新手机号失败:', error)
      toast({
        title: "更新失败",
        description: error.response?.data?.message || "手机号绑定失败，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理邮箱绑定修改
  const handleEmailSubmit = async () => {
    try {
      setIsLoading(true)

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(newEmail)) {
        toast({
          title: "格式错误",
          description: "请输入正确的邮箱地址格式",
          variant: "destructive"
        })
        return
      }

      // 调用API更新邮箱
      await updateUserInfo({
        email: newEmail
      })

      // 更新本地状态
      setUserData({ ...userData, email: newEmail })
      setFormData({ ...formData, email: newEmail })

      toast({
        title: "更新成功",
        description: "邮箱绑定成功！",
      })

      // 关闭对话框并清空表单
      setIsEmailDialogOpen(false)
      setNewEmail("")
      setEmailVerifyCode("")
    } catch (error: any) {
      console.error('更新邮箱失败:', error)
      toast({
        title: "更新失败",
        description: error.response?.data?.message || "邮箱绑定失败，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 发送验证码
  const sendVerifyCode = (type: string) => {
    toast({
      title: "验证码已发送",
      description: `验证码已发送到您${type === "phone" ? "的手机" : "的邮箱"}！`,
    })
  }

  return (
    <div className="min-h-screen flex flex-col bg-[#fdf9f1]">
      <Navbar />

      <main className="flex-grow pt-16">
        {/* 返回按钮 */}
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="inline-flex items-center text-[#111827] hover:text-[#1e7a43] transition-colors">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回首页
          </Link>
        </div>

        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* 侧边栏 */}
            <div className="w-full lg:w-1/4">
              <Card className="border-[#f5a623]/20">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center">
                    <div
                      className="relative w-24 h-24 rounded-full overflow-hidden bg-[#f5a623] flex items-center justify-center text-white text-4xl mb-2 group cursor-pointer"
                      onClick={() => setIsAvatarDialogOpen(true)}
                    >
                      {/* 使用背景图片显示头像，避免img标签的问题 */}
                      {avatarData ? (
                        <div
                          className="w-full h-full bg-center bg-cover"
                          style={{ backgroundImage: `url(${avatarData})` }}
                          onError={(e) => {
                            console.error('背景图片加载失败');
                            // 移除背景图片
                            (e.target as HTMLElement).style.backgroundImage = 'none';
                          }}
                        />
                      ) : (
                        userData.username.charAt(0)
                      )}
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <Camera className="h-8 w-8 text-white" />
                      </div>
                    </div>
                    <button
                      className="text-[#f5a623] hover:text-[#f5a623]/80 text-sm font-medium flex items-center mb-3"
                      onClick={() => setIsAvatarDialogOpen(true)}
                    >
                      <Camera className="h-3.5 w-3.5 mr-1" />
                      更换头像
                    </button>
                    <h2 className="text-xl font-bold text-[#111827]">{userData.username}</h2>
                    <p className="text-gray-500 text-sm">{userData.role}</p>
                    <p className="text-gray-500 text-sm mt-1">加入时间: {userData.joinDate}</p>
                  </div>

                  <div className="mt-6 space-y-1">
                    <button
                      className={`w-full flex items-center px-4 py-2 rounded-md ${
                        activeTab === "profile" ? "bg-[#f5a623] text-white" : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={() => setActiveTab("profile")}
                    >
                      <User className="mr-2 h-5 w-5" />
                      个人资料
                    </button>
                    <button
                      className={`w-full flex items-center px-4 py-2 rounded-md ${
                        activeTab === "settings" ? "bg-[#f5a623] text-white" : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={() => setActiveTab("settings")}
                    >
                      <Settings className="mr-2 h-5 w-5" />
                      账号设置
                    </button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 主内容区 */}
            <div className="w-full lg:w-3/4">
              <Card className="border-[#f5a623]/20">
                <CardContent className="p-6">
                  {activeTab === "profile" && (
                    <div>
                      <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-bold text-[#111827]">个人资料</h2>
                      </div>

                      <div className="space-y-6">
                        {/* 查看模式 */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                            <div className="p-2 bg-gray-50 rounded-md border border-gray-200">
                              {userData.username}
                            </div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">角色</label>
                            <div className="p-2 bg-gray-50 rounded-md border border-gray-200">{userData.role}</div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">电子邮箱</label>
                            <div className="p-2 bg-gray-50 rounded-md border border-gray-200">{userData.email}</div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">手机号码</label>
                            <div className="p-2 bg-gray-50 rounded-md border border-gray-200">{userData.phone}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === "settings" && (
                    <div>
                      <h2 className="text-2xl font-bold mb-6 text-[#111827]">账号设置</h2>
                      <div className="space-y-6">
                        <div className="border-b border-gray-200 pb-4">
                          <h3 className="text-lg font-medium mb-4 text-[#111827]">安全设置</h3>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-[#111827]">登录密码</h4>
                                <p className="text-sm text-gray-500">定期修改密码可以保护账号安全</p>
                              </div>
                              <Button
                                variant="outline"
                                className="border-[#f5a623] text-[#f5a623]"
                                onClick={() => setIsPasswordDialogOpen(true)}
                              >
                                修改
                              </Button>
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-[#111827]">手机绑定</h4>
                                <p className="text-sm text-gray-500">已绑定：{userData.phone}</p>
                              </div>
                              <Button
                                variant="outline"
                                className="border-[#f5a623] text-[#f5a623]"
                                onClick={() => setIsPhoneDialogOpen(true)}
                              >
                                更换
                              </Button>
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-[#111827]">邮箱绑定</h4>
                                <p className="text-sm text-gray-500">已绑定：{userData.email}</p>
                              </div>
                              <Button
                                variant="outline"
                                className="border-[#f5a623] text-[#f5a623]"
                                onClick={() => setIsEmailDialogOpen(true)}
                              >
                                更换
                              </Button>
                            </div>
                          </div>
                        </div>


                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>

      <Footer />

      {/* 隐藏的文件输入 */}
      <input type="file" ref={fileInputRef} className="hidden" accept="image/*" onChange={handleAvatarUpload} />

      {/* 头像上传对话框 */}
      <Dialog open={isAvatarDialogOpen} onOpenChange={setIsAvatarDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>更换头像</DialogTitle>
            <DialogDescription>选择一张图片作为您的个人头像。支持JPG、PNG格式。</DialogDescription>
          </DialogHeader>
          <div className="flex flex-col items-center justify-center py-6">
            <div className="w-32 h-32 rounded-full overflow-hidden bg-[#f5a623] flex items-center justify-center text-white text-5xl mb-4">
              {/* 使用背景图片显示头像，避免img标签的问题 */}
              {avatarData ? (
                <div
                  className="w-full h-full bg-center bg-cover"
                  style={{ backgroundImage: `url(${avatarData})` }}
                  onError={(e) => {
                    console.error('对话框中背景图片加载失败');
                    // 移除背景图片
                    (e.target as HTMLElement).style.backgroundImage = 'none';
                  }}
                />
              ) : (
                userData.username.charAt(0)
              )}
            </div>
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading}
            >
              {isLoading ? "上传中..." : "选择图片"}
            </Button>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAvatarDialogOpen(false)}>
              取消
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 修改密码对话框 */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>修改登录密码</DialogTitle>
            <DialogDescription>
              请输入您的当前密码和新密码，新密码需包含字母、数字和特殊字符。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="current-password" className="text-sm font-medium">
                当前密码
              </label>
              <Input
                id="current-password"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <label htmlFor="new-password" className="text-sm font-medium">
                新密码
              </label>
              <Input
                id="new-password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <label htmlFor="confirm-password" className="text-sm font-medium">
                确认新密码
              </label>
              <Input
                id="confirm-password"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsPasswordDialogOpen(false)}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button
              className="bg-[#f5a623] hover:bg-[#f5a623]/90 text-[#111827]"
              onClick={handlePasswordSubmit}
              disabled={isLoading}
            >
              {isLoading ? "处理中..." : "确认修改"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 更换手机绑定对话框 */}
      <Dialog open={isPhoneDialogOpen} onOpenChange={setIsPhoneDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>更换手机绑定</DialogTitle>
            <DialogDescription>请输入新的手机号码。</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="new-phone" className="text-sm font-medium">
                新手机号码
              </label>
              <Input id="new-phone" type="tel" value={newPhone} onChange={(e) => setNewPhone(e.target.value)} />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsPhoneDialogOpen(false)}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button
              className="bg-[#f5a623] hover:bg-[#f5a623]/90 text-[#111827]"
              onClick={handlePhoneSubmit}
              disabled={isLoading}
            >
              {isLoading ? "处理中..." : "确认更换"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 更换邮箱绑定对话框 */}
      <Dialog open={isEmailDialogOpen} onOpenChange={setIsEmailDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>更换邮箱绑定</DialogTitle>
            <DialogDescription>请输入新的邮箱地址。</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="new-email" className="text-sm font-medium">
                新邮箱地址
              </label>
              <Input id="new-email" type="email" value={newEmail} onChange={(e) => setNewEmail(e.target.value)} />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEmailDialogOpen(false)}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button
              className="bg-[#f5a623] hover:bg-[#f5a623]/90 text-[#111827]"
              onClick={handleEmailSubmit}
              disabled={isLoading}
            >
              {isLoading ? "处理中..." : "确认更换"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
