"use client"

import { useState } from "react"
import { Check, Database, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

// 知识库类型
interface KnowledgeBase {
  id: string
  name: string
  type: "系统" | "用户"
}

// 组件属性
interface KnowledgeBaseModalProps {
  isOpen: boolean
  onClose: () => void
  knowledgeBases: KnowledgeBase[]
  selectedIds: string[]
  onChange: (selectedIds: string[]) => void
}

/**
 * 知识库选择模态框组件
 *
 * 用于选择要查询的知识库
 */
export function KnowledgeBaseModal({
  isOpen,
  onClose,
  knowledgeBases,
  selectedIds,
  onChange
}: KnowledgeBaseModalProps) {
  const [localSelectedIds, setLocalSelectedIds] = useState<string[]>(selectedIds)

  // 处理确认
  const handleConfirm = () => {
    onChange(localSelectedIds)
    onClose()
  }

  // 处理取消
  const handleCancel = () => {
    setLocalSelectedIds(selectedIds)
    onClose()
  }

  // 处理全选系统知识库
  const handleSelectAllSystem = () => {
    const systemIds = knowledgeBases.filter((kb) => kb.type === "系统").map((kb) => kb.id)
    const allSelected = systemIds.every((id) => localSelectedIds.includes(id))

    if (allSelected) {
      // 如果全部已选，则取消全选
      setLocalSelectedIds((prev) => prev.filter((id) => !systemIds.includes(id)))
    } else {
      // 如果未全选，则全选
      const newSelectedIds = [...localSelectedIds]
      systemIds.forEach((id) => {
        if (!newSelectedIds.includes(id)) {
          newSelectedIds.push(id)
        }
      })
      setLocalSelectedIds(newSelectedIds)
    }
  }

  // 处理全选用户知识库
  const handleSelectAllUser = () => {
    const userIds = knowledgeBases.filter((kb) => kb.type === "用户").map((kb) => kb.id)
    const allSelected = userIds.every((id) => localSelectedIds.includes(id))

    if (allSelected) {
      // 如果全部已选，则取消全选
      setLocalSelectedIds((prev) => prev.filter((id) => !userIds.includes(id)))
    } else {
      // 如果未全选，则全选
      const newSelectedIds = [...localSelectedIds]
      userIds.forEach((id) => {
        if (!newSelectedIds.includes(id)) {
          newSelectedIds.push(id)
        }
      })
      setLocalSelectedIds(newSelectedIds)
    }
  }

  // 处理选择知识库
  const handleSelectKnowledgeBase = (id: string) => {
    if (localSelectedIds.includes(id)) {
      setLocalSelectedIds(localSelectedIds.filter((selectedId) => selectedId !== id))
    } else {
      setLocalSelectedIds([...localSelectedIds, id])
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* 遮罩层 */}
      <div className="fixed inset-0 bg-black/50" onClick={handleCancel}></div>

      {/* 模态框内容 */}
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md max-h-[80vh] flex flex-col relative z-[10000]">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-semibold">选择知识库</h2>
          <button onClick={handleCancel} className="text-gray-500 hover:text-gray-700">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-4 flex-1 overflow-y-auto">
          <div className="mb-4">
            <div className="text-sm text-gray-500 mb-2">已选择 {localSelectedIds.length} 个知识库</div>
          </div>

          <div className="space-y-4">
            {/* 系统知识库 */}
            {knowledgeBases.filter((kb) => kb.type === "系统").length > 0 && (
              <div>
                <div className="flex justify-between items-center px-2 py-1 bg-gray-50 rounded-sm mb-2">
                  <div className="text-xs font-semibold text-gray-500">系统知识库</div>
                  <button
                    onClick={handleSelectAllSystem}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    {knowledgeBases.filter((kb) => kb.type === "系统").every((kb) => localSelectedIds.includes(kb.id))
                      ? "取消全选"
                      : "全选"}
                  </button>
                </div>
                <div className="space-y-1">
                  {knowledgeBases
                    .filter((kb) => kb.type === "系统")
                    .map((kb) => (
                      <div
                        key={kb.id}
                        className={cn(
                          "flex items-center px-2 py-2 text-sm rounded-sm cursor-pointer",
                          localSelectedIds.includes(kb.id) ? "bg-emerald-50" : "hover:bg-gray-50",
                        )}
                        onClick={() => handleSelectKnowledgeBase(kb.id)}
                      >
                        <div
                          className={cn(
                            "mr-2 h-4 w-4 rounded-sm border flex items-center justify-center",
                            localSelectedIds.includes(kb.id)
                              ? "border-emerald-500 bg-emerald-500 text-white"
                              : "border-gray-300",
                          )}
                        >
                          {localSelectedIds.includes(kb.id) && <Check className="h-3 w-3" />}
                        </div>
                        <div className="flex items-center">
                          <Database className="h-3 w-3 mr-2 text-blue-500" />
                          <span className="text-sm">{kb.name}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* 用户知识库 */}
            {knowledgeBases.filter((kb) => kb.type === "用户").length > 0 && (
              <div>
                <div className="flex justify-between items-center px-2 py-1 bg-gray-50 rounded-sm mb-2">
                  <div className="text-xs font-semibold text-gray-500">用户知识库</div>
                  <button
                    onClick={handleSelectAllUser}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    {knowledgeBases.filter((kb) => kb.type === "用户").every((kb) => localSelectedIds.includes(kb.id))
                      ? "取消全选"
                      : "全选"}
                  </button>
                </div>
                <div className="space-y-1">
                  {knowledgeBases
                    .filter((kb) => kb.type === "用户")
                    .map((kb) => (
                      <div
                        key={kb.id}
                        className={cn(
                          "flex items-center px-2 py-2 text-sm rounded-sm cursor-pointer",
                          localSelectedIds.includes(kb.id) ? "bg-emerald-50" : "hover:bg-gray-50",
                        )}
                        onClick={() => handleSelectKnowledgeBase(kb.id)}
                      >
                        <div
                          className={cn(
                            "mr-2 h-4 w-4 rounded-sm border flex items-center justify-center",
                            localSelectedIds.includes(kb.id)
                              ? "border-emerald-500 bg-emerald-500 text-white"
                              : "border-gray-300",
                          )}
                        >
                          {localSelectedIds.includes(kb.id) && <Check className="h-3 w-3" />}
                        </div>
                        <div className="flex items-center">
                          <Database className="h-3 w-3 mr-2 text-green-500" />
                          <span className="text-sm">{kb.name}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="p-4 border-t flex justify-end space-x-2">
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button className="bg-[#1e7a43] hover:bg-[#1e7a43]/90" onClick={handleConfirm}>
            确定
          </Button>
        </div>
      </div>
    </div>
  )
}
