/**
 * 系统管理页面组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { SystemManagement } from '@/components/system/SystemManagement'
import { mockFetch, resetMockFetch } from '../test-utils'

// 模拟系统配置数据
const mockSystemConfig = {
  id: 1,
  system_name: '和富家族研究平台',
  system_logo: '/logo.png',
  system_description: '和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。',
  password_min_length: 8,
  password_require_uppercase: true,
  password_require_lowercase: true,
  password_require_number: true,
  password_require_special: true,
  max_login_attempts: 5,
  lockout_duration: 30,
  session_timeout: 60,
  file_upload_max_size: 10,
  allowed_file_types: 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif'
}

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    phone: '13900000001',
    role: 'admin',
    is_active: true,
    created_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    phone: '13800000001',
    role: 'basic_user',
    is_active: true,
    created_at: '2023-01-02T00:00:00Z'
  },
  {
    id: 3,
    username: 'user2',
    email: '<EMAIL>',
    phone: '13800000002',
    role: 'basic_user',
    is_active: false,
    created_at: '2023-01-03T00:00:00Z'
  }
]

// 模拟角色数据
const mockRoles = [
  {
    id: 1,
    name: 'admin',
    description: '管理员',
    is_system: true,
    permissions: [
      { id: 1, name: 'system:manage', description: '系统管理权限' },
      { id: 2, name: 'user:manage', description: '用户管理权限' }
    ]
  },
  {
    id: 2,
    name: 'basic_user',
    description: '基础用户',
    is_system: true,
    permissions: [
      { id: 3, name: 'content:view', description: '内容查看权限' }
    ]
  },
  {
    id: 3,
    name: 'custom_role',
    description: '自定义角色',
    is_system: false,
    permissions: [
      { id: 3, name: 'content:view', description: '内容查看权限' },
      { id: 4, name: 'content:create', description: '内容创建权限' }
    ]
  }
]

// 模拟AI助手数据
const mockAIAssistants = [
  {
    id: 1,
    name: '个人专题助手',
    type: 'personal',
    description: '用于个人专题页面的AI助手',
    api_endpoint: 'https://api.dify.ai/v1',
    api_key: '********',
    status: 'active',
    created_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: '数据查询助手',
    type: 'data',
    description: '用于数据查询页面的AI助手',
    api_endpoint: 'https://api.dify.ai/v1',
    api_key: '********',
    status: 'active',
    created_at: '2023-01-02T00:00:00Z'
  }
]

// 模拟评论数据
const mockComments = [
  {
    id: 1,
    content: '这是一条待审核的评论',
    topic_type: 'personal',
    topic_id: 1,
    creator: {
      id: 2,
      username: 'user1'
    },
    status: 'pending',
    created_at: '2023-01-04T00:00:00Z'
  },
  {
    id: 2,
    content: '这是一条已审核的评论',
    topic_type: 'personal',
    topic_id: 1,
    creator: {
      id: 3,
      username: 'user2'
    },
    status: 'approved',
    created_at: '2023-01-05T00:00:00Z'
  }
]

describe('SystemManagement组件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch()
    jest.clearAllMocks()
  })

  // 测试基本渲染
  test('应该正确渲染系统管理组件', () => {
    render(
      <SystemManagement
        systemConfig={mockSystemConfig}
        users={mockUsers}
        roles={mockRoles}
        aiAssistants={mockAIAssistants}
        comments={mockComments}
        isLoading={false}
        onSystemConfigUpdate={jest.fn()}
        onUserUpdate={jest.fn()}
        onUserDelete={jest.fn()}
        onRoleUpdate={jest.fn()}
        onRoleDelete={jest.fn()}
        onAIAssistantUpdate={jest.fn()}
        onCommentReview={jest.fn()}
      />
    )
    
    // 验证导航菜单
    expect(screen.getByText('系统设置')).toBeInTheDocument()
    expect(screen.getByText('用户管理')).toBeInTheDocument()
    expect(screen.getByText('角色管理')).toBeInTheDocument()
    expect(screen.getByText('AI助手管理')).toBeInTheDocument()
    expect(screen.getByText('评论管理')).toBeInTheDocument()
    
    // 默认应该显示系统设置
    expect(screen.getByText('系统名称')).toBeInTheDocument()
    expect(screen.getByDisplayValue('和富家族研究平台')).toBeInTheDocument()
  })

  // 测试加载状态
  test('应该显示加载状态', () => {
    render(
      <SystemManagement
        systemConfig={null}
        users={[]}
        roles={[]}
        aiAssistants={[]}
        comments={[]}
        isLoading={true}
        onSystemConfigUpdate={jest.fn()}
        onUserUpdate={jest.fn()}
        onUserDelete={jest.fn()}
        onRoleUpdate={jest.fn()}
        onRoleDelete={jest.fn()}
        onAIAssistantUpdate={jest.fn()}
        onCommentReview={jest.fn()}
      />
    )
    
    expect(screen.getByText('加载中...')).toBeInTheDocument()
  })

  // 测试系统设置更新
  test('应该调用系统设置更新函数', async () => {
    const handleSystemConfigUpdate = jest.fn().mockResolvedValue({
      ...mockSystemConfig,
      system_name: '更新后的系统名称'
    })
    
    render(
      <SystemManagement
        systemConfig={mockSystemConfig}
        users={mockUsers}
        roles={mockRoles}
        aiAssistants={mockAIAssistants}
        comments={mockComments}
        isLoading={false}
        onSystemConfigUpdate={handleSystemConfigUpdate}
        onUserUpdate={jest.fn()}
        onUserDelete={jest.fn()}
        onRoleUpdate={jest.fn()}
        onRoleDelete={jest.fn()}
        onAIAssistantUpdate={jest.fn()}
        onCommentReview={jest.fn()}
      />
    )
    
    // 修改系统名称
    const systemNameInput = screen.getByLabelText('系统名称')
    fireEvent.change(systemNameInput, { target: { value: '更新后的系统名称' } })
    
    // 点击保存按钮
    const saveButton = screen.getByRole('button', { name: '保存设置' })
    fireEvent.click(saveButton)
    
    // 验证更新函数被调用
    expect(handleSystemConfigUpdate).toHaveBeenCalledWith({
      ...mockSystemConfig,
      system_name: '更新后的系统名称'
    })
    
    // 等待更新完成
    await waitFor(() => {
      expect(screen.getByText('设置已保存')).toBeInTheDocument()
    })
  })

  // 测试用户管理
  test('应该显示用户列表并支持用户管理', async () => {
    const handleUserUpdate = jest.fn().mockResolvedValue({
      ...mockUsers[1],
      is_active: false
    })
    
    render(
      <SystemManagement
        systemConfig={mockSystemConfig}
        users={mockUsers}
        roles={mockRoles}
        aiAssistants={mockAIAssistants}
        comments={mockComments}
        isLoading={false}
        onSystemConfigUpdate={jest.fn()}
        onUserUpdate={handleUserUpdate}
        onUserDelete={jest.fn()}
        onRoleUpdate={jest.fn()}
        onRoleDelete={jest.fn()}
        onAIAssistantUpdate={jest.fn()}
        onCommentReview={jest.fn()}
      />
    )
    
    // 切换到用户管理
    const userManagementTab = screen.getByText('用户管理')
    fireEvent.click(userManagementTab)
    
    // 验证用户列表
    expect(screen.getByText('admin')).toBeInTheDocument()
    expect(screen.getByText('user1')).toBeInTheDocument()
    expect(screen.getByText('user2')).toBeInTheDocument()
    
    // 禁用用户
    const statusToggle = screen.getAllByRole('checkbox')[1] // user1的状态开关
    fireEvent.click(statusToggle)
    
    // 验证更新函数被调用
    expect(handleUserUpdate).toHaveBeenCalledWith({
      ...mockUsers[1],
      is_active: false
    })
    
    // 等待更新完成
    await waitFor(() => {
      expect(screen.getByText('用户状态已更新')).toBeInTheDocument()
    })
  })

  // 测试角色管理
  test('应该显示角色列表并支持角色管理', async () => {
    const handleRoleUpdate = jest.fn().mockResolvedValue({
      ...mockRoles[2],
      description: '更新后的角色描述'
    })
    
    render(
      <SystemManagement
        systemConfig={mockSystemConfig}
        users={mockUsers}
        roles={mockRoles}
        aiAssistants={mockAIAssistants}
        comments={mockComments}
        isLoading={false}
        onSystemConfigUpdate={jest.fn()}
        onUserUpdate={jest.fn()}
        onUserDelete={jest.fn()}
        onRoleUpdate={handleRoleUpdate}
        onRoleDelete={jest.fn()}
        onAIAssistantUpdate={jest.fn()}
        onCommentReview={jest.fn()}
      />
    )
    
    // 切换到角色管理
    const roleManagementTab = screen.getByText('角色管理')
    fireEvent.click(roleManagementTab)
    
    // 验证角色列表
    expect(screen.getByText('admin')).toBeInTheDocument()
    expect(screen.getByText('basic_user')).toBeInTheDocument()
    expect(screen.getByText('custom_role')).toBeInTheDocument()
    
    // 编辑自定义角色
    const editButtons = screen.getAllByText('编辑')
    fireEvent.click(editButtons[2]) // custom_role的编辑按钮
    
    // 修改角色描述
    const descriptionInput = screen.getByLabelText('角色描述')
    fireEvent.change(descriptionInput, { target: { value: '更新后的角色描述' } })
    
    // 保存角色
    const saveButton = screen.getByRole('button', { name: '保存' })
    fireEvent.click(saveButton)
    
    // 验证更新函数被调用
    expect(handleRoleUpdate).toHaveBeenCalledWith({
      ...mockRoles[2],
      description: '更新后的角色描述'
    })
    
    // 等待更新完成
    await waitFor(() => {
      expect(screen.getByText('角色已更新')).toBeInTheDocument()
    })
  })

  // 测试AI助手管理
  test('应该显示AI助手列表并支持AI助手管理', async () => {
    const handleAIAssistantUpdate = jest.fn().mockResolvedValue({
      ...mockAIAssistants[0],
      api_key: 'new_api_key'
    })
    
    render(
      <SystemManagement
        systemConfig={mockSystemConfig}
        users={mockUsers}
        roles={mockRoles}
        aiAssistants={mockAIAssistants}
        comments={mockComments}
        isLoading={false}
        onSystemConfigUpdate={jest.fn()}
        onUserUpdate={jest.fn()}
        onUserDelete={jest.fn()}
        onRoleUpdate={jest.fn()}
        onRoleDelete={jest.fn()}
        onAIAssistantUpdate={handleAIAssistantUpdate}
        onCommentReview={jest.fn()}
      />
    )
    
    // 切换到AI助手管理
    const aiManagementTab = screen.getByText('AI助手管理')
    fireEvent.click(aiManagementTab)
    
    // 验证AI助手列表
    expect(screen.getByText('个人专题助手')).toBeInTheDocument()
    expect(screen.getByText('数据查询助手')).toBeInTheDocument()
    
    // 编辑AI助手
    const editButtons = screen.getAllByText('编辑')
    fireEvent.click(editButtons[0]) // 个人专题助手的编辑按钮
    
    // 修改API密钥
    const apiKeyInput = screen.getByLabelText('API密钥')
    fireEvent.change(apiKeyInput, { target: { value: 'new_api_key' } })
    
    // 保存AI助手
    const saveButton = screen.getByRole('button', { name: '保存' })
    fireEvent.click(saveButton)
    
    // 验证更新函数被调用
    expect(handleAIAssistantUpdate).toHaveBeenCalledWith({
      ...mockAIAssistants[0],
      api_key: 'new_api_key'
    })
    
    // 等待更新完成
    await waitFor(() => {
      expect(screen.getByText('AI助手已更新')).toBeInTheDocument()
    })
  })

  // 测试评论管理
  test('应该显示评论列表并支持评论审核', async () => {
    const handleCommentReview = jest.fn().mockResolvedValue({
      ...mockComments[0],
      status: 'approved'
    })
    
    render(
      <SystemManagement
        systemConfig={mockSystemConfig}
        users={mockUsers}
        roles={mockRoles}
        aiAssistants={mockAIAssistants}
        comments={mockComments}
        isLoading={false}
        onSystemConfigUpdate={jest.fn()}
        onUserUpdate={jest.fn()}
        onUserDelete={jest.fn()}
        onRoleUpdate={jest.fn()}
        onRoleDelete={jest.fn()}
        onAIAssistantUpdate={jest.fn()}
        onCommentReview={handleCommentReview}
      />
    )
    
    // 切换到评论管理
    const commentManagementTab = screen.getByText('评论管理')
    fireEvent.click(commentManagementTab)
    
    // 验证评论列表
    expect(screen.getByText('这是一条待审核的评论')).toBeInTheDocument()
    expect(screen.getByText('这是一条已审核的评论')).toBeInTheDocument()
    
    // 审核评论
    const approveButton = screen.getAllByText('通过')[0]
    fireEvent.click(approveButton)
    
    // 验证审核函数被调用
    expect(handleCommentReview).toHaveBeenCalledWith(mockComments[0].id, 'approved')
    
    // 等待审核完成
    await waitFor(() => {
      expect(screen.getByText('评论已审核')).toBeInTheDocument()
    })
  })
});
