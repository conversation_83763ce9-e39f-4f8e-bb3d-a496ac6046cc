#!/bin/bash

# 和富家族研究平台部署脚本
# 用于在CentOS Stream 9服务器上部署Docker容器

# 显示彩色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以root用户运行
if [ "$EUID" -ne 0 ]; then
  error "请使用root用户运行此脚本"
  exit 1
fi

# 检查Docker是否已安装
if ! command -v docker &> /dev/null; then
    warn "Docker未安装，正在安装..."
    dnf install -y dnf-plugins-core
    dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
    dnf install -y docker-ce docker-ce-cli containerd.io
    systemctl start docker
    systemctl enable docker
    info "Docker安装完成"
else
    info "Docker已安装"
fi

# 检查Docker Compose是否已安装
if ! command -v docker-compose &> /dev/null; then
    warn "Docker Compose未安装，正在安装..."
    curl -L "https://github.com/docker/compose/releases/download/v2.18.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    info "Docker Compose安装完成"
else
    info "Docker Compose已安装"
fi

# 创建必要的目录
info "创建必要的目录..."
mkdir -p mysql/init mysql/conf logs/frontend logs/backend uploads

# 检查.env文件是否存在
if [ ! -f .env ]; then
    warn ".env文件不存在，从示例文件创建..."
    if [ -f .env.example ]; then
        cp .env.example .env
        info "已创建.env文件，请根据需要修改配置"
    else
        error ".env.example文件不存在，无法创建.env文件"
        exit 1
    fi
fi

# 设置文件权限
info "设置文件权限..."
chmod +x backend/scripts/init-db.js
chmod 755 -R logs uploads
chmod 644 mysql/conf/my.cnf

# 构建和启动容器
info "构建和启动Docker容器..."
docker-compose down
docker-compose build
docker-compose up -d

# 检查容器状态
info "检查容器状态..."
docker-compose ps

# 初始化数据库
info "等待数据库启动..."
sleep 10
info "初始化数据库..."
docker exec family-platform-backend node scripts/init-db.js

# 配置防火墙
info "配置防火墙..."
firewall-cmd --permanent --add-port=3000/tcp
firewall-cmd --permanent --add-port=5001/tcp
firewall-cmd --reload

info "部署完成！"
info "前端访问地址: http://**********:3000"
info "后端API地址: http://**********:5001"
info "管理员账户: admin / admin123 (请立即修改默认密码)"
