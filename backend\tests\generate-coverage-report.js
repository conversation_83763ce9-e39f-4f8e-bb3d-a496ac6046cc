/**
 * 测试覆盖率报告生成脚本
 * 
 * 用于生成详细的测试覆盖率报告
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 覆盖率报告目录
const coverageDir = path.join(__dirname, '..', 'coverage');

// 确保覆盖率报告目录存在
if (!fs.existsSync(coverageDir)) {
  fs.mkdirSync(coverageDir, { recursive: true });
}

// 运行测试并生成覆盖率报告
try {
  console.log('运行测试并生成覆盖率报告...');
  execSync('npx jest --coverage', { stdio: 'inherit' });
  
  // 检查覆盖率报告是否生成
  const lcovReportPath = path.join(coverageDir, 'lcov-report', 'index.html');
  if (fs.existsSync(lcovReportPath)) {
    console.log(`覆盖率报告已生成: ${lcovReportPath}`);
    console.log('可以在浏览器中打开上述文件查看详细报告');
  } else {
    console.error('覆盖率报告生成失败');
  }
} catch (error) {
  console.error(`测试失败: ${error.message}`);
  process.exit(1);
}

// 分析覆盖率报告
try {
  console.log('\n覆盖率摘要:');
  const coverageSummaryPath = path.join(coverageDir, 'coverage-summary.json');
  if (fs.existsSync(coverageSummaryPath)) {
    const summary = JSON.parse(fs.readFileSync(coverageSummaryPath, 'utf8'));
    const total = summary.total;
    
    console.log(`行覆盖率: ${total.lines.pct.toFixed(2)}%`);
    console.log(`函数覆盖率: ${total.functions.pct.toFixed(2)}%`);
    console.log(`分支覆盖率: ${total.branches.pct.toFixed(2)}%`);
    console.log(`语句覆盖率: ${total.statements.pct.toFixed(2)}%`);
    
    // 检查覆盖率是否达到目标
    const targetCoverage = 70; // 目标覆盖率百分比
    if (
      total.lines.pct < targetCoverage ||
      total.functions.pct < targetCoverage ||
      total.branches.pct < targetCoverage ||
      total.statements.pct < targetCoverage
    ) {
      console.warn(`\n警告: 覆盖率未达到目标 (${targetCoverage}%)`);
    } else {
      console.log(`\n覆盖率达到目标 (${targetCoverage}%)`);
    }
  } else {
    console.error('找不到覆盖率摘要文件');
  }
} catch (error) {
  console.error(`分析覆盖率报告失败: ${error.message}`);
}

console.log('\n测试完成');
