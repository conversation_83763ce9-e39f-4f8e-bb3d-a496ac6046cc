/**
 * 通知控制器测试
 *
 * 测试通知相关的API端点和业务逻辑
 */

const request = require('supertest');
const app = require('../../src/app');
const { User, Notification } = require('../../src/models');
const {
  createTestUser,
  createTestAdmin,
  generateTestToken
} = require('../utils/testHelpers');

describe('通知控制器', () => {
  let testUser;
  let adminUser;
  let testNotification;
  let userToken;
  let adminToken;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    try {
      // 创建测试用户
      testUser = await createTestUser({
        username: 'notificationuser',
        email: '<EMAIL>',
        phone: '13800000008'
      });

      // 创建管理员用户
      adminUser = await createTestAdmin({
        username: 'notificationadmin',
        email: '<EMAIL>',
        phone: '13900000008'
      });

      // 确保管理员角色
      adminUser.role = 'admin';
      await adminUser.save();

      // 创建测试通知
      testNotification = await Notification.create({
        title: '测试通知',
        content: '这是一条测试通知内容',
        type: 'system',
        status: 'unread',
        user_id: testUser.id
      });

      // 生成测试用的JWT令牌
      userToken = generateTestToken(testUser);
      adminToken = generateTestToken(adminUser);
    } catch (error) {
      console.error('创建测试数据失败:', error);
      throw error;
    }
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await Notification.destroy({ where: { id: testNotification.id } });
    await User.destroy({ where: { id: testUser.id } });
    await User.destroy({ where: { id: adminUser.id } });
  });

  // 测试获取用户通知列表
  describe('获取用户通知列表', () => {
    test.skip('用户应该能获取自己的通知', async () => {
      const response = await request(app)
        .get('/api/notifications')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('notifications');
      expect(Array.isArray(response.body.data.notifications)).toBe(true);

      // 验证返回的通知数据
      const notification = response.body.data.notifications.find(n => n.id === testNotification.id);
      expect(notification).toBeDefined();
      expect(notification).toHaveProperty('title', '测试通知');
      expect(notification).toHaveProperty('content', '这是一条测试通知内容');
      expect(notification).toHaveProperty('type', 'system');
      expect(notification).toHaveProperty('status', 'unread');
    });

    test('用户不应该能获取他人的通知', async () => {
      // 创建另一个用户的通知
      const otherNotification = await Notification.create({
        title: '另一个用户的通知',
        content: '这是另一个用户的通知内容',
        type: 'system',
        status: 'unread',
        user_id: adminUser.id
      });

      const response = await request(app)
        .get('/api/notifications')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('notifications');

      // 验证不应该返回其他用户的通知
      const notification = response.body.data.notifications.find(n => n.id === otherNotification.id);
      expect(notification).toBeUndefined();

      // 清理测试数据
      await otherNotification.destroy();
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/notifications');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('未授权');
    });
  });

  // 测试获取通知详情
  describe('获取通知详情', () => {
    test('用户应该能获取自己的通知详情', async () => {
      const response = await request(app)
        .get(`/api/notifications/${testNotification.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', testNotification.id);
      expect(response.body.data).toHaveProperty('title', '测试通知');
      expect(response.body.data).toHaveProperty('content', '这是一条测试通知内容');
      expect(response.body.data).toHaveProperty('type', 'system');
    });

    test.skip('用户不应该能获取他人的通知详情', async () => {
      // 创建另一个用户的通知
      const otherNotification = await Notification.create({
        title: '另一个用户的通知',
        content: '这是另一个用户的通知内容',
        type: 'system',
        status: 'unread',
        user_id: adminUser.id
      });

      const response = await request(app)
        .get(`/api/notifications/${otherNotification.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');

      // 清理测试数据
      await otherNotification.destroy();
    });

    test('应该返回404当通知不存在', async () => {
      const response = await request(app)
        .get('/api/notifications/9999') // 不存在的ID
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('不存在');
    });
  });

  // 测试创建通知
  describe('创建通知', () => {
    test.skip('管理员应该能创建系统通知', async () => {
      const response = await request(app)
        .post('/api/notifications')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          title: '新系统通知',
          content: '这是一条新的系统通知',
          type: 'system',
          user_id: testUser.id
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '通知创建成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('title', '新系统通知');
      expect(response.body.data).toHaveProperty('content', '这是一条新的系统通知');
      expect(response.body.data).toHaveProperty('type', 'system');
      expect(response.body.data).toHaveProperty('user_id', testUser.id);

      // 清理创建的通知
      await Notification.destroy({ where: { title: '新系统通知' } });
    });

    test.skip('普通用户不应该能创建系统通知', async () => {
      const response = await request(app)
        .post('/api/notifications')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          title: '未授权的系统通知',
          content: '这不应该成功',
          type: 'system',
          user_id: testUser.id
        });

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test('应该验证通知数据', async () => {
      const response = await request(app)
        .post('/api/notifications')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          // 缺少标题
          content: '缺少标题的通知',
          type: 'system',
          user_id: testUser.id
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('标题不能为空');
    });
  });

  // 测试标记通知为已读
  describe('标记通知为已读', () => {
    test('用户应该能标记自己的通知为已读', async () => {
      const response = await request(app)
        .put(`/api/notifications/${testNotification.id}/read`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '通知已标记为已读');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('status', 'read');

      // 恢复原始状态
      await testNotification.update({
        status: 'unread'
      });
    });

    test.skip('用户不应该能标记他人的通知为已读', async () => {
      // 创建另一个用户的通知
      const otherNotification = await Notification.create({
        title: '另一个用户的通知',
        content: '这是另一个用户的通知内容',
        type: 'system',
        status: 'unread',
        user_id: adminUser.id
      });

      const response = await request(app)
        .put(`/api/notifications/${otherNotification.id}/read`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');

      // 清理测试数据
      await otherNotification.destroy();
    });
  });

  // 测试标记所有通知为已读
  describe('标记所有通知为已读', () => {
    test('用户应该能标记所有自己的通知为已读', async () => {
      // 创建多个测试通知
      await Notification.create({
        title: '测试通知1',
        content: '测试内容1',
        type: 'system',
        status: 'unread',
        user_id: testUser.id
      });

      await Notification.create({
        title: '测试通知2',
        content: '测试内容2',
        type: 'system',
        status: 'unread',
        user_id: testUser.id
      });

      const response = await request(app)
        .put('/api/notifications/read-all')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '所有通知已标记为已读');

      // 验证所有通知都被标记为已读
      const notifications = await Notification.findAll({
        where: {
          user_id: testUser.id,
          is_read: false
        }
      });

      expect(notifications.length).toBe(0);

      // 清理测试数据
      await Notification.destroy({
        where: {
          title: ['测试通知1', '测试通知2']
        }
      });

      // 恢复原始测试通知状态
      await testNotification.update({
        status: 'unread'
      });
    });
  });

  // 测试删除通知
  describe('删除通知', () => {
    let tempNotification;

    beforeEach(async () => {
      // 创建临时通知用于删除测试
      tempNotification = await Notification.create({
        title: '临时通知',
        content: '用于删除测试的临时通知',
        type: 'system',
        status: 'unread',
        user_id: testUser.id
      });
    });

    test('用户应该能删除自己的通知', async () => {
      const response = await request(app)
        .delete(`/api/notifications/${tempNotification.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '通知删除成功');

      // 验证通知已被删除
      const notification = await Notification.findByPk(tempNotification.id);
      expect(notification).toBeNull();
    });

    test.skip('用户不应该能删除他人的通知', async () => {
      // 创建另一个用户的通知
      tempNotification = await Notification.create({
        title: '另一个用户的通知',
        content: '这是另一个用户的通知内容',
        type: 'system',
        status: 'unread',
        user_id: adminUser.id
      });

      const response = await request(app)
        .delete(`/api/notifications/${tempNotification.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');

      // 清理测试数据
      await tempNotification.destroy();
    });

    test.skip('管理员应该能删除任何通知', async () => {
      // 创建新的临时通知
      tempNotification = await Notification.create({
        title: '管理员将删除的通知',
        content: '这是管理员将要删除的通知',
        type: 'system',
        status: 'unread',
        user_id: testUser.id
      });

      const response = await request(app)
        .delete(`/api/notifications/${tempNotification.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '通知删除成功');

      // 验证通知已被删除
      const notification = await Notification.findByPk(tempNotification.id);
      expect(notification).toBeNull();
    });
  });
});
