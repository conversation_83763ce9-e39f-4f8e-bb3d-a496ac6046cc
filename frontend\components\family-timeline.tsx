"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"

import { useState, useEffect } from "react"
import Image from "next/image"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Edit, Plus, Trash2, Check, Upload, Loader2 } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "@/components/ui/use-toast"
import timelineService, { TimelineEvent, EventLevel, CreateTimelineEventParams, UpdateTimelineEventParams } from "@/services/timeline-service"

// 事件接口
interface TimelineEvent {
  id: number
  year: string
  title: string
  description: string
  content: string
  level: EventLevel
  position?: "left" | "right" // 改为可选
  icon?: string
  iconFile?: File // 用于存储上传的图片文件
}

// 限制为三种颜色
const colorPalette = [
  "#1e7a43", // 绿色
  "#f5a623", // 橙色
  "#3498db", // 蓝色
]

// 初始化空的事件数组，将通过API加载
const initialTimelineEvents: TimelineEvent[] = []
  {
    id: 6,
    year: "1921年",
    title: "参与党的早期工作",
    description: "蔡和森回国后加入中国共产党，参与党的早期工作。",
    content:
      "1921年，蔡和森回国后加入中国共产党，参与党的早期工作。他积极投身于党的组织建设和理论宣传工作，是党的早期领导人之一。蔡和森的回国为中国共产党的早期发展注入了新的力量，他的理论造诣和组织才能对党的建设发挥了重要作用。",
    level: EventLevel.National,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 7,
    year: "1922年",
    title: "当选中央委员",
    description: "蔡和森在党的二大上当选为中央委员，负责党的宣传工作。",
    content:
      "1922年，蔡和森在党的二大上当选为中央委员，负责党的宣传工作。作为中央委员，他参与了党的重大决策，负责理论宣传和思想建设，为党的早期发展做出了重要贡献。蔡和森在宣传工作中表现出色，他的文章和演讲对传播马克思主义思想、宣传党的方针政策起到了重要作用。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 8,
    year: "1925年",
    title: "领导五卅运动",
    description: "蔡和森领导五卅运动，展现卓越的群众斗争才能。",
    content:
      "1925年，蔡和森领导五卅运动，展现卓越的群众斗争才能。五卅运动是中国工人阶级反对帝国主义的重要斗争，蔡和森在运动中表现出色的组织能力和领导才能。他积极发动和组织工人罢工，领导群众斗争，推动了全国反帝爱国运动的高涨，彰显了共产党人的先锋作用。",
    level: EventLevel.National,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 9,
    year: "1931年",
    title: "蔡和森牺牲",
    description: "蔡和森因叛徒出卖在广州牺牲。",
    content:
      "1931年，蔡和森因叛徒出卖在广州被捕，后英勇牺牲，年仅38岁。作为中国共产党的创始人之一和杰出的马克思主义理论家，蔡和森的牺牲是中国革命的重大损失。他短暂而光辉的一生，为中国革命事业做出了不可磨灭的贡献，他的革命精神和理论贡献将永远激励后人。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },

  // 向警予的轨迹
  {
    id: 10,
    year: "1895年",
    title: "向警予出生",
    description: "向警予出生于湖南溆浦县的一个商人家庭。",
    content:
      "1895年，向警予出生于湖南溆浦县的一个商人家庭。她的出生为中国革命添加了一位杰出的女性力量，后来的向警予将成为中国共产党第一位女中央委员和中国妇女运动的先驱。",
    level: EventLevel.Personal,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 11,
    year: "1912年",
    title: "求学之路",
    description: "向警予考入湖南省立第一女子师范学校，后转入周南女校。",
    content:
      "1912年，向警予考入湖南省立第一女子师范学校，后转入周南女校。在学校期间，她接触到了进步思想，开始关注社会问题和妇女解放。这段求学经历对她思想的形成产生了重要影响，为她后来投身革命奠定了基础。",
    level: EventLevel.Personal,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 12,
    year: "1919年",
    title: "加入新民学会",
    description: "向警予加入新民学会，成为第一个女会员。",
    content:
      "1919年，向警予加入新民学会，成为第一个女会员。加入新民学会标志着她正式走上革命道路，开始参与进步青年的思想交流和革命活动。作为新民学会唯一的女性会员，向警予打破了传统性别观念的束缚，展现了她作为女性革命先驱的勇气和决心。",
    level: EventLevel.National,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 13,
    year: "1920年",
    title: "赴法勤工俭学与婚姻",
    description: "向警予与蔡和森在法国蒙达尼举行婚礼，积极参与建党工作。",
    content:
      "1920年，向警予与蔡和森在法国蒙达尼举行婚礼，积极参与建党工作。他们的婚礼是一个简朴而具有革命意义的仪式，结合了爱情与革命的双重使命。在法国期间，向警予不仅与蔡和森共同研究马克思主义，还积极参与旅欧中国社会主义青年团的工作，为中国共产党的成立做准备。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 14,
    year: "1922年",
    title: "成为首位女中央委员",
    description: "向警予成为中国共产党第一位女中央委员，任妇女部长。",
    content:
      "1922年，向警予成为中国共产党第一位女中央委员，任妇女部长。这一任命体现了党对女性同志的重视和对向警予能力的认可。作为妇女部长，她积极开展妇女工作，推动妇女解放运动，为中国妇女争取平等权利做出了重要贡献。",
    level: EventLevel.National,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 15,
    year: "1925年",
    title: "领导工人罢工",
    description: "向警予领导上海闸北丝厂和南洋烟厂的大罢工。",
    content:
      "1925年，向警予领导上海闸北丝厂和南洋烟厂的大罢工。这次罢工是中国工人运动史上的重要事件，向警予充分发挥了她的组织才能和领导艺术，带领工人特别是女工与资本家和帝国主义进行斗争。这次斗争不仅改善了工人的待遇，也提高了工人阶级的觉悟，促进了工人运动的发展。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 16,
    year: "1928年",
    title: "向警予牺牲",
    description: "向警予因叛徒出卖在汉口英勇就义。",
    content:
      "1928年，向警予因叛徒出卖在汉口被捕，后英勇就义，年仅33岁。作为中国共产党第一位女中央委员和妇女运动的先驱，向警予的牺牲是中国革命的重大损失。她短暂而光辉的一生，为中国革命和妇女解放事业做出了不可磨灭的贡献，她的革命精神和坚定信念将永远激励后人。",
    level: EventLevel.National,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },

  // 李富春的轨迹
  {
    id: 17,
    year: "1900年",
    title: "李富春出生",
    description: "李富春出生于湖南长沙一个贫寒的教师家庭。",
    content:
      "1900年，李富春出生于湖南长沙一个贫寒的教师家庭。他的出生为中国革命添加了一位杰出的力量，后来的李富春将成为中国共产党的重要领导人和新中国经济建设的奠基人之一。",
    level: EventLevel.Personal,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 18,
    year: "1919年",
    title: "李富春赴法勤工俭学",
    description: "李富春赴法勤工俭学，研读马克思主义著作，逐渐成为共产主义者。",
    content:
      "1919年，李富春赴法勤工俭学，研读马克思主义著作，逐渐成为共产主义者。在法国期间，他接触到了马克思主义理论，参与了进步留学生的活动，思想上有了质的飞跃。这段经历对李富春的革命道路产生了决定性影响，为他后来成为共产党人奠定了思想基础。",
    level: EventLevel.Personal,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 19,
    year: "1921年",
    title: "加入中国共产党",
    description: "李富春加入中国共产党，参与旅欧中国社会主义青年团的工作。",
    content:
      "1921年，李富春加入中国共产党，参与旅欧中国社会主义青年团的工作。作为旅欧党支部的成员，他积极传播马克思主义思想，组织留学生开展革命活动，为中国共产党在海外的早期发展做出了贡献。这一时期的工作经验，为李富春回国后参与革命斗争积累了宝贵经验。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 20,
    year: "1923年",
    title: "革命伴侣的结合",
    description: "李富春与蔡畅在巴黎结婚，共同投身革命事业。",
    content:
      "1923年，李富春与蔡畅在巴黎结婚，共同投身革命事业。他们的结合不仅是个人感情的结晶，更是革命理想的契合。作为革命伴侣，李富春和蔡畅在漫长的革命岁月中相互支持、共同奋斗，创造了革命家庭的典范。他们的婚姻也成为葛健豪家族革命传统延续的重要纽带。",
    level: EventLevel.Family,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 21,
    year: "1925年",
    title: "参加北伐战争",
    description: "李富春回国后参加北伐战争，担任国民革命军第二军副党代表兼政治部主任。",
    content:
      "1925年，李富春回国后参加北伐战争，担任国民革命军第二军副党代表兼政治部主任。在北伐战争中，他负责部队的政治工作，宣传革命思想，争取广大士兵对革命的支持。李富春的工作为北伐战争的胜利做出了重要贡献，也积累了军事工作的经验。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 22,
    year: "1931年",
    title: "开展地下斗争",
    description: "李富春在白色恐怖下，赴香港担任广东省委组织部长，开展地下斗争。",
    content:
      "1931年，李富春在白色恐怖下，赴香港担任广东省委组织部长，开展地下斗争。这一时期，他冒着生命危险，隐蔽身份，在极其艰难的条件下坚持革命工作，保存和发展党的力量。李富春的坚韧不拔和组织才能，为党在白色恐怖下的生存和发展做出了重要贡献。",
    level: EventLevel.National,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 23,
    year: "1949年",
    title: "参与新中国经济建设",
    description: "李富春参与中华人民共和国的经济建设工作，担任国务院副总理。",
    content:
      "1949年，李富春参与中华人民共和国的经济建设工作，担任国务院副总理。在新中国成立初期，他负责国民经济恢复和发展的重要工作，参与制定和实施第一个五年计划，为新中国的经济建设做出了重要贡献。李富春的工作能力和经济管理才能，使他成为新中国经济建设的重要领导者之一。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },

  // 蔡畅的轨迹
  {
    id: 24,
    year: "1894年",
    title: "蔡畅出生",
    description: "蔡畅出生于湖南双峰县荷叶镇。",
    content:
      "1894年，蔡畅出生于湖南双峰县荷叶镇，是葛健豪和蔡蓉峰的女儿。她的出生为中国革命添加了一位杰出的女性力量，后来的蔡畅将成为中国妇女运动的重要领导人。",
    level: EventLevel.Personal,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 25,
    year: "1919年",
    title: "蔡畅赴法勤工俭学",
    description: "蔡畅赴法勤工俭学，参与留法学生运动。",
    content:
      "1919年，蔡畅赴法勤工俭学，参与留法学生运动。在法国期间，她接触到了马克思主义思想，参与了进步留学生活动，思想上有了重大转变。这段经历为蔡畅后来成为革命者奠定了思想基础，也是她革命生涯的起点。",
    level: EventLevel.Personal,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 26,
    year: "1923年",
    title: "蔡畅与李富春结婚",
    description: "蔡畅与李富春在巴黎结婚，共同投身革命事业。",
    content:
      "1923年，蔡畅与李富春在巴黎结婚，共同投身革命事业。他们的结合既是个人感情的选择，也是革命理想的结合。作为革命伴侣，蔡畅和李富春在长期的革命斗争中相互支持、共同奋斗，创造了革命家庭的典范。他们的婚姻也成为葛健豪家族革命传统的重要延续。",
    level: EventLevel.Family,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 27,
    year: "1925年",
    title: "参与妇女解放运动",
    description: "蔡畅回国后参与妇女解放运动，担任全国妇联主席。",
    content:
      "1925年，蔡畅回国后积极参与妇女解放运动。她利用自己在法国学习和工作的经验，结合中国妇女的实际情况，开展妇女工作，组织和发动妇女参与革命斗争。蔡畅的工作为中国妇女运动的发展做出了重要贡献，推动了妇女解放事业的进步。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 28,
    year: "1949年",
    title: "继续领导全国妇联",
    description: "蔡畅继续领导全国妇联工作，推动妇女解放运动。",
    content:
      "1949年，中华人民共和国成立后，蔡畅继续担任全国妇联主席，领导全国妇女解放运动。在她的领导下，全国妇联积极贯彻党的方针政策，组织和动员广大妇女参与社会主义建设，争取妇女在政治、经济、文化等各方面的平等权利。蔡畅的工作为新中国妇女事业的发展做出了重要贡献。",
    level: EventLevel.National,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 29,
    year: "1960年代",
    title: "参与国际妇女运动",
    description: "蔡畅积极参与国际妇女运动，为妇女权益争取更多支持。",
    content:
      "1960年代，蔡畅积极参与国际妇女运动，代表中国出席各种国际会议，宣传中国妇女解放的成就，加强与各国妇女组织的交流和合作。她的国际活动扩大了中国妇女运动的国际影响，也争取到了国际社会对中国妇女事业的支持和理解。蔡畅的工作为中国妇女运动的国际化做出了重要贡献。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 30,
    year: "1990年",
    title: "蔡畅病逝",
    description: "蔡畅在北京病逝，享年96岁。",
    content:
      "1990年，蔡畅在北京病逝，享年96岁。作为中国妇女运动的重要领导人和中国共产党的优秀党员，蔡畅的一生是为革命和妇女解放事业奋斗的一生。她的辞世是中国革命事业的重大损失，但她的革命精神和对妇女解放事业的贡献将永远铭记在人民心中。",
    level: EventLevel.Personal,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },

  // 葛健豪的轨迹
  {
    id: 31,
    year: "1865年",
    title: "葛健豪出生",
    description: "葛健豪出生于湖南双峰县荷叶镇，父亲是湘军将领，家庭富裕。",
    content:
      '1865年，葛健豪出生于湖南双峰县荷叶镇，父亲是湘军将领，家庭富裕。她的出生为中国革命添加了一位杰出的女性力量，后来的葛健豪将成为中国近代史上罕见的"革命母亲"，培养出多位杰出的革命家。',
    level: EventLevel.Personal,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 32,
    year: "1878年",
    title: "争取教育权利",
    description: "10岁的葛健豪通过哭诉争取到与哥哥一起上学的机会。",
    content:
      "1878年，10岁的葛健豪通过哭诉争取到与哥哥一起上学的机会。在那个女子不被重视教育的时代，葛健豪表现出了对知识的渴望和争取平等权利的勇气。这一早期经历反映了她不畏封建礼教、追求自我发展的性格特点，也为她后来鼓励子女接受教育、投身革命奠定了基础。",
    level: EventLevel.Personal,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 33,
    year: "1881年",
    title: "遵从父命结婚",
    description: "16岁的葛健豪与蔡蓉峰结婚，这是父亲生前订下的婚约。",
    content:
      "1881年，16岁的葛健豪与蔡蓉峰结婚，这是父亲生前订下的婚约。尽管是传统的父母之命、媒妁之言，但这段婚姻最终成为了葛健豪人生的重要转折点。通过这段婚姻，她成为了蔡家的一员，并在这个家庭中生育了多位子女，其中包括后来成为革命者的蔡和森、蔡畅等。这段婚姻也是葛健豪开始接触新思想、参与社会活动的开端。",
    level: EventLevel.Personal,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 34,
    year: "1890年代",
    title: "接触进步思想",
    description: "葛健豪与秋瑾结识，受到其思想影响，开始关注社会变革。",
    content:
      "1890年代，葛健豪与秋瑾结识，受到其思想影响，开始关注社会变革。秋瑾是中国近代史上著名的女革命家，她的妇女解放思想和革命精神对葛健豪产生了深远影响。通过与秋瑾的交往，葛健豪的思想逐渐从传统走向进步，开始关注社会问题和妇女权益，这为她后来支持子女从事革命活动奠定了思想基础。",
    level: EventLevel.Family,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 35,
    year: "1903-1907年",
    title: "家庭变故",
    description: "葛健豪连续失去母亲、二女儿和长子，家庭陷入困境。",
    content:
      "1903-1907年间，葛健豪连续失去母亲、二女儿和长子，家庭陷入困境。这些接连的打击给葛健豪带来了巨大的痛苦，但也锻炼了她坚强的意志和面对困难的勇气。尽管经历了如此多的变故，葛健豪仍然坚强地支撑着家庭，并始终关心子女的教育和成长，展现了非凡的坚韧精神。",
    level: EventLevel.Personal,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 36,
    year: "1913年",
    title: "支持子女教育",
    description: "葛健豪变卖首饰支持儿子蔡和森去长沙读书，推动子女接受教育。",
    content:
      "1913年，葛健豪变卖首饰支持儿子蔡和森去长沙读书，推动子女接受教育。在经济困难的情况下，葛健豪仍然重视子女的教育，不惜变卖个人财物来支持他们的学业。这一举动不仅体现了她对子女的深爱，也反映了她对教育重要性的认识。蔡和森在长沙的学习经历，为他接触进步思想、走上革命道路奠定了基础。",
    level: EventLevel.Family,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 37,
    year: "1919年",
    title: "赴法勤工俭学",
    description: "48岁的葛健豪与子女一起赴法勤工俭学，成为当时最年长的留学生。",
    content:
      "1919年，48岁的葛健豪与子女一起赴法勤工俭学，成为当时最年长的留学生。这一决定展现了葛健豪非凡的勇气和进取精神。在法国期间，她不仅自己学习法语和各种知识，还照顾和支持子女的生活和学习。葛健豪的留法经历，不仅开阔了她自己的视野，也为子女接触马克思主义、走上革命道路创造了条件。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 38,
    year: "1922年",
    title: "创办女子职业学校",
    description: "葛健豪回国后创办平民女子职业学校，宣传女权思想，推动妇女解放。",
    content:
      "1922年，葛健豪回国后创办平民女子职业学校，宣传女权思想，推动妇女解放。这所学校不仅教授知识和技能，更重要的是传播进步思想，提高妇女的自主意识和社会地位。葛健豪的这一举措，体现了她受法国留学影响后对妇女解放事业的关注和贡献，也为中国妇女解放运动做出了实践探索。",
    level: EventLevel.National,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 39,
    year: "1925年",
    title: "参与妇女解放运动",
    description: "葛健豪参与湖南妇女解放运动，为革命工作者传递情报。",
    content:
      "1925年，葛健豪积极参与湖南妇女解放运动，并为革命工作者传递情报。尽管年过半百，她仍然热心投身革命事业，利用自己的身份和条件为党的地下工作提供支持。葛健豪的这些活动不仅体现了她对革命事业的坚定支持，也展示了她作为一名普通妇女对国家和民族命运的关心与担当。",
    level: EventLevel.National,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 40,
    year: "1927年",
    title: "家族牺牲",
    description: "葛健豪的二儿子蔡麓仙在省港罢工中牺牲。",
    content:
      "1927年，葛健豪的二儿子蔡麓仙在省港罢工中牺牲。这是葛健豪家族为革命事业付出的第一个生命代价。尽管悲痛欲绝，葛健豪仍然支持其他子女继续从事革命工作，展现了革命母亲的坚强意志和崇高品质。蔡麓仙的牺牲也成为家族革命历史的重要篇章，激励着后人继续为革命事业奋斗。",
    level: EventLevel.Family,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 41,
    year: "1931年",
    title: "蔡和森牺牲",
    description: "葛健豪的三儿子蔡和森在广州牺牲，家人隐瞒消息。",
    content:
      "1931年，葛健豪的三儿子蔡和森在广州被捕牺牲，家人为了不使葛健豪过度悲伤，隐瞒了这一消息。蔡和森作为中国共产党的创始人之一和杰出的马克思主义理论家，他的牺牲是葛健豪家族乃至中国革命的重大损失。尽管不知道儿子已经牺牲，葛健豪仍然继续支持革命事业，关心革命后代的成长。",
    level: EventLevel.Family,
    position: "left",
    icon: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 42,
    year: "1943年",
    title: "葛健豪逝世",
    description: "葛健豪在湖南双峰县病逝，至死不知蔡和森和蔡麓仙已牺牲。",
    content:
      '1943年，葛健豪在湖南双峰县病逝，享年78岁。令人动容的是，直到生命的最后一刻，她都不知道儿子蔡和森和蔡麓仙已经为革命牺牲。葛健豪一生培养了多位杰出的革命家，包括蔡和森、蔡畅、向警予和李富春等，她对中国革命的贡献是独特而重要的。作为中国近代史上罕见的"革命母亲"，葛健豪的精神将永远激励后人。',
    level: EventLevel.Personal,
    position: "right",
    icon: "/placeholder.svg?height=60&width=60",
  },
]

// 定义 "一带一路" 和 "走出去" 变量
const 一带一路 = "一带一路"
const 走出去 = "走出去"
const 革命母亲 = "革命母亲"

// 生成年份选项
const generateYearOptions = () => {
  const years = []
  for (let year = 1800; year <= 2200; year++) {
    years.push(year)
  }
  return years
}

export function FamilyTimeline() {
  const [selectedEvent, setSelectedEvent] = useState<TimelineEvent | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [editingEvent, setEditingEvent] = useState<TimelineEvent | null>(null)
  const [isAddingEvent, setIsAddingEvent] = useState(false)
  const [newEvent, setNewEvent] = useState<Partial<TimelineEvent>>({
    year: "",
    title: "",
    description: "",
    content: "",
    level: EventLevel.Personal,
  })

  // 使用Auth上下文获取用户权限信息
  const { isLoggedIn, hasPermission } = useAuth()

  // 检查用户是否有管理时间轴的权限
  const canEditTimeline = isLoggedIn && hasPermission("edit_timeline")

  // 按年份排序事件并确保一左一右分布
  const sortedEvents = [...timelineEvents]
    .sort((a, b) => {
      // 提取年份数字进行比较
      const yearA = Number.parseInt(a.year.replace(/[^0-9]/g, ""))
      const yearB = Number.parseInt(b.year.replace(/[^0-9]/g, ""))
      return yearA - yearB
    })
    .map((event, index) => {
      // 确保严格一左一右分布
      return {
        ...event,
        position: index % 2 === 0 ? "left" : "right",
      }
    })

  // 处理图片上传
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>, isNewEvent = false) => {
    const file = e.target.files?.[0]
    if (!file) return

    // 检查文件类型
    if (!file.type.startsWith("image/")) {
      alert("请上传图片文件")
      return
    }

    // 检查文件大小 (限制为 2MB)
    if (file.size > 2 * 1024 * 1024) {
      alert("图片大小不能超过 2MB")
      return
    }

    // 创建临时URL
    const imageUrl = URL.createObjectURL(file)

    // 更新状态
    if (isNewEvent) {
      setNewEvent({ ...newEvent, icon: imageUrl, iconFile: file })
    } else if (editingEvent) {
      setEditingEvent({ ...editingEvent, icon: imageUrl, iconFile: file })
    }
  }

  // 处理编辑事件
  const handleEditEvent = (event: TimelineEvent) => {
    setEditingEvent({ ...event })
    setIsAddingEvent(false)
    setSelectedEvent(null)
  }

  // 处理保存编辑
  const handleSaveEdit = () => {
    if (editingEvent) {
      // 在实际应用中，这里应该调用API上传图片并获取URL
      // 如果有新上传的图片文件，应该先上传到服务器，然后获取URL
      if (editingEvent.iconFile) {
        console.log("上传图片文件:", editingEvent.iconFile)
        // 实际应用中，这里应该是异步上传图片，然后获取URL
        // const imageUrl = await uploadImage(editingEvent.iconFile)
        // editingEvent.icon = imageUrl
      }

      // 在实际应用中，这里应该调用API保存更改
      // 这里我们只是更新前端状态
      const updatedEvents = timelineEvents.map((event) => (event.id === editingEvent.id ? editingEvent : event))
      // 在实际应用中，这里应该更新后端数据
      console.log("保存编辑的事件:", editingEvent)
      alert("事件已更新！在实际应用中，这将保存到数据库。")
    }
    setEditingEvent(null)
  }

  // 处理删除事件
  const handleDeleteEvent = (id: number) => {
    // 在实际应用中，这里应该调用API删除事件
    // 这里我们只是更新前端状态
    const updatedEvents = timelineEvents.filter((event) => event.id !== id)
    // 在实际应用中，这里应该更新后端数据
    console.log("删除事件ID:", id)
    alert("事件已删除！在实际应用中，这将从数据库中删除。")
  }

  // 处理添加新事件
  const handleAddEvent = () => {
    setNewEvent({
      year: "",
      title: "",
      description: "",
      content: "",
      level: EventLevel.Personal,
    })
    setIsAddingEvent(true)
    setEditingEvent(null)
    setSelectedEvent(null)
  }

  // 处理保存新事件
  const handleSaveNewEvent = () => {
    // 在实际应用中，这里应该调用API上传图片并获取URL
    // 如果有新上传的图片文件，应该先上传到服务器，然后获取URL
    if (newEvent.iconFile) {
      console.log("上传图片文件:", newEvent.iconFile)
      // 实际应用中，这里应该是异步上传图片，然后获取URL
      // const imageUrl = await uploadImage(newEvent.iconFile)
      // newEvent.icon = imageUrl
    }

    // 不再在这里分配位置，位置将在渲染时动态分配
    const newEventWithId = {
      ...newEvent,
      id: timelineEvents.length + 1, // 简单生成一个新ID
    } as TimelineEvent

    // 在实际应用中，这里应该更新后端数据
    console.log("保存新事件:", newEventWithId)
    alert("新事件已添加！在实际应用中，这将保存到数据库。")

    setIsAddingEvent(false)
  }

  const handleEventClick = (event: TimelineEvent) => {
    setSelectedEvent(event)
  }

  const closeModal = () => {
    setSelectedEvent(null)
  }

  // 为每个事件分配一个颜色（仅使用三种颜色）
  const getEventColor = (id: number) => {
    // 使用事件ID模3来确保只使用三种颜色
    return colorPalette[id % 3]
  }

  return (
    <>
      <div className="flex justify-end mb-4">
        {canEditTimeline && (
          <Button
            onClick={() => setIsEditing(!isEditing)}
            className="bg-[#1e7a43] hover:bg-[#1e7a43]/90 flex items-center gap-2"
          >
            {isEditing ? (
              <>
                <Check className="h-4 w-4" />
                完成编辑
              </>
            ) : (
              <>
                <Edit className="h-4 w-4" />
                编辑时间轴
              </>
            )}
          </Button>
        )}

        {isEditing && canEditTimeline && (
          <Button onClick={handleAddEvent} className="ml-2 bg-blue-600 hover:bg-blue-700 flex items-center gap-2">
            <Plus className="h-4 w-4" />
            添加事件
          </Button>
        )}
      </div>
      <div className="relative">
        {/* 移除颜色图例，不再需要 */}

        {/* 时间线中心线 - 保持绿色 */}
        <div className="absolute left-[12px] md:left-1/2 top-0 bottom-0 w-0.5 bg-[#1e7a43]"></div>

        {/* 时间线事件 */}
        <div className="relative">
          {sortedEvents.map((event) => {
            // 为每个事件获取一个颜色（从三种颜色中选择）
            const eventColor = getEventColor(event.id)

            return (
              <div
                key={event.id}
                className={cn(
                  "relative mb-8 md:mb-16",
                  event.position === "left" ? "pl-[24px] md:pr-[50%] md:pl-0" : "pl-[24px] md:pl-[50%]",
                )}
              >
                <div
                  className={cn(
                    "relative border rounded-xl p-3 md:p-6 shadow-sm hover:shadow-md cursor-pointer transition-all duration-300",
                    event.position === "left" ? "md:mr-8" : "md:ml-8",
                  )}
                  style={{ borderColor: eventColor, backgroundColor: `${eventColor}10` }}
                  onClick={() => handleEventClick(event)}
                >
                  {/* 连接线 */}
                  <div
                    className={cn(
                      "absolute top-1/2 w-[12px] md:w-8 h-0.5 bg-[#1e7a43]",
                      event.position === "left"
                        ? "left-[-12px] md:left-auto md:right-[-8px]"
                        : "left-[-12px] md:left-[-8px]",
                    )}
                  ></div>

                  {/* 时间节点 */}
                  <div
                    className={cn(
                      "absolute top-1/2 -translate-y-1/2 w-4 h-4 rounded-full border-4 border-white",
                      event.position === "left"
                        ? "left-[-20px] md:left-auto md:right-[-10px] md:-mr-2"
                        : "left-[-20px] md:left-[-10px] md:-ml-2",
                    )}
                    style={{ backgroundColor: eventColor }}
                  ></div>

                  {/* 事件内容 */}
                  <div className="flex items-start gap-2 md:gap-4">
                    {event.icon && (
                      <div className="hidden md:block flex-shrink-0">
                        <Image
                          src={event.icon || "/placeholder.svg"}
                          alt={event.title}
                          width={60}
                          height={60}
                          className="rounded-md"
                        />
                      </div>
                    )}
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <span className="text-base md:text-lg font-bold text-gray-800">{event.year}</span>
                        <span
                          className="inline-block w-4 h-4 rounded-full shadow-sm"
                          style={{ backgroundColor: eventColor }}
                        ></span>
                      </div>
                      <h3 className="text-md md:text-xl font-bold mb-2">{event.title}</h3>
                      <p className="text-sm md:text-base text-gray-600">{event.description}</p>
                    </div>
                  </div>
                  {isEditing && canEditTimeline && (
                    <div className="absolute top-2 right-2 flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEditEvent(event)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 text-red-500 hover:text-red-700"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteEvent(event.id)
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>

        {/* 事件详情模态框 */}
        {selectedEvent && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
            <div className="bg-white rounded-lg w-full max-w-2xl p-4 md:p-6 relative max-h-[80vh] overflow-y-auto">
              <button onClick={closeModal} className="absolute top-4 right-4 text-gray-500 hover:text-gray-700">
                <X className="h-5 w-5" />
              </button>

              <div className="mb-4">
                <div className="flex items-center gap-3 mb-3">
                  <span className="text-xl font-bold text-gray-800">{selectedEvent.year}</span>
                  <span
                    className="inline-block w-5 h-5 rounded-full shadow-sm"
                    style={{ backgroundColor: getEventColor(selectedEvent.id) }}
                  ></span>
                </div>
                <h2 className="text-xl md:text-2xl font-bold">{selectedEvent.title}</h2>
              </div>

              {selectedEvent.icon && (
                <div className="mb-4">
                  <Image
                    src={selectedEvent.icon || "/placeholder.svg"}
                    alt={selectedEvent.title}
                    width={120}
                    height={120}
                    className="rounded-md"
                  />
                </div>
              )}

              <p className="text-gray-700 whitespace-pre-line">{selectedEvent.content}</p>
            </div>
          </div>
        )}
        {/* 编辑事件对话框 */}
        {editingEvent && (
          <Dialog open={!!editingEvent} onOpenChange={() => setEditingEvent(null)}>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>编辑时间轴事件</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="year">年份</Label>
                    <Select
                      value={editingEvent.year.replace(/[^0-9]/g, "")}
                      onValueChange={(value) => setEditingEvent({ ...editingEvent, year: `${value}年` })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择年份" />
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] overflow-y-auto">
                        {generateYearOptions().map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}年
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="title">标题</Label>
                    <Input
                      id="title"
                      value={editingEvent.title}
                      onChange={(e) => setEditingEvent({ ...editingEvent, title: e.target.value })}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">简短描述</Label>
                  <Input
                    id="description"
                    value={editingEvent.description}
                    onChange={(e) => setEditingEvent({ ...editingEvent, description: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="content">详细内容</Label>
                  <Textarea
                    id="content"
                    rows={5}
                    value={editingEvent.content}
                    onChange={(e) => setEditingEvent({ ...editingEvent, content: e.target.value })}
                  />
                </div>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="level">影响范围</Label>
                    <Select
                      value={editingEvent.level}
                      onValueChange={(value) => setEditingEvent({ ...editingEvent, level: value as EventLevel })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择影响范围" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={EventLevel.National}>国家</SelectItem>
                        <SelectItem value={EventLevel.Family}>家族</SelectItem>
                        <SelectItem value={EventLevel.Personal}>个人</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="icon">上传图标</Label>
                  <div className="flex items-center gap-4">
                    {editingEvent.icon && (
                      <div className="relative w-16 h-16 border rounded overflow-hidden">
                        <Image
                          src={editingEvent.icon || "/placeholder.svg"}
                          alt="事件图标"
                          width={64}
                          height={64}
                          className="object-cover"
                        />
                      </div>
                    )}
                    <label className="flex items-center gap-2 px-4 py-2 border rounded cursor-pointer hover:bg-gray-50">
                      <Upload className="h-4 w-4" />
                      <span>选择图片</span>
                      <input type="file" accept="image/*" className="hidden" onChange={(e) => handleImageUpload(e)} />
                    </label>
                    {editingEvent.icon && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingEvent({ ...editingEvent, icon: undefined, iconFile: undefined })}
                      >
                        移除
                      </Button>
                    )}
                  </div>
                  <p className="text-xs text-gray-500">支持 JPG, PNG 格式，大小不超过 2MB</p>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setEditingEvent(null)}>
                  取消
                </Button>
                <Button onClick={handleSaveEdit} className="bg-[#1e7a43] hover:bg-[#1e7a43]/90">
                  保存更改
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* 添加新事件对话框 */}
        {isAddingEvent && (
          <Dialog open={isAddingEvent} onOpenChange={() => setIsAddingEvent(false)}>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>添加新的时间轴事件</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-year">年份</Label>
                    <Select
                      value={newEvent.year?.replace(/[^0-9]/g, "") || ""}
                      onValueChange={(value) => setNewEvent({ ...newEvent, year: `${value}年` })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择年份" />
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] overflow-y-auto">
                        {generateYearOptions().map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}年
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-title">标题</Label>
                    <Input
                      id="new-title"
                      value={newEvent.title}
                      onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                      placeholder="事件标题"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-description">简短描述</Label>
                  <Input
                    id="new-description"
                    value={newEvent.description}
                    onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                    placeholder="简短描述事件内容"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-content">详细内容</Label>
                  <Textarea
                    id="new-content"
                    rows={5}
                    value={newEvent.content}
                    onChange={(e) => setNewEvent({ ...newEvent, content: e.target.value })}
                    placeholder="详细描述事件内容和历史意义"
                  />
                </div>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-level">影响范围</Label>
                    <Select
                      value={newEvent.level}
                      onValueChange={(value) => setNewEvent({ ...newEvent, level: value as EventLevel })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择影响范围" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={EventLevel.National}>国家</SelectItem>
                        <SelectItem value={EventLevel.Family}>家族</SelectItem>
                        <SelectItem value={EventLevel.Personal}>个人</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-icon">上传图标</Label>
                  <div className="flex items-center gap-4">
                    {newEvent.icon && (
                      <div className="relative w-16 h-16 border rounded overflow-hidden">
                        <Image
                          src={newEvent.icon || "/placeholder.svg"}
                          alt="事件图标"
                          width={64}
                          height={64}
                          className="object-cover"
                        />
                      </div>
                    )}
                    <label className="flex items-center gap-2 px-4 py-2 border rounded cursor-pointer hover:bg-gray-50">
                      <Upload className="h-4 w-4" />
                      <span>选择图片</span>
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => handleImageUpload(e, true)}
                      />
                    </label>
                    {newEvent.icon && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setNewEvent({ ...newEvent, icon: undefined, iconFile: undefined })}
                      >
                        移除
                      </Button>
                    )}
                  </div>
                  <p className="text-xs text-gray-500">支持 JPG, PNG 格式，大小不超过 2MB</p>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddingEvent(false)}>
                  取消
                </Button>
                <Button onClick={handleSaveNewEvent} className="bg-[#1e7a43] hover:bg-[#1e7a43]/90">
                  添加事件
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </>
  )
}
