/**
 * 表单验证函数测试
 */

import {
  validateEmail,
  validatePassword,
  validatePhone,
  validateUsername,
  validateRequired,
  validateLength,
  validateMatch
} from '@/utils/validation';

describe('表单验证函数', () => {
  // 测试邮箱验证
  describe('validateEmail', () => {
    test('应该验证有效的邮箱', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    test('应该拒绝无效的邮箱', () => {
      expect(validateEmail('')).toBe(false);
      expect(validateEmail('user')).toBe(false);
      expect(validateEmail('user@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
      expect(validateEmail('user@example')).toBe(false);
      expect(validateEmail('user@.com')).toBe(false);
      expect(validateEmail('<EMAIL>')).toBe(false);
    });
  });

  // 测试密码验证
  describe('validatePassword', () => {
    test('应该验证符合要求的密码', () => {
      // 默认要求：至少8个字符，包含大小写字母、数字和特殊字符
      expect(validatePassword('Password123!')).toBe(true);
      expect(validatePassword('Str0ng_P@ssw0rd')).toBe(true);
      expect(validatePassword('C0mpl3x!P@ssw0rd')).toBe(true);
    });

    test('应该拒绝不符合要求的密码', () => {
      expect(validatePassword('')).toBe(false);
      expect(validatePassword('short')).toBe(false); // 太短
      expect(validatePassword('password123')).toBe(false); // 没有大写字母
      expect(validatePassword('PASSWORD123')).toBe(false); // 没有小写字母
      expect(validatePassword('Password')).toBe(false); // 没有数字
      expect(validatePassword('Password123')).toBe(false); // 没有特殊字符
    });

    test('应该支持自定义密码要求', () => {
      // 只要求至少6个字符
      expect(validatePassword('pass12', { minLength: 6, requireUppercase: false, requireLowercase: false, requireNumber: false, requireSpecial: false })).toBe(true);
      
      // 要求至少10个字符，包含大写字母和数字
      expect(validatePassword('Password123', { minLength: 10, requireUppercase: true, requireLowercase: true, requireNumber: true, requireSpecial: false })).toBe(true);
      expect(validatePassword('password123', { minLength: 10, requireUppercase: true, requireLowercase: true, requireNumber: true, requireSpecial: false })).toBe(false); // 没有大写字母
    });
  });

  // 测试手机号验证
  describe('validatePhone', () => {
    test('应该验证有效的中国手机号', () => {
      expect(validatePhone('13800138000')).toBe(true);
      expect(validatePhone('15912345678')).toBe(true);
      expect(validatePhone('17612345678')).toBe(true);
      expect(validatePhone('19912345678')).toBe(true);
    });

    test('应该拒绝无效的手机号', () => {
      expect(validatePhone('')).toBe(false);
      expect(validatePhone('1380013800')).toBe(false); // 太短
      expect(validatePhone('138001380001')).toBe(false); // 太长
      expect(validatePhone('23800138000')).toBe(false); // 不是1开头
      expect(validatePhone('12345678901')).toBe(false); // 不是有效的前缀
      expect(validatePhone('abcdefghijk')).toBe(false); // 不是数字
    });
  });

  // 测试用户名验证
  describe('validateUsername', () => {
    test('应该验证有效的用户名', () => {
      expect(validateUsername('user123')).toBe(true);
      expect(validateUsername('user_name')).toBe(true);
      expect(validateUsername('User-Name')).toBe(true);
      expect(validateUsername('username')).toBe(true);
    });

    test('应该拒绝无效的用户名', () => {
      expect(validateUsername('')).toBe(false);
      expect(validateUsername('ab')).toBe(false); // 太短
      expect(validateUsername('a'.repeat(21))).toBe(false); // 太长
      expect(validateUsername('user name')).toBe(false); // 包含空格
      expect(validateUsername('user@name')).toBe(false); // 包含特殊字符
      expect(validateUsername('用户名')).toBe(false); // 包含中文
    });

    test('应该支持自定义用户名要求', () => {
      // 允许2-30个字符
      expect(validateUsername('ab', { minLength: 2, maxLength: 30 })).toBe(true);
      expect(validateUsername('a'.repeat(25), { minLength: 2, maxLength: 30 })).toBe(true);
      
      // 不允许下划线和连字符
      expect(validateUsername('user_name', { allowUnderscore: false })).toBe(false);
      expect(validateUsername('user-name', { allowDash: false })).toBe(false);
    });
  });

  // 测试必填验证
  describe('validateRequired', () => {
    test('应该验证非空值', () => {
      expect(validateRequired('value')).toBe(true);
      expect(validateRequired('0')).toBe(true);
      expect(validateRequired(0)).toBe(true);
      expect(validateRequired(false)).toBe(true);
      expect(validateRequired([])).toBe(true);
      expect(validateRequired({})).toBe(true);
    });

    test('应该拒绝空值', () => {
      expect(validateRequired('')).toBe(false);
      expect(validateRequired(null)).toBe(false);
      expect(validateRequired(undefined)).toBe(false);
    });

    test('应该支持自定义错误消息', () => {
      const result = validateRequired('', '此字段为必填项');
      expect(result).toBe(false);
    });
  });

  // 测试长度验证
  describe('validateLength', () => {
    test('应该验证符合长度要求的值', () => {
      expect(validateLength('abc', 1, 5)).toBe(true);
      expect(validateLength('abc', 3, 3)).toBe(true);
      expect(validateLength('', 0, 5)).toBe(true);
    });

    test('应该拒绝不符合长度要求的值', () => {
      expect(validateLength('abc', 4, 10)).toBe(false); // 太短
      expect(validateLength('abcdefghijk', 1, 5)).toBe(false); // 太长
    });

    test('应该支持自定义错误消息', () => {
      const result = validateLength('abc', 4, 10, '长度必须在4-10个字符之间');
      expect(result).toBe(false);
    });
  });

  // 测试匹配验证
  describe('validateMatch', () => {
    test('应该验证匹配的值', () => {
      expect(validateMatch('password', 'password')).toBe(true);
      expect(validateMatch(123, 123)).toBe(true);
      expect(validateMatch(true, true)).toBe(true);
    });

    test('应该拒绝不匹配的值', () => {
      expect(validateMatch('password', 'password123')).toBe(false);
      expect(validateMatch(123, 456)).toBe(false);
      expect(validateMatch(true, false)).toBe(false);
    });

    test('应该支持自定义错误消息', () => {
      const result = validateMatch('password', 'password123', '两次输入不一致');
      expect(result).toBe(false);
    });
  });
});
