/**
 * 认证工具函数
 * 提供处理未登录用户操作的通用方法
 */

import { toast } from "@/components/ui/use-toast";

/**
 * 处理需要登录的操作
 * 如果用户未登录，显示登录提示并触发登录弹窗
 * 
 * @param isLoggedIn - 用户是否已登录
 * @param callback - 用户已登录时要执行的回调函数
 * @param message - 可选的自定义提示消息
 * @returns 如果用户已登录，返回回调函数的结果；否则返回false
 */
export const handleAuthenticatedAction = (
  isLoggedIn: boolean,
  callback: () => any,
  message: string = "请先登录后再执行此操作"
): boolean | any => {
  if (!isLoggedIn) {
    // 显示登录提示
    toast({
      title: "需要登录",
      description: message,
      variant: "destructive"
    });

    // 触发登录弹窗
    const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
    window.dispatchEvent(event);

    return false;
  }

  // 用户已登录，执行回调函数
  return callback();
};

/**
 * 检查用户是否有权限执行操作
 * 如果用户未登录，显示登录提示并触发登录弹窗
 * 如果用户已登录但没有所需权限，显示权限不足提示
 * 
 * @param isLoggedIn - 用户是否已登录
 * @param hasPermission - 函数，检查用户是否有特定权限
 * @param permissionCode - 所需的权限代码
 * @param callback - 用户有权限时要执行的回调函数
 * @param loginMessage - 可选的自定义登录提示消息
 * @param permissionMessage - 可选的自定义权限不足提示消息
 * @returns 如果用户已登录且有权限，返回回调函数的结果；否则返回false
 */
export const handlePermissionAction = (
  isLoggedIn: boolean,
  hasPermission: (code: string) => boolean,
  permissionCode: string,
  callback: () => any,
  loginMessage: string = "请先登录后再执行此操作",
  permissionMessage: string = "您没有权限执行此操作"
): boolean | any => {
  // 先检查用户是否已登录
  if (!isLoggedIn) {
    // 显示登录提示
    toast({
      title: "需要登录",
      description: loginMessage,
      variant: "destructive"
    });

    // 触发登录弹窗
    const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
    window.dispatchEvent(event);

    return false;
  }

  // 检查用户是否有所需权限
  if (!hasPermission(permissionCode)) {
    // 显示权限不足提示
    toast({
      title: "权限不足",
      description: permissionMessage,
      variant: "destructive"
    });

    return false;
  }

  // 用户已登录且有权限，执行回调函数
  return callback();
};
