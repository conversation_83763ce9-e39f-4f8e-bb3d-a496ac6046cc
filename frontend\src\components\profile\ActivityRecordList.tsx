"use client"

import { RefreshCw, LogIn, Upload, MessageSquare, Download, Eye } from 'lucide-react'
import { ActivityRecordListProps, ActivityRecord } from './types'

/**
 * 活动记录列表组件
 * 
 * 用于显示用户的活动记录
 */
export function ActivityRecordList({ records, loading }: ActivityRecordListProps) {
  // 获取活动图标
  const getActivityIcon = (type: ActivityRecord['type']) => {
    switch (type) {
      case 'login':
        return <LogIn className="h-5 w-5 text-blue-500" />
      case 'file_upload':
        return <Upload className="h-5 w-5 text-green-500" />
      case 'comment':
        return <MessageSquare className="h-5 w-5 text-purple-500" />
      case 'download':
        return <Download className="h-5 w-5 text-orange-500" />
      case 'view':
        return <Eye className="h-5 w-5 text-gray-500" />
      default:
        return <Eye className="h-5 w-5 text-gray-500" />
    }
  }

  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
      </div>
    )
  }

  if (records.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center p-8 text-gray-500">
        <p>暂无活动记录</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {records.map((record) => (
        <div key={record.id} className="flex items-start p-4 border rounded-md">
          <div className="flex-shrink-0 mr-3 mt-1">
            {getActivityIcon(record.type)}
          </div>
          <div className="flex-1">
            <p className="text-sm">{record.description}</p>
            <div className="flex justify-between items-center mt-2">
              <p className="text-xs text-gray-500">{formatTime(record.created_at)}</p>
              {record.ip_address && (
                <p className="text-xs text-gray-500">IP: {record.ip_address}</p>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
