"use client"

import { useState, useEffect } from "react"
import { MemorialActivities } from "./memorial-activities"
import { useAuth } from "@/contexts/auth-context"
import { ActivityManagementButton } from "./activity-management-button"
import { logger } from "@/utils/logger"

/**
 * 客户端纪念活动组件
 *
 * 用于在客户端渲染纪念活动组件，解决服务器端渲染与客户端渲染不匹配的问题
 */
export function ClientMemorialActivities() {
  const [isManagementMode, setIsManagementMode] = useState(false)
  const { isLoggedIn, hasPermission } = useAuth()
  const [mounted, setMounted] = useState(false)

  // 检查用户是否有管理活动的权限
  const canManageActivities = isLoggedIn && (hasPermission("manage_activities") || hasPermission("activity:manage"))

  // 调试信息
  useEffect(() => {
    if (mounted) {
      logger.debug('客户端活动组件 - 权限检查:');
      logger.debug('是否登录:', isLoggedIn);
      logger.debug('是否有manage_activities权限:', hasPermission("manage_activities"));
      logger.debug('是否有activity:manage权限:', hasPermission("activity:manage"));
      logger.debug('是否可以管理活动:', canManageActivities);
      logger.debug('当前模式:', isManagementMode ? '管理模式' : '普通模式');
    }
  }, [mounted, isLoggedIn, isManagementMode]);

  // 切换管理模式
  const toggleManagementMode = () => setIsManagementMode(!isManagementMode)

  // 确保组件已挂载，避免服务器端渲染与客户端渲染不匹配
  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <MemorialActivities
      isManagementMode={isManagementMode}
      toggleManagementMode={toggleManagementMode}
      showManagementButton={mounted && canManageActivities}
    />
  )
}
