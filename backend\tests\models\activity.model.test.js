/**
 * 活动模型测试
 *
 * 测试活动模型的字段、验证和关联
 */

const { Activity, User } = require('../../src/models');

describe('活动模型', () => {
  let testUser;
  let testActivity;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试用户
    testUser = await User.create({
      username: 'activitymodeluser',
      password: 'Password123!',
      email: '<EMAIL>',
      phone: '13800000017',
      role: 'basic_user',
      is_active: true
    });

    // 创建测试活动
    testActivity = await Activity.create({
      title: 'Test Model Activity',
      date: new Date('2023-12-31'),
      description: 'Test activity for model testing',
      status: 'published',
      creator_id: testUser.id
    });
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await Activity.destroy({ where: { id: testActivity.id } });
    await User.destroy({ where: { id: testUser.id } });
  });

  // 测试活动创建
  describe('活动创建', () => {
    test('应该成功创建活动', async () => {
      const activity = await Activity.findByPk(testActivity.id);

      expect(activity).toBeDefined();
      expect(activity.title).toBe('Test Model Activity');
      expect(activity.description).toBe('Test activity for model testing');
      expect(activity.status).toBe('published');
      expect(activity.creator_id).toBe(testUser.id);

      // 验证日期
      const activityDate = new Date(activity.date);
      expect(activityDate.getFullYear()).toBe(2023);
      expect(activityDate.getMonth()).toBe(11); // 0-based, 11 = December
      expect(activityDate.getDate()).toBe(31);
    });

    test('不应该创建没有标题的活动', async () => {
      try {
        await Activity.create({
          // 缺少标题
          date: new Date('2023-12-31'),
          description: 'Activity without title',
          status: 'draft',
          creator_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建没有日期的活动', async () => {
      try {
        await Activity.create({
          title: 'Activity Without Date',
          // 缺少日期
          description: 'Activity without date',
          status: 'draft',
          creator_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建无效状态的活动', async () => {
      try {
        await Activity.create({
          title: 'Activity With Invalid Status',
          date: new Date('2023-12-31'),
          description: 'Activity with invalid status',
          status: 'invalid_status', // 无效的状态
          creator_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  // 测试活动更新
  describe('活动更新', () => {
    test('应该成功更新活动信息', async () => {
      await testActivity.update({
        title: 'Updated Activity Title',
        description: 'Updated activity description',
        status: 'draft'
      });

      const updatedActivity = await Activity.findByPk(testActivity.id);
      expect(updatedActivity.title).toBe('Updated Activity Title');
      expect(updatedActivity.description).toBe('Updated activity description');
      expect(updatedActivity.status).toBe('draft');

      // 恢复原始数据
      await testActivity.update({
        title: 'Test Model Activity',
        description: 'Test activity for model testing',
        status: 'published'
      });
    });
  });

  // 测试活动关联
  describe('活动关联', () => {
    test('活动应该关联到创建者', async () => {
      // 检查活动模型是否有创建者关联
      expect(Activity.associations).toHaveProperty('creator');

      // 获取活动的创建者
      const activity = await Activity.findByPk(testActivity.id, {
        include: ['creator']
      });

      expect(activity.creator).toBeDefined();
      expect(activity.creator.id).toBe(testUser.id);
      expect(activity.creator.username).toBe('activitymodeluser');
    });
  });

  // 测试活动实例方法
  describe('活动实例方法', () => {
    test('isPublished方法应该正确判断活动是否已发布', async () => {
      // 假设活动模型有isPublished方法
      if (typeof testActivity.isPublished === 'function') {
        // 已发布的活动
        expect(testActivity.isPublished()).toBe(true);

        // 更新为草稿状态
        await testActivity.update({ status: 'draft' });
        expect(testActivity.isPublished()).toBe(false);

        // 恢复原始状态
        await testActivity.update({ status: 'published' });
      } else {
        // 如果没有该方法，跳过测试
        console.log('活动模型没有isPublished方法，跳过测试');
      }
    });

    test('formatDate方法应该返回格式化的日期', async () => {
      // 假设活动模型有formatDate方法
      if (typeof testActivity.formatDate === 'function') {
        const formattedDate = testActivity.formatDate();
        expect(typeof formattedDate).toBe('string');
        expect(formattedDate).toContain('2023');
        expect(formattedDate).toContain('12');
        expect(formattedDate).toContain('31');
      } else {
        // 如果没有该方法，跳过测试
        console.log('活动模型没有formatDate方法，跳过测试');
      }
    });
  });

  // 测试活动类方法
  describe('活动类方法', () => {
    test('findByStatus方法应该返回指定状态的活动列表', async () => {
      // 假设活动模型有findByStatus静态方法
      if (typeof Activity.findByStatus === 'function') {
        const publishedActivities = await Activity.findByStatus('published');
        expect(Array.isArray(publishedActivities)).toBe(true);

        // 所有返回的活动都应该是published状态
        publishedActivities.forEach(activity => {
          expect(activity.status).toBe('published');
        });
      } else {
        // 如果没有该方法，跳过测试
        console.log('活动模型没有findByStatus方法，跳过测试');
      }
    });

    test('findUpcoming方法应该返回即将到来的活动列表', async () => {
      // 假设活动模型有findUpcoming静态方法
      if (typeof Activity.findUpcoming === 'function') {
        // 创建一个未来的活动
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 30); // 30天后

        const upcomingActivity = await Activity.create({
          title: 'Upcoming Activity',
          date: futureDate,
          description: 'This activity is coming soon',
          status: 'published',
          creator_id: testUser.id
        });

        const upcomingActivities = await Activity.findUpcoming();
        expect(Array.isArray(upcomingActivities)).toBe(true);

        // 验证返回的活动中包含我们创建的即将到来的活动
        const found = upcomingActivities.some(activity => activity.id === upcomingActivity.id);
        expect(found).toBe(true);

        // 清理测试数据
        await upcomingActivity.destroy();
      } else {
        // 如果没有该方法，跳过测试
        console.log('活动模型没有findUpcoming方法，跳过测试');
      }
    });
  });
});
