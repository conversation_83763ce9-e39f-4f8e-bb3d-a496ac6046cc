const { Comment, User, Notification, Role } = require('../models');

/**
 * 获取主题评论列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCommentsByTopic = async (req, res) => {
  try {
    const { topicType, topicId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;

    // 构建查询条件
    const where = {
      topic_type: topicType,
      topic_id: topicId,
      status: 'approved' // 只返回已批准的评论
    };

    // 查询评论
    const { count, rows: comments } = await Comment.findAndCountAll({
      where,
      include: [{ model: User, as: 'user', attributes: ['id', 'username', 'avatar'] }],
      order: [['created_at', 'DESC']],
      limit: pageSize,
      offset: (page - 1) * pageSize
    });

    res.status(200).json({
      message: '获取主题评论列表成功',
      comments,
      pagination: {
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize)
      }
    });
  } catch (error) {
    console.error('获取主题评论列表失败:', error);
    res.status(500).json({ message: '服务器错误，获取主题评论列表失败' });
  }
};

/**
 * 获取评论列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getComments = async (req, res) => {
  try {
    const { topic_id, topic_type, status } = req.query;
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;

    // 构建查询条件
    const where = {};
    if (topic_id) where.topic_id = topic_id;
    if (topic_type) where.topic_type = topic_type;
    if (status) where.status = status;

    // 查询评论
    const { count, rows: comments } = await Comment.findAndCountAll({
      where,
      include: [{ model: User, as: 'user', attributes: ['id', 'username', 'avatar'] }],
      order: [['created_at', 'DESC']],
      limit: pageSize,
      offset: (page - 1) * pageSize
    });

    res.status(200).json({
      message: '获取评论列表成功',
      comments,
      pagination: {
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize)
      }
    });
  } catch (error) {
    console.error('获取评论列表失败:', error);
    res.status(500).json({ message: '服务器错误，获取评论列表失败' });
  }
};

/**
 * 创建评论
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createComment = async (req, res) => {
  try {
    console.log('创建评论请求体:', req.body);
    console.log('当前用户:', req.user);

    const { topic_id, topic_type, topic_title, content } = req.body;

    // 验证必要字段
    if (!topic_id) {
      console.error('缺少必要字段: topic_id');
      return res.status(400).json({ message: '缺少必要字段: topic_id' });
    }

    if (!topic_type) {
      console.error('缺少必要字段: topic_type');
      return res.status(400).json({ message: '缺少必要字段: topic_type' });
    }

    if (!content) {
      console.error('缺少必要字段: content');
      return res.status(400).json({ message: '缺少必要字段: content' });
    }

    // 确保topic_id是数字类型
    let parsedTopicId = topic_id;
    if (typeof topic_id === 'string') {
      parsedTopicId = parseInt(topic_id);
      if (isNaN(parsedTopicId)) {
        console.error(`无法将topic_id "${topic_id}" 转换为数字`);
        return res.status(400).json({ message: '主题ID必须是数字' });
      }
    }

    console.log('处理后的topic_id:', parsedTopicId);

    // 创建评论
    const newComment = await Comment.create({
      topic_id: parsedTopicId,
      topic_type,
      topic_title: topic_title || '未知主题',
      content,
      user_id: req.user.id,
      status: 'pending', // 默认状态为待审核
      created_at: new Date()
    });

    console.log('评论创建成功:', newComment.toJSON());

    // 获取管理员用户列表，发送通知
    const admins = await User.findAll({
      include: [{
        model: Role,
        as: 'userRole', // 使用正确的关联别名
        where: { name: '管理员' }
      }]
    });

    console.log(`找到 ${admins.length} 个管理员用户`);

    // 向管理员发送通知
    for (const admin of admins) {
      await Notification.create({
        user_id: admin.id,
        type: 'comment',
        title: '新评论待审核',
        content: `用户 ${req.user.username} 在"${topic_title || '未知主题'}"下发表了评论，请审核。`,
        related_id: newComment.id,
        related_type: 'comment',
        is_read: false,
        created_at: new Date()
      });
    }

    console.log('通知创建成功');

    res.status(201).json({
      message: '评论发表成功，等待审核',
      comment: {
        id: newComment.id,
        content: newComment.content,
        status: newComment.status,
        created_at: newComment.created_at,
        user: {
          id: req.user.id,
          username: req.user.username
        }
      }
    });
  } catch (error) {
    console.error('添加评论失败:', error);
    console.error('错误堆栈:', error.stack);

    // 检查是否是Sequelize验证错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: '数据验证失败',
        errors: error.errors.map(e => e.message)
      });
    }

    // 检查是否是外键约束错误
    if (error.name === 'SequelizeForeignKeyConstraintError') {
      return res.status(400).json({
        message: '外键约束错误，请检查关联ID是否存在',
        field: error.fields
      });
    }

    res.status(500).json({
      message: '服务器错误，添加评论失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 审核评论
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reason } = req.body;

    // 获取评论
    const comment = await Comment.findByPk(id, {
      include: [{ model: User, as: 'user' }]
    });

    if (!comment) {
      return res.status(404).json({ message: '评论不存在' });
    }

    // 更新评论状态
    await comment.update({
      status,
      review_reason: reason || null,
      reviewed_at: new Date(),
      reviewer_id: req.user.id
    });

    // 如果评论被批准，向评论发布者发送通知
    if (status === 'approved') {
      await Notification.create({
        user_id: comment.user_id,
        type: 'comment',
        title: '评论已批准',
        content: `您在"${comment.topic_title}"下的评论已被批准`,
        related_id: comment.id,
        related_type: 'comment',
        is_read: false,
        created_at: new Date()
      });
    }
    // 如果评论被拒绝，也向评论发布者发送通知
    else if (status === 'rejected') {
      await Notification.create({
        user_id: comment.user_id,
        type: 'comment',
        title: '评论未通过审核',
        content: `您在"${comment.topic_title}"下的评论未通过审核${reason ? '，原因：' + reason : ''}`,
        related_id: comment.id,
        related_type: 'comment',
        is_read: false,
        created_at: new Date()
      });
    }

    res.status(200).json({
      message: '评论审核成功',
      comment: {
        id: comment.id,
        status: comment.status,
        reviewed_at: comment.reviewed_at
      }
    });
  } catch (error) {
    console.error('评论审核失败:', error);
    res.status(500).json({ message: '服务器错误，评论审核失败' });
  }
};

/**
 * 删除评论
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteComment = async (req, res) => {
  try {
    const { id } = req.params;

    // 获取评论
    const comment = await Comment.findByPk(id);

    if (!comment) {
      return res.status(404).json({ message: '评论不存在' });
    }

    // 检查权限
    if (comment.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: '没有权限删除此评论' });
    }

    // 删除评论
    await comment.destroy();

    res.status(200).json({
      message: '评论删除成功'
    });
  } catch (error) {
    console.error('删除评论失败:', error);
    res.status(500).json({ message: '服务器错误，删除评论失败' });
  }
};
