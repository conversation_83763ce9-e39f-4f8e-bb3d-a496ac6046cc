"use client"

import { Download, FileText, Calendar, User, Database, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { KnowledgeFileDetailProps } from './types'

/**
 * 知识库文件详情组件
 * 
 * 用于显示知识库文件的详细信息
 */
export function KnowledgeFileDetail({ file, onClose, onDownload }: KnowledgeFileDetailProps) {
  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 获取文件图标
  const getFileIcon = (fileType: string) => {
    return <FileText className="h-6 w-6 text-blue-500" />
  }

  // 获取状态标签样式
  const getStatusTagStyle = (status: string) => {
    switch (status) {
      case '待审核':
        return 'bg-yellow-100 text-yellow-800'
      case '已通过':
      case '正常':
        return 'bg-green-100 text-green-800'
      case '已驳回':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          {getFileIcon(file.file_type)}
          <div>
            <h3 className="text-lg font-semibold">{file.name}</h3>
            <p className="text-sm text-gray-500">{file.original_name}</p>
          </div>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-1">
          <p className="text-sm text-gray-500">文件类型</p>
          <p className="text-sm">{file.file_type}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm text-gray-500">文件大小</p>
          <p className="text-sm">{formatFileSize(file.file_size)}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm text-gray-500">所属知识库</p>
          <div className="flex items-center gap-1">
            <Database className="h-4 w-4 text-gray-500" />
            <p className="text-sm">{file.knowledge_base_name}</p>
          </div>
        </div>
        <div className="space-y-1">
          <p className="text-sm text-gray-500">状态</p>
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusTagStyle(file.status)}`}
          >
            {file.status}
          </span>
        </div>
        <div className="space-y-1">
          <p className="text-sm text-gray-500">上传者</p>
          <div className="flex items-center gap-1">
            <User className="h-4 w-4 text-gray-500" />
            <p className="text-sm">{file.creator_name}</p>
          </div>
        </div>
        <div className="space-y-1">
          <p className="text-sm text-gray-500">上传时间</p>
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4 text-gray-500" />
            <p className="text-sm">{file.created_at}</p>
          </div>
        </div>
      </div>

      {(file.status === '已驳回' && file.reject_reason) && (
        <div className="space-y-1">
          <p className="text-sm text-gray-500">驳回原因</p>
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-800">{file.reject_reason}</p>
          </div>
        </div>
      )}

      <div className="space-y-2">
        <p className="text-sm font-medium">文件摘要</p>
        <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
          <p className="text-sm">{file.summary || '无摘要'}</p>
        </div>
      </div>

      <div className="space-y-2">
        <p className="text-sm font-medium">详细描述</p>
        <div className="bg-gray-50 border border-gray-200 rounded-md p-3 max-h-[300px] overflow-y-auto">
          <p className="text-sm whitespace-pre-line">{file.detailed_description || '无详细描述'}</p>
        </div>
      </div>

      {onDownload && (
        <div className="flex justify-end">
          <Button
            variant="outline"
            className="flex items-center"
            onClick={onDownload}
          >
            <Download className="h-4 w-4 mr-2" />
            下载文件
          </Button>
        </div>
      )}
    </div>
  )
}
