"use client"

import React, { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { FileList } from "@/components/file-list"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"

/**
 * 文件列表页面
 *
 * 显示所有文件和待审核文件
 */
export default function FilesPage() {
  const router = useRouter()
  const { isLoggedIn, hasPermission } = useAuth()
  const [activeTab, setActiveTab] = useState("all")

  // 检查用户是否已登录
  useEffect(() => {
    if (!isLoggedIn) {
      toast({
        title: "需要登录",
        description: "请先登录后再查看文件列表",
        variant: "destructive"
      })
      router.push("/")
    }
  }, [isLoggedIn, router])

  // 处理文件删除或审核后的刷新
  const handleFileUpdated = () => {
    // 这里可以添加刷新逻辑，如果需要的话
  }

  if (!isLoggedIn) {
    return null // 等待重定向
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-8 mt-16">
        <div className="mb-8">
          <h1 className="text-2xl font-bold">文件管理</h1>
          <p className="text-gray-500 mt-1">查看和管理所有文件</p>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="all">所有文件</TabsTrigger>
            {hasPermission("file:review") && (
              <TabsTrigger value="pending">待审核文件</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="all">
            <FileList
              showActions={true}
              showReview={false}
              onFileDeleted={handleFileUpdated}
              status={undefined} // 不过滤状态，显示所有文件
            />
          </TabsContent>

          {hasPermission("file:review") && (
            <TabsContent value="pending">
              <FileList
                showActions={true}
                showReview={true}
                onFileDeleted={handleFileUpdated}
                onFileReviewed={handleFileUpdated}
                status="pending" // 明确指定只显示待审核文件
              />
            </TabsContent>
          )}
        </Tabs>
      </main>

      <Footer />
    </div>
  )
}
