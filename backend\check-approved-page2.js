/**
 * 检查已批准文件第二页脚本
 * 
 * 用于查询已批准文件的第二页数据
 */

const { sequelize, File, KnowledgeBase, User } = require('./src/models');

async function checkApprovedPage2() {
  try {
    console.log('开始检查已批准文件第二页...');
    console.log('='.repeat(80));
    
    // 模拟API请求参数
    const page = 2;
    const limit = 10;
    const offset = (page - 1) * limit;
    
    console.log(`模拟请求参数: page=${page}, limit=${limit}, offset=${offset}`);
    console.log('='.repeat(80));
    
    // 构建查询条件 - 只查询已批准的文件
    const whereConditions = { status: 'approved' };
    
    console.log('查询条件:', JSON.stringify(whereConditions));
    
    // 执行查询
    const { count, rows: files } = await File.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name', 'type']
        },
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'email']
        }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });
    
    // 计算总页数
    const totalPages = Math.ceil(count / limit);
    
    console.log(`查询结果: 总数=${count}, 当前页数据=${files.length}, 总页数=${totalPages}`);
    console.log('='.repeat(80));
    
    // 输出文件列表
    console.log('已批准文件第二页列表:');
    console.log('-'.repeat(80));
    console.log('ID | 文件名 | 知识库 | 状态 | 上传者');
    console.log('-'.repeat(80));
    
    for (const file of files) {
      console.log(`${file.id} | ${file.original_name} | ${file.knowledgeBase ? file.knowledgeBase.name : '未知'} | ${file.status} | ${file.uploader ? file.uploader.username : '未知'}`);
    }
    
    console.log('='.repeat(80));
    
    // 关闭数据库连接
    await sequelize.close();
    console.log('检查完成');
  } catch (error) {
    console.error('检查已批准文件第二页失败:', error);
    process.exit(1);
  }
}

// 执行检查
checkApprovedPage2();
