/**
 * 精确调试 API 响应格式
 * 模拟前端调用 API 并查看响应格式的确切结构
 */

// 加载环境变量
require('dotenv').config();

// 导入模型和依赖
const db = require('./src/models');
const { File, User, KnowledgeBase } = db;

// 模拟 API 响应
async function debugApiResponseExact() {
  try {
    // 连接数据库
    await db.sequelize.authenticate();
    console.log('数据库连接成功');

    // 模拟 GET /files/knowledge-base/:id API 响应
    console.log('\n模拟 GET /files/knowledge-base/:id API 响应:');
    const knowledgeBaseId = 75; // 使用已知的知识库 ID
    const knowledgeBase = await KnowledgeBase.findByPk(knowledgeBaseId);
    
    if (knowledgeBase) {
      const knowledgeBaseFiles = await File.findAll({
        where: { knowledge_base_id: knowledgeBaseId },
        include: [
          {
            model: User,
            as: 'uploader',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      // 转换为 JSON 并打印
      const knowledgeBaseFilesJson = knowledgeBaseFiles.map(file => file.toJSON());
      
      // 创建与控制器返回格式完全相同的响应
      const response = {
        success: true,
        data: {
          knowledgeBase: {
            id: knowledgeBase.id,
            name: knowledgeBase.name,
            type: knowledgeBase.type
          },
          files: knowledgeBaseFilesJson,
          pagination: {
            total: knowledgeBaseFilesJson.length,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        }
      };

      // 打印完整响应
      console.log('完整API响应:', JSON.stringify(response, null, 2));
      
      // 检查上传者信息的确切结构
      console.log('\n检查上传者信息的确切结构:');
      knowledgeBaseFilesJson.forEach((file, index) => {
        console.log(`\n文件 ${index + 1} (ID: ${file.id}):`, file.original_name);
        console.log('  上传者信息类型:', typeof file.uploader);
        console.log('  上传者信息是否为null:', file.uploader === null);
        console.log('  上传者信息是否为undefined:', file.uploader === undefined);
        
        if (file.uploader) {
          console.log('  上传者信息的键:', Object.keys(file.uploader));
          console.log('  上传者ID:', file.uploader.id);
          console.log('  上传者用户名:', file.uploader.username);
          console.log('  上传者邮箱:', file.uploader.email);
          
          // 检查是否有其他隐藏属性
          console.log('  上传者信息的完整JSON:', JSON.stringify(file.uploader));
        }
        
        // 检查文件对象的所有键
        console.log('  文件对象的所有键:', Object.keys(file));
        
        // 检查uploader_id字段
        console.log('  uploader_id:', file.uploader_id);
      });
    } else {
      console.log(`未找到 ID 为 ${knowledgeBaseId} 的知识库`);
    }

    // 关闭数据库连接
    await db.sequelize.close();
  } catch (error) {
    console.error('调试失败:', error);
  }
}

// 执行调试
debugApiResponseExact();
