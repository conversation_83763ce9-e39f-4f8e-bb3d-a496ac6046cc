/**
 * 默认角色种子数据
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('roles', [
      {
        name: '管理员',
        description: '系统管理员，拥有所有权限',
        is_system: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '访问者',
        description: '新注册用户默认角色，拥有基本访问权限',
        is_system: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('roles', {
      name: {
        [Sequelize.Op.in]: ['管理员', '访问者']
      }
    }, {});
  }
};
