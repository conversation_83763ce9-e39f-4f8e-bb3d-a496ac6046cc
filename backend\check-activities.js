// 查询最近的活动记录
require('dotenv').config();
const { sequelize, Activity, User } = require('./src/models');

async function checkActivities() {
  try {
    console.log('尝试连接到数据库...');
    await sequelize.authenticate();
    console.log('数据库连接成功！');

    console.log('查询最近的活动记录...');
    const activities = await Activity.findAll({
      order: [['id', 'DESC']],
      limit: 10,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    console.log(`找到 ${activities.length} 条活动记录:`);
    activities.forEach(activity => {
      console.log(`ID: ${activity.id}, 标题: ${activity.title}, 状态: ${activity.status}, 创建者: ${activity.creator ? activity.creator.username : '未知'}`);
    });

    // 查询ID为43的活动（您提到的）
    console.log('\n查询ID为43的活动...');
    const activity43 = await Activity.findByPk(43, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    if (activity43) {
      console.log('找到ID为43的活动:');
      console.log(JSON.stringify(activity43.toJSON(), null, 2));
    } else {
      console.log('未找到ID为43的活动');
    }

  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await sequelize.close();
    console.log('数据库连接已关闭');
  }
}

checkActivities();
