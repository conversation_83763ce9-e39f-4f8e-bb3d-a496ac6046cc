/**
 * 性能优化工具
 * 
 * 提供性能优化相关的工具函数
 */

/**
 * 防抖函数
 * 
 * 延迟执行函数，如果在延迟时间内再次调用，则重新计时
 * 
 * @param fn 要执行的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖处理后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timer: NodeJS.Timeout | null = null
  
  return function(this: any, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer)
    
    timer = setTimeout(() => {
      fn.apply(this, args)
      timer = null
    }, delay)
  }
}

/**
 * 节流函数
 * 
 * 限制函数的执行频率，每隔一段时间执行一次
 * 
 * @param fn 要执行的函数
 * @param limit 时间间隔（毫秒）
 * @returns 节流处理后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  fn: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  let lastArgs: Parameters<T> | null = null
  let lastThis: any = null
  
  return function(this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      fn.apply(this, args)
      inThrottle = true
      
      setTimeout(() => {
        inThrottle = false
        
        if (lastArgs) {
          fn.apply(lastThis, lastArgs)
          lastArgs = null
          lastThis = null
        }
      }, limit)
    } else {
      lastArgs = args
      lastThis = this
    }
  }
}

/**
 * 缓存函数结果
 * 
 * 缓存函数的计算结果，避免重复计算
 * 
 * @param fn 要缓存的函数
 * @returns 缓存处理后的函数
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T
): (...args: Parameters<T>) => ReturnType<T> {
  const cache = new Map<string, ReturnType<T>>()
  
  return function(this: any, ...args: Parameters<T>): ReturnType<T> {
    const key = JSON.stringify(args)
    
    if (cache.has(key)) {
      return cache.get(key) as ReturnType<T>
    }
    
    const result = fn.apply(this, args)
    cache.set(key, result)
    
    return result
  }
}

/**
 * 延迟加载函数
 * 
 * 延迟执行函数，用于非关键操作
 * 
 * @param fn 要执行的函数
 * @param delay 延迟时间（毫秒）
 */
export function lazyLoad(fn: () => void, delay: number = 0): void {
  setTimeout(fn, delay)
}

/**
 * 批量处理函数
 * 
 * 将多个操作合并为一个批处理，减少重绘和回流
 * 
 * @param tasks 要执行的任务数组
 * @param batchSize 每批处理的任务数量
 * @param delay 批次之间的延迟时间（毫秒）
 */
export function batchProcess<T>(
  tasks: T[],
  processor: (item: T) => void,
  batchSize: number = 10,
  delay: number = 0
): void {
  let index = 0
  
  function processBatch() {
    const limit = Math.min(index + batchSize, tasks.length)
    
    for (let i = index; i < limit; i++) {
      processor(tasks[i])
    }
    
    index = limit
    
    if (index < tasks.length) {
      setTimeout(processBatch, delay)
    }
  }
  
  processBatch()
}

/**
 * 测量函数执行时间
 * 
 * @param fn 要测量的函数
 * @param label 日志标签
 * @returns 函数执行结果
 */
export function measureTime<T extends (...args: any[]) => any>(
  fn: T,
  label: string = 'Function execution time'
): (...args: Parameters<T>) => ReturnType<T> {
  return function(this: any, ...args: Parameters<T>): ReturnType<T> {
    const start = performance.now()
    const result = fn.apply(this, args)
    const end = performance.now()
    
    console.log(`${label}: ${end - start}ms`)
    
    return result
  }
}

/**
 * 检测浏览器空闲时执行函数
 * 
 * @param fn 要执行的函数
 * @param timeout 超时时间（毫秒）
 */
export function runWhenIdle(fn: () => void, timeout: number = 1000): void {
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => fn(), { timeout })
  } else {
    setTimeout(fn, 0)
  }
}

/**
 * 检测元素是否在视口内
 * 
 * @param element 要检测的元素
 * @param offset 视口偏移量（像素）
 * @returns 元素是否在视口内
 */
export function isInViewport(element: HTMLElement, offset: number = 0): boolean {
  const rect = element.getBoundingClientRect()
  
  return (
    rect.top >= 0 - offset &&
    rect.left >= 0 - offset &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) + offset &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth) + offset
  )
}

/**
 * 检测设备类型
 * 
 * @returns 设备类型信息
 */
export function getDeviceInfo(): {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isTouch: boolean
} {
  const width = window.innerWidth
  const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  
  return {
    isMobile: width < 768,
    isTablet: width >= 768 && width < 1024,
    isDesktop: width >= 1024,
    isTouch: hasTouch
  }
}
