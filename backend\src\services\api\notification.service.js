import axios from 'axios';
import { API_BASE_URL } from '../config';

const API_URL = `${API_BASE_URL}/api/notifications`;

/**
 * 通知服务
 */
export const NotificationService = {
  /**
   * 获取通知列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 通知列表
   */
  getNotifications: async (params = {}) => {
    const response = await axios.get(API_URL, { params });
    return response.data;
  },

  /**
   * 标记通知为已读
   * @param {number} id - 通知ID
   * @returns {Promise} 标记结果
   */
  markAsRead: async (id) => {
    const response = await axios.patch(`${API_URL}/${id}/read`);
    return response.data;
  },

  /**
   * 标记所有通知为已读
   * @returns {Promise} 标记结果
   */
  markAllAsRead: async () => {
    const response = await axios.patch(`${API_URL}/read-all`);
    return response.data;
  },

  /**
   * 删除通知
   * @param {number} id - 通知ID
   * @returns {Promise} 删除结果
   */
  deleteNotification: async (id) => {
    const response = await axios.delete(`${API_URL}/${id}`);
    return response.data;
  }
};
