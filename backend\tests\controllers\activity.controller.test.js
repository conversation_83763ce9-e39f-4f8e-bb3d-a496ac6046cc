/**
 * 活动控制器测试
 * 
 * 测试活动相关的API端点和业务逻辑
 */

const request = require('supertest');
const app = require('../../src/app');
const { User, Activity } = require('../../src/models');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

describe('活动控制器', () => {
  let testUser;
  let adminUser;
  let testActivity;
  let userToken;
  let adminToken;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试用户
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash('testpassword', salt);
    
    testUser = await User.create({
      username: 'activityuser',
      password: passwordHash,
      email: '<EMAIL>',
      phone: '13800000005',
      role: 'basic_user',
      is_active: true
    });

    // 创建管理员用户
    const adminPasswordHash = await bcrypt.hash('adminpassword', salt);
    
    adminUser = await User.create({
      username: 'activityadmin',
      password: adminPasswordHash,
      email: '<EMAIL>',
      phone: '13900000005',
      role: 'admin',
      is_active: true
    });

    // 创建测试活动
    testActivity = await Activity.create({
      title: 'Test Activity',
      date: new Date('2023-12-31'),
      description: 'Test activity description',
      status: 'published',
      creator_id: adminUser.id
    });

    // 生成测试用的JWT令牌
    userToken = jwt.sign(
      { id: testUser.id, role: testUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );

    adminToken = jwt.sign(
      { id: adminUser.id, role: adminUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await Activity.destroy({ where: { id: testActivity.id } });
    await User.destroy({ where: { id: testUser.id } });
    await User.destroy({ where: { id: adminUser.id } });
  });

  // 测试获取活动列表
  describe('获取活动列表', () => {
    test('应该返回已发布的活动列表', async () => {
      const response = await request(app)
        .get('/api/activities?status=published');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('activities');
      expect(Array.isArray(response.body.data.activities)).toBe(true);
      
      // 验证返回的活动数据
      const activity = response.body.data.activities.find(a => a.id === testActivity.id);
      expect(activity).toBeDefined();
      expect(activity).toHaveProperty('title', 'Test Activity');
      expect(activity).toHaveProperty('status', 'published');
    });

    test('管理员应该能获取所有活动', async () => {
      const response = await request(app)
        .get('/api/activities')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('activities');
      expect(Array.isArray(response.body.data.activities)).toBe(true);
    });

    test('应该根据标题搜索活动', async () => {
      const response = await request(app)
        .get('/api/activities?search=Test')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('activities');
      expect(Array.isArray(response.body.data.activities)).toBe(true);
      
      // 验证所有返回的活动标题都包含搜索关键词
      response.body.data.activities.forEach(activity => {
        expect(activity.title.toLowerCase()).toContain('test');
      });
    });
  });

  // 测试获取活动详情
  describe('获取活动详情', () => {
    test('应该返回活动详情', async () => {
      const response = await request(app)
        .get(`/api/activities/${testActivity.id}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', testActivity.id);
      expect(response.body.data).toHaveProperty('title', 'Test Activity');
      expect(response.body.data).toHaveProperty('description', 'Test activity description');
      expect(response.body.data).toHaveProperty('status', 'published');
    });

    test('应该返回404当活动不存在', async () => {
      const response = await request(app)
        .get('/api/activities/9999'); // 不存在的ID
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('不存在');
    });
  });

  // 测试创建活动
  describe('创建活动', () => {
    test('管理员应该能创建活动', async () => {
      const response = await request(app)
        .post('/api/activities')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          title: 'New Test Activity',
          date: '2024-01-15',
          description: 'New test activity description',
          status: 'draft'
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '活动创建成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('title', 'New Test Activity');
      expect(response.body.data).toHaveProperty('status', 'draft');
      
      // 清理创建的活动
      await Activity.destroy({ where: { title: 'New Test Activity' } });
    });

    test('普通用户不应该能创建活动', async () => {
      const response = await request(app)
        .post('/api/activities')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          title: 'Unauthorized Activity',
          date: '2024-01-20',
          description: 'This should not work',
          status: 'draft'
        });
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test('应该验证活动数据', async () => {
      const response = await request(app)
        .post('/api/activities')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          // 缺少标题
          date: '2024-01-25',
          description: 'Invalid activity',
          status: 'draft'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('标题不能为空');
    });
  });

  // 测试更新活动
  describe('更新活动', () => {
    test('管理员应该能更新活动', async () => {
      const response = await request(app)
        .put(`/api/activities/${testActivity.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          title: 'Updated Test Activity',
          description: 'Updated description'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '活动更新成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('title', 'Updated Test Activity');
      expect(response.body.data).toHaveProperty('description', 'Updated description');
      
      // 恢复原始数据
      await testActivity.update({
        title: 'Test Activity',
        description: 'Test activity description'
      });
    });

    test('普通用户不应该能更新活动', async () => {
      const response = await request(app)
        .put(`/api/activities/${testActivity.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          title: 'Unauthorized Update',
          description: 'This should not work'
        });
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });
  });

  // 测试删除活动
  describe('删除活动', () => {
    let tempActivity;

    beforeEach(async () => {
      // 创建临时活动用于删除测试
      tempActivity = await Activity.create({
        title: 'Temp Activity',
        date: new Date('2023-12-25'),
        description: 'Temporary activity for delete test',
        status: 'draft',
        creator_id: adminUser.id
      });
    });

    test('管理员应该能删除活动', async () => {
      const response = await request(app)
        .delete(`/api/activities/${tempActivity.id}`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '活动删除成功');
      
      // 验证活动已被删除
      const activity = await Activity.findByPk(tempActivity.id);
      expect(activity).toBeNull();
    });

    test('普通用户不应该能删除活动', async () => {
      // 重新创建临时活动
      tempActivity = await Activity.create({
        title: 'Temp Activity',
        date: new Date('2023-12-25'),
        description: 'Temporary activity for delete test',
        status: 'draft',
        creator_id: adminUser.id
      });

      const response = await request(app)
        .delete(`/api/activities/${tempActivity.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
      
      // 清理临时活动
      await tempActivity.destroy();
    });
  });
});
