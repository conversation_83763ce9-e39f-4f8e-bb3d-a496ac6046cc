/**
 * 评论控制器测试
 * 
 * 测试评论相关的API端点和业务逻辑
 */

const request = require('supertest');
const app = require('../../src/app');
const { User, Comment } = require('../../src/models');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

describe('评论控制器', () => {
  let testUser;
  let adminUser;
  let testComment;
  let userToken;
  let adminToken;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试用户
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash('testpassword', salt);
    
    testUser = await User.create({
      username: 'commentuser',
      password: passwordHash,
      email: '<EMAIL>',
      phone: '13800000007',
      role: 'basic_user',
      is_active: true
    });

    // 创建管理员用户
    const adminPasswordHash = await bcrypt.hash('adminpassword', salt);
    
    adminUser = await User.create({
      username: 'commentadmin',
      password: adminPasswordHash,
      email: '<EMAIL>',
      phone: '13900000007',
      role: 'admin',
      is_active: true
    });

    // 创建测试评论
    testComment = await Comment.create({
      content: '这是一条测试评论',
      topic_type: 'personal',
      topic_id: 1,
      status: 'pending',
      creator_id: testUser.id
    });

    // 生成测试用的JWT令牌
    userToken = jwt.sign(
      { id: testUser.id, role: testUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );

    adminToken = jwt.sign(
      { id: adminUser.id, role: adminUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await Comment.destroy({ where: { id: testComment.id } });
    await User.destroy({ where: { id: testUser.id } });
    await User.destroy({ where: { id: adminUser.id } });
  });

  // 测试获取主题评论列表
  describe('获取主题评论列表', () => {
    test('应该返回主题的评论列表', async () => {
      const response = await request(app)
        .get('/api/comments/topic/personal/1');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('comments');
      expect(Array.isArray(response.body.data.comments)).toBe(true);
      
      // 验证返回的评论数据
      const comment = response.body.data.comments.find(c => c.id === testComment.id);
      if (comment) { // 只有已审核的评论才会返回
        expect(comment).toHaveProperty('content', '这是一条测试评论');
        expect(comment).toHaveProperty('topic_type', 'personal');
        expect(comment).toHaveProperty('topic_id', 1);
      }
    });

    test('应该根据主题类型和ID筛选评论', async () => {
      const response = await request(app)
        .get('/api/comments/topic/personal/2'); // 不同的主题ID
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('comments');
      expect(Array.isArray(response.body.data.comments)).toBe(true);
      
      // 验证不应该返回不属于该主题的评论
      const comment = response.body.data.comments.find(c => c.id === testComment.id);
      expect(comment).toBeUndefined();
    });
  });

  // 测试获取评论列表
  describe('获取评论列表', () => {
    test('管理员应该能获取所有评论', async () => {
      const response = await request(app)
        .get('/api/comments')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('comments');
      expect(Array.isArray(response.body.data.comments)).toBe(true);
      
      // 验证返回的评论数据
      const comment = response.body.data.comments.find(c => c.id === testComment.id);
      expect(comment).toBeDefined();
      expect(comment).toHaveProperty('content', '这是一条测试评论');
      expect(comment).toHaveProperty('status', 'pending');
    });

    test('普通用户应该只能获取自己的评论', async () => {
      const response = await request(app)
        .get('/api/comments')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('comments');
      expect(Array.isArray(response.body.data.comments)).toBe(true);
      
      // 验证所有返回的评论都是用户自己的
      response.body.data.comments.forEach(comment => {
        expect(comment.creator_id).toBe(testUser.id);
      });
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/comments');
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('未授权');
    });
  });

  // 测试创建评论
  describe('创建评论', () => {
    test('用户应该能创建评论', async () => {
      const response = await request(app)
        .post('/api/comments')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: '这是一条新评论',
          topic_type: 'personal',
          topic_id: 2
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '评论创建成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('content', '这是一条新评论');
      expect(response.body.data).toHaveProperty('topic_type', 'personal');
      expect(response.body.data).toHaveProperty('topic_id', 2);
      expect(response.body.data).toHaveProperty('status', 'pending');
      
      // 清理创建的评论
      await Comment.destroy({ where: { content: '这是一条新评论' } });
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .post('/api/comments')
        .send({
          content: '未授权的评论',
          topic_type: 'personal',
          topic_id: 1
        });
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('未授权');
    });

    test('应该验证评论数据', async () => {
      const response = await request(app)
        .post('/api/comments')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          // 缺少内容
          topic_type: 'personal',
          topic_id: 1
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('内容不能为空');
    });
  });

  // 测试审核评论
  describe('审核评论', () => {
    test('管理员应该能审核评论', async () => {
      const response = await request(app)
        .put(`/api/comments/${testComment.id}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          status: 'approved',
          review_comment: '评论已审核通过'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '评论审核成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('status', 'approved');
      expect(response.body.data).toHaveProperty('review_comment', '评论已审核通过');
      
      // 恢复原始状态
      await testComment.update({
        status: 'pending',
        review_comment: null,
        reviewer_id: null
      });
    });

    test('普通用户不应该能审核评论', async () => {
      const response = await request(app)
        .put(`/api/comments/${testComment.id}/review`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          status: 'approved',
          review_comment: '尝试审核'
        });
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test('应该验证审核状态', async () => {
      const response = await request(app)
        .put(`/api/comments/${testComment.id}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          status: 'invalid_status', // 无效的状态
          review_comment: '无效状态'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('状态无效');
    });
  });

  // 测试删除评论
  describe('删除评论', () => {
    let tempComment;

    beforeEach(async () => {
      // 创建临时评论用于删除测试
      tempComment = await Comment.create({
        content: '临时评论用于删除测试',
        topic_type: 'personal',
        topic_id: 1,
        status: 'pending',
        creator_id: testUser.id
      });
    });

    test('用户应该能删除自己的评论', async () => {
      const response = await request(app)
        .delete(`/api/comments/${tempComment.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '评论删除成功');
      
      // 验证评论已被删除
      const comment = await Comment.findByPk(tempComment.id);
      expect(comment).toBeNull();
    });

    test('用户不应该能删除他人的评论', async () => {
      // 创建另一个用户的评论
      tempComment = await Comment.create({
        content: '另一个用户的评论',
        topic_type: 'personal',
        topic_id: 1,
        status: 'pending',
        creator_id: adminUser.id
      });

      const response = await request(app)
        .delete(`/api/comments/${tempComment.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
      
      // 清理临时评论
      await tempComment.destroy();
    });

    test('管理员应该能删除任何评论', async () => {
      // 创建新的临时评论
      tempComment = await Comment.create({
        content: '管理员将删除的评论',
        topic_type: 'personal',
        topic_id: 1,
        status: 'pending',
        creator_id: testUser.id
      });

      const response = await request(app)
        .delete(`/api/comments/${tempComment.id}`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '评论删除成功');
      
      // 验证评论已被删除
      const comment = await Comment.findByPk(tempComment.id);
      expect(comment).toBeNull();
    });
  });
});
