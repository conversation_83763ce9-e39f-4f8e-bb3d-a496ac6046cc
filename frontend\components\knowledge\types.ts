/**
 * 知识库类型定义
 */

// 知识库类型
export interface KnowledgeBase {
  id: string
  name: string
  creator: string
  createdAt: string
  storageSize: string
  documentsCount: number
  type: "系统" | "用户"
  description?: string
  contactPerson?: string
  contactPhone?: string
  status?: string
  updatedAt?: string
}

// 知识库文件类型
export interface KnowledgeFile {
  id: string
  name: string
  knowledge_base_id: string
  knowledge_base_name: string
  file_size: string
  file_type: string
  creator_name: string
  created_at: string
  summary?: string
  detailed_description?: string
  status?: string
  reject_reason?: string
}

// 知识库文件详情组件属性
export interface KnowledgeFileDetailProps {
  file: KnowledgeFile
  onClose: () => void
}
