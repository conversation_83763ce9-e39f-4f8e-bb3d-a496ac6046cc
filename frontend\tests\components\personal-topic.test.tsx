/**
 * 个人专题页面组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { PersonalTopic } from '@/components/personal/PersonalTopic'
import { mockFetch, resetMockFetch } from '../test-utils'

// 模拟个人专题数据
const mockTopic = {
  id: 1,
  name: '测试专题',
  description: '这是一个测试用的个人专题',
  birth_date: '1980-01-01',
  death_date: '2020-12-31',
  avatar: '/images/avatar.jpg',
  creator: {
    id: 1,
    username: 'admin'
  },
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
}

// 模拟生平轨迹数据
const mockTrajectories = [
  {
    id: 1,
    year: 1980,
    month: 1,
    day: 1,
    title: '出生',
    description: '出生于北京',
    topic_id: 1
  },
  {
    id: 2,
    year: 2000,
    month: 9,
    day: 1,
    title: '大学入学',
    description: '进入北京大学学习',
    topic_id: 1
  },
  {
    id: 3,
    year: 2020,
    month: 12,
    day: 31,
    title: '逝世',
    description: '在北京逝世',
    topic_id: 1
  }
]

// 模拟相关资料数据
const mockMaterials = [
  {
    id: 1,
    title: '测试资料1',
    description: '这是测试资料1的描述',
    file_id: 101,
    file_name: '测试文件1.pdf',
    file_type: 'pdf',
    topic_id: 1,
    created_at: '2023-01-02T00:00:00Z'
  },
  {
    id: 2,
    title: '测试资料2',
    description: '这是测试资料2的描述',
    file_id: 102,
    file_name: '测试文件2.docx',
    file_type: 'docx',
    topic_id: 1,
    created_at: '2023-01-03T00:00:00Z'
  }
]

// 模拟评论数据
const mockComments = [
  {
    id: 1,
    content: '这是一条测试评论',
    creator: {
      id: 2,
      username: 'user1'
    },
    created_at: '2023-01-04T00:00:00Z',
    status: 'approved'
  },
  {
    id: 2,
    content: '这是另一条测试评论',
    creator: {
      id: 3,
      username: 'user2'
    },
    created_at: '2023-01-05T00:00:00Z',
    status: 'approved'
  }
]

describe('PersonalTopic组件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch()
    jest.clearAllMocks()
  })

  // 测试基本渲染
  test('应该正确渲染个人专题组件', () => {
    render(
      <PersonalTopic
        topic={mockTopic}
        trajectories={mockTrajectories}
        materials={mockMaterials}
        comments={mockComments}
        isLoading={false}
        isLoggedIn={true}
        isAdmin={false}
        onCommentSubmit={jest.fn()}
        onMaterialDownload={jest.fn()}
        onAIQuery={jest.fn()}
      />
    )
    
    // 验证基本信息
    expect(screen.getByText('测试专题')).toBeInTheDocument()
    expect(screen.getByText('这是一个测试用的个人专题')).toBeInTheDocument()
    expect(screen.getByText('1980-01-01 ~ 2020-12-31')).toBeInTheDocument()
    
    // 验证生平轨迹
    expect(screen.getByText('生平轨迹')).toBeInTheDocument()
    expect(screen.getByText('出生')).toBeInTheDocument()
    expect(screen.getByText('大学入学')).toBeInTheDocument()
    expect(screen.getByText('逝世')).toBeInTheDocument()
    
    // 验证相关资料
    expect(screen.getByText('相关资料')).toBeInTheDocument()
    expect(screen.getByText('测试资料1')).toBeInTheDocument()
    expect(screen.getByText('测试资料2')).toBeInTheDocument()
    
    // 验证评论
    expect(screen.getByText('评论')).toBeInTheDocument()
    expect(screen.getByText('这是一条测试评论')).toBeInTheDocument()
    expect(screen.getByText('这是另一条测试评论')).toBeInTheDocument()
    expect(screen.getByText('user1')).toBeInTheDocument()
    expect(screen.getByText('user2')).toBeInTheDocument()
  })

  // 测试加载状态
  test('应该显示加载状态', () => {
    render(
      <PersonalTopic
        topic={null}
        trajectories={[]}
        materials={[]}
        comments={[]}
        isLoading={true}
        isLoggedIn={true}
        isAdmin={false}
        onCommentSubmit={jest.fn()}
        onMaterialDownload={jest.fn()}
        onAIQuery={jest.fn()}
      />
    )
    
    expect(screen.getByText('加载中...')).toBeInTheDocument()
  })

  // 测试评论提交
  test('应该调用评论提交函数', async () => {
    const handleCommentSubmit = jest.fn().mockResolvedValue({
      id: 3,
      content: '新评论',
      creator: {
        id: 4,
        username: 'testuser'
      },
      created_at: new Date().toISOString(),
      status: 'pending'
    })
    
    render(
      <PersonalTopic
        topic={mockTopic}
        trajectories={mockTrajectories}
        materials={mockMaterials}
        comments={mockComments}
        isLoading={false}
        isLoggedIn={true}
        isAdmin={false}
        onCommentSubmit={handleCommentSubmit}
        onMaterialDownload={jest.fn()}
        onAIQuery={jest.fn()}
      />
    )
    
    // 输入评论内容
    const commentInput = screen.getByPlaceholderText('写下你的评论...')
    fireEvent.change(commentInput, { target: { value: '新评论' } })
    
    // 点击提交按钮
    const submitButton = screen.getByRole('button', { name: '提交评论' })
    fireEvent.click(submitButton)
    
    // 验证评论提交函数被调用
    expect(handleCommentSubmit).toHaveBeenCalledWith('新评论', mockTopic.id)
    
    // 等待提交完成
    await waitFor(() => {
      expect(commentInput).toHaveValue('')
    })
  })

  // 测试未登录状态
  test('未登录时应该显示登录提示', () => {
    render(
      <PersonalTopic
        topic={mockTopic}
        trajectories={mockTrajectories}
        materials={mockMaterials}
        comments={mockComments}
        isLoading={false}
        isLoggedIn={false}
        isAdmin={false}
        onCommentSubmit={jest.fn()}
        onMaterialDownload={jest.fn()}
        onAIQuery={jest.fn()}
      />
    )
    
    expect(screen.getByText('请登录后发表评论')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument()
  })

  // 测试资料下载
  test('应该调用资料下载函数', () => {
    const handleMaterialDownload = jest.fn()
    
    render(
      <PersonalTopic
        topic={mockTopic}
        trajectories={mockTrajectories}
        materials={mockMaterials}
        comments={mockComments}
        isLoading={false}
        isLoggedIn={true}
        isAdmin={false}
        onCommentSubmit={jest.fn()}
        onMaterialDownload={handleMaterialDownload}
        onAIQuery={jest.fn()}
      />
    )
    
    // 点击下载按钮
    const downloadButtons = screen.getAllByText('下载')
    fireEvent.click(downloadButtons[0])
    
    // 验证下载函数被调用
    expect(handleMaterialDownload).toHaveBeenCalledWith(101)
  })

  // 测试AI查询
  test('应该调用AI查询函数', async () => {
    const handleAIQuery = jest.fn().mockResolvedValue({
      response: '这是AI助手的回复'
    })
    
    render(
      <PersonalTopic
        topic={mockTopic}
        trajectories={mockTrajectories}
        materials={mockMaterials}
        comments={mockComments}
        isLoading={false}
        isLoggedIn={true}
        isAdmin={false}
        onCommentSubmit={jest.fn()}
        onMaterialDownload={jest.fn()}
        onAIQuery={handleAIQuery}
      />
    )
    
    // 输入AI查询内容
    const aiInput = screen.getByPlaceholderText('向AI助手提问...')
    fireEvent.change(aiInput, { target: { value: '测试问题' } })
    
    // 点击查询按钮
    const queryButton = screen.getByRole('button', { name: '提问' })
    fireEvent.click(queryButton)
    
    // 验证AI查询函数被调用
    expect(handleAIQuery).toHaveBeenCalledWith('测试问题', mockTopic.id)
    
    // 等待AI回复显示
    await waitFor(() => {
      expect(screen.getByText('这是AI助手的回复')).toBeInTheDocument()
    })
  })

  // 测试管理员功能
  test('管理员应该看到管理按钮', () => {
    render(
      <PersonalTopic
        topic={mockTopic}
        trajectories={mockTrajectories}
        materials={mockMaterials}
        comments={mockComments}
        isLoading={false}
        isLoggedIn={true}
        isAdmin={true}
        onCommentSubmit={jest.fn()}
        onMaterialDownload={jest.fn()}
        onAIQuery={jest.fn()}
      />
    )
    
    expect(screen.getByText('编辑专题')).toBeInTheDocument()
    expect(screen.getByText('添加轨迹')).toBeInTheDocument()
    expect(screen.getByText('添加资料')).toBeInTheDocument()
  })

  // 测试空数据处理
  test('应该处理空数据', () => {
    render(
      <PersonalTopic
        topic={mockTopic}
        trajectories={[]}
        materials={[]}
        comments={[]}
        isLoading={false}
        isLoggedIn={true}
        isAdmin={false}
        onCommentSubmit={jest.fn()}
        onMaterialDownload={jest.fn()}
        onAIQuery={jest.fn()}
      />
    )
    
    expect(screen.getByText('暂无生平轨迹')).toBeInTheDocument()
    expect(screen.getByText('暂无相关资料')).toBeInTheDocument()
    expect(screen.getByText('暂无评论')).toBeInTheDocument()
  })
});
