"use client"

import { useState, useEffect } from 'react'
import { Search, Edit, AlertCircle, CheckCircle, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AIAgent, AIAgentType } from '@/types/ai-assistants'
import aiAssistantService from '@/services/ai-assistant-service'

/**
 * AI助手管理组件
 *
 * 用于系统管理页面中管理AI助手
 */
export function AIAssistantManager() {
  const [aiAgents, setAiAgents] = useState<AIAgent[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const [showEditModal, setShowEditModal] = useState(false)
  const [currentAgent, setCurrentAgent] = useState<AIAgent | null>(null)

  // 获取AI助手列表
  const fetchAIAssistants = async () => {
    setIsLoading(true)
    setError('')

    try {
      // 添加时间戳参数，避免缓存
      const data = await aiAssistantService.getAIAssistants()
      console.log('获取到的AI助手列表:', data.map(a => ({
        id: a.id,
        name: a.name,
        type: a.type,
        apiEndpoint: a.apiEndpoint,
        appId: a.appId || '空',
        appCode: a.appCode || '空'
      })))
      setAiAgents(data)
    } catch (error) {
      console.error('获取AI助手列表失败:', error)
      setError('获取AI助手列表失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    fetchAIAssistants()
  }, [])

  // 过滤AI助手
  const filteredAgents = aiAgents.filter((agent) => {
    const matchesSearch =
      agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agent.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agent.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesType = typeFilter === 'all' || agent.type === typeFilter

    return matchesSearch && matchesType
  })

  // 编辑AI助手
  const handleEditAgent = (agent: AIAgent) => {
    setCurrentAgent(agent)
    setShowEditModal(true)
  }

  // 保存AI助手
  const handleSaveAgent = async () => {
    if (!currentAgent) return

    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      console.log('保存AI助手 - 发送数据:', {
        ...currentAgent,
        apiKey: currentAgent.apiKey ? '已设置' : '未设置' // 不在日志中显示完整的API密钥
      })

      await aiAssistantService.updateAIAssistant(currentAgent.id, currentAgent)

      // 强制更新列表，确保获取最新数据
      await fetchAIAssistants()

      setSuccess('AI助手更新成功')
      setShowEditModal(false)

      // 添加延迟后再次刷新，确保数据已更新
      setTimeout(async () => {
        await fetchAIAssistants()
      }, 1000)
    } catch (error) {
      console.error('更新AI助手失败:', error)
      setError('更新AI助手失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 获取助手类型显示名称
  const getTypeDisplayName = (type: AIAgentType): string => {
    switch (type) {
      case 'personal':
        return '个人专题助手'
      case 'data-query':
        return '数据查询助手'
      case 'assistant':
        return 'AI研究助手'
      case 'knowledge-file':
        return '知识库文件分析助手'
      default:
        return type
    }
  }

  return (
    <div className="space-y-4">
      {error && (
        <div className="bg-destructive/10 text-destructive p-3 rounded-md flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 shrink-0 mt-0.5" />
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="bg-green-100 text-green-800 p-3 rounded-md flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 shrink-0 mt-0.5" />
          <p>{success}</p>
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索AI助手..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-full md:w-[200px]">
            <SelectValue placeholder="所有类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有类型</SelectItem>
            <SelectItem value="personal">个人专题助手</SelectItem>
            <SelectItem value="data-query">数据查询助手</SelectItem>
            <SelectItem value="assistant">AI研究助手</SelectItem>
            <SelectItem value="knowledge-file">知识库文件同步配置</SelectItem>
            <SelectItem value="system-knowledge-file">系统知识库文件助手</SelectItem>
            <SelectItem value="user-knowledge-file">用户知识库文件助手</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredAgents.map((agent) => (
          <Card key={agent.id} className="overflow-hidden">
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-lg flex justify-between items-center">
                <span>{agent.name}</span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleEditAgent(agent)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
              </CardTitle>
              <div className="text-sm text-muted-foreground">
                {getTypeDisplayName(agent.type as AIAgentType)}
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-2 space-y-2">
              <p className="text-sm line-clamp-2">{agent.description}</p>

              <div className="flex flex-wrap gap-1">
                {agent.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              <div className="text-xs text-muted-foreground">
                最后更新: {agent.lastUpdated}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 编辑AI助手对话框 */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑AI助手</DialogTitle>
          </DialogHeader>

          {currentAgent && (
            <div className="space-y-4 max-h-[70vh] overflow-y-auto">
              <div className="space-y-2">
                <Label htmlFor="name">名称</Label>
                <Input
                  id="name"
                  value={currentAgent.name}
                  onChange={(e) => setCurrentAgent({ ...currentAgent, name: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  value={currentAgent.description}
                  onChange={(e) => setCurrentAgent({ ...currentAgent, description: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="api-key">API密钥</Label>
                <Input
                  id="api-key"
                  value={currentAgent.apiKey}
                  onChange={(e) => setCurrentAgent({ ...currentAgent, apiKey: e.target.value })}
                  type="password"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="api-endpoint">API端点</Label>
                <Input
                  id="api-endpoint"
                  value={currentAgent.apiEndpoint}
                  onChange={(e) => setCurrentAgent({ ...currentAgent, apiEndpoint: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                {currentAgent.type === 'knowledge-file' || currentAgent.type === 'system-knowledge-file' || currentAgent.type === 'user-knowledge-file' ? (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="app-id" className="flex items-center">
                        <span>系统知识库数据集ID</span>
                        <span className="ml-2 text-xs text-blue-500">(存储在appId字段)</span>
                      </Label>
                      <Input
                        id="app-id"
                        value={currentAgent.appId || ''}
                        onChange={(e) => setCurrentAgent({ ...currentAgent, appId: e.target.value })}
                      />
                      <div className="text-xs text-muted-foreground">
                        <p>用于<strong>系统知识库</strong>文件上传的AI平台数据集ID</p>
                        <p>默认值：77199451-730a-4d79-a1c9-9b9e6bfcd747</p>
                        <p className="mt-1 text-red-500 font-medium">重要提示：系统知识库和用户知识库必须使用不同的数据集ID</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="app-code" className="flex items-center">
                        <span>用户知识库数据集ID</span>
                        <span className="ml-2 text-xs text-blue-500">(存储在appCode字段)</span>
                      </Label>
                      <Input
                        id="app-code"
                        value={currentAgent.appCode || ''}
                        onChange={(e) => setCurrentAgent({ ...currentAgent, appCode: e.target.value })}
                      />
                      <div className="text-xs text-muted-foreground">
                        <p>用于<strong>用户知识库</strong>文件上传的AI平台数据集ID</p>
                        <p>默认值：602d59cf-3384-4105-bf91-e1481b30b6b2</p>
                        <p className="mt-1 text-red-500 font-medium">请确保此ID与系统知识库数据集ID不同，否则文件会上传到同一个知识库</p>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="app-id">应用ID</Label>
                      <Input
                        id="app-id"
                        value={currentAgent.appId || ''}
                        onChange={(e) => setCurrentAgent({ ...currentAgent, appId: e.target.value })}
                      />
                      <p className="text-xs text-muted-foreground">AI平台应用的唯一标识符</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="app-code">应用代码</Label>
                      <Input
                        id="app-code"
                        value={currentAgent.appCode || ''}
                        onChange={(e) => setCurrentAgent({ ...currentAgent, appCode: e.target.value })}
                      />
                      <p className="text-xs text-muted-foreground">AI平台应用的代码</p>
                    </div>
                  </>
                )}
              </div>

              {(currentAgent.type === 'knowledge-file' || currentAgent.type === 'system-knowledge-file' || currentAgent.type === 'user-knowledge-file') && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="upload-api-path">上传API路径</Label>
                    <Input
                      id="upload-api-path"
                      value={currentAgent.uploadApiPath || ''}
                      onChange={(e) => setCurrentAgent({ ...currentAgent, uploadApiPath: e.target.value })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="analysis-api-path">分析API路径 (已弃用)</Label>
                    <Input
                      id="analysis-api-path"
                      value={currentAgent.analysisApiPath || ''}
                      onChange={(e) => setCurrentAgent({ ...currentAgent, analysisApiPath: e.target.value })}
                    />
                    <p className="text-xs text-muted-foreground">此字段已弃用，系统不再使用分析功能</p>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="tags">标签 (逗号分隔)</Label>
                <Input
                  id="tags"
                  value={currentAgent.tags.join(', ')}
                  onChange={(e) => setCurrentAgent({
                    ...currentAgent,
                    tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                  })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">状态</Label>
                <Select
                  value={currentAgent.status}
                  onValueChange={(value) => setCurrentAgent({ ...currentAgent, status: value })}
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="正常">正常</SelectItem>
                    <SelectItem value="禁用">禁用</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              取消
            </Button>
            <Button onClick={handleSaveAgent} disabled={isLoading}>
              {isLoading ? '保存中...' : '保存'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
