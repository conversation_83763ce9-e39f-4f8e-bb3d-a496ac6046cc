import type { ReactNode } from "react"

/**
 * 基础AI助手接口
 * 所有类型的AI助手都继承自这个接口
 */
export interface BaseAssistant {
  id: string
  name: string
  description: string
  icon?: ReactNode | string // 可以是 ReactNode 或字符串
  tags: string[]
  isNew?: boolean
  isPopular?: boolean
  createdAt?: string
  updatedAt?: string
  enabled?: boolean
}

/**
 * 研究助手接口
 * 用于AI研究助手页面
 */
export interface ResearchAssistant extends BaseAssistant {
  category: "research" | "data" | "document" | "personal"
  type?: "assistant" // 在系统管理中，type="assistant"的助手会显示在AI研究助手页面
}

/**
 * 个人专题助手接口
 * 用于个人专题页面
 */
export interface PersonalAssistantConfig extends BaseAssistant {
  personalId: string // 关联的个人ID
  type?: "personal" // 在系统管理中，type="personal"的助手会显示在个人专题页面
  apiKey?: string // API密钥
  apiEndpoint?: string // API端点
  appId?: string // Dify应用ID
  appCode?: string // Dify应用代码
}

/**
 * 数据查询助手接口
 * 用于数据查询页面
 */
export interface DataQueryAssistantConfig extends BaseAssistant {
  dataSourceId: string // 关联的数据源ID
  type?: "data-query" // 在系统管理中，type="data-query"的助手会显示在数据查询页面
}

/**
 * 知识库文件分析助手接口
 * 用于知识库文件上传分析
 */
export interface KnowledgeFileAssistantConfig extends BaseAssistant {
  type?: "knowledge-file" // 在系统管理中，type="knowledge-file"的助手用于知识库文件分析
  uploadApiPath?: string // 上传接口路径
  analysisApiPath?: string // 分析接口路径
}

/**
 * AI代理接口
 * 用于系统管理页面
 */
export interface AIAgent extends BaseAssistant {
  type?: "assistant" | "personal" | "data-query" | "knowledge-file" | "system-knowledge-file" | "user-knowledge-file"
  apiKey: string
  apiEndpoint: string
  appId?: string // 系统知识库数据集ID（知识库文件分析助手）或Dify应用ID（其他助手）
  appCode?: string // 用户知识库数据集ID（知识库文件分析助手）或Dify应用代码（其他助手）
  initialMessage?: string // 初始对话内容
  uploadApiPath?: string // 上传接口路径（仅知识库文件分析助手使用）
  analysisApiPath?: string // 分析接口路径（仅知识库文件分析助手使用，已弃用）
  status: "正常" | "禁用"
  lastUpdated?: string // 最后更新时间
}

/**
 * AI助手消息接口
 */
export interface AssistantMessage {
  role: "user" | "assistant"
  content: string
  timestamp?: string
}

/**
 * 对话消息接口
 */
export interface ConversationMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  created_at: string
  references?: ResearchResult[]
  materials?: any[]
}

/**
 * AI查询请求接口
 */
export interface AIQueryRequest {
  query: string
  conversation_id?: string
}

/**
 * AI查询响应接口
 */
export interface AIQueryResponse {
  id: string
  answer: string
  conversation_id: string
  created_at: string
  references?: ResearchResult[]
}

/**
 * 研究结果接口
 */
export interface ResearchResult {
  id: string
  title: string
  author: string
  year: string
  source: string
  type: 'article' | 'book' | 'thesis' | 'report'
  abstract: string
  downloadUrl?: string
}

/**
 * AI助手类型映射
 * 用于在系统管理中根据类型筛选助手
 */
export const ASSISTANT_TYPE_MAP = {
  assistant: "AI研究助手",
  personal: "个人专题助手",
  "data-query": "数据查询助手",
  "knowledge-file": "知识库文件分析助手",
}
