# 和富家族研究平台部署指南

本文档提供了和富家族研究平台的详细部署步骤，包括环境配置、数据库设置、应用部署和服务器配置等内容。

## 1. 系统要求

### 1.1 硬件要求

- **CPU**: 至少2核
- **内存**: 至少4GB RAM
- **存储**: 至少20GB可用空间
- **网络**: 稳定的网络连接

### 1.2 软件要求

- **操作系统**: Linux (推荐Ubuntu 20.04+) 或 Windows Server 2016+
- **Node.js**: v18.0.0 或更高版本
- **MySQL**: 8.0 或更高版本
- **Nginx**: 最新稳定版 (用于反向代理)
- **PM2**: 最新版 (用于进程管理)

## 2. 环境准备

### 2.1 安装Node.js

#### Linux (Ubuntu)

```bash
# 安装Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node -v
npm -v
```

#### Windows

1. 从[Node.js官网](https://nodejs.org/)下载并安装Node.js 18.x LTS版本
2. 打开命令提示符验证安装：

```cmd
node -v
npm -v
```

### 2.2 安装MySQL

#### Linux (Ubuntu)

```bash
# 安装MySQL
sudo apt update
sudo apt install mysql-server

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 配置MySQL安全设置
sudo mysql_secure_installation
```

#### Windows

1. 从[MySQL官网](https://dev.mysql.com/downloads/installer/)下载并安装MySQL
2. 按照安装向导完成安装和初始配置

### 2.3 安装Nginx (可选，用于生产环境)

#### Linux (Ubuntu)

```bash
# 安装Nginx
sudo apt update
sudo apt install nginx

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### Windows

1. 从[Nginx官网](http://nginx.org/en/download.html)下载Windows版本
2. 解压到合适的目录
3. 运行nginx.exe启动服务

### 2.4 安装PM2

```bash
# 全局安装PM2
npm install -g pm2
```

## 3. 数据库配置

### 3.1 创建数据库

登录MySQL并创建数据库：

```sql
CREATE DATABASE hefamily_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'hefamily_user'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON hefamily_prod.* TO 'hefamily_user'@'localhost';
FLUSH PRIVILEGES;
```

## 4. 应用部署

### 4.1 获取代码

```bash
# 克隆代码仓库
git clone https://your-repository-url/hefamily-research-platform.git
cd hefamily-research-platform
```

### 4.2 安装依赖

```bash
# 安装项目依赖
npm run install:all
```

### 4.3 配置环境变量

#### 后端配置

复制示例环境变量文件并编辑：

```bash
cd backend
cp .env.example .env
```

编辑`.env`文件，填入实际的配置值：

```
# 服务器配置
PORT=5000
NODE_ENV=production

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=hefamily_user
DB_PASSWORD=your_strong_password
DB_NAME=hefamily_prod

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# Dify API配置
DIFY_API_KEY=your_dify_api_key
DIFY_API_ENDPOINT=https://api.dify.ai
DIFY_APP_ID=your_dify_app_id

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760 # 10MB
```

#### 前端配置

```bash
cd ../frontend
cp .env.example .env.local
```

编辑`.env.local`文件：

```
# API配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:5000/api

# 认证配置
NEXT_PUBLIC_TOKEN_KEY=hefamily_token

# 应用配置
NEXT_PUBLIC_APP_NAME=和富家族研究平台
NEXT_PUBLIC_APP_DESCRIPTION=和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。

# 文件上传配置
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif
```

### 4.4 数据库迁移

```bash
cd ../backend
npx sequelize-cli db:migrate
npx sequelize-cli db:seed:all
```

### 4.5 构建前端

```bash
cd ../frontend
npm run build
```

### 4.6 启动应用

#### 开发环境

```bash
# 在项目根目录
npm run dev
```

#### 生产环境

使用PM2启动应用：

```bash
# 启动后端
cd backend
pm2 start src/app.js --name hefamily-backend

# 启动前端
cd ../frontend
pm2 start npm --name hefamily-frontend -- start
```

## 5. Nginx配置 (生产环境)

### 5.1 创建Nginx配置文件

```bash
sudo nano /etc/nginx/sites-available/hefamily
```

添加以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 后端API
    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态文件
    location /uploads {
        alias /path/to/hefamily-research-platform/backend/uploads;
        expires 30d;
    }
}
```

### 5.2 启用配置

```bash
sudo ln -s /etc/nginx/sites-available/hefamily /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 6. SSL配置 (推荐)

### 6.1 安装Certbot

```bash
sudo apt install certbot python3-certbot-nginx
```

### 6.2 获取SSL证书

```bash
sudo certbot --nginx -d your-domain.com
```

## 7. 系统维护

### 7.1 日志管理

PM2日志查看：

```bash
# 查看后端日志
pm2 logs hefamily-backend

# 查看前端日志
pm2 logs hefamily-frontend
```

### 7.2 数据库备份

```bash
# 创建备份目录
mkdir -p /backup/hefamily

# 备份数据库
mysqldump -u hefamily_user -p hefamily_prod > /backup/hefamily/backup_$(date +%Y%m%d).sql
```

### 7.3 应用更新

```bash
# 拉取最新代码
git pull

# 安装依赖
npm run install:all

# 执行数据库迁移
cd backend
npx sequelize-cli db:migrate

# 重新构建前端
cd ../frontend
npm run build

# 重启服务
pm2 restart hefamily-backend
pm2 restart hefamily-frontend
```

## 8. 故障排除

### 8.1 常见问题

#### 数据库连接失败

- 检查数据库服务是否运行
- 验证数据库凭据是否正确
- 确认数据库用户权限是否正确

```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 测试数据库连接
mysql -u hefamily_user -p -h localhost hefamily_prod
```

#### API请求失败

- 检查后端服务是否运行
- 验证API路径是否正确
- 检查网络连接和防火墙设置

```bash
# 检查后端服务状态
pm2 status hefamily-backend

# 测试API连接
curl http://localhost:5000/api/users
```

#### 前端无法访问

- 检查前端服务是否运行
- 验证Nginx配置是否正确
- 检查浏览器控制台错误

```bash
# 检查前端服务状态
pm2 status hefamily-frontend

# 检查Nginx配置
sudo nginx -t
```

### 8.2 日志检查

```bash
# 检查Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 检查应用日志
pm2 logs
```

## 9. 安全建议

- 定期更新系统和依赖包
- 使用强密码和密钥
- 配置防火墙，只开放必要端口
- 启用HTTPS
- 定期备份数据
- 实施适当的用户权限控制

## 10. 性能优化

- 配置Nginx缓存
- 优化数据库查询
- 使用CDN加速静态资源
- 启用Gzip压缩
- 配置适当的PM2集群模式

```bash
# 使用集群模式启动后端
pm2 start src/app.js --name hefamily-backend -i max
```

## 11. 联系支持

如遇到无法解决的问题，请联系技术支持团队：

- 邮箱: <EMAIL>
- 电话: +86 123 4567 8910
