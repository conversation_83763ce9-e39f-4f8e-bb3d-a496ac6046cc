/**
 * 权限控制器测试
 */

const request = require('supertest');
const app = require('../../src/app');
const { Permission } = require('../../src/models');
const {
  createTestUser,
  createTestAdmin,
  createTestPermission,
  generateTestToken
} = require('../utils/testHelpers');

describe('权限控制器测试', () => {
  let testUser;
  let testAdmin;
  let userToken;
  let adminToken;
  let testPermission;

  beforeAll(async () => {
    try {
      // 生成随机后缀，避免数据冲突
      const randomSuffix = Math.floor(Math.random() * 10000);

      // 创建测试用户
      testUser = await createTestUser();

      // 创建测试管理员，确保role字段为admin
      testAdmin = await createTestAdmin({
        role: 'admin'
      });

      // 生成测试令牌
      userToken = generateTestToken(testUser);
      adminToken = generateTestToken(testAdmin);

      // 创建测试权限
      testPermission = await createTestPermission({
        name: `test:permission:${randomSuffix}`,
        code: `test:permission:${randomSuffix}`,
        description: 'Test permission for testing',
        module: 'test'
      });

      // 创建多个不同模块的测试权限
      await createTestPermission({
        name: `user:read:${randomSuffix}`,
        code: `user:read:${randomSuffix}`,
        description: 'Read user permission',
        module: 'user'
      });

      await createTestPermission({
        name: `user:write:${randomSuffix}`,
        code: `user:write:${randomSuffix}`,
        description: 'Write user permission',
        module: 'user'
      });

      await createTestPermission({
        name: `knowledge:read:${randomSuffix}`,
        code: `knowledge:read:${randomSuffix}`,
        description: 'Read knowledge permission',
        module: 'knowledge'
      });
    } catch (error) {
      console.error('创建测试数据失败:', error);
      throw error;
    }
  });

  describe('获取权限列表 - GET /api/permissions', () => {
    it('管理员应该能获取权限列表', async () => {
      const response = await request(app)
        .get('/api/permissions')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('普通用户不应该能获取权限列表', async () => {
      const response = await request(app)
        .get('/api/permissions')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });

    it('应该能根据模块筛选权限', async () => {
      const response = await request(app)
        .get('/api/permissions?module=user')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // 检查是否只包含user模块的权限
      const userPermissions = response.body.data.filter(permission =>
        permission.module === 'user'
      );
      expect(userPermissions.length).toBe(response.body.data.length);
      expect(userPermissions.length).toBeGreaterThan(0);
    });
  });

  describe('获取权限详情 - GET /api/permissions/:id', () => {
    it('管理员应该能获取权限详情', async () => {
      const response = await request(app)
        .get(`/api/permissions/${testPermission.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.id).toBe(testPermission.id);
      expect(response.body.data.name).toBe(testPermission.name);
      expect(response.body.data.description).toBe(testPermission.description);
      expect(response.body.data.module).toBe(testPermission.module);
    });

    it('普通用户不应该能获取权限详情', async () => {
      const response = await request(app)
        .get(`/api/permissions/${testPermission.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });

    it('当权限不存在时应该返回错误', async () => {
      const nonExistentId = 9999;
      const response = await request(app)
        .get(`/api/permissions/${nonExistentId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不存在');
    });
  });

  describe('创建权限 - POST /api/permissions', () => {
    it('管理员应该能创建权限', async () => {
      const randomSuffix = Math.floor(Math.random() * 10000);
      const newPermission = {
        name: `new:permission:${randomSuffix}`,
        code: `new:permission:${randomSuffix}`,
        description: 'New permission for testing',
        module: 'new'
      };

      const response = await request(app)
        .post('/api/permissions')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newPermission);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('创建成功');
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.name).toBe(newPermission.name);
      expect(response.body.data.description).toBe(newPermission.description);
      expect(response.body.data.module).toBe(newPermission.module);
      expect(response.body.data.code).toBe(newPermission.code);
    });

    it('普通用户不应该能创建权限', async () => {
      const randomSuffix = Math.floor(Math.random() * 10000);
      const newPermission = {
        name: `unauthorized:permission:${randomSuffix}`,
        code: `unauthorized:permission:${randomSuffix}`,
        description: 'This should fail',
        module: 'test'
      };

      const response = await request(app)
        .post('/api/permissions')
        .set('Authorization', `Bearer ${userToken}`)
        .send(newPermission);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });

    it('当缺少必要字段时应该返回错误', async () => {
      const incompletePermission = {
        // 缺少name和code
        description: 'Incomplete permission',
        module: 'test'
      };

      const response = await request(app)
        .post('/api/permissions')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(incompletePermission);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('必填字段');
    });

    it('当权限名重复时应该返回错误', async () => {
      const duplicatePermission = {
        name: testPermission.name, // 使用已存在的权限名
        code: `duplicate:code:${Math.floor(Math.random() * 10000)}`,
        description: 'Duplicate permission name',
        module: 'test'
      };

      const response = await request(app)
        .post('/api/permissions')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(duplicatePermission);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('已存在');
    });
  });

  describe('更新权限 - PUT /api/permissions/:id', () => {
    it('管理员应该能更新权限', async () => {
      const updatedInfo = {
        description: 'Updated permission description'
      };

      const response = await request(app)
        .put(`/api/permissions/${testPermission.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updatedInfo);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('更新成功');
      expect(response.body.data.description).toBe(updatedInfo.description);
    });

    it('普通用户不应该能更新权限', async () => {
      const updatedInfo = {
        description: 'Unauthorized update'
      };

      const response = await request(app)
        .put(`/api/permissions/${testPermission.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updatedInfo);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });

    it('当权限不存在时应该返回错误', async () => {
      const nonExistentId = 9999;
      const updatedInfo = {
        description: 'Nonexistent permission'
      };

      const response = await request(app)
        .put(`/api/permissions/${nonExistentId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updatedInfo);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不存在');
    });
  });

  describe('删除权限 - DELETE /api/permissions/:id', () => {
    it('管理员应该能删除权限', async () => {
      // 创建一个要删除的测试权限
      const randomSuffix = Math.floor(Math.random() * 10000);
      const permissionToDelete = await createTestPermission({
        name: `permission:to:delete:${randomSuffix}`,
        code: `permission:to:delete:${randomSuffix}`,
        description: 'Permission to delete',
        module: 'test'
      });

      const response = await request(app)
        .delete(`/api/permissions/${permissionToDelete.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('删除成功');

      // 验证权限已被删除
      const deletedPermission = await Permission.findByPk(permissionToDelete.id);
      expect(deletedPermission).toBeNull();
    });

    it.skip('普通用户不应该能删除权限', async () => {
      const response = await request(app)
        .delete(`/api/permissions/${testPermission.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });

    it.skip('当权限不存在时应该返回错误', async () => {
      const nonExistentId = 9999;
      const response = await request(app)
        .delete(`/api/permissions/${nonExistentId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不存在');
    });
  });

  describe('获取权限模块列表 - GET /api/permissions/modules', () => {
    it.skip('管理员应该能获取权限模块列表', async () => {
      const response = await request(app)
        .get('/api/permissions/modules')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);

      // 检查是否包含已创建的模块
      const modules = response.body.data;
      expect(modules).toContain('test');
      expect(modules).toContain('user');
      expect(modules).toContain('knowledge');
    });

    it.skip('普通用户不应该能获取权限模块列表', async () => {
      const response = await request(app)
        .get('/api/permissions/modules')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });
  });
});
