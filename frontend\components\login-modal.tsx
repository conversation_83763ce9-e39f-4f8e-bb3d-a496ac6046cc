"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useRouter } from "next/navigation"
import { register } from "@/services/auth-service"
import { toast } from "@/components/ui/use-toast"
import { useAuth } from "@/contexts/auth-context"

interface LoginModalProps {
  isOpen: boolean
  onClose: () => void
  isRegister: boolean
  onLogin?: (userData: { username: string; avatar?: string }) => void
}

export function LoginModal({ isOpen, onClose, isRegister: initialIsRegister, onLogin }: LoginModalProps) {
  const [isRegisterMode, setIsRegisterMode] = useState(initialIsRegister)
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()
  const { login } = useAuth() // 从AuthContext获取login函数

  // 同步外部isRegister状态
  useEffect(() => {
    setIsRegisterMode(initialIsRegister)
  }, [initialIsRegister])

  // 添加登录处理函数
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      // 验证输入
      if (!username || !password) {
        setError("请输入用户名和密码")
        setIsLoading(false)
        return
      }

      // 开始登录流程

      // 直接使用auth-service.ts中的login函数，而不是通过context
      try {
        // 确保用户名和密码都有值
        if (!username.trim() || !password.trim()) {
          throw new Error("用户名和密码不能为空");
        }

        // 调用AuthContext中的login函数
        const loginSuccess = await login({
          username: username.trim(),
          password: password.trim()
        });

        if (!loginSuccess) {
          throw new Error("登录失败，请检查用户名和密码");
        }

        // 获取用户信息
        const userInfo = {
          username: username.trim(),
          avatar: "/vibrant-street-market.png" // 默认头像
        };

        // 登录成功
        toast({
          title: "登录成功",
          description: `欢迎回来，${userInfo.username}！`,
        });

        if (onLogin) {
          onLogin(userInfo);
        }

        onClose();

        // 触发登录成功事件
        const loginSuccessEvent = new CustomEvent("login-success");
        window.dispatchEvent(loginSuccessEvent);

        // 登录成功后刷新页面以更新状态
        router.refresh();

        // 延迟一下再刷新一次，确保状态更新
        setTimeout(() => {
          router.refresh();

          // 如果刷新不足以更新所有组件，则完全重新加载页面
          setTimeout(() => {
            window.location.reload();
          }, 100);
        }, 500);
      } catch (error: any) {
        // 提供更详细的错误信息
        let errorMessage = "登录失败，请检查用户名和密码";

        // 处理业务逻辑错误（如用户名不存在、密码错误等）
        if (error.response?.status === 400 && error.response?.data?.message) {
          errorMessage = error.response.data.message;
        }
        // 处理404错误
        else if (error.response?.status === 404) {
          errorMessage = "用户不存在";
        }
        // 处理401错误
        else if (error.response?.status === 401) {
          errorMessage = "密码错误";
        }
        // 处理网络错误
        else if (error.message === 'Network Error') {
          errorMessage = "网络错误，无法连接到服务器";
        }
        // 处理其他错误
        else if (error.message) {
          errorMessage = error.message;
        }

        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  }

  // 添加注册处理函数
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    // 获取确认密码值
    const confirmPassword = (document.getElementById("confirm-password") as HTMLInputElement)?.value

    // 验证密码长度
    if (password.length < 8) {
      setIsLoading(false)
      setError("密码长度不能少于8位")
      return
    }

    // 验证密码是否一致
    if (password !== confirmPassword) {
      setIsLoading(false)
      setError("两次输入的密码不一致")
      return
    }

    try {
      // 调用注册API
      if (username && password && email && phone) {
        // 调用注册API
        const result = await register({
          username,
          password,
          email,
          phone
        });

        // 注册成功
        toast({
          title: "注册成功",
          description: "您的账号已创建，请登录",
        });

        setError("");
        // 直接切换到登录模式，不关闭弹窗
        setIsRegisterMode(false);
        // 保留用户名，方便用户登录
      } else {
        // 输入验证失败
        setError("请填写完整的注册信息");
      }
    } catch (error: any) {
      // 提供更详细的错误信息
      let errorMessage = "注册失败，请稍后再试";

      // 处理业务逻辑错误（如用户名已存在等）
      if (error.response?.status === 400 && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      // 处理网络错误
      else if (error.message === 'Network Error') {
        errorMessage = "网络错误，无法连接到服务器";
      }
      // 处理其他错误
      else if (error.message) {
        errorMessage = error.message;
      }

      // 设置错误信息
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }

  // 重置表单状态
  useEffect(() => {
    if (!isOpen) {
      setUsername("")
      setPassword("")
      setEmail("")
      setPhone("")
      setError("")
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg w-full max-w-md p-6 relative">
        <button onClick={onClose} className="absolute top-4 right-4 text-gray-500 hover:text-gray-700">
          <X className="h-5 w-5" />
        </button>

        <h2 className="text-2xl font-bold text-center mb-4">{isRegisterMode ? "注册账号" : "用户登录"}</h2>

        <div className="text-center mb-6">
          <div className="inline-flex rounded-md shadow-sm" role="group">
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-l-lg ${
                !isRegisterMode
                  ? "bg-[#f5a623] text-white"
                  : "bg-white text-gray-700 border border-gray-200 hover:bg-gray-100"
              }`}
              onClick={() => setIsRegisterMode(false)}
            >
              登录
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-r-lg ${
                isRegisterMode
                  ? "bg-[#f5a623] text-white"
                  : "bg-white text-gray-700 border border-gray-200 hover:bg-gray-100"
              }`}
              onClick={() => setIsRegisterMode(true)}
            >
              注册
            </button>
          </div>
        </div>

        <div className="mb-4">
          <div className="text-center text-sm text-gray-500 mb-4">
            账号密码登录
          </div>

          <form onSubmit={isRegisterMode ? handleRegister : handleLogin} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="username" className="block text-sm font-medium">
                用户名
              </label>
              <Input
                id="username"
                type="text"
                placeholder="请输入用户名"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="block text-sm font-medium">
                密码
              </label>
              <Input
                id="password"
                type="password"
                placeholder="请输入密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
              {isRegisterMode && (
                <p className="text-xs text-gray-500">
                  密码长度不少于8位
                </p>
              )}
            </div>

            {error && <div className="text-red-500 text-sm">{error}</div>}

            {isRegisterMode && (
              <>
                <div className="space-y-2">
                  <label htmlFor="confirm-password" className="block text-sm font-medium">
                    确认密码
                  </label>
                  <Input id="confirm-password" type="password" placeholder="请再次输入密码" required />
                </div>
                <div className="space-y-2">
                  <label htmlFor="phone" className="block text-sm font-medium">
                    手机号
                  </label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="请输入手机号"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="email" className="block text-sm font-medium">
                    邮箱
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="请输入邮箱"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
              </>
            )}

            <Button
              type="submit"
              className="w-full bg-[#f5a623] text-white hover:bg-[#f5a623]/90"
              disabled={isLoading}
            >
              {isLoading ? (isRegisterMode ? "注册中..." : "登录中...") : isRegisterMode ? "注册" : "登录"}
            </Button>
          </form>
        </div>
      </div>
    </div>
  )
}
