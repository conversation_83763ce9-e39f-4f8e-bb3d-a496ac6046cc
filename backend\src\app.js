/**
 * 和富家族研究平台后端服务入口文件
 *
 * 该文件是后端应用的主入口点，负责设置Express服务器、
 * 连接数据库、注册中间件和路由。
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { sequelize } = require('./models');
const sessionMiddleware = require('./middlewares/sessionMiddleware');
require('dotenv').config();

// 导入路由
const userRoutes = require('./routes/userRoutes');
const knowledgeRoutes = require('./routes/knowledge.routes');
const fileRoutes = require('./routes/file.routes');
const aiAssistantRoutes = require('./routes/ai-assistant.routes');
const roleRoutes = require('./routes/role.routes');
const permissionRoutes = require('./routes/permission.routes');
const systemRoutes = require('./routes/system.routes');
const activityRoutes = require('./routes/activity.routes');
const commentRoutes = require('./routes/comment.routes');
const notificationRoutes = require('./routes/notification.routes');
const timelineEventRoutes = require('./routes/timeline-event.routes');
const uploadRoutes = require('./routes/upload.routes');
const avatarRoutes = require('./routes/avatar.routes');
const knowledgeAccessRequestRoutes = require('./routes/knowledge-access-request.routes');
const dbRoutes = require('./routes/db.routes'); // 数据库直接访问路由（仅用于开发和测试）

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 5002;

// 注册中间件
app.use(cors({
  origin: function(origin, callback) {
    // 允许的源列表
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'http://localhost:3003',
      'http://localhost:3004',
      'http://localhost:3005',
      'http://localhost:3006',
      'http://hefuf.com',
      'https://hefuf.com',
      'http://www.hefuf.com',
      'https://www.hefuf.com',
      'http://hefuf.com:80',
      'https://hefuf.com:443',
      'http://*************:99',
      'http://*************:3000',
      'http://*************',
      'https://*************:99',
      'https://*************:3000',
      'https://*************'
    ];

    // 如果环境变量中有CORS_ORIGIN，添加到允许的源列表中
    if (process.env.CORS_ORIGIN) {
      allowedOrigins.push(process.env.CORS_ORIGIN);
    }

    // 允许没有origin的请求（如移动应用或Postman）
    if (!origin) return callback(null, true);

    // 检查请求的源是否在允许的源列表中
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      // 开发环境下允许所有源
      if (process.env.NODE_ENV === 'development') {
        callback(null, true);
      } else {
        callback(new Error('不允许的跨域请求'));
      }
    }
  },
  credentials: true
}));
// 解析JSON请求体 - 增加大小限制
app.use(express.json({
  limit: '1gb', // 增加到1GB
  verify: (req, res, buf) => {
    try {
      JSON.parse(buf);
    } catch (e) {
      console.error('JSON解析错误:', e);
      res.status(400).json({
        success: false,
        message: '无效的JSON格式'
      });
      throw new Error('无效的JSON格式');
    }
  }
}));

// 解析URL编码的请求体 - 增加大小限制
app.use(express.urlencoded({
  extended: true,
  limit: '1gb' // 增加到1GB
}));

// 添加请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`\n=== 请求开始 ===`);
  console.log(`${timestamp} - ${req.method} ${req.url}`);
  console.log(`请求头:`, req.headers);

  // 特别关注知识库访问申请请求
  if (req.url.includes('knowledge-access-requests')) {
    console.log('检测到知识库访问申请请求!');
    console.log('请求方法:', req.method);
    console.log('请求URL:', req.url);
    console.log('请求头:', JSON.stringify(req.headers, null, 2));
    console.log('请求体:', req.body);

    // 检查认证信息
    if (req.headers.authorization) {
      console.log('认证头存在:', req.headers.authorization.substring(0, 20) + '...');
    } else {
      console.log('警告: 没有认证头!');
    }
  }

  if (req.method === 'POST' || req.method === 'PUT') {
    console.log('请求体:', JSON.stringify(req.body, null, 2));
  }

  if (req.query && Object.keys(req.query).length > 0) {
    console.log('查询参数:', req.query);
  }

  // 捕获响应
  const originalSend = res.send;
  res.send = function(body) {
    console.log(`\n=== 响应 ===`);
    console.log(`状态码: ${res.statusCode}`);
    try {
      const parsedBody = JSON.parse(body);
      console.log('响应体:', JSON.stringify(parsedBody, null, 2));

      // 特别关注知识库访问申请响应
      if (req.url.includes('knowledge-access-requests')) {
        console.log('知识库访问申请响应:', JSON.stringify(parsedBody, null, 2));
      }
    } catch (e) {
      console.log('响应体: [非JSON数据]');
    }
    console.log(`=== 请求结束 ===\n`);
    return originalSend.call(this, body);
  };

  next();
});

app.use(sessionMiddleware);

// 静态文件服务
// 为上传目录提供静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));
// 为public目录提供静态文件服务
app.use('/public', express.static(path.join(__dirname, '../public')));
// 为public/uploads目录提供静态文件服务
app.use('/public/uploads', express.static(path.join(__dirname, '../public/uploads')));

// 添加调试路由，用于检查文件是否存在
app.get('/check-file', (req, res) => {
  const filePath = req.query.path;
  if (!filePath) {
    return res.status(400).json({ exists: false, error: '未提供文件路径' });
  }

  const fullPath = path.join(__dirname, '..', filePath);
  const exists = fs.existsSync(fullPath);

  res.json({
    exists,
    requestedPath: filePath,
    fullPath,
    directories: {
      current: __dirname,
      parent: path.join(__dirname, '..'),
      uploads: path.join(__dirname, '../uploads'),
      publicUploads: path.join(__dirname, '../public/uploads')
    }
  });
});

// 注册路由
app.use('/api/users', userRoutes);
app.use('/api/knowledge', knowledgeRoutes);
app.use('/api/files', fileRoutes);
app.use('/api/ai', aiAssistantRoutes);
app.use('/api/roles', roleRoutes);
app.use('/api/permissions', permissionRoutes);
app.use('/api/system', systemRoutes);
app.use('/api/activities', activityRoutes);
app.use('/api/comments', commentRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/timeline-events', timelineEventRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/avatar', avatarRoutes);
app.use('/api/knowledge-access-requests', knowledgeAccessRequestRoutes);
app.use('/api/db', dbRoutes); // 数据库直接访问路由（仅用于开发和测试）

// 根路由
app.get('/', (req, res) => {
  res.send('和富家族研究平台API服务正在运行');
});

// 添加测试图片路由
app.get('/test-image', (req, res) => {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>图片测试</title>
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .image-test { margin-bottom: 20px; border: 1px solid #ccc; padding: 10px; }
        img { max-width: 200px; max-height: 200px; display: block; margin-top: 10px; }
        .success { color: green; }
        .error { color: red; }
      </style>
    </head>
    <body>
      <h1>图片路径测试</h1>

      <div class="image-test">
        <h3>测试1: /uploads/timeline-icons/icon-1746690230888-522954614.png</h3>
        <img src="/uploads/timeline-icons/icon-1746690230888-522954614.png" onerror="this.nextElementSibling.innerHTML='加载失败'; this.nextElementSibling.className='error';" onload="this.nextElementSibling.innerHTML='加载成功'; this.nextElementSibling.className='success';">
        <div>加载中...</div>
      </div>

      <div class="image-test">
        <h3>测试2: /public/uploads/timeline-icons/icon-1746690230888-522954614.png</h3>
        <img src="/public/uploads/timeline-icons/icon-1746690230888-522954614.png" onerror="this.nextElementSibling.innerHTML='加载失败'; this.nextElementSibling.className='error';" onload="this.nextElementSibling.innerHTML='加载成功'; this.nextElementSibling.className='success';">
        <div>加载中...</div>
      </div>
    </body>
    </html>
  `;

  res.send(html);
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 连接数据库并启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 检查知识库访问申请表是否存在，如果不存在则创建
    try {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS \`knowledge_base_access_requests\` (
          \`id\` int(11) NOT NULL AUTO_INCREMENT,
          \`knowledge_base_id\` int(11) NOT NULL,
          \`user_id\` int(11) NOT NULL,
          \`reason\` text,
          \`status\` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
          \`reviewed_by\` int(11) DEFAULT NULL,
          \`reviewed_at\` datetime DEFAULT NULL,
          \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (\`id\`),
          KEY \`knowledge_base_id\` (\`knowledge_base_id\`),
          KEY \`user_id\` (\`user_id\`),
          KEY \`status\` (\`status\`),
          CONSTRAINT \`knowledge_base_access_requests_ibfk_1\` FOREIGN KEY (\`knowledge_base_id\`) REFERENCES \`knowledge_bases\` (\`id\`) ON DELETE CASCADE ON UPDATE CASCADE,
          CONSTRAINT \`knowledge_base_access_requests_ibfk_2\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE ON UPDATE CASCADE,
          CONSTRAINT \`knowledge_base_access_requests_ibfk_3\` FOREIGN KEY (\`reviewed_by\`) REFERENCES \`users\` (\`id\`) ON DELETE SET NULL ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
      `);
      console.log('知识库访问申请表检查/创建完成');
    } catch (err) {
      console.error('知识库访问申请表创建失败:', err);
    }

    app.listen(PORT, () => {
      console.log(`服务器运行在端口 ${PORT}`);
      console.log('后端服务器已启动，等待请求...');
      console.log('环境变量:');
      console.log('DIFY_API_KEY:', process.env.DIFY_API_KEY ? '已设置' : '未设置');
      console.log('DIFY_API_ENDPOINT:', process.env.DIFY_API_ENDPOINT);
      console.log('DIFY_APP_ID:', process.env.DIFY_APP_ID);
      console.log('DIFY_DATASET_ID:', process.env.DIFY_DATASET_ID);

      // 设置硬编码的Dify API密钥
      process.env.DIFY_API_KEY = process.env.DIFY_API_KEY || 'dataset-DLFJlUe25VUHOwMO4HbO4hQk';
      console.log('使用的Dify API密钥:', process.env.DIFY_API_KEY);

      // 检查 exports 对象中的函数
      const fileController = require('./controllers/file.controller');
      console.log('文件控制器导出的函数:');
      console.log(Object.keys(fileController));
      console.log('uploadFileToDifyDataset 函数是否存在:', typeof fileController.uploadFileToDifyDataset === 'function');
    });
  } catch (error) {
    console.error('无法启动服务器:', error);
    process.exit(1);
  }
};

// 只在非测试环境下启动服务器
if (process.env.NODE_ENV !== 'test') {
  startServer();
}

module.exports = app; // 导出供测试使用
