"use client"

import { createContext, useContext, useState, type ReactNode } from "react"
import { X } from "lucide-react"

interface ModalContentProps {
  title?: string
  content: ReactNode
  footer?: ReactNode
  showFooter?: boolean
}

interface ModalContextType {
  isOpen: boolean
  openModal: (contentOrProps: ReactNode | ModalContentProps) => void
  closeModal: () => void
  modalContent: ReactNode | null
}

const ModalContext = createContext<ModalContextType | undefined>(undefined)

export function useModal() {
  const context = useContext(ModalContext)
  if (context === undefined) {
    throw new Error("useModal must be used within a ModalProvider")
  }
  return context
}

interface ModalProviderProps {
  children: ReactNode
}

export function ModalProvider({ children }: ModalProviderProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [modalContent, setModalContent] = useState<ReactNode | null>(null)

  const openModal = (contentOrProps: ReactNode | ModalContentProps) => {
    // 检查是否是对象形式的参数
    if (contentOrProps && typeof contentOrProps === 'object' && 'content' in contentOrProps) {
      const { title, content, footer, showFooter = true } = contentOrProps as ModalContentProps;
      setModalContent(
        <ModalContent title={title} footer={showFooter ? footer : undefined}>
          {content}
        </ModalContent>
      );
    } else {
      // 兼容旧的用法
      setModalContent(contentOrProps as ReactNode);
    }

    setIsOpen(true)
    document.body.style.overflow = "hidden"
  }

  const closeModal = () => {
    setIsOpen(false)
    document.body.style.overflow = "auto"
    // Small delay to allow animation to complete before removing content
    setTimeout(() => {
      setModalContent(null)
    }, 300)
  }

  return (
    <ModalContext.Provider value={{ isOpen, openModal, closeModal, modalContent }}>
      {children}
      {isOpen && (
        <div className="fixed inset-0 z-[99999] flex items-center justify-center overflow-hidden">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={closeModal}
            style={{ backdropFilter: "blur(2px)" }}
          ></div>
          <div
            className="bg-white rounded-lg w-full max-w-4xl p-6 relative max-h-[80vh] overflow-y-auto mx-4 z-[100000] shadow-xl"
            onClick={(e) => {
              // 阻止事件冒泡，防止点击内容区域时关闭模态框
              e.stopPropagation();
            }}
          >
            {modalContent}
          </div>
        </div>
      )}
    </ModalContext.Provider>
  )
}

interface ModalContentComponentProps {
  title?: string
  children: ReactNode
  footer?: ReactNode
}

export function ModalContent({ title, children, footer }: ModalContentComponentProps) {
  const { closeModal } = useModal()

  return (
    <>
      {title && (
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-[#1e7a43] pr-8">{title}</h2>
          <button
            onClick={(e) => {
              // 阻止事件冒泡，确保只关闭模态框而不触发其他事件
              e.stopPropagation();
              e.preventDefault();
              closeModal();
            }}
            className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
      )}
      <div>{children}</div>
      {footer && <div className="mt-6">{footer}</div>}
    </>
  )
}
