/**
 * 认证中间件测试
 */

const jwt = require('jsonwebtoken');
const authMiddleware = require('../../src/middlewares/authMiddleware');
const { checkPermission } = require('../../src/middlewares/authMiddleware');
const { User, Role, Permission, RolePermission } = require('../../src/models');
const { createTestUser, createTestAdmin, createTestRole, createTestPermission } = require('../utils/testHelpers');

describe('认证中间件测试', () => {
  let testUser;
  let testAdmin;
  let userRole;
  let adminRole;
  let testPermission;
  let req;
  let res;
  let next;

  beforeEach(() => {
    // 模拟请求、响应和next函数
    req = {
      headers: {},
      user: null
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    next = jest.fn();
  });

  beforeAll(async () => {
    // 创建测试角色，使用随机名称避免冲突
    const randomSuffix = Math.floor(Math.random() * 10000);

    userRole = await createTestRole({
      name: `user_${randomSuffix}`,
      description: 'Regular user role'
    });

    adminRole = await createTestRole({
      name: `admin_${randomSuffix}`,
      description: 'Administrator role'
    });

    // 创建测试权限
    testPermission = await createTestPermission({
      name: `test:permission:${randomSuffix}`,
      code: `test:permission:${randomSuffix}`,
      description: 'Test permission',
      module: 'test'
    });

    // 为管理员角色添加测试权限
    await adminRole.addPermission(testPermission, { transaction: global.testTransaction });

    // 创建测试用户
    testUser = await createTestUser({
      role_id: userRole.id
    });

    // 创建测试管理员
    testAdmin = await createTestAdmin({
      role_id: adminRole.id
    });
  });

  describe('authMiddleware', () => {
    it('当未提供令牌时应该返回401错误', async () => {
      await authMiddleware(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        message: expect.stringContaining('未提供认证令牌')
      }));
      expect(next).not.toHaveBeenCalled();
    });

    it('当提供无效令牌时应该返回401错误', async () => {
      req.headers.authorization = 'Bearer invalid-token';

      await authMiddleware(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        message: expect.stringContaining('无效的令牌')
      }));
      expect(next).not.toHaveBeenCalled();
    });

    it('当提供有效令牌但用户不存在时应该返回401错误', async () => {
      const token = jwt.sign(
        { id: 9999, username: 'nonexistent' },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );
      req.headers.authorization = `Bearer ${token}`;

      await authMiddleware(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        message: expect.stringContaining('用户不存在')
      }));
      expect(next).not.toHaveBeenCalled();
    });

    it('当提供有效令牌且用户存在时应该设置req.user并调用next', async () => {
      const token = jwt.sign(
        { id: testUser.id, username: testUser.username },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );
      req.headers.authorization = `Bearer ${token}`;

      await authMiddleware(req, res, next);

      expect(req.user).toBeDefined();
      expect(req.user.id).toBe(testUser.id);
      expect(req.user.username).toBe(testUser.username);
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });
  });

  describe('checkPermission', () => {
    it.skip('当用户没有所需权限时应该返回403错误', async () => {
      // 设置req.user为普通用户
      req.user = testUser;

      const permissionMiddleware = checkPermission(testPermission.code);
      await permissionMiddleware(req, res, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        message: expect.stringContaining('权限不足')
      }));
      expect(next).not.toHaveBeenCalled();
    });

    it.skip('当用户有所需权限时应该调用next', async () => {
      // 设置req.user为管理员
      req.user = testAdmin;

      const permissionMiddleware = checkPermission(testPermission.code);
      await permissionMiddleware(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });

    it.skip('当用户是管理员时应该拥有所有权限', async () => {
      // 设置req.user为管理员
      req.user = testAdmin;

      // 测试一个管理员没有显式添加的权限
      const permissionMiddleware = checkPermission('some:other:permission');
      await permissionMiddleware(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });
  });
});
