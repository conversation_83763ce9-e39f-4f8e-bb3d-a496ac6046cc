/**
 * 头像相关路由
 *
 * 处理用户头像上传和获取的请求
 */

const express = require('express');
const router = express.Router();
const { User } = require('../models');
const authMiddleware = require('../middlewares/authMiddleware');

// 上传头像
router.post('/upload', authMiddleware, async (req, res) => {
  try {
    console.log('收到头像上传请求');
    const { avatar_data } = req.body;

    if (!avatar_data) {
      console.log('未提供头像数据');
      return res.status(400).json({
        success: false,
        message: '未提供头像数据'
      });
    }

    console.log('头像数据长度:', avatar_data.length);

    // 获取当前用户
    const user = await User.findByPk(req.user.id);

    if (!user) {
      console.log('用户不存在:', req.user.id);
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    console.log('找到用户:', user.username);

    // 更新用户头像
    user.avatar = avatar_data;
    await user.save();

    console.log('头像更新成功');

    res.status(200).json({
      success: true,
      message: '头像上传成功',
      data: {
        id: user.id,
        username: user.username,
        avatar: user.avatar ? '有头像数据' : '无头像数据'
      }
    });
  } catch (error) {
    console.error('头像上传失败:', error);
    res.status(500).json({
      success: false,
      message: '头像上传失败',
      error: error.message
    });
  }
});

// 获取头像
router.get('/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    // 获取用户
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    if (!user.avatar) {
      return res.status(404).json({
        success: false,
        message: '用户没有头像'
      });
    }

    // 设置响应头
    res.setHeader('Content-Type', 'image/jpeg');
    
    // 返回头像数据
    res.send(user.avatar);
  } catch (error) {
    console.error('获取头像失败:', error);
    res.status(500).json({
      success: false,
      message: '获取头像失败',
      error: error.message
    });
  }
});

module.exports = router;
