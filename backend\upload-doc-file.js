/**
 * 将DOC文件上传到知识库
 */

const fs = require('fs');
const path = require('path');
const { Sequelize } = require('sequelize');
require('dotenv').config();

// 连接数据库
const sequelize = new Sequelize(
  process.env.DB_NAME || 'hefamily_dev',
  process.env.DB_USERNAME || 'root',
  process.env.DB_PASSWORD || 'Misaya9987.',
  {
    host: process.env.DB_HOST || 'localhost',
    dialect: 'mysql',
    logging: false
  }
);

// 定义文件模型
const File = sequelize.define('File', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: Sequelize.STRING,
  original_name: Sequelize.STRING,
  path: Sequelize.STRING,
  type: Sequelize.STRING,
  size: Sequelize.INTEGER,
  status: Sequelize.STRING,
  dify_task_id: Sequelize.STRING,
  analysis_status: Sequelize.STRING,
  analysis_error: Sequelize.TEXT,
  knowledge_base_id: Sequelize.INTEGER,
  uploader_id: Sequelize.INTEGER,
  summary: Sequelize.TEXT,
  detailed_description: Sequelize.TEXT,
  mime_type: Sequelize.STRING
}, {
  tableName: 'files',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 主函数
async function main() {
  try {
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 源文件路径和目标文件路径
    const sourceFilePath = path.join(__dirname, 'uploads', 'test-doc-file.txt');
    const targetFilePath = path.join(__dirname, 'uploads', 'test-doc-file.doc');
    
    // 检查源文件是否存在
    if (!fs.existsSync(sourceFilePath)) {
      console.error(`源文件不存在: ${sourceFilePath}`);
      process.exit(1);
    }
    
    console.log(`源文件存在: ${sourceFilePath}`);
    
    // 复制文件并重命名为.doc
    fs.copyFileSync(sourceFilePath, targetFilePath);
    console.log(`文件已复制并重命名为: ${targetFilePath}`);
    
    // 检查目标文件是否存在
    if (!fs.existsSync(targetFilePath)) {
      console.error(`目标文件不存在: ${targetFilePath}`);
      process.exit(1);
    }
    
    console.log(`目标文件存在: ${targetFilePath}`);
    
    // 获取文件大小
    const stats = fs.statSync(targetFilePath);
    const fileSize = stats.size;
    
    // 创建文件记录
    const file = await File.create({
      name: 'test-doc-file.doc',
      original_name: 'test-doc-file.doc',
      path: targetFilePath,
      type: 'doc',
      size: fileSize,
      status: 'approved',
      knowledge_base_id: 74, // 系统知识库ID
      uploader_id: 1, // 管理员ID
      summary: '测试DOC文件',
      detailed_description: '这是一个测试DOC文件，用于测试上传到Dify知识库的功能。',
      mime_type: 'application/msword'
    });
    
    console.log(`文件记录已创建: ${file.id}, ${file.original_name}`);
    
    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
    
    // 返回文件ID
    return file.id;
  } catch (error) {
    console.error('发生错误:', error);
    process.exit(1);
  }
}

// 执行主函数
main().then((fileId) => {
  console.log(`测试完成，文件ID: ${fileId}`);
  console.log('现在可以使用test-dify-upload-final.js脚本上传这个文件到Dify');
  process.exit(0);
}).catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
