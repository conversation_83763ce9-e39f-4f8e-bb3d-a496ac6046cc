"use client"

import { useState, useEffect, useRef } from 'react'
import { Send, Trash2, RefreshCw, Database } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AIQueryRequest, ConversationMessage } from '@/types/ai-assistants'
import aiAssistantService from '@/services/ai-assistant-service'
import { HtmlTypewriter } from '@/components/ui/html-typewriter'

interface KnowledgeBase {
  id: string
  name: string
  type: 'system' | 'user'
}

/**
 * 数据查询AI助手组件
 *
 * 用于在数据查询页面中提供AI查询功能
 */
export function DataQueryAssistant() {
  const [query, setQuery] = useState('')
  const [messages, setMessages] = useState<ConversationMessage[]>([])
  const [conversationId, setConversationId] = useState<string | undefined>()
  const [isLoading, setIsLoading] = useState(false)
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([
    { id: '1', name: '系统知识库', type: 'system' },
    { id: '2', name: '用户知识库1', type: 'user' },
    { id: '3', name: '用户知识库2', type: 'user' },
  ])
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<string>('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 打字机效果相关状态
  const [showTypewriter, setShowTypewriter] = useState(false)
  const [typewriterText, setTypewriterText] = useState('')
  const [lastMessageId, setLastMessageId] = useState<string | null>(null)

  // 初始化助手
  useEffect(() => {
    setMessages([
      {
        id: 'initial',
        role: 'assistant',
        content: '您好，我是数据查询助手，请选择一个知识库并输入您的查询问题。',
        created_at: new Date().toISOString()
      }
    ])

    // 这里应该从API获取知识库列表
    // 暂时使用模拟数据
  }, [])

  // 滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // 选择知识库
  const handleSelectKnowledgeBase = async (value: string) => {
    setSelectedKnowledgeBase(value)

    try {
      // 设置知识库
      await aiAssistantService.setDataAssistantKnowledgeBase({
        knowledge_base_id: value
      })

      // 添加系统消息
      const selectedKB = knowledgeBases.find(kb => kb.id === value)
      if (selectedKB) {
        const systemMessage: ConversationMessage = {
          id: `system-${Date.now()}`,
          role: 'assistant',
          content: `已选择知识库: ${selectedKB.name}。您现在可以开始查询了。`,
          created_at: new Date().toISOString()
        }
        setMessages(prev => [...prev, systemMessage])
      }
    } catch (error) {
      console.error('设置知识库失败:', error)

      // 添加错误消息
      const errorMessage: ConversationMessage = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: '设置知识库失败，请重试。',
        created_at: new Date().toISOString()
      }
      setMessages(prev => [...prev, errorMessage])
    }
  }

  // 打字机效果完成后的回调函数
  const handleTypewriterComplete = () => {
    console.log('打字机效果完成')
    setShowTypewriter(false)
    setIsLoading(false)
  }

  // 发送查询
  const handleSendQuery = async () => {
    if (!query.trim() || isLoading) return

    if (!selectedKnowledgeBase) {
      // 添加提示消息
      const promptMessage: ConversationMessage = {
        id: `prompt-${Date.now()}`,
        role: 'assistant',
        content: '请先选择一个知识库。',
        created_at: new Date().toISOString()
      }
      setMessages(prev => [...prev, promptMessage])
      return
    }

    // 重置打字机状态
    setShowTypewriter(false)
    setTypewriterText('')

    // 添加用户消息
    const userMessage: ConversationMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: query,
      created_at: new Date().toISOString()
    }
    setMessages(prev => [...prev, userMessage])

    // 清空输入框
    setQuery('')
    setIsLoading(true)

    try {
      // 准备请求数据
      const requestData: AIQueryRequest = {
        query: userMessage.content,
        conversation_id: conversationId
      }

      // 调用API
      const response = await aiAssistantService.queryDataAssistant(requestData)

      // 保存会话ID
      if (response.conversation_id) {
        setConversationId(response.conversation_id)
      }

      // 处理响应内容
      if (response.answer && response.answer.trim() !== "") {
        // 检查是否包含思考过程（details标签）
        let cleanedAnswer = response.answer;

        // 如果包含思考过程，提取出正式回复部分
        if (response.answer.includes('<details') && response.answer.includes('</details>')) {
          const detailsEndIndex = response.answer.indexOf('</details>') + 10;
          cleanedAnswer = response.answer.substring(detailsEndIndex).trim();
          console.log('提取出正式回复部分，长度:', cleanedAnswer.length);
        }

        // 移除所有HTML标签，只保留纯文本
        const textOnly = cleanedAnswer.replace(/<[^>]*>/g, '');
        console.log('移除HTML标签后的纯文本，长度:', textOnly.length);

        // 设置打字机文本内容
        setTypewriterText(textOnly)

        // 添加空的助手消息占位符
        const assistantMessage: ConversationMessage = {
          id: response.id || `assistant-${Date.now()}`,
          role: 'assistant',
          content: textOnly, // 使用纯文本内容
          created_at: response.created_at || new Date().toISOString()
        }

        // 保存最后一条消息ID，用于打字机效果
        setLastMessageId(assistantMessage.id)

        // 添加消息到列表
        setMessages(prev => [...prev, assistantMessage])

        // 显示打字机效果
        setShowTypewriter(true)
      } else {
        // 如果响应为空，添加默认消息
        const errorMessage: ConversationMessage = {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: '抱歉，我无法处理您的请求，请稍后再试。',
          created_at: new Date().toISOString()
        }
        setMessages(prev => [...prev, errorMessage])
        setIsLoading(false)
      }
    } catch (error) {
      console.error('查询失败:', error)

      // 添加错误消息
      const errorMessage: ConversationMessage = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: '抱歉，我遇到了一些问题，无法回答您的问题。请稍后再试。',
        created_at: new Date().toISOString()
      }
      setMessages(prev => [...prev, errorMessage])
      setIsLoading(false)
    }
  }

  // 清空对话
  const handleClearConversation = async () => {
    if (conversationId) {
      try {
        await aiAssistantService.clearConversationHistory('data-query', conversationId)
      } catch (error) {
        console.error('清除对话历史失败:', error)
      }
    }

    // 重置状态
    setMessages([
      {
        id: 'initial',
        role: 'assistant',
        content: '您好，我是数据查询助手，请选择一个知识库并输入您的查询问题。',
        created_at: new Date().toISOString()
      }
    ])
    setConversationId(undefined)
    // 保留已选择的知识库
  }

  return (
    <Card className="w-full h-full flex flex-col">
      <CardHeader className="px-4 py-3 border-b">
        <CardTitle className="text-lg flex justify-between items-center">
          <span>数据查询助手</span>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClearConversation}
              title="清空对话"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden p-0 flex flex-col">
        <div className="p-4 border-b">
          <Select value={selectedKnowledgeBase} onValueChange={handleSelectKnowledgeBase}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="选择知识库" />
            </SelectTrigger>
            <SelectContent>
              {knowledgeBases.map((kb) => (
                <SelectItem key={kb.id} value={kb.id}>
                  <div className="flex items-center">
                    <Database className="h-4 w-4 mr-2" />
                    <span>{kb.name}</span>
                    <span className="ml-2 text-xs text-muted-foreground">
                      ({kb.type === 'system' ? '系统' : '用户'})
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] rounded-lg px-4 py-2 ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                {showTypewriter && message.id === lastMessageId ? (
                  <HtmlTypewriter
                    html={typewriterText}
                    className="text-lg whitespace-pre-line"
                    speed={30}
                    onComplete={handleTypewriterComplete}
                  />
                ) : (
                  message.content
                )}
              </div>
            </div>
          ))}
          {isLoading && !showTypewriter && (
            <div className="flex justify-start">
              <div className="max-w-[80%] rounded-lg px-4 py-2 bg-muted flex items-center">
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                查询中...
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
        <div className="p-4 border-t">
          <div className="flex gap-2">
            <Textarea
              placeholder="请输入您的查询问题..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  handleSendQuery()
                }
              }}
              className="min-h-[60px] resize-none"
            />
            <Button
              onClick={handleSendQuery}
              disabled={!query.trim() || isLoading}
              className="shrink-0"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
