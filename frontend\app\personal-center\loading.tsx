import { Skeleton } from "@/components/ui/skeleton"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"

export default function Loading() {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Navbar />

      <main className="flex-grow pt-16">
        <div className="container mx-auto px-4 py-4">
          <Skeleton className="h-6 w-24" />
        </div>

        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* 侧边栏骨架 */}
            <div className="w-full lg:w-1/4">
              <Skeleton className="h-[400px] w-full rounded-lg" />
            </div>

            {/* 主内容区骨架 */}
            <div className="w-full lg:w-3/4">
              <Skeleton className="h-[600px] w-full rounded-lg" />
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
