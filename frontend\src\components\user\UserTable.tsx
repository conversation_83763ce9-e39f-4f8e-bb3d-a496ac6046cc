"use client"

import { useState, useEffect } from 'react'
import { User, Search, ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  UserTableProps,
  UserType,
  UserStatus,
  UserTableParams,
  UserFilter,
  UserPagination,
  UserSorter
} from './types'
import roleService, { Role } from '@/services/role-service'

/**
 * 用户表格组件
 *
 * 用于显示用户列表，支持分页、排序、筛选和批量操作
 */
export function UserTable({
  loading,
  data,
  onTableChange,
  onActionClick,
  onBatchActionClick,
  onSelectionChange
}: UserTableProps) {
  const { list, pagination } = data
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [filters, setFilters] = useState<UserFilter>({
    search: '',
    status: 'all',
    roleId: 'all'
  })
  const [roles, setRoles] = useState<Role[]>([])
  const [loadingRoles, setLoadingRoles] = useState(false)

  // 获取角色列表
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        setLoadingRoles(true)
        const roleList = await roleService.getRoles()
        setRoles(roleList)
      } catch (error) {
        console.error('获取角色列表失败:', error)
      } finally {
        setLoadingRoles(false)
      }
    }

    fetchRoles()
  }, [])

  // 处理筛选变化
  const handleFilterChange = (key: keyof UserFilter, value: string) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)

    // 重置分页到第一页
    const newParams: UserTableParams = {
      pagination: { ...pagination, current: 1 },
      filters: newFilters,
      sorter: { field: 'createdAt', order: 'descend' }
    }

    onTableChange(newParams)
  }

  // 处理分页变化
  const handlePageChange = (page: number) => {
    const newParams: UserTableParams = {
      pagination: { ...pagination, current: page },
      filters,
      sorter: { field: 'createdAt', order: 'descend' }
    }

    onTableChange(newParams)
  }

  // 处理选择变化
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = list.map(user => user.id)
      setSelectedRowKeys(allIds)
      onSelectionChange(allIds)
    } else {
      setSelectedRowKeys([])
      onSelectionChange([])
    }
  }

  // 处理单行选择
  const handleSelectRow = (userId: string, checked: boolean) => {
    let newSelectedRowKeys: string[]

    if (checked) {
      newSelectedRowKeys = [...selectedRowKeys, userId]
    } else {
      newSelectedRowKeys = selectedRowKeys.filter(key => key !== userId)
    }

    setSelectedRowKeys(newSelectedRowKeys)
    onSelectionChange(newSelectedRowKeys)
  }

  // 获取状态标签样式
  const getStatusTagStyle = (status: UserStatus) => {
    switch (status) {
      case '正常':
        return 'bg-green-100 text-green-800'
      case '待审核':
        return 'bg-yellow-100 text-yellow-800'
      case '已禁用':
        return 'bg-red-100 text-red-800'
      case '待激活':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div>
      <div className="mb-6 flex flex-col md:flex-row gap-4">
        <div className="relative md:w-64">
          <Input
            placeholder="搜索用户名/手机/邮箱"
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="pl-10"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        </div>
        <div className="flex space-x-2">
          <Select
            value={filters.status}
            onValueChange={(value) => handleFilterChange('status', value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="所有状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有状态</SelectItem>
              <SelectItem value="正常">正常</SelectItem>
              <SelectItem value="待审核">待审核</SelectItem>
              <SelectItem value="已禁用">已禁用</SelectItem>
              <SelectItem value="待激活">待激活</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex space-x-2">
          <Select
            value={filters.roleId}
            onValueChange={(value) => handleFilterChange('roleId', value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="所有角色" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有角色</SelectItem>
              {roles.map(role => (
                <SelectItem key={role.id} value={role.id.toString()}>
                  {role.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                <div className="flex items-center">
                  <Checkbox
                    checked={selectedRowKeys.length === list.length && list.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                用户信息
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                角色
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                状态
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                创建时间
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {list.map((user) => (
              <tr key={user.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Checkbox
                      checked={selectedRowKeys.includes(user.id)}
                      onCheckedChange={(checked) => handleSelectRow(user.id, !!checked)}
                    />
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                      <User className="h-5 w-5 text-gray-500" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                      <div className="text-sm text-gray-500">{user.phone}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                    {user.roleName}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusTagStyle(user.status)}`}
                  >
                    {user.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.createdAt}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      className="text-indigo-600 hover:text-indigo-900"
                      onClick={() => onActionClick('edit', user)}
                    >
                      编辑
                    </button>
                    <button
                      className="text-blue-600 hover:text-blue-900"
                      onClick={() => onActionClick('resetPassword', user)}
                    >
                      重置密码
                    </button>
                    <button
                      className="text-red-600 hover:text-red-900"
                      onClick={() => onActionClick('delete', user)}
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {list.length === 0 && !loading && (
        <div className="text-center py-8">
          <p className="text-gray-500 text-lg">暂时没有可见的用户</p>
          <p className="text-gray-400 mt-2">
            {filters.search || filters.status !== 'all' || filters.roleId !== 'all'
              ? '尝试清除筛选条件或检查您的权限'
              : '请确保您已登录并具有管理员权限'}
          </p>
        </div>
      )}

      {loading && (
        <div className="text-center py-8">
          <p className="text-gray-500">正在加载用户数据...</p>
        </div>
      )}

      <div className="mt-6 flex justify-between items-center">
        <div className="text-sm text-gray-700">
          共 <span className="font-medium">{pagination.total}</span> 条记录
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center"
            disabled={pagination.current <= 1}
            onClick={() => handlePageChange(pagination.current - 1)}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            上一页
          </Button>

          {/* 简单的分页按钮 */}
          {Array.from({ length: Math.min(5, Math.ceil(pagination.total / pagination.pageSize)) }).map((_, index) => {
            const page = index + 1
            return (
              <Button
                key={page}
                variant={pagination.current === page ? "default" : "outline"}
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => handlePageChange(page)}
              >
                {page}
              </Button>
            )
          })}

          <Button
            variant="outline"
            size="sm"
            className="flex items-center"
            disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
            onClick={() => handlePageChange(pagination.current + 1)}
          >
            下一页
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>

      <div className="mt-6 border-t pt-6">
        <h3 className="text-md font-medium mb-4">批量操作</h3>
        <div className="flex flex-wrap gap-3">
          <Button
            variant="outline"
            className="text-green-600 border-green-600 hover:bg-green-50"
            disabled={selectedRowKeys.length === 0}
            onClick={() => onBatchActionClick('enable', selectedRowKeys)}
          >
            批量启用
          </Button>
          <Button
            variant="outline"
            className="text-red-600 border-red-600 hover:bg-red-50"
            disabled={selectedRowKeys.length === 0}
            onClick={() => onBatchActionClick('disable', selectedRowKeys)}
          >
            批量禁用
          </Button>
          <Button
            variant="outline"
            className="text-blue-600 border-blue-600 hover:bg-blue-50"
            disabled={selectedRowKeys.length === 0}
            onClick={() => onBatchActionClick('changeRole', selectedRowKeys)}
          >
            批量修改角色
          </Button>
          <Button
            variant="outline"
            className="text-red-600 border-red-600 hover:bg-red-50"
            disabled={selectedRowKeys.length === 0}
            onClick={() => onBatchActionClick('delete', selectedRowKeys)}
          >
            批量删除
          </Button>
        </div>
      </div>
    </div>
  )
}
