"use client"

import { useState, useRef } from "react"
import { Upload, X, Check, RefreshCw, AlertCircle, FileText, Info } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FileAnalysisAssistant } from "@/components/ai"
import { KnowledgeBase } from "@/components/knowledge/types"
import { uploadFile } from "@/services/knowledge-file-service"
import { toast } from "@/components/ui/use-toast"
import { createKnowledgeBase } from "@/services/knowledge-service"

// 上传文件类型
interface UploadFile {
  id: string
  name: string
  size: number
  type: string
  progress: number
  status: "uploading" | "completed" | "error"
  error?: string
  file: File
}

// 组件属性
interface KnowledgeFileUploadDialogProps {
  isOpen: boolean
  onClose: () => void
  systemKnowledgeBases: KnowledgeBase[]
  userKnowledgeBases: KnowledgeBase[]
  onUploadComplete: (message: string) => void
  isAdmin: boolean
}

/**
 * 知识库文件上传对话框组件
 *
 * 用于上传文件到知识库
 */
export function KnowledgeFileUploadDialog({
  isOpen,
  onClose,
  systemKnowledgeBases,
  userKnowledgeBases,
  onUploadComplete,
  isAdmin
}: KnowledgeFileUploadDialogProps) {
  // 状态
  const [uploadToSystemKb, setUploadToSystemKb] = useState(false)
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState("")
  const [createNewKnowledgeBase, setCreateNewKnowledgeBase] = useState(false)
  const [newKnowledgeBaseName, setNewKnowledgeBaseName] = useState("")
  const [newKnowledgeBaseDescription, setNewKnowledgeBaseDescription] = useState("")
  const [contactPerson, setContactPerson] = useState("")
  const [contactPhone, setContactPhone] = useState("")
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [uploadingFiles, setUploadingFiles] = useState<UploadFile[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [overallProgress, setOverallProgress] = useState(0)
  const [uploadComplete, setUploadComplete] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  // 移除useAI相关状态，现在所有文件都会自动使用AI分析
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理文件上传
  const handleFileUpload = (files: FileList | null) => {
    if (!files || files.length === 0) return
    const fileArray = Array.from(files)
    addFilesToSelection(fileArray)
  }

  // 添加文件到选择列表
  const addFilesToSelection = (newFiles: File[]) => {
    setSelectedFiles((prevFiles) => {
      const existingFileNames = new Set(prevFiles.map((f) => f.name))
      const uniqueNewFiles = newFiles.filter((file) => !existingFileNames.has(file.name))
      return [...prevFiles, ...uniqueNewFiles]
    })
  }

  // 从选择列表中删除文件
  const removeFileFromSelection = (fileIndex: number) => {
    setSelectedFiles((prevFiles) => {
      const newFiles = [...prevFiles]
      newFiles.splice(fileIndex, 1)
      return newFiles
    })
  }

  // 处理拖拽
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(false)

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      handleFileUpload(event.dataTransfer.files)
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 开始上传过程
  const startUploadProcess = async () => {
    // 重置错误状态
    setUploadError(null)

    if (selectedFiles.length === 0) {
      setUploadError("请选择要上传的文件")
      return
    }

    // 验证知识库选择
    if (!uploadToSystemKb && !selectedKnowledgeBase && !createNewKnowledgeBase) {
      setUploadError("请选择一个知识库或创建新的知识库")
      return
    }

    if (createNewKnowledgeBase && !newKnowledgeBaseName) {
      setUploadError("请输入新知识库名称")
      return
    }

    // 开始上传
    setIsUploading(true)
    setOverallProgress(0)
    setUploadComplete(false)

    try {
      // 如果需要创建新知识库，先创建知识库
      let targetKnowledgeBaseId = selectedKnowledgeBase;

      if (createNewKnowledgeBase) {
        try {
          console.log("创建新知识库:", newKnowledgeBaseName);
          const newKB = await createKnowledgeBase({
            name: newKnowledgeBaseName,
            description: newKnowledgeBaseDescription,
            type: "user" // 用户知识库
          });

          console.log("新知识库创建成功:", newKB);
          targetKnowledgeBaseId = newKB.id;
        } catch (error) {
          console.error("创建知识库失败:", error);
          setUploadError("创建知识库失败，请稍后再试");
          setIsUploading(false);
          return;
        }
      }

      // 如果选择了系统知识库，使用选中的系统知识库ID
      if (uploadToSystemKb && selectedKnowledgeBase) {
        targetKnowledgeBaseId = selectedKnowledgeBase;
      }

      if (!targetKnowledgeBaseId) {
        setUploadError("未能确定目标知识库，请重新选择");
        setIsUploading(false);
        return;
      }

      // 准备上传文件
      const filesToUpload: UploadFile[] = selectedFiles.map((file, index) => ({
        id: `file-${index}-${file.name.replace(/\s+/g, "-")}`,
        name: file.name,
        size: file.size,
        type: file.type.split('/')[1] || file.type,
        progress: 0,
        status: "uploading",
        file
      }));

      setUploadingFiles(filesToUpload);

      // 实际上传过程
      let completedFiles = 0;
      let failedFiles = 0;
      const totalFiles = filesToUpload.length;

      // 使用Promise.all处理所有文件上传
      const uploadPromises = filesToUpload.map((fileToUpload, index) => {
        return new Promise<void>((resolve) => {
          // 调用实际的上传API，强制使用Dify分析
          uploadFile(
            {
              file: fileToUpload.file,
              knowledge_base_id: targetKnowledgeBaseId,
              onProgress: (progress) => {
                // 更新文件进度
                setUploadingFiles((prev) => {
                  const newFiles = [...prev];
                  newFiles[index].progress = progress;
                  return newFiles;
                });
              }
            },
            true // 强制使用Dify分析
          )
            .then(() => {
              // 上传成功
              setUploadingFiles((prev) => {
                const newFiles = [...prev];
                newFiles[index].status = "completed";
                newFiles[index].progress = 100;
                return newFiles;
              });

              completedFiles++;

              // 更新总体进度
              const newOverallProgress = (completedFiles + failedFiles) / totalFiles * 100;
              setOverallProgress(newOverallProgress);

              resolve();
            })
            .catch((error) => {
              // 处理权限错误
              let errorMessage = "上传失败";

              if (error.name === 'PermissionError' || error.response?.status === 403) {
                // 记录权限错误但不作为严重错误
                console.info(`文件 ${fileToUpload.name} 上传权限错误:`,
                  error.message || error.response?.data?.message || "权限不足");
                errorMessage = error.message || error.response?.data?.message || "您没有权限上传文件到该知识库";
              } else {
                // 其他错误正常记录
                console.error(`文件 ${fileToUpload.name} 上传失败:`, error);
                errorMessage = error.message || "上传失败";
              }

              // 上传失败
              setUploadingFiles((prev) => {
                const newFiles = [...prev];
                newFiles[index].status = "error";
                newFiles[index].error = errorMessage;
                return newFiles;
              });

              failedFiles++;

              // 更新总体进度
              const newOverallProgress = (completedFiles + failedFiles) / totalFiles * 100;
              setOverallProgress(newOverallProgress);

              resolve(); // 即使失败也解析Promise，以便继续处理其他文件
            });
        });
      });

      // 等待所有文件上传完成
      await Promise.all(uploadPromises);

      // 所有文件处理完成
      setUploadComplete(true);

      // 根据上传结果显示不同的消息
      if (failedFiles === 0) {
        onUploadComplete(`已成功上传 ${completedFiles} 个文件到知识库。`);
      } else if (completedFiles === 0) {
        setUploadError(`所有 ${totalFiles} 个文件上传失败。`);
      } else {
        onUploadComplete(`已上传 ${completedFiles} 个文件，${failedFiles} 个文件失败。`);
      }

      // 延迟关闭对话框
      setTimeout(() => {
        resetState();
      }, 2000);

    } catch (error) {
      console.error("上传过程出错:", error);
      setUploadError("上传过程中发生错误，请稍后再试");
      setIsUploading(false);
    }
  }

  // 重置状态
  const resetState = () => {
    setSelectedFiles([])
    setUploadingFiles([])
    setIsUploading(false)
    setUploadComplete(false)
    setUploadError(null)
    setCreateNewKnowledgeBase(false)
    setNewKnowledgeBaseName("")
    setNewKnowledgeBaseDescription("")
    setContactPerson("")
    setContactPhone("")
    setSelectedKnowledgeBase("")
    setUploadToSystemKb(false)
    // 移除useAI相关重置，现在所有文件都会自动使用AI分析
    onClose()
  }

  // 移除handleAnalysisComplete函数，现在所有文件都会自动使用AI分析

  // 获取文件图标
  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case "pdf":
        return <FileText className="text-red-500" />
      case "docx":
      case "doc":
        return <FileText className="text-blue-500" />
      case "xlsx":
      case "xls":
        return <FileText className="text-green-500" />
      case "pptx":
      case "ppt":
        return <FileText className="text-orange-500" />
      case "png":
      case "jpg":
      case "jpeg":
        return <FileText className="text-purple-500" />
      case "mp4":
      case "mov":
        return <FileText className="text-indigo-500" />
      case "zip":
      case "rar":
        return <FileText className="text-yellow-500" />
      default:
        return <FileText className="text-gray-500" />
    }
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!isUploading) {
          if (!open) {
            resetState()
          }
        }
      }}
    >
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl">上传文件</DialogTitle>
          <DialogDescription>选择上传文件的类型和目标知识库</DialogDescription>
        </DialogHeader>

        {/* 上传状态提示 */}
        {isUploading && (
          <div className="mb-4">
            <Alert className={uploadComplete ? "bg-green-50 border-green-200" : "bg-blue-50 border-blue-200"}>
              {uploadComplete ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
              )}
              <AlertTitle className={uploadComplete ? "text-green-800" : "text-blue-800"}>
                {uploadComplete ? "上传完成" : "正在上传"}
              </AlertTitle>
              <AlertDescription className={uploadComplete ? "text-green-700" : "text-blue-700"}>
                {uploadComplete
                  ? `已成功上传 ${uploadingFiles.length} 个文件`
                  : `正在上传文件，总进度 ${Math.round(overallProgress)}%`}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* 错误提示 */}
        {uploadError && (
          <Alert className="mb-4 bg-red-50 border-red-200">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertTitle className="text-red-800">上传错误</AlertTitle>
            <AlertDescription className="text-red-700">{uploadError}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-6 py-4">
          {!isUploading && (
            <>
              <div className="space-y-2">
                <h3 className="text-sm font-medium">上传到</h3>
                <RadioGroup
                  value={uploadToSystemKb ? "system" : "user"}
                  onValueChange={(value) => setUploadToSystemKb(value === "system")}
                >
                  <div className="flex items-start space-x-2 p-3 rounded-md border border-gray-200 bg-gray-50 mb-2">
                    <RadioGroupItem value="system" id="public" />
                    <div className="grid gap-1.5">
                      <Label htmlFor="public" className="font-medium">
                        系统知识库
                      </Label>
                      <p className="text-sm text-muted-foreground">上传到系统公开知识库，所有用户可访问</p>
                      <div className="mt-1 text-xs text-amber-600 flex items-center">
                        <Info className="h-3 w-3 mr-1" />
                        注意：上传到系统知识库的文件需要管理员审核后才会显示
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-2 p-3 rounded-md border border-gray-200 bg-gray-50">
                    <RadioGroupItem value="user" id="private" />
                    <div className="grid gap-1.5">
                      <Label htmlFor="private" className="font-medium">
                        用户知识库
                      </Label>
                      <p className="text-sm text-muted-foreground">上传到您的用户知识库，仅您和授权用户可访问</p>
                    </div>
                  </div>
                </RadioGroup>
              </div>

              {uploadToSystemKb && (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">选择系统知识库</h3>
                  <Select
                    value={selectedKnowledgeBase}
                    onValueChange={setSelectedKnowledgeBase}
                  >
                    <SelectTrigger className="border-gray-300">
                      <SelectValue placeholder="选择知识库" />
                    </SelectTrigger>
                    <SelectContent>
                      {systemKnowledgeBases.map((kb) => (
                        <SelectItem key={kb.id} value={kb.id}>
                          {kb.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {!uploadToSystemKb && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">选择用户知识库</h3>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="create-new"
                          checked={createNewKnowledgeBase}
                          onCheckedChange={(checked) => {
                            setCreateNewKnowledgeBase(checked === true)
                            if (checked !== true) {
                              setNewKnowledgeBaseName("")
                              setNewKnowledgeBaseDescription("")
                              setContactPerson("")
                              setContactPhone("")
                            }
                          }}
                        />
                        <label htmlFor="create-new" className="text-sm">
                          创建新知识库
                        </label>
                      </div>
                    </div>

                    {!createNewKnowledgeBase ? (
                      <Select
                        value={selectedKnowledgeBase}
                        onValueChange={setSelectedKnowledgeBase}
                        disabled={createNewKnowledgeBase}
                      >
                        <SelectTrigger className="border-gray-300">
                          <SelectValue placeholder="选择知识库" />
                        </SelectTrigger>
                        <SelectContent>
                          {userKnowledgeBases.map((kb) => (
                            <SelectItem key={kb.id} value={kb.id}>
                              {kb.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="space-y-4 p-4 border border-gray-200 rounded-md bg-gray-50">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">知识库名称</label>
                          <input
                            type="text"
                            value={newKnowledgeBaseName}
                            onChange={(e) => setNewKnowledgeBaseName(e.target.value)}
                            placeholder="输入知识库名称"
                            className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                          />
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium">知识库描述</label>
                          <textarea
                            value={newKnowledgeBaseDescription}
                            onChange={(e) => setNewKnowledgeBaseDescription(e.target.value)}
                            placeholder="输入知识库描述（可选）"
                            className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent h-24 resize-none"
                          ></textarea>
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium">联系人</label>
                          <input
                            type="text"
                            value={contactPerson}
                            onChange={(e) => setContactPerson(e.target.value)}
                            placeholder="输入联系人姓名"
                            className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                          />
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium">联系电话</label>
                          <input
                            type="tel"
                            value={contactPhone}
                            onChange={(e) => setContactPhone(e.target.value)}
                            placeholder="输入联系电话"
                            className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <h3 className="text-sm font-medium">选择文件</h3>
                <div
                  className={`border-2 border-dashed rounded-md p-6 text-center ${
                    isDragging ? "border-emerald-500 bg-emerald-50" : "border-gray-300 hover:border-emerald-500"
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    multiple
                    onChange={(e) => handleFileUpload(e.target.files)}
                  />
                  <div className="space-y-2">
                    <div className="mx-auto w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center">
                      <Upload className="h-6 w-6 text-emerald-600" />
                    </div>
                    <div className="text-sm">
                      <button
                        type="button"
                        className="text-emerald-600 hover:text-emerald-700 font-medium"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        点击上传
                      </button>{" "}
                      或拖放文件到此处
                    </div>
                    <p className="text-xs text-gray-500">支持 PDF, Word, Excel, PowerPoint, 图片等格式</p>
                  </div>
                </div>
              </div>

              {selectedFiles.length > 0 && (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">已选择 {selectedFiles.length} 个文件</h3>
                  <div className="max-h-40 overflow-y-auto space-y-2 border border-gray-200 rounded-md p-2">
                    {selectedFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                        <div className="flex items-center">
                          {getFileIcon(file.type.split('/')[1] || file.type)}
                          <span className="ml-2 text-sm truncate max-w-[200px]">{file.name}</span>
                          <span className="ml-2 text-xs text-gray-500">{formatFileSize(file.size)}</span>
                        </div>
                        <button
                          type="button"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => removeFileFromSelection(index)}
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 已移除AI分析勾选框，现在所有文件都会自动使用AI分析 */}
            </>
          )}

          {/* 上传进度 */}
          {isUploading && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium">总体进度</h3>
                  <span className="text-sm">{Math.round(overallProgress)}%</span>
                </div>
                <Progress value={overallProgress} className="h-2" />
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">文件上传进度</h3>
                <div className="max-h-40 overflow-y-auto space-y-2 border border-gray-200 rounded-md p-2">
                  {uploadingFiles.map((file) => (
                    <div key={file.id} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {file.status === "completed" ? (
                            <Check className="h-4 w-4 text-green-500 mr-2" />
                          ) : file.status === "error" ? (
                            <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                          ) : (
                            <RefreshCw className="h-4 w-4 text-blue-500 animate-spin mr-2" />
                          )}
                          <span className="text-sm truncate max-w-[200px]">{file.name}</span>
                        </div>
                        <span className="text-xs">
                          {file.status === "completed"
                            ? "完成"
                            : file.status === "error"
                            ? "失败"
                            : `${Math.round(file.progress)}%`}
                        </span>
                      </div>
                      <Progress
                        value={file.progress}
                        className={`h-1 ${
                          file.status === "completed"
                            ? "bg-green-100"
                            : file.status === "error"
                            ? "bg-red-100"
                            : "bg-blue-100"
                        }`}
                      />
                      {file.error && <p className="text-xs text-red-500">{file.error}</p>}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          {!isUploading ? (
            <>
              <Button variant="outline" onClick={onClose} disabled={isUploading}>
                取消
              </Button>
              <Button
                className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                onClick={startUploadProcess}
                disabled={selectedFiles.length === 0}
              >
                上传
              </Button>
            </>
          ) : (
            <Button
              variant="outline"
              onClick={onClose}
              disabled={!uploadComplete}
            >
              {uploadComplete ? "关闭" : "上传中..."}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
