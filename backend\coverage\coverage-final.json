{"C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\activity.controller.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\activity.controller.js", "statementMap": {"0": {"start": {"line": 7, "column": 47}, "end": {"line": 7, "column": 67}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 35}}, "2": {"start": {"line": 9, "column": 11}, "end": {"line": 9, "column": 24}}, "3": {"start": {"line": 10, "column": 13}, "end": {"line": 10, "column": 28}}, "4": {"start": {"line": 17, "column": 0}, "end": {"line": 87, "column": 2}}, "5": {"start": {"line": 18, "column": 2}, "end": {"line": 86, "column": 3}}, "6": {"start": {"line": 19, "column": 31}, "end": {"line": 19, "column": 40}}, "7": {"start": {"line": 20, "column": 17}, "end": {"line": 20, "column": 46}}, "8": {"start": {"line": 21, "column": 18}, "end": {"line": 21, "column": 49}}, "9": {"start": {"line": 22, "column": 19}, "end": {"line": 22, "column": 37}}, "10": {"start": {"line": 25, "column": 28}, "end": {"line": 25, "column": 30}}, "11": {"start": {"line": 28, "column": 4}, "end": {"line": 33, "column": 5}}, "12": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 38}}, "13": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 43}}, "14": {"start": {"line": 36, "column": 4}, "end": {"line": 41, "column": 5}}, "15": {"start": {"line": 37, "column": 6}, "end": {"line": 40, "column": 8}}, "16": {"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 5}}, "17": {"start": {"line": 45, "column": 6}, "end": {"line": 47, "column": 7}}, "18": {"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": 38}}, "19": {"start": {"line": 51, "column": 40}, "end": {"line": 63, "column": 6}}, "20": {"start": {"line": 66, "column": 23}, "end": {"line": 66, "column": 47}}, "21": {"start": {"line": 68, "column": 4}, "end": {"line": 79, "column": 7}}, "22": {"start": {"line": 81, "column": 4}, "end": {"line": 85, "column": 7}}, "23": {"start": {"line": 94, "column": 0}, "end": {"line": 157, "column": 2}}, "24": {"start": {"line": 95, "column": 2}, "end": {"line": 156, "column": 3}}, "25": {"start": {"line": 96, "column": 19}, "end": {"line": 96, "column": 29}}, "26": {"start": {"line": 99, "column": 21}, "end": {"line": 112, "column": 6}}, "27": {"start": {"line": 114, "column": 4}, "end": {"line": 119, "column": 5}}, "28": {"start": {"line": 115, "column": 6}, "end": {"line": 118, "column": 9}}, "29": {"start": {"line": 122, "column": 4}, "end": {"line": 129, "column": 5}}, "30": {"start": {"line": 123, "column": 6}, "end": {"line": 128, "column": 7}}, "31": {"start": {"line": 124, "column": 8}, "end": {"line": 127, "column": 11}}, "32": {"start": {"line": 132, "column": 24}, "end": {"line": 141, "column": 6}}, "33": {"start": {"line": 143, "column": 4}, "end": {"line": 149, "column": 7}}, "34": {"start": {"line": 151, "column": 4}, "end": {"line": 155, "column": 7}}, "35": {"start": {"line": 164, "column": 0}, "end": {"line": 198, "column": 2}}, "36": {"start": {"line": 165, "column": 2}, "end": {"line": 197, "column": 3}}, "37": {"start": {"line": 166, "column": 56}, "end": {"line": 166, "column": 64}}, "38": {"start": {"line": 168, "column": 4}, "end": {"line": 173, "column": 5}}, "39": {"start": {"line": 169, "column": 6}, "end": {"line": 172, "column": 9}}, "40": {"start": {"line": 176, "column": 21}, "end": {"line": 184, "column": 6}}, "41": {"start": {"line": 186, "column": 4}, "end": {"line": 190, "column": 7}}, "42": {"start": {"line": 192, "column": 4}, "end": {"line": 196, "column": 7}}, "43": {"start": {"line": 205, "column": 0}, "end": {"line": 238, "column": 2}}, "44": {"start": {"line": 206, "column": 2}, "end": {"line": 237, "column": 3}}, "45": {"start": {"line": 208, "column": 4}, "end": {"line": 213, "column": 5}}, "46": {"start": {"line": 209, "column": 6}, "end": {"line": 212, "column": 9}}, "47": {"start": {"line": 216, "column": 22}, "end": {"line": 216, "column": 55}}, "48": {"start": {"line": 218, "column": 4}, "end": {"line": 225, "column": 7}}, "49": {"start": {"line": 228, "column": 4}, "end": {"line": 230, "column": 5}}, "50": {"start": {"line": 229, "column": 6}, "end": {"line": 229, "column": 35}}, "51": {"start": {"line": 232, "column": 4}, "end": {"line": 236, "column": 7}}, "52": {"start": {"line": 245, "column": 0}, "end": {"line": 298, "column": 2}}, "53": {"start": {"line": 246, "column": 2}, "end": {"line": 297, "column": 3}}, "54": {"start": {"line": 247, "column": 19}, "end": {"line": 247, "column": 29}}, "55": {"start": {"line": 248, "column": 56}, "end": {"line": 248, "column": 64}}, "56": {"start": {"line": 251, "column": 21}, "end": {"line": 251, "column": 48}}, "57": {"start": {"line": 253, "column": 4}, "end": {"line": 258, "column": 5}}, "58": {"start": {"line": 254, "column": 6}, "end": {"line": 257, "column": 9}}, "59": {"start": {"line": 261, "column": 4}, "end": {"line": 268, "column": 7}}, "60": {"start": {"line": 271, "column": 28}, "end": {"line": 284, "column": 6}}, "61": {"start": {"line": 286, "column": 4}, "end": {"line": 290, "column": 7}}, "62": {"start": {"line": 292, "column": 4}, "end": {"line": 296, "column": 7}}, "63": {"start": {"line": 305, "column": 0}, "end": {"line": 355, "column": 2}}, "64": {"start": {"line": 306, "column": 2}, "end": {"line": 354, "column": 3}}, "65": {"start": {"line": 307, "column": 19}, "end": {"line": 307, "column": 29}}, "66": {"start": {"line": 310, "column": 21}, "end": {"line": 310, "column": 48}}, "67": {"start": {"line": 312, "column": 4}, "end": {"line": 317, "column": 5}}, "68": {"start": {"line": 313, "column": 6}, "end": {"line": 316, "column": 9}}, "69": {"start": {"line": 320, "column": 24}, "end": {"line": 322, "column": 6}}, "70": {"start": {"line": 325, "column": 4}, "end": {"line": 329, "column": 5}}, "71": {"start": {"line": 326, "column": 6}, "end": {"line": 328, "column": 7}}, "72": {"start": {"line": 327, "column": 8}, "end": {"line": 327, "column": 39}}, "73": {"start": {"line": 332, "column": 4}, "end": {"line": 334, "column": 5}}, "74": {"start": {"line": 333, "column": 6}, "end": {"line": 333, "column": 36}}, "75": {"start": {"line": 337, "column": 4}, "end": {"line": 339, "column": 7}}, "76": {"start": {"line": 342, "column": 4}, "end": {"line": 342, "column": 29}}, "77": {"start": {"line": 344, "column": 4}, "end": {"line": 347, "column": 7}}, "78": {"start": {"line": 349, "column": 4}, "end": {"line": 353, "column": 7}}, "79": {"start": {"line": 362, "column": 0}, "end": {"line": 410, "column": 2}}, "80": {"start": {"line": 363, "column": 2}, "end": {"line": 409, "column": 3}}, "81": {"start": {"line": 364, "column": 19}, "end": {"line": 364, "column": 29}}, "82": {"start": {"line": 367, "column": 21}, "end": {"line": 367, "column": 48}}, "83": {"start": {"line": 369, "column": 4}, "end": {"line": 374, "column": 5}}, "84": {"start": {"line": 370, "column": 6}, "end": {"line": 373, "column": 9}}, "85": {"start": {"line": 377, "column": 4}, "end": {"line": 384, "column": 5}}, "86": {"start": {"line": 378, "column": 6}, "end": {"line": 383, "column": 7}}, "87": {"start": {"line": 379, "column": 8}, "end": {"line": 382, "column": 11}}, "88": {"start": {"line": 387, "column": 24}, "end": {"line": 397, "column": 6}}, "89": {"start": {"line": 399, "column": 4}, "end": {"line": 402, "column": 7}}, "90": {"start": {"line": 404, "column": 4}, "end": {"line": 408, "column": 7}}, "91": {"start": {"line": 417, "column": 0}, "end": {"line": 484, "column": 2}}, "92": {"start": {"line": 418, "column": 2}, "end": {"line": 483, "column": 3}}, "93": {"start": {"line": 419, "column": 19}, "end": {"line": 419, "column": 29}}, "94": {"start": {"line": 422, "column": 21}, "end": {"line": 422, "column": 48}}, "95": {"start": {"line": 424, "column": 4}, "end": {"line": 434, "column": 5}}, "96": {"start": {"line": 426, "column": 6}, "end": {"line": 428, "column": 7}}, "97": {"start": {"line": 427, "column": 8}, "end": {"line": 427, "column": 37}}, "98": {"start": {"line": 430, "column": 6}, "end": {"line": 433, "column": 9}}, "99": {"start": {"line": 437, "column": 4}, "end": {"line": 442, "column": 5}}, "100": {"start": {"line": 438, "column": 6}, "end": {"line": 441, "column": 9}}, "101": {"start": {"line": 445, "column": 23}, "end": {"line": 454, "column": 6}}, "102": {"start": {"line": 457, "column": 27}, "end": {"line": 465, "column": 6}}, "103": {"start": {"line": 467, "column": 4}, "end": {"line": 471, "column": 7}}, "104": {"start": {"line": 474, "column": 4}, "end": {"line": 476, "column": 5}}, "105": {"start": {"line": 475, "column": 6}, "end": {"line": 475, "column": 35}}, "106": {"start": {"line": 478, "column": 4}, "end": {"line": 482, "column": 7}}, "107": {"start": {"line": 491, "column": 0}, "end": {"line": 544, "column": 2}}, "108": {"start": {"line": 492, "column": 2}, "end": {"line": 543, "column": 3}}, "109": {"start": {"line": 493, "column": 29}, "end": {"line": 493, "column": 39}}, "110": {"start": {"line": 496, "column": 23}, "end": {"line": 503, "column": 6}}, "111": {"start": {"line": 505, "column": 4}, "end": {"line": 510, "column": 5}}, "112": {"start": {"line": 506, "column": 6}, "end": {"line": 509, "column": 9}}, "113": {"start": {"line": 513, "column": 4}, "end": {"line": 520, "column": 5}}, "114": {"start": {"line": 514, "column": 6}, "end": {"line": 519, "column": 7}}, "115": {"start": {"line": 515, "column": 8}, "end": {"line": 518, "column": 11}}, "116": {"start": {"line": 523, "column": 4}, "end": {"line": 528, "column": 5}}, "117": {"start": {"line": 524, "column": 6}, "end": {"line": 527, "column": 9}}, "118": {"start": {"line": 531, "column": 4}, "end": {"line": 531, "column": 56}}, "119": {"start": {"line": 532, "column": 4}, "end": {"line": 532, "column": 115}}, "120": {"start": {"line": 535, "column": 23}, "end": {"line": 535, "column": 59}}, "121": {"start": {"line": 536, "column": 4}, "end": {"line": 536, "column": 25}}, "122": {"start": {"line": 538, "column": 4}, "end": {"line": 542, "column": 7}}, "123": {"start": {"line": 551, "column": 0}, "end": {"line": 591, "column": 2}}, "124": {"start": {"line": 552, "column": 2}, "end": {"line": 590, "column": 3}}, "125": {"start": {"line": 553, "column": 29}, "end": {"line": 553, "column": 39}}, "126": {"start": {"line": 556, "column": 23}, "end": {"line": 563, "column": 6}}, "127": {"start": {"line": 565, "column": 4}, "end": {"line": 570, "column": 5}}, "128": {"start": {"line": 566, "column": 6}, "end": {"line": 569, "column": 9}}, "129": {"start": {"line": 573, "column": 4}, "end": {"line": 575, "column": 5}}, "130": {"start": {"line": 574, "column": 6}, "end": {"line": 574, "column": 37}}, "131": {"start": {"line": 578, "column": 4}, "end": {"line": 578, "column": 31}}, "132": {"start": {"line": 580, "column": 4}, "end": {"line": 583, "column": 7}}, "133": {"start": {"line": 585, "column": 4}, "end": {"line": 589, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 25}}, "loc": {"start": {"line": 17, "column": 44}, "end": {"line": 87, "column": 1}}, "line": 17}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 94, "column": 26}, "end": {"line": 94, "column": 27}}, "loc": {"start": {"line": 94, "column": 46}, "end": {"line": 157, "column": 1}}, "line": 94}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 164, "column": 25}, "end": {"line": 164, "column": 26}}, "loc": {"start": {"line": 164, "column": 45}, "end": {"line": 198, "column": 1}}, "line": 164}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 205, "column": 30}, "end": {"line": 205, "column": 31}}, "loc": {"start": {"line": 205, "column": 50}, "end": {"line": 238, "column": 1}}, "line": 205}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 245, "column": 25}, "end": {"line": 245, "column": 26}}, "loc": {"start": {"line": 245, "column": 45}, "end": {"line": 298, "column": 1}}, "line": 245}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 305, "column": 25}, "end": {"line": 305, "column": 26}}, "loc": {"start": {"line": 305, "column": 45}, "end": {"line": 355, "column": 1}}, "line": 305}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 362, "column": 33}, "end": {"line": 362, "column": 34}}, "loc": {"start": {"line": 362, "column": 53}, "end": {"line": 410, "column": 1}}, "line": 362}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 417, "column": 35}, "end": {"line": 417, "column": 36}}, "loc": {"start": {"line": 417, "column": 55}, "end": {"line": 484, "column": 1}}, "line": 417}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 491, "column": 37}, "end": {"line": 491, "column": 38}}, "loc": {"start": {"line": 491, "column": 57}, "end": {"line": 544, "column": 1}}, "line": 491}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 551, "column": 35}, "end": {"line": 551, "column": 36}}, "loc": {"start": {"line": 551, "column": 55}, "end": {"line": 591, "column": 1}}, "line": 551}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 17}, "end": {"line": 20, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 17}, "end": {"line": 20, "column": 41}}, {"start": {"line": 20, "column": 45}, "end": {"line": 20, "column": 46}}], "line": 20}, "1": {"loc": {"start": {"line": 21, "column": 18}, "end": {"line": 21, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 18}, "end": {"line": 21, "column": 43}}, {"start": {"line": 21, "column": 47}, "end": {"line": 21, "column": 49}}], "line": 21}, "2": {"loc": {"start": {"line": 28, "column": 4}, "end": {"line": 33, "column": 5}}, "type": "if", "locations": [{"start": {"line": 28, "column": 4}, "end": {"line": 33, "column": 5}}, {"start": {"line": 30, "column": 11}, "end": {"line": 33, "column": 5}}], "line": 28}, "3": {"loc": {"start": {"line": 36, "column": 4}, "end": {"line": 41, "column": 5}}, "type": "if", "locations": [{"start": {"line": 36, "column": 4}, "end": {"line": 41, "column": 5}}, {"start": {}, "end": {}}], "line": 36}, "4": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 5}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 5}}, {"start": {}, "end": {}}], "line": 44}, "5": {"loc": {"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 16}}, {"start": {"line": 44, "column": 20}, "end": {"line": 44, "column": 45}}], "line": 44}, "6": {"loc": {"start": {"line": 45, "column": 6}, "end": {"line": 47, "column": 7}}, "type": "if", "locations": [{"start": {"line": 45, "column": 6}, "end": {"line": 47, "column": 7}}, {"start": {}, "end": {}}], "line": 45}, "7": {"loc": {"start": {"line": 114, "column": 4}, "end": {"line": 119, "column": 5}}, "type": "if", "locations": [{"start": {"line": 114, "column": 4}, "end": {"line": 119, "column": 5}}, {"start": {}, "end": {}}], "line": 114}, "8": {"loc": {"start": {"line": 122, "column": 4}, "end": {"line": 129, "column": 5}}, "type": "if", "locations": [{"start": {"line": 122, "column": 4}, "end": {"line": 129, "column": 5}}, {"start": {}, "end": {}}], "line": 122}, "9": {"loc": {"start": {"line": 123, "column": 6}, "end": {"line": 128, "column": 7}}, "type": "if", "locations": [{"start": {"line": 123, "column": 6}, "end": {"line": 128, "column": 7}}, {"start": {}, "end": {}}], "line": 123}, "10": {"loc": {"start": {"line": 123, "column": 10}, "end": {"line": 123, "column": 89}}, "type": "binary-expr", "locations": [{"start": {"line": 123, "column": 10}, "end": {"line": 123, "column": 19}}, {"start": {"line": 123, "column": 24}, "end": {"line": 123, "column": 49}}, {"start": {"line": 123, "column": 53}, "end": {"line": 123, "column": 88}}], "line": 123}, "11": {"loc": {"start": {"line": 168, "column": 4}, "end": {"line": 173, "column": 5}}, "type": "if", "locations": [{"start": {"line": 168, "column": 4}, "end": {"line": 173, "column": 5}}, {"start": {}, "end": {}}], "line": 168}, "12": {"loc": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 14}}, {"start": {"line": 168, "column": 18}, "end": {"line": 168, "column": 23}}, {"start": {"line": 168, "column": 27}, "end": {"line": 168, "column": 39}}], "line": 168}, "13": {"loc": {"start": {"line": 181, "column": 14}, "end": {"line": 181, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 181, "column": 14}, "end": {"line": 181, "column": 20}}, {"start": {"line": 181, "column": 24}, "end": {"line": 181, "column": 31}}], "line": 181}, "14": {"loc": {"start": {"line": 208, "column": 4}, "end": {"line": 213, "column": 5}}, "type": "if", "locations": [{"start": {"line": 208, "column": 4}, "end": {"line": 213, "column": 5}}, {"start": {}, "end": {}}], "line": 208}, "15": {"loc": {"start": {"line": 228, "column": 4}, "end": {"line": 230, "column": 5}}, "type": "if", "locations": [{"start": {"line": 228, "column": 4}, "end": {"line": 230, "column": 5}}, {"start": {}, "end": {}}], "line": 228}, "16": {"loc": {"start": {"line": 228, "column": 8}, "end": {"line": 228, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 228, "column": 8}, "end": {"line": 228, "column": 16}}, {"start": {"line": 228, "column": 20}, "end": {"line": 228, "column": 48}}], "line": 228}, "17": {"loc": {"start": {"line": 253, "column": 4}, "end": {"line": 258, "column": 5}}, "type": "if", "locations": [{"start": {"line": 253, "column": 4}, "end": {"line": 258, "column": 5}}, {"start": {}, "end": {}}], "line": 253}, "18": {"loc": {"start": {"line": 262, "column": 13}, "end": {"line": 262, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 262, "column": 13}, "end": {"line": 262, "column": 18}}, {"start": {"line": 262, "column": 22}, "end": {"line": 262, "column": 36}}], "line": 262}, "19": {"loc": {"start": {"line": 263, "column": 12}, "end": {"line": 263, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 263, "column": 19}, "end": {"line": 263, "column": 33}}, {"start": {"line": 263, "column": 36}, "end": {"line": 263, "column": 49}}], "line": 263}, "20": {"loc": {"start": {"line": 264, "column": 19}, "end": {"line": 264, "column": 81}}, "type": "cond-expr", "locations": [{"start": {"line": 264, "column": 47}, "end": {"line": 264, "column": 58}}, {"start": {"line": 264, "column": 61}, "end": {"line": 264, "column": 81}}], "line": 264}, "21": {"loc": {"start": {"line": 265, "column": 13}, "end": {"line": 265, "column": 57}}, "type": "cond-expr", "locations": [{"start": {"line": 265, "column": 35}, "end": {"line": 265, "column": 40}}, {"start": {"line": 265, "column": 43}, "end": {"line": 265, "column": 57}}], "line": 265}, "22": {"loc": {"start": {"line": 266, "column": 14}, "end": {"line": 266, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 266, "column": 14}, "end": {"line": 266, "column": 20}}, {"start": {"line": 266, "column": 24}, "end": {"line": 266, "column": 39}}], "line": 266}, "23": {"loc": {"start": {"line": 312, "column": 4}, "end": {"line": 317, "column": 5}}, "type": "if", "locations": [{"start": {"line": 312, "column": 4}, "end": {"line": 317, "column": 5}}, {"start": {}, "end": {}}], "line": 312}, "24": {"loc": {"start": {"line": 326, "column": 6}, "end": {"line": 328, "column": 7}}, "type": "if", "locations": [{"start": {"line": 326, "column": 6}, "end": {"line": 328, "column": 7}}, {"start": {}, "end": {}}], "line": 326}, "25": {"loc": {"start": {"line": 332, "column": 4}, "end": {"line": 334, "column": 5}}, "type": "if", "locations": [{"start": {"line": 332, "column": 4}, "end": {"line": 334, "column": 5}}, {"start": {}, "end": {}}], "line": 332}, "26": {"loc": {"start": {"line": 332, "column": 8}, "end": {"line": 332, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 332, "column": 8}, "end": {"line": 332, "column": 22}}, {"start": {"line": 332, "column": 26}, "end": {"line": 332, "column": 55}}], "line": 332}, "27": {"loc": {"start": {"line": 369, "column": 4}, "end": {"line": 374, "column": 5}}, "type": "if", "locations": [{"start": {"line": 369, "column": 4}, "end": {"line": 374, "column": 5}}, {"start": {}, "end": {}}], "line": 369}, "28": {"loc": {"start": {"line": 377, "column": 4}, "end": {"line": 384, "column": 5}}, "type": "if", "locations": [{"start": {"line": 377, "column": 4}, "end": {"line": 384, "column": 5}}, {"start": {}, "end": {}}], "line": 377}, "29": {"loc": {"start": {"line": 378, "column": 6}, "end": {"line": 383, "column": 7}}, "type": "if", "locations": [{"start": {"line": 378, "column": 6}, "end": {"line": 383, "column": 7}}, {"start": {}, "end": {}}], "line": 378}, "30": {"loc": {"start": {"line": 378, "column": 10}, "end": {"line": 378, "column": 89}}, "type": "binary-expr", "locations": [{"start": {"line": 378, "column": 10}, "end": {"line": 378, "column": 19}}, {"start": {"line": 378, "column": 24}, "end": {"line": 378, "column": 49}}, {"start": {"line": 378, "column": 53}, "end": {"line": 378, "column": 88}}], "line": 378}, "31": {"loc": {"start": {"line": 424, "column": 4}, "end": {"line": 434, "column": 5}}, "type": "if", "locations": [{"start": {"line": 424, "column": 4}, "end": {"line": 434, "column": 5}}, {"start": {}, "end": {}}], "line": 424}, "32": {"loc": {"start": {"line": 426, "column": 6}, "end": {"line": 428, "column": 7}}, "type": "if", "locations": [{"start": {"line": 426, "column": 6}, "end": {"line": 428, "column": 7}}, {"start": {}, "end": {}}], "line": 426}, "33": {"loc": {"start": {"line": 426, "column": 10}, "end": {"line": 426, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 426, "column": 10}, "end": {"line": 426, "column": 18}}, {"start": {"line": 426, "column": 22}, "end": {"line": 426, "column": 50}}], "line": 426}, "34": {"loc": {"start": {"line": 437, "column": 4}, "end": {"line": 442, "column": 5}}, "type": "if", "locations": [{"start": {"line": 437, "column": 4}, "end": {"line": 442, "column": 5}}, {"start": {}, "end": {}}], "line": 437}, "35": {"loc": {"start": {"line": 474, "column": 4}, "end": {"line": 476, "column": 5}}, "type": "if", "locations": [{"start": {"line": 474, "column": 4}, "end": {"line": 476, "column": 5}}, {"start": {}, "end": {}}], "line": 474}, "36": {"loc": {"start": {"line": 474, "column": 8}, "end": {"line": 474, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 474, "column": 8}, "end": {"line": 474, "column": 16}}, {"start": {"line": 474, "column": 20}, "end": {"line": 474, "column": 48}}], "line": 474}, "37": {"loc": {"start": {"line": 505, "column": 4}, "end": {"line": 510, "column": 5}}, "type": "if", "locations": [{"start": {"line": 505, "column": 4}, "end": {"line": 510, "column": 5}}, {"start": {}, "end": {}}], "line": 505}, "38": {"loc": {"start": {"line": 513, "column": 4}, "end": {"line": 520, "column": 5}}, "type": "if", "locations": [{"start": {"line": 513, "column": 4}, "end": {"line": 520, "column": 5}}, {"start": {}, "end": {}}], "line": 513}, "39": {"loc": {"start": {"line": 514, "column": 6}, "end": {"line": 519, "column": 7}}, "type": "if", "locations": [{"start": {"line": 514, "column": 6}, "end": {"line": 519, "column": 7}}, {"start": {}, "end": {}}], "line": 514}, "40": {"loc": {"start": {"line": 514, "column": 10}, "end": {"line": 514, "column": 100}}, "type": "binary-expr", "locations": [{"start": {"line": 514, "column": 10}, "end": {"line": 514, "column": 19}}, {"start": {"line": 514, "column": 24}, "end": {"line": 514, "column": 49}}, {"start": {"line": 514, "column": 53}, "end": {"line": 514, "column": 99}}], "line": 514}, "41": {"loc": {"start": {"line": 523, "column": 4}, "end": {"line": 528, "column": 5}}, "type": "if", "locations": [{"start": {"line": 523, "column": 4}, "end": {"line": 528, "column": 5}}, {"start": {}, "end": {}}], "line": 523}, "42": {"loc": {"start": {"line": 565, "column": 4}, "end": {"line": 570, "column": 5}}, "type": "if", "locations": [{"start": {"line": 565, "column": 4}, "end": {"line": 570, "column": 5}}, {"start": {}, "end": {}}], "line": 565}, "43": {"loc": {"start": {"line": 573, "column": 4}, "end": {"line": 575, "column": 5}}, "type": "if", "locations": [{"start": {"line": 573, "column": 4}, "end": {"line": 575, "column": 5}}, {"start": {}, "end": {}}], "line": 573}}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 13, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 13, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 13, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 13, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 13, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 13, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 13, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 13, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 13, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0, 0], "11": [0, 0], "12": [0, 0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0, 0, 0], "41": [0, 0], "42": [0, 0], "43": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "be01d6fdecb26492066d7f22df679c998f1f46c5"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\ai-assistant.controller.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\ai-assistant.controller.js", "statementMap": {"0": {"start": {"line": 7, "column": 45}, "end": {"line": 7, "column": 65}}, "1": {"start": {"line": 8, "column": 14}, "end": {"line": 8, "column": 30}}, "2": {"start": {"line": 15, "column": 0}, "end": {"line": 75, "column": 2}}, "3": {"start": {"line": 16, "column": 2}, "end": {"line": 74, "column": 3}}, "4": {"start": {"line": 17, "column": 21}, "end": {"line": 17, "column": 30}}, "5": {"start": {"line": 20, "column": 28}, "end": {"line": 22, "column": 5}}, "6": {"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 5}}, "7": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 34}}, "8": {"start": {"line": 30, "column": 23}, "end": {"line": 45, "column": 6}}, "9": {"start": {"line": 48, "column": 31}, "end": {"line": 62, "column": 6}}, "10": {"start": {"line": 49, "column": 19}, "end": {"line": 49, "column": 37}}, "11": {"start": {"line": 52, "column": 6}, "end": {"line": 59, "column": 7}}, "12": {"start": {"line": 53, "column": 8}, "end": {"line": 53, "column": 28}}, "13": {"start": {"line": 54, "column": 8}, "end": {"line": 54, "column": 33}}, "14": {"start": {"line": 55, "column": 8}, "end": {"line": 55, "column": 27}}, "15": {"start": {"line": 56, "column": 8}, "end": {"line": 56, "column": 29}}, "16": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 36}}, "17": {"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 38}}, "18": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 18}}, "19": {"start": {"line": 64, "column": 4}, "end": {"line": 67, "column": 7}}, "20": {"start": {"line": 69, "column": 4}, "end": {"line": 73, "column": 7}}, "21": {"start": {"line": 82, "column": 0}, "end": {"line": 133, "column": 2}}, "22": {"start": {"line": 83, "column": 2}, "end": {"line": 132, "column": 3}}, "23": {"start": {"line": 84, "column": 19}, "end": {"line": 84, "column": 29}}, "24": {"start": {"line": 87, "column": 22}, "end": {"line": 100, "column": 6}}, "25": {"start": {"line": 102, "column": 4}, "end": {"line": 107, "column": 5}}, "26": {"start": {"line": 103, "column": 6}, "end": {"line": 106, "column": 9}}, "27": {"start": {"line": 110, "column": 17}, "end": {"line": 110, "column": 35}}, "28": {"start": {"line": 113, "column": 4}, "end": {"line": 120, "column": 5}}, "29": {"start": {"line": 114, "column": 6}, "end": {"line": 114, "column": 26}}, "30": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 31}}, "31": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 25}}, "32": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 27}}, "33": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 34}}, "34": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 36}}, "35": {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 7}}, "36": {"start": {"line": 127, "column": 4}, "end": {"line": 131, "column": 7}}, "37": {"start": {"line": 140, "column": 0}, "end": {"line": 219, "column": 2}}, "38": {"start": {"line": 141, "column": 2}, "end": {"line": 218, "column": 3}}, "39": {"start": {"line": 142, "column": 19}, "end": {"line": 142, "column": 29}}, "40": {"start": {"line": 155, "column": 8}, "end": {"line": 155, "column": 16}}, "41": {"start": {"line": 158, "column": 22}, "end": {"line": 158, "column": 52}}, "42": {"start": {"line": 160, "column": 4}, "end": {"line": 165, "column": 5}}, "43": {"start": {"line": 161, "column": 6}, "end": {"line": 164, "column": 9}}, "44": {"start": {"line": 168, "column": 4}, "end": {"line": 173, "column": 5}}, "45": {"start": {"line": 169, "column": 6}, "end": {"line": 172, "column": 9}}, "46": {"start": {"line": 176, "column": 4}, "end": {"line": 189, "column": 7}}, "47": {"start": {"line": 192, "column": 29}, "end": {"line": 205, "column": 6}}, "48": {"start": {"line": 207, "column": 4}, "end": {"line": 211, "column": 7}}, "49": {"start": {"line": 213, "column": 4}, "end": {"line": 217, "column": 7}}, "50": {"start": {"line": 226, "column": 0}, "end": {"line": 281, "column": 2}}, "51": {"start": {"line": 227, "column": 2}, "end": {"line": 280, "column": 3}}, "52": {"start": {"line": 228, "column": 39}, "end": {"line": 228, "column": 47}}, "53": {"start": {"line": 230, "column": 4}, "end": {"line": 235, "column": 5}}, "54": {"start": {"line": 231, "column": 6}, "end": {"line": 234, "column": 9}}, "55": {"start": {"line": 238, "column": 22}, "end": {"line": 243, "column": 6}}, "56": {"start": {"line": 245, "column": 4}, "end": {"line": 250, "column": 5}}, "57": {"start": {"line": 246, "column": 6}, "end": {"line": 249, "column": 9}}, "58": {"start": {"line": 253, "column": 21}, "end": {"line": 268, "column": 5}}, "59": {"start": {"line": 270, "column": 4}, "end": {"line": 273, "column": 7}}, "60": {"start": {"line": 275, "column": 4}, "end": {"line": 279, "column": 7}}, "61": {"start": {"line": 288, "column": 0}, "end": {"line": 348, "column": 2}}, "62": {"start": {"line": 289, "column": 2}, "end": {"line": 347, "column": 3}}, "63": {"start": {"line": 290, "column": 39}, "end": {"line": 290, "column": 47}}, "64": {"start": {"line": 292, "column": 4}, "end": {"line": 297, "column": 5}}, "65": {"start": {"line": 293, "column": 6}, "end": {"line": 296, "column": 9}}, "66": {"start": {"line": 300, "column": 22}, "end": {"line": 305, "column": 6}}, "67": {"start": {"line": 307, "column": 4}, "end": {"line": 312, "column": 5}}, "68": {"start": {"line": 308, "column": 6}, "end": {"line": 311, "column": 9}}, "69": {"start": {"line": 315, "column": 28}, "end": {"line": 315, "column": 71}}, "70": {"start": {"line": 318, "column": 21}, "end": {"line": 335, "column": 5}}, "71": {"start": {"line": 337, "column": 4}, "end": {"line": 340, "column": 7}}, "72": {"start": {"line": 342, "column": 4}, "end": {"line": 346, "column": 7}}, "73": {"start": {"line": 355, "column": 0}, "end": {"line": 439, "column": 2}}, "74": {"start": {"line": 356, "column": 2}, "end": {"line": 438, "column": 3}}, "75": {"start": {"line": 357, "column": 34}, "end": {"line": 357, "column": 42}}, "76": {"start": {"line": 359, "column": 4}, "end": {"line": 364, "column": 5}}, "77": {"start": {"line": 360, "column": 6}, "end": {"line": 363, "column": 9}}, "78": {"start": {"line": 367, "column": 26}, "end": {"line": 367, "column": 73}}, "79": {"start": {"line": 369, "column": 4}, "end": {"line": 374, "column": 5}}, "80": {"start": {"line": 370, "column": 6}, "end": {"line": 373, "column": 9}}, "81": {"start": {"line": 377, "column": 24}, "end": {"line": 377, "column": 29}}, "82": {"start": {"line": 380, "column": 4}, "end": {"line": 404, "column": 5}}, "83": {"start": {"line": 381, "column": 6}, "end": {"line": 381, "column": 27}}, "84": {"start": {"line": 384, "column": 9}, "end": {"line": 404, "column": 5}}, "85": {"start": {"line": 385, "column": 6}, "end": {"line": 385, "column": 27}}, "86": {"start": {"line": 388, "column": 9}, "end": {"line": 404, "column": 5}}, "87": {"start": {"line": 389, "column": 6}, "end": {"line": 389, "column": 27}}, "88": {"start": {"line": 393, "column": 38}, "end": {"line": 393, "column": 58}}, "89": {"start": {"line": 394, "column": 21}, "end": {"line": 399, "column": 8}}, "90": {"start": {"line": 401, "column": 6}, "end": {"line": 403, "column": 7}}, "91": {"start": {"line": 402, "column": 8}, "end": {"line": 402, "column": 29}}, "92": {"start": {"line": 406, "column": 4}, "end": {"line": 411, "column": 5}}, "93": {"start": {"line": 407, "column": 6}, "end": {"line": 410, "column": 9}}, "94": {"start": {"line": 414, "column": 4}, "end": {"line": 416, "column": 5}}, "95": {"start": {"line": 415, "column": 6}, "end": {"line": 415, "column": 23}}, "96": {"start": {"line": 418, "column": 4}, "end": {"line": 420, "column": 5}}, "97": {"start": {"line": 419, "column": 6}, "end": {"line": 419, "column": 37}}, "98": {"start": {"line": 422, "column": 4}, "end": {"line": 422, "column": 66}}, "99": {"start": {"line": 424, "column": 4}, "end": {"line": 431, "column": 7}}, "100": {"start": {"line": 433, "column": 4}, "end": {"line": 437, "column": 7}}, "101": {"start": {"line": 446, "column": 0}, "end": {"line": 523, "column": 2}}, "102": {"start": {"line": 447, "column": 2}, "end": {"line": 522, "column": 3}}, "103": {"start": {"line": 448, "column": 30}, "end": {"line": 448, "column": 40}}, "104": {"start": {"line": 449, "column": 32}, "end": {"line": 449, "column": 41}}, "105": {"start": {"line": 452, "column": 4}, "end": {"line": 457, "column": 5}}, "106": {"start": {"line": 453, "column": 6}, "end": {"line": 456, "column": 9}}, "107": {"start": {"line": 460, "column": 22}, "end": {"line": 465, "column": 6}}, "108": {"start": {"line": 467, "column": 4}, "end": {"line": 472, "column": 5}}, "109": {"start": {"line": 468, "column": 6}, "end": {"line": 471, "column": 9}}, "110": {"start": {"line": 475, "column": 4}, "end": {"line": 494, "column": 5}}, "111": {"start": {"line": 476, "column": 23}, "end": {"line": 488, "column": 7}}, "112": {"start": {"line": 490, "column": 6}, "end": {"line": 493, "column": 9}}, "113": {"start": {"line": 497, "column": 21}, "end": {"line": 510, "column": 5}}, "114": {"start": {"line": 512, "column": 4}, "end": {"line": 515, "column": 7}}, "115": {"start": {"line": 517, "column": 4}, "end": {"line": 521, "column": 7}}, "116": {"start": {"line": 530, "column": 0}, "end": {"line": 602, "column": 2}}, "117": {"start": {"line": 531, "column": 2}, "end": {"line": 601, "column": 3}}, "118": {"start": {"line": 532, "column": 30}, "end": {"line": 532, "column": 40}}, "119": {"start": {"line": 533, "column": 32}, "end": {"line": 533, "column": 40}}, "120": {"start": {"line": 536, "column": 4}, "end": {"line": 541, "column": 5}}, "121": {"start": {"line": 537, "column": 6}, "end": {"line": 540, "column": 9}}, "122": {"start": {"line": 544, "column": 22}, "end": {"line": 549, "column": 6}}, "123": {"start": {"line": 551, "column": 4}, "end": {"line": 556, "column": 5}}, "124": {"start": {"line": 552, "column": 6}, "end": {"line": 555, "column": 9}}, "125": {"start": {"line": 559, "column": 4}, "end": {"line": 576, "column": 5}}, "126": {"start": {"line": 560, "column": 6}, "end": {"line": 570, "column": 8}}, "127": {"start": {"line": 572, "column": 6}, "end": {"line": 575, "column": 9}}, "128": {"start": {"line": 579, "column": 4}, "end": {"line": 589, "column": 6}}, "129": {"start": {"line": 591, "column": 4}, "end": {"line": 594, "column": 7}}, "130": {"start": {"line": 596, "column": 4}, "end": {"line": 600, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 26}, "end": {"line": 15, "column": 27}}, "loc": {"start": {"line": 15, "column": 46}, "end": {"line": 75, "column": 1}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 48, "column": 46}, "end": {"line": 48, "column": 47}}, "loc": {"start": {"line": 48, "column": 59}, "end": {"line": 62, "column": 5}}, "line": 48}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 82, "column": 29}, "end": {"line": 82, "column": 30}}, "loc": {"start": {"line": 82, "column": 49}, "end": {"line": 133, "column": 1}}, "line": 82}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 140, "column": 28}, "end": {"line": 140, "column": 29}}, "loc": {"start": {"line": 140, "column": 48}, "end": {"line": 219, "column": 1}}, "line": 140}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 226, "column": 33}, "end": {"line": 226, "column": 34}}, "loc": {"start": {"line": 226, "column": 53}, "end": {"line": 281, "column": 1}}, "line": 226}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 288, "column": 29}, "end": {"line": 288, "column": 30}}, "loc": {"start": {"line": 288, "column": 49}, "end": {"line": 348, "column": 1}}, "line": 288}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 355, "column": 40}, "end": {"line": 355, "column": 41}}, "loc": {"start": {"line": 355, "column": 60}, "end": {"line": 439, "column": 1}}, "line": 355}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 446, "column": 33}, "end": {"line": 446, "column": 34}}, "loc": {"start": {"line": 446, "column": 53}, "end": {"line": 523, "column": 1}}, "line": 446}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 530, "column": 35}, "end": {"line": 530, "column": 36}}, "loc": {"start": {"line": 530, "column": 55}, "end": {"line": 602, "column": 1}}, "line": 530}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 5}}, "type": "if", "locations": [{"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 5}}, {"start": {}, "end": {}}], "line": 25}, "1": {"loc": {"start": {"line": 52, "column": 6}, "end": {"line": 59, "column": 7}}, "type": "if", "locations": [{"start": {"line": 52, "column": 6}, "end": {"line": 59, "column": 7}}, {"start": {}, "end": {}}], "line": 52}, "2": {"loc": {"start": {"line": 102, "column": 4}, "end": {"line": 107, "column": 5}}, "type": "if", "locations": [{"start": {"line": 102, "column": 4}, "end": {"line": 107, "column": 5}}, {"start": {}, "end": {}}], "line": 102}, "3": {"loc": {"start": {"line": 113, "column": 4}, "end": {"line": 120, "column": 5}}, "type": "if", "locations": [{"start": {"line": 113, "column": 4}, "end": {"line": 120, "column": 5}}, {"start": {}, "end": {}}], "line": 113}, "4": {"loc": {"start": {"line": 160, "column": 4}, "end": {"line": 165, "column": 5}}, "type": "if", "locations": [{"start": {"line": 160, "column": 4}, "end": {"line": 165, "column": 5}}, {"start": {}, "end": {}}], "line": 160}, "5": {"loc": {"start": {"line": 168, "column": 4}, "end": {"line": 173, "column": 5}}, "type": "if", "locations": [{"start": {"line": 168, "column": 4}, "end": {"line": 173, "column": 5}}, {"start": {}, "end": {}}], "line": 168}, "6": {"loc": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 80}}, "type": "binary-expr", "locations": [{"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 27}}, {"start": {"line": 168, "column": 31}, "end": {"line": 168, "column": 44}}, {"start": {"line": 168, "column": 48}, "end": {"line": 168, "column": 80}}], "line": 168}, "7": {"loc": {"start": {"line": 177, "column": 12}, "end": {"line": 177, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 177, "column": 12}, "end": {"line": 177, "column": 16}}, {"start": {"line": 177, "column": 20}, "end": {"line": 177, "column": 34}}], "line": 177}, "8": {"loc": {"start": {"line": 178, "column": 19}, "end": {"line": 178, "column": 82}}, "type": "cond-expr", "locations": [{"start": {"line": 178, "column": 47}, "end": {"line": 178, "column": 58}}, {"start": {"line": 178, "column": 61}, "end": {"line": 178, "column": 82}}], "line": 178}, "9": {"loc": {"start": {"line": 179, "column": 15}, "end": {"line": 179, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 179, "column": 15}, "end": {"line": 179, "column": 22}}, {"start": {"line": 179, "column": 26}, "end": {"line": 179, "column": 43}}], "line": 179}, "10": {"loc": {"start": {"line": 180, "column": 20}, "end": {"line": 180, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 180, "column": 20}, "end": {"line": 180, "column": 32}}, {"start": {"line": 180, "column": 36}, "end": {"line": 180, "column": 58}}], "line": 180}, "11": {"loc": {"start": {"line": 181, "column": 14}, "end": {"line": 181, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 181, "column": 14}, "end": {"line": 181, "column": 20}}, {"start": {"line": 181, "column": 24}, "end": {"line": 181, "column": 40}}], "line": 181}, "12": {"loc": {"start": {"line": 182, "column": 16}, "end": {"line": 182, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 182, "column": 16}, "end": {"line": 182, "column": 24}}, {"start": {"line": 182, "column": 28}, "end": {"line": 182, "column": 46}}], "line": 182}, "13": {"loc": {"start": {"line": 183, "column": 23}, "end": {"line": 183, "column": 98}}, "type": "cond-expr", "locations": [{"start": {"line": 183, "column": 55}, "end": {"line": 183, "column": 70}}, {"start": {"line": 183, "column": 73}, "end": {"line": 183, "column": 98}}], "line": 183}, "14": {"loc": {"start": {"line": 184, "column": 25}, "end": {"line": 184, "column": 106}}, "type": "cond-expr", "locations": [{"start": {"line": 184, "column": 59}, "end": {"line": 184, "column": 76}}, {"start": {"line": 184, "column": 79}, "end": {"line": 184, "column": 106}}], "line": 184}, "15": {"loc": {"start": {"line": 185, "column": 12}, "end": {"line": 185, "column": 54}}, "type": "cond-expr", "locations": [{"start": {"line": 185, "column": 33}, "end": {"line": 185, "column": 37}}, {"start": {"line": 185, "column": 40}, "end": {"line": 185, "column": 54}}], "line": 185}, "16": {"loc": {"start": {"line": 186, "column": 23}, "end": {"line": 186, "column": 98}}, "type": "cond-expr", "locations": [{"start": {"line": 186, "column": 55}, "end": {"line": 186, "column": 70}}, {"start": {"line": 186, "column": 73}, "end": {"line": 186, "column": 98}}], "line": 186}, "17": {"loc": {"start": {"line": 187, "column": 14}, "end": {"line": 187, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 187, "column": 14}, "end": {"line": 187, "column": 20}}, {"start": {"line": 187, "column": 24}, "end": {"line": 187, "column": 40}}], "line": 187}, "18": {"loc": {"start": {"line": 230, "column": 4}, "end": {"line": 235, "column": 5}}, "type": "if", "locations": [{"start": {"line": 230, "column": 4}, "end": {"line": 235, "column": 5}}, {"start": {}, "end": {}}], "line": 230}, "19": {"loc": {"start": {"line": 245, "column": 4}, "end": {"line": 250, "column": 5}}, "type": "if", "locations": [{"start": {"line": 245, "column": 4}, "end": {"line": 250, "column": 5}}, {"start": {}, "end": {}}], "line": 245}, "20": {"loc": {"start": {"line": 292, "column": 4}, "end": {"line": 297, "column": 5}}, "type": "if", "locations": [{"start": {"line": 292, "column": 4}, "end": {"line": 297, "column": 5}}, {"start": {}, "end": {}}], "line": 292}, "21": {"loc": {"start": {"line": 307, "column": 4}, "end": {"line": 312, "column": 5}}, "type": "if", "locations": [{"start": {"line": 307, "column": 4}, "end": {"line": 312, "column": 5}}, {"start": {}, "end": {}}], "line": 307}, "22": {"loc": {"start": {"line": 359, "column": 4}, "end": {"line": 364, "column": 5}}, "type": "if", "locations": [{"start": {"line": 359, "column": 4}, "end": {"line": 364, "column": 5}}, {"start": {}, "end": {}}], "line": 359}, "23": {"loc": {"start": {"line": 369, "column": 4}, "end": {"line": 374, "column": 5}}, "type": "if", "locations": [{"start": {"line": 369, "column": 4}, "end": {"line": 374, "column": 5}}, {"start": {}, "end": {}}], "line": 369}, "24": {"loc": {"start": {"line": 380, "column": 4}, "end": {"line": 404, "column": 5}}, "type": "if", "locations": [{"start": {"line": 380, "column": 4}, "end": {"line": 404, "column": 5}}, {"start": {"line": 384, "column": 9}, "end": {"line": 404, "column": 5}}], "line": 380}, "25": {"loc": {"start": {"line": 384, "column": 9}, "end": {"line": 404, "column": 5}}, "type": "if", "locations": [{"start": {"line": 384, "column": 9}, "end": {"line": 404, "column": 5}}, {"start": {"line": 388, "column": 9}, "end": {"line": 404, "column": 5}}], "line": 384}, "26": {"loc": {"start": {"line": 388, "column": 9}, "end": {"line": 404, "column": 5}}, "type": "if", "locations": [{"start": {"line": 388, "column": 9}, "end": {"line": 404, "column": 5}}, {"start": {"line": 392, "column": 9}, "end": {"line": 404, "column": 5}}], "line": 388}, "27": {"loc": {"start": {"line": 401, "column": 6}, "end": {"line": 403, "column": 7}}, "type": "if", "locations": [{"start": {"line": 401, "column": 6}, "end": {"line": 403, "column": 7}}, {"start": {}, "end": {}}], "line": 401}, "28": {"loc": {"start": {"line": 406, "column": 4}, "end": {"line": 411, "column": 5}}, "type": "if", "locations": [{"start": {"line": 406, "column": 4}, "end": {"line": 411, "column": 5}}, {"start": {}, "end": {}}], "line": 406}, "29": {"loc": {"start": {"line": 414, "column": 4}, "end": {"line": 416, "column": 5}}, "type": "if", "locations": [{"start": {"line": 414, "column": 4}, "end": {"line": 416, "column": 5}}, {"start": {}, "end": {}}], "line": 414}, "30": {"loc": {"start": {"line": 418, "column": 4}, "end": {"line": 420, "column": 5}}, "type": "if", "locations": [{"start": {"line": 418, "column": 4}, "end": {"line": 420, "column": 5}}, {"start": {}, "end": {}}], "line": 418}, "31": {"loc": {"start": {"line": 452, "column": 4}, "end": {"line": 457, "column": 5}}, "type": "if", "locations": [{"start": {"line": 452, "column": 4}, "end": {"line": 457, "column": 5}}, {"start": {}, "end": {}}], "line": 452}, "32": {"loc": {"start": {"line": 467, "column": 4}, "end": {"line": 472, "column": 5}}, "type": "if", "locations": [{"start": {"line": 467, "column": 4}, "end": {"line": 472, "column": 5}}, {"start": {}, "end": {}}], "line": 467}, "33": {"loc": {"start": {"line": 475, "column": 4}, "end": {"line": 494, "column": 5}}, "type": "if", "locations": [{"start": {"line": 475, "column": 4}, "end": {"line": 494, "column": 5}}, {"start": {}, "end": {}}], "line": 475}, "34": {"loc": {"start": {"line": 536, "column": 4}, "end": {"line": 541, "column": 5}}, "type": "if", "locations": [{"start": {"line": 536, "column": 4}, "end": {"line": 541, "column": 5}}, {"start": {}, "end": {}}], "line": 536}, "35": {"loc": {"start": {"line": 551, "column": 4}, "end": {"line": 556, "column": 5}}, "type": "if", "locations": [{"start": {"line": 551, "column": 4}, "end": {"line": 556, "column": 5}}, {"start": {}, "end": {}}], "line": 551}, "36": {"loc": {"start": {"line": 559, "column": 4}, "end": {"line": 576, "column": 5}}, "type": "if", "locations": [{"start": {"line": 559, "column": 4}, "end": {"line": 576, "column": 5}}, {"start": {}, "end": {}}], "line": 559}}, "s": {"0": 13, "1": 13, "2": 13, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 13, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 13, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 13, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 13, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 13, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 13, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 13, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "2c02ff6a2c8b86a89ae06fe357c97c5dbe44cc3e"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\comment.controller.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\comment.controller.js", "statementMap": {"0": {"start": {"line": 1, "column": 46}, "end": {"line": 1, "column": 66}}, "1": {"start": {"line": 8, "column": 0}, "end": {"line": 44, "column": 2}}, "2": {"start": {"line": 9, "column": 2}, "end": {"line": 43, "column": 3}}, "3": {"start": {"line": 10, "column": 35}, "end": {"line": 10, "column": 45}}, "4": {"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": 46}}, "5": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 55}}, "6": {"start": {"line": 15, "column": 18}, "end": {"line": 19, "column": 5}}, "7": {"start": {"line": 22, "column": 38}, "end": {"line": 28, "column": 6}}, "8": {"start": {"line": 30, "column": 4}, "end": {"line": 39, "column": 7}}, "9": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 40}}, "10": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 58}}, "11": {"start": {"line": 51, "column": 0}, "end": {"line": 86, "column": 2}}, "12": {"start": {"line": 52, "column": 2}, "end": {"line": 85, "column": 3}}, "13": {"start": {"line": 53, "column": 45}, "end": {"line": 53, "column": 54}}, "14": {"start": {"line": 54, "column": 17}, "end": {"line": 54, "column": 46}}, "15": {"start": {"line": 55, "column": 21}, "end": {"line": 55, "column": 55}}, "16": {"start": {"line": 58, "column": 18}, "end": {"line": 58, "column": 20}}, "17": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 44}}, "18": {"start": {"line": 59, "column": 18}, "end": {"line": 59, "column": 44}}, "19": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 50}}, "20": {"start": {"line": 60, "column": 20}, "end": {"line": 60, "column": 50}}, "21": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 38}}, "22": {"start": {"line": 61, "column": 16}, "end": {"line": 61, "column": 38}}, "23": {"start": {"line": 64, "column": 38}, "end": {"line": 70, "column": 6}}, "24": {"start": {"line": 72, "column": 4}, "end": {"line": 81, "column": 7}}, "25": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 38}}, "26": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 56}}, "27": {"start": {"line": 93, "column": 0}, "end": {"line": 147, "column": 2}}, "28": {"start": {"line": 94, "column": 2}, "end": {"line": 146, "column": 3}}, "29": {"start": {"line": 95, "column": 59}, "end": {"line": 95, "column": 67}}, "30": {"start": {"line": 98, "column": 23}, "end": {"line": 106, "column": 6}}, "31": {"start": {"line": 109, "column": 19}, "end": {"line": 114, "column": 6}}, "32": {"start": {"line": 117, "column": 4}, "end": {"line": 128, "column": 5}}, "33": {"start": {"line": 118, "column": 6}, "end": {"line": 127, "column": 9}}, "34": {"start": {"line": 130, "column": 4}, "end": {"line": 142, "column": 7}}, "35": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 36}}, "36": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 54}}, "37": {"start": {"line": 154, "column": 0}, "end": {"line": 215, "column": 2}}, "38": {"start": {"line": 155, "column": 2}, "end": {"line": 214, "column": 3}}, "39": {"start": {"line": 156, "column": 19}, "end": {"line": 156, "column": 29}}, "40": {"start": {"line": 157, "column": 31}, "end": {"line": 157, "column": 39}}, "41": {"start": {"line": 160, "column": 20}, "end": {"line": 162, "column": 6}}, "42": {"start": {"line": 164, "column": 4}, "end": {"line": 166, "column": 5}}, "43": {"start": {"line": 165, "column": 6}, "end": {"line": 165, "column": 56}}, "44": {"start": {"line": 169, "column": 4}, "end": {"line": 174, "column": 7}}, "45": {"start": {"line": 177, "column": 4}, "end": {"line": 201, "column": 5}}, "46": {"start": {"line": 178, "column": 6}, "end": {"line": 187, "column": 9}}, "47": {"start": {"line": 190, "column": 9}, "end": {"line": 201, "column": 5}}, "48": {"start": {"line": 191, "column": 6}, "end": {"line": 200, "column": 9}}, "49": {"start": {"line": 203, "column": 4}, "end": {"line": 210, "column": 7}}, "50": {"start": {"line": 212, "column": 4}, "end": {"line": 212, "column": 36}}, "51": {"start": {"line": 213, "column": 4}, "end": {"line": 213, "column": 54}}, "52": {"start": {"line": 222, "column": 0}, "end": {"line": 248, "column": 2}}, "53": {"start": {"line": 223, "column": 2}, "end": {"line": 247, "column": 3}}, "54": {"start": {"line": 224, "column": 19}, "end": {"line": 224, "column": 29}}, "55": {"start": {"line": 227, "column": 20}, "end": {"line": 227, "column": 46}}, "56": {"start": {"line": 229, "column": 4}, "end": {"line": 231, "column": 5}}, "57": {"start": {"line": 230, "column": 6}, "end": {"line": 230, "column": 56}}, "58": {"start": {"line": 234, "column": 4}, "end": {"line": 236, "column": 5}}, "59": {"start": {"line": 235, "column": 6}, "end": {"line": 235, "column": 60}}, "60": {"start": {"line": 239, "column": 4}, "end": {"line": 239, "column": 28}}, "61": {"start": {"line": 241, "column": 4}, "end": {"line": 243, "column": 7}}, "62": {"start": {"line": 245, "column": 4}, "end": {"line": 245, "column": 36}}, "63": {"start": {"line": 246, "column": 4}, "end": {"line": 246, "column": 54}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 49}, "end": {"line": 44, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 51, "column": 22}, "end": {"line": 51, "column": 23}}, "loc": {"start": {"line": 51, "column": 42}, "end": {"line": 86, "column": 1}}, "line": 51}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 93, "column": 24}, "end": {"line": 93, "column": 25}}, "loc": {"start": {"line": 93, "column": 44}, "end": {"line": 147, "column": 1}}, "line": 93}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 154, "column": 24}, "end": {"line": 154, "column": 25}}, "loc": {"start": {"line": 154, "column": 44}, "end": {"line": 215, "column": 1}}, "line": 154}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 222, "column": 24}, "end": {"line": 222, "column": 25}}, "loc": {"start": {"line": 222, "column": 44}, "end": {"line": 248, "column": 1}}, "line": 222}}, "branchMap": {"0": {"loc": {"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": 41}}, {"start": {"line": 11, "column": 45}, "end": {"line": 11, "column": 46}}], "line": 11}, "1": {"loc": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 49}}, {"start": {"line": 12, "column": 53}, "end": {"line": 12, "column": 55}}], "line": 12}, "2": {"loc": {"start": {"line": 54, "column": 17}, "end": {"line": 54, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 54, "column": 17}, "end": {"line": 54, "column": 41}}, {"start": {"line": 54, "column": 45}, "end": {"line": 54, "column": 46}}], "line": 54}, "3": {"loc": {"start": {"line": 55, "column": 21}, "end": {"line": 55, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 21}, "end": {"line": 55, "column": 49}}, {"start": {"line": 55, "column": 53}, "end": {"line": 55, "column": 55}}], "line": 55}, "4": {"loc": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 44}}, "type": "if", "locations": [{"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 44}}, {"start": {}, "end": {}}], "line": 59}, "5": {"loc": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 50}}, "type": "if", "locations": [{"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 50}}, {"start": {}, "end": {}}], "line": 60}, "6": {"loc": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 38}}, "type": "if", "locations": [{"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 38}}, {"start": {}, "end": {}}], "line": 61}, "7": {"loc": {"start": {"line": 164, "column": 4}, "end": {"line": 166, "column": 5}}, "type": "if", "locations": [{"start": {"line": 164, "column": 4}, "end": {"line": 166, "column": 5}}, {"start": {}, "end": {}}], "line": 164}, "8": {"loc": {"start": {"line": 171, "column": 21}, "end": {"line": 171, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 171, "column": 21}, "end": {"line": 171, "column": 27}}, {"start": {"line": 171, "column": 31}, "end": {"line": 171, "column": 35}}], "line": 171}, "9": {"loc": {"start": {"line": 177, "column": 4}, "end": {"line": 201, "column": 5}}, "type": "if", "locations": [{"start": {"line": 177, "column": 4}, "end": {"line": 201, "column": 5}}, {"start": {"line": 190, "column": 9}, "end": {"line": 201, "column": 5}}], "line": 177}, "10": {"loc": {"start": {"line": 190, "column": 9}, "end": {"line": 201, "column": 5}}, "type": "if", "locations": [{"start": {"line": 190, "column": 9}, "end": {"line": 201, "column": 5}}, {"start": {}, "end": {}}], "line": 190}, "11": {"loc": {"start": {"line": 195, "column": 55}, "end": {"line": 195, "column": 84}}, "type": "cond-expr", "locations": [{"start": {"line": 195, "column": 64}, "end": {"line": 195, "column": 79}}, {"start": {"line": 195, "column": 82}, "end": {"line": 195, "column": 84}}], "line": 195}, "12": {"loc": {"start": {"line": 229, "column": 4}, "end": {"line": 231, "column": 5}}, "type": "if", "locations": [{"start": {"line": 229, "column": 4}, "end": {"line": 231, "column": 5}}, {"start": {}, "end": {}}], "line": 229}, "13": {"loc": {"start": {"line": 234, "column": 4}, "end": {"line": 236, "column": 5}}, "type": "if", "locations": [{"start": {"line": 234, "column": 4}, "end": {"line": 236, "column": 5}}, {"start": {}, "end": {}}], "line": 234}, "14": {"loc": {"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 39}}, {"start": {"line": 234, "column": 43}, "end": {"line": 234, "column": 71}}], "line": 234}}, "s": {"0": 13, "1": 13, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 13, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 13, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 13, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 13, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "8163732c93938e4f49d9e231ce228f0436f24d9c"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\file.controller.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\file.controller.js", "statementMap": {"0": {"start": {"line": 7, "column": 59}, "end": {"line": 7, "column": 79}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 35}}, "2": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": 28}}, "3": {"start": {"line": 10, "column": 11}, "end": {"line": 10, "column": 24}}, "4": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 30}}, "5": {"start": {"line": 18, "column": 0}, "end": {"line": 142, "column": 2}}, "6": {"start": {"line": 19, "column": 2}, "end": {"line": 141, "column": 3}}, "7": {"start": {"line": 20, "column": 32}, "end": {"line": 20, "column": 42}}, "8": {"start": {"line": 23, "column": 4}, "end": {"line": 28, "column": 5}}, "9": {"start": {"line": 24, "column": 6}, "end": {"line": 27, "column": 9}}, "10": {"start": {"line": 31, "column": 26}, "end": {"line": 31, "column": 71}}, "11": {"start": {"line": 33, "column": 4}, "end": {"line": 41, "column": 5}}, "12": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 35}}, "13": {"start": {"line": 37, "column": 6}, "end": {"line": 40, "column": 9}}, "14": {"start": {"line": 44, "column": 24}, "end": {"line": 44, "column": 29}}, "15": {"start": {"line": 47, "column": 4}, "end": {"line": 69, "column": 5}}, "16": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 27}}, "17": {"start": {"line": 51, "column": 9}, "end": {"line": 69, "column": 5}}, "18": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 27}}, "19": {"start": {"line": 56, "column": 21}, "end": {"line": 64, "column": 8}}, "20": {"start": {"line": 66, "column": 6}, "end": {"line": 68, "column": 7}}, "21": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 29}}, "22": {"start": {"line": 71, "column": 4}, "end": {"line": 79, "column": 5}}, "23": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 35}}, "24": {"start": {"line": 75, "column": 6}, "end": {"line": 78, "column": 9}}, "25": {"start": {"line": 84, "column": 17}, "end": {"line": 84, "column": 26}}, "26": {"start": {"line": 86, "column": 4}, "end": {"line": 89, "column": 5}}, "27": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 26}}, "28": {"start": {"line": 92, "column": 17}, "end": {"line": 104, "column": 6}}, "29": {"start": {"line": 107, "column": 4}, "end": {"line": 110, "column": 7}}, "30": {"start": {"line": 113, "column": 4}, "end": {"line": 116, "column": 5}}, "31": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 44}}, "32": {"start": {"line": 118, "column": 4}, "end": {"line": 129, "column": 7}}, "33": {"start": {"line": 132, "column": 4}, "end": {"line": 134, "column": 5}}, "34": {"start": {"line": 133, "column": 6}, "end": {"line": 133, "column": 35}}, "35": {"start": {"line": 136, "column": 4}, "end": {"line": 140, "column": 7}}, "36": {"start": {"line": 149, "column": 0}, "end": {"line": 231, "column": 2}}, "37": {"start": {"line": 150, "column": 2}, "end": {"line": 230, "column": 3}}, "38": {"start": {"line": 151, "column": 37}, "end": {"line": 151, "column": 46}}, "39": {"start": {"line": 152, "column": 17}, "end": {"line": 152, "column": 46}}, "40": {"start": {"line": 153, "column": 18}, "end": {"line": 153, "column": 49}}, "41": {"start": {"line": 154, "column": 19}, "end": {"line": 154, "column": 37}}, "42": {"start": {"line": 157, "column": 28}, "end": {"line": 157, "column": 30}}, "43": {"start": {"line": 160, "column": 4}, "end": {"line": 162, "column": 5}}, "44": {"start": {"line": 161, "column": 6}, "end": {"line": 161, "column": 38}}, "45": {"start": {"line": 165, "column": 4}, "end": {"line": 167, "column": 5}}, "46": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 34}}, "47": {"start": {"line": 170, "column": 4}, "end": {"line": 174, "column": 5}}, "48": {"start": {"line": 171, "column": 6}, "end": {"line": 173, "column": 8}}, "49": {"start": {"line": 177, "column": 4}, "end": {"line": 182, "column": 5}}, "50": {"start": {"line": 178, "column": 6}, "end": {"line": 181, "column": 8}}, "51": {"start": {"line": 185, "column": 35}, "end": {"line": 207, "column": 6}}, "52": {"start": {"line": 210, "column": 23}, "end": {"line": 210, "column": 47}}, "53": {"start": {"line": 212, "column": 4}, "end": {"line": 223, "column": 7}}, "54": {"start": {"line": 225, "column": 4}, "end": {"line": 229, "column": 7}}, "55": {"start": {"line": 238, "column": 0}, "end": {"line": 369, "column": 2}}, "56": {"start": {"line": 239, "column": 2}, "end": {"line": 368, "column": 3}}, "57": {"start": {"line": 240, "column": 32}, "end": {"line": 240, "column": 42}}, "58": {"start": {"line": 241, "column": 37}, "end": {"line": 241, "column": 46}}, "59": {"start": {"line": 242, "column": 17}, "end": {"line": 242, "column": 46}}, "60": {"start": {"line": 243, "column": 18}, "end": {"line": 243, "column": 49}}, "61": {"start": {"line": 244, "column": 19}, "end": {"line": 244, "column": 37}}, "62": {"start": {"line": 247, "column": 26}, "end": {"line": 247, "column": 71}}, "63": {"start": {"line": 249, "column": 4}, "end": {"line": 254, "column": 5}}, "64": {"start": {"line": 250, "column": 6}, "end": {"line": 253, "column": 9}}, "65": {"start": {"line": 257, "column": 24}, "end": {"line": 257, "column": 29}}, "66": {"start": {"line": 260, "column": 4}, "end": {"line": 283, "column": 5}}, "67": {"start": {"line": 261, "column": 6}, "end": {"line": 261, "column": 27}}, "68": {"start": {"line": 264, "column": 9}, "end": {"line": 283, "column": 5}}, "69": {"start": {"line": 265, "column": 6}, "end": {"line": 265, "column": 27}}, "70": {"start": {"line": 268, "column": 9}, "end": {"line": 283, "column": 5}}, "71": {"start": {"line": 269, "column": 6}, "end": {"line": 269, "column": 27}}, "72": {"start": {"line": 273, "column": 21}, "end": {"line": 278, "column": 8}}, "73": {"start": {"line": 280, "column": 6}, "end": {"line": 282, "column": 7}}, "74": {"start": {"line": 281, "column": 8}, "end": {"line": 281, "column": 29}}, "75": {"start": {"line": 285, "column": 4}, "end": {"line": 290, "column": 5}}, "76": {"start": {"line": 286, "column": 6}, "end": {"line": 289, "column": 9}}, "77": {"start": {"line": 293, "column": 28}, "end": {"line": 295, "column": 5}}, "78": {"start": {"line": 298, "column": 4}, "end": {"line": 308, "column": 5}}, "79": {"start": {"line": 299, "column": 6}, "end": {"line": 299, "column": 38}}, "80": {"start": {"line": 302, "column": 6}, "end": {"line": 307, "column": 7}}, "81": {"start": {"line": 303, "column": 8}, "end": {"line": 306, "column": 10}}, "82": {"start": {"line": 311, "column": 4}, "end": {"line": 313, "column": 5}}, "83": {"start": {"line": 312, "column": 6}, "end": {"line": 312, "column": 34}}, "84": {"start": {"line": 316, "column": 4}, "end": {"line": 320, "column": 5}}, "85": {"start": {"line": 317, "column": 6}, "end": {"line": 319, "column": 8}}, "86": {"start": {"line": 323, "column": 35}, "end": {"line": 340, "column": 6}}, "87": {"start": {"line": 343, "column": 23}, "end": {"line": 343, "column": 47}}, "88": {"start": {"line": 345, "column": 4}, "end": {"line": 361, "column": 7}}, "89": {"start": {"line": 363, "column": 4}, "end": {"line": 367, "column": 7}}, "90": {"start": {"line": 376, "column": 0}, "end": {"line": 458, "column": 2}}, "91": {"start": {"line": 377, "column": 2}, "end": {"line": 457, "column": 3}}, "92": {"start": {"line": 378, "column": 19}, "end": {"line": 378, "column": 29}}, "93": {"start": {"line": 381, "column": 17}, "end": {"line": 399, "column": 6}}, "94": {"start": {"line": 401, "column": 4}, "end": {"line": 406, "column": 5}}, "95": {"start": {"line": 402, "column": 6}, "end": {"line": 405, "column": 9}}, "96": {"start": {"line": 409, "column": 24}, "end": {"line": 409, "column": 29}}, "97": {"start": {"line": 412, "column": 4}, "end": {"line": 438, "column": 5}}, "98": {"start": {"line": 413, "column": 6}, "end": {"line": 413, "column": 27}}, "99": {"start": {"line": 416, "column": 9}, "end": {"line": 438, "column": 5}}, "100": {"start": {"line": 417, "column": 6}, "end": {"line": 417, "column": 27}}, "101": {"start": {"line": 420, "column": 9}, "end": {"line": 438, "column": 5}}, "102": {"start": {"line": 422, "column": 6}, "end": {"line": 437, "column": 7}}, "103": {"start": {"line": 423, "column": 8}, "end": {"line": 423, "column": 29}}, "104": {"start": {"line": 427, "column": 23}, "end": {"line": 432, "column": 10}}, "105": {"start": {"line": 434, "column": 8}, "end": {"line": 436, "column": 9}}, "106": {"start": {"line": 435, "column": 10}, "end": {"line": 435, "column": 31}}, "107": {"start": {"line": 440, "column": 4}, "end": {"line": 445, "column": 5}}, "108": {"start": {"line": 441, "column": 6}, "end": {"line": 444, "column": 9}}, "109": {"start": {"line": 447, "column": 4}, "end": {"line": 450, "column": 7}}, "110": {"start": {"line": 452, "column": 4}, "end": {"line": 456, "column": 7}}, "111": {"start": {"line": 465, "column": 0}, "end": {"line": 548, "column": 2}}, "112": {"start": {"line": 466, "column": 2}, "end": {"line": 547, "column": 3}}, "113": {"start": {"line": 467, "column": 19}, "end": {"line": 467, "column": 29}}, "114": {"start": {"line": 470, "column": 17}, "end": {"line": 478, "column": 6}}, "115": {"start": {"line": 480, "column": 4}, "end": {"line": 485, "column": 5}}, "116": {"start": {"line": 481, "column": 6}, "end": {"line": 484, "column": 9}}, "117": {"start": {"line": 488, "column": 24}, "end": {"line": 488, "column": 29}}, "118": {"start": {"line": 491, "column": 4}, "end": {"line": 517, "column": 5}}, "119": {"start": {"line": 492, "column": 6}, "end": {"line": 492, "column": 27}}, "120": {"start": {"line": 495, "column": 9}, "end": {"line": 517, "column": 5}}, "121": {"start": {"line": 496, "column": 6}, "end": {"line": 496, "column": 27}}, "122": {"start": {"line": 499, "column": 9}, "end": {"line": 517, "column": 5}}, "123": {"start": {"line": 501, "column": 6}, "end": {"line": 516, "column": 7}}, "124": {"start": {"line": 502, "column": 8}, "end": {"line": 502, "column": 29}}, "125": {"start": {"line": 506, "column": 23}, "end": {"line": 511, "column": 10}}, "126": {"start": {"line": 513, "column": 8}, "end": {"line": 515, "column": 9}}, "127": {"start": {"line": 514, "column": 10}, "end": {"line": 514, "column": 31}}, "128": {"start": {"line": 519, "column": 4}, "end": {"line": 524, "column": 5}}, "129": {"start": {"line": 520, "column": 6}, "end": {"line": 523, "column": 9}}, "130": {"start": {"line": 527, "column": 4}, "end": {"line": 532, "column": 5}}, "131": {"start": {"line": 528, "column": 6}, "end": {"line": 531, "column": 9}}, "132": {"start": {"line": 535, "column": 4}, "end": {"line": 535, "column": 50}}, "133": {"start": {"line": 536, "column": 4}, "end": {"line": 536, "column": 109}}, "134": {"start": {"line": 539, "column": 23}, "end": {"line": 539, "column": 53}}, "135": {"start": {"line": 540, "column": 4}, "end": {"line": 540, "column": 25}}, "136": {"start": {"line": 542, "column": 4}, "end": {"line": 546, "column": 7}}, "137": {"start": {"line": 555, "column": 0}, "end": {"line": 631, "column": 2}}, "138": {"start": {"line": 556, "column": 2}, "end": {"line": 630, "column": 3}}, "139": {"start": {"line": 557, "column": 19}, "end": {"line": 557, "column": 29}}, "140": {"start": {"line": 558, "column": 38}, "end": {"line": 558, "column": 46}}, "141": {"start": {"line": 561, "column": 4}, "end": {"line": 566, "column": 5}}, "142": {"start": {"line": 562, "column": 6}, "end": {"line": 565, "column": 9}}, "143": {"start": {"line": 569, "column": 17}, "end": {"line": 580, "column": 6}}, "144": {"start": {"line": 582, "column": 4}, "end": {"line": 587, "column": 5}}, "145": {"start": {"line": 583, "column": 6}, "end": {"line": 586, "column": 9}}, "146": {"start": {"line": 590, "column": 4}, "end": {"line": 595, "column": 5}}, "147": {"start": {"line": 591, "column": 6}, "end": {"line": 594, "column": 9}}, "148": {"start": {"line": 598, "column": 4}, "end": {"line": 603, "column": 7}}, "149": {"start": {"line": 606, "column": 4}, "end": {"line": 609, "column": 5}}, "150": {"start": {"line": 608, "column": 6}, "end": {"line": 608, "column": 44}}, "151": {"start": {"line": 613, "column": 4}, "end": {"line": 623, "column": 7}}, "152": {"start": {"line": 625, "column": 4}, "end": {"line": 629, "column": 7}}, "153": {"start": {"line": 638, "column": 0}, "end": {"line": 729, "column": 2}}, "154": {"start": {"line": 639, "column": 2}, "end": {"line": 728, "column": 3}}, "155": {"start": {"line": 640, "column": 19}, "end": {"line": 640, "column": 29}}, "156": {"start": {"line": 643, "column": 17}, "end": {"line": 650, "column": 6}}, "157": {"start": {"line": 652, "column": 4}, "end": {"line": 657, "column": 5}}, "158": {"start": {"line": 653, "column": 6}, "end": {"line": 656, "column": 9}}, "159": {"start": {"line": 660, "column": 4}, "end": {"line": 665, "column": 5}}, "160": {"start": {"line": 661, "column": 6}, "end": {"line": 664, "column": 9}}, "161": {"start": {"line": 668, "column": 24}, "end": {"line": 668, "column": 29}}, "162": {"start": {"line": 671, "column": 4}, "end": {"line": 695, "column": 5}}, "163": {"start": {"line": 672, "column": 6}, "end": {"line": 672, "column": 27}}, "164": {"start": {"line": 675, "column": 9}, "end": {"line": 695, "column": 5}}, "165": {"start": {"line": 676, "column": 6}, "end": {"line": 676, "column": 27}}, "166": {"start": {"line": 679, "column": 9}, "end": {"line": 695, "column": 5}}, "167": {"start": {"line": 680, "column": 6}, "end": {"line": 680, "column": 27}}, "168": {"start": {"line": 684, "column": 21}, "end": {"line": 690, "column": 8}}, "169": {"start": {"line": 692, "column": 6}, "end": {"line": 694, "column": 7}}, "170": {"start": {"line": 693, "column": 8}, "end": {"line": 693, "column": 29}}, "171": {"start": {"line": 697, "column": 4}, "end": {"line": 702, "column": 5}}, "172": {"start": {"line": 698, "column": 6}, "end": {"line": 701, "column": 9}}, "173": {"start": {"line": 705, "column": 19}, "end": {"line": 705, "column": 55}}, "174": {"start": {"line": 708, "column": 4}, "end": {"line": 711, "column": 7}}, "175": {"start": {"line": 713, "column": 4}, "end": {"line": 721, "column": 7}}, "176": {"start": {"line": 723, "column": 4}, "end": {"line": 727, "column": 7}}, "177": {"start": {"line": 736, "column": 0}, "end": {"line": 824, "column": 2}}, "178": {"start": {"line": 737, "column": 2}, "end": {"line": 823, "column": 3}}, "179": {"start": {"line": 738, "column": 19}, "end": {"line": 738, "column": 29}}, "180": {"start": {"line": 741, "column": 17}, "end": {"line": 748, "column": 6}}, "181": {"start": {"line": 750, "column": 4}, "end": {"line": 755, "column": 5}}, "182": {"start": {"line": 751, "column": 6}, "end": {"line": 754, "column": 9}}, "183": {"start": {"line": 758, "column": 24}, "end": {"line": 758, "column": 29}}, "184": {"start": {"line": 761, "column": 4}, "end": {"line": 785, "column": 5}}, "185": {"start": {"line": 762, "column": 6}, "end": {"line": 762, "column": 27}}, "186": {"start": {"line": 765, "column": 9}, "end": {"line": 785, "column": 5}}, "187": {"start": {"line": 766, "column": 6}, "end": {"line": 766, "column": 27}}, "188": {"start": {"line": 769, "column": 9}, "end": {"line": 785, "column": 5}}, "189": {"start": {"line": 770, "column": 6}, "end": {"line": 770, "column": 27}}, "190": {"start": {"line": 774, "column": 21}, "end": {"line": 780, "column": 8}}, "191": {"start": {"line": 782, "column": 6}, "end": {"line": 784, "column": 7}}, "192": {"start": {"line": 783, "column": 8}, "end": {"line": 783, "column": 29}}, "193": {"start": {"line": 787, "column": 4}, "end": {"line": 792, "column": 5}}, "194": {"start": {"line": 788, "column": 6}, "end": {"line": 791, "column": 9}}, "195": {"start": {"line": 795, "column": 26}, "end": {"line": 795, "column": 78}}, "196": {"start": {"line": 798, "column": 4}, "end": {"line": 800, "column": 5}}, "197": {"start": {"line": 799, "column": 6}, "end": {"line": 799, "column": 31}}, "198": {"start": {"line": 803, "column": 4}, "end": {"line": 808, "column": 5}}, "199": {"start": {"line": 804, "column": 6}, "end": {"line": 807, "column": 9}}, "200": {"start": {"line": 811, "column": 4}, "end": {"line": 811, "column": 25}}, "201": {"start": {"line": 813, "column": 4}, "end": {"line": 816, "column": 7}}, "202": {"start": {"line": 818, "column": 4}, "end": {"line": 822, "column": 7}}, "203": {"start": {"line": 830, "column": 0}, "end": {"line": 859, "column": 2}}, "204": {"start": {"line": 831, "column": 2}, "end": {"line": 858, "column": 3}}, "205": {"start": {"line": 833, "column": 17}, "end": {"line": 840, "column": 6}}, "206": {"start": {"line": 842, "column": 4}, "end": {"line": 844, "column": 5}}, "207": {"start": {"line": 843, "column": 6}, "end": {"line": 843, "column": 13}}, "208": {"start": {"line": 847, "column": 19}, "end": {"line": 847, "column": 55}}, "209": {"start": {"line": 850, "column": 4}, "end": {"line": 853, "column": 7}}, "210": {"start": {"line": 855, "column": 4}, "end": {"line": 855, "column": 37}}, "211": {"start": {"line": 857, "column": 4}, "end": {"line": 857, "column": 47}}, "212": {"start": {"line": 866, "column": 0}, "end": {"line": 936, "column": 2}}, "213": {"start": {"line": 867, "column": 2}, "end": {"line": 935, "column": 3}}, "214": {"start": {"line": 869, "column": 28}, "end": {"line": 869, "column": 48}}, "215": {"start": {"line": 870, "column": 22}, "end": {"line": 875, "column": 6}}, "216": {"start": {"line": 877, "column": 4}, "end": {"line": 879, "column": 5}}, "217": {"start": {"line": 878, "column": 6}, "end": {"line": 878, "column": 38}}, "218": {"start": {"line": 882, "column": 21}, "end": {"line": 882, "column": 35}}, "219": {"start": {"line": 883, "column": 4}, "end": {"line": 883, "column": 80}}, "220": {"start": {"line": 885, "column": 27}, "end": {"line": 894, "column": 5}}, "221": {"start": {"line": 896, "column": 4}, "end": {"line": 898, "column": 5}}, "222": {"start": {"line": 897, "column": 6}, "end": {"line": 897, "column": 37}}, "223": {"start": {"line": 900, "column": 19}, "end": {"line": 900, "column": 41}}, "224": {"start": {"line": 903, "column": 28}, "end": {"line": 917, "column": 5}}, "225": {"start": {"line": 919, "column": 4}, "end": {"line": 921, "column": 5}}, "226": {"start": {"line": 920, "column": 6}, "end": {"line": 920, "column": 32}}, "227": {"start": {"line": 923, "column": 4}, "end": {"line": 926, "column": 6}}, "228": {"start": {"line": 928, "column": 4}, "end": {"line": 928, "column": 46}}, "229": {"start": {"line": 931, "column": 4}, "end": {"line": 934, "column": 6}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 21}, "end": {"line": 18, "column": 22}}, "loc": {"start": {"line": 18, "column": 41}, "end": {"line": 142, "column": 1}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 149, "column": 19}, "end": {"line": 149, "column": 20}}, "loc": {"start": {"line": 149, "column": 39}, "end": {"line": 231, "column": 1}}, "line": 149}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 238, "column": 34}, "end": {"line": 238, "column": 35}}, "loc": {"start": {"line": 238, "column": 54}, "end": {"line": 369, "column": 1}}, "line": 238}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 376, "column": 22}, "end": {"line": 376, "column": 23}}, "loc": {"start": {"line": 376, "column": 42}, "end": {"line": 458, "column": 1}}, "line": 376}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 465, "column": 23}, "end": {"line": 465, "column": 24}}, "loc": {"start": {"line": 465, "column": 43}, "end": {"line": 548, "column": 1}}, "line": 465}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 555, "column": 21}, "end": {"line": 555, "column": 22}}, "loc": {"start": {"line": 555, "column": 41}, "end": {"line": 631, "column": 1}}, "line": 555}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 638, "column": 22}, "end": {"line": 638, "column": 23}}, "loc": {"start": {"line": 638, "column": 42}, "end": {"line": 729, "column": 1}}, "line": 638}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 736, "column": 21}, "end": {"line": 736, "column": 22}}, "loc": {"start": {"line": 736, "column": 41}, "end": {"line": 824, "column": 1}}, "line": 736}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 830, "column": 34}, "end": {"line": 830, "column": 35}}, "loc": {"start": {"line": 830, "column": 52}, "end": {"line": 859, "column": 1}}, "line": 830}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 866, "column": 30}, "end": {"line": 866, "column": 31}}, "loc": {"start": {"line": 866, "column": 46}, "end": {"line": 936, "column": 1}}, "line": 866}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 28, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 28, "column": 5}}, {"start": {}, "end": {}}], "line": 23}, "1": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 41, "column": 5}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 41, "column": 5}}, {"start": {}, "end": {}}], "line": 33}, "2": {"loc": {"start": {"line": 47, "column": 4}, "end": {"line": 69, "column": 5}}, "type": "if", "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 69, "column": 5}}, {"start": {"line": 51, "column": 9}, "end": {"line": 69, "column": 5}}], "line": 47}, "3": {"loc": {"start": {"line": 51, "column": 9}, "end": {"line": 69, "column": 5}}, "type": "if", "locations": [{"start": {"line": 51, "column": 9}, "end": {"line": 69, "column": 5}}, {"start": {"line": 55, "column": 9}, "end": {"line": 69, "column": 5}}], "line": 51}, "4": {"loc": {"start": {"line": 66, "column": 6}, "end": {"line": 68, "column": 7}}, "type": "if", "locations": [{"start": {"line": 66, "column": 6}, "end": {"line": 68, "column": 7}}, {"start": {}, "end": {}}], "line": 66}, "5": {"loc": {"start": {"line": 71, "column": 4}, "end": {"line": 79, "column": 5}}, "type": "if", "locations": [{"start": {"line": 71, "column": 4}, "end": {"line": 79, "column": 5}}, {"start": {}, "end": {}}], "line": 71}, "6": {"loc": {"start": {"line": 86, "column": 4}, "end": {"line": 89, "column": 5}}, "type": "if", "locations": [{"start": {"line": 86, "column": 4}, "end": {"line": 89, "column": 5}}, {"start": {}, "end": {}}], "line": 86}, "7": {"loc": {"start": {"line": 86, "column": 8}, "end": {"line": 87, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 33}}, {"start": {"line": 87, "column": 9}, "end": {"line": 87, "column": 38}}, {"start": {"line": 87, "column": 42}, "end": {"line": 87, "column": 82}}], "line": 86}, "8": {"loc": {"start": {"line": 102, "column": 19}, "end": {"line": 102, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 102, "column": 43}, "end": {"line": 102, "column": 54}}, {"start": {"line": 102, "column": 57}, "end": {"line": 102, "column": 61}}], "line": 102}, "9": {"loc": {"start": {"line": 103, "column": 19}, "end": {"line": 103, "column": 60}}, "type": "cond-expr", "locations": [{"start": {"line": 103, "column": 43}, "end": {"line": 103, "column": 53}}, {"start": {"line": 103, "column": 56}, "end": {"line": 103, "column": 60}}], "line": 103}, "10": {"loc": {"start": {"line": 113, "column": 4}, "end": {"line": 116, "column": 5}}, "type": "if", "locations": [{"start": {"line": 113, "column": 4}, "end": {"line": 116, "column": 5}}, {"start": {}, "end": {}}], "line": 113}, "11": {"loc": {"start": {"line": 120, "column": 15}, "end": {"line": 120, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 120, "column": 39}, "end": {"line": 120, "column": 47}}, {"start": {"line": 120, "column": 50}, "end": {"line": 120, "column": 63}}], "line": 120}, "12": {"loc": {"start": {"line": 132, "column": 4}, "end": {"line": 134, "column": 5}}, "type": "if", "locations": [{"start": {"line": 132, "column": 4}, "end": {"line": 134, "column": 5}}, {"start": {}, "end": {}}], "line": 132}, "13": {"loc": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 16}}, {"start": {"line": 132, "column": 20}, "end": {"line": 132, "column": 48}}], "line": 132}, "14": {"loc": {"start": {"line": 152, "column": 17}, "end": {"line": 152, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 17}, "end": {"line": 152, "column": 41}}, {"start": {"line": 152, "column": 45}, "end": {"line": 152, "column": 46}}], "line": 152}, "15": {"loc": {"start": {"line": 153, "column": 18}, "end": {"line": 153, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 153, "column": 18}, "end": {"line": 153, "column": 43}}, {"start": {"line": 153, "column": 47}, "end": {"line": 153, "column": 49}}], "line": 153}, "16": {"loc": {"start": {"line": 160, "column": 4}, "end": {"line": 162, "column": 5}}, "type": "if", "locations": [{"start": {"line": 160, "column": 4}, "end": {"line": 162, "column": 5}}, {"start": {}, "end": {}}], "line": 160}, "17": {"loc": {"start": {"line": 165, "column": 4}, "end": {"line": 167, "column": 5}}, "type": "if", "locations": [{"start": {"line": 165, "column": 4}, "end": {"line": 167, "column": 5}}, {"start": {}, "end": {}}], "line": 165}, "18": {"loc": {"start": {"line": 170, "column": 4}, "end": {"line": 174, "column": 5}}, "type": "if", "locations": [{"start": {"line": 170, "column": 4}, "end": {"line": 174, "column": 5}}, {"start": {}, "end": {}}], "line": 170}, "19": {"loc": {"start": {"line": 177, "column": 4}, "end": {"line": 182, "column": 5}}, "type": "if", "locations": [{"start": {"line": 177, "column": 4}, "end": {"line": 182, "column": 5}}, {"start": {}, "end": {}}], "line": 177}, "20": {"loc": {"start": {"line": 242, "column": 17}, "end": {"line": 242, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 242, "column": 17}, "end": {"line": 242, "column": 41}}, {"start": {"line": 242, "column": 45}, "end": {"line": 242, "column": 46}}], "line": 242}, "21": {"loc": {"start": {"line": 243, "column": 18}, "end": {"line": 243, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 243, "column": 18}, "end": {"line": 243, "column": 43}}, {"start": {"line": 243, "column": 47}, "end": {"line": 243, "column": 49}}], "line": 243}, "22": {"loc": {"start": {"line": 249, "column": 4}, "end": {"line": 254, "column": 5}}, "type": "if", "locations": [{"start": {"line": 249, "column": 4}, "end": {"line": 254, "column": 5}}, {"start": {}, "end": {}}], "line": 249}, "23": {"loc": {"start": {"line": 260, "column": 4}, "end": {"line": 283, "column": 5}}, "type": "if", "locations": [{"start": {"line": 260, "column": 4}, "end": {"line": 283, "column": 5}}, {"start": {"line": 264, "column": 9}, "end": {"line": 283, "column": 5}}], "line": 260}, "24": {"loc": {"start": {"line": 264, "column": 9}, "end": {"line": 283, "column": 5}}, "type": "if", "locations": [{"start": {"line": 264, "column": 9}, "end": {"line": 283, "column": 5}}, {"start": {"line": 268, "column": 9}, "end": {"line": 283, "column": 5}}], "line": 264}, "25": {"loc": {"start": {"line": 268, "column": 9}, "end": {"line": 283, "column": 5}}, "type": "if", "locations": [{"start": {"line": 268, "column": 9}, "end": {"line": 283, "column": 5}}, {"start": {"line": 272, "column": 9}, "end": {"line": 283, "column": 5}}], "line": 268}, "26": {"loc": {"start": {"line": 280, "column": 6}, "end": {"line": 282, "column": 7}}, "type": "if", "locations": [{"start": {"line": 280, "column": 6}, "end": {"line": 282, "column": 7}}, {"start": {}, "end": {}}], "line": 280}, "27": {"loc": {"start": {"line": 285, "column": 4}, "end": {"line": 290, "column": 5}}, "type": "if", "locations": [{"start": {"line": 285, "column": 4}, "end": {"line": 290, "column": 5}}, {"start": {}, "end": {}}], "line": 285}, "28": {"loc": {"start": {"line": 298, "column": 4}, "end": {"line": 308, "column": 5}}, "type": "if", "locations": [{"start": {"line": 298, "column": 4}, "end": {"line": 308, "column": 5}}, {"start": {"line": 300, "column": 11}, "end": {"line": 308, "column": 5}}], "line": 298}, "29": {"loc": {"start": {"line": 302, "column": 6}, "end": {"line": 307, "column": 7}}, "type": "if", "locations": [{"start": {"line": 302, "column": 6}, "end": {"line": 307, "column": 7}}, {"start": {}, "end": {}}], "line": 302}, "30": {"loc": {"start": {"line": 302, "column": 10}, "end": {"line": 302, "column": 79}}, "type": "binary-expr", "locations": [{"start": {"line": 302, "column": 10}, "end": {"line": 302, "column": 35}}, {"start": {"line": 302, "column": 39}, "end": {"line": 302, "column": 79}}], "line": 302}, "31": {"loc": {"start": {"line": 311, "column": 4}, "end": {"line": 313, "column": 5}}, "type": "if", "locations": [{"start": {"line": 311, "column": 4}, "end": {"line": 313, "column": 5}}, {"start": {}, "end": {}}], "line": 311}, "32": {"loc": {"start": {"line": 316, "column": 4}, "end": {"line": 320, "column": 5}}, "type": "if", "locations": [{"start": {"line": 316, "column": 4}, "end": {"line": 320, "column": 5}}, {"start": {}, "end": {}}], "line": 316}, "33": {"loc": {"start": {"line": 401, "column": 4}, "end": {"line": 406, "column": 5}}, "type": "if", "locations": [{"start": {"line": 401, "column": 4}, "end": {"line": 406, "column": 5}}, {"start": {}, "end": {}}], "line": 401}, "34": {"loc": {"start": {"line": 412, "column": 4}, "end": {"line": 438, "column": 5}}, "type": "if", "locations": [{"start": {"line": 412, "column": 4}, "end": {"line": 438, "column": 5}}, {"start": {"line": 416, "column": 9}, "end": {"line": 438, "column": 5}}], "line": 412}, "35": {"loc": {"start": {"line": 416, "column": 9}, "end": {"line": 438, "column": 5}}, "type": "if", "locations": [{"start": {"line": 416, "column": 9}, "end": {"line": 438, "column": 5}}, {"start": {"line": 420, "column": 9}, "end": {"line": 438, "column": 5}}], "line": 416}, "36": {"loc": {"start": {"line": 420, "column": 9}, "end": {"line": 438, "column": 5}}, "type": "if", "locations": [{"start": {"line": 420, "column": 9}, "end": {"line": 438, "column": 5}}, {"start": {}, "end": {}}], "line": 420}, "37": {"loc": {"start": {"line": 422, "column": 6}, "end": {"line": 437, "column": 7}}, "type": "if", "locations": [{"start": {"line": 422, "column": 6}, "end": {"line": 437, "column": 7}}, {"start": {"line": 426, "column": 11}, "end": {"line": 437, "column": 7}}], "line": 422}, "38": {"loc": {"start": {"line": 434, "column": 8}, "end": {"line": 436, "column": 9}}, "type": "if", "locations": [{"start": {"line": 434, "column": 8}, "end": {"line": 436, "column": 9}}, {"start": {}, "end": {}}], "line": 434}, "39": {"loc": {"start": {"line": 440, "column": 4}, "end": {"line": 445, "column": 5}}, "type": "if", "locations": [{"start": {"line": 440, "column": 4}, "end": {"line": 445, "column": 5}}, {"start": {}, "end": {}}], "line": 440}, "40": {"loc": {"start": {"line": 480, "column": 4}, "end": {"line": 485, "column": 5}}, "type": "if", "locations": [{"start": {"line": 480, "column": 4}, "end": {"line": 485, "column": 5}}, {"start": {}, "end": {}}], "line": 480}, "41": {"loc": {"start": {"line": 491, "column": 4}, "end": {"line": 517, "column": 5}}, "type": "if", "locations": [{"start": {"line": 491, "column": 4}, "end": {"line": 517, "column": 5}}, {"start": {"line": 495, "column": 9}, "end": {"line": 517, "column": 5}}], "line": 491}, "42": {"loc": {"start": {"line": 495, "column": 9}, "end": {"line": 517, "column": 5}}, "type": "if", "locations": [{"start": {"line": 495, "column": 9}, "end": {"line": 517, "column": 5}}, {"start": {"line": 499, "column": 9}, "end": {"line": 517, "column": 5}}], "line": 495}, "43": {"loc": {"start": {"line": 499, "column": 9}, "end": {"line": 517, "column": 5}}, "type": "if", "locations": [{"start": {"line": 499, "column": 9}, "end": {"line": 517, "column": 5}}, {"start": {}, "end": {}}], "line": 499}, "44": {"loc": {"start": {"line": 501, "column": 6}, "end": {"line": 516, "column": 7}}, "type": "if", "locations": [{"start": {"line": 501, "column": 6}, "end": {"line": 516, "column": 7}}, {"start": {"line": 505, "column": 11}, "end": {"line": 516, "column": 7}}], "line": 501}, "45": {"loc": {"start": {"line": 513, "column": 8}, "end": {"line": 515, "column": 9}}, "type": "if", "locations": [{"start": {"line": 513, "column": 8}, "end": {"line": 515, "column": 9}}, {"start": {}, "end": {}}], "line": 513}, "46": {"loc": {"start": {"line": 519, "column": 4}, "end": {"line": 524, "column": 5}}, "type": "if", "locations": [{"start": {"line": 519, "column": 4}, "end": {"line": 524, "column": 5}}, {"start": {}, "end": {}}], "line": 519}, "47": {"loc": {"start": {"line": 527, "column": 4}, "end": {"line": 532, "column": 5}}, "type": "if", "locations": [{"start": {"line": 527, "column": 4}, "end": {"line": 532, "column": 5}}, {"start": {}, "end": {}}], "line": 527}, "48": {"loc": {"start": {"line": 561, "column": 4}, "end": {"line": 566, "column": 5}}, "type": "if", "locations": [{"start": {"line": 561, "column": 4}, "end": {"line": 566, "column": 5}}, {"start": {}, "end": {}}], "line": 561}, "49": {"loc": {"start": {"line": 582, "column": 4}, "end": {"line": 587, "column": 5}}, "type": "if", "locations": [{"start": {"line": 582, "column": 4}, "end": {"line": 587, "column": 5}}, {"start": {}, "end": {}}], "line": 582}, "50": {"loc": {"start": {"line": 590, "column": 4}, "end": {"line": 595, "column": 5}}, "type": "if", "locations": [{"start": {"line": 590, "column": 4}, "end": {"line": 595, "column": 5}}, {"start": {}, "end": {}}], "line": 590}, "51": {"loc": {"start": {"line": 602, "column": 21}, "end": {"line": 602, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 602, "column": 45}, "end": {"line": 602, "column": 58}}, {"start": {"line": 602, "column": 61}, "end": {"line": 602, "column": 65}}], "line": 602}, "52": {"loc": {"start": {"line": 606, "column": 4}, "end": {"line": 609, "column": 5}}, "type": "if", "locations": [{"start": {"line": 606, "column": 4}, "end": {"line": 609, "column": 5}}, {"start": {}, "end": {}}], "line": 606}, "53": {"loc": {"start": {"line": 615, "column": 15}, "end": {"line": 615, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 615, "column": 39}, "end": {"line": 615, "column": 46}}, {"start": {"line": 615, "column": 49}, "end": {"line": 615, "column": 56}}], "line": 615}, "54": {"loc": {"start": {"line": 652, "column": 4}, "end": {"line": 657, "column": 5}}, "type": "if", "locations": [{"start": {"line": 652, "column": 4}, "end": {"line": 657, "column": 5}}, {"start": {}, "end": {}}], "line": 652}, "55": {"loc": {"start": {"line": 660, "column": 4}, "end": {"line": 665, "column": 5}}, "type": "if", "locations": [{"start": {"line": 660, "column": 4}, "end": {"line": 665, "column": 5}}, {"start": {}, "end": {}}], "line": 660}, "56": {"loc": {"start": {"line": 671, "column": 4}, "end": {"line": 695, "column": 5}}, "type": "if", "locations": [{"start": {"line": 671, "column": 4}, "end": {"line": 695, "column": 5}}, {"start": {"line": 675, "column": 9}, "end": {"line": 695, "column": 5}}], "line": 671}, "57": {"loc": {"start": {"line": 675, "column": 9}, "end": {"line": 695, "column": 5}}, "type": "if", "locations": [{"start": {"line": 675, "column": 9}, "end": {"line": 695, "column": 5}}, {"start": {"line": 679, "column": 9}, "end": {"line": 695, "column": 5}}], "line": 675}, "58": {"loc": {"start": {"line": 679, "column": 9}, "end": {"line": 695, "column": 5}}, "type": "if", "locations": [{"start": {"line": 679, "column": 9}, "end": {"line": 695, "column": 5}}, {"start": {"line": 683, "column": 9}, "end": {"line": 695, "column": 5}}], "line": 679}, "59": {"loc": {"start": {"line": 692, "column": 6}, "end": {"line": 694, "column": 7}}, "type": "if", "locations": [{"start": {"line": 692, "column": 6}, "end": {"line": 694, "column": 7}}, {"start": {}, "end": {}}], "line": 692}, "60": {"loc": {"start": {"line": 697, "column": 4}, "end": {"line": 702, "column": 5}}, "type": "if", "locations": [{"start": {"line": 697, "column": 4}, "end": {"line": 702, "column": 5}}, {"start": {}, "end": {}}], "line": 697}, "61": {"loc": {"start": {"line": 750, "column": 4}, "end": {"line": 755, "column": 5}}, "type": "if", "locations": [{"start": {"line": 750, "column": 4}, "end": {"line": 755, "column": 5}}, {"start": {}, "end": {}}], "line": 750}, "62": {"loc": {"start": {"line": 761, "column": 4}, "end": {"line": 785, "column": 5}}, "type": "if", "locations": [{"start": {"line": 761, "column": 4}, "end": {"line": 785, "column": 5}}, {"start": {"line": 765, "column": 9}, "end": {"line": 785, "column": 5}}], "line": 761}, "63": {"loc": {"start": {"line": 765, "column": 9}, "end": {"line": 785, "column": 5}}, "type": "if", "locations": [{"start": {"line": 765, "column": 9}, "end": {"line": 785, "column": 5}}, {"start": {"line": 769, "column": 9}, "end": {"line": 785, "column": 5}}], "line": 765}, "64": {"loc": {"start": {"line": 769, "column": 9}, "end": {"line": 785, "column": 5}}, "type": "if", "locations": [{"start": {"line": 769, "column": 9}, "end": {"line": 785, "column": 5}}, {"start": {"line": 773, "column": 9}, "end": {"line": 785, "column": 5}}], "line": 769}, "65": {"loc": {"start": {"line": 782, "column": 6}, "end": {"line": 784, "column": 7}}, "type": "if", "locations": [{"start": {"line": 782, "column": 6}, "end": {"line": 784, "column": 7}}, {"start": {}, "end": {}}], "line": 782}, "66": {"loc": {"start": {"line": 787, "column": 4}, "end": {"line": 792, "column": 5}}, "type": "if", "locations": [{"start": {"line": 787, "column": 4}, "end": {"line": 792, "column": 5}}, {"start": {}, "end": {}}], "line": 787}, "67": {"loc": {"start": {"line": 798, "column": 4}, "end": {"line": 800, "column": 5}}, "type": "if", "locations": [{"start": {"line": 798, "column": 4}, "end": {"line": 800, "column": 5}}, {"start": {}, "end": {}}], "line": 798}, "68": {"loc": {"start": {"line": 803, "column": 4}, "end": {"line": 808, "column": 5}}, "type": "if", "locations": [{"start": {"line": 803, "column": 4}, "end": {"line": 808, "column": 5}}, {"start": {}, "end": {}}], "line": 803}, "69": {"loc": {"start": {"line": 842, "column": 4}, "end": {"line": 844, "column": 5}}, "type": "if", "locations": [{"start": {"line": 842, "column": 4}, "end": {"line": 844, "column": 5}}, {"start": {}, "end": {}}], "line": 842}, "70": {"loc": {"start": {"line": 842, "column": 8}, "end": {"line": 842, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 842, "column": 8}, "end": {"line": 842, "column": 13}}, {"start": {"line": 842, "column": 17}, "end": {"line": 842, "column": 43}}], "line": 842}, "71": {"loc": {"start": {"line": 877, "column": 4}, "end": {"line": 879, "column": 5}}, "type": "if", "locations": [{"start": {"line": 877, "column": 4}, "end": {"line": 879, "column": 5}}, {"start": {}, "end": {}}], "line": 877}, "72": {"loc": {"start": {"line": 896, "column": 4}, "end": {"line": 898, "column": 5}}, "type": "if", "locations": [{"start": {"line": 896, "column": 4}, "end": {"line": 898, "column": 5}}, {"start": {}, "end": {}}], "line": 896}, "73": {"loc": {"start": {"line": 896, "column": 8}, "end": {"line": 896, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 896, "column": 8}, "end": {"line": 896, "column": 28}}, {"start": {"line": 896, "column": 32}, "end": {"line": 896, "column": 55}}], "line": 896}, "74": {"loc": {"start": {"line": 919, "column": 4}, "end": {"line": 921, "column": 5}}, "type": "if", "locations": [{"start": {"line": 919, "column": 4}, "end": {"line": 921, "column": 5}}, {"start": {}, "end": {}}], "line": 919}, "75": {"loc": {"start": {"line": 924, "column": 15}, "end": {"line": 924, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 924, "column": 15}, "end": {"line": 924, "column": 43}}, {"start": {"line": 924, "column": 47}, "end": {"line": 924, "column": 49}}], "line": 924}, "76": {"loc": {"start": {"line": 925, "column": 28}, "end": {"line": 925, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 925, "column": 28}, "end": {"line": 925, "column": 69}}, {"start": {"line": 925, "column": 73}, "end": {"line": 925, "column": 75}}], "line": 925}}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 13, "37": 1, "38": 1, "39": 1, "40": 1, "41": 1, "42": 1, "43": 1, "44": 0, "45": 1, "46": 0, "47": 1, "48": 0, "49": 1, "50": 1, "51": 1, "52": 1, "53": 1, "54": 0, "55": 13, "56": 1, "57": 1, "58": 1, "59": 1, "60": 1, "61": 1, "62": 1, "63": 1, "64": 0, "65": 1, "66": 1, "67": 0, "68": 1, "69": 0, "70": 1, "71": 1, "72": 0, "73": 0, "74": 0, "75": 1, "76": 0, "77": 1, "78": 1, "79": 0, "80": 1, "81": 0, "82": 1, "83": 0, "84": 1, "85": 0, "86": 1, "87": 1, "88": 1, "89": 0, "90": 13, "91": 2, "92": 2, "93": 2, "94": 2, "95": 1, "96": 1, "97": 1, "98": 0, "99": 1, "100": 1, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 1, "108": 0, "109": 1, "110": 0, "111": 13, "112": 2, "113": 2, "114": 2, "115": 2, "116": 1, "117": 1, "118": 1, "119": 0, "120": 1, "121": 1, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 1, "129": 0, "130": 1, "131": 0, "132": 1, "133": 1, "134": 1, "135": 1, "136": 0, "137": 13, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 13, "154": 1, "155": 1, "156": 1, "157": 1, "158": 0, "159": 1, "160": 1, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 13, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0, "183": 0, "184": 0, "185": 0, "186": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "193": 0, "194": 0, "195": 0, "196": 0, "197": 0, "198": 0, "199": 0, "200": 0, "201": 0, "202": 0, "203": 13, "204": 0, "205": 0, "206": 0, "207": 0, "208": 0, "209": 0, "210": 0, "211": 0, "212": 13, "213": 0, "214": 0, "215": 0, "216": 0, "217": 0, "218": 0, "219": 0, "220": 0, "221": 0, "222": 0, "223": 0, "224": 0, "225": 0, "226": 0, "227": 0, "228": 0, "229": 0}, "f": {"0": 0, "1": 1, "2": 1, "3": 2, "4": 2, "5": 0, "6": 1, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [1, 1], "15": [1, 1], "16": [0, 1], "17": [0, 1], "18": [0, 1], "19": [1, 0], "20": [1, 1], "21": [1, 1], "22": [0, 1], "23": [0, 1], "24": [0, 1], "25": [1, 0], "26": [0, 0], "27": [0, 1], "28": [0, 1], "29": [0, 1], "30": [1, 1], "31": [0, 1], "32": [0, 1], "33": [1, 1], "34": [0, 1], "35": [1, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 1], "40": [1, 1], "41": [0, 1], "42": [1, 0], "43": [0, 0], "44": [0, 0], "45": [0, 0], "46": [0, 1], "47": [0, 1], "48": [0, 0], "49": [0, 0], "50": [0, 0], "51": [0, 0], "52": [0, 0], "53": [0, 0], "54": [0, 1], "55": [1, 0], "56": [0, 0], "57": [0, 0], "58": [0, 0], "59": [0, 0], "60": [0, 0], "61": [0, 0], "62": [0, 0], "63": [0, 0], "64": [0, 0], "65": [0, 0], "66": [0, 0], "67": [0, 0], "68": [0, 0], "69": [0, 0], "70": [0, 0], "71": [0, 0], "72": [0, 0], "73": [0, 0], "74": [0, 0], "75": [0, 0], "76": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "ed0109b0594ef2b1d4dc76aa79ad41f7cadc0ccc"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\knowledge.controller.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\knowledge.controller.js", "statementMap": {"0": {"start": {"line": 7, "column": 59}, "end": {"line": 7, "column": 79}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 35}}, "2": {"start": {"line": 15, "column": 0}, "end": {"line": 61, "column": 2}}, "3": {"start": {"line": 16, "column": 2}, "end": {"line": 60, "column": 3}}, "4": {"start": {"line": 17, "column": 69}, "end": {"line": 17, "column": 77}}, "5": {"start": {"line": 20, "column": 4}, "end": {"line": 25, "column": 5}}, "6": {"start": {"line": 21, "column": 6}, "end": {"line": 24, "column": 9}}, "7": {"start": {"line": 28, "column": 26}, "end": {"line": 37, "column": 6}}, "8": {"start": {"line": 40, "column": 4}, "end": {"line": 47, "column": 5}}, "9": {"start": {"line": 41, "column": 6}, "end": {"line": 46, "column": 9}}, "10": {"start": {"line": 49, "column": 4}, "end": {"line": 53, "column": 7}}, "11": {"start": {"line": 55, "column": 4}, "end": {"line": 59, "column": 7}}, "12": {"start": {"line": 68, "column": 0}, "end": {"line": 144, "column": 2}}, "13": {"start": {"line": 69, "column": 2}, "end": {"line": 143, "column": 3}}, "14": {"start": {"line": 70, "column": 29}, "end": {"line": 70, "column": 38}}, "15": {"start": {"line": 71, "column": 17}, "end": {"line": 71, "column": 46}}, "16": {"start": {"line": 72, "column": 18}, "end": {"line": 72, "column": 49}}, "17": {"start": {"line": 73, "column": 19}, "end": {"line": 73, "column": 37}}, "18": {"start": {"line": 76, "column": 28}, "end": {"line": 76, "column": 30}}, "19": {"start": {"line": 79, "column": 4}, "end": {"line": 81, "column": 5}}, "20": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 34}}, "21": {"start": {"line": 84, "column": 4}, "end": {"line": 89, "column": 5}}, "22": {"start": {"line": 85, "column": 6}, "end": {"line": 88, "column": 8}}, "23": {"start": {"line": 92, "column": 4}, "end": {"line": 105, "column": 5}}, "24": {"start": {"line": 94, "column": 25}, "end": {"line": 97, "column": 8}}, "25": {"start": {"line": 99, "column": 28}, "end": {"line": 99, "column": 78}}, "26": {"start": {"line": 99, "column": 53}, "end": {"line": 99, "column": 77}}, "27": {"start": {"line": 101, "column": 6}, "end": {"line": 104, "column": 8}}, "28": {"start": {"line": 108, "column": 44}, "end": {"line": 120, "column": 6}}, "29": {"start": {"line": 123, "column": 23}, "end": {"line": 123, "column": 47}}, "30": {"start": {"line": 125, "column": 4}, "end": {"line": 136, "column": 7}}, "31": {"start": {"line": 138, "column": 4}, "end": {"line": 142, "column": 7}}, "32": {"start": {"line": 151, "column": 0}, "end": {"line": 222, "column": 2}}, "33": {"start": {"line": 152, "column": 2}, "end": {"line": 221, "column": 3}}, "34": {"start": {"line": 153, "column": 19}, "end": {"line": 153, "column": 29}}, "35": {"start": {"line": 156, "column": 26}, "end": {"line": 164, "column": 6}}, "36": {"start": {"line": 166, "column": 4}, "end": {"line": 171, "column": 5}}, "37": {"start": {"line": 167, "column": 6}, "end": {"line": 170, "column": 9}}, "38": {"start": {"line": 174, "column": 4}, "end": {"line": 188, "column": 5}}, "39": {"start": {"line": 175, "column": 24}, "end": {"line": 180, "column": 8}}, "40": {"start": {"line": 182, "column": 6}, "end": {"line": 187, "column": 7}}, "41": {"start": {"line": 183, "column": 8}, "end": {"line": 186, "column": 11}}, "42": {"start": {"line": 191, "column": 22}, "end": {"line": 193, "column": 6}}, "43": {"start": {"line": 196, "column": 23}, "end": {"line": 205, "column": 6}}, "44": {"start": {"line": 207, "column": 4}, "end": {"line": 214, "column": 7}}, "45": {"start": {"line": 216, "column": 4}, "end": {"line": 220, "column": 7}}, "46": {"start": {"line": 229, "column": 0}, "end": {"line": 291, "column": 2}}, "47": {"start": {"line": 230, "column": 2}, "end": {"line": 290, "column": 3}}, "48": {"start": {"line": 231, "column": 19}, "end": {"line": 231, "column": 29}}, "49": {"start": {"line": 232, "column": 63}, "end": {"line": 232, "column": 71}}, "50": {"start": {"line": 235, "column": 26}, "end": {"line": 235, "column": 58}}, "51": {"start": {"line": 237, "column": 4}, "end": {"line": 242, "column": 5}}, "52": {"start": {"line": 238, "column": 6}, "end": {"line": 241, "column": 9}}, "53": {"start": {"line": 245, "column": 4}, "end": {"line": 269, "column": 5}}, "54": {"start": {"line": 247, "column": 6}, "end": {"line": 252, "column": 7}}, "55": {"start": {"line": 248, "column": 8}, "end": {"line": 251, "column": 11}}, "56": {"start": {"line": 255, "column": 24}, "end": {"line": 261, "column": 8}}, "57": {"start": {"line": 263, "column": 6}, "end": {"line": 268, "column": 7}}, "58": {"start": {"line": 264, "column": 8}, "end": {"line": 267, "column": 11}}, "59": {"start": {"line": 272, "column": 4}, "end": {"line": 277, "column": 7}}, "60": {"start": {"line": 279, "column": 4}, "end": {"line": 283, "column": 7}}, "61": {"start": {"line": 285, "column": 4}, "end": {"line": 289, "column": 7}}, "62": {"start": {"line": 298, "column": 0}, "end": {"line": 370, "column": 2}}, "63": {"start": {"line": 299, "column": 2}, "end": {"line": 369, "column": 3}}, "64": {"start": {"line": 300, "column": 19}, "end": {"line": 300, "column": 29}}, "65": {"start": {"line": 303, "column": 26}, "end": {"line": 303, "column": 58}}, "66": {"start": {"line": 305, "column": 4}, "end": {"line": 310, "column": 5}}, "67": {"start": {"line": 306, "column": 6}, "end": {"line": 309, "column": 9}}, "68": {"start": {"line": 313, "column": 4}, "end": {"line": 337, "column": 5}}, "69": {"start": {"line": 315, "column": 6}, "end": {"line": 320, "column": 7}}, "70": {"start": {"line": 316, "column": 8}, "end": {"line": 319, "column": 11}}, "71": {"start": {"line": 323, "column": 24}, "end": {"line": 329, "column": 8}}, "72": {"start": {"line": 331, "column": 6}, "end": {"line": 336, "column": 7}}, "73": {"start": {"line": 332, "column": 8}, "end": {"line": 335, "column": 11}}, "74": {"start": {"line": 340, "column": 22}, "end": {"line": 342, "column": 6}}, "75": {"start": {"line": 344, "column": 4}, "end": {"line": 349, "column": 5}}, "76": {"start": {"line": 345, "column": 6}, "end": {"line": 348, "column": 9}}, "77": {"start": {"line": 352, "column": 4}, "end": {"line": 354, "column": 7}}, "78": {"start": {"line": 357, "column": 4}, "end": {"line": 357, "column": 34}}, "79": {"start": {"line": 359, "column": 4}, "end": {"line": 362, "column": 7}}, "80": {"start": {"line": 364, "column": 4}, "end": {"line": 368, "column": 7}}, "81": {"start": {"line": 377, "column": 0}, "end": {"line": 446, "column": 2}}, "82": {"start": {"line": 378, "column": 2}, "end": {"line": 445, "column": 3}}, "83": {"start": {"line": 379, "column": 19}, "end": {"line": 379, "column": 29}}, "84": {"start": {"line": 382, "column": 26}, "end": {"line": 382, "column": 58}}, "85": {"start": {"line": 384, "column": 4}, "end": {"line": 389, "column": 5}}, "86": {"start": {"line": 385, "column": 6}, "end": {"line": 388, "column": 9}}, "87": {"start": {"line": 392, "column": 4}, "end": {"line": 416, "column": 5}}, "88": {"start": {"line": 394, "column": 6}, "end": {"line": 399, "column": 7}}, "89": {"start": {"line": 395, "column": 8}, "end": {"line": 398, "column": 11}}, "90": {"start": {"line": 402, "column": 24}, "end": {"line": 408, "column": 8}}, "91": {"start": {"line": 410, "column": 6}, "end": {"line": 415, "column": 7}}, "92": {"start": {"line": 411, "column": 8}, "end": {"line": 414, "column": 11}}, "93": {"start": {"line": 419, "column": 23}, "end": {"line": 433, "column": 6}}, "94": {"start": {"line": 435, "column": 4}, "end": {"line": 438, "column": 7}}, "95": {"start": {"line": 440, "column": 4}, "end": {"line": 444, "column": 7}}, "96": {"start": {"line": 453, "column": 0}, "end": {"line": 564, "column": 2}}, "97": {"start": {"line": 454, "column": 2}, "end": {"line": 563, "column": 3}}, "98": {"start": {"line": 455, "column": 19}, "end": {"line": 455, "column": 29}}, "99": {"start": {"line": 456, "column": 37}, "end": {"line": 456, "column": 45}}, "100": {"start": {"line": 459, "column": 4}, "end": {"line": 464, "column": 5}}, "101": {"start": {"line": 460, "column": 6}, "end": {"line": 463, "column": 9}}, "102": {"start": {"line": 467, "column": 26}, "end": {"line": 467, "column": 58}}, "103": {"start": {"line": 469, "column": 4}, "end": {"line": 474, "column": 5}}, "104": {"start": {"line": 470, "column": 6}, "end": {"line": 473, "column": 9}}, "105": {"start": {"line": 477, "column": 4}, "end": {"line": 501, "column": 5}}, "106": {"start": {"line": 479, "column": 6}, "end": {"line": 484, "column": 7}}, "107": {"start": {"line": 480, "column": 8}, "end": {"line": 483, "column": 11}}, "108": {"start": {"line": 487, "column": 24}, "end": {"line": 493, "column": 8}}, "109": {"start": {"line": 495, "column": 6}, "end": {"line": 500, "column": 7}}, "110": {"start": {"line": 496, "column": 8}, "end": {"line": 499, "column": 11}}, "111": {"start": {"line": 504, "column": 17}, "end": {"line": 504, "column": 45}}, "112": {"start": {"line": 506, "column": 4}, "end": {"line": 511, "column": 5}}, "113": {"start": {"line": 507, "column": 6}, "end": {"line": 510, "column": 9}}, "114": {"start": {"line": 514, "column": 27}, "end": {"line": 519, "column": 6}}, "115": {"start": {"line": 521, "column": 4}, "end": {"line": 526, "column": 5}}, "116": {"start": {"line": 522, "column": 6}, "end": {"line": 525, "column": 9}}, "117": {"start": {"line": 529, "column": 19}, "end": {"line": 534, "column": 6}}, "118": {"start": {"line": 537, "column": 23}, "end": {"line": 550, "column": 6}}, "119": {"start": {"line": 552, "column": 4}, "end": {"line": 556, "column": 7}}, "120": {"start": {"line": 558, "column": 4}, "end": {"line": 562, "column": 7}}, "121": {"start": {"line": 571, "column": 0}, "end": {"line": 678, "column": 2}}, "122": {"start": {"line": 572, "column": 2}, "end": {"line": 677, "column": 3}}, "123": {"start": {"line": 573, "column": 27}, "end": {"line": 573, "column": 37}}, "124": {"start": {"line": 574, "column": 28}, "end": {"line": 574, "column": 36}}, "125": {"start": {"line": 577, "column": 4}, "end": {"line": 582, "column": 5}}, "126": {"start": {"line": 578, "column": 6}, "end": {"line": 581, "column": 9}}, "127": {"start": {"line": 585, "column": 26}, "end": {"line": 585, "column": 58}}, "128": {"start": {"line": 587, "column": 4}, "end": {"line": 592, "column": 5}}, "129": {"start": {"line": 588, "column": 6}, "end": {"line": 591, "column": 9}}, "130": {"start": {"line": 595, "column": 4}, "end": {"line": 619, "column": 5}}, "131": {"start": {"line": 597, "column": 6}, "end": {"line": 602, "column": 7}}, "132": {"start": {"line": 598, "column": 8}, "end": {"line": 601, "column": 11}}, "133": {"start": {"line": 605, "column": 24}, "end": {"line": 611, "column": 8}}, "134": {"start": {"line": 613, "column": 6}, "end": {"line": 618, "column": 7}}, "135": {"start": {"line": 614, "column": 8}, "end": {"line": 617, "column": 11}}, "136": {"start": {"line": 622, "column": 19}, "end": {"line": 627, "column": 6}}, "137": {"start": {"line": 629, "column": 4}, "end": {"line": 634, "column": 5}}, "138": {"start": {"line": 630, "column": 6}, "end": {"line": 633, "column": 9}}, "139": {"start": {"line": 637, "column": 4}, "end": {"line": 642, "column": 5}}, "140": {"start": {"line": 638, "column": 6}, "end": {"line": 641, "column": 9}}, "141": {"start": {"line": 645, "column": 4}, "end": {"line": 648, "column": 7}}, "142": {"start": {"line": 651, "column": 23}, "end": {"line": 664, "column": 6}}, "143": {"start": {"line": 666, "column": 4}, "end": {"line": 670, "column": 7}}, "144": {"start": {"line": 672, "column": 4}, "end": {"line": 676, "column": 7}}, "145": {"start": {"line": 685, "column": 0}, "end": {"line": 763, "column": 2}}, "146": {"start": {"line": 686, "column": 2}, "end": {"line": 762, "column": 3}}, "147": {"start": {"line": 687, "column": 27}, "end": {"line": 687, "column": 37}}, "148": {"start": {"line": 690, "column": 26}, "end": {"line": 690, "column": 58}}, "149": {"start": {"line": 692, "column": 4}, "end": {"line": 697, "column": 5}}, "150": {"start": {"line": 693, "column": 6}, "end": {"line": 696, "column": 9}}, "151": {"start": {"line": 700, "column": 4}, "end": {"line": 724, "column": 5}}, "152": {"start": {"line": 702, "column": 6}, "end": {"line": 707, "column": 7}}, "153": {"start": {"line": 703, "column": 8}, "end": {"line": 706, "column": 11}}, "154": {"start": {"line": 710, "column": 24}, "end": {"line": 716, "column": 8}}, "155": {"start": {"line": 718, "column": 6}, "end": {"line": 723, "column": 7}}, "156": {"start": {"line": 719, "column": 8}, "end": {"line": 722, "column": 11}}, "157": {"start": {"line": 727, "column": 19}, "end": {"line": 732, "column": 6}}, "158": {"start": {"line": 734, "column": 4}, "end": {"line": 739, "column": 5}}, "159": {"start": {"line": 735, "column": 6}, "end": {"line": 738, "column": 9}}, "160": {"start": {"line": 742, "column": 4}, "end": {"line": 747, "column": 5}}, "161": {"start": {"line": 743, "column": 6}, "end": {"line": 746, "column": 9}}, "162": {"start": {"line": 750, "column": 4}, "end": {"line": 750, "column": 27}}, "163": {"start": {"line": 752, "column": 4}, "end": {"line": 755, "column": 7}}, "164": {"start": {"line": 757, "column": 4}, "end": {"line": 761, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 30}, "end": {"line": 15, "column": 31}}, "loc": {"start": {"line": 15, "column": 50}, "end": {"line": 61, "column": 1}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 68, "column": 28}, "end": {"line": 68, "column": 29}}, "loc": {"start": {"line": 68, "column": 48}, "end": {"line": 144, "column": 1}}, "line": 68}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 99, "column": 43}, "end": {"line": 99, "column": 44}}, "loc": {"start": {"line": 99, "column": 53}, "end": {"line": 99, "column": 77}}, "line": 99}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 151, "column": 31}, "end": {"line": 151, "column": 32}}, "loc": {"start": {"line": 151, "column": 51}, "end": {"line": 222, "column": 1}}, "line": 151}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 229, "column": 30}, "end": {"line": 229, "column": 31}}, "loc": {"start": {"line": 229, "column": 50}, "end": {"line": 291, "column": 1}}, "line": 229}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 298, "column": 30}, "end": {"line": 298, "column": 31}}, "loc": {"start": {"line": 298, "column": 50}, "end": {"line": 370, "column": 1}}, "line": 298}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 377, "column": 33}, "end": {"line": 377, "column": 34}}, "loc": {"start": {"line": 377, "column": 53}, "end": {"line": 446, "column": 1}}, "line": 377}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 453, "column": 33}, "end": {"line": 453, "column": 34}}, "loc": {"start": {"line": 453, "column": 53}, "end": {"line": 564, "column": 1}}, "line": 453}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 571, "column": 36}, "end": {"line": 571, "column": 37}}, "loc": {"start": {"line": 571, "column": 56}, "end": {"line": 678, "column": 1}}, "line": 571}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 685, "column": 36}, "end": {"line": 685, "column": 37}}, "loc": {"start": {"line": 685, "column": 56}, "end": {"line": 763, "column": 1}}, "line": 685}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 4}, "end": {"line": 25, "column": 5}}, "type": "if", "locations": [{"start": {"line": 20, "column": 4}, "end": {"line": 25, "column": 5}}, {"start": {}, "end": {}}], "line": 20}, "1": {"loc": {"start": {"line": 20, "column": 8}, "end": {"line": 20, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 8}, "end": {"line": 20, "column": 25}}, {"start": {"line": 20, "column": 29}, "end": {"line": 20, "column": 54}}], "line": 20}, "2": {"loc": {"start": {"line": 31, "column": 12}, "end": {"line": 31, "column": 26}}, "type": "binary-expr", "locations": [{"start": {"line": 31, "column": 12}, "end": {"line": 31, "column": 16}}, {"start": {"line": 31, "column": 20}, "end": {"line": 31, "column": 26}}], "line": 31}, "3": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 47, "column": 5}}, "type": "if", "locations": [{"start": {"line": 40, "column": 4}, "end": {"line": 47, "column": 5}}, {"start": {}, "end": {}}], "line": 40}, "4": {"loc": {"start": {"line": 71, "column": 17}, "end": {"line": 71, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 71, "column": 17}, "end": {"line": 71, "column": 41}}, {"start": {"line": 71, "column": 45}, "end": {"line": 71, "column": 46}}], "line": 71}, "5": {"loc": {"start": {"line": 72, "column": 18}, "end": {"line": 72, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 18}, "end": {"line": 72, "column": 43}}, {"start": {"line": 72, "column": 47}, "end": {"line": 72, "column": 49}}], "line": 72}, "6": {"loc": {"start": {"line": 79, "column": 4}, "end": {"line": 81, "column": 5}}, "type": "if", "locations": [{"start": {"line": 79, "column": 4}, "end": {"line": 81, "column": 5}}, {"start": {}, "end": {}}], "line": 79}, "7": {"loc": {"start": {"line": 84, "column": 4}, "end": {"line": 89, "column": 5}}, "type": "if", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 89, "column": 5}}, {"start": {}, "end": {}}], "line": 84}, "8": {"loc": {"start": {"line": 92, "column": 4}, "end": {"line": 105, "column": 5}}, "type": "if", "locations": [{"start": {"line": 92, "column": 4}, "end": {"line": 105, "column": 5}}, {"start": {}, "end": {}}], "line": 92}, "9": {"loc": {"start": {"line": 166, "column": 4}, "end": {"line": 171, "column": 5}}, "type": "if", "locations": [{"start": {"line": 166, "column": 4}, "end": {"line": 171, "column": 5}}, {"start": {}, "end": {}}], "line": 166}, "10": {"loc": {"start": {"line": 174, "column": 4}, "end": {"line": 188, "column": 5}}, "type": "if", "locations": [{"start": {"line": 174, "column": 4}, "end": {"line": 188, "column": 5}}, {"start": {}, "end": {}}], "line": 174}, "11": {"loc": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 39}}, {"start": {"line": 174, "column": 43}, "end": {"line": 174, "column": 68}}], "line": 174}, "12": {"loc": {"start": {"line": 182, "column": 6}, "end": {"line": 187, "column": 7}}, "type": "if", "locations": [{"start": {"line": 182, "column": 6}, "end": {"line": 187, "column": 7}}, {"start": {}, "end": {}}], "line": 182}, "13": {"loc": {"start": {"line": 237, "column": 4}, "end": {"line": 242, "column": 5}}, "type": "if", "locations": [{"start": {"line": 237, "column": 4}, "end": {"line": 242, "column": 5}}, {"start": {}, "end": {}}], "line": 237}, "14": {"loc": {"start": {"line": 245, "column": 4}, "end": {"line": 269, "column": 5}}, "type": "if", "locations": [{"start": {"line": 245, "column": 4}, "end": {"line": 269, "column": 5}}, {"start": {}, "end": {}}], "line": 245}, "15": {"loc": {"start": {"line": 247, "column": 6}, "end": {"line": 252, "column": 7}}, "type": "if", "locations": [{"start": {"line": 247, "column": 6}, "end": {"line": 252, "column": 7}}, {"start": {}, "end": {}}], "line": 247}, "16": {"loc": {"start": {"line": 263, "column": 6}, "end": {"line": 268, "column": 7}}, "type": "if", "locations": [{"start": {"line": 263, "column": 6}, "end": {"line": 268, "column": 7}}, {"start": {}, "end": {}}], "line": 263}, "17": {"loc": {"start": {"line": 273, "column": 12}, "end": {"line": 273, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 273, "column": 12}, "end": {"line": 273, "column": 16}}, {"start": {"line": 273, "column": 20}, "end": {"line": 273, "column": 38}}], "line": 273}, "18": {"loc": {"start": {"line": 274, "column": 19}, "end": {"line": 274, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 274, "column": 47}, "end": {"line": 274, "column": 58}}, {"start": {"line": 274, "column": 61}, "end": {"line": 274, "column": 86}}], "line": 274}, "19": {"loc": {"start": {"line": 275, "column": 20}, "end": {"line": 275, "column": 90}}, "type": "cond-expr", "locations": [{"start": {"line": 275, "column": 49}, "end": {"line": 275, "column": 61}}, {"start": {"line": 275, "column": 64}, "end": {"line": 275, "column": 90}}], "line": 275}, "20": {"loc": {"start": {"line": 276, "column": 21}, "end": {"line": 276, "column": 94}}, "type": "cond-expr", "locations": [{"start": {"line": 276, "column": 51}, "end": {"line": 276, "column": 64}}, {"start": {"line": 276, "column": 67}, "end": {"line": 276, "column": 94}}], "line": 276}, "21": {"loc": {"start": {"line": 305, "column": 4}, "end": {"line": 310, "column": 5}}, "type": "if", "locations": [{"start": {"line": 305, "column": 4}, "end": {"line": 310, "column": 5}}, {"start": {}, "end": {}}], "line": 305}, "22": {"loc": {"start": {"line": 313, "column": 4}, "end": {"line": 337, "column": 5}}, "type": "if", "locations": [{"start": {"line": 313, "column": 4}, "end": {"line": 337, "column": 5}}, {"start": {}, "end": {}}], "line": 313}, "23": {"loc": {"start": {"line": 315, "column": 6}, "end": {"line": 320, "column": 7}}, "type": "if", "locations": [{"start": {"line": 315, "column": 6}, "end": {"line": 320, "column": 7}}, {"start": {}, "end": {}}], "line": 315}, "24": {"loc": {"start": {"line": 331, "column": 6}, "end": {"line": 336, "column": 7}}, "type": "if", "locations": [{"start": {"line": 331, "column": 6}, "end": {"line": 336, "column": 7}}, {"start": {}, "end": {}}], "line": 331}, "25": {"loc": {"start": {"line": 344, "column": 4}, "end": {"line": 349, "column": 5}}, "type": "if", "locations": [{"start": {"line": 344, "column": 4}, "end": {"line": 349, "column": 5}}, {"start": {}, "end": {}}], "line": 344}, "26": {"loc": {"start": {"line": 384, "column": 4}, "end": {"line": 389, "column": 5}}, "type": "if", "locations": [{"start": {"line": 384, "column": 4}, "end": {"line": 389, "column": 5}}, {"start": {}, "end": {}}], "line": 384}, "27": {"loc": {"start": {"line": 392, "column": 4}, "end": {"line": 416, "column": 5}}, "type": "if", "locations": [{"start": {"line": 392, "column": 4}, "end": {"line": 416, "column": 5}}, {"start": {}, "end": {}}], "line": 392}, "28": {"loc": {"start": {"line": 394, "column": 6}, "end": {"line": 399, "column": 7}}, "type": "if", "locations": [{"start": {"line": 394, "column": 6}, "end": {"line": 399, "column": 7}}, {"start": {}, "end": {}}], "line": 394}, "29": {"loc": {"start": {"line": 410, "column": 6}, "end": {"line": 415, "column": 7}}, "type": "if", "locations": [{"start": {"line": 410, "column": 6}, "end": {"line": 415, "column": 7}}, {"start": {}, "end": {}}], "line": 410}, "30": {"loc": {"start": {"line": 459, "column": 4}, "end": {"line": 464, "column": 5}}, "type": "if", "locations": [{"start": {"line": 459, "column": 4}, "end": {"line": 464, "column": 5}}, {"start": {}, "end": {}}], "line": 459}, "31": {"loc": {"start": {"line": 469, "column": 4}, "end": {"line": 474, "column": 5}}, "type": "if", "locations": [{"start": {"line": 469, "column": 4}, "end": {"line": 474, "column": 5}}, {"start": {}, "end": {}}], "line": 469}, "32": {"loc": {"start": {"line": 477, "column": 4}, "end": {"line": 501, "column": 5}}, "type": "if", "locations": [{"start": {"line": 477, "column": 4}, "end": {"line": 501, "column": 5}}, {"start": {}, "end": {}}], "line": 477}, "33": {"loc": {"start": {"line": 479, "column": 6}, "end": {"line": 484, "column": 7}}, "type": "if", "locations": [{"start": {"line": 479, "column": 6}, "end": {"line": 484, "column": 7}}, {"start": {}, "end": {}}], "line": 479}, "34": {"loc": {"start": {"line": 495, "column": 6}, "end": {"line": 500, "column": 7}}, "type": "if", "locations": [{"start": {"line": 495, "column": 6}, "end": {"line": 500, "column": 7}}, {"start": {}, "end": {}}], "line": 495}, "35": {"loc": {"start": {"line": 506, "column": 4}, "end": {"line": 511, "column": 5}}, "type": "if", "locations": [{"start": {"line": 506, "column": 4}, "end": {"line": 511, "column": 5}}, {"start": {}, "end": {}}], "line": 506}, "36": {"loc": {"start": {"line": 521, "column": 4}, "end": {"line": 526, "column": 5}}, "type": "if", "locations": [{"start": {"line": 521, "column": 4}, "end": {"line": 526, "column": 5}}, {"start": {}, "end": {}}], "line": 521}, "37": {"loc": {"start": {"line": 577, "column": 4}, "end": {"line": 582, "column": 5}}, "type": "if", "locations": [{"start": {"line": 577, "column": 4}, "end": {"line": 582, "column": 5}}, {"start": {}, "end": {}}], "line": 577}, "38": {"loc": {"start": {"line": 587, "column": 4}, "end": {"line": 592, "column": 5}}, "type": "if", "locations": [{"start": {"line": 587, "column": 4}, "end": {"line": 592, "column": 5}}, {"start": {}, "end": {}}], "line": 587}, "39": {"loc": {"start": {"line": 595, "column": 4}, "end": {"line": 619, "column": 5}}, "type": "if", "locations": [{"start": {"line": 595, "column": 4}, "end": {"line": 619, "column": 5}}, {"start": {}, "end": {}}], "line": 595}, "40": {"loc": {"start": {"line": 597, "column": 6}, "end": {"line": 602, "column": 7}}, "type": "if", "locations": [{"start": {"line": 597, "column": 6}, "end": {"line": 602, "column": 7}}, {"start": {}, "end": {}}], "line": 597}, "41": {"loc": {"start": {"line": 613, "column": 6}, "end": {"line": 618, "column": 7}}, "type": "if", "locations": [{"start": {"line": 613, "column": 6}, "end": {"line": 618, "column": 7}}, {"start": {}, "end": {}}], "line": 613}, "42": {"loc": {"start": {"line": 629, "column": 4}, "end": {"line": 634, "column": 5}}, "type": "if", "locations": [{"start": {"line": 629, "column": 4}, "end": {"line": 634, "column": 5}}, {"start": {}, "end": {}}], "line": 629}, "43": {"loc": {"start": {"line": 637, "column": 4}, "end": {"line": 642, "column": 5}}, "type": "if", "locations": [{"start": {"line": 637, "column": 4}, "end": {"line": 642, "column": 5}}, {"start": {}, "end": {}}], "line": 637}, "44": {"loc": {"start": {"line": 692, "column": 4}, "end": {"line": 697, "column": 5}}, "type": "if", "locations": [{"start": {"line": 692, "column": 4}, "end": {"line": 697, "column": 5}}, {"start": {}, "end": {}}], "line": 692}, "45": {"loc": {"start": {"line": 700, "column": 4}, "end": {"line": 724, "column": 5}}, "type": "if", "locations": [{"start": {"line": 700, "column": 4}, "end": {"line": 724, "column": 5}}, {"start": {}, "end": {}}], "line": 700}, "46": {"loc": {"start": {"line": 702, "column": 6}, "end": {"line": 707, "column": 7}}, "type": "if", "locations": [{"start": {"line": 702, "column": 6}, "end": {"line": 707, "column": 7}}, {"start": {}, "end": {}}], "line": 702}, "47": {"loc": {"start": {"line": 718, "column": 6}, "end": {"line": 723, "column": 7}}, "type": "if", "locations": [{"start": {"line": 718, "column": 6}, "end": {"line": 723, "column": 7}}, {"start": {}, "end": {}}], "line": 718}, "48": {"loc": {"start": {"line": 734, "column": 4}, "end": {"line": 739, "column": 5}}, "type": "if", "locations": [{"start": {"line": 734, "column": 4}, "end": {"line": 739, "column": 5}}, {"start": {}, "end": {}}], "line": 734}, "49": {"loc": {"start": {"line": 742, "column": 4}, "end": {"line": 747, "column": 5}}, "type": "if", "locations": [{"start": {"line": 742, "column": 4}, "end": {"line": 747, "column": 5}}, {"start": {}, "end": {}}], "line": 742}}, "s": {"0": 13, "1": 13, "2": 13, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 13, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 13, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 13, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 13, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 13, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 13, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 13, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 13, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0, 0], "41": [0, 0], "42": [0, 0], "43": [0, 0], "44": [0, 0], "45": [0, 0], "46": [0, 0], "47": [0, 0], "48": [0, 0], "49": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "46b8fdb1d51d71d7b8a987a35ce3173679a25a4c"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\notification.controller.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\notification.controller.js", "statementMap": {"0": {"start": {"line": 1, "column": 25}, "end": {"line": 1, "column": 45}}, "1": {"start": {"line": 8, "column": 0}, "end": {"line": 50, "column": 2}}, "2": {"start": {"line": 9, "column": 2}, "end": {"line": 49, "column": 3}}, "3": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 39}}, "4": {"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": 46}}, "5": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 55}}, "6": {"start": {"line": 15, "column": 18}, "end": {"line": 15, "column": 42}}, "7": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 32}}, "8": {"start": {"line": 16, "column": 14}, "end": {"line": 16, "column": 32}}, "9": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 66}}, "10": {"start": {"line": 17, "column": 31}, "end": {"line": 17, "column": 66}}, "11": {"start": {"line": 20, "column": 43}, "end": {"line": 25, "column": 6}}, "12": {"start": {"line": 28, "column": 24}, "end": {"line": 33, "column": 6}}, "13": {"start": {"line": 35, "column": 4}, "end": {"line": 45, "column": 7}}, "14": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 38}}, "15": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 56}}, "16": {"start": {"line": 57, "column": 0}, "end": {"line": 87, "column": 2}}, "17": {"start": {"line": 58, "column": 2}, "end": {"line": 86, "column": 3}}, "18": {"start": {"line": 59, "column": 19}, "end": {"line": 59, "column": 29}}, "19": {"start": {"line": 62, "column": 25}, "end": {"line": 67, "column": 6}}, "20": {"start": {"line": 69, "column": 4}, "end": {"line": 71, "column": 5}}, "21": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 56}}, "22": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 49}}, "23": {"start": {"line": 76, "column": 4}, "end": {"line": 82, "column": 7}}, "24": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 39}}, "25": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": 57}}, "26": {"start": {"line": 94, "column": 0}, "end": {"line": 114, "column": 2}}, "27": {"start": {"line": 95, "column": 2}, "end": {"line": 113, "column": 3}}, "28": {"start": {"line": 97, "column": 4}, "end": {"line": 105, "column": 6}}, "29": {"start": {"line": 107, "column": 4}, "end": {"line": 109, "column": 7}}, "30": {"start": {"line": 111, "column": 4}, "end": {"line": 111, "column": 41}}, "31": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 59}}, "32": {"start": {"line": 121, "column": 0}, "end": {"line": 147, "column": 2}}, "33": {"start": {"line": 122, "column": 2}, "end": {"line": 146, "column": 3}}, "34": {"start": {"line": 123, "column": 19}, "end": {"line": 123, "column": 29}}, "35": {"start": {"line": 126, "column": 25}, "end": {"line": 131, "column": 6}}, "36": {"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}, "37": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 56}}, "38": {"start": {"line": 138, "column": 4}, "end": {"line": 138, "column": 33}}, "39": {"start": {"line": 140, "column": 4}, "end": {"line": 142, "column": 7}}, "40": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 36}}, "41": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 54}}, "42": {"start": {"line": 154, "column": 0}, "end": {"line": 178, "column": 2}}, "43": {"start": {"line": 155, "column": 2}, "end": {"line": 177, "column": 3}}, "44": {"start": {"line": 157, "column": 24}, "end": {"line": 162, "column": 6}}, "45": {"start": {"line": 164, "column": 4}, "end": {"line": 169, "column": 7}}, "46": {"start": {"line": 171, "column": 4}, "end": {"line": 171, "column": 40}}, "47": {"start": {"line": 172, "column": 4}, "end": {"line": 176, "column": 7}}, "48": {"start": {"line": 185, "column": 0}, "end": {"line": 206, "column": 2}}, "49": {"start": {"line": 186, "column": 2}, "end": {"line": 205, "column": 3}}, "50": {"start": {"line": 188, "column": 4}, "end": {"line": 192, "column": 7}}, "51": {"start": {"line": 194, "column": 4}, "end": {"line": 197, "column": 7}}, "52": {"start": {"line": 199, "column": 4}, "end": {"line": 199, "column": 38}}, "53": {"start": {"line": 200, "column": 4}, "end": {"line": 204, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 28}}, "loc": {"start": {"line": 8, "column": 47}, "end": {"line": 50, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 57, "column": 33}, "end": {"line": 57, "column": 34}}, "loc": {"start": {"line": 57, "column": 53}, "end": {"line": 87, "column": 1}}, "line": 57}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 94, "column": 37}, "end": {"line": 94, "column": 38}}, "loc": {"start": {"line": 94, "column": 57}, "end": {"line": 114, "column": 1}}, "line": 94}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 121, "column": 29}, "end": {"line": 121, "column": 30}}, "loc": {"start": {"line": 121, "column": 49}, "end": {"line": 147, "column": 1}}, "line": 121}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 154, "column": 37}, "end": {"line": 154, "column": 38}}, "loc": {"start": {"line": 154, "column": 57}, "end": {"line": 178, "column": 1}}, "line": 154}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 185, "column": 33}, "end": {"line": 185, "column": 34}}, "loc": {"start": {"line": 185, "column": 53}, "end": {"line": 206, "column": 1}}, "line": 185}}, "branchMap": {"0": {"loc": {"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": 41}}, {"start": {"line": 11, "column": 45}, "end": {"line": 11, "column": 46}}], "line": 11}, "1": {"loc": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 49}}, {"start": {"line": 12, "column": 53}, "end": {"line": 12, "column": 55}}], "line": 12}, "2": {"loc": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 32}}, "type": "if", "locations": [{"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 32}}, {"start": {}, "end": {}}], "line": 16}, "3": {"loc": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 66}}, "type": "if", "locations": [{"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 66}}, {"start": {}, "end": {}}], "line": 17}, "4": {"loc": {"start": {"line": 69, "column": 4}, "end": {"line": 71, "column": 5}}, "type": "if", "locations": [{"start": {"line": 69, "column": 4}, "end": {"line": 71, "column": 5}}, {"start": {}, "end": {}}], "line": 69}, "5": {"loc": {"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}, "type": "if", "locations": [{"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}, {"start": {}, "end": {}}], "line": 133}}, "s": {"0": 13, "1": 13, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 0, "9": 2, "10": 0, "11": 2, "12": 2, "13": 2, "14": 0, "15": 0, "16": 13, "17": 2, "18": 2, "19": 2, "20": 2, "21": 1, "22": 1, "23": 1, "24": 0, "25": 0, "26": 13, "27": 1, "28": 1, "29": 1, "30": 0, "31": 0, "32": 13, "33": 3, "34": 3, "35": 3, "36": 3, "37": 2, "38": 1, "39": 1, "40": 0, "41": 0, "42": 13, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 13, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0}, "f": {"0": 2, "1": 2, "2": 1, "3": 3, "4": 0, "5": 0}, "b": {"0": [2, 2], "1": [2, 2], "2": [0, 2], "3": [0, 2], "4": [1, 1], "5": [2, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "995c85bcab2188ab9f49fa8833959f8b01e4ae0f"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\permission.controller.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\permission.controller.js", "statementMap": {"0": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 43}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 35}}, "2": {"start": {"line": 15, "column": 0}, "end": {"line": 53, "column": 2}}, "3": {"start": {"line": 16, "column": 2}, "end": {"line": 52, "column": 3}}, "4": {"start": {"line": 17, "column": 31}, "end": {"line": 17, "column": 40}}, "5": {"start": {"line": 20, "column": 28}, "end": {"line": 20, "column": 30}}, "6": {"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, "7": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 38}}, "8": {"start": {"line": 28, "column": 4}, "end": {"line": 34, "column": 5}}, "9": {"start": {"line": 29, "column": 6}, "end": {"line": 33, "column": 8}}, "10": {"start": {"line": 37, "column": 24}, "end": {"line": 40, "column": 6}}, "11": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 7}}, "12": {"start": {"line": 47, "column": 4}, "end": {"line": 51, "column": 7}}, "13": {"start": {"line": 60, "column": 0}, "end": {"line": 85, "column": 2}}, "14": {"start": {"line": 61, "column": 2}, "end": {"line": 84, "column": 3}}, "15": {"start": {"line": 62, "column": 19}, "end": {"line": 62, "column": 29}}, "16": {"start": {"line": 65, "column": 23}, "end": {"line": 65, "column": 52}}, "17": {"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": 5}}, "18": {"start": {"line": 68, "column": 6}, "end": {"line": 71, "column": 9}}, "19": {"start": {"line": 74, "column": 4}, "end": {"line": 77, "column": 7}}, "20": {"start": {"line": 79, "column": 4}, "end": {"line": 83, "column": 7}}, "21": {"start": {"line": 92, "column": 0}, "end": {"line": 115, "column": 2}}, "22": {"start": {"line": 93, "column": 2}, "end": {"line": 114, "column": 3}}, "23": {"start": {"line": 95, "column": 20}, "end": {"line": 99, "column": 6}}, "24": {"start": {"line": 102, "column": 24}, "end": {"line": 102, "column": 56}}, "25": {"start": {"line": 102, "column": 44}, "end": {"line": 102, "column": 55}}, "26": {"start": {"line": 104, "column": 4}, "end": {"line": 107, "column": 7}}, "27": {"start": {"line": 109, "column": 4}, "end": {"line": 113, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 25}, "end": {"line": 15, "column": 26}}, "loc": {"start": {"line": 15, "column": 45}, "end": {"line": 53, "column": 1}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 60, "column": 28}, "end": {"line": 60, "column": 29}}, "loc": {"start": {"line": 60, "column": 48}, "end": {"line": 85, "column": 1}}, "line": 60}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 92, "column": 31}, "end": {"line": 92, "column": 32}}, "loc": {"start": {"line": 92, "column": 51}, "end": {"line": 115, "column": 1}}, "line": 92}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 102, "column": 36}, "end": {"line": 102, "column": 37}}, "loc": {"start": {"line": 102, "column": 44}, "end": {"line": 102, "column": 55}}, "line": 102}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, {"start": {}, "end": {}}], "line": 23}, "1": {"loc": {"start": {"line": 28, "column": 4}, "end": {"line": 34, "column": 5}}, "type": "if", "locations": [{"start": {"line": 28, "column": 4}, "end": {"line": 34, "column": 5}}, {"start": {}, "end": {}}], "line": 28}, "2": {"loc": {"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": 5}}, "type": "if", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": 5}}, {"start": {}, "end": {}}], "line": 67}}, "s": {"0": 13, "1": 13, "2": 13, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 13, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 13, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e2f1c27330de25af38d5133c58e1faaa6c485f36"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\role.controller.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\role.controller.js", "statementMap": {"0": {"start": {"line": 7, "column": 35}, "end": {"line": 7, "column": 55}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 35}}, "2": {"start": {"line": 15, "column": 0}, "end": {"line": 67, "column": 2}}, "3": {"start": {"line": 16, "column": 2}, "end": {"line": 66, "column": 3}}, "4": {"start": {"line": 17, "column": 23}, "end": {"line": 17, "column": 32}}, "5": {"start": {"line": 20, "column": 28}, "end": {"line": 20, "column": 30}}, "6": {"start": {"line": 23, "column": 4}, "end": {"line": 28, "column": 5}}, "7": {"start": {"line": 24, "column": 6}, "end": {"line": 27, "column": 8}}, "8": {"start": {"line": 31, "column": 18}, "end": {"line": 42, "column": 6}}, "9": {"start": {"line": 45, "column": 31}, "end": {"line": 54, "column": 7}}, "10": {"start": {"line": 46, "column": 24}, "end": {"line": 48, "column": 8}}, "11": {"start": {"line": 50, "column": 23}, "end": {"line": 50, "column": 36}}, "12": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 38}}, "13": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 22}}, "14": {"start": {"line": 56, "column": 4}, "end": {"line": 59, "column": 7}}, "15": {"start": {"line": 61, "column": 4}, "end": {"line": 65, "column": 7}}, "16": {"start": {"line": 74, "column": 0}, "end": {"line": 116, "column": 2}}, "17": {"start": {"line": 75, "column": 2}, "end": {"line": 115, "column": 3}}, "18": {"start": {"line": 76, "column": 19}, "end": {"line": 76, "column": 29}}, "19": {"start": {"line": 79, "column": 17}, "end": {"line": 88, "column": 6}}, "20": {"start": {"line": 90, "column": 4}, "end": {"line": 95, "column": 5}}, "21": {"start": {"line": 91, "column": 6}, "end": {"line": 94, "column": 9}}, "22": {"start": {"line": 98, "column": 22}, "end": {"line": 100, "column": 6}}, "23": {"start": {"line": 102, "column": 21}, "end": {"line": 102, "column": 34}}, "24": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 36}}, "25": {"start": {"line": 105, "column": 4}, "end": {"line": 108, "column": 7}}, "26": {"start": {"line": 110, "column": 4}, "end": {"line": 114, "column": 7}}, "27": {"start": {"line": 123, "column": 0}, "end": {"line": 199, "column": 2}}, "28": {"start": {"line": 124, "column": 2}, "end": {"line": 198, "column": 3}}, "29": {"start": {"line": 125, "column": 47}, "end": {"line": 125, "column": 55}}, "30": {"start": {"line": 127, "column": 4}, "end": {"line": 132, "column": 5}}, "31": {"start": {"line": 128, "column": 6}, "end": {"line": 131, "column": 9}}, "32": {"start": {"line": 135, "column": 25}, "end": {"line": 137, "column": 6}}, "33": {"start": {"line": 139, "column": 4}, "end": {"line": 144, "column": 5}}, "34": {"start": {"line": 140, "column": 6}, "end": {"line": 143, "column": 9}}, "35": {"start": {"line": 147, "column": 17}, "end": {"line": 151, "column": 6}}, "36": {"start": {"line": 154, "column": 4}, "end": {"line": 173, "column": 5}}, "37": {"start": {"line": 156, "column": 34}, "end": {"line": 162, "column": 8}}, "38": {"start": {"line": 164, "column": 6}, "end": {"line": 169, "column": 7}}, "39": {"start": {"line": 165, "column": 8}, "end": {"line": 168, "column": 11}}, "40": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": 53}}, "41": {"start": {"line": 176, "column": 24}, "end": {"line": 185, "column": 6}}, "42": {"start": {"line": 187, "column": 4}, "end": {"line": 191, "column": 7}}, "43": {"start": {"line": 193, "column": 4}, "end": {"line": 197, "column": 7}}, "44": {"start": {"line": 206, "column": 0}, "end": {"line": 273, "column": 2}}, "45": {"start": {"line": 207, "column": 2}, "end": {"line": 272, "column": 3}}, "46": {"start": {"line": 208, "column": 19}, "end": {"line": 208, "column": 29}}, "47": {"start": {"line": 209, "column": 34}, "end": {"line": 209, "column": 42}}, "48": {"start": {"line": 212, "column": 17}, "end": {"line": 212, "column": 40}}, "49": {"start": {"line": 214, "column": 4}, "end": {"line": 219, "column": 5}}, "50": {"start": {"line": 215, "column": 6}, "end": {"line": 218, "column": 9}}, "51": {"start": {"line": 222, "column": 4}, "end": {"line": 227, "column": 5}}, "52": {"start": {"line": 223, "column": 6}, "end": {"line": 226, "column": 9}}, "53": {"start": {"line": 230, "column": 4}, "end": {"line": 241, "column": 5}}, "54": {"start": {"line": 231, "column": 27}, "end": {"line": 233, "column": 8}}, "55": {"start": {"line": 235, "column": 6}, "end": {"line": 240, "column": 7}}, "56": {"start": {"line": 236, "column": 8}, "end": {"line": 239, "column": 11}}, "57": {"start": {"line": 244, "column": 4}, "end": {"line": 247, "column": 7}}, "58": {"start": {"line": 250, "column": 24}, "end": {"line": 259, "column": 6}}, "59": {"start": {"line": 261, "column": 4}, "end": {"line": 265, "column": 7}}, "60": {"start": {"line": 267, "column": 4}, "end": {"line": 271, "column": 7}}, "61": {"start": {"line": 280, "column": 0}, "end": {"line": 328, "column": 2}}, "62": {"start": {"line": 281, "column": 2}, "end": {"line": 327, "column": 3}}, "63": {"start": {"line": 282, "column": 19}, "end": {"line": 282, "column": 29}}, "64": {"start": {"line": 285, "column": 17}, "end": {"line": 285, "column": 40}}, "65": {"start": {"line": 287, "column": 4}, "end": {"line": 292, "column": 5}}, "66": {"start": {"line": 288, "column": 6}, "end": {"line": 291, "column": 9}}, "67": {"start": {"line": 295, "column": 4}, "end": {"line": 300, "column": 5}}, "68": {"start": {"line": 296, "column": 6}, "end": {"line": 299, "column": 9}}, "69": {"start": {"line": 303, "column": 22}, "end": {"line": 305, "column": 6}}, "70": {"start": {"line": 307, "column": 4}, "end": {"line": 312, "column": 5}}, "71": {"start": {"line": 308, "column": 6}, "end": {"line": 311, "column": 9}}, "72": {"start": {"line": 315, "column": 4}, "end": {"line": 315, "column": 25}}, "73": {"start": {"line": 317, "column": 4}, "end": {"line": 320, "column": 7}}, "74": {"start": {"line": 322, "column": 4}, "end": {"line": 326, "column": 7}}, "75": {"start": {"line": 335, "column": 0}, "end": {"line": 365, "column": 2}}, "76": {"start": {"line": 336, "column": 2}, "end": {"line": 364, "column": 3}}, "77": {"start": {"line": 337, "column": 19}, "end": {"line": 337, "column": 29}}, "78": {"start": {"line": 340, "column": 17}, "end": {"line": 340, "column": 40}}, "79": {"start": {"line": 342, "column": 4}, "end": {"line": 347, "column": 5}}, "80": {"start": {"line": 343, "column": 6}, "end": {"line": 346, "column": 9}}, "81": {"start": {"line": 350, "column": 24}, "end": {"line": 352, "column": 6}}, "82": {"start": {"line": 354, "column": 4}, "end": {"line": 357, "column": 7}}, "83": {"start": {"line": 359, "column": 4}, "end": {"line": 363, "column": 7}}, "84": {"start": {"line": 372, "column": 0}, "end": {"line": 430, "column": 2}}, "85": {"start": {"line": 373, "column": 2}, "end": {"line": 429, "column": 3}}, "86": {"start": {"line": 374, "column": 19}, "end": {"line": 374, "column": 29}}, "87": {"start": {"line": 375, "column": 28}, "end": {"line": 375, "column": 36}}, "88": {"start": {"line": 377, "column": 4}, "end": {"line": 382, "column": 5}}, "89": {"start": {"line": 378, "column": 6}, "end": {"line": 381, "column": 9}}, "90": {"start": {"line": 385, "column": 17}, "end": {"line": 385, "column": 40}}, "91": {"start": {"line": 387, "column": 4}, "end": {"line": 392, "column": 5}}, "92": {"start": {"line": 388, "column": 6}, "end": {"line": 391, "column": 9}}, "93": {"start": {"line": 395, "column": 32}, "end": {"line": 401, "column": 6}}, "94": {"start": {"line": 403, "column": 4}, "end": {"line": 408, "column": 5}}, "95": {"start": {"line": 404, "column": 6}, "end": {"line": 407, "column": 9}}, "96": {"start": {"line": 411, "column": 4}, "end": {"line": 411, "column": 51}}, "97": {"start": {"line": 414, "column": 31}, "end": {"line": 416, "column": 6}}, "98": {"start": {"line": 418, "column": 4}, "end": {"line": 422, "column": 7}}, "99": {"start": {"line": 424, "column": 4}, "end": {"line": 428, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 19}, "end": {"line": 15, "column": 20}}, "loc": {"start": {"line": 15, "column": 39}, "end": {"line": 67, "column": 1}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 45, "column": 59}, "end": {"line": 45, "column": 60}}, "loc": {"start": {"line": 45, "column": 75}, "end": {"line": 54, "column": 5}}, "line": 45}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 74, "column": 22}, "end": {"line": 74, "column": 23}}, "loc": {"start": {"line": 74, "column": 42}, "end": {"line": 116, "column": 1}}, "line": 74}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 123, "column": 21}, "end": {"line": 123, "column": 22}}, "loc": {"start": {"line": 123, "column": 41}, "end": {"line": 199, "column": 1}}, "line": 123}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 206, "column": 21}, "end": {"line": 206, "column": 22}}, "loc": {"start": {"line": 206, "column": 41}, "end": {"line": 273, "column": 1}}, "line": 206}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 280, "column": 21}, "end": {"line": 280, "column": 22}}, "loc": {"start": {"line": 280, "column": 41}, "end": {"line": 328, "column": 1}}, "line": 280}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 335, "column": 29}, "end": {"line": 335, "column": 30}}, "loc": {"start": {"line": 335, "column": 49}, "end": {"line": 365, "column": 1}}, "line": 335}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 372, "column": 32}, "end": {"line": 372, "column": 33}}, "loc": {"start": {"line": 372, "column": 52}, "end": {"line": 430, "column": 1}}, "line": 372}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 28, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 28, "column": 5}}, {"start": {}, "end": {}}], "line": 23}, "1": {"loc": {"start": {"line": 90, "column": 4}, "end": {"line": 95, "column": 5}}, "type": "if", "locations": [{"start": {"line": 90, "column": 4}, "end": {"line": 95, "column": 5}}, {"start": {}, "end": {}}], "line": 90}, "2": {"loc": {"start": {"line": 127, "column": 4}, "end": {"line": 132, "column": 5}}, "type": "if", "locations": [{"start": {"line": 127, "column": 4}, "end": {"line": 132, "column": 5}}, {"start": {}, "end": {}}], "line": 127}, "3": {"loc": {"start": {"line": 139, "column": 4}, "end": {"line": 144, "column": 5}}, "type": "if", "locations": [{"start": {"line": 139, "column": 4}, "end": {"line": 144, "column": 5}}, {"start": {}, "end": {}}], "line": 139}, "4": {"loc": {"start": {"line": 154, "column": 4}, "end": {"line": 173, "column": 5}}, "type": "if", "locations": [{"start": {"line": 154, "column": 4}, "end": {"line": 173, "column": 5}}, {"start": {}, "end": {}}], "line": 154}, "5": {"loc": {"start": {"line": 154, "column": 8}, "end": {"line": 154, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 154, "column": 8}, "end": {"line": 154, "column": 19}}, {"start": {"line": 154, "column": 23}, "end": {"line": 154, "column": 45}}], "line": 154}, "6": {"loc": {"start": {"line": 164, "column": 6}, "end": {"line": 169, "column": 7}}, "type": "if", "locations": [{"start": {"line": 164, "column": 6}, "end": {"line": 169, "column": 7}}, {"start": {}, "end": {}}], "line": 164}, "7": {"loc": {"start": {"line": 214, "column": 4}, "end": {"line": 219, "column": 5}}, "type": "if", "locations": [{"start": {"line": 214, "column": 4}, "end": {"line": 219, "column": 5}}, {"start": {}, "end": {}}], "line": 214}, "8": {"loc": {"start": {"line": 222, "column": 4}, "end": {"line": 227, "column": 5}}, "type": "if", "locations": [{"start": {"line": 222, "column": 4}, "end": {"line": 227, "column": 5}}, {"start": {}, "end": {}}], "line": 222}, "9": {"loc": {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 22}}, {"start": {"line": 222, "column": 26}, "end": {"line": 222, "column": 30}}, {"start": {"line": 222, "column": 34}, "end": {"line": 222, "column": 52}}], "line": 222}, "10": {"loc": {"start": {"line": 230, "column": 4}, "end": {"line": 241, "column": 5}}, "type": "if", "locations": [{"start": {"line": 230, "column": 4}, "end": {"line": 241, "column": 5}}, {"start": {}, "end": {}}], "line": 230}, "11": {"loc": {"start": {"line": 230, "column": 8}, "end": {"line": 230, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 230, "column": 8}, "end": {"line": 230, "column": 12}}, {"start": {"line": 230, "column": 16}, "end": {"line": 230, "column": 34}}], "line": 230}, "12": {"loc": {"start": {"line": 235, "column": 6}, "end": {"line": 240, "column": 7}}, "type": "if", "locations": [{"start": {"line": 235, "column": 6}, "end": {"line": 240, "column": 7}}, {"start": {}, "end": {}}], "line": 235}, "13": {"loc": {"start": {"line": 245, "column": 12}, "end": {"line": 245, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 245, "column": 12}, "end": {"line": 245, "column": 16}}, {"start": {"line": 245, "column": 20}, "end": {"line": 245, "column": 29}}], "line": 245}, "14": {"loc": {"start": {"line": 246, "column": 19}, "end": {"line": 246, "column": 77}}, "type": "cond-expr", "locations": [{"start": {"line": 246, "column": 47}, "end": {"line": 246, "column": 58}}, {"start": {"line": 246, "column": 61}, "end": {"line": 246, "column": 77}}], "line": 246}, "15": {"loc": {"start": {"line": 287, "column": 4}, "end": {"line": 292, "column": 5}}, "type": "if", "locations": [{"start": {"line": 287, "column": 4}, "end": {"line": 292, "column": 5}}, {"start": {}, "end": {}}], "line": 287}, "16": {"loc": {"start": {"line": 295, "column": 4}, "end": {"line": 300, "column": 5}}, "type": "if", "locations": [{"start": {"line": 295, "column": 4}, "end": {"line": 300, "column": 5}}, {"start": {}, "end": {}}], "line": 295}, "17": {"loc": {"start": {"line": 307, "column": 4}, "end": {"line": 312, "column": 5}}, "type": "if", "locations": [{"start": {"line": 307, "column": 4}, "end": {"line": 312, "column": 5}}, {"start": {}, "end": {}}], "line": 307}, "18": {"loc": {"start": {"line": 342, "column": 4}, "end": {"line": 347, "column": 5}}, "type": "if", "locations": [{"start": {"line": 342, "column": 4}, "end": {"line": 347, "column": 5}}, {"start": {}, "end": {}}], "line": 342}, "19": {"loc": {"start": {"line": 377, "column": 4}, "end": {"line": 382, "column": 5}}, "type": "if", "locations": [{"start": {"line": 377, "column": 4}, "end": {"line": 382, "column": 5}}, {"start": {}, "end": {}}], "line": 377}, "20": {"loc": {"start": {"line": 377, "column": 8}, "end": {"line": 377, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 377, "column": 8}, "end": {"line": 377, "column": 20}}, {"start": {"line": 377, "column": 24}, "end": {"line": 377, "column": 51}}], "line": 377}, "21": {"loc": {"start": {"line": 387, "column": 4}, "end": {"line": 392, "column": 5}}, "type": "if", "locations": [{"start": {"line": 387, "column": 4}, "end": {"line": 392, "column": 5}}, {"start": {}, "end": {}}], "line": 387}, "22": {"loc": {"start": {"line": 403, "column": 4}, "end": {"line": 408, "column": 5}}, "type": "if", "locations": [{"start": {"line": 403, "column": 4}, "end": {"line": 408, "column": 5}}, {"start": {}, "end": {}}], "line": 403}}, "s": {"0": 13, "1": 13, "2": 13, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 13, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 13, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 13, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 13, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 13, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 13, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "abddb2eae3546ce1a9941f6f5776435073445ad8"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\system.controller.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\system.controller.js", "statementMap": {"0": {"start": {"line": 7, "column": 65}, "end": {"line": 7, "column": 85}}, "1": {"start": {"line": 8, "column": 22}, "end": {"line": 8, "column": 42}}, "2": {"start": {"line": 15, "column": 0}, "end": {"line": 53, "column": 2}}, "3": {"start": {"line": 16, "column": 2}, "end": {"line": 52, "column": 3}}, "4": {"start": {"line": 18, "column": 17}, "end": {"line": 20, "column": 6}}, "5": {"start": {"line": 23, "column": 4}, "end": {"line": 40, "column": 5}}, "6": {"start": {"line": 24, "column": 6}, "end": {"line": 39, "column": 9}}, "7": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 7}}, "8": {"start": {"line": 47, "column": 4}, "end": {"line": 51, "column": 7}}, "9": {"start": {"line": 60, "column": 0}, "end": {"line": 132, "column": 2}}, "10": {"start": {"line": 61, "column": 2}, "end": {"line": 131, "column": 3}}, "11": {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": 16}}, "12": {"start": {"line": 79, "column": 17}, "end": {"line": 81, "column": 6}}, "13": {"start": {"line": 84, "column": 4}, "end": {"line": 101, "column": 5}}, "14": {"start": {"line": 85, "column": 6}, "end": {"line": 100, "column": 9}}, "15": {"start": {"line": 104, "column": 4}, "end": {"line": 118, "column": 7}}, "16": {"start": {"line": 120, "column": 4}, "end": {"line": 124, "column": 7}}, "17": {"start": {"line": 126, "column": 4}, "end": {"line": 130, "column": 7}}, "18": {"start": {"line": 139, "column": 0}, "end": {"line": 231, "column": 2}}, "19": {"start": {"line": 140, "column": 2}, "end": {"line": 230, "column": 3}}, "20": {"start": {"line": 142, "column": 22}, "end": {"line": 142, "column": 40}}, "21": {"start": {"line": 145, "column": 31}, "end": {"line": 145, "column": 58}}, "22": {"start": {"line": 148, "column": 22}, "end": {"line": 148, "column": 40}}, "23": {"start": {"line": 151, "column": 29}, "end": {"line": 151, "column": 54}}, "24": {"start": {"line": 154, "column": 37}, "end": {"line": 156, "column": 6}}, "25": {"start": {"line": 159, "column": 35}, "end": {"line": 161, "column": 6}}, "26": {"start": {"line": 164, "column": 29}, "end": {"line": 166, "column": 6}}, "27": {"start": {"line": 169, "column": 30}, "end": {"line": 171, "column": 6}}, "28": {"start": {"line": 174, "column": 30}, "end": {"line": 176, "column": 6}}, "29": {"start": {"line": 179, "column": 29}, "end": {"line": 179, "column": 51}}, "30": {"start": {"line": 182, "column": 24}, "end": {"line": 186, "column": 6}}, "31": {"start": {"line": 189, "column": 24}, "end": {"line": 205, "column": 6}}, "32": {"start": {"line": 207, "column": 4}, "end": {"line": 223, "column": 7}}, "33": {"start": {"line": 225, "column": 4}, "end": {"line": 229, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 26}, "end": {"line": 15, "column": 27}}, "loc": {"start": {"line": 15, "column": 46}, "end": {"line": 53, "column": 1}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 60, "column": 29}, "end": {"line": 60, "column": 30}}, "loc": {"start": {"line": 60, "column": 49}, "end": {"line": 132, "column": 1}}, "line": 60}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 139, "column": 25}, "end": {"line": 139, "column": 26}}, "loc": {"start": {"line": 139, "column": 45}, "end": {"line": 231, "column": 1}}, "line": 139}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 40, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 40, "column": 5}}, {"start": {}, "end": {}}], "line": 23}, "1": {"loc": {"start": {"line": 84, "column": 4}, "end": {"line": 101, "column": 5}}, "type": "if", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 101, "column": 5}}, {"start": {}, "end": {}}], "line": 84}, "2": {"loc": {"start": {"line": 105, "column": 27}, "end": {"line": 105, "column": 111}}, "type": "cond-expr", "locations": [{"start": {"line": 105, "column": 63}, "end": {"line": 105, "column": 82}}, {"start": {"line": 105, "column": 85}, "end": {"line": 105, "column": 111}}], "line": 105}, "3": {"loc": {"start": {"line": 106, "column": 34}, "end": {"line": 106, "column": 139}}, "type": "cond-expr", "locations": [{"start": {"line": 106, "column": 77}, "end": {"line": 106, "column": 103}}, {"start": {"line": 106, "column": 106}, "end": {"line": 106, "column": 139}}], "line": 106}, "4": {"loc": {"start": {"line": 107, "column": 34}, "end": {"line": 107, "column": 139}}, "type": "cond-expr", "locations": [{"start": {"line": 107, "column": 77}, "end": {"line": 107, "column": 103}}, {"start": {"line": 107, "column": 106}, "end": {"line": 107, "column": 139}}], "line": 107}, "5": {"loc": {"start": {"line": 108, "column": 31}, "end": {"line": 108, "column": 127}}, "type": "cond-expr", "locations": [{"start": {"line": 108, "column": 71}, "end": {"line": 108, "column": 94}}, {"start": {"line": 108, "column": 97}, "end": {"line": 108, "column": 127}}], "line": 108}, "6": {"loc": {"start": {"line": 109, "column": 32}, "end": {"line": 109, "column": 131}}, "type": "cond-expr", "locations": [{"start": {"line": 109, "column": 73}, "end": {"line": 109, "column": 97}}, {"start": {"line": 109, "column": 100}, "end": {"line": 109, "column": 131}}], "line": 109}, "7": {"loc": {"start": {"line": 110, "column": 26}, "end": {"line": 110, "column": 107}}, "type": "cond-expr", "locations": [{"start": {"line": 110, "column": 61}, "end": {"line": 110, "column": 79}}, {"start": {"line": 110, "column": 82}, "end": {"line": 110, "column": 107}}], "line": 110}, "8": {"loc": {"start": {"line": 111, "column": 24}, "end": {"line": 111, "column": 99}}, "type": "cond-expr", "locations": [{"start": {"line": 111, "column": 57}, "end": {"line": 111, "column": 73}}, {"start": {"line": 111, "column": 76}, "end": {"line": 111, "column": 99}}], "line": 111}, "9": {"loc": {"start": {"line": 112, "column": 23}, "end": {"line": 112, "column": 95}}, "type": "cond-expr", "locations": [{"start": {"line": 112, "column": 55}, "end": {"line": 112, "column": 70}}, {"start": {"line": 112, "column": 73}, "end": {"line": 112, "column": 95}}], "line": 112}, "10": {"loc": {"start": {"line": 113, "column": 28}, "end": {"line": 113, "column": 115}}, "type": "cond-expr", "locations": [{"start": {"line": 113, "column": 65}, "end": {"line": 113, "column": 85}}, {"start": {"line": 113, "column": 88}, "end": {"line": 113, "column": 115}}], "line": 113}, "11": {"loc": {"start": {"line": 114, "column": 26}, "end": {"line": 114, "column": 107}}, "type": "cond-expr", "locations": [{"start": {"line": 114, "column": 61}, "end": {"line": 114, "column": 79}}, {"start": {"line": 114, "column": 82}, "end": {"line": 114, "column": 107}}], "line": 114}, "12": {"loc": {"start": {"line": 115, "column": 19}, "end": {"line": 115, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 115, "column": 47}, "end": {"line": 115, "column": 58}}, {"start": {"line": 115, "column": 61}, "end": {"line": 115, "column": 79}}], "line": 115}, "13": {"loc": {"start": {"line": 116, "column": 19}, "end": {"line": 116, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 116, "column": 47}, "end": {"line": 116, "column": 58}}, {"start": {"line": 116, "column": 61}, "end": {"line": 116, "column": 79}}], "line": 116}, "14": {"loc": {"start": {"line": 117, "column": 26}, "end": {"line": 117, "column": 107}}, "type": "cond-expr", "locations": [{"start": {"line": 117, "column": 61}, "end": {"line": 117, "column": 79}}, {"start": {"line": 117, "column": 82}, "end": {"line": 117, "column": 107}}], "line": 117}, "15": {"loc": {"start": {"line": 219, "column": 28}, "end": {"line": 219, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 219, "column": 28}, "end": {"line": 219, "column": 44}}, {"start": {"line": 219, "column": 48}, "end": {"line": 219, "column": 49}}], "line": 219}}, "s": {"0": 13, "1": 13, "2": 13, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 13, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 13, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "32f8d8a9841ba3abae9bd85f69767ad4d27cd956"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\userController.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\controllers\\userController.js", "statementMap": {"0": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 43}}, "1": {"start": {"line": 8, "column": 12}, "end": {"line": 8, "column": 35}}, "2": {"start": {"line": 9, "column": 15}, "end": {"line": 9, "column": 34}}, "3": {"start": {"line": 16, "column": 0}, "end": {"line": 77, "column": 2}}, "4": {"start": {"line": 17, "column": 2}, "end": {"line": 76, "column": 3}}, "5": {"start": {"line": 18, "column": 49}, "end": {"line": 18, "column": 57}}, "6": {"start": {"line": 21, "column": 25}, "end": {"line": 28, "column": 6}}, "7": {"start": {"line": 30, "column": 4}, "end": {"line": 35, "column": 5}}, "8": {"start": {"line": 31, "column": 6}, "end": {"line": 34, "column": 9}}, "9": {"start": {"line": 38, "column": 22}, "end": {"line": 40, "column": 6}}, "10": {"start": {"line": 42, "column": 4}, "end": {"line": 47, "column": 5}}, "11": {"start": {"line": 43, "column": 6}, "end": {"line": 46, "column": 9}}, "12": {"start": {"line": 50, "column": 17}, "end": {"line": 58, "column": 6}}, "13": {"start": {"line": 60, "column": 4}, "end": {"line": 69, "column": 7}}, "14": {"start": {"line": 71, "column": 4}, "end": {"line": 75, "column": 7}}, "15": {"start": {"line": 84, "column": 0}, "end": {"line": 161, "column": 2}}, "16": {"start": {"line": 85, "column": 2}, "end": {"line": 160, "column": 3}}, "17": {"start": {"line": 86, "column": 35}, "end": {"line": 86, "column": 43}}, "18": {"start": {"line": 89, "column": 17}, "end": {"line": 98, "column": 6}}, "19": {"start": {"line": 100, "column": 4}, "end": {"line": 105, "column": 5}}, "20": {"start": {"line": 101, "column": 6}, "end": {"line": 104, "column": 9}}, "21": {"start": {"line": 108, "column": 20}, "end": {"line": 108, "column": 56}}, "22": {"start": {"line": 109, "column": 4}, "end": {"line": 114, "column": 5}}, "23": {"start": {"line": 110, "column": 6}, "end": {"line": 113, "column": 9}}, "24": {"start": {"line": 117, "column": 4}, "end": {"line": 122, "column": 5}}, "25": {"start": {"line": 118, "column": 6}, "end": {"line": 121, "column": 9}}, "26": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": 33}}, "27": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 22}}, "28": {"start": {"line": 129, "column": 18}, "end": {"line": 137, "column": 5}}, "29": {"start": {"line": 139, "column": 4}, "end": {"line": 153, "column": 7}}, "30": {"start": {"line": 155, "column": 4}, "end": {"line": 159, "column": 7}}, "31": {"start": {"line": 168, "column": 0}, "end": {"line": 199, "column": 2}}, "32": {"start": {"line": 169, "column": 2}, "end": {"line": 198, "column": 3}}, "33": {"start": {"line": 170, "column": 17}, "end": {"line": 179, "column": 6}}, "34": {"start": {"line": 181, "column": 4}, "end": {"line": 186, "column": 5}}, "35": {"start": {"line": 182, "column": 6}, "end": {"line": 185, "column": 9}}, "36": {"start": {"line": 188, "column": 4}, "end": {"line": 191, "column": 7}}, "37": {"start": {"line": 193, "column": 4}, "end": {"line": 197, "column": 7}}, "38": {"start": {"line": 206, "column": 0}, "end": {"line": 243, "column": 2}}, "39": {"start": {"line": 207, "column": 2}, "end": {"line": 242, "column": 3}}, "40": {"start": {"line": 208, "column": 29}, "end": {"line": 208, "column": 37}}, "41": {"start": {"line": 210, "column": 17}, "end": {"line": 210, "column": 49}}, "42": {"start": {"line": 212, "column": 4}, "end": {"line": 217, "column": 5}}, "43": {"start": {"line": 213, "column": 6}, "end": {"line": 216, "column": 9}}, "44": {"start": {"line": 220, "column": 4}, "end": {"line": 223, "column": 7}}, "45": {"start": {"line": 225, "column": 4}, "end": {"line": 235, "column": 7}}, "46": {"start": {"line": 237, "column": 4}, "end": {"line": 241, "column": 7}}, "47": {"start": {"line": 250, "column": 0}, "end": {"line": 287, "column": 2}}, "48": {"start": {"line": 251, "column": 2}, "end": {"line": 286, "column": 3}}, "49": {"start": {"line": 252, "column": 45}, "end": {"line": 252, "column": 53}}, "50": {"start": {"line": 254, "column": 17}, "end": {"line": 254, "column": 49}}, "51": {"start": {"line": 256, "column": 4}, "end": {"line": 261, "column": 5}}, "52": {"start": {"line": 257, "column": 6}, "end": {"line": 260, "column": 9}}, "53": {"start": {"line": 264, "column": 20}, "end": {"line": 264, "column": 63}}, "54": {"start": {"line": 265, "column": 4}, "end": {"line": 270, "column": 5}}, "55": {"start": {"line": 266, "column": 6}, "end": {"line": 269, "column": 9}}, "56": {"start": {"line": 273, "column": 4}, "end": {"line": 273, "column": 32}}, "57": {"start": {"line": 274, "column": 4}, "end": {"line": 274, "column": 22}}, "58": {"start": {"line": 276, "column": 4}, "end": {"line": 279, "column": 7}}, "59": {"start": {"line": 281, "column": 4}, "end": {"line": 285, "column": 7}}, "60": {"start": {"line": 296, "column": 0}, "end": {"line": 329, "column": 2}}, "61": {"start": {"line": 297, "column": 2}, "end": {"line": 328, "column": 3}}, "62": {"start": {"line": 299, "column": 4}, "end": {"line": 304, "column": 5}}, "63": {"start": {"line": 300, "column": 6}, "end": {"line": 303, "column": 9}}, "64": {"start": {"line": 306, "column": 18}, "end": {"line": 315, "column": 6}}, "65": {"start": {"line": 317, "column": 4}, "end": {"line": 321, "column": 7}}, "66": {"start": {"line": 323, "column": 4}, "end": {"line": 327, "column": 7}}, "67": {"start": {"line": 336, "column": 0}, "end": {"line": 375, "column": 2}}, "68": {"start": {"line": 337, "column": 2}, "end": {"line": 374, "column": 3}}, "69": {"start": {"line": 339, "column": 4}, "end": {"line": 344, "column": 5}}, "70": {"start": {"line": 340, "column": 6}, "end": {"line": 343, "column": 9}}, "71": {"start": {"line": 346, "column": 17}, "end": {"line": 355, "column": 6}}, "72": {"start": {"line": 357, "column": 4}, "end": {"line": 362, "column": 5}}, "73": {"start": {"line": 358, "column": 6}, "end": {"line": 361, "column": 9}}, "74": {"start": {"line": 364, "column": 4}, "end": {"line": 367, "column": 7}}, "75": {"start": {"line": 369, "column": 4}, "end": {"line": 373, "column": 7}}, "76": {"start": {"line": 382, "column": 0}, "end": {"line": 447, "column": 2}}, "77": {"start": {"line": 383, "column": 2}, "end": {"line": 446, "column": 3}}, "78": {"start": {"line": 385, "column": 4}, "end": {"line": 390, "column": 5}}, "79": {"start": {"line": 386, "column": 6}, "end": {"line": 389, "column": 9}}, "80": {"start": {"line": 392, "column": 59}, "end": {"line": 392, "column": 67}}, "81": {"start": {"line": 394, "column": 17}, "end": {"line": 394, "column": 51}}, "82": {"start": {"line": 396, "column": 4}, "end": {"line": 401, "column": 5}}, "83": {"start": {"line": 397, "column": 6}, "end": {"line": 400, "column": 9}}, "84": {"start": {"line": 404, "column": 4}, "end": {"line": 412, "column": 5}}, "85": {"start": {"line": 405, "column": 19}, "end": {"line": 405, "column": 47}}, "86": {"start": {"line": 406, "column": 6}, "end": {"line": 411, "column": 7}}, "87": {"start": {"line": 407, "column": 8}, "end": {"line": 410, "column": 11}}, "88": {"start": {"line": 415, "column": 4}, "end": {"line": 421, "column": 7}}, "89": {"start": {"line": 424, "column": 24}, "end": {"line": 433, "column": 6}}, "90": {"start": {"line": 435, "column": 4}, "end": {"line": 439, "column": 7}}, "91": {"start": {"line": 441, "column": 4}, "end": {"line": 445, "column": 7}}, "92": {"start": {"line": 454, "column": 0}, "end": {"line": 494, "column": 2}}, "93": {"start": {"line": 455, "column": 2}, "end": {"line": 493, "column": 3}}, "94": {"start": {"line": 457, "column": 4}, "end": {"line": 462, "column": 5}}, "95": {"start": {"line": 458, "column": 6}, "end": {"line": 461, "column": 9}}, "96": {"start": {"line": 464, "column": 17}, "end": {"line": 464, "column": 51}}, "97": {"start": {"line": 466, "column": 4}, "end": {"line": 471, "column": 5}}, "98": {"start": {"line": 467, "column": 6}, "end": {"line": 470, "column": 9}}, "99": {"start": {"line": 474, "column": 4}, "end": {"line": 479, "column": 5}}, "100": {"start": {"line": 475, "column": 6}, "end": {"line": 478, "column": 9}}, "101": {"start": {"line": 481, "column": 4}, "end": {"line": 481, "column": 25}}, "102": {"start": {"line": 483, "column": 4}, "end": {"line": 486, "column": 7}}, "103": {"start": {"line": 488, "column": 4}, "end": {"line": 492, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 16, "column": 19}, "end": {"line": 16, "column": 20}}, "loc": {"start": {"line": 16, "column": 39}, "end": {"line": 77, "column": 1}}, "line": 16}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 84, "column": 16}, "end": {"line": 84, "column": 17}}, "loc": {"start": {"line": 84, "column": 36}, "end": {"line": 161, "column": 1}}, "line": 84}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 168, "column": 25}, "end": {"line": 168, "column": 26}}, "loc": {"start": {"line": 168, "column": 45}, "end": {"line": 199, "column": 1}}, "line": 168}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 206, "column": 28}, "end": {"line": 206, "column": 29}}, "loc": {"start": {"line": 206, "column": 48}, "end": {"line": 243, "column": 1}}, "line": 206}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 250, "column": 25}, "end": {"line": 250, "column": 26}}, "loc": {"start": {"line": 250, "column": 45}, "end": {"line": 287, "column": 1}}, "line": 250}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 296, "column": 22}, "end": {"line": 296, "column": 23}}, "loc": {"start": {"line": 296, "column": 42}, "end": {"line": 329, "column": 1}}, "line": 296}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 336, "column": 22}, "end": {"line": 336, "column": 23}}, "loc": {"start": {"line": 336, "column": 42}, "end": {"line": 375, "column": 1}}, "line": 336}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 382, "column": 21}, "end": {"line": 382, "column": 22}}, "loc": {"start": {"line": 382, "column": 41}, "end": {"line": 447, "column": 1}}, "line": 382}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 454, "column": 21}, "end": {"line": 454, "column": 22}}, "loc": {"start": {"line": 454, "column": 41}, "end": {"line": 494, "column": 1}}, "line": 454}}, "branchMap": {"0": {"loc": {"start": {"line": 30, "column": 4}, "end": {"line": 35, "column": 5}}, "type": "if", "locations": [{"start": {"line": 30, "column": 4}, "end": {"line": 35, "column": 5}}, {"start": {}, "end": {}}], "line": 30}, "1": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 47, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 47, "column": 5}}, {"start": {}, "end": {}}], "line": 42}, "2": {"loc": {"start": {"line": 100, "column": 4}, "end": {"line": 105, "column": 5}}, "type": "if", "locations": [{"start": {"line": 100, "column": 4}, "end": {"line": 105, "column": 5}}, {"start": {}, "end": {}}], "line": 100}, "3": {"loc": {"start": {"line": 109, "column": 4}, "end": {"line": 114, "column": 5}}, "type": "if", "locations": [{"start": {"line": 109, "column": 4}, "end": {"line": 114, "column": 5}}, {"start": {}, "end": {}}], "line": 109}, "4": {"loc": {"start": {"line": 117, "column": 4}, "end": {"line": 122, "column": 5}}, "type": "if", "locations": [{"start": {"line": 117, "column": 4}, "end": {"line": 122, "column": 5}}, {"start": {}, "end": {}}], "line": 117}, "5": {"loc": {"start": {"line": 133, "column": 19}, "end": {"line": 133, "column": 60}}, "type": "cond-expr", "locations": [{"start": {"line": 133, "column": 35}, "end": {"line": 133, "column": 53}}, {"start": {"line": 133, "column": 56}, "end": {"line": 133, "column": 60}}], "line": 133}, "6": {"loc": {"start": {"line": 136, "column": 19}, "end": {"line": 136, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 136, "column": 19}, "end": {"line": 136, "column": 45}}, {"start": {"line": 136, "column": 49}, "end": {"line": 136, "column": 53}}], "line": 136}, "7": {"loc": {"start": {"line": 150, "column": 21}, "end": {"line": 150, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 150, "column": 37}, "end": {"line": 150, "column": 55}}, {"start": {"line": 150, "column": 58}, "end": {"line": 150, "column": 62}}], "line": 150}, "8": {"loc": {"start": {"line": 181, "column": 4}, "end": {"line": 186, "column": 5}}, "type": "if", "locations": [{"start": {"line": 181, "column": 4}, "end": {"line": 186, "column": 5}}, {"start": {}, "end": {}}], "line": 181}, "9": {"loc": {"start": {"line": 212, "column": 4}, "end": {"line": 217, "column": 5}}, "type": "if", "locations": [{"start": {"line": 212, "column": 4}, "end": {"line": 217, "column": 5}}, {"start": {}, "end": {}}], "line": 212}, "10": {"loc": {"start": {"line": 221, "column": 13}, "end": {"line": 221, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 221, "column": 13}, "end": {"line": 221, "column": 18}}, {"start": {"line": 221, "column": 22}, "end": {"line": 221, "column": 32}}], "line": 221}, "11": {"loc": {"start": {"line": 222, "column": 13}, "end": {"line": 222, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 222, "column": 13}, "end": {"line": 222, "column": 18}}, {"start": {"line": 222, "column": 22}, "end": {"line": 222, "column": 32}}], "line": 222}, "12": {"loc": {"start": {"line": 256, "column": 4}, "end": {"line": 261, "column": 5}}, "type": "if", "locations": [{"start": {"line": 256, "column": 4}, "end": {"line": 261, "column": 5}}, {"start": {}, "end": {}}], "line": 256}, "13": {"loc": {"start": {"line": 265, "column": 4}, "end": {"line": 270, "column": 5}}, "type": "if", "locations": [{"start": {"line": 265, "column": 4}, "end": {"line": 270, "column": 5}}, {"start": {}, "end": {}}], "line": 265}, "14": {"loc": {"start": {"line": 299, "column": 4}, "end": {"line": 304, "column": 5}}, "type": "if", "locations": [{"start": {"line": 299, "column": 4}, "end": {"line": 304, "column": 5}}, {"start": {}, "end": {}}], "line": 299}, "15": {"loc": {"start": {"line": 339, "column": 4}, "end": {"line": 344, "column": 5}}, "type": "if", "locations": [{"start": {"line": 339, "column": 4}, "end": {"line": 344, "column": 5}}, {"start": {}, "end": {}}], "line": 339}, "16": {"loc": {"start": {"line": 357, "column": 4}, "end": {"line": 362, "column": 5}}, "type": "if", "locations": [{"start": {"line": 357, "column": 4}, "end": {"line": 362, "column": 5}}, {"start": {}, "end": {}}], "line": 357}, "17": {"loc": {"start": {"line": 385, "column": 4}, "end": {"line": 390, "column": 5}}, "type": "if", "locations": [{"start": {"line": 385, "column": 4}, "end": {"line": 390, "column": 5}}, {"start": {}, "end": {}}], "line": 385}, "18": {"loc": {"start": {"line": 396, "column": 4}, "end": {"line": 401, "column": 5}}, "type": "if", "locations": [{"start": {"line": 396, "column": 4}, "end": {"line": 401, "column": 5}}, {"start": {}, "end": {}}], "line": 396}, "19": {"loc": {"start": {"line": 404, "column": 4}, "end": {"line": 412, "column": 5}}, "type": "if", "locations": [{"start": {"line": 404, "column": 4}, "end": {"line": 412, "column": 5}}, {"start": {}, "end": {}}], "line": 404}, "20": {"loc": {"start": {"line": 406, "column": 6}, "end": {"line": 411, "column": 7}}, "type": "if", "locations": [{"start": {"line": 406, "column": 6}, "end": {"line": 411, "column": 7}}, {"start": {}, "end": {}}], "line": 406}, "21": {"loc": {"start": {"line": 416, "column": 16}, "end": {"line": 416, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 416, "column": 16}, "end": {"line": 416, "column": 24}}, {"start": {"line": 416, "column": 28}, "end": {"line": 416, "column": 41}}], "line": 416}, "22": {"loc": {"start": {"line": 417, "column": 13}, "end": {"line": 417, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 417, "column": 13}, "end": {"line": 417, "column": 18}}, {"start": {"line": 417, "column": 22}, "end": {"line": 417, "column": 32}}], "line": 417}, "23": {"loc": {"start": {"line": 418, "column": 13}, "end": {"line": 418, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 418, "column": 13}, "end": {"line": 418, "column": 18}}, {"start": {"line": 418, "column": 22}, "end": {"line": 418, "column": 32}}], "line": 418}, "24": {"loc": {"start": {"line": 419, "column": 15}, "end": {"line": 419, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 419, "column": 15}, "end": {"line": 419, "column": 22}}, {"start": {"line": 419, "column": 26}, "end": {"line": 419, "column": 38}}], "line": 419}, "25": {"loc": {"start": {"line": 420, "column": 17}, "end": {"line": 420, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 420, "column": 43}, "end": {"line": 420, "column": 52}}, {"start": {"line": 420, "column": 55}, "end": {"line": 420, "column": 69}}], "line": 420}, "26": {"loc": {"start": {"line": 457, "column": 4}, "end": {"line": 462, "column": 5}}, "type": "if", "locations": [{"start": {"line": 457, "column": 4}, "end": {"line": 462, "column": 5}}, {"start": {}, "end": {}}], "line": 457}, "27": {"loc": {"start": {"line": 466, "column": 4}, "end": {"line": 471, "column": 5}}, "type": "if", "locations": [{"start": {"line": 466, "column": 4}, "end": {"line": 471, "column": 5}}, {"start": {}, "end": {}}], "line": 466}, "28": {"loc": {"start": {"line": 474, "column": 4}, "end": {"line": 479, "column": 5}}, "type": "if", "locations": [{"start": {"line": 474, "column": 4}, "end": {"line": 479, "column": 5}}, {"start": {}, "end": {}}], "line": 474}, "29": {"loc": {"start": {"line": 474, "column": 8}, "end": {"line": 474, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 474, "column": 8}, "end": {"line": 474, "column": 29}}, {"start": {"line": 474, "column": 33}, "end": {"line": 474, "column": 56}}], "line": 474}}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 13, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 13, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 13, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 13, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 13, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 13, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 13, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 13, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "2a8fbb83a39ee748956ebc33c5a78525721d5939"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\middlewares\\authMiddleware.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\middlewares\\authMiddleware.js", "statementMap": {"0": {"start": {"line": 7, "column": 12}, "end": {"line": 7, "column": 35}}, "1": {"start": {"line": 8, "column": 35}, "end": {"line": 8, "column": 55}}, "2": {"start": {"line": 16, "column": 23}, "end": {"line": 106, "column": 1}}, "3": {"start": {"line": 17, "column": 2}, "end": {"line": 105, "column": 3}}, "4": {"start": {"line": 19, "column": 23}, "end": {"line": 19, "column": 48}}, "5": {"start": {"line": 21, "column": 4}, "end": {"line": 26, "column": 5}}, "6": {"start": {"line": 22, "column": 6}, "end": {"line": 25, "column": 9}}, "7": {"start": {"line": 29, "column": 18}, "end": {"line": 29, "column": 42}}, "8": {"start": {"line": 31, "column": 4}, "end": {"line": 36, "column": 5}}, "9": {"start": {"line": 32, "column": 6}, "end": {"line": 35, "column": 9}}, "10": {"start": {"line": 39, "column": 20}, "end": {"line": 39, "column": 61}}, "11": {"start": {"line": 42, "column": 17}, "end": {"line": 57, "column": 6}}, "12": {"start": {"line": 59, "column": 4}, "end": {"line": 64, "column": 5}}, "13": {"start": {"line": 60, "column": 6}, "end": {"line": 63, "column": 9}}, "14": {"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": 5}}, "15": {"start": {"line": 68, "column": 6}, "end": {"line": 71, "column": 9}}, "16": {"start": {"line": 75, "column": 4}, "end": {"line": 82, "column": 6}}, "17": {"start": {"line": 81, "column": 70}, "end": {"line": 81, "column": 76}}, "18": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 11}}, "19": {"start": {"line": 86, "column": 4}, "end": {"line": 91, "column": 5}}, "20": {"start": {"line": 87, "column": 6}, "end": {"line": 90, "column": 9}}, "21": {"start": {"line": 93, "column": 4}, "end": {"line": 98, "column": 5}}, "22": {"start": {"line": 94, "column": 6}, "end": {"line": 97, "column": 9}}, "23": {"start": {"line": 100, "column": 4}, "end": {"line": 104, "column": 7}}, "24": {"start": {"line": 113, "column": 24}, "end": {"line": 130, "column": 1}}, "25": {"start": {"line": 114, "column": 2}, "end": {"line": 129, "column": 4}}, "26": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, "27": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 20}}, "28": {"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, "29": {"start": {"line": 122, "column": 6}, "end": {"line": 122, "column": 20}}, "30": {"start": {"line": 125, "column": 4}, "end": {"line": 128, "column": 7}}, "31": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 32}}, "32": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 49}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 16, "column": 23}, "end": {"line": 16, "column": 24}}, "loc": {"start": {"line": 16, "column": 49}, "end": {"line": 106, "column": 1}}, "line": 16}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 81, "column": 65}, "end": {"line": 81, "column": 66}}, "loc": {"start": {"line": 81, "column": 70}, "end": {"line": 81, "column": 76}}, "line": 81}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 113, "column": 24}, "end": {"line": 113, "column": 25}}, "loc": {"start": {"line": 113, "column": 44}, "end": {"line": 130, "column": 1}}, "line": 113}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 114, "column": 9}, "end": {"line": 114, "column": 10}}, "loc": {"start": {"line": 114, "column": 29}, "end": {"line": 129, "column": 3}}, "line": 114}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 4}, "end": {"line": 26, "column": 5}}, "type": "if", "locations": [{"start": {"line": 21, "column": 4}, "end": {"line": 26, "column": 5}}, {"start": {}, "end": {}}], "line": 21}, "1": {"loc": {"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": 19}}, {"start": {"line": 21, "column": 23}, "end": {"line": 21, "column": 56}}], "line": 21}, "2": {"loc": {"start": {"line": 31, "column": 4}, "end": {"line": 36, "column": 5}}, "type": "if", "locations": [{"start": {"line": 31, "column": 4}, "end": {"line": 36, "column": 5}}, {"start": {}, "end": {}}], "line": 31}, "3": {"loc": {"start": {"line": 59, "column": 4}, "end": {"line": 64, "column": 5}}, "type": "if", "locations": [{"start": {"line": 59, "column": 4}, "end": {"line": 64, "column": 5}}, {"start": {}, "end": {}}], "line": 59}, "4": {"loc": {"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": 5}}, "type": "if", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": 5}}, {"start": {}, "end": {}}], "line": 67}, "5": {"loc": {"start": {"line": 79, "column": 12}, "end": {"line": 79, "column": 96}}, "type": "cond-expr", "locations": [{"start": {"line": 79, "column": 28}, "end": {"line": 79, "column": 81}}, {"start": {"line": 79, "column": 84}, "end": {"line": 79, "column": 96}}], "line": 79}, "6": {"loc": {"start": {"line": 79, "column": 28}, "end": {"line": 79, "column": 81}}, "type": "cond-expr", "locations": [{"start": {"line": 79, "column": 59}, "end": {"line": 79, "column": 66}}, {"start": {"line": 79, "column": 69}, "end": {"line": 79, "column": 81}}], "line": 79}, "7": {"loc": {"start": {"line": 81, "column": 19}, "end": {"line": 81, "column": 82}}, "type": "cond-expr", "locations": [{"start": {"line": 81, "column": 35}, "end": {"line": 81, "column": 77}}, {"start": {"line": 81, "column": 80}, "end": {"line": 81, "column": 82}}], "line": 81}, "8": {"loc": {"start": {"line": 86, "column": 4}, "end": {"line": 91, "column": 5}}, "type": "if", "locations": [{"start": {"line": 86, "column": 4}, "end": {"line": 91, "column": 5}}, {"start": {}, "end": {}}], "line": 86}, "9": {"loc": {"start": {"line": 93, "column": 4}, "end": {"line": 98, "column": 5}}, "type": "if", "locations": [{"start": {"line": 93, "column": 4}, "end": {"line": 98, "column": 5}}, {"start": {}, "end": {}}], "line": 93}, "10": {"loc": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, "type": "if", "locations": [{"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, {"start": {}, "end": {}}], "line": 116}, "11": {"loc": {"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, "type": "if", "locations": [{"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, {"start": {}, "end": {}}], "line": 121}}, "s": {"0": 15, "1": 15, "2": 15, "3": 45, "4": 45, "5": 45, "6": 7, "7": 38, "8": 38, "9": 0, "10": 38, "11": 35, "12": 35, "13": 1, "14": 34, "15": 1, "16": 33, "17": 0, "18": 33, "19": 3, "20": 2, "21": 1, "22": 1, "23": 0, "24": 15, "25": 325, "26": 17, "27": 0, "28": 17, "29": 0, "30": 17, "31": 15, "32": 15}, "f": {"0": 45, "1": 0, "2": 325, "3": 17}, "b": {"0": [7, 38], "1": [45, 38], "2": [0, 38], "3": [1, 34], "4": [1, 33], "5": [0, 33], "6": [0, 0], "7": [0, 33], "8": [2, 1], "9": [1, 0], "10": [0, 17], "11": [0, 17]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "45ba5e58f7d56f9cc975c7a35f8071ece1a24e75"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\middlewares\\sessionMiddleware.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\middlewares\\sessionMiddleware.js", "statementMap": {"0": {"start": {"line": 10, "column": 16}, "end": {"line": 10, "column": 42}}, "1": {"start": {"line": 13, "column": 26}, "end": {"line": 21, "column": 2}}, "2": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 35}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 10}, "end": {"line": 14, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 14, "column": 10}, "end": {"line": 14, "column": 36}}, {"start": {"line": 14, "column": 40}, "end": {"line": 14, "column": 65}}], "line": 14}}, "s": {"0": 13, "1": 13, "2": 13}, "f": {}, "b": {"0": [13, 13]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e9e9e23a73d8a3941a3581b4176c2af75347d372"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\activity-attachment.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\activity-attachment.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 85, "column": 2}}, "1": {"start": {"line": 10, "column": 29}, "end": {"line": 67, "column": 4}}, "2": {"start": {"line": 70, "column": 2}, "end": {"line": 82, "column": 4}}, "3": {"start": {"line": 72, "column": 4}, "end": {"line": 75, "column": 7}}, "4": {"start": {"line": 78, "column": 4}, "end": {"line": 81, "column": 7}}, "5": {"start": {"line": 84, "column": 2}, "end": {"line": 84, "column": 28}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 43}, "end": {"line": 85, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 70, "column": 33}, "end": {"line": 70, "column": 34}}, "loc": {"start": {"line": 70, "column": 45}, "end": {"line": 82, "column": 3}}, "line": 70}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27, "5": 27}, "f": {"0": 27, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "b95508f91b9a98cfea10a2c3031c84a23e805a7c"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\activity.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\activity.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 85, "column": 2}}, "1": {"start": {"line": 10, "column": 19}, "end": {"line": 61, "column": 4}}, "2": {"start": {"line": 64, "column": 2}, "end": {"line": 82, "column": 4}}, "3": {"start": {"line": 66, "column": 4}, "end": {"line": 69, "column": 7}}, "4": {"start": {"line": 72, "column": 4}, "end": {"line": 75, "column": 7}}, "5": {"start": {"line": 78, "column": 4}, "end": {"line": 81, "column": 7}}, "6": {"start": {"line": 84, "column": 2}, "end": {"line": 84, "column": 18}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 43}, "end": {"line": 85, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 64, "column": 23}, "end": {"line": 64, "column": 24}}, "loc": {"start": {"line": 64, "column": 35}, "end": {"line": 82, "column": 3}}, "line": 64}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27, "5": 27, "6": 27}, "f": {"0": 27, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "70abe50a96f576e892b20e60b62fdb04bc5b917b"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\ai-assistant.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\ai-assistant.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 120, "column": 2}}, "1": {"start": {"line": 10, "column": 22}, "end": {"line": 102, "column": 4}}, "2": {"start": {"line": 105, "column": 2}, "end": {"line": 117, "column": 4}}, "3": {"start": {"line": 107, "column": 4}, "end": {"line": 110, "column": 7}}, "4": {"start": {"line": 113, "column": 4}, "end": {"line": 116, "column": 7}}, "5": {"start": {"line": 119, "column": 2}, "end": {"line": 119, "column": 21}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 43}, "end": {"line": 120, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 105, "column": 26}, "end": {"line": 105, "column": 27}}, "loc": {"start": {"line": 105, "column": 38}, "end": {"line": 117, "column": 3}}, "line": 105}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27, "5": 27}, "f": {"0": 27, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "19f151a3f01177e40be39dc3cf77f7b183956be0"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\comment.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\comment.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 93, "column": 2}}, "1": {"start": {"line": 10, "column": 18}, "end": {"line": 75, "column": 4}}, "2": {"start": {"line": 78, "column": 2}, "end": {"line": 90, "column": 4}}, "3": {"start": {"line": 80, "column": 4}, "end": {"line": 83, "column": 7}}, "4": {"start": {"line": 86, "column": 4}, "end": {"line": 89, "column": 7}}, "5": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 17}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 43}, "end": {"line": 93, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 78, "column": 22}, "end": {"line": 78, "column": 23}}, "loc": {"start": {"line": 78, "column": 34}, "end": {"line": 90, "column": 3}}, "line": 78}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27, "5": 27}, "f": {"0": 27, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "fa95881976d2102688ec79732810d1b89349a0cb"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\file.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\file.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 126, "column": 2}}, "1": {"start": {"line": 10, "column": 15}, "end": {"line": 102, "column": 4}}, "2": {"start": {"line": 105, "column": 2}, "end": {"line": 123, "column": 4}}, "3": {"start": {"line": 107, "column": 4}, "end": {"line": 110, "column": 7}}, "4": {"start": {"line": 113, "column": 4}, "end": {"line": 116, "column": 7}}, "5": {"start": {"line": 119, "column": 4}, "end": {"line": 122, "column": 7}}, "6": {"start": {"line": 125, "column": 2}, "end": {"line": 125, "column": 14}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 43}, "end": {"line": 126, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 105, "column": 19}, "end": {"line": 105, "column": 20}}, "loc": {"start": {"line": 105, "column": 31}, "end": {"line": 123, "column": 3}}, "line": 105}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27, "5": 27, "6": 27}, "f": {"0": 27, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "62d1c364a3db65dd0dae8e006f4e7d6867f1651b"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\index.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\index.js", "statementMap": {"0": {"start": {"line": 7, "column": 11}, "end": {"line": 7, "column": 24}}, "1": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 28}}, "2": {"start": {"line": 9, "column": 18}, "end": {"line": 9, "column": 38}}, "3": {"start": {"line": 10, "column": 17}, "end": {"line": 10, "column": 42}}, "4": {"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 49}}, "5": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 55}}, "6": {"start": {"line": 13, "column": 11}, "end": {"line": 13, "column": 13}}, "7": {"start": {"line": 17, "column": 0}, "end": {"line": 21, "column": 1}}, "8": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 74}}, "9": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 87}}, "10": {"start": {"line": 24, "column": 0}, "end": {"line": 32, "column": 5}}, "11": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 90}}, "12": {"start": {"line": 30, "column": 18}, "end": {"line": 30, "column": 85}}, "13": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 27}}, "14": {"start": {"line": 35, "column": 0}, "end": {"line": 39, "column": 3}}, "15": {"start": {"line": 36, "column": 2}, "end": {"line": 38, "column": 3}}, "16": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 32}}, "17": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 25}}, "18": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 25}}, "19": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 20}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 26, "column": 10}, "end": {"line": 26, "column": 11}}, "loc": {"start": {"line": 26, "column": 18}, "end": {"line": 28, "column": 3}}, "line": 26}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 29, "column": 11}, "end": {"line": 29, "column": 12}}, "loc": {"start": {"line": 29, "column": 19}, "end": {"line": 32, "column": 3}}, "line": 29}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 35, "column": 24}, "end": {"line": 35, "column": 25}}, "loc": {"start": {"line": 35, "column": 37}, "end": {"line": 39, "column": 1}}, "line": 35}}, "branchMap": {"0": {"loc": {"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 32}}, {"start": {"line": 11, "column": 36}, "end": {"line": 11, "column": 49}}], "line": 11}, "1": {"loc": {"start": {"line": 17, "column": 0}, "end": {"line": 21, "column": 1}}, "type": "if", "locations": [{"start": {"line": 17, "column": 0}, "end": {"line": 21, "column": 1}}, {"start": {"line": 19, "column": 7}, "end": {"line": 21, "column": 1}}], "line": 17}, "2": {"loc": {"start": {"line": 27, "column": 11}, "end": {"line": 27, "column": 89}}, "type": "binary-expr", "locations": [{"start": {"line": 27, "column": 12}, "end": {"line": 27, "column": 35}}, {"start": {"line": 27, "column": 41}, "end": {"line": 27, "column": 58}}, {"start": {"line": 27, "column": 64}, "end": {"line": 27, "column": 88}}], "line": 27}, "3": {"loc": {"start": {"line": 36, "column": 2}, "end": {"line": 38, "column": 3}}, "type": "if", "locations": [{"start": {"line": 36, "column": 2}, "end": {"line": 38, "column": 3}}, {"start": {}, "end": {}}], "line": 36}}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27, "5": 27, "6": 27, "7": 27, "8": 0, "9": 27, "10": 27, "11": 351, "12": 324, "13": 324, "14": 27, "15": 324, "16": 297, "17": 27, "18": 27, "19": 27}, "f": {"0": 351, "1": 324, "2": 324}, "b": {"0": [27, 0], "1": [0, 27], "2": [351, 351, 324], "3": [297, 27]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e0157178f91ee67f31ee26f72e8fd2188198e64c"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\knowledge-base-access.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\knowledge-base-access.model.js", "statementMap": {"0": {"start": {"line": 8, "column": 0}, "end": {"line": 79, "column": 2}}, "1": {"start": {"line": 9, "column": 30}, "end": {"line": 59, "column": 4}}, "2": {"start": {"line": 61, "column": 2}, "end": {"line": 76, "column": 4}}, "3": {"start": {"line": 62, "column": 4}, "end": {"line": 65, "column": 7}}, "4": {"start": {"line": 67, "column": 4}, "end": {"line": 70, "column": 7}}, "5": {"start": {"line": 72, "column": 4}, "end": {"line": 75, "column": 7}}, "6": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 29}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 17}, "end": {"line": 8, "column": 18}}, "loc": {"start": {"line": 8, "column": 43}, "end": {"line": 79, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 61, "column": 34}, "end": {"line": 61, "column": 35}}, "loc": {"start": {"line": 61, "column": 46}, "end": {"line": 76, "column": 3}}, "line": 61}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27, "5": 27, "6": 27}, "f": {"0": 27, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "b3e121065fed09f4090446afa16d4bea0017d4ff"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\knowledge-base.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\knowledge-base.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 90, "column": 2}}, "1": {"start": {"line": 10, "column": 24}, "end": {"line": 64, "column": 4}}, "2": {"start": {"line": 67, "column": 2}, "end": {"line": 87, "column": 4}}, "3": {"start": {"line": 69, "column": 4}, "end": {"line": 72, "column": 7}}, "4": {"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 7}}, "5": {"start": {"line": 81, "column": 4}, "end": {"line": 86, "column": 7}}, "6": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 23}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 43}, "end": {"line": 90, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 67, "column": 28}, "end": {"line": 67, "column": 29}}, "loc": {"start": {"line": 67, "column": 40}, "end": {"line": 87, "column": 3}}, "line": 67}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27, "5": 27, "6": 27}, "f": {"0": 27, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a1b64601e15d6387b9dd3cf8fd6b49fc0ad1ed41"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\notification.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\notification.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 76, "column": 2}}, "1": {"start": {"line": 10, "column": 23}, "end": {"line": 64, "column": 4}}, "2": {"start": {"line": 67, "column": 2}, "end": {"line": 73, "column": 4}}, "3": {"start": {"line": 69, "column": 4}, "end": {"line": 72, "column": 7}}, "4": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 43}, "end": {"line": 76, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 67, "column": 27}, "end": {"line": 67, "column": 28}}, "loc": {"start": {"line": 67, "column": 39}, "end": {"line": 73, "column": 3}}, "line": 67}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27}, "f": {"0": 27, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "5423905f213da79f835facf3c7542984b51385a3"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\permission.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\permission.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 59, "column": 2}}, "1": {"start": {"line": 10, "column": 21}, "end": {"line": 45, "column": 4}}, "2": {"start": {"line": 48, "column": 2}, "end": {"line": 56, "column": 4}}, "3": {"start": {"line": 50, "column": 4}, "end": {"line": 55, "column": 7}}, "4": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 20}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 43}, "end": {"line": 59, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 48, "column": 25}, "end": {"line": 48, "column": 26}}, "loc": {"start": {"line": 48, "column": 37}, "end": {"line": 56, "column": 3}}, "line": 48}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27}, "f": {"0": 27, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a34129df3e8df727734fce873efd865d51b17982"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\role.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\role.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 56, "column": 2}}, "1": {"start": {"line": 10, "column": 15}, "end": {"line": 36, "column": 4}}, "2": {"start": {"line": 39, "column": 2}, "end": {"line": 53, "column": 4}}, "3": {"start": {"line": 41, "column": 4}, "end": {"line": 44, "column": 7}}, "4": {"start": {"line": 47, "column": 4}, "end": {"line": 52, "column": 7}}, "5": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 14}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 43}, "end": {"line": 56, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 39, "column": 19}, "end": {"line": 39, "column": 20}}, "loc": {"start": {"line": 39, "column": 31}, "end": {"line": 53, "column": 3}}, "line": 39}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27, "3": 27, "4": 27, "5": 27}, "f": {"0": 27, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "306cb7e6ccc9d81c654547b2110420ecc03f2553"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\system-config.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\system-config.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 104, "column": 2}}, "1": {"start": {"line": 10, "column": 23}, "end": {"line": 101, "column": 4}}, "2": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 43}, "end": {"line": 104, "column": 1}}, "line": 9}}, "branchMap": {}, "s": {"0": 27, "1": 27, "2": 27}, "f": {"0": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "cc5bcfb0b1c32aa431f513f03f8bae509f9e743d"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\user.model.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\models\\user.model.js", "statementMap": {"0": {"start": {"line": 9, "column": 15}, "end": {"line": 9, "column": 34}}, "1": {"start": {"line": 11, "column": 0}, "end": {"line": 107, "column": 2}}, "2": {"start": {"line": 12, "column": 15}, "end": {"line": 88, "column": 4}}, "3": {"start": {"line": 76, "column": 8}, "end": {"line": 79, "column": 9}}, "4": {"start": {"line": 77, "column": 23}, "end": {"line": 77, "column": 47}}, "5": {"start": {"line": 78, "column": 10}, "end": {"line": 78, "column": 65}}, "6": {"start": {"line": 82, "column": 8}, "end": {"line": 85, "column": 9}}, "7": {"start": {"line": 83, "column": 23}, "end": {"line": 83, "column": 47}}, "8": {"start": {"line": 84, "column": 10}, "end": {"line": 84, "column": 65}}, "9": {"start": {"line": 91, "column": 2}, "end": {"line": 93, "column": 4}}, "10": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 60}}, "11": {"start": {"line": 96, "column": 2}, "end": {"line": 104, "column": 4}}, "12": {"start": {"line": 98, "column": 4}, "end": {"line": 101, "column": 7}}, "13": {"start": {"line": 106, "column": 2}, "end": {"line": 106, "column": 14}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": 18}}, "loc": {"start": {"line": 11, "column": 43}, "end": {"line": 107, "column": 1}}, "line": 11}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 75, "column": 20}, "end": {"line": 75, "column": 21}}, "loc": {"start": {"line": 75, "column": 36}, "end": {"line": 80, "column": 7}}, "line": 75}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 81, "column": 20}, "end": {"line": 81, "column": 21}}, "loc": {"start": {"line": 81, "column": 36}, "end": {"line": 86, "column": 7}}, "line": 81}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 91, "column": 35}, "end": {"line": 91, "column": 36}}, "loc": {"start": {"line": 91, "column": 69}, "end": {"line": 93, "column": 3}}, "line": 91}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 96, "column": 19}, "end": {"line": 96, "column": 20}}, "loc": {"start": {"line": 96, "column": 31}, "end": {"line": 104, "column": 3}}, "line": 96}}, "branchMap": {"0": {"loc": {"start": {"line": 76, "column": 8}, "end": {"line": 79, "column": 9}}, "type": "if", "locations": [{"start": {"line": 76, "column": 8}, "end": {"line": 79, "column": 9}}, {"start": {}, "end": {}}], "line": 76}, "1": {"loc": {"start": {"line": 82, "column": 8}, "end": {"line": 85, "column": 9}}, "type": "if", "locations": [{"start": {"line": 82, "column": 8}, "end": {"line": 85, "column": 9}}, {"start": {}, "end": {}}], "line": 82}}, "s": {"0": 27, "1": 27, "2": 27, "3": 25, "4": 25, "5": 25, "6": 5, "7": 2, "8": 2, "9": 27, "10": 2, "11": 27, "12": 27, "13": 27}, "f": {"0": 27, "1": 25, "2": 5, "3": 2, "4": 27}, "b": {"0": [25, 0], "1": [2, 3]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "d3727a69c5122fcb1bae4cd99942cb1bbd4a6b67"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\activity.routes.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\activity.routes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 72}}, "3": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": 63}}, "4": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 68}}, "5": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 32}}, "6": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 28}}, "7": {"start": {"line": 14, "column": 11}, "end": {"line": 14, "column": 24}}, "8": {"start": {"line": 17, "column": 16}, "end": {"line": 35, "column": 2}}, "9": {"start": {"line": 19, "column": 22}, "end": {"line": 19, "column": 68}}, "10": {"start": {"line": 20, "column": 21}, "end": {"line": 20, "column": 56}}, "11": {"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, "12": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 50}}, "13": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 23}}, "14": {"start": {"line": 31, "column": 25}, "end": {"line": 31, "column": 75}}, "15": {"start": {"line": 32, "column": 16}, "end": {"line": 32, "column": 47}}, "16": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 47}}, "17": {"start": {"line": 38, "column": 19}, "end": {"line": 51, "column": 1}}, "18": {"start": {"line": 40, "column": 23}, "end": {"line": 44, "column": 3}}, "19": {"start": {"line": 46, "column": 2}, "end": {"line": 50, "column": 3}}, "20": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 19}}, "21": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 56}}, "22": {"start": {"line": 54, "column": 15}, "end": {"line": 60, "column": 2}}, "23": {"start": {"line": 63, "column": 0}, "end": {"line": 65, "column": 2}}, "24": {"start": {"line": 68, "column": 0}, "end": {"line": 70, "column": 2}}, "25": {"start": {"line": 73, "column": 0}, "end": {"line": 77, "column": 2}}, "26": {"start": {"line": 80, "column": 0}, "end": {"line": 85, "column": 2}}, "27": {"start": {"line": 88, "column": 0}, "end": {"line": 92, "column": 2}}, "28": {"start": {"line": 95, "column": 0}, "end": {"line": 99, "column": 2}}, "29": {"start": {"line": 102, "column": 0}, "end": {"line": 104, "column": 2}}, "30": {"start": {"line": 107, "column": 0}, "end": {"line": 112, "column": 2}}, "31": {"start": {"line": 115, "column": 0}, "end": {"line": 117, "column": 2}}, "32": {"start": {"line": 120, "column": 0}, "end": {"line": 124, "column": 2}}, "33": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 15}, "end": {"line": 18, "column": 16}}, "loc": {"start": {"line": 18, "column": 34}, "end": {"line": 28, "column": 3}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 29, "column": 12}, "end": {"line": 29, "column": 13}}, "loc": {"start": {"line": 29, "column": 31}, "end": {"line": 34, "column": 3}}, "line": 29}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 38, "column": 19}, "end": {"line": 38, "column": 20}}, "loc": {"start": {"line": 38, "column": 38}, "end": {"line": 51, "column": 1}}, "line": 38}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 22}, "end": {"line": 19, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 22}, "end": {"line": 19, "column": 44}}, {"start": {"line": 19, "column": 48}, "end": {"line": 19, "column": 68}}], "line": 19}, "1": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, {"start": {}, "end": {}}], "line": 23}, "2": {"loc": {"start": {"line": 46, "column": 2}, "end": {"line": 50, "column": 3}}, "type": "if", "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 50, "column": 3}}, {"start": {"line": 48, "column": 9}, "end": {"line": 50, "column": 3}}], "line": 46}}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 13, "18": 0, "19": 0, "20": 0, "21": 0, "22": 13, "23": 13, "24": 13, "25": 13, "26": 13, "27": 13, "28": 13, "29": 13, "30": 13, "31": 13, "32": 13, "33": 13}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "10a1769d8af809d398cfd43865a15dc9316dece9"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\ai-assistant.routes.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\ai-assistant.routes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 30}, "end": {"line": 9, "column": 79}}, "3": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": 63}}, "4": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 68}}, "5": {"start": {"line": 14, "column": 0}, "end": {"line": 17, "column": 2}}, "6": {"start": {"line": 20, "column": 0}, "end": {"line": 23, "column": 2}}, "7": {"start": {"line": 26, "column": 0}, "end": {"line": 30, "column": 2}}, "8": {"start": {"line": 33, "column": 0}, "end": {"line": 37, "column": 2}}, "9": {"start": {"line": 40, "column": 0}, "end": {"line": 44, "column": 2}}, "10": {"start": {"line": 47, "column": 0}, "end": {"line": 51, "column": 2}}, "11": {"start": {"line": 54, "column": 0}, "end": {"line": 57, "column": 2}}, "12": {"start": {"line": 60, "column": 0}, "end": {"line": 63, "column": 2}}, "13": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13, "9": 13, "10": 13, "11": 13, "12": 13, "13": 13}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "8de0cfe7a7c73a5630d97cae5e815ab57df2615d"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\comment.routes.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\comment.routes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 26}, "end": {"line": 9, "column": 70}}, "3": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": 63}}, "4": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 68}}, "5": {"start": {"line": 14, "column": 0}, "end": {"line": 16, "column": 2}}, "6": {"start": {"line": 18, "column": 0}, "end": {"line": 21, "column": 2}}, "7": {"start": {"line": 25, "column": 0}, "end": {"line": 28, "column": 2}}, "8": {"start": {"line": 32, "column": 0}, "end": {"line": 36, "column": 2}}, "9": {"start": {"line": 40, "column": 0}, "end": {"line": 43, "column": 2}}, "10": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13, "9": 13, "10": 13}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "80e74ad271f34c9e6fb85b56abaa67345f32a11c"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\file.routes.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\file.routes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 64}}, "3": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": 63}}, "4": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 68}}, "5": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 32}}, "6": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 28}}, "7": {"start": {"line": 14, "column": 11}, "end": {"line": 14, "column": 24}}, "8": {"start": {"line": 17, "column": 16}, "end": {"line": 35, "column": 2}}, "9": {"start": {"line": 19, "column": 22}, "end": {"line": 19, "column": 57}}, "10": {"start": {"line": 20, "column": 21}, "end": {"line": 20, "column": 56}}, "11": {"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, "12": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 50}}, "13": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 23}}, "14": {"start": {"line": 31, "column": 25}, "end": {"line": 31, "column": 75}}, "15": {"start": {"line": 32, "column": 16}, "end": {"line": 32, "column": 47}}, "16": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 33}}, "17": {"start": {"line": 38, "column": 19}, "end": {"line": 59, "column": 1}}, "18": {"start": {"line": 40, "column": 23}, "end": {"line": 52, "column": 3}}, "19": {"start": {"line": 54, "column": 2}, "end": {"line": 58, "column": 3}}, "20": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 19}}, "21": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 37}}, "22": {"start": {"line": 62, "column": 15}, "end": {"line": 68, "column": 2}}, "23": {"start": {"line": 71, "column": 0}, "end": {"line": 75, "column": 2}}, "24": {"start": {"line": 78, "column": 0}, "end": {"line": 81, "column": 2}}, "25": {"start": {"line": 84, "column": 0}, "end": {"line": 87, "column": 2}}, "26": {"start": {"line": 90, "column": 0}, "end": {"line": 93, "column": 2}}, "27": {"start": {"line": 96, "column": 0}, "end": {"line": 99, "column": 2}}, "28": {"start": {"line": 102, "column": 0}, "end": {"line": 106, "column": 2}}, "29": {"start": {"line": 109, "column": 0}, "end": {"line": 112, "column": 2}}, "30": {"start": {"line": 115, "column": 0}, "end": {"line": 118, "column": 2}}, "31": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 15}, "end": {"line": 18, "column": 16}}, "loc": {"start": {"line": 18, "column": 34}, "end": {"line": 28, "column": 3}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 29, "column": 12}, "end": {"line": 29, "column": 13}}, "loc": {"start": {"line": 29, "column": 31}, "end": {"line": 34, "column": 3}}, "line": 29}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 38, "column": 19}, "end": {"line": 38, "column": 20}}, "loc": {"start": {"line": 38, "column": 38}, "end": {"line": 59, "column": 1}}, "line": 38}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 22}, "end": {"line": 19, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 22}, "end": {"line": 19, "column": 44}}, {"start": {"line": 19, "column": 48}, "end": {"line": 19, "column": 57}}], "line": 19}, "1": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, {"start": {}, "end": {}}], "line": 23}, "2": {"loc": {"start": {"line": 54, "column": 2}, "end": {"line": 58, "column": 3}}, "type": "if", "locations": [{"start": {"line": 54, "column": 2}, "end": {"line": 58, "column": 3}}, {"start": {"line": 56, "column": 9}, "end": {"line": 58, "column": 3}}], "line": 54}, "3": {"loc": {"start": {"line": 66, "column": 14}, "end": {"line": 66, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 66, "column": 14}, "end": {"line": 66, "column": 49}}, {"start": {"line": 66, "column": 53}, "end": {"line": 66, "column": 69}}], "line": 66}}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 13, "18": 0, "19": 0, "20": 0, "21": 0, "22": 13, "23": 13, "24": 13, "25": 13, "26": 13, "27": 13, "28": 13, "29": 13, "30": 13, "31": 13}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [13, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "d570ae1ee6a8d3aae7cb4ff24493c71b3b048b2c"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\knowledge.routes.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\knowledge.routes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 28}, "end": {"line": 9, "column": 74}}, "3": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": 63}}, "4": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 68}}, "5": {"start": {"line": 14, "column": 0}, "end": {"line": 17, "column": 2}}, "6": {"start": {"line": 20, "column": 0}, "end": {"line": 23, "column": 2}}, "7": {"start": {"line": 26, "column": 0}, "end": {"line": 29, "column": 2}}, "8": {"start": {"line": 32, "column": 0}, "end": {"line": 35, "column": 2}}, "9": {"start": {"line": 38, "column": 0}, "end": {"line": 41, "column": 2}}, "10": {"start": {"line": 44, "column": 0}, "end": {"line": 47, "column": 2}}, "11": {"start": {"line": 50, "column": 0}, "end": {"line": 53, "column": 2}}, "12": {"start": {"line": 56, "column": 0}, "end": {"line": 59, "column": 2}}, "13": {"start": {"line": 62, "column": 0}, "end": {"line": 65, "column": 2}}, "14": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13, "9": 13, "10": 13, "11": 13, "12": 13, "13": 13, "14": 13}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "f3119bae51dbef2d8427d03f79daab92460296d0"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\notification.routes.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\notification.routes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 31}, "end": {"line": 9, "column": 80}}, "3": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": 63}}, "4": {"start": {"line": 13, "column": 0}, "end": {"line": 16, "column": 2}}, "5": {"start": {"line": 19, "column": 0}, "end": {"line": 22, "column": 2}}, "6": {"start": {"line": 25, "column": 0}, "end": {"line": 28, "column": 2}}, "7": {"start": {"line": 31, "column": 0}, "end": {"line": 34, "column": 2}}, "8": {"start": {"line": 37, "column": 0}, "end": {"line": 40, "column": 2}}, "9": {"start": {"line": 43, "column": 0}, "end": {"line": 46, "column": 2}}, "10": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13, "9": 13, "10": 13}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "6622fd9211b0d57a119caefe35c7b927950897db"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\permission.routes.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\permission.routes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 76}}, "3": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": 63}}, "4": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 68}}, "5": {"start": {"line": 14, "column": 0}, "end": {"line": 18, "column": 2}}, "6": {"start": {"line": 21, "column": 0}, "end": {"line": 25, "column": 2}}, "7": {"start": {"line": 28, "column": 0}, "end": {"line": 32, "column": 2}}, "8": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "f288a6151100a65a0e6986c07c899418ca506cd0"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\role.routes.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\role.routes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 64}}, "3": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": 63}}, "4": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 68}}, "5": {"start": {"line": 14, "column": 0}, "end": {"line": 18, "column": 2}}, "6": {"start": {"line": 21, "column": 0}, "end": {"line": 25, "column": 2}}, "7": {"start": {"line": 28, "column": 0}, "end": {"line": 32, "column": 2}}, "8": {"start": {"line": 35, "column": 0}, "end": {"line": 39, "column": 2}}, "9": {"start": {"line": 42, "column": 0}, "end": {"line": 46, "column": 2}}, "10": {"start": {"line": 49, "column": 0}, "end": {"line": 53, "column": 2}}, "11": {"start": {"line": 56, "column": 0}, "end": {"line": 60, "column": 2}}, "12": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13, "9": 13, "10": 13, "11": 13, "12": 13}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e76c1a281e367c5c4dee1a94dae2523eff60149e"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\system.routes.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\system.routes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 68}}, "3": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": 63}}, "4": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 68}}, "5": {"start": {"line": 14, "column": 0}, "end": {"line": 18, "column": 2}}, "6": {"start": {"line": 21, "column": 0}, "end": {"line": 25, "column": 2}}, "7": {"start": {"line": 28, "column": 0}, "end": {"line": 32, "column": 2}}, "8": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "bb7501ef6d21bd10a14af693fe983e42fd08a4e5"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\userRoutes.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\routes\\userRoutes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 63}}, "3": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": 63}}, "4": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 50}}, "5": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 44}}, "6": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 65}}, "7": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 68}}, "8": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 78}}, "9": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 60}}, "10": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 63}}, "11": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 62}}, "12": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 65}}, "13": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13, "9": 13, "10": 13, "11": 13, "12": 13, "13": 13}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "0d9fddb65659d47871ff2a0bd8fed4afa908a5c5"}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\services\\api\\notification.service.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\services\\api\\notification.service.js", "statementMap": {"0": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 51}}, "1": {"start": {"line": 9, "column": 35}, "end": {"line": 48, "column": 1}}, "2": {"start": {"line": 16, "column": 21}, "end": {"line": 16, "column": 57}}, "3": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 25}}, "4": {"start": {"line": 26, "column": 21}, "end": {"line": 26, "column": 63}}, "5": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 25}}, "6": {"start": {"line": 35, "column": 21}, "end": {"line": 35, "column": 61}}, "7": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 25}}, "8": {"start": {"line": 45, "column": 21}, "end": {"line": 45, "column": 59}}, "9": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 25}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 20}, "end": {"line": 15, "column": 21}}, "loc": {"start": {"line": 15, "column": 43}, "end": {"line": 18, "column": 3}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 25, "column": 14}, "end": {"line": 25, "column": 15}}, "loc": {"start": {"line": 25, "column": 28}, "end": {"line": 28, "column": 3}}, "line": 25}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 34, "column": 17}, "end": {"line": 34, "column": 18}}, "loc": {"start": {"line": 34, "column": 29}, "end": {"line": 37, "column": 3}}, "line": 34}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 44, "column": 22}, "end": {"line": 44, "column": 23}}, "loc": {"start": {"line": 44, "column": 36}, "end": {"line": 47, "column": 3}}, "line": 44}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 27}, "end": {"line": 15, "column": 38}}, "type": "default-arg", "locations": [{"start": {"line": 15, "column": 36}, "end": {"line": 15, "column": 38}}], "line": 15}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0]}}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\utils\\file-access-utils.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\utils\\file-access-utils.js", "statementMap": {"0": {"start": {"line": 1, "column": 38}, "end": {"line": 1, "column": 58}}, "1": {"start": {"line": 11, "column": 19}, "end": {"line": 11, "column": 52}}, "2": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, "3": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 17}}, "4": {"start": {"line": 19, "column": 35}, "end": {"line": 22, "column": 4}}, "5": {"start": {"line": 24, "column": 37}, "end": {"line": 24, "column": 101}}, "6": {"start": {"line": 24, "column": 76}, "end": {"line": 24, "column": 100}}, "7": {"start": {"line": 27, "column": 2}, "end": {"line": 47, "column": 5}}, "8": {"start": {"line": 29, "column": 4}, "end": {"line": 31, "column": 5}}, "9": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 18}}, "10": {"start": {"line": 34, "column": 4}, "end": {"line": 44, "column": 5}}, "11": {"start": {"line": 36, "column": 6}, "end": {"line": 38, "column": 7}}, "12": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": 20}}, "13": {"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 7}}, "14": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 20}}, "15": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 17}}, "16": {"start": {"line": 50, "column": 0}, "end": {"line": 52, "column": 2}}}, "fnMap": {"0": {"name": "filterAccessibleFiles", "decl": {"start": {"line": 9, "column": 15}, "end": {"line": 9, "column": 36}}, "loc": {"start": {"line": 9, "column": 50}, "end": {"line": 48, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 24, "column": 66}, "end": {"line": 24, "column": 67}}, "loc": {"start": {"line": 24, "column": 76}, "end": {"line": 24, "column": 100}}, "line": 24}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 27, "column": 22}, "end": {"line": 27, "column": 23}}, "loc": {"start": {"line": 27, "column": 30}, "end": {"line": 47, "column": 3}}, "line": 27}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, "type": "if", "locations": [{"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, {"start": {}, "end": {}}], "line": 14}, "1": {"loc": {"start": {"line": 29, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 29, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {}, "end": {}}], "line": 29}, "2": {"loc": {"start": {"line": 34, "column": 4}, "end": {"line": 44, "column": 5}}, "type": "if", "locations": [{"start": {"line": 34, "column": 4}, "end": {"line": 44, "column": 5}}, {"start": {}, "end": {}}], "line": 34}, "3": {"loc": {"start": {"line": 36, "column": 6}, "end": {"line": 38, "column": 7}}, "type": "if", "locations": [{"start": {"line": 36, "column": 6}, "end": {"line": 38, "column": 7}}, {"start": {}, "end": {}}], "line": 36}, "4": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 7}}, "type": "if", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 7}}, {"start": {}, "end": {}}], "line": 41}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\utils\\helpers.js": {"path": "C:\\Users\\<USER>\\Desktop\\P\\backend\\src\\utils\\helpers.js", "statementMap": {"0": {"start": {"line": 12, "column": 19}, "end": {"line": 25, "column": 1}}, "1": {"start": {"line": 13, "column": 2}, "end": {"line": 15, "column": 3}}, "2": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 14}}, "3": {"start": {"line": 17, "column": 15}, "end": {"line": 17, "column": 33}}, "4": {"start": {"line": 18, "column": 16}, "end": {"line": 18, "column": 60}}, "5": {"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 53}}, "6": {"start": {"line": 20, "column": 16}, "end": {"line": 20, "column": 56}}, "7": {"start": {"line": 21, "column": 18}, "end": {"line": 21, "column": 60}}, "8": {"start": {"line": 22, "column": 18}, "end": {"line": 22, "column": 60}}, "9": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 66}}, "10": {"start": {"line": 32, "column": 29}, "end": {"line": 46, "column": 1}}, "11": {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, "12": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 14}}, "13": {"start": {"line": 37, "column": 21}, "end": {"line": 37, "column": 85}}, "14": {"start": {"line": 38, "column": 15}, "end": {"line": 38, "column": 17}}, "15": {"start": {"line": 40, "column": 2}, "end": {"line": 43, "column": 3}}, "16": {"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": 16}}, "17": {"start": {"line": 41, "column": 24}, "end": {"line": 41, "column": 69}}, "18": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 45}}, "19": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 16}}, "20": {"start": {"line": 53, "column": 22}, "end": {"line": 60, "column": 1}}, "21": {"start": {"line": 54, "column": 2}, "end": {"line": 56, "column": 3}}, "22": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 17}}, "23": {"start": {"line": 58, "column": 21}, "end": {"line": 58, "column": 49}}, "24": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 32}}, "25": {"start": {"line": 67, "column": 22}, "end": {"line": 74, "column": 1}}, "26": {"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}, "27": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 17}}, "28": {"start": {"line": 72, "column": 21}, "end": {"line": 72, "column": 36}}, "29": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 32}}, "30": {"start": {"line": 81, "column": 22}, "end": {"line": 98, "column": 1}}, "31": {"start": {"line": 82, "column": 2}, "end": {"line": 84, "column": 3}}, "32": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 14}}, "33": {"start": {"line": 87, "column": 15}, "end": {"line": 95, "column": 11}}, "34": {"start": {"line": 97, "column": 2}, "end": {"line": 97, "column": 16}}, "35": {"start": {"line": 107, "column": 24}, "end": {"line": 131, "column": 1}}, "36": {"start": {"line": 109, "column": 21}, "end": {"line": 109, "column": 54}}, "37": {"start": {"line": 110, "column": 20}, "end": {"line": 110, "column": 39}}, "38": {"start": {"line": 111, "column": 24}, "end": {"line": 111, "column": 52}}, "39": {"start": {"line": 114, "column": 16}, "end": {"line": 114, "column": 33}}, "40": {"start": {"line": 115, "column": 21}, "end": {"line": 115, "column": 53}}, "41": {"start": {"line": 118, "column": 21}, "end": {"line": 118, "column": 52}}, "42": {"start": {"line": 119, "column": 19}, "end": {"line": 119, "column": 45}}, "43": {"start": {"line": 122, "column": 25}, "end": {"line": 122, "column": 63}}, "44": {"start": {"line": 124, "column": 2}, "end": {"line": 130, "column": 4}}, "45": {"start": {"line": 133, "column": 0}, "end": {"line": 140, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 19}, "end": {"line": 12, "column": 20}}, "loc": {"start": {"line": 12, "column": 29}, "end": {"line": 25, "column": 1}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 32, "column": 29}, "end": {"line": 32, "column": 30}}, "loc": {"start": {"line": 32, "column": 41}, "end": {"line": 46, "column": 1}}, "line": 32}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 53, "column": 22}, "end": {"line": 53, "column": 23}}, "loc": {"start": {"line": 53, "column": 33}, "end": {"line": 60, "column": 1}}, "line": 53}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 67, "column": 22}, "end": {"line": 67, "column": 23}}, "loc": {"start": {"line": 67, "column": 33}, "end": {"line": 74, "column": 1}}, "line": 67}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 81, "column": 22}, "end": {"line": 81, "column": 23}}, "loc": {"start": {"line": 81, "column": 33}, "end": {"line": 98, "column": 1}}, "line": 81}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 107, "column": 24}, "end": {"line": 107, "column": 25}}, "loc": {"start": {"line": 107, "column": 51}, "end": {"line": 131, "column": 1}}, "line": 107}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 2}, "end": {"line": 15, "column": 3}}, "type": "if", "locations": [{"start": {"line": 13, "column": 2}, "end": {"line": 15, "column": 3}}, {"start": {}, "end": {}}], "line": 13}, "1": {"loc": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 11}}, {"start": {"line": 13, "column": 15}, "end": {"line": 13, "column": 38}}, {"start": {"line": 13, "column": 42}, "end": {"line": 13, "column": 63}}], "line": 13}, "2": {"loc": {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, "type": "if", "locations": [{"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, {"start": {}, "end": {}}], "line": 33}, "3": {"loc": {"start": {"line": 54, "column": 2}, "end": {"line": 56, "column": 3}}, "type": "if", "locations": [{"start": {"line": 54, "column": 2}, "end": {"line": 56, "column": 3}}, {"start": {}, "end": {}}], "line": 54}, "4": {"loc": {"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}, "type": "if", "locations": [{"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}, {"start": {}, "end": {}}], "line": 68}, "5": {"loc": {"start": {"line": 82, "column": 2}, "end": {"line": 84, "column": 3}}, "type": "if", "locations": [{"start": {"line": 82, "column": 2}, "end": {"line": 84, "column": 3}}, {"start": {}, "end": {}}], "line": 82}, "6": {"loc": {"start": {"line": 109, "column": 21}, "end": {"line": 109, "column": 54}}, "type": "cond-expr", "locations": [{"start": {"line": 109, "column": 44}, "end": {"line": 109, "column": 49}}, {"start": {"line": 109, "column": 52}, "end": {"line": 109, "column": 54}}], "line": 109}, "7": {"loc": {"start": {"line": 110, "column": 20}, "end": {"line": 110, "column": 39}}, "type": "cond-expr", "locations": [{"start": {"line": 110, "column": 31}, "end": {"line": 110, "column": 35}}, {"start": {"line": 110, "column": 38}, "end": {"line": 110, "column": 39}}], "line": 110}, "8": {"loc": {"start": {"line": 111, "column": 24}, "end": {"line": 111, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 111, "column": 39}, "end": {"line": 111, "column": 47}}, {"start": {"line": 111, "column": 50}, "end": {"line": 111, "column": 52}}], "line": 111}}, "s": {"0": 1, "1": 3, "2": 2, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 4, "12": 1, "13": 3, "14": 3, "15": 3, "16": 3, "17": 30, "18": 30, "19": 3, "20": 1, "21": 11, "22": 3, "23": 8, "24": 8, "25": 1, "26": 11, "27": 3, "28": 8, "29": 8, "30": 1, "31": 5, "32": 3, "33": 2, "34": 2, "35": 1, "36": 5, "37": 5, "38": 5, "39": 5, "40": 5, "41": 5, "42": 5, "43": 5, "44": 5, "45": 1}, "f": {"0": 3, "1": 4, "2": 11, "3": 11, "4": 5, "5": 5}, "b": {"0": [2, 1], "1": [3, 2, 1], "2": [1, 3], "3": [3, 8], "4": [3, 8], "5": [3, 2], "6": [5, 0], "7": [4, 1], "8": [4, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "02bfedd5ab9537a72d0d20af2e54a229e8fdf51e"}}