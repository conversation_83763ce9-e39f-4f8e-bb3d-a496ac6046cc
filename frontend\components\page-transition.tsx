"use client"

import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'

/**
 * 页面过渡组件
 *
 * 为页面切换提供平滑的过渡动画
 *
 * @param children 页面内容
 */
export function PageTransition({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [isVisible, setIsVisible] = useState(true)

  // 监听路由变化
  useEffect(() => {
    const handleRouteChangeStart = () => {
      setIsLoading(true)
      setIsVisible(false)

      // 短暂延迟后显示内容，模拟淡入效果
      setTimeout(() => {
        setIsVisible(true)
      }, 100)
    }

    const handleRouteChangeComplete = () => {
      setIsLoading(false)
    }

    // 添加自定义事件监听器
    window.addEventListener('route-change-start', handleRouteChangeStart)
    window.addEventListener('route-change-complete', handleRouteChangeComplete)

    return () => {
      window.removeEventListener('route-change-start', handleRouteChangeStart)
      window.removeEventListener('route-change-complete', handleRouteChangeComplete)
    }
  }, [])

  return (
    <div
      className={`transition-opacity duration-200 ${isVisible ? 'opacity-100' : 'opacity-0'}`}
    >
      {isLoading ? (
        <div className="fixed inset-0 bg-white bg-opacity-50 z-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1e7a43]"></div>
        </div>
      ) : null}
      {children}
    </div>
  )
}
