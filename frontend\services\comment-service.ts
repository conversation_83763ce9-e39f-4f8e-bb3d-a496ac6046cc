import apiService from './api-service';
import { logger, sanitizeData } from '@/utils/logger';

/**
 * 评论接口定义
 */
export interface Comment {
  id: number;
  content: string;
  topic_type: string;
  topic_id: number;
  creator: {
    id: number;
    username: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  review_comment?: string;
  reviewer_id?: number;
}

/**
 * 获取评论列表
 * @param params 查询参数
 * @returns 评论列表和分页信息
 */
export async function getCommentList(params: {
  page?: number;
  limit?: number;
  status?: 'pending' | 'approved' | 'rejected' | 'all';
}) {
  const response = await apiService.get('/comments', { params });
  if (!response.success) {
    throw new Error(response.message || '获取评论列表失败');
  }
  return response.data;
}

/**
 * 获取主题评论
 * @param topicType 主题类型
 * @param topicId 主题ID
 * @param params 查询参数
 * @returns 评论列表和分页信息
 */
export async function getTopicComments(
  topicType: string,
  topicId: number | string,
  params: {
    page?: number;
    limit?: number;
  } = {}
) {
  try {
    logger.debug("顶层函数获取主题评论:", { topicType, topicId, params });

    const response = await apiService.get(`/comments/topic/${topicType}/${topicId}`, { params });

    // 检查response是否有success属性，如果有且为false则抛出错误
    if (response && typeof response.success === 'boolean' && !response.success) {
      throw new Error(response.message || '获取主题评论失败');
    }
    // 如果response是直接的数据对象或者有data属性，则返回适当的数据
    return response.data || response;
  } catch (error) {
    logger.error('获取主题评论列表错误:', sanitizeData(error));
    throw error;
  }
}

/**
 * 创建评论
 * @param data 评论数据
 * @returns 创建的评论
 */
export async function createComment(data: {
  content: string;
  topic_type: string;
  topic_id: number;
  topic_title?: string;
}) {
  try {
    logger.debug("发送评论创建请求:", sanitizeData(data));

    // 确保所有必要字段都存在
    if (!data.content || !data.topic_type || data.topic_id === undefined || data.topic_id === null) {
      throw new Error("评论缺少必要字段");
    }

    // 确保topic_id是数字类型
    if (typeof data.topic_id !== 'number') {
      throw new Error("主题ID必须是数字类型");
    }

    // 确保已登录并有有效的token
    const token = localStorage.getItem('hefamily_token');
    if (!token) {
      throw new Error("用户未登录，无法创建评论");
    }

    // 检查登录状态标志
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    if (!isLoggedIn) {
      throw new Error("登录状态异常，请重新登录");
    }

    // 准备请求数据
    const requestData = {
      content: data.content,
      topic_type: data.topic_type,
      topic_id: data.topic_id,
      topic_title: data.topic_title || "未知主题"
    };

    logger.debug("最终请求数据:", sanitizeData(requestData));
    logger.debug("认证Token存在");

    // 发送请求
    const response = await apiService.post('/comments', requestData);

    logger.debug("评论创建响应:", sanitizeData(response));

    // 检查响应格式
    if (response && typeof response.message === 'string') {
      logger.debug("评论创建成功:", response.message);
    }

    return response;
  } catch (error) {
    logger.error("评论创建失败:", sanitizeData(error));

    // 如果是401错误，清除登录状态
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('hefamily_token');
      localStorage.removeItem('isLoggedIn');
      throw new Error("登录已过期，请重新登录");
    }

    throw error;
  }
}

/**
 * 审核评论
 * @param id 评论ID
 * @param status 审核状态
 * @param reviewComment 审核意见
 * @returns 审核结果
 */
export async function reviewComment(
  id: number,
  status: 'approved' | 'rejected',
  reviewComment?: string
) {
  const response = await apiService.put(`/comments/${id}/review`, {
    status,
    review_comment: reviewComment
  });
  if (!response.success) {
    throw new Error(response.message || '审核评论失败');
  }
  return response.data;
}

/**
 * 删除评论
 * @param id 评论ID
 * @returns 删除结果
 */
export async function deleteComment(id: number) {
  const response = await apiService.del(`/comments/${id}`);
  if (!response.success) {
    throw new Error(response.message || '删除评论失败');
  }
  return response;
}

/**
 * 评论服务
 *
 * 处理评论的创建、查询、审核等操作
 */
class CommentService {
  /**
   * 获取评论列表
   * @param params 查询参数
   * @returns 评论列表和分页信息
   */
  async getComments(params: {
    page?: number;
    limit?: number;
    topic_id?: string;
    topic_type?: string;
    status?: string;
  }) {
    try {
      // 检查是否有认证token
      const token = localStorage.getItem('hefamily_token');
      if (!token) {
        logger.warn('未找到认证token，无法获取评论列表');
        throw new Error('未找到认证token');
      }

      // 确保请求头中包含认证token
      const config = {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };

      const response = await apiService.get('/comments', { params }, config);

      logger.debug('评论列表响应:', sanitizeData(response));

      // 直接返回响应，因为后端返回的格式是 { message, comments, pagination }
      if (response) {
        return response;
      } else {
        return { comments: [], pagination: { total: 0, page: 1, pageSize: 10, totalPages: 0 } };
      }
    } catch (error) {
      logger.error('获取评论列表失败:', sanitizeData(error));
      throw error;
    }
  }

  /**
   * 获取主题评论列表
   * @param topicType 主题类型
   * @param topicId 主题ID
   * @param params 查询参数
   * @returns 评论列表和分页信息
   */
  async getTopicComments(
    topicType: string,
    topicId: string,
    params: {
      page?: number;
      limit?: number;
    } = {}
  ) {
    try {
      logger.debug("获取主题评论:", { topicType, topicId, params });

      // 直接使用原始topicId，不再尝试转换为整数
      // 这样可以支持字符串ID（如"cai-hesen"）和数字ID
      const response = await apiService.get(`/comments/topic/${topicType}/${topicId}`, { params });

      logger.debug("获取主题评论响应:", sanitizeData(response));

      // 检查response是否有success属性，如果有且为false则抛出错误
      if (response && typeof response.success === 'boolean' && !response.success) {
        throw new Error(response.message || '获取主题评论失败');
      }

      // 如果response是直接的数据对象或者有data属性，则返回适当的数据
      return response.data || response;
    } catch (error) {
      logger.error('获取主题评论列表错误:', sanitizeData(error));
      throw error;
    }
  }

  /**
   * 创建评论
   * @param data 评论数据
   * @returns 创建结果
   */
  async createComment(data: {
    topic_id: string | number;
    topic_type: string;
    topic_title: string;
    content: string;
  }) {
    try {
      logger.debug("准备创建评论:", sanitizeData(data));

      // 检查登录状态
      const token = localStorage.getItem('hefamily_token');
      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';

      logger.debug("检查登录状态:", {
        hasToken: !!token,
        isLoggedInFlag: isLoggedIn
      });

      if (!token || !isLoggedIn) {
        throw new Error("用户未登录或登录状态异常，请重新登录");
      }

      // 检查topic_id是否存在
      if (data.topic_id === undefined || data.topic_id === null) {
        throw new Error("主题ID不能为空");
      }

      // 确保topic_id是数字类型
      let topicId: number;
      if (typeof data.topic_id === 'string') {
        // 尝试将字符串转换为数字
        if (data.topic_id.match(/^\d+$/)) {
          topicId = parseInt(data.topic_id);
        } else {
          // 如果不是纯数字字符串，使用默认ID
          logger.warn(`无法将topic_id "${data.topic_id}" 转换为数字，使用默认ID 1`);
          topicId = 1;
        }
      } else {
        topicId = data.topic_id;
      }

      logger.debug(`转换后的主题ID: ${topicId} (${typeof topicId})`);

      // 准备请求数据
      const requestData = {
        content: data.content,
        topic_type: data.topic_type,
        topic_id: topicId, // 使用数字ID
        topic_title: data.topic_title || "未知主题"
      };

      logger.debug("最终请求数据:", sanitizeData(requestData));

      // 直接使用apiService发送请求，避免多层嵌套调用
      const response = await apiService.post('/comments', requestData);

      logger.debug("评论创建响应:", sanitizeData(response));

      if (response && typeof response.message === 'string') {
        logger.debug("评论创建成功:", response.message);
      }

      return response;
    } catch (error) {
      logger.error("创建评论失败:", sanitizeData(error));

      // 如果是401错误，清除登录状态
      if (error.response && error.response.status === 401) {
        localStorage.removeItem('hefamily_token');
        localStorage.removeItem('isLoggedIn');
        throw new Error("登录已过期，请重新登录");
      }

      throw error;
    }
  }

  /**
   * 审核评论
   * @param id 评论ID
   * @param data 审核数据
   * @returns 审核结果
   */
  async reviewComment(id: string, data: {
    status: 'approved' | 'rejected';
    reason?: string;
  }) {
    try {
      // 检查是否有认证token
      const token = localStorage.getItem('hefamily_token');
      if (!token) {
        logger.warn('未找到认证token，无法审核评论');
        throw new Error('未找到认证token');
      }

      // 确保请求头中包含认证token
      const config = {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };

      const response = await apiService.put(`/comments/${id}/review`, {
        status: data.status,
        review_comment: data.reason
      }, config);

      return response;
    } catch (error) {
      logger.error('审核评论失败:', sanitizeData(error));
      throw error;
    }
  }

  /**
   * 删除评论
   * @param id 评论ID
   * @returns 删除结果
   */
  async deleteComment(id: string) {
    try {
      // 检查是否有认证token
      const token = localStorage.getItem('hefamily_token');
      if (!token) {
        logger.warn('未找到认证token，无法删除评论');
        throw new Error('未找到认证token');
      }

      // 确保请求头中包含认证token
      const config = {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };

      const response = await apiService.del(`/comments/${id}`, config);
      return response;
    } catch (error) {
      logger.error('删除评论失败:', sanitizeData(error));
      throw error;
    }
  }
}

const commentService = new CommentService();
export default commentService;
