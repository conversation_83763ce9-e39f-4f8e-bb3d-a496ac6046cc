"use client"

import { But<PERSON> } from "@/components/ui/button"

interface ActivityManagementButtonProps {
  isManagementMode: boolean
  toggleManagementMode: () => void
}

export function ActivityManagementButton({ isManagementMode, toggleManagementMode }: ActivityManagementButtonProps) {
  return (
    <Button
      onClick={toggleManagementMode}
      variant="outline"
      className="border-[#1e7a43] text-[#1e7a43] hover:bg-[#1e7a43]/10"
    >
      {isManagementMode ? "退出管理" : "管理活动"}
    </Button>
  )
}
