"use client"

import React, { useState, useEffect, useRef } from 'react'

/**
 * 简单文本打字机效果组件
 *
 * 用于实现纯文本的打字机效果，不处理HTML标签
 * @param text 要显示的文本内容
 * @param className 自定义CSS类
 * @param speed 打字速度（毫秒/字符）
 * @param onComplete 打字完成时的回调函数
 */
export function HtmlTypewriter({
  html,
  className = "",
  speed = 30,
  onComplete
}: {
  html: string,
  className?: string,
  speed?: number,
  onComplete?: () => void
}) {
  // 显示的文本
  const [displayText, setDisplayText] = useState("")
  // 文本的字符数组
  const [textChars, setTextChars] = useState<string[]>([])
  // 当前显示的字符索引
  const [currentIndex, setCurrentIndex] = useState(0)
  // 是否完成打字
  const [isComplete, setIsComplete] = useState(false)

  // 初始化文本字符数组
  useEffect(() => {
    if (html) {
      console.log('初始化文本字符数组，文本长度:', html.length)
      // 将文本拆分为字符数组
      const chars = html.split('')
      setTextChars(chars)
      setDisplayText("")
      setCurrentIndex(0)
      setIsComplete(false)
    }
  }, [html])

  // 打字效果
  useEffect(() => {
    if (textChars.length > 0 && currentIndex < textChars.length) {
      // 每100个字符输出一次日志，避免日志过多
      if (currentIndex % 100 === 0 || currentIndex === 1) {
        console.log(`打字效果进行中: ${currentIndex}/${textChars.length}`)
      }

      const timer = setTimeout(() => {
        setDisplayText(prev => prev + textChars[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }, speed)

      return () => clearTimeout(timer)
    } else if (textChars.length > 0 && currentIndex >= textChars.length && !isComplete) {
      console.log('打字效果完成')
      setIsComplete(true)
      if (onComplete) {
        console.log('调用onComplete回调')
        onComplete()
      }
    }
  }, [textChars, currentIndex, speed, onComplete, isComplete])

  // 将文本中的换行符转换为<br>标签
  const formattedText = displayText.split('\n').map((line, i, arr) => (
    <React.Fragment key={i}>
      {line}
      {i < arr.length - 1 && <br />}
    </React.Fragment>
  ))

  return (
    <div className={className}>
      {formattedText}
      {!isComplete && (
        <span className="inline-block w-2 h-4 bg-gray-500 ml-1 animate-pulse"></span>
      )}
    </div>
  )
}
