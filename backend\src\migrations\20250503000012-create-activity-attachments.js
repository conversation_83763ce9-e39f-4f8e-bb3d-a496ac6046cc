/**
 * 创建活动附件表迁移文件
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('activity_attachments', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      activity_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'activities',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      original_name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: '原始文件名'
      },
      path: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: '文件存储路径'
      },
      type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '文件类型，如pdf, doc, jpg等'
      },
      mime_type: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '文件MIME类型'
      },
      size: {
        type: Sequelize.BIGINT,
        allowNull: false,
        comment: '文件大小（字节）'
      },
      uploader_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('activity_attachments');
  }
};
