/**
 * 文件上传中间件
 *
 * 处理文件上传相关的中间件函数
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { SystemConfig } = require('../models');

/**
 * 获取上传配置
 * @returns {Promise<Object>} 上传配置对象
 */
const getUploadConfig = async () => {
  try {
    const config = await SystemConfig.findOne({
      where: { id: 1 }
    });

    if (config) {
      return {
        maxFileSize: config.file_upload_max_size * 1024 * 1024, // 转换为字节
        allowedTypes: config.allowed_file_types ? config.allowed_file_types.split(',').map(type => `.${type.trim()}`) : ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'],
        uploadPath: './uploads'
      };
    }

    // 默认配置
    return {
      maxFileSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'],
      uploadPath: './uploads'
    };
  } catch (error) {
    console.error('获取上传配置失败:', error);
    // 默认配置
    return {
      maxFileSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'],
      uploadPath: './uploads'
    };
  }
};

/**
 * 创建存储配置
 * @param {string} destination 目标目录
 * @returns {Object} multer存储配置
 */
const createStorage = (destination) => {
  return multer.diskStorage({
    destination: (req, file, cb) => {
      // 确保目录存在
      if (!fs.existsSync(destination)) {
        fs.mkdirSync(destination, { recursive: true });
      }
      cb(null, destination);
    },
    filename: (req, file, cb) => {
      // 生成唯一文件名
      // 解码原始文件名，确保中文字符正确处理
      const decodedOriginalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
      const uniqueFilename = `${uuidv4()}${path.extname(decodedOriginalName)}`;

      // 保存原始文件名到请求对象，以便后续使用
      if (!req.decodedFileNames) {
        req.decodedFileNames = {};
      }
      req.decodedFileNames[file.originalname] = decodedOriginalName;

      cb(null, uniqueFilename);
    }
  });
};

/**
 * 文件过滤器
 * @param {Array<string>} allowedTypes 允许的文件类型
 * @returns {Function} 文件过滤函数
 */
const fileFilter = (allowedTypes) => {
  return (req, file, cb) => {
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error(`不支持的文件类型。允许的类型: ${allowedTypes.join(', ')}`), false);
    }
  };
};

/**
 * 创建上传中间件
 * @param {string} fieldName 字段名称
 * @param {Object} options 上传选项
 * @returns {Function} multer中间件
 */
const createUploadMiddleware = async (fieldName = 'file', options = {}) => {
  const config = await getUploadConfig();
  const uploadPath = options.uploadPath || config.uploadPath;
  const maxFileSize = options.maxFileSize || config.maxFileSize;
  const allowedTypes = options.allowedTypes || config.allowedTypes;

  const storage = createStorage(uploadPath);

  const upload = multer({
    storage,
    limits: {
      fileSize: maxFileSize
    },
    fileFilter: fileFilter(allowedTypes)
  });

  return upload.single(fieldName);
};

/**
 * 创建多文件上传中间件
 * @param {string} fieldName 字段名称
 * @param {number} maxCount 最大文件数
 * @param {Object} options 上传选项
 * @returns {Function} multer中间件
 */
const createMultiUploadMiddleware = async (fieldName = 'files', maxCount = 5, options = {}) => {
  const config = await getUploadConfig();
  const uploadPath = options.uploadPath || config.uploadPath;
  const maxFileSize = options.maxFileSize || config.maxFileSize;
  const allowedTypes = options.allowedTypes || config.allowedTypes;

  const storage = createStorage(uploadPath);

  const upload = multer({
    storage,
    limits: {
      fileSize: maxFileSize
    },
    fileFilter: fileFilter(allowedTypes)
  });

  return upload.array(fieldName, maxCount);
};

/**
 * 错误处理中间件
 * @param {Error} err 错误对象
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 * @param {Function} next 下一个中间件
 */
const handleUploadError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超出限制'
      });
    }
    return res.status(400).json({
      success: false,
      message: `上传错误: ${err.message}`
    });
  }

  if (err) {
    return res.status(400).json({
      success: false,
      message: err.message
    });
  }

  next();
};

/**
 * 文件上传后处理中间件
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 * @param {Function} next 下一个中间件
 */
const uploadMiddleware = (req, res, next) => {
  // 检查是否有文件
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: '没有文件上传'
    });
  }

  // 获取文件信息
  const { originalname, filename, path: filePath, mimetype, size } = req.file;

  // 获取文件扩展名
  const fileExtension = path.extname(originalname).toLowerCase();
  const fileType = fileExtension.substring(1);

  // 检查文件类型是否允许
  const allowedTypes = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'];

  if (!allowedTypes.includes(fileExtension)) {
    return res.status(400).json({
      success: false,
      message: `文件类型不允许。允许的类型: ${allowedTypes.join(', ')}`
    });
  }

  // 检查文件大小是否超过限制
  const maxFileSize = 10 * 1024 * 1024; // 10MB
  if (size > maxFileSize) {
    return res.status(400).json({
      success: false,
      message: `文件大小超过限制。最大允许大小: ${maxFileSize / (1024 * 1024)}MB`
    });
  }

  // 添加文件信息到请求对象
  req.fileInfo = {
    originalName: originalname,
    fileName: filename,
    fileType: fileType,
    mimeType: mimetype,
    filePath,
    fileSize: size
  };

  // 继续下一个中间件
  next();
};

module.exports = uploadMiddleware;

// 为了向后兼容，保留原有导出
module.exports.createUploadMiddleware = createUploadMiddleware;
module.exports.createMultiUploadMiddleware = createMultiUploadMiddleware;
module.exports.handleUploadError = handleUploadError;
module.exports.getUploadConfig = getUploadConfig;
