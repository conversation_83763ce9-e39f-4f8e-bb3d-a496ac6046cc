"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { Bell } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { NotificationDetailModal } from "@/components/notification-detail-modal"
import notificationService, { Notification } from "@/services/notification-service"
import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"

/**
 * 通知下拉菜单组件
 *
 * 显示用户的通知列表，支持查看通知详情和标记已读
 */
export function NotificationDropdown() {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(false)

  const dropdownRef = useRef<HTMLDivElement>(null)

  // 获取通知列表和未读数量
  useEffect(() => {
    if (isOpen) {
      fetchNotifications()
    }
    fetchUnreadCount()
  }, [isOpen])

  // 获取通知列表
  const fetchNotifications = async () => {
    try {
      setLoading(true)
      const response = await notificationService.getNotifications({
        page: 1,
        limit: 4,
      })
      setNotifications(response.notifications)
    } catch (error) {
      console.error("获取通知列表失败:", error)
    } finally {
      setLoading(false)
    }
  }

  // 获取未读通知数量
  const fetchUnreadCount = async () => {
    try {
      const response = await notificationService.getUnreadNotificationCount()
      setUnreadCount(response.unread_count)
      console.log("更新未读通知数量:", response.unread_count)
    } catch (error) {
      console.error("获取未读通知数量失败:", error)
    }
  }

  // 标记通知为已读
  const markAsRead = async (id: string) => {
    try {
      await notificationService.markNotificationAsRead(id)

      // 更新本地通知状态
      setNotifications(notifications.map((n) => (n.id === id ? { ...n, is_read: true } : n)))

      // 直接减少未读计数
      setUnreadCount(prev => Math.max(0, prev - 1))

      // 再次获取最新的未读计数
      setTimeout(() => {
        fetchUnreadCount()
      }, 500)
    } catch (error) {
      console.error("标记通知为已读失败:", error)
    }
  }

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    try {
      await notificationService.markAllNotificationsAsRead()

      // 更新本地通知状态
      setNotifications(notifications.map((n) => ({ ...n, is_read: true })))

      // 立即将未读计数设为0
      setUnreadCount(0)

      // 再次获取最新的未读计数
      setTimeout(() => {
        fetchUnreadCount()
      }, 500)
    } catch (error) {
      console.error("标记所有通知为已读失败:", error)
    }
  }

  // 处理通知点击
  const handleNotificationClick = (notification: Notification) => {
    setSelectedNotification(notification)
    setIsDetailModalOpen(true)
    if (!notification.is_read) {
      markAsRead(notification.id)
    }
  }

  // 关闭通知详情模态框
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false)
    setSelectedNotification(null)
  }

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: zhCN })
    } catch (error) {
      return dateString
    }
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  return (
    <div className="relative" ref={dropdownRef}>
      <Button variant="ghost" size="icon" className="relative" onClick={() => setIsOpen(!isOpen)} aria-label="通知">
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge className="absolute -top-1 -right-1 px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center bg-red-500 text-white text-xs">
            {unreadCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
          <div className="p-3 border-b flex justify-between items-center">
            <h3 className="text-sm font-medium">通知</h3>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-[#1e7a43] hover:text-[#1e7a43]/80"
                onClick={markAllAsRead}
              >
                全部标为已读
              </Button>
            )}
          </div>
          <div className="max-h-[400px] overflow-y-auto">
            {loading ? (
              <div className="py-8 text-center">
                <p className="text-sm text-gray-500">加载中...</p>
              </div>
            ) : notifications.length > 0 ? (
              <div className="py-1">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`px-4 py-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0 ${notification.is_read ? "" : "bg-blue-50"}`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex justify-between items-start">
                      <p className="text-sm font-medium">{notification.title}</p>
                      <span className="text-xs text-gray-500">{formatTime(notification.created_at)}</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{notification.content}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-8 text-center">
                <p className="text-sm text-gray-500">暂无通知</p>
              </div>
            )}
          </div>
          <div className="p-2 border-t text-center">
            <Link href="/notifications" className="text-xs text-[#1e7a43] hover:text-[#1e7a43]/80">
              查看全部通知
            </Link>
          </div>
        </div>
      )}

      <NotificationDetailModal
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
        notification={selectedNotification}
      />
    </div>
  )
}
