/**
 * 直接测试上传文件到Dify
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');

// 测试文件路径
const testFilePath = path.join(__dirname, 'test-file-for-dify.txt');

// 检查文件是否存在
if (!fs.existsSync(testFilePath)) {
  console.error(`测试文件不存在: ${testFilePath}`);
  process.exit(1);
}

console.log(`测试文件存在: ${testFilePath}`);

// 上传文件到Dify
async function uploadFileToDify() {
  try {
    console.log(`开始上传文件到Dify知识库: ${testFilePath}`);

    // 准备FormData
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath), 'test-file-for-dify.txt');

    // 添加处理规则
    const processRule = {
      indexing_technique: "high_quality",
      process_rule: {
        rules: {
          pre_processing_rules: [
            { id: "remove_extra_spaces", enabled: true },
            { id: "remove_urls_emails", enabled: true }
          ],
          segmentation: {
            separator: "###",
            max_tokens: 500
          }
        },
        mode: "custom"
      }
    };

    formData.append('data', JSON.stringify(processRule), { type: 'text/plain' });

    // 设置API端点
    const datasetId = '77199451-730a-4d79-a1c9-9b9e6bfcd747';
    const apiEndpoint = `https://ai.glab.vip/v1/datasets/${datasetId}/document/create-by-file`;
    const apiKey = "dataset-DLFJlUe25VUHOwMO4HbO4hQk";

    console.log(`API端点: ${apiEndpoint}`);
    console.log('准备发送POST请求到Dify API');

    // 发送请求
    const response = await axios.post(
      apiEndpoint,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          ...formData.getHeaders()
        },
        timeout: 120000 // 120秒超时
      }
    );

    console.log('Dify API响应:');
    console.log('状态码:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));

    if (response.data && response.data.document) {
      console.log(`文件上传成功，文档ID: ${response.data.document.id}`);
      return true;
    } else {
      console.error('上传失败，响应数据不包含document字段');
      return false;
    }
  } catch (error) {
    console.error('上传文件到Dify知识库失败:');

    if (error.response) {
      // 服务器响应了错误状态码
      console.error('响应状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('未收到响应:', error.request);
    } else {
      // 设置请求时发生错误
      console.error('错误消息:', error.message);
    }

    return false;
  }
}

// 执行上传
uploadFileToDify()
  .then(result => {
    console.log('测试完成，结果:', result ? '成功' : '失败');
    process.exit(result ? 0 : 1);
  })
  .catch(error => {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
  });
