/**
 * 系统控制器
 * 
 * 处理系统配置相关的业务逻辑，如查询、更新系统配置等
 */

const { SystemConfig, User, KnowledgeBase, File, AIAssistant } = require('../models');
const { sequelize } = require('../models');

/**
 * 获取系统配置
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getSystemConfig = async (req, res) => {
  try {
    // 查询系统配置
    let config = await SystemConfig.findOne({
      where: { id: 1 }
    });
    
    // 如果不存在，创建默认配置
    if (!config) {
      config = await SystemConfig.create({
        id: 1,
        password_min_length: 8,
        password_require_uppercase: true,
        password_require_lowercase: true,
        password_require_number: true,
        password_require_special: true,
        max_login_attempts: 5,
        lockout_duration: 30, // 分钟
        session_timeout: 60, // 分钟
        file_upload_max_size: 10, // MB
        allowed_file_types: 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif',
        system_name: '和富家族研究平台',
        system_logo: '/logo.png',
        system_description: '和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。'
      });
    }

    res.status(200).json({
      success: true,
      data: config
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取系统配置失败',
      error: error.message
    });
  }
};

/**
 * 更新系统配置
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateSystemConfig = async (req, res) => {
  try {
    const {
      password_min_length,
      password_require_uppercase,
      password_require_lowercase,
      password_require_number,
      password_require_special,
      max_login_attempts,
      lockout_duration,
      session_timeout,
      file_upload_max_size,
      allowed_file_types,
      system_name,
      system_logo,
      system_description
    } = req.body;

    // 查询系统配置
    let config = await SystemConfig.findOne({
      where: { id: 1 }
    });
    
    // 如果不存在，创建默认配置
    if (!config) {
      config = await SystemConfig.create({
        id: 1,
        password_min_length: 8,
        password_require_uppercase: true,
        password_require_lowercase: true,
        password_require_number: true,
        password_require_special: true,
        max_login_attempts: 5,
        lockout_duration: 30,
        session_timeout: 60,
        file_upload_max_size: 10,
        allowed_file_types: 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif',
        system_name: '和富家族研究平台',
        system_logo: '/logo.png',
        system_description: '和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。'
      });
    }

    // 更新系统配置
    await config.update({
      password_min_length: password_min_length !== undefined ? password_min_length : config.password_min_length,
      password_require_uppercase: password_require_uppercase !== undefined ? password_require_uppercase : config.password_require_uppercase,
      password_require_lowercase: password_require_lowercase !== undefined ? password_require_lowercase : config.password_require_lowercase,
      password_require_number: password_require_number !== undefined ? password_require_number : config.password_require_number,
      password_require_special: password_require_special !== undefined ? password_require_special : config.password_require_special,
      max_login_attempts: max_login_attempts !== undefined ? max_login_attempts : config.max_login_attempts,
      lockout_duration: lockout_duration !== undefined ? lockout_duration : config.lockout_duration,
      session_timeout: session_timeout !== undefined ? session_timeout : config.session_timeout,
      file_upload_max_size: file_upload_max_size !== undefined ? file_upload_max_size : config.file_upload_max_size,
      allowed_file_types: allowed_file_types !== undefined ? allowed_file_types : config.allowed_file_types,
      system_name: system_name !== undefined ? system_name : config.system_name,
      system_logo: system_logo !== undefined ? system_logo : config.system_logo,
      system_description: system_description !== undefined ? system_description : config.system_description
    });

    res.status(200).json({
      success: true,
      message: '系统配置更新成功',
      data: config
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新系统配置失败',
      error: error.message
    });
  }
};

/**
 * 获取系统统计信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getSystemStats = async (req, res) => {
  try {
    // 获取用户数量
    const userCount = await User.count();
    
    // 获取知识库数量
    const knowledgeBaseCount = await KnowledgeBase.count();
    
    // 获取文件数量
    const fileCount = await File.count();
    
    // 获取AI助手数量
    const aiAssistantCount = await AIAssistant.count();
    
    // 获取系统知识库数量
    const systemKnowledgeBaseCount = await KnowledgeBase.count({
      where: { type: 'system' }
    });
    
    // 获取用户知识库数量
    const userKnowledgeBaseCount = await KnowledgeBase.count({
      where: { type: 'user' }
    });
    
    // 获取待审核文件数量
    const pendingFileCount = await File.count({
      where: { status: 'pending' }
    });
    
    // 获取已批准文件数量
    const approvedFileCount = await File.count({
      where: { status: 'approved' }
    });
    
    // 获取已拒绝文件数量
    const rejectedFileCount = await File.count({
      where: { status: 'rejected' }
    });
    
    // 获取总存储大小
    const totalStorageSize = await File.sum('size');
    
    // 获取最近注册的用户
    const recentUsers = await User.findAll({
      attributes: ['id', 'username', 'email', 'created_at'],
      order: [['created_at', 'DESC']],
      limit: 5
    });
    
    // 获取最近上传的文件
    const recentFiles = await File.findAll({
      attributes: ['id', 'original_name', 'type', 'size', 'status', 'created_at'],
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username']
        },
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: 5
    });

    res.status(200).json({
      success: true,
      data: {
        user_count: userCount,
        knowledge_base_count: knowledgeBaseCount,
        file_count: fileCount,
        ai_assistant_count: aiAssistantCount,
        system_knowledge_base_count: systemKnowledgeBaseCount,
        user_knowledge_base_count: userKnowledgeBaseCount,
        pending_file_count: pendingFileCount,
        approved_file_count: approvedFileCount,
        rejected_file_count: rejectedFileCount,
        total_storage_size: totalStorageSize || 0,
        recent_users: recentUsers,
        recent_files: recentFiles
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取系统统计信息失败',
      error: error.message
    });
  }
};
