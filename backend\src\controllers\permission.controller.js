/**
 * 权限控制器
 *
 * 处理权限相关的业务逻辑，如查询权限等
 */

const { Permission } = require('../models');
const { Op } = require('sequelize');

/**
 * 获取权限列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPermissions = async (req, res) => {
  try {
    const { module, search } = req.query;

    // 构建查询条件
    const whereConditions = {};

    // 根据模块筛选
    if (module) {
      whereConditions.module = module;
    }

    // 根据名称或代码搜索
    if (search) {
      whereConditions[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { code: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    // 查询权限
    const permissions = await Permission.findAll({
      where: whereConditions,
      order: [['module', 'ASC'], ['name', 'ASC']]
    });

    res.status(200).json({
      success: true,
      data: permissions
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取权限列表失败',
      error: error.message
    });
  }
};

/**
 * 获取权限详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPermissionById = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询权限
    const permission = await Permission.findByPk(id);

    if (!permission) {
      return res.status(404).json({
        success: false,
        message: '权限不存在'
      });
    }

    res.status(200).json({
      success: true,
      data: permission
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取权限详情失败',
      error: error.message
    });
  }
};

/**
 * 获取权限模块列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPermissionModules = async (req, res) => {
  try {
    // 查询所有权限模块
    const modules = await Permission.findAll({
      attributes: ['module'],
      group: ['module'],
      order: [['module', 'ASC']]
    });

    // 提取模块名称
    const moduleNames = modules.map(item => item.module);

    res.status(200).json({
      success: true,
      data: moduleNames
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取权限模块列表失败',
      error: error.message
    });
  }
};

/**
 * 创建权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createPermission = async (req, res) => {
  try {
    const { name, code, description, module } = req.body;

    // 验证必填字段
    if (!name || !code || !module) {
      return res.status(400).json({
        success: false,
        message: '名称、代码和模块是必填字段'
      });
    }

    // 检查权限名是否已存在
    const existingPermission = await Permission.findOne({
      where: {
        [Op.or]: [
          { name },
          { code }
        ]
      }
    });

    if (existingPermission) {
      return res.status(400).json({
        success: false,
        message: '权限名或代码已存在'
      });
    }

    // 创建权限
    const permission = await Permission.create({
      name,
      code,
      description,
      module
    });

    res.status(201).json({
      success: true,
      message: '权限创建成功',
      data: permission
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建权限失败',
      error: error.message
    });
  }
};

/**
 * 更新权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updatePermission = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, description, module } = req.body;

    // 查询权限
    const permission = await Permission.findByPk(id);

    if (!permission) {
      return res.status(404).json({
        success: false,
        message: '权限不存在'
      });
    }

    // 如果更新名称或代码，检查是否与其他权限冲突
    if ((name && name !== permission.name) || (code && code !== permission.code)) {
      const existingPermission = await Permission.findOne({
        where: {
          id: { [Op.ne]: id },
          [Op.or]: [
            { name: name || permission.name },
            { code: code || permission.code }
          ]
        }
      });

      if (existingPermission) {
        return res.status(400).json({
          success: false,
          message: '权限名或代码已存在'
        });
      }
    }

    // 更新权限
    await permission.update({
      name: name || permission.name,
      code: code || permission.code,
      description: description !== undefined ? description : permission.description,
      module: module || permission.module
    });

    res.status(200).json({
      success: true,
      message: '权限更新成功',
      data: permission
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新权限失败',
      error: error.message
    });
  }
};

/**
 * 删除权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deletePermission = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询权限
    const permission = await Permission.findByPk(id);

    if (!permission) {
      return res.status(404).json({
        success: false,
        message: '权限不存在'
      });
    }

    // 删除权限
    await permission.destroy();

    res.status(200).json({
      success: true,
      message: '权限删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除权限失败',
      error: error.message
    });
  }
};
