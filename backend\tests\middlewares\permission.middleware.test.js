/**
 * 权限检查中间件测试
 *
 * 测试权限检查中间件的功能
 */

const { User, Role, Permission } = require('../../src/models');
const permissionMiddleware = require('../../src/middlewares/permissionMiddleware');

// 设置较长的超时时间
jest.setTimeout(30000);

describe('权限检查中间件', () => {
  let testUser;
  let adminUser;
  let testRole;
  let adminRole;
  let testPermission;
  let mockNext;

  // 模拟请求、响应和下一个中间件
  const mockRequest = (user) => ({
    user
  });

  const mockResponse = () => {
    const res = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    return res;
  };

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试角色
    testRole = await Role.create({
      name: 'test_permission_role_' + Date.now(),
      description: '测试权限角色',
      is_system: false
    });

    // 创建管理员角色
    adminRole = await Role.create({
      name: 'admin_test_' + Date.now(),
      description: '管理员权限角色',
      is_system: true
    });

    // 创建测试权限
    testPermission = await Permission.create({
      name: 'test_permission_' + Date.now(),
      description: '测试权限',
      code: 'test:read',
      module: 'test'
    });

    // 关联角色和权限
    await testRole.addPermission(testPermission);
    await adminRole.addPermission(testPermission);

    // 创建测试用户
    testUser = await User.create({
      username: 'permissionuser',
      password: 'Password123!',
      email: '<EMAIL>',
      phone: '13800000015',
      role: 'basic_user',
      role_id: testRole.id,
      is_active: true
    });

    // 创建管理员用户
    adminUser = await User.create({
      username: 'permissionadmin',
      password: 'Password123!',
      email: '<EMAIL>',
      phone: '13900000015',
      role: 'admin',
      role_id: adminRole.id,
      is_active: true
    });
  });

  // 在每个测试前初始化模拟函数
  beforeEach(() => {
    mockNext = jest.fn();
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    try {
      // 删除测试数据
      if (testUser && testUser.id) {
        await User.destroy({ where: { id: testUser.id } });
      }
      if (adminUser && adminUser.id) {
        await User.destroy({ where: { id: adminUser.id } });
      }
      if (testPermission && testPermission.id) {
        await Permission.destroy({ where: { id: testPermission.id } });
      }
      if (testRole && testRole.id) {
        await Role.destroy({ where: { id: testRole.id } });
      }
      if (adminRole && adminRole.id) {
        await Role.destroy({ where: { id: adminRole.id } });
      }
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  });

  // 测试权限检查中间件
  describe('权限检查中间件', () => {
    test('应该在用户有权限时通过检查', async () => {
      const checkPermission = permissionMiddleware('test', 'read');
      const req = mockRequest({
        id: testUser.id,
        role: testUser.role,
        role_id: testUser.role_id
      });
      const res = mockResponse();

      await checkPermission(req, res, mockNext);

      // 验证next被调用
      expect(mockNext).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('应该在用户没有权限时拒绝访问', async () => {
      const checkPermission = permissionMiddleware('test', 'write'); // 用户没有写权限
      const req = mockRequest({
        id: testUser.id,
        role: testUser.role,
        role_id: testUser.role_id
      });
      const res = mockResponse();

      await checkPermission(req, res, mockNext);

      // 验证响应状态和消息
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('权限不足')
        })
      );

      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('管理员应该有所有权限', async () => {
      const checkPermission = permissionMiddleware('any', 'any'); // 任意权限
      const req = mockRequest({
        id: adminUser.id,
        role: adminUser.role,
        role_id: adminUser.role_id
      });
      const res = mockResponse();

      await checkPermission(req, res, mockNext);

      // 验证next被调用
      expect(mockNext).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('应该在请求中没有用户信息时拒绝访问', async () => {
      const checkPermission = permissionMiddleware('test', 'read');
      const req = mockRequest(undefined);
      const res = mockResponse();

      await checkPermission(req, res, mockNext);

      // 验证响应状态和消息
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('未授权')
        })
      );

      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该在用户角色不存在时拒绝访问', async () => {
      const checkPermission = permissionMiddleware('test', 'read');
      const req = mockRequest({
        id: testUser.id,
        role: 'nonexistent_role'
      });
      const res = mockResponse();

      await checkPermission(req, res, mockNext);

      // 验证响应状态和消息
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('角色不存在')
        })
      );

      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该支持检查多个权限', async () => {
      // 创建另一个测试权限
      const anotherPermission = await Permission.create({
        name: 'another_permission_' + Date.now(),
        description: '另一个测试权限',
        code: 'test:write',
        module: 'test'
      });

      // 将权限添加到测试角色
      await testRole.addPermission(anotherPermission);

      // 检查多个权限
      const checkMultiplePermissions = permissionMiddleware(['test:read', 'test:write']);
      const req = mockRequest({
        id: testUser.id,
        role: testUser.role,
        role_id: testUser.role_id
      });
      const res = mockResponse();

      await checkMultiplePermissions(req, res, mockNext);

      // 验证next被调用
      expect(mockNext).toHaveBeenCalled();

      // 清理测试数据
      await testRole.removePermission(anotherPermission);
      await Permission.destroy({ where: { id: anotherPermission.id } });
    });

    test('应该在用户只有部分权限时拒绝访问', async () => {
      // 检查多个权限，其中一个用户没有
      const checkMultiplePermissions = permissionMiddleware(['test:read', 'test:delete']);
      const req = mockRequest({
        id: testUser.id,
        role: testUser.role,
        role_id: testUser.role_id
      });
      const res = mockResponse();

      await checkMultiplePermissions(req, res, mockNext);

      // 验证响应状态和消息
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('权限不足')
        })
      );

      // 验证next没有被调用
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
