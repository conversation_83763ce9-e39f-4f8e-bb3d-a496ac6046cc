'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'

/**
 * 全局错误边界组件
 * 当根布局或模板中发生错误时显示
 */
export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // 在开发环境中记录错误到控制台
    console.error('根布局发生错误:', error)
  }, [error])

  return (
    <html lang="zh-CN">
      <body>
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
          <div className="max-w-md text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">严重错误</h2>
            <p className="text-gray-600 mb-6">
              抱歉，应用程序遇到了一个严重错误。我们的技术团队已经收到通知。
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button
                onClick={() => reset()}
                className="bg-primary hover:bg-primary/90"
              >
                重试
              </Button>
              <Button
                onClick={() => window.location.href = '/'}
                variant="outline"
              >
                返回首页
              </Button>
            </div>
          </div>
        </div>
      </body>
    </html>
  )
}
