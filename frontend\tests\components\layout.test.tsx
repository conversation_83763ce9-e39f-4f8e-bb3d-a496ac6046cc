/**
 * 导航栏和页脚组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { Navbar } from '@/components/layout/Navbar'
import { Footer } from '@/components/layout/Footer'
import { mockFetch, resetMockFetch } from '../test-utils'

// 模拟用户数据
const mockUser = {
  id: 1,
  username: 'testuser',
  role: 'basic_user'
}

// 模拟管理员用户
const mockAdminUser = {
  id: 2,
  username: 'admin',
  role: 'admin'
}

// 模拟通知数据
const mockNotifications = [
  {
    id: 1,
    title: '系统通知',
    content: '欢迎使用和富家族研究平台',
    type: 'system',
    status: 'unread',
    created_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 2,
    title: '评论通知',
    content: '您的评论已被审核通过',
    type: 'comment',
    status: 'read',
    created_at: '2023-01-02T00:00:00Z'
  }
]

describe('布局组件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch()
    jest.clearAllMocks()
  })

  // 测试导航栏组件
  describe('Navbar组件', () => {
    test('应该正确渲染导航栏', () => {
      render(
        <Navbar
          user={null}
          notifications={[]}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onRegister={jest.fn()}
          onNotificationRead={jest.fn()}
        />
      )
      
      // 验证导航链接
      expect(screen.getByText('首页')).toBeInTheDocument()
      expect(screen.getByText('个人专题')).toBeInTheDocument()
      expect(screen.getByText('知识库')).toBeInTheDocument()
      expect(screen.getByText('数据查询')).toBeInTheDocument()
      
      // 验证登录/注册按钮
      expect(screen.getByText('登录')).toBeInTheDocument()
      expect(screen.getByText('注册')).toBeInTheDocument()
    })

    test('已登录用户应该看到用户菜单', () => {
      render(
        <Navbar
          user={mockUser}
          notifications={mockNotifications}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onRegister={jest.fn()}
          onNotificationRead={jest.fn()}
        />
      )
      
      // 验证用户名显示
      expect(screen.getByText('testuser')).toBeInTheDocument()
      
      // 验证通知图标
      expect(screen.getByTestId('notification-icon')).toBeInTheDocument()
      
      // 验证未读通知数量
      expect(screen.getByText('1')).toBeInTheDocument() // 1个未读通知
    })

    test('管理员应该看到系统管理链接', () => {
      render(
        <Navbar
          user={mockAdminUser}
          notifications={[]}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onRegister={jest.fn()}
          onNotificationRead={jest.fn()}
        />
      )
      
      // 验证系统管理链接
      expect(screen.getByText('系统管理')).toBeInTheDocument()
    })

    test('应该调用登录函数', () => {
      const handleLogin = jest.fn()
      
      render(
        <Navbar
          user={null}
          notifications={[]}
          onLogin={handleLogin}
          onLogout={jest.fn()}
          onRegister={jest.fn()}
          onNotificationRead={jest.fn()}
        />
      )
      
      // 点击登录按钮
      const loginButton = screen.getByText('登录')
      fireEvent.click(loginButton)
      
      // 验证登录函数被调用
      expect(handleLogin).toHaveBeenCalled()
    })

    test('应该调用注册函数', () => {
      const handleRegister = jest.fn()
      
      render(
        <Navbar
          user={null}
          notifications={[]}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onRegister={handleRegister}
          onNotificationRead={jest.fn()}
        />
      )
      
      // 点击注册按钮
      const registerButton = screen.getByText('注册')
      fireEvent.click(registerButton)
      
      // 验证注册函数被调用
      expect(handleRegister).toHaveBeenCalled()
    })

    test('应该调用登出函数', () => {
      const handleLogout = jest.fn()
      
      render(
        <Navbar
          user={mockUser}
          notifications={[]}
          onLogin={jest.fn()}
          onLogout={handleLogout}
          onRegister={jest.fn()}
          onNotificationRead={jest.fn()}
        />
      )
      
      // 点击用户名打开下拉菜单
      const userMenu = screen.getByText('testuser')
      fireEvent.click(userMenu)
      
      // 点击登出按钮
      const logoutButton = screen.getByText('退出登录')
      fireEvent.click(logoutButton)
      
      // 验证登出函数被调用
      expect(handleLogout).toHaveBeenCalled()
    })

    test('应该显示通知下拉菜单', () => {
      render(
        <Navbar
          user={mockUser}
          notifications={mockNotifications}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onRegister={jest.fn()}
          onNotificationRead={jest.fn()}
        />
      )
      
      // 点击通知图标
      const notificationIcon = screen.getByTestId('notification-icon')
      fireEvent.click(notificationIcon)
      
      // 验证通知内容显示
      expect(screen.getByText('系统通知')).toBeInTheDocument()
      expect(screen.getByText('评论通知')).toBeInTheDocument()
    })

    test('应该调用通知已读函数', () => {
      const handleNotificationRead = jest.fn()
      
      render(
        <Navbar
          user={mockUser}
          notifications={mockNotifications}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onRegister={jest.fn()}
          onNotificationRead={handleNotificationRead}
        />
      )
      
      // 点击通知图标
      const notificationIcon = screen.getByTestId('notification-icon')
      fireEvent.click(notificationIcon)
      
      // 点击未读通知
      const unreadNotification = screen.getByText('系统通知')
      fireEvent.click(unreadNotification)
      
      // 验证通知已读函数被调用
      expect(handleNotificationRead).toHaveBeenCalledWith(mockNotifications[0].id)
    })

    test('应该正确处理响应式导航', () => {
      render(
        <Navbar
          user={null}
          notifications={[]}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onRegister={jest.fn()}
          onNotificationRead={jest.fn()}
        />
      )
      
      // 验证汉堡菜单按钮
      const hamburgerButton = screen.getByTestId('hamburger-button')
      expect(hamburgerButton).toBeInTheDocument()
      
      // 点击汉堡菜单按钮
      fireEvent.click(hamburgerButton)
      
      // 验证移动菜单显示
      expect(screen.getByTestId('mobile-menu')).toHaveClass('block')
    })
  })

  // 测试页脚组件
  describe('Footer组件', () => {
    test('应该正确渲染页脚', () => {
      render(<Footer />)
      
      // 验证版权信息
      expect(screen.getByText(/版权所有/)).toBeInTheDocument()
      expect(screen.getByText(/和富家族研究平台/)).toBeInTheDocument()
      
      // 验证链接
      expect(screen.getByText('关于我们')).toBeInTheDocument()
      expect(screen.getByText('联系方式')).toBeInTheDocument()
      expect(screen.getByText('使用条款')).toBeInTheDocument()
      expect(screen.getByText('隐私政策')).toBeInTheDocument()
    })

    test('应该包含当前年份', () => {
      render(<Footer />)
      
      // 获取当前年份
      const currentYear = new Date().getFullYear().toString()
      
      // 验证页脚包含当前年份
      const footerText = screen.getByText(/版权所有/)
      expect(footerText.textContent).toContain(currentYear)
    })

    test('应该包含社交媒体链接', () => {
      render(<Footer />)
      
      // 验证社交媒体图标
      expect(screen.getByTestId('weixin-icon')).toBeInTheDocument()
      expect(screen.getByTestId('weibo-icon')).toBeInTheDocument()
      expect(screen.getByTestId('email-icon')).toBeInTheDocument()
    })

    test('应该显示微信二维码', () => {
      render(<Footer />)
      
      // 点击微信图标
      const weixinIcon = screen.getByTestId('weixin-icon')
      fireEvent.mouseEnter(weixinIcon)
      
      // 验证二维码显示
      expect(screen.getByAltText('微信二维码')).toBeInTheDocument()
      
      // 移开鼠标
      fireEvent.mouseLeave(weixinIcon)
      
      // 验证二维码隐藏
      expect(screen.queryByAltText('微信二维码')).not.toBeInTheDocument()
    })
  })
});
