  // 渲染用户管理界面
  return (
    <div className="space-y-6">
      {/* 搜索和筛选 */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative md:w-1/3">
          <input
            type="text"
            placeholder="搜索用户..."
            value={userSearchQuery}
            onChange={(e) => setUserSearchQuery(e.target.value)}
            className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        </div>

        <select
          value={userStatusFilter}
          onChange={(e) => setUserStatusFilter(e.target.value)}
          className="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
        >
          <option value="全部状态">全部状态</option>
          <option value="正常">正常</option>
          <option value="待审核">待审核</option>
          <option value="已禁用">已禁用</option>
          <option value="待激活">待激活</option>
        </select>

        <select
          value={userRoleFilter}
          onChange={(e) => setUserRoleFilter(e.target.value)}
          className="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
        >
          <option value="全部角色">全部角色</option>
          {rolesList.map((role) => (
            <option key={role.id} value={role.name}>
              {role.name}
            </option>
          ))}
        </select>

        <div className="flex-grow"></div>

        <Button
          onClick={() => openUserModal()}
          className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
        >
          添加用户
        </Button>
      </div>

      {/* 批量操作 */}
      {selectedUsers.length > 0 && (
        <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-md">
          <span className="text-sm text-gray-500">已选择 {selectedUsers.length} 个用户</span>
          <Button
            variant="outline"
            size="sm"
            onClick={enableSelectedUsers}
            className="text-green-600 border-green-600 hover:bg-green-50"
          >
            <CheckCircle className="h-4 w-4 mr-1" />
            启用
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={disableSelectedUsers}
            className="text-orange-600 border-orange-600 hover:bg-orange-50"
          >
            <AlertCircle className="h-4 w-4 mr-1" />
            禁用
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={deleteSelectedUsers}
            className="text-red-600 border-red-600 hover:bg-red-50"
          >
            <Trash className="h-4 w-4 mr-1" />
            删除
          </Button>
        </div>
      )}

      {/* 用户列表 */}
      <div className="bg-white rounded-md shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <Checkbox
                  checked={selectedUsers.length > 0 && selectedUsers.length === filteredUsers.length}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedUsers(filteredUsers.map((user) => user.id))
                    } else {
                      setSelectedUsers([])
                    }
                  }}
                />
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户名
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                联系方式
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                角色
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                注册时间
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredUsers.length > 0 ? (
              filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={() => handleUserSelect(user.id)}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{user.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{user.email}</div>
                    <div className="text-sm text-gray-500">{user.phone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{user.roleName}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        user.status === "正常"
                          ? "bg-green-100 text-green-800"
                          : user.status === "待审核"
                          ? "bg-yellow-100 text-yellow-800"
                          : user.status === "已禁用"
                          ? "bg-red-100 text-red-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {user.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.createdAt}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => openUserModal(user)}
                      className="text-indigo-600 hover:text-indigo-900 mr-4"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => openDeleteUserModal(user)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                  没有找到匹配的用户
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* 用户编辑模态框 */}
      {showUserModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">
                {currentUser ? "编辑用户" : "添加用户"}
              </h2>
              <button onClick={closeUserModal} className="text-gray-500 hover:text-gray-700">
                <X className="h-6 w-6" />
              </button>
            </div>

            <form
              onSubmit={(e) => {
                e.preventDefault()
                const formData = new FormData(e.currentTarget)
                const userData: UserType = {
                  id: currentUser?.id || "",
                  name: formData.get("name") as string,
                  email: formData.get("email") as string,
                  phone: formData.get("phone") as string,
                  roleId: formData.get("roleId") as string,
                  roleName: rolesList.find((role) => role.id === formData.get("roleId"))?.name || "",
                  status: formData.get("status") as "正常" | "待审核" | "已禁用" | "待激活",
                  createdAt: currentUser?.createdAt || "",
                }
                saveUser(userData)
              }}
              className="space-y-4"
            >
              <div>
                <Label htmlFor="name">用户名</Label>
                <Input
                  id="name"
                  name="name"
                  defaultValue={currentUser?.name || ""}
                  required
                />
              </div>

              <div>
                <Label htmlFor="email">电子邮件</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  defaultValue={currentUser?.email || ""}
                  required
                />
              </div>

              <div>
                <Label htmlFor="phone">手机号码</Label>
                <Input
                  id="phone"
                  name="phone"
                  defaultValue={currentUser?.phone || ""}
                  required
                />
              </div>

              <div>
                <Label htmlFor="roleId">角色</Label>
                <select
                  id="roleId"
                  name="roleId"
                  defaultValue={currentUser?.roleId || ""}
                  className="w-full border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                  required
                >
                  <option value="" disabled>
                    选择角色
                  </option>
                  {rolesList.map((role) => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <Label htmlFor="status">状态</Label>
                <select
                  id="status"
                  name="status"
                  defaultValue={currentUser?.status || "正常"}
                  className="w-full border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                  required
                >
                  <option value="正常">正常</option>
                  <option value="待审核">待审核</option>
                  <option value="已禁用">已禁用</option>
                  <option value="待激活">待激活</option>
                </select>
              </div>

              <div className="flex justify-end gap-4 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={closeUserModal}
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                >
                  保存
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 删除用户确认模态框 */}
      {showDeleteUserModal && userToDelete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">确认删除</h2>
              <button
                onClick={closeDeleteUserModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <p className="mb-6">
              您确定要删除用户 <strong>{userToDelete.name}</strong> 吗？此操作无法撤销。
            </p>

            <div className="flex justify-end gap-4">
              <Button
                variant="outline"
                onClick={closeDeleteUserModal}
              >
                取消
              </Button>
              <Button
                onClick={deleteUser}
                className="bg-red-600 hover:bg-red-700"
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default UserManager
