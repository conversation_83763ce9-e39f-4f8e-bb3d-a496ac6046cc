/**
 * 活动模型
 * 
 * 定义活动数据结构，包括活动标题、日期、描述等信息
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} Activity模型
 */
module.exports = (sequelize, DataTypes) => {
  const Activity = sequelize.define('Activity', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    image: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '活动图片路径'
    },
    status: {
      type: DataTypes.ENUM('published', 'draft', 'archived'),
      allowNull: false,
      defaultValue: 'draft',
      comment: '活动状态：已发布、草稿、已归档'
    },
    creator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    last_updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'activities',
    timestamps: true
  });

  // 关联关系
  Activity.associate = (models) => {
    // 活动与创建者的多对一关系
    Activity.belongsTo(models.User, {
      foreignKey: 'creator_id',
      as: 'creator'
    });

    // 活动与最后更新者的多对一关系
    Activity.belongsTo(models.User, {
      foreignKey: 'last_updated_by',
      as: 'lastUpdatedBy'
    });

    // 活动与附件的一对多关系
    Activity.hasMany(models.ActivityAttachment, {
      foreignKey: 'activity_id',
      as: 'attachments'
    });
  };

  return Activity;
};
