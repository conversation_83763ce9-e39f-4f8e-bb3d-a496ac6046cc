/**
 * AI助手相关路由
 *
 * 处理AI助手的配置、查询等请求
 */

const express = require('express');
const router = express.Router();
const aiAssistantController = require('../controllers/ai-assistant.controller');
const aiAssistantStreamController = require('../controllers/ai-assistant-stream.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');

// 获取AI助手列表 (需要认证)
router.get('/',
  authMiddleware,
  aiAssistantController.getAIAssistants
);

// 创建AI助手 (需要认证和权限)
router.post('/',
  authMiddleware,
  checkPermission('ai:manage'),
  aiAssistantController.createAIAssistant
);

// 根据名称获取AI助手 (需要认证)
router.get('/assistants/by-name',
  authMiddleware,
  aiAssistantController.getAIAssistantByName
);

// 获取AI助手详情 (需要认证)
router.get('/:id',
  authMiddleware,
  aiAssistantController.getAIAssistantById
);

// 更新AI助手 (需要认证和权限)
router.put('/:id',
  authMiddleware,
  checkPermission('ai:manage'),
  aiAssistantController.updateAIAssistant
);

// 删除AI助手 (需要认证和权限)
router.delete('/:id',
  authMiddleware,
  checkPermission('ai:manage'),
  aiAssistantController.deleteAIAssistant
);

// 个人专题助手查询 (需要认证)
router.post('/personal/query',
  authMiddleware,
  checkPermission('personal:ai_use'),
  aiAssistantController.queryPersonalAssistant
);

// 数据查询助手查询 (需要认证)
router.post('/data-query/query',
  authMiddleware,
  checkPermission('data:ai_query'),
  aiAssistantController.queryDataAssistant
);

// AI研究助手查询 (需要认证)
router.post('/assistant/query',
  authMiddleware,
  checkPermission('assistant:use'),
  aiAssistantController.queryResearchAssistant
);

// 特定AI研究助手查询 (需要认证)
router.post('/assistant/:id/query',
  authMiddleware,
  checkPermission('assistant:use'),
  aiAssistantController.queryResearchAssistant
);

// 异步AI研究助手查询 (需要认证)
router.post('/assistant/async-query',
  authMiddleware,
  checkPermission('assistant:use'),
  aiAssistantController.asyncQueryResearchAssistant
);

// 获取异步AI研究助手查询结果 (需要认证)
router.get('/assistant/task/:taskId',
  authMiddleware,
  checkPermission('assistant:use'),
  aiAssistantController.getResearchTaskStatus
);

// 设置数据查询助手知识库 (需要认证)
router.post('/data-query/set-knowledge-base',
  authMiddleware,
  checkPermission('data:ai_query'),
  aiAssistantController.setDataAssistantKnowledgeBase
);

// 获取AI助手对话历史 (需要认证)
router.get('/conversations/:assistantType',
  authMiddleware,
  aiAssistantController.getConversationHistory
);

// 清除AI助手对话历史 (需要认证)
router.delete('/conversations/:assistantType',
  authMiddleware,
  aiAssistantController.clearConversationHistory
);

// 流式API端点 - 特定AI研究助手查询 (需要认证)
router.post('/assistant/:id/stream',
  authMiddleware,
  checkPermission('assistant:use'),
  aiAssistantStreamController.streamQueryAssistant
);

// 流式API端点 - 数据查询助手查询 (需要认证)
router.post('/data-query/stream',
  authMiddleware,
  checkPermission('data:ai_query'),
  aiAssistantStreamController.streamQueryDataAssistant
);

// 流式API端点 - 特定数据查询助手查询 (需要认证)
router.post('/data-query/:id/stream',
  authMiddleware,
  checkPermission('data:ai_query'),
  aiAssistantStreamController.streamQueryDataAssistant
);

module.exports = router;
