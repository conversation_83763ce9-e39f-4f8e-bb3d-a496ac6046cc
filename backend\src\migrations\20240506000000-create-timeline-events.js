/**
 * 创建时间轴事件表的迁移文件
 */

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('timeline_events', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      year: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '事件年份'
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '事件标题'
      },
      description: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '事件简短描述'
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: '事件详细内容'
      },
      level: {
        type: Sequelize.ENUM('national', 'family', 'personal'),
        allowNull: false,
        defaultValue: 'personal',
        comment: '事件级别：国家级、家族级、个人级'
      },
      icon: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '事件图标URL'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('timeline_events');
  }
};
