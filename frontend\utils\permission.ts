/**
 * 权限工具函数
 * 
 * 提供权限检查相关的工具函数
 */

/**
 * 用户类型定义
 */
interface User {
  id: number;
  username: string;
  role: string;
  permissions?: string[];
}

/**
 * 检查用户是否拥有指定权限
 * @param user 用户对象
 * @param permission 权限或权限数组
 * @param requireAll 是否要求拥有所有权限
 * @returns 是否拥有权限
 */
export function hasPermission(
  user: User | null | undefined,
  permission: string | string[],
  requireAll = false
): boolean {
  // 未登录用户没有任何权限
  if (!user) return false;

  // 用户没有权限属性
  if (!user.permissions) return false;

  // 管理员拥有所有权限
  if (user.role === 'admin' || user.permissions.includes('*')) return true;

  // 处理单个权限
  if (typeof permission === 'string') {
    return user.permissions.includes(permission);
  }

  // 处理权限数组
  if (requireAll) {
    // 要求拥有所有权限
    return permission.every(p => user.permissions!.includes(p));
  } else {
    // 只要拥有其中一个权限即可
    return permission.some(p => user.permissions!.includes(p));
  }
}

/**
 * 检查用户是否是管理员
 * @param user 用户对象
 * @returns 是否是管理员
 */
export function isAdmin(user: User | null | undefined): boolean {
  if (!user) return false;
  return user.role === 'admin';
}

/**
 * 检查用户是否可以访问指定页面
 * @param user 用户对象
 * @param page 页面标识
 * @param pagePermissions 页面权限配置
 * @returns 是否可以访问
 */
export function canAccessPage(
  user: User | null | undefined,
  page: string,
  pagePermissions: Record<string, string | null>
): boolean {
  // 管理员可以访问所有页面
  if (isAdmin(user)) return true;

  // 获取页面所需权限
  const requiredPermission = pagePermissions[page];

  // 公开页面，所有人可访问
  if (requiredPermission === null) return true;

  // 页面未配置，默认不可访问
  if (requiredPermission === undefined) return false;

  // 检查用户是否有权限
  return hasPermission(user, requiredPermission);
}

/**
 * 检查用户是否可以执行指定操作
 * @param user 用户对象
 * @param action 操作标识
 * @param actionPermissions 操作权限配置
 * @param resource 资源对象
 * @param customCheck 自定义检查函数
 * @returns 是否可以执行
 */
export function canPerformAction(
  user: User | null | undefined,
  action: string,
  actionPermissions: Record<string, string>,
  resource?: any,
  customCheck?: (user: User | null | undefined, action: string, resource?: any) => boolean
): boolean {
  // 管理员可以执行所有操作
  if (isAdmin(user)) return true;

  // 未登录用户不能执行任何操作
  if (!user) return false;

  // 获取操作所需权限
  const requiredPermission = actionPermissions[action];

  // 操作未配置，默认不可执行
  if (requiredPermission === undefined) return false;

  // 检查用户是否有权限
  const hasRequiredPermission = hasPermission(user, requiredPermission);

  // 如果没有所需权限，直接返回false
  if (!hasRequiredPermission) return false;

  // 如果有自定义检查函数，执行自定义检查
  if (customCheck) {
    return customCheck(user, action, resource);
  }

  return true;
}
