/**
 * 通知模型
 *
 * 定义通知数据结构，包括通知标题、内容、类型等信息
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} Notification模型
 */
module.exports = (sequelize, DataTypes) => {
  const Notification = sequelize.define('Notification', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '通知接收者ID'
    },
    title: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('system', 'activity', 'file', 'comment', 'file_review', 'file_review_result'),
      allowNull: false,
      comment: '通知类型：系统通知、活动通知、文件通知、评论通知、文件审核通知、文件审核结果通知'
    },
    is_read: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否已读'
    },
    read_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '阅读时间'
    },
    related_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '相关对象ID'
    },
    related_type: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '相关对象类型'
    }
  }, {
    tableName: 'notifications',
    timestamps: true
  });

  // 关联关系
  Notification.associate = (models) => {
    // 通知与用户的多对一关系
    Notification.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  };

  return Notification;
};
