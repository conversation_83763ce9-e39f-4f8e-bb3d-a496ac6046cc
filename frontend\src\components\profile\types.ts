/**
 * 个人中心组件类型定义
 */

// 用户信息
export interface UserProfile {
  id: string;
  username: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  role: string;
  last_login?: string;
  created_at: string;
}

// 个人信息表单字段
export interface ProfileFormFields {
  name: string;
  email: string;
  phone: string;
  avatar?: File;
}

// 修改密码表单字段
export interface ChangePasswordFormFields {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 个人信息表单属性
export interface ProfileFormProps {
  user: UserProfile;
  loading: boolean;
  onSubmit: (values: ProfileFormFields) => void;
}

// 修改密码表单属性
export interface ChangePasswordFormProps {
  loading: boolean;
  onSubmit: (values: ChangePasswordFormFields) => void;
}

// 个人中心属性
export interface ProfileCenterProps {
  user: UserProfile;
}

// 个人活动记录
export interface ActivityRecord {
  id: string;
  type: 'login' | 'file_upload' | 'comment' | 'download' | 'view';
  description: string;
  ip_address?: string;
  created_at: string;
}

// 个人活动记录列表属性
export interface ActivityRecordListProps {
  records: ActivityRecord[];
  loading: boolean;
}
