/**
 * 查询知识库数据的脚本
 */

// 加载环境变量
require('dotenv').config();

// 导入模型
const db = require('./src/models');

// 查询知识库数据
async function queryKnowledgeBases() {
  try {
    // 连接数据库
    await db.sequelize.authenticate();
    console.log('数据库连接成功');

    // 查询知识库表
    const knowledgeBases = await db.sequelize.query('SELECT * FROM knowledge_bases', {
      type: db.sequelize.QueryTypes.SELECT
    });

    console.log('知识库数据:');
    console.log(JSON.stringify(knowledgeBases, null, 2));

    // 关闭数据库连接
    await db.sequelize.close();
  } catch (error) {
    console.error('查询失败:', error);
  }
}

// 执行查询
queryKnowledgeBases();
