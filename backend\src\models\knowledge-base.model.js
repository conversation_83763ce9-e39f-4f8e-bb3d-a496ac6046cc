/**
 * 知识库模型
 *
 * 定义知识库数据结构，包括知识库名称、描述、类型等信息
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} KnowledgeBase模型
 */
module.exports = (sequelize, DataTypes) => {
  const KnowledgeBase = sequelize.define('KnowledgeBase', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    type: {
      type: DataTypes.ENUM('system', 'user'),
      allowNull: false,
      defaultValue: 'user',
      comment: '知识库类型：系统知识库或用户知识库'
    },
    creator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    contact_name: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '联系人姓名'
    },
    contact_phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '联系人电话'
    },
    storage_size: {
      type: DataTypes.BIGINT,
      defaultValue: 0,
      comment: '知识库存储大小（字节）'
    },
    file_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '知识库文件数量'
    }
  }, {
    tableName: 'knowledge_bases',
    timestamps: true
  });

  // 关联关系
  KnowledgeBase.associate = (models) => {
    // 知识库与创建者的多对一关系
    KnowledgeBase.belongsTo(models.User, {
      foreignKey: 'creator_id',
      as: 'creator'
    });

    // 知识库与文件的一对多关系
    KnowledgeBase.hasMany(models.File, {
      foreignKey: 'knowledge_base_id',
      as: 'files'
    });

    // 知识库与用户的多对多关系（知识库访问权限）
    KnowledgeBase.belongsToMany(models.User, {
      through: models.KnowledgeBaseAccess,
      foreignKey: 'knowledge_base_id',
      otherKey: 'user_id',
      as: 'authorizedUsers'
    });

    // 知识库与访问申请的一对多关系
    if (models.KnowledgeBaseAccessRequest) {
      KnowledgeBase.hasMany(models.KnowledgeBaseAccessRequest, {
        foreignKey: 'knowledge_base_id',
        as: 'accessRequests'
      });
    }
  };

  return KnowledgeBase;
};
