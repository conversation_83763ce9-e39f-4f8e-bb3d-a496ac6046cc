/**
 * 工具函数测试
 */

import {
  formatDate,
  formatFileSize,
  truncateText,
  generateId,
  validateEmail,
  validatePassword,
  validatePhone
} from '@/lib/utils'

describe('工具函数', () => {
  // 测试日期格式化
  describe('formatDate', () => {
    test('应该正确格式化日期字符串', () => {
      const date = '2023-05-15T10:30:00Z'
      const result = formatDate(date)
      expect(result).toMatch(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}/)
    })

    test('应该正确格式化Date对象', () => {
      const date = new Date('2023-05-15T10:30:00Z')
      const result = formatDate(date)
      expect(result).toMatch(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}/)
    })

    test('应该处理无效日期', () => {
      const result = formatDate('invalid-date')
      expect(result).toBe('无效日期')
    })

    test('应该使用自定义格式', () => {
      const date = '2023-05-15T10:30:00Z'
      const result = formatDate(date, 'yyyy年MM月dd日')
      expect(result).toMatch(/\d{4}年\d{2}月\d{2}日/)
    })
  })

  // 测试文件大小格式化
  describe('formatFileSize', () => {
    test('应该格式化字节', () => {
      expect(formatFileSize(500)).toBe('500 B')
    })

    test('应该格式化KB', () => {
      expect(formatFileSize(1500)).toBe('1.46 KB')
    })

    test('应该格式化MB', () => {
      expect(formatFileSize(1500000)).toBe('1.43 MB')
    })

    test('应该格式化GB', () => {
      expect(formatFileSize(1500000000)).toBe('1.4 GB')
    })

    test('应该处理0', () => {
      expect(formatFileSize(0)).toBe('0 B')
    })

    test('应该处理负数', () => {
      expect(formatFileSize(-1000)).toBe('0 B')
    })
  })

  // 测试文本截断
  describe('truncateText', () => {
    test('应该截断超过最大长度的文本', () => {
      const text = '这是一段很长的文本，需要被截断'
      expect(truncateText(text, 10)).toBe('这是一段很长的文本，...')
    })

    test('不应该截断未超过最大长度的文本', () => {
      const text = '短文本'
      expect(truncateText(text, 10)).toBe('短文本')
    })

    test('应该使用自定义后缀', () => {
      const text = '这是一段很长的文本，需要被截断'
      expect(truncateText(text, 10, '···')).toBe('这是一段很长的文本，···')
    })

    test('应该处理空字符串', () => {
      expect(truncateText('', 10)).toBe('')
    })
  })

  // 测试ID生成
  describe('generateId', () => {
    test('应该生成指定长度的ID', () => {
      expect(generateId(10)).toHaveLength(10)
    })

    test('应该生成不同的ID', () => {
      const id1 = generateId(10)
      const id2 = generateId(10)
      expect(id1).not.toBe(id2)
    })

    test('应该使用默认长度', () => {
      expect(generateId()).toHaveLength(8)
    })
  })

  // 测试邮箱验证
  describe('validateEmail', () => {
    test('应该验证有效的邮箱', () => {
      expect(validateEmail('<EMAIL>')).toBe(true)
      expect(validateEmail('<EMAIL>')).toBe(true)
    })

    test('应该拒绝无效的邮箱', () => {
      expect(validateEmail('invalid')).toBe(false)
      expect(validateEmail('invalid@')).toBe(false)
      expect(validateEmail('@domain.com')).toBe(false)
      expect(validateEmail('user@domain')).toBe(false)
    })

    test('应该处理空字符串', () => {
      expect(validateEmail('')).toBe(false)
    })
  })

  // 测试密码验证
  describe('validatePassword', () => {
    test('应该验证符合要求的密码', () => {
      expect(validatePassword('Password123!')).toBe(true)
    })

    test('应该拒绝过短的密码', () => {
      expect(validatePassword('Pass1!')).toBe(false)
    })

    test('应该拒绝没有大写字母的密码', () => {
      expect(validatePassword('password123!')).toBe(false)
    })

    test('应该拒绝没有小写字母的密码', () => {
      expect(validatePassword('PASSWORD123!')).toBe(false)
    })

    test('应该拒绝没有数字的密码', () => {
      expect(validatePassword('Password!')).toBe(false)
    })

    test('应该拒绝没有特殊字符的密码', () => {
      expect(validatePassword('Password123')).toBe(false)
    })

    test('应该处理空字符串', () => {
      expect(validatePassword('')).toBe(false)
    })
  })

  // 测试手机号验证
  describe('validatePhone', () => {
    test('应该验证有效的中国手机号', () => {
      expect(validatePhone('13800138000')).toBe(true)
      expect(validatePhone('15912345678')).toBe(true)
      expect(validatePhone('17612345678')).toBe(true)
      expect(validatePhone('19912345678')).toBe(true)
    })

    test('应该拒绝无效的手机号', () => {
      expect(validatePhone('1380013800')).toBe(false) // 少一位
      expect(validatePhone('138001380001')).toBe(false) // 多一位
      expect(validatePhone('12345678901')).toBe(false) // 不是1[3-9]开头
      expect(validatePhone('abcdefghijk')).toBe(false) // 非数字
    })

    test('应该处理空字符串', () => {
      expect(validatePhone('')).toBe(false)
    })
  })
});
