name: Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x]

    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
    
    - name: Install dependencies
      working-directory: backend
      run: npm ci
    
    - name: Run tests
      working-directory: backend
      run: npm test
    
    - name: Check test coverage
      working-directory: backend
      run: npm run test:check-coverage
    
    - name: Upload test report
      uses: actions/upload-artifact@v3
      with:
        name: test-report
        path: backend/reports/
    
    - name: Upload coverage report
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: backend/coverage/
