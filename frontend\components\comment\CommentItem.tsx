/**
 * 评论项组件
 * 
 * 用于展示单条评论
 */

import React from 'react'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Comment } from './CommentList'

// 评论项组件属性
interface CommentItemProps {
  comment: Comment
}

// 评论项组件
export const CommentItem: React.FC<CommentItemProps> = ({ comment }) => {
  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">待审核</Badge>
      case 'rejected':
        return <Badge variant="destructive">已拒绝</Badge>
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div className="font-medium">{comment.user_name}</div>
          {getStatusBadge(comment.status)}
        </div>
      </CardHeader>
      <CardContent>
        <p className="whitespace-pre-wrap">{comment.content}</p>
      </CardContent>
      <CardFooter className="pt-0 text-sm text-gray-500">
        {formatDate(comment.created_at)}
      </CardFooter>
      
      {/* 子评论 */}
      {comment.children && comment.children.length > 0 && (
        <div className="ml-8 mb-4 space-y-2">
          {comment.children.map(child => (
            <CommentItem key={child.id} comment={child} />
          ))}
        </div>
      )}
    </Card>
  )
}

export default CommentItem
