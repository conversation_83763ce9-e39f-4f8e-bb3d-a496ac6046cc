/**
 * 通知模型测试
 *
 * 测试通知模型的字段、验证和关联
 */

const { Notification, User } = require('../../src/models');

describe('通知模型', () => {
  let testUser;
  let testNotification;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试用户
    testUser = await User.create({
      username: 'notificationmodeluser',
      password: 'Password123!',
      email: '<EMAIL>',
      phone: '13800000019',
      role: 'basic_user',
      is_active: true
    });

    // 创建测试通知
    testNotification = await Notification.create({
      title: '测试通知',
      content: '这是一条测试通知内容',
      type: 'system',
      is_read: false,
      user_id: testUser.id
    });
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await Notification.destroy({ where: { id: testNotification.id } });
    await User.destroy({ where: { id: testUser.id } });
  });

  // 测试通知创建
  describe('通知创建', () => {
    test('应该成功创建通知', async () => {
      const notification = await Notification.findByPk(testNotification.id);

      expect(notification).toBeDefined();
      expect(notification.title).toBe('测试通知');
      expect(notification.content).toBe('这是一条测试通知内容');
      expect(notification.type).toBe('system');
      expect(notification.is_read).toBe(false);
      expect(notification.user_id).toBe(testUser.id);
    });

    test('不应该创建没有标题的通知', async () => {
      try {
        await Notification.create({
          // 缺少标题
          content: '缺少标题的通知',
          type: 'system',
          status: 'unread',
          user_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建没有内容的通知', async () => {
      try {
        await Notification.create({
          title: '缺少内容的通知',
          // 缺少内容
          type: 'system',
          status: 'unread',
          user_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建无效类型的通知', async () => {
      try {
        await Notification.create({
          title: '无效类型的通知',
          content: '这是一条无效类型的通知',
          type: 'invalid_type', // 无效的类型
          status: 'unread',
          user_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建无效类型的is_read', async () => {
      try {
        await Notification.create({
          title: '无效is_read的通知',
          content: '这是一条无效is_read的通知',
          type: 'system',
          is_read: 'not_a_boolean', // 无效的is_read值
          user_id: testUser.id
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  // 测试通知更新
  describe('通知更新', () => {
    test('应该成功更新通知已读状态', async () => {
      await testNotification.update({
        is_read: true,
        read_time: new Date()
      });

      const updatedNotification = await Notification.findByPk(testNotification.id);
      expect(updatedNotification.is_read).toBe(true);
      expect(updatedNotification.read_time).not.toBeNull();

      // 恢复原始数据
      await testNotification.update({
        is_read: false,
        read_time: null
      });
    });
  });

  // 测试通知关联
  describe('通知关联', () => {
    test('通知应该关联到用户', async () => {
      // 检查通知模型是否有用户关联
      expect(Notification.associations).toHaveProperty('user');

      // 获取通知的用户
      const notification = await Notification.findByPk(testNotification.id, {
        include: ['user']
      });

      expect(notification.user).toBeDefined();
      expect(notification.user.id).toBe(testUser.id);
      expect(notification.user.username).toBe('notificationmodeluser');
    });
  });

  // 测试通知实例方法
  describe('通知实例方法', () => {
    test('isRead属性应该正确反映通知是否已读', async () => {
      // 初始状态为未读
      expect(testNotification.is_read).toBe(false);

      // 更新为已读状态
      await testNotification.update({ is_read: true });

      // 重新获取通知
      const updatedNotification = await Notification.findByPk(testNotification.id);
      expect(updatedNotification.is_read).toBe(true);

      // 恢复原始状态
      await testNotification.update({ is_read: false });
    });

    test('可以手动将通知标记为已读', async () => {
      // 初始状态为未读
      expect(testNotification.is_read).toBe(false);

      // 手动标记为已读
      const now = new Date();
      await testNotification.update({
        is_read: true,
        read_time: now
      });

      // 验证状态已更新
      const updatedNotification = await Notification.findByPk(testNotification.id);
      expect(updatedNotification.is_read).toBe(true);
      expect(updatedNotification.read_time).not.toBeNull();

      // 恢复原始状态
      await testNotification.update({
        is_read: false,
        read_time: null
      });
    });
  });

  // 测试通知类方法
  describe('通知类方法', () => {
    test('findByUser方法应该返回指定用户的通知列表', async () => {
      // 假设通知模型有findByUser静态方法
      if (typeof Notification.findByUser === 'function') {
        const userNotifications = await Notification.findByUser(testUser.id);
        expect(Array.isArray(userNotifications)).toBe(true);

        // 所有返回的通知都应该属于指定用户
        userNotifications.forEach(notification => {
          expect(notification.user_id).toBe(testUser.id);
        });
      } else {
        // 如果没有该方法，跳过测试
        console.log('通知模型没有findByUser方法，跳过测试');
      }
    });

    test('可以查询未读通知', async () => {
      // 查询未读通知
      const unreadNotifications = await Notification.findAll({
        where: { is_read: false }
      });

      expect(Array.isArray(unreadNotifications)).toBe(true);

      // 所有返回的通知都应该是未读状态
      unreadNotifications.forEach(notification => {
        expect(notification.is_read).toBe(false);
      });
    });

    test('可以将用户的所有通知标记为已读', async () => {
      // 创建多个测试通知
      await Notification.bulkCreate([
        {
          title: '测试通知1',
          content: '测试内容1',
          type: 'system',
          is_read: false,
          user_id: testUser.id
        },
        {
          title: '测试通知2',
          content: '测试内容2',
          type: 'system',
          is_read: false,
          user_id: testUser.id
        }
      ]);

      // 将所有通知标记为已读
      const now = new Date();
      const [count] = await Notification.update(
        {
          is_read: true,
          read_time: now
        },
        {
          where: {
            user_id: testUser.id,
            is_read: false
          }
        }
      );

      expect(count).toBeGreaterThan(0);

      // 验证所有通知都被标记为已读
      const unreadNotifications = await Notification.findAll({
        where: {
          user_id: testUser.id,
          is_read: false
        }
      });

      expect(unreadNotifications.length).toBe(0);

      // 清理测试数据
      await Notification.destroy({
        where: {
          title: ['测试通知1', '测试通知2']
        }
      });
    });
  });
});
