/**
 * 知识库服务
 *
 * 提供与知识库和知识库文件相关的API调用
 */

import axios from 'axios';
import {
  KnowledgeBase,
  KnowledgeFile,
  KnowledgeBaseFormFields,
  KnowledgeFileUploadFields,
  KnowledgeFileReviewFields,
  KnowledgeBaseTableParams,
  KnowledgeFileTableParams
} from '@/components/knowledge/types';

// 获取API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';

// 知识库服务
const knowledgeService = {
  /**
   * 获取知识库列表
   * @param params 查询参数
   * @returns 知识库列表
   */
  async getKnowledgeBases(params?: KnowledgeBaseTableParams): Promise<{ list: KnowledgeBase[]; total: number }> {
    try {
      const response = await axios.get(`${API_BASE_URL}/knowledge-bases`, { params });
      return response.data.data;
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取知识库详情
   * @param id 知识库ID
   * @returns 知识库详情
   */
  async getKnowledgeBaseById(id: string): Promise<KnowledgeBase> {
    try {
      const response = await axios.get(`${API_BASE_URL}/knowledge-bases/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('获取知识库详情失败:', error);
      throw error;
    }
  },

  /**
   * 创建知识库
   * @param data 知识库数据
   * @returns 创建的知识库
   */
  async createKnowledgeBase(data: KnowledgeBaseFormFields): Promise<KnowledgeBase> {
    try {
      const response = await axios.post(`${API_BASE_URL}/knowledge-bases`, data);
      return response.data.data;
    } catch (error) {
      console.error('创建知识库失败:', error);
      throw error;
    }
  },

  /**
   * 更新知识库
   * @param id 知识库ID
   * @param data 知识库数据
   * @returns 更新后的知识库
   */
  async updateKnowledgeBase(id: string, data: Partial<KnowledgeBaseFormFields>): Promise<KnowledgeBase> {
    try {
      const response = await axios.put(`${API_BASE_URL}/knowledge-bases/${id}`, data);
      return response.data.data;
    } catch (error) {
      console.error('更新知识库失败:', error);
      throw error;
    }
  },

  /**
   * 删除知识库
   * @param id 知识库ID
   * @returns 操作结果
   */
  async deleteKnowledgeBase(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.delete(`${API_BASE_URL}/knowledge-bases/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除知识库失败:', error);
      throw error;
    }
  },

  /**
   * 获取知识库文件列表
   * @param params 查询参数
   * @returns 知识库文件列表
   */
  async getKnowledgeFiles(params?: KnowledgeFileTableParams): Promise<{ list: KnowledgeFile[]; total: number }> {
    try {
      const response = await axios.get(`${API_BASE_URL}/knowledge-files`, { params });
      return response.data.data;
    } catch (error) {
      console.error('获取知识库文件列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取知识库文件详情
   * @param id 知识库文件ID
   * @returns 知识库文件详情
   */
  async getKnowledgeFileById(id: string): Promise<KnowledgeFile> {
    try {
      const response = await axios.get(`${API_BASE_URL}/knowledge-files/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('获取知识库文件详情失败:', error);
      throw error;
    }
  },

  /**
   * 上传知识库文件
   * @param data 知识库文件数据
   * @returns 上传的知识库文件
   */
  async uploadKnowledgeFile(data: KnowledgeFileUploadFields): Promise<KnowledgeFile> {
    try {
      const formData = new FormData();
      formData.append('knowledge_base_id', data.knowledge_base_id);
      formData.append('file', data.file);

      if (data.summary) {
        formData.append('summary', data.summary);
      }

      if (data.detailed_description) {
        formData.append('detailed_description', data.detailed_description);
      }

      const response = await axios.post(`${API_BASE_URL}/knowledge-files/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data.data;
    } catch (error) {
      console.error('上传知识库文件失败:', error);
      throw error;
    }
  },

  /**
   * 审核知识库文件
   * @param id 知识库文件ID
   * @param data 审核数据
   * @returns 审核后的知识库文件
   */
  async reviewKnowledgeFile(id: string, data: KnowledgeFileReviewFields): Promise<KnowledgeFile> {
    try {
      const response = await axios.put(`${API_BASE_URL}/knowledge-files/${id}/review`, data);
      return response.data.data;
    } catch (error) {
      console.error('审核知识库文件失败:', error);
      throw error;
    }
  },

  /**
   * 删除知识库文件
   * @param id 知识库文件ID
   * @returns 操作结果
   */
  async deleteKnowledgeFile(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.delete(`${API_BASE_URL}/knowledge-files/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除知识库文件失败:', error);
      throw error;
    }
  },

  /**
   * 下载知识库文件
   * @param id 知识库文件ID
   * @returns 文件URL
   */
  async downloadKnowledgeFile(id: string): Promise<string> {
    try {
      const response = await axios.get(`${API_BASE_URL}/knowledge-files/${id}/download`, {
        responseType: 'blob'
      });

      // 创建临时URL
      const url = window.URL.createObjectURL(new Blob([response.data]));
      return url;
    } catch (error) {
      console.error('下载知识库文件失败:', error);
      throw error;
    }
  }
};

export default knowledgeService;
