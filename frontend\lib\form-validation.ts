/**
 * 表单验证工具
 *
 * 提供常用的表单验证函数和错误消息
 */

// 验证结果类型
export type ValidationResult = {
  valid: boolean
  message?: string
}

/**
 * 验证必填字段
 * @param value 字段值
 * @param fieldName 字段名称（用于错误消息）
 * @returns 验证结果
 */
export function validateRequired(value: string, fieldName: string = "此字段"): ValidationResult {
  if (!value || value.trim() === "") {
    return {
      valid: false,
      message: `${fieldName}不能为空`
    }
  }
  return { valid: true }
}

/**
 * 验证字段最小长度
 * @param value 字段值
 * @param minLength 最小长度
 * @param fieldName 字段名称（用于错误消息）
 * @returns 验证结果
 */
export function validateMinLength(value: string, minLength: number, fieldName: string = "此字段"): ValidationResult {
  if (value.length < minLength) {
    return {
      valid: false,
      message: `${fieldName}长度不能少于${minLength}个字符`
    }
  }
  return { valid: true }
}

/**
 * 验证字段最大长度
 * @param value 字段值
 * @param maxLength 最大长度
 * @param fieldName 字段名称（用于错误消息）
 * @returns 验证结果
 */
export function validateMaxLength(value: string, maxLength: number, fieldName: string = "此字段"): ValidationResult {
  if (value.length > maxLength) {
    return {
      valid: false,
      message: `${fieldName}长度不能超过${maxLength}个字符`
    }
  }
  return { valid: true }
}

/**
 * 验证邮箱格式
 * @param value 邮箱地址
 * @returns 验证结果
 */
export function validateEmail(value: string): ValidationResult {
  if (!value) return { valid: true } // 如果为空，不验证（使用validateRequired处理必填）

  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  if (!emailRegex.test(value)) {
    return {
      valid: false,
      message: "请输入有效的邮箱地址"
    }
  }
  return { valid: true }
}

/**
 * 验证手机号格式
 * @param value 手机号
 * @returns 验证结果
 */
export function validatePhone(value: string): ValidationResult {
  if (!value) return { valid: true } // 如果为空，不验证（使用validateRequired处理必填）

  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(value)) {
    return {
      valid: false,
      message: "请输入有效的手机号码"
    }
  }
  return { valid: true }
}

/**
 * 验证密码强度
 * @param value 密码
 * @param options 密码要求选项
 * @returns 验证结果
 */
export function validatePassword(
  value: string,
  options: {
    minLength?: number
    requireUppercase?: boolean
    requireLowercase?: boolean
    requireNumbers?: boolean
    requireSpecialChars?: boolean
  } = {}
): ValidationResult {
  // 设置默认值，但只在选项未明确指定时使用默认值
  const minLength = options.minLength !== undefined ? options.minLength : 8;
  const requireUppercase = options.requireUppercase !== undefined ? options.requireUppercase : false;
  const requireLowercase = options.requireLowercase !== undefined ? options.requireLowercase : false;
  const requireNumbers = options.requireNumbers !== undefined ? options.requireNumbers : false;
  const requireSpecialChars = options.requireSpecialChars !== undefined ? options.requireSpecialChars : false;

  // 验证最小长度
  if (value.length < minLength) {
    return {
      valid: false,
      message: `密码长度不能少于${minLength}个字符`
    }
  }

  // 验证大写字母
  if (requireUppercase && !/[A-Z]/.test(value)) {
    return {
      valid: false,
      message: "密码必须包含至少一个大写字母"
    }
  }

  // 验证小写字母
  if (requireLowercase && !/[a-z]/.test(value)) {
    return {
      valid: false,
      message: "密码必须包含至少一个小写字母"
    }
  }

  // 验证数字
  if (requireNumbers && !/\d/.test(value)) {
    return {
      valid: false,
      message: "密码必须包含至少一个数字"
    }
  }

  // 验证特殊字符
  if (requireSpecialChars && !/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(value)) {
    return {
      valid: false,
      message: "密码必须包含至少一个特殊字符"
    }
  }

  return { valid: true }
}

/**
 * 验证两个字段是否匹配
 * @param value1 第一个字段值
 * @param value2 第二个字段值
 * @param fieldName 字段名称（用于错误消息）
 * @returns 验证结果
 */
export function validateMatch(value1: string, value2: string, fieldName: string = "两个字段"): ValidationResult {
  if (value1 !== value2) {
    return {
      valid: false,
      message: `${fieldName}不匹配`
    }
  }
  return { valid: true }
}

/**
 * 验证URL格式
 * @param value URL
 * @returns 验证结果
 */
export function validateUrl(value: string): ValidationResult {
  if (!value) return { valid: true } // 如果为空，不验证（使用validateRequired处理必填）

  // 检查URL是否包含协议
  if (!value.match(/^https?:\/\//i)) {
    return {
      valid: false,
      message: "URL必须包含http://或https://协议"
    }
  }

  try {
    new URL(value)
    return { valid: true }
  } catch (e) {
    return {
      valid: false,
      message: "请输入有效的URL"
    }
  }
}

/**
 * 组合多个验证函数
 * @param value 要验证的值
 * @param validators 验证函数数组
 * @returns 验证结果
 */
export function validateAll(value: string, validators: ((value: string) => ValidationResult)[]): ValidationResult {
  for (const validator of validators) {
    const result = validator(value)
    if (!result.valid) {
      return result
    }
  }
  return { valid: true }
}
