/**
 * 通知服务
 *
 * 提供创建各种类型通知的方法
 */

const { Notification, User, File, KnowledgeBase } = require('../models');

/**
 * 创建文件审核通知
 * @param {Object} file - 文件对象
 * @param {Object} knowledgeBase - 知识库对象
 * @param {Object} uploader - 上传者对象
 * @returns {Promise<Array>} 创建的通知数组
 */
exports.createFileReviewNotification = async (file, knowledgeBase, uploader) => {
  try {
    // 获取所有管理员用户
    const admins = await User.findAll({
      where: { role: 'admin' }
    });

    if (!admins || admins.length === 0) {
      console.warn('没有找到管理员用户，无法发送文件审核通知');
      return [];
    }

    console.log(`找到 ${admins.length} 个管理员用户，准备发送文件审核通知`);

    // 为每个管理员创建通知
    const notifications = [];
    for (const admin of admins) {
      const notification = await Notification.create({
        title: '新文件等待审核',
        content: `用户 ${uploader.username} 上传了文件 "${file.original_name}" 到知识库 "${knowledgeBase.name}"，请审核。`,
        type: 'file_review',
        is_read: false,
        user_id: admin.id,
        related_id: file.id,
        related_type: 'file'
      });

      notifications.push(notification);
      console.log(`已为管理员 ${admin.username} 创建文件审核通知`);
    }

    return notifications;
  } catch (error) {
    console.error('创建文件审核通知失败:', error);
    return [];
  }
};

/**
 * 创建文件审核结果通知
 * @param {Object} file - 文件对象
 * @param {Object} knowledgeBase - 知识库对象
 * @param {Object} reviewer - 审核者对象
 * @param {string} status - 审核状态 (approved/rejected)
 * @param {string} reason - 拒绝原因 (可选)
 * @returns {Promise<Notification>} 创建的通知
 */
exports.createFileReviewResultNotification = async (file, knowledgeBase, reviewer, status, reason) => {
  try {
    // 获取上传者
    const uploader = await User.findByPk(file.uploader_id);

    if (!uploader) {
      console.warn(`未找到文件 ${file.id} 的上传者，无法发送审核结果通知`);
      return null;
    }

    // 创建通知内容
    let title = '';
    let content = '';

    if (status === 'approved') {
      title = '文件审核已通过';
      content = `您上传到知识库 "${knowledgeBase.name}" 的文件 "${file.original_name}" 已通过审核。`;
    } else {
      title = '文件审核被拒绝';
      content = `您上传到知识库 "${knowledgeBase.name}" 的文件 "${file.original_name}" 被拒绝。`;
      if (reason) {
        content += ` 原因: ${reason}`;
      }
    }

    // 创建通知
    const notification = await Notification.create({
      title,
      content,
      type: 'file_review_result',
      is_read: false,
      user_id: uploader.id,
      related_id: file.id,
      related_type: 'file'
    });

    console.log(`已为用户 ${uploader.username} 创建文件审核结果通知`);
    return notification;
  } catch (error) {
    console.error('创建文件审核结果通知失败:', error);
    return null;
  }
};
