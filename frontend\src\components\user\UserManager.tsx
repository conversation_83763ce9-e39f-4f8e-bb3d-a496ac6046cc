"use client"

import { useState, useEffect } from 'react'
import { X, AlertCircle, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { UserTable } from './UserTable'
import { UserForm } from './UserForm'
import { ResetPasswordForm } from './ResetPasswordForm'
import {
  UserType,
  UserTableParams,
  UserTableData,
  UserFormFields,
  ResetPasswordFormFields,
  UserTableAction,
  UserBatchAction
} from './types'
import userService from '@/services/user-service'
import roleService, { Role } from '@/services/role-service'

/**
 * 用户管理组件
 *
 * 用于系统管理页面中管理用户
 */
export function UserManager() {
  // 用户表格数据
  const [tableData, setTableData] = useState<UserTableData>({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0
    }
  })

  // 表格加载状态
  const [loading, setLoading] = useState(false)

  // 选中的用户ID
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([])

  // 角色列表
  const [roles, setRoles] = useState<Role[]>([])

  // 模态框状态
  const [showUserModal, setShowUserModal] = useState(false)
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false)
  const [showDeleteUserModal, setShowDeleteUserModal] = useState(false)
  const [showDeleteBatchModal, setShowDeleteBatchModal] = useState(false)
  const [showChangeRoleModal, setShowChangeRoleModal] = useState(false)

  // 当前操作的用户
  const [currentUser, setCurrentUser] = useState<UserType | null>(null)

  // 批量操作的角色ID
  const [batchRoleId, setBatchRoleId] = useState('')

  // 提示消息
  const [showSuccessToast, setShowSuccessToast] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')
  const [showErrorToast, setShowErrorToast] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')

  // 获取用户列表
  const fetchUsers = async (params: UserTableParams) => {
    setLoading(true)
    try {
      // 调用API获取用户列表
      const response = await userService.getAllUsers()

      // 确保我们有正确的数据格式
      const userData = response.data || response

      // 转换API响应为前端所需格式
      const users: UserType[] = userData.map(user => ({
        id: user.id.toString(),
        name: user.username, // 使用username作为name
        username: user.username,
        phone: user.phone || '',
        email: user.email,
        roleId: user.role_id?.toString() || '',
        roleName: user.userRole ? user.userRole.name : '', // 直接使用API返回的角色名称
        status: user.is_active ? '正常' : '禁用',
        createdAt: new Date(user.created_at).toLocaleString(),
        lastLogin: user.last_login ? new Date(user.last_login).toLocaleString() : undefined
      }))

      // 填充角色名称（仅当API返回的角色名称为空时）
      for (const user of users) {
        if (!user.roleName && user.roleId) {
          const role = roles.find(r => r.id.toString() === user.roleId)
          if (role) {
            user.roleName = role.name
          }
        }
      }

      // 筛选
      let filteredUsers = [...users]

      // 搜索筛选
      if (params.filters.search) {
        const searchText = params.filters.search.toLowerCase()
        filteredUsers = filteredUsers.filter(
          user =>
            user.name.toLowerCase().includes(searchText) ||
            user.username.toLowerCase().includes(searchText) ||
            (user.phone && user.phone.includes(searchText)) ||
            user.email.toLowerCase().includes(searchText)
        )
      }

      // 状态筛选
      if (params.filters.status !== 'all') {
        filteredUsers = filteredUsers.filter(user => user.status === params.filters.status)
      }

      // 角色筛选
      if (params.filters.roleId !== 'all') {
        filteredUsers = filteredUsers.filter(user => user.roleId === params.filters.roleId)
      }

      // 排序
      if (params.sorter) {
        const { field, order } = params.sorter
        filteredUsers.sort((a, b) => {
          let comparison = 0

          // 根据字段进行比较
          switch (field) {
            case 'username':
              comparison = a.username.localeCompare(b.username)
              break
            case 'email':
              comparison = a.email.localeCompare(b.email)
              break
            case 'roleName':
              comparison = a.roleName.localeCompare(b.roleName)
              break
            case 'status':
              comparison = a.status.localeCompare(b.status)
              break
            case 'createdAt':
              comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
              break
            case 'lastLogin':
              if (a.lastLogin && b.lastLogin) {
                comparison = new Date(a.lastLogin).getTime() - new Date(b.lastLogin).getTime()
              } else if (a.lastLogin) {
                comparison = 1
              } else if (b.lastLogin) {
                comparison = -1
              }
              break
            default:
              break
          }

          // 根据排序方向调整
          return order === 'ascend' ? comparison : -comparison
        })
      }

      // 计算分页
      const total = filteredUsers.length
      const start = (params.pagination.current - 1) * params.pagination.pageSize
      const end = start + params.pagination.pageSize
      const pagedUsers = filteredUsers.slice(start, end)

      setTableData({
        list: pagedUsers,
        pagination: {
          ...params.pagination,
          total
        }
      })
    } catch (error) {
      console.error('获取用户列表失败:', error)
      showError('获取用户列表失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      // 调用API获取角色列表
      const roleList = await roleService.getRoles()
      setRoles(roleList)
    } catch (error) {
      console.error('获取角色列表失败:', error)
      showError('获取角色列表失败，请重试')
    }
  }

  // 初始化
  useEffect(() => {
    const initParams: UserTableParams = {
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      filters: {
        search: '',
        status: 'all',
        roleId: 'all'
      },
      sorter: {
        field: 'createdAt',
        order: 'descend'
      }
    }

    // 先获取角色列表，然后再获取用户列表
    const initData = async () => {
      try {
        // 先获取角色列表
        await fetchRoles()
        // 然后获取用户列表
        await fetchUsers(initParams)
      } catch (error) {
        console.error('初始化数据失败:', error)
        showError('初始化数据失败，请刷新页面重试')
      }
    }

    initData()
  }, [])

  // 处理表格变化
  const handleTableChange = (params: UserTableParams) => {
    fetchUsers(params)
  }

  // 处理表格操作
  const handleTableAction = (action: UserTableAction, user: UserType) => {
    setCurrentUser(user)

    switch (action) {
      case 'edit':
        setShowUserModal(true)
        break
      case 'resetPassword':
        setShowResetPasswordModal(true)
        break
      case 'delete':
        setShowDeleteUserModal(true)
        break
      case 'enable':
        handleEnableUser(user.id)
        break
      case 'disable':
        handleDisableUser(user.id)
        break
      default:
        break
    }
  }

  // 处理批量操作
  const handleBatchAction = (action: UserBatchAction, userIds: string[]) => {
    if (userIds.length === 0) return

    switch (action) {
      case 'enable':
        handleEnableUsers(userIds)
        break
      case 'disable':
        handleDisableUsers(userIds)
        break
      case 'delete':
        setSelectedUserIds(userIds)
        setShowDeleteBatchModal(true)
        break
      case 'changeRole':
        setSelectedUserIds(userIds)
        setShowChangeRoleModal(true)
        break
      default:
        break
    }
  }

  // 处理选择变化
  const handleSelectionChange = (selectedRowKeys: string[]) => {
    setSelectedUserIds(selectedRowKeys)
  }

  // 处理创建/编辑用户
  const handleSubmitUser = async (values: UserFormFields) => {
    setLoading(true)
    try {
      if (values.id) {
        // 编辑用户
        const userId = parseInt(values.id)
        await userService.updateUser(userId, {
          email: values.email,
          phone: values.phone,
          role_id: parseInt(values.roleId),
          is_active: values.status === '正常'
        })
        showSuccess(`已成功更新用户 ${values.username}！`)
      } else {
        // 创建用户
        await userService.register({
          username: values.username,
          password: values.password || '123456', // 默认密码
          email: values.email,
          phone: values.phone
        })
        showSuccess(`已成功创建用户 ${values.username}！`)
      }

      // 关闭模态框
      setShowUserModal(false)

      // 刷新用户列表
      const params: UserTableParams = {
        pagination: tableData.pagination,
        filters: {
          search: '',
          status: 'all',
          roleId: 'all'
        },
        sorter: {
          field: 'createdAt',
          order: 'descend'
        }
      }
      fetchUsers(params)
    } catch (error: any) {
      console.error('提交用户失败:', error)
      showError(error.response?.data?.message || '提交用户失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理重置密码
  const handleResetPassword = async (values: ResetPasswordFormFields) => {
    if (!currentUser) return

    setLoading(true)
    try {
      // 调用API重置密码
      const userId = parseInt(currentUser.id)
      await userService.resetUserPassword(userId, values.password)

      showSuccess(`已成功重置用户 ${currentUser.name} 的密码！`)

      // 关闭模态框
      setShowResetPasswordModal(false)
    } catch (error: any) {
      console.error('重置密码失败:', error)
      showError(error.response?.data?.message || '重置密码失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理删除用户
  const handleDeleteUser = async () => {
    if (!currentUser) return

    setLoading(true)
    try {
      // 调用API删除用户
      const userId = parseInt(currentUser.id)
      await userService.deleteUser(userId)

      showSuccess(`已成功删除用户 ${currentUser.name}！`)

      // 关闭模态框
      setShowDeleteUserModal(false)

      // 刷新用户列表
      const params: UserTableParams = {
        pagination: tableData.pagination,
        filters: {
          search: '',
          status: 'all',
          roleId: 'all'
        },
        sorter: {
          field: 'createdAt',
          order: 'descend'
        }
      }
      fetchUsers(params)
    } catch (error: any) {
      console.error('删除用户失败:', error)
      showError(error.response?.data?.message || '删除用户失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理批量删除用户
  const handleDeleteUsers = async () => {
    if (selectedUserIds.length === 0) return

    setLoading(true)
    try {
      // 调用API批量删除用户
      for (const userId of selectedUserIds) {
        await userService.deleteUser(parseInt(userId))
      }

      showSuccess(`已成功删除 ${selectedUserIds.length} 个用户！`)

      // 关闭模态框
      setShowDeleteBatchModal(false)

      // 清空选中的用户
      setSelectedUserIds([])

      // 刷新用户列表
      const params: UserTableParams = {
        pagination: tableData.pagination,
        filters: {
          search: '',
          status: 'all',
          roleId: 'all'
        },
        sorter: {
          field: 'createdAt',
          order: 'descend'
        }
      }
      fetchUsers(params)
    } catch (error: any) {
      console.error('批量删除用户失败:', error)
      showError(error.response?.data?.message || '批量删除用户失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理批量修改角色
  const handleChangeRoles = async () => {
    if (selectedUserIds.length === 0 || !batchRoleId) return

    setLoading(true)
    try {
      // 调用API批量修改角色
      for (const userId of selectedUserIds) {
        await userService.updateUser(parseInt(userId), {
          role_id: parseInt(batchRoleId)
        })
      }

      const roleName = roles.find(role => role.id.toString() === batchRoleId)?.name || ''
      showSuccess(`已成功将 ${selectedUserIds.length} 个用户的角色修改为 ${roleName}！`)

      // 关闭模态框
      setShowChangeRoleModal(false)

      // 清空选中的用户
      setSelectedUserIds([])

      // 刷新用户列表
      const params: UserTableParams = {
        pagination: tableData.pagination,
        filters: {
          search: '',
          status: 'all',
          roleId: 'all'
        },
        sorter: {
          field: 'createdAt',
          order: 'descend'
        }
      }
      fetchUsers(params)
    } catch (error: any) {
      console.error('批量修改角色失败:', error)
      showError(error.response?.data?.message || '批量修改角色失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理启用用户
  const handleEnableUser = async (userId: string) => {
    setLoading(true)
    try {
      // 调用API启用用户
      await userService.updateUser(parseInt(userId), {
        is_active: true
      })

      showSuccess('用户已成功启用！')

      // 刷新用户列表
      const params: UserTableParams = {
        pagination: tableData.pagination,
        filters: {
          search: '',
          status: 'all',
          roleId: 'all'
        },
        sorter: {
          field: 'createdAt',
          order: 'descend'
        }
      }
      fetchUsers(params)
    } catch (error: any) {
      console.error('启用用户失败:', error)
      showError(error.response?.data?.message || '启用用户失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理禁用用户
  const handleDisableUser = async (userId: string) => {
    setLoading(true)
    try {
      // 调用API禁用用户
      await userService.updateUser(parseInt(userId), {
        is_active: false
      })

      showSuccess('用户已成功禁用！')

      // 刷新用户列表
      const params: UserTableParams = {
        pagination: tableData.pagination,
        filters: {
          search: '',
          status: 'all',
          roleId: 'all'
        },
        sorter: {
          field: 'createdAt',
          order: 'descend'
        }
      }
      fetchUsers(params)
    } catch (error: any) {
      console.error('禁用用户失败:', error)
      showError(error.response?.data?.message || '禁用用户失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理批量启用用户
  const handleEnableUsers = async (userIds: string[]) => {
    if (userIds.length === 0) return

    setLoading(true)
    try {
      // 调用API批量启用用户
      for (const userId of userIds) {
        await userService.updateUser(parseInt(userId), {
          is_active: true
        })
      }

      showSuccess(`已成功启用 ${userIds.length} 个用户！`)

      // 清空选中的用户
      setSelectedUserIds([])

      // 刷新用户列表
      const params: UserTableParams = {
        pagination: tableData.pagination,
        filters: {
          search: '',
          status: 'all',
          roleId: 'all'
        },
        sorter: {
          field: 'createdAt',
          order: 'descend'
        }
      }
      fetchUsers(params)
    } catch (error: any) {
      console.error('批量启用用户失败:', error)
      showError(error.response?.data?.message || '批量启用用户失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理批量禁用用户
  const handleDisableUsers = async (userIds: string[]) => {
    if (userIds.length === 0) return

    setLoading(true)
    try {
      // 调用API批量禁用用户
      for (const userId of userIds) {
        await userService.updateUser(parseInt(userId), {
          is_active: false
        })
      }

      showSuccess(`已成功禁用 ${userIds.length} 个用户！`)

      // 清空选中的用户
      setSelectedUserIds([])

      // 刷新用户列表
      const params: UserTableParams = {
        pagination: tableData.pagination,
        filters: {
          search: '',
          status: 'all',
          roleId: 'all'
        },
        sorter: {
          field: 'createdAt',
          order: 'descend'
        }
      }
      fetchUsers(params)
    } catch (error: any) {
      console.error('批量禁用用户失败:', error)
      showError(error.response?.data?.message || '批量禁用用户失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 显示成功提示
  const showSuccess = (message: string) => {
    setSuccessMessage(message)
    setShowSuccessToast(true)
    setTimeout(() => {
      setShowSuccessToast(false)
    }, 3000)
  }

  // 显示错误提示
  const showError = (message: string) => {
    setErrorMessage(message)
    setShowErrorToast(true)
    setTimeout(() => {
      setShowErrorToast(false)
    }, 3000)
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-bold">用户管理</h2>
        <Button
          className="bg-[#f5a623] hover:bg-[#f5a623]/90"
          onClick={() => {
            setCurrentUser(null)
            setShowUserModal(true)
          }}
        >
          添加用户
        </Button>
      </div>

      <UserTable
        loading={loading}
        data={tableData}
        onTableChange={handleTableChange}
        onActionClick={handleTableAction}
        onBatchActionClick={handleBatchAction}
        onSelectionChange={handleSelectionChange}
      />

      {/* 用户编辑模态框 */}
      <Dialog open={showUserModal} onOpenChange={setShowUserModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{currentUser ? "编辑用户" : "新增用户"}</DialogTitle>
          </DialogHeader>
          <UserForm
            loading={loading}
            user={currentUser || undefined}
            roles={roles.map(role => ({ id: role.id.toString(), name: role.name }))}
            onSubmit={handleSubmitUser}
            onCancel={() => setShowUserModal(false)}
          />
        </DialogContent>
      </Dialog>

      {/* 重置密码模态框 */}
      {showResetPasswordModal && currentUser && (
        <Dialog open={showResetPasswordModal} onOpenChange={setShowResetPasswordModal}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>重置密码</DialogTitle>
            </DialogHeader>
            <ResetPasswordForm
              loading={loading}
              user={currentUser}
              onSubmit={handleResetPassword}
              onCancel={() => setShowResetPasswordModal(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* 删除用户确认模态框 */}
      {showDeleteUserModal && currentUser && (
        <Dialog open={showDeleteUserModal} onOpenChange={setShowDeleteUserModal}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>删除用户</DialogTitle>
            </DialogHeader>
            <p className="mb-4">
              您确定要删除用户 <span className="font-bold">{currentUser.name}</span> 吗？此操作不可撤销。
            </p>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowDeleteUserModal(false)} disabled={loading}>
                取消
              </Button>
              <Button
                className="bg-red-600 hover:bg-red-700 text-white"
                onClick={handleDeleteUser}
                disabled={loading}
              >
                {loading ? '删除中...' : '确认删除'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* 批量删除用户确认模态框 */}
      {showDeleteBatchModal && (
        <Dialog open={showDeleteBatchModal} onOpenChange={setShowDeleteBatchModal}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>批量删除用户</DialogTitle>
            </DialogHeader>
            <p className="mb-4">
              您确定要删除选中的 <span className="font-bold">{selectedUserIds.length}</span> 个用户吗？此操作不可撤销。
            </p>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowDeleteBatchModal(false)} disabled={loading}>
                取消
              </Button>
              <Button
                className="bg-red-600 hover:bg-red-700 text-white"
                onClick={handleDeleteUsers}
                disabled={loading}
              >
                {loading ? '删除中...' : '确认删除'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* 批量修改角色模态框 */}
      {showChangeRoleModal && (
        <Dialog open={showChangeRoleModal} onOpenChange={setShowChangeRoleModal}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>批量修改角色</DialogTitle>
            </DialogHeader>
            <p className="mb-4">
              您正在为选中的 <span className="font-bold">{selectedUserIds.length}</span> 个用户修改角色。
            </p>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                选择角色
              </label>
              <select
                className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                value={batchRoleId}
                onChange={(e) => setBatchRoleId(e.target.value)}
                disabled={loading}
              >
                <option value="">请选择角色</option>
                {roles.map((role) => (
                  <option key={role.id} value={role.id.toString()}>
                    {role.name}
                  </option>
                ))}
              </select>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowChangeRoleModal(false)} disabled={loading}>
                取消
              </Button>
              <Button
                onClick={handleChangeRoles}
                disabled={loading || !batchRoleId}
              >
                {loading ? '提交中...' : '确认修改'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* 成功提示 */}
      {showSuccessToast && (
        <div className="fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md z-50 animate-fade-in-up">
          <div className="flex">
            <div className="py-1">
              <CheckCircle className="h-6 w-6 text-green-500 mr-4" />
            </div>
            <div>
              <p className="font-bold">成功</p>
              <p className="text-sm">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {showErrorToast && (
        <div className="fixed bottom-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-md z-50 animate-fade-in-up">
          <div className="flex">
            <div className="py-1">
              <AlertCircle className="h-6 w-6 text-red-500 mr-4" />
            </div>
            <div>
              <p className="font-bold">错误</p>
              <p className="text-sm">{errorMessage}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
