"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"

// 为window对象添加retryCount属性
declare global {
  interface Window {
    retryCount?: number;
  }
}
import { Navbar } from "@/components/navbar"
import { Search, X, MessageSquare, Alert<PERSON>riangle, Send } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import type { ResearchAssistant } from "@/types/ai-assistants"
import { getResearchAssistants as getAssistants, queryResearchAssistant as queryAssistant } from "@/services/ai-assistant-service"
import { isLoggedIn as checkIsLoggedIn } from "@/services/auth-service"
import { toast } from "@/components/ui/use-toast"
import { StreamTypewriter } from "@/components/ui/typewriter"
import { SimpleTypewriter } from "@/components/ui/simple-typewriter"
import { SimpleTypewriterNew } from "@/components/ui/simple-typewriter-new"
import { HtmlTypewriter } from "@/components/ui/html-typewriter"
import { logger, sanitizeData } from "@/utils/logger"
export default function AIAssistantPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [showAssistantModal, setShowAssistantModal] = useState(false)
  const [selectedAssistant, setSelectedAssistant] = useState<ResearchAssistant | null>(null)
  const [chatInput, setChatInput] = useState("")
  const [chatMessages, setChatMessages] = useState<{ role: "user" | "assistant"; content: string }[]>([])
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [assistants, setAssistants] = useState<ResearchAssistant[]>([])
  const [loading, setLoading] = useState(true)
  const [conversationId, setConversationId] = useState<string | undefined>(undefined)
  const [isThinking, setIsThinking] = useState(false)
  const [thinkingDots, setThinkingDots] = useState(".")
  const [isStreaming, setIsStreaming] = useState(false)
  const chatEndRef = useRef<HTMLDivElement>(null)

  // 是否显示打字机效果
  const [showTypewriter, setShowTypewriter] = useState(false)
  // 打字机显示的文本内容
  const [typewriterText, setTypewriterText] = useState("")
  // 流式响应内容
  const [streamingContent, setStreamingContent] = useState("")
  // 流式响应是否正在进行
  const [isTyping, setIsTyping] = useState(false)
  // 流式响应的会话ID
  const [streamingConversationId, setStreamingConversationId] = useState<string | null>(null)

  // 滚动到底部
  const scrollToBottom = () => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }

  // 当消息更新时滚动到底部
  useEffect(() => {
    scrollToBottom()
  }, [chatMessages, streamingContent])

  // 重置打字机状态
  const resetTypewriter = () => {
    setShowTypewriter(false)
    setTypewriterText("")
    setStreamingContent("")
    setIsTyping(false)
  }

  // 保存最后一次响应的引用
  const lastResponse = useRef<{answer: string} | null>(null)

  // 监听打字机效果完成
  const handleTypewriterComplete = () => {
    logger.debug('打字机效果完成，更新聊天记录')
    // 打字机效果完成后，更新聊天记录
    setChatMessages(prev => {
      logger.debug('更新聊天记录，当前消息数:', prev.length)
      const newMessages = [...prev]
      // 找到最后一条助手消息并替换为纯文本内容（与打字机显示的内容一致）
      const lastIndex = newMessages.length - 1
      if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
        logger.debug('找到最后一条助手消息，替换内容')
        // 使用纯文本内容（与打字机显示的内容一致）
        logger.debug('更新最后一条消息为纯文本回复，长度:', typewriterText.length)
        newMessages[lastIndex] = {
          role: "assistant",
          content: typewriterText
        }
      } else {
        logger.debug('未找到助手消息，最后一条消息角色:', lastIndex >= 0 ? newMessages[lastIndex].role : '无消息')
      }
      return newMessages
    })
    // 隐藏打字机效果
    logger.debug('隐藏打字机效果')
    setShowTypewriter(false)
    // 结束流式响应状态
    logger.debug('结束流式响应状态')
    setIsStreaming(false)
    // 滚动到底部
    scrollToBottom()
  }

  // 思考动画效果
  useEffect(() => {
    if (isThinking) {
      const interval = setInterval(() => {
        setThinkingDots(prev => {
          if (prev.length >= 3) return "."
          return prev + "."
        })
      }, 500)

      return () => clearInterval(interval)
    }
  }, [isThinking])

  // 检查用户是否已登录并获取助手数据
  useEffect(() => {
    const initialize = async () => {
      try {
        // 检查登录状态
        const loggedIn = checkIsLoggedIn()
        setIsLoggedIn(loggedIn)

        // 只有在用户已登录时才获取研究助手列表
        if (loggedIn) {
          setLoading(true)
          const assistantList = await getAssistants()
          logger.debug('获取到的研究助手列表:', sanitizeData(assistantList))
          setAssistants(assistantList)
          setLoading(false) // 无论成功与否，都设置loading为false
        } else {
          // 用户未登录，不加载数据
          setLoading(false)
        }
      } catch (error) {
        logger.error('初始化失败:', sanitizeData(error))
        toast({
          title: '加载失败',
          description: '无法获取AI助手列表，请稍后再试',
          variant: 'destructive'
        })
        setLoading(false)
      }
    }

    initialize()
  }, [])

  // 根据搜索过滤助手
  const filteredAssistants = assistants.filter((assistant) => {
    return (
      searchQuery === "" ||
      assistant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      assistant.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })

  // 打开助手对话窗口
  const openAssistantModal = (assistant: ResearchAssistant) => {
    // 检查用户是否登录
    if (!isLoggedIn) {
      toast({
        title: '需要登录',
        description: '请先登录后再使用AI助手',
        variant: 'destructive'
      })
      return
    }

    setSelectedAssistant(assistant)
    setShowAssistantModal(true)
    // 初始化对话
    setChatMessages([
      {
        role: "assistant",
        content: `您好！我是${assistant.name}，${assistant.description}。请问有什么可以帮助您的吗？`,
      },
    ])
    // 重置会话ID
    setConversationId(undefined)
  }

  // 关闭助手对话窗口
  const closeAssistantModal = () => {
    setShowAssistantModal(false)
    setSelectedAssistant(null)
    setChatInput("")
    setChatMessages([])
    setConversationId(undefined)
  }

  // 发送消息
  const sendMessage = async () => {
    if (!chatInput.trim() || !selectedAssistant || isStreaming) return

    // 重置打字机状态
    resetTypewriter()

    // 记录当前消息，用于调试
    logger.debug('发送消息:', chatInput)

    // 检查用户是否登录
    if (!isLoggedIn) {
      toast({
        title: '需要登录',
        description: '请先登录后再使用AI助手',
        variant: 'destructive'
      })
      return
    }

    const userMessage = chatInput.trim()
    setChatInput("")

    // 添加用户消息到聊天记录
    setChatMessages(prev => [...prev, { role: "user", content: userMessage }])

    // 所有助手都使用统一的处理方式（流式响应+打字机效果）

    // 设置流式状态
    setIsStreaming(true)

    // 重置打字机状态
    resetTypewriter()

    try {
      // 检查登录状态
      if (!isLoggedIn) {
        logger.error('用户未登录，无法使用流式响应')
        throw new Error('未登录，请先登录')
      }

      logger.debug('开始流式打字，助手ID:', selectedAssistant.id, '会话ID:', conversationId || '新会话')

      // 添加一个空的助手消息，用于显示流式响应
      setChatMessages(prev => {
        // 检查是否已经有助手消息
        const lastIndex = prev.length - 1
        if (lastIndex >= 0 && prev[lastIndex].role === "assistant") {
          // 已经有助手消息，移除它并添加新的空消息
          logger.debug('移除现有助手消息并添加新的空消息')
          return [...prev.slice(0, lastIndex), { role: "assistant", content: "正在生成回复..." }]
        }
        logger.debug('添加新的空助手消息')
        return [...prev, { role: "assistant", content: "正在生成回复..." }]
      })

      // 使用直接API调用替代流式响应
      logger.debug('发送API请求，使用会话ID:', conversationId || '新会话')

      try {
        // 调用API获取AI回复
        const response = await queryAssistant(userMessage, conversationId, selectedAssistant?.id)

        logger.debug('API响应成功，内容长度:', response.answer?.length || 0)

        // 保存原始响应，用于打字机效果完成后更新聊天记录
        lastResponse.current = response

        // 如果是第一次对话，保存会话ID
        if (!conversationId && response.conversation_id) {
          logger.debug('更新会话ID:', response.conversation_id)
          setConversationId(response.conversation_id)
        }

        // 检查响应内容是否为空
        if (response.answer && response.answer.trim() !== "") {
          // 检查是否包含思考过程（details标签）
          let cleanedAnswer = response.answer;

          // 如果包含思考过程，提取出正式回复部分
          if (response.answer.includes('<details') && response.answer.includes('</details>')) {
            const detailsEndIndex = response.answer.indexOf('</details>') + 10;
            cleanedAnswer = response.answer.substring(detailsEndIndex).trim();
            logger.debug('提取出正式回复部分，长度:', cleanedAnswer.length);
          }

          // 移除所有HTML标签，只保留纯文本
          const textOnly = cleanedAnswer.replace(/<[^>]*>/g, '');
          logger.debug('移除HTML标签后的纯文本，长度:', textOnly.length);

          // 检查是否是占位符消息
          const placeholderMessages = [
            '正在处理您的请求，请稍候...',
            '正在思考您的问题，请稍候...',
            '开始生成回复，请稍候...',
            'AI助手已开始处理您的请求，请稍候...',
            'AI助手正在思考您的问题，请稍候...',
            'AI助手开始生成回复，请稍候...',
            'AI助手正在生成回复，请稍候...',
            'AI助手正在处理您的请求，请稍候...'
          ];

          const isPlaceholder = placeholderMessages.some(placeholder =>
            textOnly.includes(placeholder) && textOnly.length < 100
          );

          if (isPlaceholder) {
            logger.debug('检测到占位符消息，等待真实回复...');
            // 如果是占位符消息，显示"正在生成回复..."
            setChatMessages(prev => {
              const newMessages = [...prev];
              const lastIndex = newMessages.length - 1;
              if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
                newMessages[lastIndex] = {
                  role: "assistant",
                  content: "正在生成回复..."
                };
              }
              return newMessages;
            });

            // 继续保持流式状态
            setIsStreaming(true);

            // 5秒后重试
            setTimeout(() => {
              logger.debug('重新查询AI助手...');
              queryAssistant(userMessage, conversationId, selectedAssistant?.id)
                .then(newResponse => {
                  logger.debug('重试获取到新响应:', sanitizeData(newResponse));
                  // 递归处理新响应
                  if (newResponse && newResponse.answer &&
                      !placeholderMessages.some(p => newResponse.answer.includes(p) && newResponse.answer.length < 100)) {
                    // 如果新响应不是占位符，处理它
                    lastResponse.current = newResponse;

                    // 清理HTML和提取正式回复
                    let newCleanedAnswer = newResponse.answer;
                    if (newResponse.answer.includes('<details') && newResponse.answer.includes('</details>')) {
                      const detailsEndIndex = newResponse.answer.indexOf('</details>') + 10;
                      newCleanedAnswer = newResponse.answer.substring(detailsEndIndex).trim();
                    }

                    // 移除HTML标签
                    const newTextOnly = newCleanedAnswer.replace(/<[^>]*>/g, '');

                    // 设置打字机文本
                    logger.debug('设置打字机文本:', newTextOnly.substring(0, 50) + '...');
                    setTypewriterText(newTextOnly);
                    setShowTypewriter(true);
                  } else {
                    // 如果还是占位符，再次重试（最多重试3次）
                    if (window.retryCount === undefined) {
                      window.retryCount = 1;
                    } else if (window.retryCount < 3) {
                      window.retryCount++;
                    } else {
                      // 超过重试次数，显示错误消息
                      logger.debug('超过重试次数，显示错误消息');
                      setChatMessages(prev => {
                        const newMessages = [...prev];
                        const lastIndex = newMessages.length - 1;
                        if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
                          newMessages[lastIndex] = {
                            role: "assistant",
                            content: "抱歉，AI助手暂时无法生成有效回复，请稍后再试。"
                          };
                        }
                        return newMessages;
                      });
                      setIsStreaming(false);
                      window.retryCount = 0;
                    }
                  }
                })
                .catch(error => {
                  logger.error('重试查询AI助手失败:', sanitizeData(error));
                  setChatMessages(prev => {
                    const newMessages = [...prev];
                    const lastIndex = newMessages.length - 1;
                    if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
                      newMessages[lastIndex] = {
                        role: "assistant",
                        content: "抱歉，生成回复时出现问题，请重试。"
                      };
                    }
                    return newMessages;
                  });
                  setIsStreaming(false);
                });
            }, 5000);

            return; // 不继续处理
          }

          // 设置打字机文本内容
          logger.debug('设置打字机文本:', textOnly.substring(0, 50) + '...')
          setTypewriterText(textOnly)
          // 显示打字机效果
          logger.debug('启用打字机效果')
          setShowTypewriter(true)

          // 更新聊天记录，但保留"正在生成回复..."的占位符
          // 实际内容将由打字机效果显示
          setChatMessages(prev => {
            logger.debug('更新聊天记录，添加空助手消息')
            const newMessages = [...prev]
            // 找到最后一条助手消息并替换为空白内容
            // 这样可以保持消息气泡，但内容由打字机组件显示
            const lastIndex = newMessages.length - 1
            if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
              logger.debug('找到最后一条助手消息，设置为空内容')
              newMessages[lastIndex] = {
                role: "assistant",
                content: "" // 空内容，实际内容由打字机组件显示
              }
            } else {
              logger.debug('未找到助手消息，最后一条消息角色:', lastIndex >= 0 ? newMessages[lastIndex].role : '无消息')
            }
            return newMessages
          })
        } else {
          logger.error('API响应成功，但内容为空')
          // 设置一个默认消息
          setChatMessages(prev => {
            const newMessages = [...prev]
            // 找到最后一条助手消息并替换
            const lastIndex = newMessages.length - 1
            if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
              newMessages[lastIndex] = {
                role: "assistant",
                content: "抱歉，生成回复时出现问题，请重试。"
              }
            }
            return newMessages
          })

          // 结束流式响应状态
          setIsStreaming(false)
        }

        // 最后一次滚动到底部
        scrollToBottom()
      } catch (error: any) {
        logger.error('查询AI助手失败:', sanitizeData(error))
        setIsStreaming(false)

        // 设置错误消息
        setChatMessages(prev => {
          const newMessages = [...prev]
          // 找到最后一条助手消息并替换
          const lastIndex = newMessages.length - 1
          if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
            newMessages[lastIndex] = {
              role: "assistant",
              content: "抱歉，生成回复时出现问题，请重试。"
            }
          }
          return newMessages
        })

        toast({
          title: '查询失败',
          description: '无法获取AI助手回复，请稍后再试',
          variant: 'destructive'
        })
      }
    } catch (error: any) {
      logger.error('流式查询AI助手失败:', sanitizeData(error))
      setIsStreaming(false)

      // 获取详细错误信息
      let errorMessage = "抱歉，发生了错误，无法获取回复。请稍后再试。"

      if (error.message) {
        if (error.message.includes('未登录')) {
          errorMessage = "您需要登录后才能使用AI助手。请先登录或注册一个账号。"

          // 触发登录弹窗
          try {
            const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
            window.dispatchEvent(event);
          } catch (e) {
            logger.error("触发登录弹窗时出错:", sanitizeData(e));
          }
        } else {
          errorMessage = `错误: ${error.message}`
        }
      }

      // 更新聊天记录
      setChatMessages(prev => {
        const newMessages = [...prev]
        // 找到最后一条助手消息并替换
        const lastIndex = newMessages.length - 1
        if (lastIndex >= 0 && newMessages[lastIndex].role === "assistant") {
          newMessages[lastIndex] = {
            role: "assistant",
            content: errorMessage
          }
        }
        return newMessages
      })

      toast({
        title: '查询失败',
        description: '无法获取AI助手回复，请稍后再试',
        variant: 'destructive'
      })
    }
  }

  // 处理输入框回车事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    try {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault()
        void sendMessage() // 使用void操作符处理Promise
      }
    } catch (error) {
      console.error("处理键盘事件时出错:", error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">AI研究助手</h1>
          <p className="text-gray-600">选择适合您研究需求的智能助手，获取专业的研究支持和知识服务</p>
        </div>

        {!isLoggedIn ? (
          <div className="bg-white p-8 rounded-lg shadow-md max-w-md mx-auto mt-20">
            <div className="flex flex-col items-center text-center">
              <AlertTriangle className="h-16 w-16 text-yellow-500 mb-4" />
              <h2 className="text-2xl font-bold mb-4">需要登录</h2>
              <p className="text-gray-600 mb-6">
                您需要登录后才能使用AI研究助手。请先登录或注册一个账号。
              </p>
              <div className="flex gap-4">
                <Button
                  onClick={(e) => {
                    try {
                      e.preventDefault();
                      // 触发登录弹窗
                      const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
                      window.dispatchEvent(event);
                    } catch (error) {
                      console.error("触发登录弹窗时出错:", error);
                    }
                  }}
                  className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                >
                  登录
                </Button>
                <Button
                  variant="outline"
                  onClick={(e) => {
                    try {
                      e.preventDefault();
                      // 触发注册弹窗
                      const event = new CustomEvent('open-login-modal', { detail: { isRegister: true } });
                      window.dispatchEvent(event);
                    } catch (error) {
                      console.error("触发注册弹窗时出错:", error);
                    }
                  }}
                >
                  注册
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* 搜索 */}
            <div className="mb-8 flex flex-col md:flex-row gap-4">
              <div className="relative md:w-1/2">
                <input
                  type="text"
                  placeholder="搜索助手..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              </div>
            </div>

            {/* 加载状态 */}
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1e7a43] mx-auto mb-4"></div>
                <p className="text-gray-500">正在加载AI助手...</p>
              </div>
            ) : (
              <>
                {/* 助手卡片网格 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredAssistants.map((assistant) => (
                    <div
                      key={assistant.id}
                      className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={(e) => {
                        try {
                          e.preventDefault();
                          openAssistantModal(assistant);
                        } catch (error) {
                          console.error("打开助手对话窗口时出错:", error);
                        }
                      }}
                    >
                      <div className="flex items-start mb-4">
                        <div className="p-2 rounded-lg bg-gray-50 mr-4">
                          <MessageSquare className="h-5 w-5 text-[#1e7a43]" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold">{assistant.name}</h3>
                          <p className="text-sm text-gray-500 mt-1">{assistant.description}</p>
                        </div>
                      </div>
                      {/* 标签 */}
                      {assistant.tags && assistant.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {assistant.tags.map((tag, index) => (
                            <span
                              key={`assistant-tag-${assistant.id}-${index}`}
                              className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* 没有结果时显示 */}
                {filteredAssistants.length === 0 && (
                  <div className="text-center py-12">
                    <div className="mb-4">
                      <Search className="h-12 w-12 text-gray-300 mx-auto" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-700 mb-2">未找到匹配的助手</h3>
                    <p className="text-gray-500">请尝试使用其他关键词或清除筛选条件</p>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>

      {/* 助手对话模态窗口 - 只有在登录状态下才显示 */}
      {isLoggedIn && showAssistantModal && selectedAssistant && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-4xl h-[80vh] flex flex-col relative">
            {/* 模态窗口头部 */}
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-gray-50 mr-3">
                  <MessageSquare className="h-5 w-5 text-[#1e7a43]" />
                </div>
                <div>
                  <h2 className="text-xl font-bold">{selectedAssistant.name}</h2>
                  <p className="text-sm text-gray-500">{selectedAssistant.description}</p>
                </div>
              </div>
              <button
                onClick={(e) => {
                  try {
                    e.preventDefault();
                    closeAssistantModal();
                  } catch (error) {
                    console.error("关闭助手对话窗口时出错:", error);
                  }
                }}
                className="text-gray-500 hover:text-gray-700">
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* 聊天内容区域 */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {chatMessages.map((message, index) => (
                <div key={index} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                  {message.role === "user" ? (
                    // 用户消息
                    <div className="max-w-[80%] rounded-lg p-4 bg-[#1e7a43] text-white">
                      <div className="whitespace-pre-line">{message.content}</div>
                    </div>
                  ) : (
                    // AI助手消息
                    <div className="max-w-[80%] rounded-lg overflow-hidden">
                      {/* 如果是最后一条消息且正在流式响应，显示等待动画 */}
                      {index === chatMessages.length - 1 && showTypewriter ? (
                        /* 流式响应结束后，显示打字机效果 */
                        <div className="bg-white border border-[#1e7a43]/20 p-3 rounded-lg">
                          <div className="text-lg text-gray-800 whitespace-pre-line">
                            {console.log('渲染打字机组件，文本长度:', typewriterText.length)}
                            {typewriterText && (
                              <HtmlTypewriter
                                html={typewriterText}
                                className="text-lg text-gray-800 whitespace-pre-line"
                                speed={30}
                                onComplete={handleTypewriterComplete}
                              />
                            )}
                          </div>
                        </div>
                      ) : index === chatMessages.length - 1 && isStreaming ? (
                        <div className="bg-white border border-[#1e7a43]/20 p-3 rounded-lg">
                          <div className="text-lg text-gray-800 whitespace-pre-line">
                            {/* 显示等待动画 */}
                            <div className="flex items-center space-x-2">
                              <span>正在生成回复</span>
                              <div className="flex space-x-1">
                                <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"></div>
                                <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce [animation-delay:0.2s]"></div>
                                <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce [animation-delay:0.4s]"></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : message.content.includes("Thinking...") || message.content.includes("思考中") ? (
                        // 思考中的消息
                        <div className="bg-white border border-[#1e7a43]/20 p-3 rounded-lg">
                          <div className="flex flex-col space-y-2">
                            {isThinking && (
                              <div className="flex space-x-2">
                                <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"></div>
                                <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce [animation-delay:0.2s]"></div>
                                <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce [animation-delay:0.4s]"></div>
                              </div>
                            )}
                            <div className="text-lg text-gray-800 whitespace-pre-line">
                              {message.content}
                            </div>
                          </div>
                        </div>
                      ) : message.content.startsWith("你好！") || message.content.startsWith("您好！") ? (
                        // 欢迎消息
                        <div className="bg-white border border-[#1e7a43]/20 p-3 rounded-lg">
                          <div className="text-gray-800 text-lg whitespace-pre-line">{message.content}</div>
                        </div>
                      ) : (
                        // 正式回复，尝试分离思考过程和正式回复
                        <div>
                          {(() => {
                            try {
                              // 安全检查，确保message.content是字符串
                              if (!message.content || typeof message.content !== 'string') {
                                return (
                                  <div className="p-3 bg-white border border-[#1e7a43]/20 rounded-lg text-gray-800 text-lg whitespace-pre-line">
                                    {message.content || "无内容"}
                                  </div>
                                );
                              }

                              // 尝试识别思考过程和正式回复
                              // 方法1：查找明确的分隔标记
                              const hasThinkingHeader = message.content.includes("Thinking...") ||
                                                       message.content.includes("思考中") ||
                                                       message.content.includes("<details style=");

                              // 方法2：查找常见的回复开头模式
                              const responsePatterns = [
                                "你好！", "您好！", "根据您的问题", "关于您的问题", "对于您的问题",
                                "我找到了", "以下是", "首先，", "我理解您的问题", "感谢您的提问",
                                "我认为", "经过分析", "通过查询", "根据分析"
                              ];

                              // 查找可能的分隔点
                              let splitIndex = -1;
                              let formalResponse = "";
                              let thinkingProcess = "";

                              if (hasThinkingHeader) {
                                // 尝试找到思考过程后的第一个明确回复开头
                                for (const pattern of responsePatterns) {
                                  const patternIndex = message.content.indexOf(pattern);
                                  if (patternIndex > 50) { // 确保不是在开头就匹配到
                                    // 找到最前面的匹配
                                    if (splitIndex === -1 || patternIndex < splitIndex) {
                                      splitIndex = patternIndex;
                                    }
                                  }
                                }

                                // 如果找到分隔点
                                if (splitIndex !== -1) {
                                  thinkingProcess = message.content.substring(0, splitIndex).trim();
                                  formalResponse = message.content.substring(splitIndex).trim();
                                }
                              }

                              // 如果成功分离了思考过程和正式回复
                              if (thinkingProcess && formalResponse) {
                                return (
                                  <>
                                    <details className="bg-white border border-[#1e7a43]/20 rounded-t-lg">
                                      <summary className="px-4 py-2 cursor-pointer text-sm text-gray-600 hover:bg-gray-100 flex items-center font-medium">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                        </svg>
                                        思考过程
                                      </summary>
                                      <div className="p-4 bg-white text-gray-800 text-lg whitespace-pre-line border-t border-[#1e7a43]/20">
                                        {thinkingProcess}
                                      </div>
                                    </details>
                                    <div className="p-3 bg-white border border-[#1e7a43]/20 border-t-0 text-gray-800 text-lg whitespace-pre-line rounded-b-lg">
                                      {formalResponse}
                                    </div>
                                  </>
                                );
                              } else {
                                try {
                                  // 尝试使用更简单的方法：查找两个连续的换行符作为分隔
                                  const parts = message.content.split(/\n\n(?=你好|您好|根据|关于|对于|我找到了|以下是|首先|我理解|感谢|我认为|经过分析|通过查询)/);

                                  if (parts.length >= 2 && parts[0].length > 100) {
                                    // 如果第一部分足够长，可能是思考过程
                                    return (
                                      <>
                                        <details className="bg-white border border-[#1e7a43]/20 rounded-t-lg">
                                          <summary className="px-4 py-2 cursor-pointer text-sm text-gray-600 hover:bg-gray-100 flex items-center font-medium">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                            </svg>
                                            思考过程
                                          </summary>
                                          <div className="p-4 bg-white text-gray-800 text-lg whitespace-pre-line border-t border-[#1e7a43]/20">
                                            {parts[0]}
                                          </div>
                                        </details>
                                        <div className="p-3 bg-white border border-[#1e7a43]/20 border-t-0 text-gray-800 text-lg whitespace-pre-line rounded-b-lg">
                                          {parts.slice(1).join("\n\n")}
                                        </div>
                                      </>
                                    );
                                  }
                                } catch (splitError) {
                                  console.error("分割消息内容时出错:", splitError);
                                }

                                // 没有明确的分隔或处理出错，显示完整内容
                                return (
                                  <div className="p-3 bg-white border border-[#1e7a43]/20 rounded-lg text-gray-800 text-lg whitespace-pre-line">
                                    {message.content}
                                  </div>
                                );
                              }
                            } catch (error) {
                              console.error("处理消息内容时出错:", error);
                              // 出错时显示原始内容
                              return (
                                <div className="p-3 bg-white border border-[#1e7a43]/20 rounded-lg text-gray-800 text-lg whitespace-pre-line">
                                  {message.content || "消息内容处理出错"}
                                </div>
                              );
                            }
                          })()}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
              <div ref={chatEndRef} />
            </div>

            {/* 输入区域 */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center">
                <textarea
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder={`向${selectedAssistant.name}提问...`}
                  className="flex-1 border rounded-l-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent resize-none"
                  rows={1}
                  style={{ minHeight: "44px", maxHeight: "120px" }}
                  disabled={isStreaming || isThinking}
                />
                <Button
                  onClick={(e) => {
                    try {
                      e.preventDefault();
                      void sendMessage(); // 使用void操作符处理Promise
                    } catch (error) {
                      console.error("发送消息时出错:", error);
                    }
                  }}
                  className={`rounded-l-none h-[44px] ${isThinking || isStreaming ? 'bg-gray-400' : 'bg-[#f5a623] hover:bg-[#f5a623]/90'}`}
                  disabled={!chatInput.trim() || isThinking || isStreaming}
                >
                  {isThinking ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      <span>思考中</span>
                    </div>
                  ) : isStreaming ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      <span>生成中</span>
                    </div>
                  ) : (
                    <MessageSquare className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {isStreaming && (
                <div className="text-xs text-gray-500 mt-2">
                  AI正在生成回复，请稍候...
                </div>
              )}
              {conversationId && (
                <div className="mt-2 text-xs text-gray-500 text-right">
                  会话ID: {conversationId}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
