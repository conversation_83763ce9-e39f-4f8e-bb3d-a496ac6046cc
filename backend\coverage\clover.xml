<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1746448330779" clover="3.2.0">
  <project timestamp="1746448330780" name="All files">
    <metrics statements="1405" coveredstatements="501" conditionals="692" coveredconditionals="93" methods="126" coveredmethods="47" elements="2223" coveredelements="641" complexity="0" loc="1405" ncloc="1405" packages="6" files="38" classes="38"/>
    <package name="controllers">
      <metrics statements="1037" coveredstatements="189" conditionals="609" coveredconditionals="46" methods="74" coveredmethods="9"/>
      <file name="activity.controller.js" path="C:\Users\<USER>\Desktop\P\backend\src\controllers\activity.controller.js">
        <metrics statements="134" coveredstatements="14" conditionals="92" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="17" count="13" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="94" count="13" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="124" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="164" count="13" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="169" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="205" count="13" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="229" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="245" count="13" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="305" count="13" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="313" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="327" count="0" type="stmt"/>
        <line num="332" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="333" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="362" count="13" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="370" count="0" type="stmt"/>
        <line num="377" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="378" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="379" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="417" count="13" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="424" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="426" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="427" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="437" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="438" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="474" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="475" count="0" type="stmt"/>
        <line num="478" count="0" type="stmt"/>
        <line num="491" count="13" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="493" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="505" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="506" count="0" type="stmt"/>
        <line num="513" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="514" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="515" count="0" type="stmt"/>
        <line num="523" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="524" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="535" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="551" count="13" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="556" count="0" type="stmt"/>
        <line num="565" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="566" count="0" type="stmt"/>
        <line num="573" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="574" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="585" count="0" type="stmt"/>
      </file>
      <file name="ai-assistant.controller.js" path="C:\Users\<USER>\Desktop\P\backend\src\controllers\ai-assistant.controller.js">
        <metrics statements="131" coveredstatements="10" conditionals="75" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="15" count="13" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="82" count="13" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="140" count="13" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="169" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="226" count="13" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="246" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="288" count="13" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="293" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="308" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="355" count="13" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="360" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="370" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="380" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="381" count="0" type="stmt"/>
        <line num="384" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="385" count="0" type="stmt"/>
        <line num="388" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="389" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="401" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="402" count="0" type="stmt"/>
        <line num="406" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="407" count="0" type="stmt"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="stmt"/>
        <line num="418" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="419" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="446" count="13" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="452" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="453" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="467" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="468" count="0" type="stmt"/>
        <line num="475" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="476" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="497" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="530" count="13" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="533" count="0" type="stmt"/>
        <line num="536" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="537" count="0" type="stmt"/>
        <line num="544" count="0" type="stmt"/>
        <line num="551" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="552" count="0" type="stmt"/>
        <line num="559" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="560" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
      </file>
      <file name="comment.controller.js" path="C:\Users\<USER>\Desktop\P\backend\src\controllers\comment.controller.js">
        <metrics statements="61" coveredstatements="6" conditionals="30" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="51" count="13" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="93" count="13" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="154" count="13" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="222" count="13" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="235" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
      </file>
      <file name="file.controller.js" path="C:\Users\<USER>\Desktop\P\backend\src\controllers\file.controller.js">
        <metrics statements="230" coveredstatements="83" conditionals="155" coveredconditionals="36" methods="10" coveredmethods="5"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="11" count="13" type="stmt"/>
        <line num="18" count="13" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="149" count="13" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="153" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="154" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="160" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="161" count="0" type="stmt"/>
        <line num="165" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="166" count="0" type="stmt"/>
        <line num="170" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="171" count="0" type="stmt"/>
        <line num="177" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="178" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="238" count="13" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="243" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="244" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="249" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="250" count="0" type="stmt"/>
        <line num="257" count="1" type="stmt"/>
        <line num="260" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="261" count="0" type="stmt"/>
        <line num="264" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="265" count="0" type="stmt"/>
        <line num="268" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="269" count="1" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="281" count="0" type="stmt"/>
        <line num="285" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="286" count="0" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="298" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="299" count="0" type="stmt"/>
        <line num="302" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="303" count="0" type="stmt"/>
        <line num="311" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="312" count="0" type="stmt"/>
        <line num="316" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="317" count="0" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="343" count="1" type="stmt"/>
        <line num="345" count="1" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="376" count="13" type="stmt"/>
        <line num="377" count="2" type="stmt"/>
        <line num="378" count="2" type="stmt"/>
        <line num="381" count="2" type="stmt"/>
        <line num="401" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="402" count="1" type="stmt"/>
        <line num="409" count="1" type="stmt"/>
        <line num="412" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="413" count="0" type="stmt"/>
        <line num="416" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="417" count="1" type="stmt"/>
        <line num="420" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="422" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="423" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="434" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="435" count="0" type="stmt"/>
        <line num="440" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="441" count="0" type="stmt"/>
        <line num="447" count="1" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="465" count="13" type="stmt"/>
        <line num="466" count="2" type="stmt"/>
        <line num="467" count="2" type="stmt"/>
        <line num="470" count="2" type="stmt"/>
        <line num="480" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="481" count="1" type="stmt"/>
        <line num="488" count="1" type="stmt"/>
        <line num="491" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="492" count="0" type="stmt"/>
        <line num="495" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="496" count="1" type="stmt"/>
        <line num="499" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="501" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="502" count="0" type="stmt"/>
        <line num="506" count="0" type="stmt"/>
        <line num="513" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="514" count="0" type="stmt"/>
        <line num="519" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="520" count="0" type="stmt"/>
        <line num="527" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="528" count="0" type="stmt"/>
        <line num="535" count="1" type="stmt"/>
        <line num="536" count="1" type="stmt"/>
        <line num="539" count="1" type="stmt"/>
        <line num="540" count="1" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="555" count="13" type="stmt"/>
        <line num="556" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="558" count="0" type="stmt"/>
        <line num="561" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="562" count="0" type="stmt"/>
        <line num="569" count="0" type="stmt"/>
        <line num="582" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="583" count="0" type="stmt"/>
        <line num="590" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="591" count="0" type="stmt"/>
        <line num="598" count="0" type="stmt"/>
        <line num="606" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="608" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="625" count="0" type="stmt"/>
        <line num="638" count="13" type="stmt"/>
        <line num="639" count="1" type="stmt"/>
        <line num="640" count="1" type="stmt"/>
        <line num="643" count="1" type="stmt"/>
        <line num="652" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="653" count="0" type="stmt"/>
        <line num="660" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="661" count="1" type="stmt"/>
        <line num="668" count="0" type="stmt"/>
        <line num="671" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="672" count="0" type="stmt"/>
        <line num="675" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="676" count="0" type="stmt"/>
        <line num="679" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="680" count="0" type="stmt"/>
        <line num="684" count="0" type="stmt"/>
        <line num="692" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="693" count="0" type="stmt"/>
        <line num="697" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="698" count="0" type="stmt"/>
        <line num="705" count="0" type="stmt"/>
        <line num="708" count="0" type="stmt"/>
        <line num="713" count="0" type="stmt"/>
        <line num="723" count="0" type="stmt"/>
        <line num="736" count="13" type="stmt"/>
        <line num="737" count="0" type="stmt"/>
        <line num="738" count="0" type="stmt"/>
        <line num="741" count="0" type="stmt"/>
        <line num="750" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="751" count="0" type="stmt"/>
        <line num="758" count="0" type="stmt"/>
        <line num="761" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="762" count="0" type="stmt"/>
        <line num="765" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="766" count="0" type="stmt"/>
        <line num="769" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="770" count="0" type="stmt"/>
        <line num="774" count="0" type="stmt"/>
        <line num="782" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="783" count="0" type="stmt"/>
        <line num="787" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="788" count="0" type="stmt"/>
        <line num="795" count="0" type="stmt"/>
        <line num="798" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="799" count="0" type="stmt"/>
        <line num="803" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="804" count="0" type="stmt"/>
        <line num="811" count="0" type="stmt"/>
        <line num="813" count="0" type="stmt"/>
        <line num="818" count="0" type="stmt"/>
        <line num="830" count="13" type="stmt"/>
        <line num="831" count="0" type="stmt"/>
        <line num="833" count="0" type="stmt"/>
        <line num="842" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="843" count="0" type="stmt"/>
        <line num="847" count="0" type="stmt"/>
        <line num="850" count="0" type="stmt"/>
        <line num="855" count="0" type="stmt"/>
        <line num="857" count="0" type="stmt"/>
        <line num="866" count="13" type="stmt"/>
        <line num="867" count="0" type="stmt"/>
        <line num="869" count="0" type="stmt"/>
        <line num="870" count="0" type="stmt"/>
        <line num="877" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="878" count="0" type="stmt"/>
        <line num="882" count="0" type="stmt"/>
        <line num="883" count="0" type="stmt"/>
        <line num="885" count="0" type="stmt"/>
        <line num="896" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="897" count="0" type="stmt"/>
        <line num="900" count="0" type="stmt"/>
        <line num="903" count="0" type="stmt"/>
        <line num="919" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="920" count="0" type="stmt"/>
        <line num="923" count="0" type="stmt"/>
        <line num="928" count="0" type="stmt"/>
        <line num="931" count="0" type="stmt"/>
      </file>
      <file name="knowledge.controller.js" path="C:\Users\<USER>\Desktop\P\backend\src\controllers\knowledge.controller.js">
        <metrics statements="164" coveredstatements="11" conditionals="100" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="15" count="13" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="68" count="13" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="151" count="13" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="175" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="229" count="13" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="264" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="298" count="13" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="306" count="0" type="stmt"/>
        <line num="313" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="315" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="316" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="331" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="332" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="345" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="377" count="13" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="384" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="385" count="0" type="stmt"/>
        <line num="392" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="394" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="395" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="410" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="411" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="453" count="13" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="459" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="460" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="469" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="470" count="0" type="stmt"/>
        <line num="477" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="479" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="480" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="495" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="496" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="506" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="507" count="0" type="stmt"/>
        <line num="514" count="0" type="stmt"/>
        <line num="521" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="522" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="558" count="0" type="stmt"/>
        <line num="571" count="13" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="574" count="0" type="stmt"/>
        <line num="577" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="578" count="0" type="stmt"/>
        <line num="585" count="0" type="stmt"/>
        <line num="587" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="588" count="0" type="stmt"/>
        <line num="595" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="597" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="598" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="613" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="614" count="0" type="stmt"/>
        <line num="622" count="0" type="stmt"/>
        <line num="629" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="630" count="0" type="stmt"/>
        <line num="637" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="638" count="0" type="stmt"/>
        <line num="645" count="0" type="stmt"/>
        <line num="651" count="0" type="stmt"/>
        <line num="666" count="0" type="stmt"/>
        <line num="672" count="0" type="stmt"/>
        <line num="685" count="13" type="stmt"/>
        <line num="686" count="0" type="stmt"/>
        <line num="687" count="0" type="stmt"/>
        <line num="690" count="0" type="stmt"/>
        <line num="692" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="693" count="0" type="stmt"/>
        <line num="700" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="702" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="703" count="0" type="stmt"/>
        <line num="710" count="0" type="stmt"/>
        <line num="718" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="719" count="0" type="stmt"/>
        <line num="727" count="0" type="stmt"/>
        <line num="734" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="735" count="0" type="stmt"/>
        <line num="742" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="743" count="0" type="stmt"/>
        <line num="750" count="0" type="stmt"/>
        <line num="752" count="0" type="stmt"/>
        <line num="757" count="0" type="stmt"/>
      </file>
      <file name="notification.controller.js" path="C:\Users\<USER>\Desktop\P\backend\src\controllers\notification.controller.js">
        <metrics statements="52" coveredstatements="34" conditionals="12" coveredconditionals="10" methods="6" coveredmethods="4"/>
        <line num="1" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="10" count="2" type="stmt"/>
        <line num="11" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="12" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="17" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="20" count="2" type="stmt"/>
        <line num="28" count="2" type="stmt"/>
        <line num="35" count="2" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="57" count="13" type="stmt"/>
        <line num="58" count="2" type="stmt"/>
        <line num="59" count="2" type="stmt"/>
        <line num="62" count="2" type="stmt"/>
        <line num="69" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="70" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="94" count="13" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="121" count="13" type="stmt"/>
        <line num="122" count="3" type="stmt"/>
        <line num="123" count="3" type="stmt"/>
        <line num="126" count="3" type="stmt"/>
        <line num="133" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="134" count="2" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="154" count="13" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="185" count="13" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
      </file>
      <file name="permission.controller.js" path="C:\Users\<USER>\Desktop\P\backend\src\controllers\permission.controller.js">
        <metrics statements="27" coveredstatements="5" conditionals="6" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="15" count="13" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="60" count="13" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="92" count="13" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
      </file>
      <file name="role.controller.js" path="C:\Users\<USER>\Desktop\P\backend\src\controllers\role.controller.js">
        <metrics statements="100" coveredstatements="9" conditionals="47" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="15" count="13" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="74" count="13" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="123" count="13" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="156" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="206" count="13" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="223" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="231" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="280" count="13" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="288" count="0" type="stmt"/>
        <line num="295" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="296" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="308" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="335" count="13" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="343" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="372" count="13" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="377" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="378" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="387" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="388" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="403" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="404" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
      </file>
      <file name="system.controller.js" path="C:\Users\<USER>\Desktop\P\backend\src\controllers\system.controller.js">
        <metrics statements="34" coveredstatements="5" conditionals="32" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="15" count="13" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="60" count="13" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="139" count="13" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
      </file>
      <file name="userController.js" path="C:\Users\<USER>\Desktop\P\backend\src\controllers\userController.js">
        <metrics statements="104" coveredstatements="12" conditionals="60" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="16" count="13" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="84" count="13" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="168" count="13" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="206" count="13" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="213" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="250" count="13" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="257" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="266" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="296" count="13" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="300" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="336" count="13" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="339" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="340" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="357" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="358" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="382" count="13" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="385" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="386" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="396" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="397" count="0" type="stmt"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="405" count="0" type="stmt"/>
        <line num="406" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="407" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="454" count="13" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="457" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="458" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="466" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="467" count="0" type="stmt"/>
        <line num="474" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="475" count="0" type="stmt"/>
        <line num="481" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
      </file>
    </package>
    <package name="middlewares">
      <metrics statements="36" coveredstatements="31" conditionals="26" coveredconditionals="18" methods="4" coveredmethods="3"/>
      <file name="authMiddleware.js" path="C:\Users\<USER>\Desktop\P\backend\src\middlewares\authMiddleware.js">
        <metrics statements="33" coveredstatements="28" conditionals="24" coveredconditionals="16" methods="4" coveredmethods="3"/>
        <line num="7" count="15" type="stmt"/>
        <line num="8" count="15" type="stmt"/>
        <line num="16" count="15" type="stmt"/>
        <line num="17" count="45" type="stmt"/>
        <line num="19" count="45" type="stmt"/>
        <line num="21" count="45" type="cond" truecount="4" falsecount="0"/>
        <line num="22" count="7" type="stmt"/>
        <line num="29" count="38" type="stmt"/>
        <line num="31" count="38" type="cond" truecount="1" falsecount="1"/>
        <line num="32" count="0" type="stmt"/>
        <line num="39" count="38" type="stmt"/>
        <line num="42" count="35" type="stmt"/>
        <line num="59" count="35" type="cond" truecount="2" falsecount="0"/>
        <line num="60" count="1" type="stmt"/>
        <line num="67" count="34" type="cond" truecount="2" falsecount="0"/>
        <line num="68" count="1" type="stmt"/>
        <line num="75" count="33" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="1" falsecount="1"/>
        <line num="84" count="33" type="stmt"/>
        <line num="86" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="87" count="2" type="stmt"/>
        <line num="93" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="94" count="1" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="113" count="15" type="stmt"/>
        <line num="114" count="325" type="stmt"/>
        <line num="116" count="17" type="cond" truecount="1" falsecount="1"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="17" type="cond" truecount="1" falsecount="1"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="17" type="stmt"/>
        <line num="132" count="15" type="stmt"/>
        <line num="133" count="15" type="stmt"/>
      </file>
      <file name="sessionMiddleware.js" path="C:\Users\<USER>\Desktop\P\backend\src\middlewares\sessionMiddleware.js">
        <metrics statements="3" coveredstatements="3" conditionals="2" coveredconditionals="2" methods="0" coveredmethods="0"/>
        <line num="10" count="13" type="stmt"/>
        <line num="13" count="13" type="stmt"/>
        <line num="23" count="13" type="stmt"/>
      </file>
    </package>
    <package name="models">
      <metrics statements="99" coveredstatements="98" conditionals="13" coveredconditionals="10" methods="29" coveredmethods="29"/>
      <file name="activity-attachment.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\activity-attachment.model.js">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="70" count="27" type="stmt"/>
        <line num="72" count="27" type="stmt"/>
        <line num="78" count="27" type="stmt"/>
        <line num="84" count="27" type="stmt"/>
      </file>
      <file name="activity.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\activity.model.js">
        <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="64" count="27" type="stmt"/>
        <line num="66" count="27" type="stmt"/>
        <line num="72" count="27" type="stmt"/>
        <line num="78" count="27" type="stmt"/>
        <line num="84" count="27" type="stmt"/>
      </file>
      <file name="ai-assistant.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\ai-assistant.model.js">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="105" count="27" type="stmt"/>
        <line num="107" count="27" type="stmt"/>
        <line num="113" count="27" type="stmt"/>
        <line num="119" count="27" type="stmt"/>
      </file>
      <file name="comment.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\comment.model.js">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="78" count="27" type="stmt"/>
        <line num="80" count="27" type="stmt"/>
        <line num="86" count="27" type="stmt"/>
        <line num="92" count="27" type="stmt"/>
      </file>
      <file name="file.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\file.model.js">
        <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="105" count="27" type="stmt"/>
        <line num="107" count="27" type="stmt"/>
        <line num="113" count="27" type="stmt"/>
        <line num="119" count="27" type="stmt"/>
        <line num="125" count="27" type="stmt"/>
      </file>
      <file name="index.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\index.js">
        <metrics statements="20" coveredstatements="19" conditionals="9" coveredconditionals="7" methods="3" coveredmethods="3"/>
        <line num="7" count="27" type="stmt"/>
        <line num="8" count="27" type="stmt"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="11" count="27" type="cond" truecount="1" falsecount="1"/>
        <line num="12" count="27" type="stmt"/>
        <line num="13" count="27" type="stmt"/>
        <line num="17" count="27" type="cond" truecount="1" falsecount="1"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="27" type="stmt"/>
        <line num="24" count="27" type="stmt"/>
        <line num="27" count="351" type="cond" truecount="3" falsecount="0"/>
        <line num="30" count="324" type="stmt"/>
        <line num="31" count="324" type="stmt"/>
        <line num="35" count="27" type="stmt"/>
        <line num="36" count="324" type="cond" truecount="2" falsecount="0"/>
        <line num="37" count="297" type="stmt"/>
        <line num="42" count="27" type="stmt"/>
        <line num="43" count="27" type="stmt"/>
        <line num="45" count="27" type="stmt"/>
      </file>
      <file name="knowledge-base-access.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\knowledge-base-access.model.js">
        <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="8" count="27" type="stmt"/>
        <line num="9" count="27" type="stmt"/>
        <line num="61" count="27" type="stmt"/>
        <line num="62" count="27" type="stmt"/>
        <line num="67" count="27" type="stmt"/>
        <line num="72" count="27" type="stmt"/>
        <line num="78" count="27" type="stmt"/>
      </file>
      <file name="knowledge-base.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\knowledge-base.model.js">
        <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="67" count="27" type="stmt"/>
        <line num="69" count="27" type="stmt"/>
        <line num="75" count="27" type="stmt"/>
        <line num="81" count="27" type="stmt"/>
        <line num="89" count="27" type="stmt"/>
      </file>
      <file name="notification.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\notification.model.js">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="67" count="27" type="stmt"/>
        <line num="69" count="27" type="stmt"/>
        <line num="75" count="27" type="stmt"/>
      </file>
      <file name="permission.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\permission.model.js">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="48" count="27" type="stmt"/>
        <line num="50" count="27" type="stmt"/>
        <line num="58" count="27" type="stmt"/>
      </file>
      <file name="role.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\role.model.js">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="39" count="27" type="stmt"/>
        <line num="41" count="27" type="stmt"/>
        <line num="47" count="27" type="stmt"/>
        <line num="55" count="27" type="stmt"/>
      </file>
      <file name="system-config.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\system-config.model.js">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="9" count="27" type="stmt"/>
        <line num="10" count="27" type="stmt"/>
        <line num="103" count="27" type="stmt"/>
      </file>
      <file name="user.model.js" path="C:\Users\<USER>\Desktop\P\backend\src\models\user.model.js">
        <metrics statements="14" coveredstatements="14" conditionals="4" coveredconditionals="3" methods="5" coveredmethods="5"/>
        <line num="9" count="27" type="stmt"/>
        <line num="11" count="27" type="stmt"/>
        <line num="12" count="27" type="stmt"/>
        <line num="76" count="25" type="cond" truecount="1" falsecount="1"/>
        <line num="77" count="25" type="stmt"/>
        <line num="78" count="25" type="stmt"/>
        <line num="82" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="83" count="2" type="stmt"/>
        <line num="84" count="2" type="stmt"/>
        <line num="91" count="27" type="stmt"/>
        <line num="92" count="2" type="stmt"/>
        <line num="96" count="27" type="stmt"/>
        <line num="98" count="27" type="stmt"/>
        <line num="106" count="27" type="stmt"/>
      </file>
    </package>
    <package name="routes">
      <metrics statements="162" coveredstatements="138" conditionals="14" coveredconditionals="1" methods="6" coveredmethods="0"/>
      <file name="activity.routes.js" path="C:\Users\<USER>\Desktop\P\backend\src\routes\activity.routes.js">
        <metrics statements="34" coveredstatements="22" conditionals="6" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="11" count="13" type="stmt"/>
        <line num="12" count="13" type="stmt"/>
        <line num="13" count="13" type="stmt"/>
        <line num="14" count="13" type="stmt"/>
        <line num="17" count="13" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="13" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="13" type="stmt"/>
        <line num="63" count="13" type="stmt"/>
        <line num="68" count="13" type="stmt"/>
        <line num="73" count="13" type="stmt"/>
        <line num="80" count="13" type="stmt"/>
        <line num="88" count="13" type="stmt"/>
        <line num="95" count="13" type="stmt"/>
        <line num="102" count="13" type="stmt"/>
        <line num="107" count="13" type="stmt"/>
        <line num="115" count="13" type="stmt"/>
        <line num="120" count="13" type="stmt"/>
        <line num="126" count="13" type="stmt"/>
      </file>
      <file name="ai-assistant.routes.js" path="C:\Users\<USER>\Desktop\P\backend\src\routes\ai-assistant.routes.js">
        <metrics statements="14" coveredstatements="14" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="11" count="13" type="stmt"/>
        <line num="14" count="13" type="stmt"/>
        <line num="20" count="13" type="stmt"/>
        <line num="26" count="13" type="stmt"/>
        <line num="33" count="13" type="stmt"/>
        <line num="40" count="13" type="stmt"/>
        <line num="47" count="13" type="stmt"/>
        <line num="54" count="13" type="stmt"/>
        <line num="60" count="13" type="stmt"/>
        <line num="65" count="13" type="stmt"/>
      </file>
      <file name="comment.routes.js" path="C:\Users\<USER>\Desktop\P\backend\src\routes\comment.routes.js">
        <metrics statements="11" coveredstatements="11" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="11" count="13" type="stmt"/>
        <line num="14" count="13" type="stmt"/>
        <line num="18" count="13" type="stmt"/>
        <line num="25" count="13" type="stmt"/>
        <line num="32" count="13" type="stmt"/>
        <line num="40" count="13" type="stmt"/>
        <line num="45" count="13" type="stmt"/>
      </file>
      <file name="file.routes.js" path="C:\Users\<USER>\Desktop\P\backend\src\routes\file.routes.js">
        <metrics statements="32" coveredstatements="20" conditionals="8" coveredconditionals="1" methods="3" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="11" count="13" type="stmt"/>
        <line num="12" count="13" type="stmt"/>
        <line num="13" count="13" type="stmt"/>
        <line num="14" count="13" type="stmt"/>
        <line num="17" count="13" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="13" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="62" count="13" type="stmt"/>
        <line num="71" count="13" type="stmt"/>
        <line num="78" count="13" type="stmt"/>
        <line num="84" count="13" type="stmt"/>
        <line num="90" count="13" type="stmt"/>
        <line num="96" count="13" type="stmt"/>
        <line num="102" count="13" type="stmt"/>
        <line num="109" count="13" type="stmt"/>
        <line num="115" count="13" type="stmt"/>
        <line num="120" count="13" type="stmt"/>
      </file>
      <file name="knowledge.routes.js" path="C:\Users\<USER>\Desktop\P\backend\src\routes\knowledge.routes.js">
        <metrics statements="15" coveredstatements="15" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="11" count="13" type="stmt"/>
        <line num="14" count="13" type="stmt"/>
        <line num="20" count="13" type="stmt"/>
        <line num="26" count="13" type="stmt"/>
        <line num="32" count="13" type="stmt"/>
        <line num="38" count="13" type="stmt"/>
        <line num="44" count="13" type="stmt"/>
        <line num="50" count="13" type="stmt"/>
        <line num="56" count="13" type="stmt"/>
        <line num="62" count="13" type="stmt"/>
        <line num="67" count="13" type="stmt"/>
      </file>
      <file name="notification.routes.js" path="C:\Users\<USER>\Desktop\P\backend\src\routes\notification.routes.js">
        <metrics statements="11" coveredstatements="11" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="13" count="13" type="stmt"/>
        <line num="19" count="13" type="stmt"/>
        <line num="25" count="13" type="stmt"/>
        <line num="31" count="13" type="stmt"/>
        <line num="37" count="13" type="stmt"/>
        <line num="43" count="13" type="stmt"/>
        <line num="48" count="13" type="stmt"/>
      </file>
      <file name="permission.routes.js" path="C:\Users\<USER>\Desktop\P\backend\src\routes\permission.routes.js">
        <metrics statements="9" coveredstatements="9" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="11" count="13" type="stmt"/>
        <line num="14" count="13" type="stmt"/>
        <line num="21" count="13" type="stmt"/>
        <line num="28" count="13" type="stmt"/>
        <line num="34" count="13" type="stmt"/>
      </file>
      <file name="role.routes.js" path="C:\Users\<USER>\Desktop\P\backend\src\routes\role.routes.js">
        <metrics statements="13" coveredstatements="13" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="11" count="13" type="stmt"/>
        <line num="14" count="13" type="stmt"/>
        <line num="21" count="13" type="stmt"/>
        <line num="28" count="13" type="stmt"/>
        <line num="35" count="13" type="stmt"/>
        <line num="42" count="13" type="stmt"/>
        <line num="49" count="13" type="stmt"/>
        <line num="56" count="13" type="stmt"/>
        <line num="62" count="13" type="stmt"/>
      </file>
      <file name="system.routes.js" path="C:\Users\<USER>\Desktop\P\backend\src\routes\system.routes.js">
        <metrics statements="9" coveredstatements="9" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="11" count="13" type="stmt"/>
        <line num="14" count="13" type="stmt"/>
        <line num="21" count="13" type="stmt"/>
        <line num="28" count="13" type="stmt"/>
        <line num="34" count="13" type="stmt"/>
      </file>
      <file name="userRoutes.js" path="C:\Users\<USER>\Desktop\P\backend\src\routes\userRoutes.js">
        <metrics statements="14" coveredstatements="14" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="13" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="13" count="13" type="stmt"/>
        <line num="16" count="13" type="stmt"/>
        <line num="19" count="13" type="stmt"/>
        <line num="22" count="13" type="stmt"/>
        <line num="25" count="13" type="stmt"/>
        <line num="28" count="13" type="stmt"/>
        <line num="31" count="13" type="stmt"/>
        <line num="34" count="13" type="stmt"/>
        <line num="37" count="13" type="stmt"/>
        <line num="39" count="13" type="stmt"/>
      </file>
    </package>
    <package name="services.api">
      <metrics statements="10" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="notification.service.js" path="C:\Users\<USER>\Desktop\P\backend\src\services\api\notification.service.js">
        <metrics statements="10" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="61" coveredstatements="45" conditionals="29" coveredconditionals="18" methods="9" coveredmethods="6"/>
      <file name="file-access-utils.js" path="C:\Users\<USER>\Desktop\P\backend\src\utils\file-access-utils.js">
        <metrics statements="16" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
      </file>
      <file name="helpers.js" path="C:\Users\<USER>\Desktop\P\backend\src\utils\helpers.js">
        <metrics statements="45" coveredstatements="45" conditionals="19" coveredconditionals="18" methods="6" coveredmethods="6"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="3" type="cond" truecount="5" falsecount="0"/>
        <line num="14" count="2" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="34" count="1" type="stmt"/>
        <line num="37" count="3" type="stmt"/>
        <line num="38" count="3" type="stmt"/>
        <line num="40" count="3" type="stmt"/>
        <line num="41" count="30" type="stmt"/>
        <line num="42" count="30" type="stmt"/>
        <line num="45" count="3" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="55" count="3" type="stmt"/>
        <line num="58" count="8" type="stmt"/>
        <line num="59" count="8" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="69" count="3" type="stmt"/>
        <line num="72" count="8" type="stmt"/>
        <line num="73" count="8" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="83" count="3" type="stmt"/>
        <line num="87" count="2" type="stmt"/>
        <line num="97" count="2" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="109" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="110" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="111" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="114" count="5" type="stmt"/>
        <line num="115" count="5" type="stmt"/>
        <line num="118" count="5" type="stmt"/>
        <line num="119" count="5" type="stmt"/>
        <line num="122" count="5" type="stmt"/>
        <line num="124" count="5" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
