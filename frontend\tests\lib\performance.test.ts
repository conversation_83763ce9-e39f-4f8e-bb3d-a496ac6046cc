/**
 * 性能优化工具测试
 */

import {
  debounce,
  throttle,
  memoize,
  batchProcess,
  isInViewport
} from '@/lib/performance'

// 模拟定时器
jest.useFakeTimers()

describe('性能优化工具', () => {
  // 测试防抖函数
  describe('debounce', () => {
    test('应该延迟执行函数', () => {
      const mockFn = jest.fn()
      const debouncedFn = debounce(mockFn, 1000)
      
      debouncedFn()
      expect(mockFn).not.toHaveBeenCalled()
      
      jest.advanceTimersByTime(500)
      expect(mockFn).not.toHaveBeenCalled()
      
      jest.advanceTimersByTime(500)
      expect(mockFn).toHaveBeenCalledTimes(1)
    })
    
    test('应该在多次调用时重新计时', () => {
      const mockFn = jest.fn()
      const debouncedFn = debounce(mockFn, 1000)
      
      debouncedFn()
      jest.advanceTimersByTime(500)
      
      debouncedFn()
      jest.advanceTimersByTime(500)
      expect(mockFn).not.toHaveBeenCalled()
      
      jest.advanceTimersByTime(500)
      expect(mockFn).toHaveBeenCalledTimes(1)
    })
    
    test('应该传递参数和上下文', () => {
      const mockFn = jest.fn()
      const debouncedFn = debounce(mockFn, 1000)
      const context = { name: 'test' }
      
      function callWithContext(this: any, arg: string) {
        debouncedFn.call(this, arg)
      }
      
      callWithContext.call(context, 'arg')
      jest.advanceTimersByTime(1000)
      
      expect(mockFn).toHaveBeenCalledWith('arg')
      // 注意：Jest中无法测试this上下文
    })
  })
  
  // 测试节流函数
  describe('throttle', () => {
    test('应该立即执行第一次调用', () => {
      const mockFn = jest.fn()
      const throttledFn = throttle(mockFn, 1000)
      
      throttledFn()
      expect(mockFn).toHaveBeenCalledTimes(1)
    })
    
    test('应该限制执行频率', () => {
      const mockFn = jest.fn()
      const throttledFn = throttle(mockFn, 1000)
      
      throttledFn()
      throttledFn()
      throttledFn()
      
      expect(mockFn).toHaveBeenCalledTimes(1)
      
      jest.advanceTimersByTime(1000)
      expect(mockFn).toHaveBeenCalledTimes(2)
    })
    
    test('应该在限制时间后执行最后一次调用', () => {
      const mockFn = jest.fn()
      const throttledFn = throttle(mockFn, 1000)
      
      throttledFn('first')
      throttledFn('second')
      throttledFn('third')
      
      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenLastCalledWith('first')
      
      jest.advanceTimersByTime(1000)
      expect(mockFn).toHaveBeenCalledTimes(2)
      expect(mockFn).toHaveBeenLastCalledWith('third')
    })
  })
  
  // 测试缓存函数
  describe('memoize', () => {
    test('应该缓存函数结果', () => {
      const mockFn = jest.fn((a, b) => a + b)
      const memoizedFn = memoize(mockFn)
      
      expect(memoizedFn(1, 2)).toBe(3)
      expect(memoizedFn(1, 2)).toBe(3)
      expect(memoizedFn(1, 2)).toBe(3)
      
      expect(mockFn).toHaveBeenCalledTimes(1)
    })
    
    test('应该为不同的参数缓存不同的结果', () => {
      const mockFn = jest.fn((a, b) => a + b)
      const memoizedFn = memoize(mockFn)
      
      expect(memoizedFn(1, 2)).toBe(3)
      expect(memoizedFn(2, 3)).toBe(5)
      expect(memoizedFn(1, 2)).toBe(3)
      
      expect(mockFn).toHaveBeenCalledTimes(2)
    })
    
    test('应该使用JSON.stringify区分参数', () => {
      const mockFn = jest.fn((obj) => obj.value)
      const memoizedFn = memoize(mockFn)
      
      const obj1 = { value: 1 }
      const obj2 = { value: 1 }
      const obj3 = { value: 2 }
      
      expect(memoizedFn(obj1)).toBe(1)
      expect(memoizedFn(obj2)).toBe(1)
      expect(memoizedFn(obj3)).toBe(2)
      
      expect(mockFn).toHaveBeenCalledTimes(2)
    })
  })
  
  // 测试批量处理函数
  describe('batchProcess', () => {
    test('应该分批处理任务', () => {
      const mockProcessor = jest.fn()
      const tasks = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
      
      batchProcess(tasks, mockProcessor, 5, 100)
      
      // 第一批处理
      expect(mockProcessor).toHaveBeenCalledTimes(5)
      expect(mockProcessor).toHaveBeenNthCalledWith(1, 1)
      expect(mockProcessor).toHaveBeenNthCalledWith(5, 5)
      
      // 第二批处理
      jest.advanceTimersByTime(100)
      expect(mockProcessor).toHaveBeenCalledTimes(10)
      expect(mockProcessor).toHaveBeenNthCalledWith(6, 6)
      expect(mockProcessor).toHaveBeenNthCalledWith(10, 10)
      
      // 第三批处理
      jest.advanceTimersByTime(100)
      expect(mockProcessor).toHaveBeenCalledTimes(12)
      expect(mockProcessor).toHaveBeenNthCalledWith(11, 11)
      expect(mockProcessor).toHaveBeenNthCalledWith(12, 12)
    })
    
    test('应该处理空任务数组', () => {
      const mockProcessor = jest.fn()
      const tasks: number[] = []
      
      batchProcess(tasks, mockProcessor)
      
      expect(mockProcessor).not.toHaveBeenCalled()
    })
    
    test('应该处理小于批量大小的任务数组', () => {
      const mockProcessor = jest.fn()
      const tasks = [1, 2, 3]
      
      batchProcess(tasks, mockProcessor, 5)
      
      expect(mockProcessor).toHaveBeenCalledTimes(3)
      expect(mockProcessor).toHaveBeenNthCalledWith(1, 1)
      expect(mockProcessor).toHaveBeenNthCalledWith(2, 2)
      expect(mockProcessor).toHaveBeenNthCalledWith(3, 3)
    })
  })
  
  // 测试视口检测函数
  describe('isInViewport', () => {
    test('应该检测元素是否在视口内', () => {
      // 模拟DOM元素
      const element = {
        getBoundingClientRect: () => ({
          top: 100,
          left: 100,
          bottom: 200,
          right: 200
        })
      } as HTMLElement
      
      // 模拟窗口尺寸
      Object.defineProperty(window, 'innerHeight', { value: 800 })
      Object.defineProperty(window, 'innerWidth', { value: 1200 })
      
      expect(isInViewport(element)).toBe(true)
    })
    
    test('应该检测元素是否不在视口内', () => {
      // 模拟DOM元素（在视口外）
      const element = {
        getBoundingClientRect: () => ({
          top: -300,
          left: 100,
          bottom: -200,
          right: 200
        })
      } as HTMLElement
      
      // 模拟窗口尺寸
      Object.defineProperty(window, 'innerHeight', { value: 800 })
      Object.defineProperty(window, 'innerWidth', { value: 1200 })
      
      expect(isInViewport(element)).toBe(false)
    })
    
    test('应该支持偏移量', () => {
      // 模拟DOM元素（在视口外，但在偏移范围内）
      const element = {
        getBoundingClientRect: () => ({
          top: -100,
          left: 100,
          bottom: 0,
          right: 200
        })
      } as HTMLElement
      
      // 模拟窗口尺寸
      Object.defineProperty(window, 'innerHeight', { value: 800 })
      Object.defineProperty(window, 'innerWidth', { value: 1200 })
      
      expect(isInViewport(element, 0)).toBe(false)
      expect(isInViewport(element, 100)).toBe(true)
    })
  })
})
