/**
 * 知识库相关API
 * 
 * 处理知识库的创建、查询、更新、删除等请求
 */

import request from '../index';

/**
 * 获取知识库列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.search - 搜索关键词
 * @param {string} params.type - 知识库类型
 * @returns {Promise} 知识库列表
 */
export function getKnowledgeBaseList(params) {
  return request({
    url: '/knowledge',
    method: 'get',
    params
  });
}

/**
 * 获取我的知识库列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.search - 搜索关键词
 * @returns {Promise} 知识库列表
 */
export function getMyKnowledgeBaseList(params) {
  return request({
    url: '/knowledge/my',
    method: 'get',
    params
  });
}

/**
 * 获取知识库详情
 * @param {string} id - 知识库ID
 * @returns {Promise} 知识库详情
 */
export function getKnowledgeBaseById(id) {
  return request({
    url: `/knowledge/${id}`,
    method: 'get'
  });
}

/**
 * 创建知识库
 * @param {Object} data - 知识库信息
 * @param {string} data.name - 知识库名称
 * @param {string} data.description - 知识库描述
 * @param {string} data.type - 知识库类型
 * @returns {Promise} 创建结果
 */
export function createKnowledgeBase(data) {
  return request({
    url: '/knowledge',
    method: 'post',
    data
  });
}

/**
 * 更新知识库
 * @param {string} id - 知识库ID
 * @param {Object} data - 知识库信息
 * @returns {Promise} 更新结果
 */
export function updateKnowledgeBase(id, data) {
  return request({
    url: `/knowledge/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除知识库
 * @param {string} id - 知识库ID
 * @returns {Promise} 删除结果
 */
export function deleteKnowledgeBase(id) {
  return request({
    url: `/knowledge/${id}`,
    method: 'delete'
  });
}

/**
 * 获取知识库访问权限列表
 * @param {string} id - 知识库ID
 * @returns {Promise} 访问权限列表
 */
export function getKnowledgeBaseAccess(id) {
  return request({
    url: `/knowledge/${id}/access`,
    method: 'get'
  });
}

/**
 * 添加知识库访问权限
 * @param {string} id - 知识库ID
 * @param {Object} data - 访问权限信息
 * @param {string} data.userId - 用户ID
 * @param {string} data.accessType - 访问类型
 * @returns {Promise} 添加结果
 */
export function addKnowledgeBaseAccess(id, data) {
  return request({
    url: `/knowledge/${id}/access`,
    method: 'post',
    data
  });
}

/**
 * 更新知识库访问权限
 * @param {string} id - 知识库ID
 * @param {string} accessId - 访问权限ID
 * @param {Object} data - 访问权限信息
 * @param {string} data.accessType - 访问类型
 * @returns {Promise} 更新结果
 */
export function updateKnowledgeBaseAccess(id, accessId, data) {
  return request({
    url: `/knowledge/${id}/access/${accessId}`,
    method: 'put',
    data
  });
}

/**
 * 删除知识库访问权限
 * @param {string} id - 知识库ID
 * @param {string} accessId - 访问权限ID
 * @returns {Promise} 删除结果
 */
export function deleteKnowledgeBaseAccess(id, accessId) {
  return request({
    url: `/knowledge/${id}/access/${accessId}`,
    method: 'delete'
  });
}

export default {
  getKnowledgeBaseList,
  getMyKnowledgeBaseList,
  getKnowledgeBaseById,
  createKnowledgeBase,
  updateKnowledgeBase,
  deleteKnowledgeBase,
  getKnowledgeBaseAccess,
  addKnowledgeBaseAccess,
  updateKnowledgeBaseAccess,
  deleteKnowledgeBaseAccess
};
