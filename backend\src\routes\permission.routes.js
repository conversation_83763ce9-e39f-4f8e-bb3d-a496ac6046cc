/**
 * 权限相关路由
 *
 * 处理权限的查询等请求
 */

const express = require('express');
const router = express.Router();
const permissionController = require('../controllers/permission.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');

// 获取权限列表 (需要认证和权限)
router.get('/',
  authMiddleware,
  (req, res, next) => {
    // 检查用户是否为管理员
    if (req.user && req.user.role === 'admin') {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: '权限不足，只有管理员可以访问'
      });
    }
  },
  permissionController.getPermissions
);

// 获取权限模块列表 (需要认证和权限)
router.get('/modules',
  authMiddleware,
  (req, res, next) => {
    // 检查用户是否为管理员
    if (req.user && req.user.role === 'admin') {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: '权限不足，只有管理员可以访问'
      });
    }
  },
  permissionController.getPermissionModules
);

// 获取权限详情 (需要认证和权限)
router.get('/:id',
  authMiddleware,
  (req, res, next) => {
    // 检查用户是否为管理员
    if (req.user && req.user.role === 'admin') {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: '权限不足，只有管理员可以访问'
      });
    }
  },
  permissionController.getPermissionById
);

// 创建权限 (需要认证和权限)
router.post('/',
  authMiddleware,
  (req, res, next) => {
    // 检查用户是否为管理员
    if (req.user && req.user.role === 'admin') {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: '权限不足，只有管理员可以访问'
      });
    }
  },
  permissionController.createPermission
);

// 更新权限 (需要认证和权限)
router.put('/:id',
  authMiddleware,
  (req, res, next) => {
    // 检查用户是否为管理员
    if (req.user && req.user.role === 'admin') {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: '权限不足，只有管理员可以访问'
      });
    }
  },
  permissionController.updatePermission
);

// 删除权限 (需要认证和权限)
router.delete('/:id',
  authMiddleware,
  (req, res, next) => {
    // 检查用户是否为管理员
    if (req.user && req.user.role === 'admin') {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: '权限不足，只有管理员可以访问'
      });
    }
  },
  permissionController.deletePermission
);

module.exports = router;
