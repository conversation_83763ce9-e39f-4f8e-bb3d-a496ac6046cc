/**
 * 创建通知表迁移文件
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('notifications', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: '通知接收者ID'
      },
      title: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      type: {
        type: Sequelize.ENUM('system', 'activity', 'file', 'comment'),
        allowNull: false,
        comment: '通知类型：系统通知、活动通知、文件通知、评论通知'
      },
      is_read: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: '是否已读'
      },
      read_time: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '阅读时间'
      },
      related_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '相关对象ID'
      },
      related_type: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '相关对象类型'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('notifications');
  }
};
