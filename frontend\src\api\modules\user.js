/**
 * 用户相关API
 * 
 * 处理用户登录、注册、获取用户信息等请求
 */

import request from '../index';

/**
 * 用户登录
 * @param {Object} data - 登录信息
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @returns {Promise} 登录结果
 */
export function login(data) {
  return request({
    url: '/users/login',
    method: 'post',
    data
  });
}

/**
 * 用户注册
 * @param {Object} data - 注册信息
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @param {string} data.email - 邮箱
 * @param {string} data.phone - 手机号
 * @returns {Promise} 注册结果
 */
export function register(data) {
  return request({
    url: '/users/register',
    method: 'post',
    data
  });
}

/**
 * 获取当前用户信息
 * @returns {Promise} 用户信息
 */
export function getUserInfo() {
  return request({
    url: '/users/info',
    method: 'get'
  });
}

/**
 * 更新用户信息
 * @param {Object} data - 用户信息
 * @returns {Promise} 更新结果
 */
export function updateUserInfo(data) {
  return request({
    url: '/users/info',
    method: 'put',
    data
  });
}

/**
 * 修改密码
 * @param {Object} data - 密码信息
 * @param {string} data.oldPassword - 旧密码
 * @param {string} data.newPassword - 新密码
 * @returns {Promise} 修改结果
 */
export function changePassword(data) {
  return request({
    url: '/users/change-password',
    method: 'post',
    data
  });
}

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.search - 搜索关键词
 * @param {string} params.role - 角色ID
 * @returns {Promise} 用户列表
 */
export function getUserList(params) {
  return request({
    url: '/users',
    method: 'get',
    params
  });
}

/**
 * 获取用户详情
 * @param {string} id - 用户ID
 * @returns {Promise} 用户详情
 */
export function getUserById(id) {
  return request({
    url: `/users/${id}`,
    method: 'get'
  });
}

/**
 * 创建用户
 * @param {Object} data - 用户信息
 * @returns {Promise} 创建结果
 */
export function createUser(data) {
  return request({
    url: '/users',
    method: 'post',
    data
  });
}

/**
 * 更新用户
 * @param {string} id - 用户ID
 * @param {Object} data - 用户信息
 * @returns {Promise} 更新结果
 */
export function updateUser(id, data) {
  return request({
    url: `/users/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除用户
 * @param {string} id - 用户ID
 * @returns {Promise} 删除结果
 */
export function deleteUser(id) {
  return request({
    url: `/users/${id}`,
    method: 'delete'
  });
}

/**
 * 重置用户密码
 * @param {string} id - 用户ID
 * @returns {Promise} 重置结果
 */
export function resetUserPassword(id) {
  return request({
    url: `/users/${id}/reset-password`,
    method: 'post'
  });
}

/**
 * 更新用户角色
 * @param {string} id - 用户ID
 * @param {Object} data - 角色信息
 * @param {string} data.roleId - 角色ID
 * @returns {Promise} 更新结果
 */
export function updateUserRole(id, data) {
  return request({
    url: `/users/${id}/role`,
    method: 'put',
    data
  });
}

export default {
  login,
  register,
  getUserInfo,
  updateUserInfo,
  changePassword,
  getUserList,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
  updateUserRole
};
