/**
 * 时间轴事件控制器
 *
 * 处理时间轴事件的创建、查询、更新、删除等请求
 */

const { TimelineEvent } = require('../models');

/**
 * 获取所有时间轴事件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTimelineEvents = async (req, res) => {
  try {
    const events = await TimelineEvent.findAll({
      order: [['year', 'ASC']]
    });

    res.status(200).json({
      success: true,
      data: events
    });
  } catch (error) {
    console.error('获取时间轴事件失败:', error);
    res.status(500).json({
      success: false,
      message: '获取时间轴事件失败',
      error: error.message
    });
  }
};

/**
 * 获取单个时间轴事件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTimelineEventById = async (req, res) => {
  try {
    const { id } = req.params;

    const event = await TimelineEvent.findByPk(id);

    if (!event) {
      return res.status(404).json({
        success: false,
        message: '未找到该时间轴事件'
      });
    }

    res.status(200).json({
      success: true,
      data: event
    });
  } catch (error) {
    console.error('获取时间轴事件详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取时间轴事件详情失败',
      error: error.message
    });
  }
};

/**
 * 创建时间轴事件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createTimelineEvent = async (req, res) => {
  try {
    const { year, title, description, content, level, icon } = req.body;

    // 验证必填字段
    if (!year || !title || !description || !content || !level) {
      return res.status(400).json({
        success: false,
        message: '请提供所有必填字段'
      });
    }

    // 验证level字段值是否有效
    const validLevels = ['national', 'family', 'personal'];
    if (!validLevels.includes(level)) {
      return res.status(400).json({
        success: false,
        message: '无效的事件级别，必须是 national、family 或 personal'
      });
    }

    const newEvent = await TimelineEvent.create({
      year,
      title,
      description,
      content,
      level,
      icon
    });

    res.status(201).json({
      success: true,
      data: newEvent
    });
  } catch (error) {
    console.error('创建时间轴事件失败:', error);
    res.status(500).json({
      success: false,
      message: '创建时间轴事件失败',
      error: error.message
    });
  }
};

/**
 * 更新时间轴事件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateTimelineEvent = async (req, res) => {
  try {
    const { id } = req.params;
    const { year, title, description, content, level, icon } = req.body;

    const event = await TimelineEvent.findByPk(id);

    if (!event) {
      return res.status(404).json({
        success: false,
        message: '未找到该时间轴事件'
      });
    }

    // 验证level字段值是否有效（如果提供了）
    if (level) {
      const validLevels = ['national', 'family', 'personal'];
      if (!validLevels.includes(level)) {
        return res.status(400).json({
          success: false,
          message: '无效的事件级别，必须是 national、family 或 personal'
        });
      }
    }

    // 更新事件
    await event.update({
      year: year || event.year,
      title: title || event.title,
      description: description || event.description,
      content: content || event.content,
      level: level || event.level,
      icon: icon !== undefined ? icon : event.icon
    });

    res.status(200).json({
      success: true,
      data: event
    });
  } catch (error) {
    console.error('更新时间轴事件失败:', error);
    res.status(500).json({
      success: false,
      message: '更新时间轴事件失败',
      error: error.message
    });
  }
};

/**
 * 删除时间轴事件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteTimelineEvent = async (req, res) => {
  try {
    const { id } = req.params;

    const event = await TimelineEvent.findByPk(id);

    if (!event) {
      return res.status(404).json({
        success: false,
        message: '未找到该时间轴事件'
      });
    }

    await event.destroy();

    res.status(200).json({
      success: true,
      message: '时间轴事件已成功删除',
      data: { id }
    });
  } catch (error) {
    console.error('删除时间轴事件失败:', error);
    res.status(500).json({
      success: false,
      message: '删除时间轴事件失败',
      error: error.message
    });
  }
};

/**
 * 上传时间轴事件图标
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.uploadTimelineEventIcon = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传图标文件'
      });
    }

    // 构建图标URL - 使用完整URL
    const iconUrl = `/public/uploads/timeline-icons/${req.file.filename}`;

    // 检查文件是否存在
    const fs = require('fs');
    const path = require('path');
    const filePath = path.join(__dirname, '../../public/uploads/timeline-icons', req.file.filename);

    const fileExists = fs.existsSync(filePath);

    console.log('上传图标成功:', {
      originalPath: req.file.path,
      filename: req.file.filename,
      url: iconUrl,
      absolutePath: filePath,
      fileExists: fileExists
    });

    res.status(200).json({
      success: true,
      data: {
        url: iconUrl
      }
    });
  } catch (error) {
    console.error('上传时间轴事件图标失败:', error);
    res.status(500).json({
      success: false,
      message: '上传时间轴事件图标失败',
      error: error.message
    });
  }
};
