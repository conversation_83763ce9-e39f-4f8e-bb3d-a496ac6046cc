"use client"

import { useState, useEffect, useRef } from "react"
import { Checkbox } from "@/components/ui/checkbox"

interface PermissionCheckboxProps {
  code: string;
  label: string;
  defaultChecked?: boolean;
}

/**
 * 权限复选框组件
 *
 * 用于系统管理页面的权限设置
 *
 * @param code 权限代码
 * @param label 权限标签
 * @param defaultChecked 是否默认选中
 */
export function PermissionCheckbox({ code, label, defaultChecked = false }: PermissionCheckboxProps) {
  const id = `permission-${code.replace(/:/g, '-')}`;
  const [checked, setChecked] = useState(defaultChecked);
  const checkboxRef = useRef<HTMLInputElement>(null);

  // 监听外部设置的选中状态
  useEffect(() => {
    // 创建一个事件监听器，用于接收权限选中状态的更新
    const handlePermissionUpdate = (event: CustomEvent) => {
      const { permissionCodes } = event.detail;
      if (Array.isArray(permissionCodes)) {
        const isChecked = permissionCodes.includes(code);
        setChecked(isChecked);

        // 同时更新DOM元素的checked属性，确保查询时能获取到正确的状态
        if (checkboxRef.current) {
          checkboxRef.current.checked = isChecked;
        }

        // 记录权限状态变更
        console.log(`权限复选框 ${code} 状态通过事件更新为: ${isChecked}`);

        // 更新全局权限复选框列表中的状态
        if (typeof window !== 'undefined' && window.permissionCheckboxes && window.permissionCheckboxes[code]) {
          window.permissionCheckboxes[code].isChecked = () => isChecked;
        }
      }
    };

    // 添加事件监听器
    window.addEventListener('permission-update' as any, handlePermissionUpdate);

    // 注册到全局权限复选框列表
    if (typeof window !== 'undefined') {
      if (!window.permissionCheckboxes) {
        window.permissionCheckboxes = {};
      }
      window.permissionCheckboxes[code] = {
        isChecked: () => checked,
        setChecked: (value: boolean) => {
          setChecked(value);
          // 同时更新DOM元素的checked属性
          if (checkboxRef.current) {
            checkboxRef.current.checked = value;
          }
        }
      };
    }

    // 清理函数
    return () => {
      window.removeEventListener('permission-update' as any, handlePermissionUpdate);

      // 从全局权限复选框列表中移除
      if (typeof window !== 'undefined' && window.permissionCheckboxes) {
        delete window.permissionCheckboxes[code];
      }
    };
  }, [code]);

  // 处理复选框状态变化
  const handleCheckedChange = (isChecked: boolean) => {
    setChecked(isChecked);

    // 同时更新DOM元素的checked属性
    if (checkboxRef.current) {
      checkboxRef.current.checked = isChecked;
    }

    // 更新全局权限复选框列表中的状态
    if (typeof window !== 'undefined' && window.permissionCheckboxes && window.permissionCheckboxes[code]) {
      window.permissionCheckboxes[code].isChecked = () => isChecked;
    }

    console.log(`权限复选框 ${code} 状态变更为: ${isChecked}`);

    // 记录当前选中的所有权限，用于调试
    if (typeof window !== 'undefined' && window.permissionCheckboxes) {
      const selectedPermissions = Object.entries(window.permissionCheckboxes)
        .filter(([_, value]) => value.isChecked())
        .map(([code]) => code);
      console.log('当前选中的所有权限:', selectedPermissions);
    }
  };

  return (
    <div className="flex items-center">
      <input
        type="checkbox"
        ref={checkboxRef}
        id={id}
        name="permission-checkbox"
        data-permission-code={code}
        checked={checked}
        onChange={(e) => handleCheckedChange(e.target.checked)}
        className="hidden" // 隐藏原生复选框，但保留其状态
      />
      <Checkbox
        id={`${id}-ui`}
        checked={checked}
        onCheckedChange={handleCheckedChange}
      />
      <label htmlFor={`${id}-ui`} className="ml-2 text-sm">
        {label}
      </label>
    </div>
  );
}

// 为Window对象添加permissionCheckboxes属性
declare global {
  interface Window {
    permissionCheckboxes?: Record<string, {
      isChecked: () => boolean;
      setChecked: (value: boolean) => void;
    }>;
  }
}
