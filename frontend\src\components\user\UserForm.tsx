"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { UserFormProps, UserFormFields, UserStatus } from './types'

/**
 * 用户表单组件
 *
 * 用于创建和编辑用户
 */
export function UserForm({ loading, user, roles, onSubmit, onCancel }: UserFormProps) {
  const [formData, setFormData] = useState<UserFormFields>({
    id: user?.id,
    username: user?.username || '',
    password: '',
    confirmPassword: '',
    phone: user?.phone || '',
    email: user?.email || '',
    roleId: user?.roleId || '',
    status: user?.status || '正常'
  })
  const [showPassword, setShowPassword] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })

    // 清除错误
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' })
    }
  }

  // 处理选择变化
  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value })

    // 清除错误
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' })
    }
  }

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // 验证用户名
    if (!formData.username) {
      newErrors.username = '请输入用户名'
    }

    // 验证密码（仅在创建用户时必填）
    if (!user && !formData.password) {
      newErrors.password = '请输入密码'
    }

    // 验证确认密码
    if (formData.password && formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致'
    }

    // 验证手机号
    if (!formData.phone) {
      newErrors.phone = '请输入手机号'
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入有效的手机号'
    }

    // 验证邮箱
    if (!formData.email) {
      newErrors.email = '请输入邮箱'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱'
    }

    // 验证角色
    if (!formData.roleId) {
      newErrors.roleId = '请选择角色'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (validateForm()) {
      onSubmit(formData)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
          用户名
        </Label>
        <Input
          id="username"
          name="username"
          value={formData.username}
          onChange={handleInputChange}
          className={errors.username ? 'border-red-500' : ''}
          disabled={loading}
        />
        {errors.username && <p className="mt-1 text-xs text-red-500">{errors.username}</p>}
      </div>

      {/* 密码字段（仅在创建用户时显示） */}
      {!user && (
        <>
          <div>
            <Label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              密码
            </Label>
            <div className="relative">
              <Input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleInputChange}
                className={errors.password ? 'border-red-500' : ''}
                disabled={loading}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? '隐藏' : '显示'}
              </button>
            </div>
            {errors.password && <p className="mt-1 text-xs text-red-500">{errors.password}</p>}
            <p className="text-xs text-gray-500 mt-1">
              密码长度不少于8位
            </p>
          </div>

          <div>
            <Label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
              确认密码
            </Label>
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type={showPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className={errors.confirmPassword ? 'border-red-500' : ''}
              disabled={loading}
            />
            {errors.confirmPassword && <p className="mt-1 text-xs text-red-500">{errors.confirmPassword}</p>}
          </div>
        </>
      )}

      <div>
        <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
          手机号
        </Label>
        <Input
          id="phone"
          name="phone"
          value={formData.phone}
          onChange={handleInputChange}
          className={errors.phone ? 'border-red-500' : ''}
          disabled={loading}
        />
        {errors.phone && <p className="mt-1 text-xs text-red-500">{errors.phone}</p>}
      </div>

      <div>
        <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
          邮箱
        </Label>
        <Input
          id="email"
          name="email"
          type="email"
          value={formData.email}
          onChange={handleInputChange}
          className={errors.email ? 'border-red-500' : ''}
          disabled={loading}
        />
        {errors.email && <p className="mt-1 text-xs text-red-500">{errors.email}</p>}
      </div>

      <div>
        <Label htmlFor="roleId" className="block text-sm font-medium text-gray-700 mb-1">
          角色
        </Label>
        <Select
          value={formData.roleId}
          onValueChange={(value) => handleSelectChange('roleId', value)}
          disabled={loading}
        >
          <SelectTrigger className={errors.roleId ? 'border-red-500' : ''}>
            <SelectValue placeholder="选择角色" />
          </SelectTrigger>
          <SelectContent>
            {roles.map((role) => (
              <SelectItem key={role.id} value={role.id}>
                {role.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.roleId && <p className="mt-1 text-xs text-red-500">{errors.roleId}</p>}
        <p className="text-xs text-gray-500 mt-1">一个用户只能分配一个角色</p>
      </div>

      <div>
        <Label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
          状态
        </Label>
        <Select
          value={formData.status}
          onValueChange={(value) => handleSelectChange('status', value as UserStatus)}
          disabled={loading}
        >
          <SelectTrigger>
            <SelectValue placeholder="选择状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="正常">正常</SelectItem>
            <SelectItem value="待审核">待审核</SelectItem>
            <SelectItem value="已禁用">已禁用</SelectItem>
            <SelectItem value="待激活">待激活</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button variant="outline" onClick={onCancel} disabled={loading}>
          取消
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? '提交中...' : user ? '保存' : '创建'}
        </Button>
      </div>
    </form>
  )
}
