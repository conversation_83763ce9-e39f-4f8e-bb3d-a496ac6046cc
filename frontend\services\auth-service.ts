/**
 * 认证服务
 *
 * 处理用户登录、注册、获取用户信息等
 */

import apiService from './api-service'
import permissionService from './permission-service'
import { logger, sanitizeData } from '@/utils/logger'

// 用户类型
export interface User {
  id: string
  username: string
  email: string
  phone?: string
  avatar?: string
  role: string
  permissions: string[]
  created_at: string
  last_login?: string
}

// 登录参数
export interface LoginParams {
  username: string
  password: string
}

// 注册参数
export interface RegisterParams {
  username: string
  password: string
  email: string
  phone?: string
}

// 修改密码参数
export interface ChangePasswordParams {
  old_password: string
  new_password: string
  confirm_password: string
}

// 更新用户信息参数
export interface UpdateUserParams {
  email?: string
  phone?: string
  avatar?: string
  avatar_url?: string
}

/**
 * 用户登录
 * @param params 登录参数
 */
export const login = async (params: LoginParams): Promise<{ token: string; user: User }> => {
  try {
    logger.debug("开始登录用户:", params.username);

    // 验证参数
    if (!params.username || !params.password) {
      logger.error("登录参数无效:", { username: params.username, hasPassword: !!params.password });
      throw new Error("用户名和密码不能为空");
    }

    const response = await apiService.post<any>('/users/login', params);
    logger.debug("登录响应原始数据:", sanitizeData(response));

    // 打印响应的详细结构，帮助调试
    logger.debug("响应类型:", typeof response);
    logger.debug("响应是否为对象:", response !== null && typeof response === 'object');
    logger.debug("响应的键:", response !== null && typeof response === 'object' ? Object.keys(response) : 'N/A');

    if (response !== null && typeof response === 'object') {
      if ('data' in response) logger.debug("响应中的data:", sanitizeData(response.data));
      if ('success' in response) logger.debug("响应中的success:", response.success);
      if ('message' in response) logger.debug("响应中的message:", response.message);
    }

    // 处理不同的响应格式
    let result: { token: string; user: User };

    // 格式1: { data: { token, user } }
    if (response && typeof response === 'object' && response.data && response.data.token && response.data.user) {
      logger.debug("处理格式1: 响应的data字段包含token和user");
      result = {
        token: response.data.token,
        user: response.data.user
      };
    }
    // 格式2: { token, user }
    else if (response && typeof response === 'object' && response.token && response.user) {
      logger.debug("处理格式2: 响应包含token和user字段");
      result = {
        token: response.token,
        user: response.user
      };
    }
    // 格式3: { success: true, message: '登录成功', data: { token, user } }
    else if (response && typeof response === 'object' && response.success === true &&
             response.data && response.data.token && response.data.user) {
      logger.debug("处理格式3: 响应包含success和data字段");
      result = {
        token: response.data.token,
        user: response.data.user
      };
    }
    // 完全不匹配的情况
    else {
      logger.error("登录响应格式不匹配:", sanitizeData(response));
      throw new Error('登录响应格式错误');
    }

    logger.debug("处理后的登录结果:", sanitizeData(result));

    // 保存token和用户信息到localStorage
    localStorage.setItem('hefamily_token', result.token);
    localStorage.setItem('hefamily_user_info', JSON.stringify(result.user));

    // 同时保存到hefamily_user_data，确保所有组件使用相同的用户信息
    localStorage.setItem('hefamily_user_data', JSON.stringify(result.user));

    return result;
  } catch (error: any) {
    logger.error("登录过程中发生错误:", sanitizeData(error));

    // 处理业务逻辑错误
    if (error.response?.status === 400 && error.response?.data?.message) {
      logger.debug("登录业务逻辑错误:", error.response.data.message);
      throw new Error(error.response.data.message);
    }

    throw error;
  }
}

/**
 * 用户注册
 * @param params 注册参数
 */
export const register = async (params: RegisterParams): Promise<{ token: string; user: User }> => {
  try {
    logger.debug("开始注册用户:", params.username);
    const response = await apiService.post<any>('/users/register', params);
    logger.debug("注册响应原始数据:", sanitizeData(response));

    // 打印响应的详细结构，帮助调试
    logger.debug("响应类型:", typeof response);
    logger.debug("响应是否为对象:", response !== null && typeof response === 'object');
    logger.debug("响应的键:", response !== null && typeof response === 'object' ? Object.keys(response) : 'N/A');

    if (response !== null && typeof response === 'object') {
      if ('data' in response) logger.debug("响应中的data:", sanitizeData(response.data));
      if ('success' in response) logger.debug("响应中的success:", response.success);
      if ('message' in response) logger.debug("响应中的message:", response.message);
    }

    // 处理不同的响应格式
    let result: { token: string; user: User };

    // 处理情况1: 响应本身就是一个包含data字段的对象
    // 格式: { success: true, message: '用户注册成功', data: { id, username, email, role } }
    if (response && typeof response === 'object' && response.success === true && response.data) {
      logger.debug("处理格式1: 响应包含success和data字段");
      result = {
        // 注册成功后没有token，需要用户登录
        token: '',
        user: {
          id: response.data.id,
          username: response.data.username,
          email: response.data.email,
          role: response.data.role || 'basic_user',
          permissions: [],
          created_at: new Date().toISOString()
        }
      };
    }
    // 处理情况2: 响应本身就是一个包含id字段的对象
    // 格式: { id, username, email, role }
    else if (response && typeof response === 'object' && response.id) {
      logger.debug("处理格式2: 响应本身包含id字段");
      result = {
        token: '',
        user: {
          id: response.id,
          username: response.username,
          email: response.email,
          role: response.role || 'basic_user',
          permissions: [],
          created_at: new Date().toISOString()
        }
      };
    }
    // 处理情况3: 响应包含data字段，且data是一个包含id字段的对象
    // 格式: { data: { id, username, email, role } }
    else if (response && typeof response === 'object' && response.data && typeof response.data === 'object' && response.data.id) {
      logger.debug("处理格式3: 响应的data字段包含id");
      result = {
        token: response.token || '',
        user: {
          id: response.data.id,
          username: response.data.username,
          email: response.data.email,
          role: response.data.role || 'basic_user',
          permissions: [],
          created_at: new Date().toISOString()
        }
      };
    }
    // 处理情况4: 响应包含token和user字段
    // 格式: { token, user }
    else if (response && typeof response === 'object' && response.token && response.user) {
      logger.debug("处理格式4: 响应包含token和user字段");
      result = {
        token: response.token,
        user: response.user
      };
    }
    // 处理情况5: 响应是一个空对象或null，但注册可能成功
    // 格式: {} 或 null
    else if (!response || (typeof response === 'object' && Object.keys(response).length === 0)) {
      logger.debug("处理格式5: 响应为空对象或null");
      // 创建一个临时用户对象，用户需要登录才能获取完整信息
      result = {
        token: '',
        user: {
          id: '0', // 临时ID
          username: params.username,
          email: params.email,
          role: 'basic_user',
          permissions: [],
          created_at: new Date().toISOString()
        }
      };
    }
    // 完全不匹配的情况
    else {
      logger.error("注册响应格式不匹配:", sanitizeData(response));
      throw new Error('注册响应格式错误');
    }

    logger.debug("处理后的注册结果:", sanitizeData(result));

    // 保存token和用户信息到localStorage
    if (result.token) {
      localStorage.setItem('hefamily_token', result.token);
      localStorage.setItem('hefamily_user_info', JSON.stringify(result.user));

      // 同时保存到hefamily_user_data，确保所有组件使用相同的用户信息
      localStorage.setItem('hefamily_user_data', JSON.stringify(result.user));
    }

    return result;
  } catch (error: any) {
    // 处理业务逻辑错误（如用户名已存在等）
    if (error.response?.status === 400 && error.response?.data?.message) {
      logger.debug("注册业务逻辑错误:", error.response.data.message);
      // 创建一个新的错误对象，包含后端返回的错误信息
      const businessError = new Error(error.response.data.message);
      throw businessError;
    } else {
      logger.error("注册过程中发生错误:", sanitizeData(error));
      throw error;
    }
  }
}

/**
 * 用户登出
 */
export const logout = (): void => {
  // 清除localStorage中的token和用户信息
  localStorage.removeItem('hefamily_token')
  localStorage.removeItem('hefamily_user_info')
}

/**
 * 获取当前用户信息
 * @param forceRefresh 是否强制从服务器刷新
 */
export const getCurrentUser = async (forceRefresh: boolean = false): Promise<User> => {
  // 如果不强制刷新，先尝试从localStorage获取
  if (!forceRefresh) {
    const userInfo = localStorage.getItem('hefamily_user_info')
    if (userInfo) {
      return JSON.parse(userInfo)
    }
  }

  try {
    // 从API获取最新用户信息
    logger.debug('从服务器获取最新用户信息')
    const response = await apiService.get<User>('/users/me')

    logger.debug('获取到的用户信息:', sanitizeData(response))

    // 保存用户信息到localStorage
    localStorage.setItem('hefamily_user_info', JSON.stringify(response))

    // 同时保存到hefamily_user_data，确保所有组件使用相同的用户信息
    localStorage.setItem('hefamily_user_data', JSON.stringify(response))

    return response
  } catch (error) {
    logger.error('获取用户信息失败:', sanitizeData(error))

    // 如果API请求失败，尝试从localStorage获取
    const userInfo = localStorage.getItem('hefamily_user_info')
    if (userInfo) {
      return JSON.parse(userInfo)
    }

    throw error
  }
}

/**
 * 更新用户信息
 * @param params 更新参数
 */
export const updateUserInfo = async (params: UpdateUserParams): Promise<User> => {
  let response: User;

  // 如果包含头像，需要使用FormData
  if (params.avatar && params.avatar.startsWith('data:')) {
    const formData = new FormData();

    // 将base64转换为Blob
    const base64Response = await fetch(params.avatar);
    const blob = await base64Response.blob();

    // 添加到FormData
    formData.append('avatar', blob, 'avatar.jpg');

    // 添加其他参数
    if (params.email) formData.append('email', params.email);
    if (params.phone) formData.append('phone', params.phone);

    // 发送请求
    response = await apiService.putFormData<User>('/users/me', formData);
  } else {
    // 普通的JSON请求
    response = await apiService.put<User>('/users/me', params);
  }

  logger.debug('用户信息更新成功，准备刷新用户信息')

  // 强制从服务器获取最新的用户信息
  try {
    const latestUser = await getCurrentUser(true)
    logger.debug('获取到最新的用户信息:', sanitizeData(latestUser))
    return latestUser
  } catch (error) {
    logger.error('获取最新用户信息失败，使用响应中的数据:', sanitizeData(error))

    // 如果获取最新信息失败，使用响应中的数据
    // 更新localStorage中的用户信息
    const userInfo = localStorage.getItem('hefamily_user_info')
    if (userInfo) {
      const user = JSON.parse(userInfo)
      const updatedUser = {
        ...user,
        ...response
      }

      // 更新hefamily_user_info
      localStorage.setItem('hefamily_user_info', JSON.stringify(updatedUser))

      // 同时更新hefamily_user_data
      localStorage.setItem('hefamily_user_data', JSON.stringify(updatedUser))
    }

    return response
  }
}

/**
 * 修改密码
 * @param params 修改密码参数
 */
export const changePassword = async (params: ChangePasswordParams): Promise<{ message: string }> => {
  try {
    logger.debug("开始修改密码");

    // 根据后端API设计，修改密码的API路径是 /users/change-password，使用PUT方法
    // 参数名称需要与后端控制器匹配
    const requestData = {
      currentPassword: params.old_password,
      newPassword: params.new_password,
      confirmPassword: params.confirm_password
    };

    logger.debug("发送修改密码请求");

    // 使用PUT方法调用API
    return await apiService.put<{ message: string }>('/users/change-password', requestData);
  } catch (error: any) {
    logger.error("修改密码失败:", sanitizeData(error));
    throw error;
  }
}

/**
 * 检查用户是否已登录
 */
export const isLoggedIn = (): boolean => {
  return !!localStorage.getItem('hefamily_token')
}

/**
 * 检查用户是否有指定权限
 * @param permission 权限代码
 */
export const hasPermission = (permission: string): boolean => {
  const userInfo = localStorage.getItem('hefamily_user_info')
  if (!userInfo) {
    return false
  }

  const user: User = JSON.parse(userInfo)
  return user.permissions?.includes(permission) || false
}

/**
 * 检查用户是否是管理员
 */
export const isAdmin = (): boolean => {
  const userInfo = localStorage.getItem('hefamily_user_info')
  if (!userInfo) {
    return false
  }

  const user: User = JSON.parse(userInfo)
  return user.role === 'admin'
}

/**
 * 刷新用户权限
 *
 * 从服务器获取最新的用户权限并更新本地存储
 *
 * @returns 是否成功刷新权限
 */
export const refreshUserPermissions = async (): Promise<boolean> => {
  try {
    logger.debug('开始刷新用户权限');

    // 检查token是否存在
    const token = localStorage.getItem('hefamily_token');
    if (!token) {
      logger.warn('刷新用户权限失败: 未登录');
      return false;
    }

    // 从API获取最新的用户权限
    const response = await apiService.get('/users/me/permissions');

    // 检查响应是否有效
    if (response && Array.isArray(response)) {
      logger.debug('成功获取最新用户权限:', sanitizeData(response));

      // 提取权限代码
      const permissionCodes = response.map(p => p.code);

      // 保存到本地存储
      permissionService.saveUserPermissionsToStorage(permissionCodes);

      // 触发权限更新事件
      const event = new CustomEvent('permissions-updated', {
        detail: { permissionCodes }
      });
      window.dispatchEvent(event);

      return true;
    } else {
      logger.warn('API返回的权限格式无效:', sanitizeData(response));
      return false;
    }
  } catch (error) {
    logger.error('刷新用户权限失败:', sanitizeData(error));
    return false;
  }
};

export default {
  login,
  register,
  logout,
  getCurrentUser,
  updateUserInfo,
  changePassword,
  isLoggedIn,
  hasPermission,
  isAdmin,
  refreshUserPermissions
}
