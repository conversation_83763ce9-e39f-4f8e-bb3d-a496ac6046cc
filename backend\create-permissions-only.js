/**
 * 仅创建权限脚本
 * 
 * 运行方法：
 * node create-permissions-only.js
 */

const db = require('./src/models');

(async () => {
  try {
    console.log('开始创建权限...');

    // 要创建的权限列表
    const permissions = [
      {
        name: '访问首页',
        code: 'home:access',
        description: '访问系统首页',
        module: 'basic'
      },
      {
        name: '访问家族专题',
        code: 'family:access',
        description: '访问家族专题页面',
        module: 'basic'
      },
      {
        name: '使用AI研究助手',
        code: 'assistant:use',
        description: '使用AI研究助手功能',
        module: 'assistant'
      }
    ];

    // 创建权限
    for (const permissionData of permissions) {
      try {
        // 检查权限是否已存在
        let permission = await db.Permission.findOne({
          where: { code: permissionData.code }
        });

        // 如果权限不存在，创建它
        if (!permission) {
          console.log(`创建权限: ${permissionData.name} (${permissionData.code})`);
          permission = await db.Permission.create(permissionData);
          console.log(`创建成功，权限ID: ${permission.id}`);
        } else {
          console.log(`权限已存在: ${permissionData.name} (${permissionData.code}), ID: ${permission.id}`);
        }
      } catch (error) {
        console.error(`创建权限 ${permissionData.code} 失败:`, error);
      }
    }

    console.log('权限创建完成');
  } catch (error) {
    console.error('创建权限失败:', error);
  } finally {
    process.exit();
  }
})();
