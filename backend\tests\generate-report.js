/**
 * 测试报告生成脚本
 * 
 * 用于生成详细的测试报告
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 报告目录
const REPORT_DIR = path.join(__dirname, '..', 'reports');
const COVERAGE_DIR = path.join(__dirname, '..', 'coverage');
const REPORT_FILE = path.join(REPORT_DIR, 'test-report.html');

// 确保报告目录存在
if (!fs.existsSync(REPORT_DIR)) {
  fs.mkdirSync(REPORT_DIR, { recursive: true });
}

// 运行测试并生成报告
console.log('运行测试并生成报告...');
try {
  execSync('npx jest --coverage --reporters=default --reporters=jest-html-reporter', { stdio: 'inherit' });
} catch (error) {
  console.error('测试失败:', error.message);
  process.exit(1);
}

// 检查报告是否生成
if (fs.existsSync(REPORT_FILE)) {
  console.log(`测试报告已生成: ${REPORT_FILE}`);
} else {
  console.error('测试报告生成失败');
  process.exit(1);
}

// 检查覆盖率报告是否生成
const COVERAGE_REPORT = path.join(COVERAGE_DIR, 'lcov-report', 'index.html');
if (fs.existsSync(COVERAGE_REPORT)) {
  console.log(`覆盖率报告已生成: ${COVERAGE_REPORT}`);
} else {
  console.error('覆盖率报告生成失败');
  process.exit(1);
}

// 生成测试摘要
console.log('\n生成测试摘要...');
const SUMMARY_FILE = path.join(REPORT_DIR, 'test-summary.md');

// 读取覆盖率摘要
const COVERAGE_SUMMARY_PATH = path.join(COVERAGE_DIR, 'coverage-summary.json');
let coverageSummary = {};
if (fs.existsSync(COVERAGE_SUMMARY_PATH)) {
  coverageSummary = JSON.parse(fs.readFileSync(COVERAGE_SUMMARY_PATH, 'utf8')).total;
}

// 生成摘要内容
const now = new Date();
const summaryContent = `# 测试摘要报告

## 测试时间
${now.toLocaleString()}

## 测试覆盖率
- 行覆盖率: ${coverageSummary.lines ? coverageSummary.lines.pct.toFixed(2) : 'N/A'}%
- 函数覆盖率: ${coverageSummary.functions ? coverageSummary.functions.pct.toFixed(2) : 'N/A'}%
- 分支覆盖率: ${coverageSummary.branches ? coverageSummary.branches.pct.toFixed(2) : 'N/A'}%
- 语句覆盖率: ${coverageSummary.statements ? coverageSummary.statements.pct.toFixed(2) : 'N/A'}%

## 测试文件
${fs.readdirSync(path.join(__dirname, 'controllers')).map(file => `- ${file}`).join('\n')}
${fs.readdirSync(path.join(__dirname, 'models')).map(file => `- ${file}`).join('\n')}
${fs.readdirSync(path.join(__dirname, 'middlewares')).map(file => `- ${file}`).join('\n')}
${fs.readdirSync(path.join(__dirname, 'unit')).map(file => `- ${file}`).join('\n')}
${fs.readdirSync(path.join(__dirname, 'integration')).map(file => `- ${file}`).join('\n')}

## 报告链接
- [详细测试报告](../reports/test-report.html)
- [覆盖率报告](../coverage/lcov-report/index.html)

## 注意事项
- 如果测试失败，请查看详细测试报告以了解具体原因
- 如果覆盖率不足，请增加测试用例以提高覆盖率
`;

// 写入摘要文件
fs.writeFileSync(SUMMARY_FILE, summaryContent);
console.log(`测试摘要已生成: ${SUMMARY_FILE}`);

console.log('\n测试报告生成完成');
