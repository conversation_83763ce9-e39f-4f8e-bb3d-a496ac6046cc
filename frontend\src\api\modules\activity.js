/**
 * 活动相关API
 * 
 * 处理活动的创建、查询、更新、删除等请求
 */

import request from '../index';

/**
 * 获取活动列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.search - 搜索关键词
 * @param {string} params.status - 活动状态
 * @returns {Promise} 活动列表
 */
export function getActivityList(params) {
  return request({
    url: '/activities',
    method: 'get',
    params
  });
}

/**
 * 获取活动详情
 * @param {string} id - 活动ID
 * @returns {Promise} 活动详情
 */
export function getActivityById(id) {
  return request({
    url: `/activities/${id}`,
    method: 'get'
  });
}

/**
 * 创建活动
 * @param {Object} data - 活动信息
 * @param {string} data.title - 活动标题
 * @param {string} data.date - 活动日期
 * @param {string} data.description - 活动描述
 * @param {string} data.image - 活动图片
 * @param {string} data.status - 活动状态
 * @returns {Promise} 创建结果
 */
export function createActivity(data) {
  return request({
    url: '/activities',
    method: 'post',
    data
  });
}

/**
 * 上传活动图片
 * @param {FormData} formData - 图片表单数据
 * @returns {Promise} 上传结果
 */
export function uploadActivityImage(formData) {
  return request({
    url: '/activities/upload-image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 更新活动
 * @param {string} id - 活动ID
 * @param {Object} data - 活动信息
 * @returns {Promise} 更新结果
 */
export function updateActivity(id, data) {
  return request({
    url: `/activities/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除活动
 * @param {string} id - 活动ID
 * @returns {Promise} 删除结果
 */
export function deleteActivity(id) {
  return request({
    url: `/activities/${id}`,
    method: 'delete'
  });
}

/**
 * 获取活动附件列表
 * @param {string} id - 活动ID
 * @returns {Promise} 附件列表
 */
export function getActivityAttachments(id) {
  return request({
    url: `/activities/${id}/attachments`,
    method: 'get'
  });
}

/**
 * 上传活动附件
 * @param {string} id - 活动ID
 * @param {FormData} formData - 附件表单数据
 * @returns {Promise} 上传结果
 */
export function uploadActivityAttachment(id, formData) {
  return request({
    url: `/activities/${id}/attachments`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 下载活动附件
 * @param {string} attachmentId - 附件ID
 * @returns {Promise} 下载结果
 */
export function downloadActivityAttachment(attachmentId) {
  return request({
    url: `/activities/attachments/${attachmentId}/download`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 删除活动附件
 * @param {string} attachmentId - 附件ID
 * @returns {Promise} 删除结果
 */
export function deleteActivityAttachment(attachmentId) {
  return request({
    url: `/activities/attachments/${attachmentId}`,
    method: 'delete'
  });
}

export default {
  getActivityList,
  getActivityById,
  createActivity,
  uploadActivityImage,
  updateActivity,
  deleteActivity,
  getActivityAttachments,
  uploadActivityAttachment,
  downloadActivityAttachment,
  deleteActivityAttachment
};
