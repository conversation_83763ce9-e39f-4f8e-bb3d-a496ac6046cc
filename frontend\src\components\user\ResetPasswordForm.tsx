"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ResetPasswordFormProps, ResetPasswordFormFields } from './types'

/**
 * 重置密码表单组件
 *
 * 用于重置用户密码
 */
export function ResetPasswordForm({ loading, user, onSubmit, onCancel }: ResetPasswordFormProps) {
  const [formData, setFormData] = useState<ResetPasswordFormFields>({
    password: '',
    confirmPassword: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })

    // 清除错误
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' })
    }
  }

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // 验证密码
    if (!formData.password) {
      newErrors.password = '请输入新密码'
    } else if (formData.password.length < 8) {
      newErrors.password = '密码长度不能少于8位'
    }

    // 验证确认密码
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认新密码'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (validateForm()) {
      onSubmit(formData)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <p className="mb-4">
          您正在重置用户 <span className="font-bold">{user.name}</span> 的密码。
        </p>
      </div>

      <div>
        <Label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
          新密码
        </Label>
        <div className="relative">
          <Input
            id="password"
            name="password"
            type={showPassword ? 'text' : 'password'}
            value={formData.password}
            onChange={handleInputChange}
            className={errors.password ? 'border-red-500' : ''}
            disabled={loading}
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? '隐藏' : '显示'}
          </button>
        </div>
        {errors.password && <p className="mt-1 text-xs text-red-500">{errors.password}</p>}
        <p className="text-xs text-gray-500 mt-1">
          密码长度不少于8位
        </p>
      </div>

      <div>
        <Label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
          确认密码
        </Label>
        <div className="relative">
          <Input
            id="confirmPassword"
            name="confirmPassword"
            type={showPassword ? 'text' : 'password'}
            value={formData.confirmPassword}
            onChange={handleInputChange}
            className={errors.confirmPassword ? 'border-red-500' : ''}
            disabled={loading}
          />
        </div>
        {errors.confirmPassword && <p className="mt-1 text-xs text-red-500">{errors.confirmPassword}</p>}
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button variant="outline" onClick={onCancel} disabled={loading}>
          取消
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? '提交中...' : '确认重置'}
        </Button>
      </div>
    </form>
  )
}
