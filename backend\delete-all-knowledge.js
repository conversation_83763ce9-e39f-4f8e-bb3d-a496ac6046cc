/**
 * 删除所有知识库和相关文件的脚本
 * 警告：此脚本将永久删除所有知识库和相关文件
 */

// 加载环境变量
require('dotenv').config();

// 导入模型
const db = require('./src/models');

// 删除所有知识库和相关文件
async function deleteAllKnowledgeBasesAndFiles() {
  try {
    // 连接数据库
    await db.sequelize.authenticate();
    console.log('数据库连接成功');

    // 开始事务
    const transaction = await db.sequelize.transaction();

    try {
      // 1. 首先获取所有知识库ID，用于日志记录
      const knowledgeBases = await db.sequelize.query('SELECT id, name FROM knowledge_bases', {
        type: db.sequelize.QueryTypes.SELECT,
        transaction
      });

      console.log(`找到 ${knowledgeBases.length} 个知识库待删除:`);
      knowledgeBases.forEach(kb => {
        console.log(`- ID: ${kb.id}, 名称: ${kb.name}`);
      });

      // 2. 删除所有文件（如果存在文件表）
      try {
        const filesDeleted = await db.sequelize.query('DELETE FROM files', {
          transaction
        });
        console.log(`已删除所有文件: ${filesDeleted[0]} 条记录`);
      } catch (error) {
        console.log('文件表可能不存在或为空，跳过文件删除');
      }

      // 3. 删除所有知识库
      const kbDeleted = await db.sequelize.query('DELETE FROM knowledge_bases', {
        transaction
      });
      console.log(`已删除所有知识库: ${kbDeleted[0]} 条记录`);

      // 提交事务
      await transaction.commit();
      console.log('所有数据已成功删除');

    } catch (error) {
      // 如果出错，回滚事务
      await transaction.rollback();
      throw error;
    }

    // 关闭数据库连接
    await db.sequelize.close();
  } catch (error) {
    console.error('删除失败:', error);
  }
}

// 执行删除操作
console.log('警告: 此操作将删除所有知识库和相关文件!');
console.log('5秒后开始删除...');

setTimeout(() => {
  deleteAllKnowledgeBasesAndFiles();
}, 5000);
