/**
 * 活动附件模型
 * 
 * 定义活动附件数据结构，包括附件名称、类型、大小、路径等信息
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} ActivityAttachment模型
 */
module.exports = (sequelize, DataTypes) => {
  const ActivityAttachment = sequelize.define('ActivityAttachment', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    activity_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'activities',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    original_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '原始文件名'
    },
    path: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '文件存储路径'
    },
    type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '文件类型，如pdf, doc, jpg等'
    },
    mime_type: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '文件MIME类型'
    },
    size: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '文件大小（字节）'
    },
    uploader_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'activity_attachments',
    timestamps: true
  });

  // 关联关系
  ActivityAttachment.associate = (models) => {
    // 附件与活动的多对一关系
    ActivityAttachment.belongsTo(models.Activity, {
      foreignKey: 'activity_id',
      as: 'activity'
    });

    // 附件与上传者的多对一关系
    ActivityAttachment.belongsTo(models.User, {
      foreignKey: 'uploader_id',
      as: 'uploader'
    });
  };

  return ActivityAttachment;
};
