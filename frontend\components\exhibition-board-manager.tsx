"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bsList,
  TabsTrigger
} from "@/components/ui/tabs"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { Loader2, Plus, <PERSON>rash, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Eye } from "lucide-react"
import * as exhibitionBoardService from "@/services/exhibition-board-service"
import { useAuth } from "@/contexts/auth-context"
import { Switch } from "@/components/ui/switch"

/**
 * 展板管理组件
 * 用于管理首页、家族专题和个人专题的展板图片
 */
export function ExhibitionBoardManager() {
  // 状态
  const [activeTab, setActiveTab] = useState("home")
  const [exhibitionBoards, setExhibitionBoards] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showPreviewDialog, setShowPreviewDialog] = useState(false)
  const [currentBoard, setCurrentBoard] = useState<any>(null)
  const [formData, setFormData] = useState({
    type: "home",
    sub_type: "",
    image_url: "",
    title: "",
    description: "",
    order: 0,
    status: "active"
  })
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState("")
  const [uploadingImage, setUploadingImage] = useState(false)

  const { isLoggedIn, hasPermission } = useAuth()

  // 获取展板图片列表
  const fetchExhibitionBoards = async () => {
    try {
      setLoading(true)
      const data = await exhibitionBoardService.getExhibitionBoards(
        activeTab as any,
        activeTab === "personal" ? formData.sub_type : undefined
      )
      setExhibitionBoards(data)
    } catch (error) {
      console.error("获取展板图片列表失败:", error)
      toast({
        title: "获取展板图片列表失败",
        description: "请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 当标签页变化时重新获取数据
  useEffect(() => {
    if (isLoggedIn && hasPermission("exhibition:manage")) {
      fetchExhibitionBoards()
    }
  }, [activeTab, isLoggedIn, hasPermission])

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // 处理选择框变化
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // 处理开关变化
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked ? "active" : "inactive" }))
  }

  // 处理图片上传
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    try {
      // 创建预览
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)

      setImageFile(file)
    } catch (error) {
      console.error("处理图片失败:", error)
      toast({
        title: "处理图片失败",
        description: "请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 上传图片到服务器
  const uploadImage = async () => {
    if (!imageFile) return ""

    try {
      setUploadingImage(true)
      const result = await exhibitionBoardService.uploadExhibitionImage(imageFile)
      return result.url
    } catch (error) {
      console.error("上传图片失败:", error)
      toast({
        title: "上传图片失败",
        description: "请稍后重试",
        variant: "destructive"
      })
      return ""
    } finally {
      setUploadingImage(false)
    }
  }

  // 添加展板图片
  const handleAddBoard = async () => {
    try {
      // 上传图片
      let imageUrl = formData.image_url
      if (imageFile) {
        imageUrl = await uploadImage()
        if (!imageUrl) return
      }

      // 创建展板图片
      await exhibitionBoardService.createExhibitionBoard({
        ...formData,
        type: activeTab,
        image_url: imageUrl
      })

      toast({
        title: "添加展板图片成功",
        description: "展板图片已成功添加"
      })

      // 重置表单
      setFormData({
        type: activeTab,
        sub_type: "",
        image_url: "",
        title: "",
        description: "",
        order: 0,
        status: "active"
      })
      setImageFile(null)
      setImagePreview("")
      setShowAddDialog(false)

      // 重新获取数据
      fetchExhibitionBoards()
    } catch (error) {
      console.error("添加展板图片失败:", error)
      toast({
        title: "添加展板图片失败",
        description: "请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 编辑展板图片
  const handleEditBoard = async () => {
    if (!currentBoard) return

    try {
      // 上传图片
      let imageUrl = formData.image_url
      if (imageFile) {
        imageUrl = await uploadImage()
        if (!imageUrl) return
      }

      // 更新展板图片
      await exhibitionBoardService.updateExhibitionBoard(currentBoard.id, {
        ...formData,
        image_url: imageUrl
      })

      toast({
        title: "更新展板图片成功",
        description: "展板图片已成功更新"
      })

      // 重置表单
      setCurrentBoard(null)
      setImageFile(null)
      setImagePreview("")
      setShowEditDialog(false)

      // 重新获取数据
      fetchExhibitionBoards()
    } catch (error) {
      console.error("更新展板图片失败:", error)
      toast({
        title: "更新展板图片失败",
        description: "请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 删除展板图片
  const handleDeleteBoard = async (id: number) => {
    if (!confirm("确定要删除这个展板图片吗？此操作不可撤销。")) return

    try {
      await exhibitionBoardService.deleteExhibitionBoard(id)

      toast({
        title: "删除展板图片成功",
        description: "展板图片已成功删除"
      })

      // 重新获取数据
      fetchExhibitionBoards()
    } catch (error) {
      console.error("删除展板图片失败:", error)
      toast({
        title: "删除展板图片失败",
        description: "请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 打开编辑对话框
  const openEditDialog = (board: any) => {
    setCurrentBoard(board)
    setFormData({
      type: board.type,
      sub_type: board.sub_type || "",
      image_url: board.image_url,
      title: board.title || "",
      description: board.description || "",
      order: board.order || 0,
      status: board.status
    })
    setImagePreview(board.image_url)
    setShowEditDialog(true)
  }

  // 打开预览对话框
  const openPreviewDialog = (board: any) => {
    setCurrentBoard(board)
    setShowPreviewDialog(true)
  }

  // 移动展板图片顺序
  const moveBoard = async (id: number, direction: "up" | "down") => {
    const currentIndex = exhibitionBoards.findIndex(board => board.id === id)
    if (currentIndex === -1) return

    const newBoards = [...exhibitionBoards]
    const targetIndex = direction === "up" ? currentIndex - 1 : currentIndex + 1

    // 检查目标索引是否有效
    if (targetIndex < 0 || targetIndex >= newBoards.length) return

    // 交换顺序
    const temp = newBoards[currentIndex].order
    newBoards[currentIndex].order = newBoards[targetIndex].order
    newBoards[targetIndex].order = temp

    // 交换位置
    [newBoards[currentIndex], newBoards[targetIndex]] = [newBoards[targetIndex], newBoards[currentIndex]]

    // 更新状态
    setExhibitionBoards(newBoards)

    // 更新服务器
    try {
      await exhibitionBoardService.updateExhibitionBoardOrder([
        { id: newBoards[currentIndex].id, order: newBoards[currentIndex].order },
        { id: newBoards[targetIndex].id, order: newBoards[targetIndex].order }
      ])
    } catch (error) {
      console.error("更新展板图片顺序失败:", error)
      toast({
        title: "更新展板图片顺序失败",
        description: "请稍后重试",
        variant: "destructive"
      })
      // 重新获取数据
      fetchExhibitionBoards()
    }
  }

  // 渲染展板图片列表
  const renderExhibitionBoards = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )
    }

    if (exhibitionBoards.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          暂无展板图片，请点击"添加展板图片"按钮添加
        </div>
      )
    }

    return (
      <Table>
        <TableCaption>展板图片列表</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">序号</TableHead>
            <TableHead>预览</TableHead>
            <TableHead>标题</TableHead>
            <TableHead>状态</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {exhibitionBoards.map((board, index) => (
            <TableRow key={board.id}>
              <TableCell>{index + 1}</TableCell>
              <TableCell>
                <div className="relative h-16 w-24 overflow-hidden rounded-md">
                  <img
                    src={board.image_url}
                    alt={board.title || "展板图片"}
                    className="h-full w-full object-cover"
                  />
                </div>
              </TableCell>
              <TableCell>{board.title || "无标题"}</TableCell>
              <TableCell>
                <span className={`px-2 py-1 rounded-full text-xs ${board.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}`}>
                  {board.status === "active" ? "启用" : "禁用"}
                </span>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => moveBoard(board.id, "up")}
                    disabled={index === 0}
                  >
                    <ArrowUp className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => moveBoard(board.id, "down")}
                    disabled={index === exhibitionBoards.length - 1}
                  >
                    <ArrowDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => openPreviewDialog(board)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => openEditDialog(board)}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    className="text-red-500 hover:text-red-700"
                    onClick={() => handleDeleteBoard(board.id)}
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    )
  }

  // 渲染添加/编辑表单
  const renderForm = (isEdit: boolean) => (
    <>
      <div className="grid grid-cols-1 gap-4 py-4">
        {activeTab === "home" && (
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sub_type" className="text-right">
              类型
            </Label>
            <div className="col-span-3">
              <Select
                value={formData.sub_type}
                onValueChange={(value) => handleSelectChange("sub_type", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sy">首页展板图片</SelectItem>
                  <SelectItem value="carousel">轮播图</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {activeTab === "personal" && (
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sub_type" className="text-right">
              人物
            </Label>
            <div className="col-span-3">
              <Select
                value={formData.sub_type}
                onValueChange={(value) => handleSelectChange("sub_type", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择人物" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cai-hesen">蔡和森</SelectItem>
                  <SelectItem value="xiang-jingyu">向警予</SelectItem>
                  <SelectItem value="cai-chang">蔡畅</SelectItem>
                  <SelectItem value="li-fuchun">李富春</SelectItem>
                  <SelectItem value="ge-jianhao">葛健豪</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="image" className="text-right">
            图片
          </Label>
          <div className="col-span-3">
            <Input
              id="image"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
            />
            {imagePreview && (
              <div className="mt-2 relative h-40 w-full overflow-hidden rounded-md">
                <img
                  src={imagePreview}
                  alt="预览"
                  className="h-full w-full object-contain"
                />
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="title" className="text-right">
            标题
          </Label>
          <div className="col-span-3">
            <Input
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="输入标题"
            />
          </div>
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="description" className="text-right">
            描述
          </Label>
          <div className="col-span-3">
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="输入描述"
              rows={3}
            />
          </div>
        </div>



        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="order" className="text-right">
            排序
          </Label>
          <div className="col-span-3">
            <Input
              id="order"
              name="order"
              type="number"
              value={formData.order.toString()}
              onChange={handleInputChange}
              placeholder="输入排序顺序"
            />
          </div>
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="status" className="text-right">
            状态
          </Label>
          <div className="col-span-3 flex items-center gap-2">
            <Switch
              id="status"
              checked={formData.status === "active"}
              onCheckedChange={(checked) => handleSwitchChange("status", checked)}
            />
            <Label htmlFor="status" className="cursor-pointer">
              {formData.status === "active" ? "启用" : "禁用"}
            </Label>
          </div>
        </div>
      </div>
    </>
  )

  // 如果用户没有权限，显示提示信息
  if (!isLoggedIn || !hasPermission("exhibition:manage")) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-white rounded-lg border border-gray-200">
        <div className="text-xl font-bold text-gray-700 mb-4">权限不足</div>
        <p className="text-gray-500 text-center mb-6">
          您没有管理展板图片的权限，请联系系统管理员获取相应权限。
        </p>
        {!isLoggedIn && (
          <Link href="/login">
            <Button className="bg-[#1e7a43] hover:bg-[#1e7a43]/90">
              登录
            </Button>
          </Link>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-bold">展板图片管理</h2>
        <Button
          className="bg-[#f5a623] hover:bg-[#f5a623]/90"
          onClick={() => {
            setFormData({
              type: activeTab,
              sub_type: "",
              image_url: "",
              title: "",
              description: "",
              order: exhibitionBoards.length,
              status: "active"
            })
            setImageFile(null)
            setImagePreview("")
            setShowAddDialog(true)
          }}
        >
          <Plus className="mr-2 h-4 w-4" /> 添加展板图片
        </Button>
      </div>

      <Tabs defaultValue="home" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="home">首页展板图片</TabsTrigger>
          <TabsTrigger value="family">家族专题展板</TabsTrigger>
          <TabsTrigger value="personal">个人专题展板</TabsTrigger>
        </TabsList>

        <TabsContent value="home" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>首页展板图片</CardTitle>
              <CardDescription>
                管理首页展板图片，其中类型为"首页展板图片"的图片将显示在首页顶部，不会轮播
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderExhibitionBoards()}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="family" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>家族专题展板</CardTitle>
              <CardDescription>
                管理家族专题展板，这些图片将显示在家族专题页面
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderExhibitionBoards()}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="personal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>个人专题展板</CardTitle>
              <CardDescription>
                管理个人专题展板，这些图片将显示在个人专题页面
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderExhibitionBoards()}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 添加展板图片对话框 */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加展板图片</DialogTitle>
            <DialogDescription>
              添加新的展板图片，填写以下信息并点击保存
            </DialogDescription>
          </DialogHeader>
          {renderForm(false)}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              取消
            </Button>
            <Button
              className="bg-[#f5a623] hover:bg-[#f5a623]/90"
              onClick={handleAddBoard}
              disabled={uploadingImage}
            >
              {uploadingImage && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑展板图片对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑展板图片</DialogTitle>
            <DialogDescription>
              编辑展板图片信息，修改以下内容并点击保存
            </DialogDescription>
          </DialogHeader>
          {renderForm(true)}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              取消
            </Button>
            <Button
              className="bg-[#f5a623] hover:bg-[#f5a623]/90"
              onClick={handleEditBoard}
              disabled={uploadingImage}
            >
              {uploadingImage && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 预览展板图片对话框 */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>预览展板图片</DialogTitle>
          </DialogHeader>
          {currentBoard && (
            <div className="py-4">
              <div className="relative h-[400px] w-full overflow-hidden rounded-md mb-4">
                <img
                  src={currentBoard.image_url}
                  alt={currentBoard.title || "展板图片"}
                  className="h-full w-full object-cover"
                />
                <div className="absolute inset-0 bg-black/30 flex items-center">
                  <div className="container mx-auto px-4">
                    <div className="max-w-2xl text-white">
                      <h1 className="text-4xl font-bold mb-4">{currentBoard.title}</h1>
                      <p className="text-lg mb-8">
                        {currentBoard.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">类型</p>
                  <p>
                    {currentBoard.type === "home" ?
                      (currentBoard.sub_type === "sy" ? "首页展板图片" :
                       currentBoard.sub_type === "carousel" ? "首页轮播图" : "首页图片") :
                     currentBoard.type === "family" ? "家族专题展板" : "个人专题展板"}
                  </p>
                </div>
                {currentBoard.type === "home" && currentBoard.sub_type && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">子类型</p>
                    <p>
                      {currentBoard.sub_type === "sy" ? "首页展板图片" :
                       currentBoard.sub_type === "carousel" ? "轮播图" :
                       currentBoard.sub_type}
                    </p>
                  </div>
                )}

                {currentBoard.type === "personal" && currentBoard.sub_type && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">人物</p>
                    <p>
                      {currentBoard.sub_type === "cai-hesen" ? "蔡和森" :
                       currentBoard.sub_type === "xiang-jingyu" ? "向警予" :
                       currentBoard.sub_type === "cai-chang" ? "蔡畅" :
                       currentBoard.sub_type === "li-fuchun" ? "李富春" :
                       currentBoard.sub_type === "ge-jianhao" ? "葛健豪" :
                       currentBoard.sub_type}
                    </p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-gray-500">状态</p>
                  <p>{currentBoard.status === "active" ? "启用" : "禁用"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">排序</p>
                  <p>{currentBoard.order}</p>
                </div>

              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreviewDialog(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
