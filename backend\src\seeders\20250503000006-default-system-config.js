/**
 * 默认系统配置种子数据
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('system_config', [
      {
        id: 1,
        password_min_length: 8,
        password_require_uppercase: true,
        password_require_lowercase: true,
        password_require_number: true,
        password_require_special: true,
        max_login_attempts: 5,
        lockout_duration: 30,
        session_timeout: 60,
        file_upload_max_size: 10,
        allowed_file_types: 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif',
        system_name: '和富家族研究平台',
        system_logo: '/logo.png',
        system_description: '和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。',
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('system_config', { id: 1 }, {});
  }
};
