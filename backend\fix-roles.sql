-- 角色修复SQL脚本
-- 此脚本用于修复系统中的角色问题

-- 1. 查看所有角色
SELECT id, name, description, is_system, created_at, updated_at FROM roles;

-- 2. 查看admin用户使用的角色
SELECT u.id, u.username, u.role, u.role_id, r.name as role_name, r.is_system
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
WHERE u.username = 'admin';

-- 3. 确保管理员角色被标记为系统角色
UPDATE roles
SET is_system = 1
WHERE name = '管理员' OR name = 'admin';

-- 4. 确保访问者角色被标记为系统角色
UPDATE roles
SET is_system = 1
WHERE name = '访问者' OR name = '初级访问者' OR name = 'basic_user';

-- 5. 删除测试角色（请确保先将使用这些角色的用户迁移到其他角色）
-- 首先查看使用测试角色的用户
SELECT u.id, u.username, u.role, u.role_id, r.name as role_name
FROM users u
JOIN roles r ON u.role_id = r.id
WHERE r.name LIKE 'test_%' OR r.name LIKE '%_test' OR r.name LIKE '%_role_%';

-- 6. 查找访问者角色ID
SELECT id FROM roles WHERE name = '访问者' OR name = '初级访问者' OR name = 'basic_user' LIMIT 1;

-- 7. 将使用测试角色的用户迁移到访问者角色（请替换 {访问者角色ID} 为实际ID）
-- UPDATE users
-- SET role_id = {访问者角色ID}
-- WHERE role_id IN (SELECT id FROM roles WHERE name LIKE 'test_%' OR name LIKE '%_test' OR name LIKE '%_role_%');

-- 8. 删除测试角色
-- DELETE FROM roles
-- WHERE name LIKE 'test_%' OR name LIKE '%_test' OR name LIKE '%_role_%';

-- 9. 查找管理员角色ID
SELECT id FROM roles WHERE name = '管理员' OR name = 'admin' LIMIT 1;

-- 10. 确保admin用户使用正确的管理员角色（请替换 {管理员角色ID} 为实际ID）
-- UPDATE users
-- SET role_id = {管理员角色ID}
-- WHERE username = 'admin';

-- 11. 再次查看所有角色，确认修复结果
-- SELECT id, name, description, is_system, created_at, updated_at FROM roles;

-- 注意：执行步骤7、8和10之前，请先取消相应行的注释，并替换占位符为实际值
