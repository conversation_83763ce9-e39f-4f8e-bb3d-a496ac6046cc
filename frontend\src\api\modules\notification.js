/**
 * 通知相关API
 * 
 * 处理通知的查询、标记已读等请求
 */

import request from '../index';

/**
 * 获取通知列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.type - 通知类型
 * @param {boolean} params.is_read - 是否已读
 * @returns {Promise} 通知列表
 */
export function getNotificationList(params) {
  return request({
    url: '/notifications',
    method: 'get',
    params
  });
}

/**
 * 获取未读通知数量
 * @returns {Promise} 未读通知数量
 */
export function getUnreadNotificationCount() {
  return request({
    url: '/notifications/unread-count',
    method: 'get'
  });
}

/**
 * 标记通知为已读
 * @param {string} id - 通知ID
 * @returns {Promise} 标记结果
 */
export function markNotificationAsRead(id) {
  return request({
    url: `/notifications/${id}/read`,
    method: 'put'
  });
}

/**
 * 标记所有通知为已读
 * @returns {Promise} 标记结果
 */
export function markAllNotificationsAsRead() {
  return request({
    url: '/notifications/read-all',
    method: 'put'
  });
}

/**
 * 删除通知
 * @param {string} id - 通知ID
 * @returns {Promise} 删除结果
 */
export function deleteNotification(id) {
  return request({
    url: `/notifications/${id}`,
    method: 'delete'
  });
}

/**
 * 删除所有通知
 * @returns {Promise} 删除结果
 */
export function deleteAllNotifications() {
  return request({
    url: '/notifications',
    method: 'delete'
  });
}

export default {
  getNotificationList,
  getUnreadNotificationCount,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  deleteAllNotifications
};
