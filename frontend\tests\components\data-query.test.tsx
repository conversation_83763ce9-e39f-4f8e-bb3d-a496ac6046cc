/**
 * 数据查询组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { DataQuery } from '@/components/data-query/DataQuery'
import { mockFetch, resetMockFetch } from '../test-utils'

// 模拟知识库数据
const mockKnowledgeBases = [
  {
    id: 1,
    name: '系统知识库1',
    type: 'system'
  },
  {
    id: 2,
    name: '用户知识库1',
    type: 'user'
  }
]

// 模拟查询结果数据
const mockQueryResults = [
  {
    id: 1,
    title: '测试文档1',
    content: '这是测试文档1的内容摘要...',
    source: '系统知识库1',
    file_type: 'pdf',
    file_id: 101,
    relevance: 0.95,
    created_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 2,
    title: '测试文档2',
    content: '这是测试文档2的内容摘要...',
    source: '用户知识库1',
    file_type: 'docx',
    file_id: 102,
    relevance: 0.85,
    created_at: '2023-01-02T00:00:00Z'
  }
]

describe('DataQuery组件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch()
    jest.clearAllMocks()
  })

  // 测试基本渲染
  test('应该正确渲染数据查询组件', () => {
    render(
      <DataQuery
        knowledgeBases={mockKnowledgeBases}
        isLoggedIn={true}
        onQuery={jest.fn()}
        onDownload={jest.fn()}
      />
    )
    
    expect(screen.getByText('数据查询')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('输入查询内容...')).toBeInTheDocument()
    expect(screen.getByText('选择知识库')).toBeInTheDocument()
    expect(screen.getByText('系统知识库1')).toBeInTheDocument()
    expect(screen.getByText('用户知识库1')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '查询' })).toBeInTheDocument()
  })

  // 测试未登录状态
  test('未登录时应该显示登录提示', () => {
    render(
      <DataQuery
        knowledgeBases={mockKnowledgeBases}
        isLoggedIn={false}
        onQuery={jest.fn()}
        onDownload={jest.fn()}
      />
    )
    
    expect(screen.getByText('请登录后使用数据查询功能')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument()
  })

  // 测试查询功能
  test('应该执行查询并显示结果', async () => {
    const handleQuery = jest.fn().mockResolvedValue({
      results: mockQueryResults,
      total: 2
    })
    
    render(
      <DataQuery
        knowledgeBases={mockKnowledgeBases}
        isLoggedIn={true}
        onQuery={handleQuery}
        onDownload={jest.fn()}
      />
    )
    
    // 输入查询内容
    const queryInput = screen.getByPlaceholderText('输入查询内容...')
    fireEvent.change(queryInput, { target: { value: '测试查询' } })
    
    // 选择知识库
    const knowledgeBaseSelect = screen.getByLabelText('选择知识库')
    fireEvent.change(knowledgeBaseSelect, { target: { value: '1' } })
    
    // 点击查询按钮
    const queryButton = screen.getByRole('button', { name: '查询' })
    fireEvent.click(queryButton)
    
    // 验证查询函数被调用
    expect(handleQuery).toHaveBeenCalledWith('测试查询', ['1'])
    
    // 等待查询结果显示
    await waitFor(() => {
      expect(screen.getByText('测试文档1')).toBeInTheDocument()
      expect(screen.getByText('测试文档2')).toBeInTheDocument()
      expect(screen.getByText('这是测试文档1的内容摘要...')).toBeInTheDocument()
      expect(screen.getByText('这是测试文档2的内容摘要...')).toBeInTheDocument()
    })
  })

  // 测试AI查询
  test('应该执行AI查询', async () => {
    const handleQuery = jest.fn().mockResolvedValue({
      results: mockQueryResults,
      total: 2,
      ai_response: '这是AI助手的回复，基于您的查询，我找到了以下相关文档...'
    })
    
    render(
      <DataQuery
        knowledgeBases={mockKnowledgeBases}
        isLoggedIn={true}
        onQuery={handleQuery}
        onDownload={jest.fn()}
      />
    )
    
    // 输入查询内容
    const queryInput = screen.getByPlaceholderText('输入查询内容...')
    fireEvent.change(queryInput, { target: { value: '测试AI查询' } })
    
    // 选择知识库
    const knowledgeBaseSelect = screen.getByLabelText('选择知识库')
    fireEvent.change(knowledgeBaseSelect, { target: { value: '1' } })
    
    // 切换到AI查询模式
    const aiQuerySwitch = screen.getByLabelText('AI查询')
    fireEvent.click(aiQuerySwitch)
    
    // 点击查询按钮
    const queryButton = screen.getByRole('button', { name: '查询' })
    fireEvent.click(queryButton)
    
    // 验证查询函数被调用
    expect(handleQuery).toHaveBeenCalledWith('测试AI查询', ['1'], true)
    
    // 等待AI回复显示
    await waitFor(() => {
      expect(screen.getByText('这是AI助手的回复，基于您的查询，我找到了以下相关文档...')).toBeInTheDocument()
    })
  })

  // 测试文件下载
  test('应该调用文件下载函数', async () => {
    const handleQuery = jest.fn().mockResolvedValue({
      results: mockQueryResults,
      total: 2
    })
    
    const handleDownload = jest.fn()
    
    render(
      <DataQuery
        knowledgeBases={mockKnowledgeBases}
        isLoggedIn={true}
        onQuery={handleQuery}
        onDownload={handleDownload}
      />
    )
    
    // 执行查询以显示结果
    const queryInput = screen.getByPlaceholderText('输入查询内容...')
    fireEvent.change(queryInput, { target: { value: '测试查询' } })
    
    const queryButton = screen.getByRole('button', { name: '查询' })
    fireEvent.click(queryButton)
    
    // 等待查询结果显示
    await waitFor(() => {
      expect(screen.getByText('测试文档1')).toBeInTheDocument()
    })
    
    // 点击下载按钮
    const downloadButtons = await screen.findAllByText('下载')
    fireEvent.click(downloadButtons[0])
    
    // 验证下载函数被调用
    expect(handleDownload).toHaveBeenCalledWith(101)
  })

  // 测试查询错误处理
  test('应该处理查询错误', async () => {
    const handleQuery = jest.fn().mockRejectedValue(new Error('查询失败'))
    
    render(
      <DataQuery
        knowledgeBases={mockKnowledgeBases}
        isLoggedIn={true}
        onQuery={handleQuery}
        onDownload={jest.fn()}
      />
    )
    
    // 输入查询内容
    const queryInput = screen.getByPlaceholderText('输入查询内容...')
    fireEvent.change(queryInput, { target: { value: '错误查询' } })
    
    // 点击查询按钮
    const queryButton = screen.getByRole('button', { name: '查询' })
    fireEvent.click(queryButton)
    
    // 等待错误消息显示
    await waitFor(() => {
      expect(screen.getByText('查询失败')).toBeInTheDocument()
    })
  })

  // 测试空查询验证
  test('不应该执行空查询', () => {
    const handleQuery = jest.fn()
    
    render(
      <DataQuery
        knowledgeBases={mockKnowledgeBases}
        isLoggedIn={true}
        onQuery={handleQuery}
        onDownload={jest.fn()}
      />
    )
    
    // 不输入查询内容，直接点击查询按钮
    const queryButton = screen.getByRole('button', { name: '查询' })
    fireEvent.click(queryButton)
    
    // 验证查询函数未被调用
    expect(handleQuery).not.toHaveBeenCalled()
    
    // 验证错误提示
    expect(screen.getByText('请输入查询内容')).toBeInTheDocument()
  })

  // 测试多选知识库
  test('应该支持选择多个知识库', async () => {
    const handleQuery = jest.fn().mockResolvedValue({
      results: mockQueryResults,
      total: 2
    })
    
    render(
      <DataQuery
        knowledgeBases={mockKnowledgeBases}
        isLoggedIn={true}
        onQuery={handleQuery}
        onDownload={jest.fn()}
      />
    )
    
    // 输入查询内容
    const queryInput = screen.getByPlaceholderText('输入查询内容...')
    fireEvent.change(queryInput, { target: { value: '测试查询' } })
    
    // 选择多个知识库
    const knowledgeBaseCheckboxes = screen.getAllByRole('checkbox')
    fireEvent.click(knowledgeBaseCheckboxes[0]) // 选择第一个知识库
    fireEvent.click(knowledgeBaseCheckboxes[1]) // 选择第二个知识库
    
    // 点击查询按钮
    const queryButton = screen.getByRole('button', { name: '查询' })
    fireEvent.click(queryButton)
    
    // 验证查询函数被调用，参数包含多个知识库ID
    expect(handleQuery).toHaveBeenCalledWith('测试查询', ['1', '2'])
  })
});
