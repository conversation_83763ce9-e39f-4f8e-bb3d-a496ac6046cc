"use client"

import React, { useState, useEffect, useRef } from 'react'

/**
 * 简单打字机效果组件
 *
 * 用于实现打字机效果的React组件
 * @param text 要显示的文本
 * @param className 自定义CSS类
 * @param speed 打字速度（毫秒/字符）
 * @param showCursor 是否显示光标
 * @param onComplete 打字完成时的回调函数
 */
export function SimpleTypewriterNew({
  text,
  className = "",
  speed = 30,
  showCursor = true,
  onComplete
}: {
  text: string,
  className?: string,
  speed?: number,
  showCursor?: boolean,
  onComplete?: () => void
}) {
  // 显示的文本
  const [displayText, setDisplayText] = useState("")
  // 是否完成打字
  const [isComplete, setIsComplete] = useState(false)
  // 保存文本引用，用于比较变化
  const textRef = useRef(text)
  // 定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // 当文本变化时重置状态
  useEffect(() => {
    // 如果文本为空，不执行打字效果
    if (!text || text.trim() === "") {
      console.log('文本为空，不执行打字效果')
      setDisplayText("")
      setIsComplete(true)
      return
    }

    // 如果文本变化，重置状态
    if (text !== textRef.current) {
      console.log('文本变化，重置打字效果:', text.substring(0, 20) + '...')
      textRef.current = text
      setDisplayText("")
      setIsComplete(false)

      // 清除现有定时器
      if (timerRef.current) {
        clearTimeout(timerRef.current)
        timerRef.current = null
      }

      // 开始打字效果
      let index = 0
      const typeNextChar = () => {
        if (index < text.length) {
          setDisplayText(prev => prev + text[index])
          index++
          timerRef.current = setTimeout(typeNextChar, speed)
        } else {
          setIsComplete(true)
          // 调用完成回调
          if (onComplete) {
            console.log('打字机效果完成，调用onComplete回调')
            onComplete()
          }
        }
      }

      // 开始打字
      timerRef.current = setTimeout(typeNextChar, speed)
    }

    // 清理函数
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
        timerRef.current = null
      }
    }
  }, [text, speed, onComplete])

  // 将文本中的换行符转换为<br>标签
  const formattedText = displayText.split('\n').map((line, i, arr) => (
    <React.Fragment key={i}>
      {line}
      {i < arr.length - 1 && <br />}
    </React.Fragment>
  ))

  return (
    <div className={className}>
      {formattedText}
      {showCursor && !isComplete && (
        <span className="inline-block w-2 h-4 bg-gray-500 ml-1 animate-pulse"></span>
      )}
    </div>
  )
}
