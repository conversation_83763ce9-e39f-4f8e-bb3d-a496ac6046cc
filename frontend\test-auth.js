// 测试用户认证和权限
const token = localStorage.getItem('hefamily_token');
const userInfo = localStorage.getItem('hefamily_user_info');
const userData = localStorage.getItem('hefamily_user_data');
const permissions = localStorage.getItem('hefamily_user_permissions');
const isLoggedIn = localStorage.getItem('isLoggedIn');

console.log('认证信息:');
console.log('Token:', token ? '存在' : '不存在');
console.log('用户信息:', userInfo ? JSON.parse(userInfo) : '不存在');
console.log('用户数据:', userData ? JSON.parse(userData) : '不存在');
console.log('权限:', permissions ? JSON.parse(permissions) : '不存在');
console.log('登录状态:', isLoggedIn);

// 检查是否有管理活动的权限
const hasManageActivitiesPermission = permissions ? 
  JSON.parse(permissions).includes('manage_activities') : false;

console.log('是否有管理活动的权限:', hasManageActivitiesPermission);
