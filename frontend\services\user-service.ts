/**
 * 用户服务
 *
 * 处理用户相关的API请求，并实现安全的日志记录
 */

import apiService from './api-service';
import { logger, sanitizeData } from '@/utils/logger';

/**
 * 用户接口定义
 */
export interface User {
  id: number;
  username: string;
  email?: string;
  phone?: string;
  avatar?: string;
  role: string;
  role_id?: number;
  role_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * 用户服务类
 */
class UserService {
  /**
   * 获取认证令牌
   * @returns 存储的认证令牌
   */
  getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('hefamily_token');
    }
    return null;
  }

  /**
   * 设置认证令牌
   * @param token 认证令牌
   */
  setAuthToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('hefamily_token', token);
    }
  }

  /**
   * 清除认证令牌
   */
  clearAuthToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('hefamily_token');
    }
  }
  /**
   * 获取用户列表
   * @param params 查询参数
   * @returns 用户列表和分页信息
   */
  async getUsers(params: {
    page?: number;
    limit?: number;
    role?: string;
    is_active?: boolean;
    search?: string;
  }) {
    const response = await apiService.get('/users', { params });
    return {
      users: response.data.users as User[],
      pagination: response.data.pagination
    };
  }

  /**
   * 获取所有用户（用于管理员界面）
   * @returns 所有用户列表
   */
  async getAllUsers() {
    try {
      logger.debug("开始获取所有用户");
      const response = await apiService.get('/users');
      logger.debug("获取所有用户响应:", sanitizeData(response));

      // 检查响应格式
      if (response && response.data && response.data.data) {
        // 标准格式：{ success: true, count: N, data: [...] }
        logger.debug("返回标准格式数据");
        return response.data.data;
      } else if (response && response.data) {
        // 直接返回数据数组
        logger.debug("返回数据数组");
        return response.data;
      } else {
        logger.error("获取用户列表响应格式错误:", sanitizeData(response));
        throw new Error("获取用户列表响应格式错误");
      }
    } catch (error) {
      logger.error("获取所有用户失败:", sanitizeData(error));

      // 如果是403错误，表示权限不足
      if (error.response && error.response.status === 403) {
        logger.warn("权限不足，无法获取用户列表");
        return []; // 返回空数组
      }

      // 尝试从数据库获取真实用户数据
      try {
        logger.debug("尝试直接从数据库获取用户数据");
        const dbResponse = await fetch('/api/db/users');
        if (dbResponse.ok) {
          const users = await dbResponse.json();
          logger.debug("从数据库获取到用户数据:", sanitizeData(users));
          return users;
        }
      } catch (dbError) {
        logger.error("从数据库获取用户数据失败:", sanitizeData(dbError));
      }

      // 不再返回模拟数据，而是抛出错误
      throw error;
    }
  }

  /**
   * 获取用户详情
   * @param id 用户ID
   * @returns 用户详情
   */
  async getUser(id: number) {
    const response = await apiService.get(`/users/${id}`);
    return response.data as User;
  }

  /**
   * 创建用户
   * @param data 用户数据
   * @returns 创建结果
   */
  async createUser(data: {
    username: string;
    password: string;
    email?: string;
    phone?: string;
    role?: string;
  }) {
    const response = await apiService.post('/users', data);
    return response.data;
  }

  /**
   * 更新用户
   * @param id 用户ID
   * @param data 更新数据
   * @returns 更新结果
   */
  async updateUser(id: number, data: {
    email?: string;
    phone?: string;
    avatar?: string;
    role?: string;
    is_active?: boolean;
  }) {
    const response = await apiService.put(`/users/${id}`, data);
    return response.data;
  }

  /**
   * 删除用户
   * @param id 用户ID
   * @returns 删除结果
   */
  async deleteUser(id: number) {
    const response = await apiService.del(`/users/${id}`);
    return response.data;
  }

  /**
   * 修改密码
   * @param data 密码数据
   * @returns 修改结果
   */
  async changePassword(data: {
    old_password: string;
    new_password: string;
  }) {
    const response = await apiService.put('/users/password', data);
    return response.data;
  }

  /**
   * 重置密码
   * @param id 用户ID
   * @returns 重置结果
   */
  async resetPassword(id: number) {
    const response = await apiService.put(`/users/${id}/reset-password`);
    return response.data;
  }

  /**
   * 获取当前用户信息
   * @returns 当前用户信息
   */
  async getCurrentUser() {
    try {
      logger.debug("开始获取当前用户信息");
      const response = await apiService.get('/users/me');
      logger.debug("获取当前用户信息成功:", sanitizeData(response));

      // 检查响应格式
      if (response && response.data) {
        return response.data as User;
      } else if (response) {
        return response as User;
      } else {
        logger.error("获取用户信息响应格式错误:", sanitizeData(response));
        throw new Error("获取用户信息响应格式错误");
      }
    } catch (error) {
      logger.error("获取当前用户信息失败:", sanitizeData(error));

      // 如果是401错误，清除token
      if (error.response && error.response.status === 401) {
        logger.warn("用户未授权，清除token");
        this.clearAuthToken();
      }

      throw error;
    }
  }

  /**
   * 更新当前用户信息
   * @param data 更新数据
   * @returns 更新结果
   */
  async updateCurrentUser(data: {
    email?: string;
    phone?: string;
    avatar?: string;
  }) {
    const response = await apiService.put('/users/me', data);
    return response.data;
  }

  /**
   * 用户登录
   * @param credentials 登录凭证
   * @returns 登录结果
   */
  async login(credentials: { username: string; password: string }) {
    logger.debug("开始登录请求，用户名:", credentials.username);

    // 验证参数
    if (!credentials.username || !credentials.password) {
      logger.error("登录参数无效:", { username: credentials.username, hasPassword: !!credentials.password });
      throw new Error("用户名和密码不能为空");
    }

    try {
      // 确保参数是正确的格式
      const loginData = {
        username: credentials.username.trim(),
        password: credentials.password
      };

      logger.debug("发送登录请求数据:", { username: loginData.username, hasPassword: !!loginData.password });

      // 使用JSON.stringify确保数据被正确序列化
      const response = await apiService.post('/users/login', loginData);
      logger.debug("登录响应:", sanitizeData(response));
      return response;
    } catch (error: any) {
      // 根据错误类型使用不同的日志级别
      if (error.response?.status === 400) {
        logger.info("登录参数错误:", sanitizeData(error.response.data));
      } else if (error.response?.status === 404) {
        // 用户不存在的情况
        logger.info("用户不存在:", sanitizeData(error.response.data));
        // 确保错误对象包含正确的消息
        if (!error.response.data?.message || !error.response.data.message.includes('用户不存在')) {
          // 如果后端没有返回明确的"用户不存在"消息，我们手动添加
          error.response.data = error.response.data || {};
          error.response.data.message = '用户不存在';
        }
      } else if (error.response?.status === 401) {
        logger.info("密码错误:", sanitizeData(error.response.data));
      } else {
        // 其他错误使用warn级别
        logger.warn("登录请求失败:", sanitizeData(error));
      }

      throw error;
    }
  }

  /**
   * 用户登出
   */
  logout() {
    this.clearAuthToken();
    if (typeof window !== 'undefined') {
      // 清除所有用户相关的数据
      localStorage.removeItem('hefamily_user_info');
      localStorage.removeItem('hefamily_user_data');
      localStorage.removeItem('isLoggedIn');
    }
  }
}

const userService = new UserService();
export default userService;
