/**
 * 优化构建脚本
 * 
 * 用于优化生产环境构建，提高性能
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('开始优化构建...');

// 清理缓存
console.log('清理缓存...');
try {
  execSync('npx rimraf .next', { stdio: 'inherit' });
  execSync('npx rimraf node_modules/.cache', { stdio: 'inherit' });
} catch (error) {
  console.error('清理缓存失败:', error);
}

// 构建生产环境
console.log('构建生产环境...');
try {
  execSync('npm run build', { stdio: 'inherit' });
} catch (error) {
  console.error('构建失败:', error);
  process.exit(1);
}

// 优化生成的文件
console.log('优化生成的文件...');

// 检查是否存在 .next/static 目录
const staticDir = path.join(__dirname, '.next', 'static');
if (!fs.existsSync(staticDir)) {
  console.error('找不到 .next/static 目录，跳过文件优化');
} else {
  // 优化 JavaScript 文件
  console.log('优化 JavaScript 文件...');
  try {
    execSync('npx terser-dir .next/static -o .next/static -e -x .js', { stdio: 'inherit' });
  } catch (error) {
    console.warn('JavaScript 文件优化失败，跳过:', error);
  }

  // 优化 CSS 文件
  console.log('优化 CSS 文件...');
  try {
    execSync('npx cleancss -o .next/static/css .next/static/css', { stdio: 'inherit' });
  } catch (error) {
    console.warn('CSS 文件优化失败，跳过:', error);
  }
}

console.log('优化构建完成！');
