import apiService from './api-service'

/**
 * 查询研究助手
 *
 * 向后端发送请求，获取AI助手的回复
 * @param message 用户消息
 * @param conversationId 会话ID，如果是新会话则为null
 * @param assistantId 助手ID
 * @returns 包含AI回复的响应对象
 */
export async function queryResearchAssistant(message: string, conversationId?: string | null, assistantId?: number) {
  try {
    console.log('调用研究助手API，参数:', { message, conversationId, assistantId })
    const response = await apiService.post('/ai/assistant/query', {
      message,
      conversation_id: conversationId,
      assistant_id: assistantId
    })
    console.log('研究助手API响应:', response)
    return response
  } catch (error) {
    console.error('查询研究助手失败:', error)
    throw error
  }
}

/**
 * 获取研究助手列表
 *
 * 向后端发送请求，获取所有可用的研究助手
 * @returns 包含研究助手列表的响应对象
 */
export async function getResearchAssistants() {
  try {
    const response = await apiService.get('/ai')
    return response
  } catch (error) {
    console.error('获取研究助手列表失败:', error)
    throw error
  }
}

/**
 * 获取研究助手会话历史
 *
 * 向后端发送请求，获取指定会话的历史消息
 * @param conversationId 会话ID
 * @returns 包含会话历史的响应对象
 */
export async function getConversationHistory(conversationId: string) {
  try {
    const response = await apiService.get(`/ai/conversations/assistant`)
    return response
  } catch (error) {
    console.error('获取会话历史失败:', error)
    throw error
  }
}
