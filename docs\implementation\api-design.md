# 和富家族研究平台API设计文档

## 1. 概述

本文档描述了和富家族研究平台的API设计，包括API架构、接口规范、认证机制、错误处理等内容。API设计遵循RESTful风格，提供简单、一致、安全的接口，满足前端应用与后端服务的交互需求。

### 1.1 文档目的

本文档旨在：
- 为开发团队提供API设计指南和接口规范
- 为前端开发人员提供接口使用说明
- 为测试人员提供接口测试依据
- 为系统维护人员提供接口维护参考

### 1.2 适用范围

本文档适用于和富家族研究平台的所有API接口，包括：
- 用户管理接口
- 内容管理接口
- 活动管理接口
- 知识库管理接口
- 数据查询接口
- AI助手接口
- 系统管理接口
- 通知接口

### 1.3 术语与缩略语

- **API**：应用程序接口(Application Programming Interface)
- **REST**：表述性状态转移(Representational State Transfer)
- **JWT**：JSON Web Token，用于身份验证的令牌
- **HTTP**：超文本传输协议(Hypertext Transfer Protocol)
- **JSON**：JavaScript对象表示法(JavaScript Object Notation)
- **CRUD**：创建(Create)、读取(Read)、更新(Update)、删除(Delete)操作

## 2. API设计原则

### 2.1 RESTful设计原则

API设计遵循RESTful架构风格，主要原则包括：

1. **资源导向**：API围绕资源设计，使用名词表示资源
2. **HTTP方法语义**：使用HTTP方法表示操作
   - GET：获取资源
   - POST：创建资源
   - PUT：更新资源（全量更新）
   - PATCH：更新资源（部分更新）
   - DELETE：删除资源
3. **无状态**：服务器不保存客户端状态，每个请求包含所有必要信息
4. **统一接口**：接口设计保持一致性，便于理解和使用
5. **分层系统**：客户端无需了解后端实现细节

### 2.2 API版本控制

API版本控制采用URL路径方式：

```
/api/v1/resource
```

当API发生不兼容变更时，增加版本号，保持旧版本API可用一段时间，以便客户端平滑迁移。

### 2.3 命名规范

1. **URL路径**：
   - 使用小写字母
   - 使用连字符(-)分隔单词
   - 使用复数形式表示资源集合
   - 例如：`/api/v1/knowledge-files`

2. **查询参数**：
   - 使用驼峰命名法
   - 例如：`/api/v1/users?pageSize=10&pageNumber=1`

3. **请求体字段**：
   - 使用驼峰命名法
   - 例如：`{ "userName": "john", "emailAddress": "<EMAIL>" }`

### 2.4 响应格式

所有API响应采用统一的JSON格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  },
  "timestamp": "2023-07-01T12:00:00Z"
}
```

字段说明：
- `code`：响应状态码，与HTTP状态码保持一致
- `message`：响应消息，描述操作结果
- `data`：响应数据，可以是对象、数组或null
- `timestamp`：响应时间戳，ISO 8601格式

### 2.5 分页设计

列表查询接口支持分页，分页参数通过查询参数传递：

```
/api/v1/resources?pageSize=10&pageNumber=1
```

分页响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [
      // 资源列表
    ],
    "pagination": {
      "total": 100,
      "pageSize": 10,
      "pageNumber": 1,
      "totalPages": 10
    }
  },
  "timestamp": "2023-07-01T12:00:00Z"
}
```

### 2.6 过滤、排序与搜索

1. **过滤**：通过查询参数指定过滤条件
   ```
   /api/v1/resources?status=active&type=document
   ```

2. **排序**：通过`sort`参数指定排序字段和方向
   ```
   /api/v1/resources?sort=createdAt:desc,name:asc
   ```

3. **搜索**：通过`q`参数指定搜索关键词
   ```
   /api/v1/resources?q=keyword
   ```

## 3. 认证与授权

### 3.1 认证机制

系统采用JWT(JSON Web Token)进行用户认证：

1. **登录流程**：
   - 客户端发送用户名和密码到`/api/v1/auth/login`
   - 服务器验证用户名和密码
   - 验证成功后，服务器生成JWT令牌并返回给客户端
   - 客户端存储JWT令牌（如localStorage）

2. **请求认证**：
   - 客户端在每个需要认证的请求中，在HTTP头部添加`Authorization`字段
   - 格式为：`Authorization: Bearer {token}`
   - 服务器验证令牌的有效性和过期时间

3. **令牌刷新**：
   - JWT令牌有过期时间（如24小时）
   - 客户端可以在令牌过期前，使用刷新令牌获取新的访问令牌
   - 刷新接口：`/api/v1/auth/refresh-token`

### 3.2 授权机制

系统采用基于角色的访问控制(RBAC)：

1. **角色定义**：
   - 系统预定义多个角色，如管理员、研究员、普通用户等
   - 每个角色拥有不同的权限

2. **权限检查**：
   - 服务器在处理请求前，检查用户角色是否有权限执行请求的操作
   - 如果没有权限，返回403 Forbidden错误

3. **权限粒度**：
   - 资源级权限：控制对整个资源类型的访问
   - 操作级权限：控制对资源的特定操作（如读取、创建、更新、删除）
   - 字段级权限：控制对资源特定字段的访问

### 3.3 安全措施

1. **HTTPS**：所有API请求通过HTTPS加密传输
2. **CSRF防护**：对非GET请求实施CSRF令牌验证
3. **请求限流**：限制单个用户的请求频率，防止DoS攻击
4. **输入验证**：严格验证所有输入参数，防止注入攻击
5. **敏感数据处理**：敏感数据（如密码）不在响应中返回

## 4. 错误处理

### 4.1 错误响应格式

错误响应采用统一的JSON格式：

```json
{
  "code": 400,
  "message": "请求参数错误",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ],
  "timestamp": "2023-07-01T12:00:00Z"
}
```

字段说明：
- `code`：HTTP状态码
- `message`：错误描述
- `errors`：详细错误信息，可选
- `timestamp`：错误发生时间

### 4.2 HTTP状态码使用

系统使用标准HTTP状态码表示请求结果：

- **2xx**：成功
  - 200 OK：请求成功
  - 201 Created：资源创建成功
  - 204 No Content：请求成功，无返回内容（如删除操作）

- **4xx**：客户端错误
  - 400 Bad Request：请求参数错误
  - 401 Unauthorized：未认证或认证失败
  - 403 Forbidden：无权限访问
  - 404 Not Found：资源不存在
  - 409 Conflict：资源冲突（如创建已存在的资源）
  - 422 Unprocessable Entity：请求格式正确但语义错误

- **5xx**：服务器错误
  - 500 Internal Server Error：服务器内部错误
  - 503 Service Unavailable：服务不可用

### 4.3 错误码设计

除HTTP状态码外，系统还定义了业务错误码，用于更精确地描述错误：

| 错误码 | 描述 | HTTP状态码 |
|--------|------|------------|
| 10001 | 用户名或密码错误 | 401 |
| 10002 | 账号已被禁用 | 403 |
| 10003 | 令牌已过期 | 401 |
| 20001 | 资源不存在 | 404 |
| 20002 | 资源已存在 | 409 |
| 30001 | 权限不足 | 403 |
| 40001 | 参数验证失败 | 400 |
| 50001 | 服务器内部错误 | 500 |

错误响应示例：

```json
{
  "code": 10001,
  "message": "用户名或密码错误",
  "timestamp": "2023-07-01T12:00:00Z"
}
```

### 4.4 异常处理流程

1. **参数验证**：请求参数验证失败时，返回400状态码和详细的验证错误信息
2. **认证异常**：认证失败时，返回401状态码
3. **授权异常**：权限不足时，返回403状态码
4. **资源异常**：资源不存在时，返回404状态码
5. **业务异常**：业务逻辑错误时，返回对应的错误码和状态码
6. **系统异常**：系统内部错误时，返回500状态码，并记录详细错误日志

## 5. API文档化

### 5.1 文档工具

系统使用Swagger/OpenAPI规范生成API文档：

1. **Swagger UI**：提供交互式API文档界面
2. **OpenAPI规范**：使用YAML或JSON格式描述API

### 5.2 文档访问

API文档通过以下方式访问：

1. **开发环境**：`/swagger-ui.html`
2. **测试环境**：`/api/docs`
3. **生产环境**：不提供在线文档，提供离线文档

### 5.3 文档内容

API文档包含以下内容：

1. **API概述**：描述API的用途和使用方法
2. **认证说明**：描述API的认证机制
3. **接口列表**：按模块组织的接口列表
4. **接口详情**：每个接口的详细说明，包括：
   - 请求方法和URL
   - 请求参数说明
   - 请求体结构
   - 响应结构
   - 错误码说明
   - 示例请求和响应
5. **模型定义**：API使用的数据模型定义

## 6. API接口定义

### 6.1 认证接口

#### 6.1.1 用户登录

- **接口**：`POST /api/v1/auth/login`
- **描述**：用户登录接口，验证用户名和密码，返回JWT令牌
- **请求体**：
  ```json
  {
    "username": "john",
    "password": "password123"
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 86400,
      "user": {
        "id": 1,
        "username": "john",
        "email": "<EMAIL>",
        "role": "初级访问者"
      }
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 10001：用户名或密码错误
  - 10002：账号已被禁用

#### 6.1.2 用户注册

- **接口**：`POST /api/v1/auth/register`
- **描述**：用户注册接口，创建新用户
- **请求体**：
  ```json
  {
    "username": "john",
    "password": "password123",
    "email": "<EMAIL>",
    "phone": "13800138000"
  }
  ```
- **响应**：
  ```json
  {
    "code": 201,
    "message": "注册成功",
    "data": {
      "id": 1,
      "username": "john",
      "email": "<EMAIL>"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20002：用户名已存在
  - 40001：参数验证失败

#### 6.1.3 刷新令牌

- **接口**：`POST /api/v1/auth/refresh-token`
- **描述**：使用刷新令牌获取新的访问令牌
- **请求体**：
  ```json
  {
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "刷新成功",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 86400
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 10003：令牌已过期
  - 10004：无效的刷新令牌

#### 6.1.4 退出登录

- **接口**：`POST /api/v1/auth/logout`
- **描述**：用户退出登录，使当前令牌失效
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "退出成功",
    "data": null,
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

### 6.2 用户接口

#### 6.2.1 获取当前用户信息

- **接口**：`GET /api/v1/users/profile`
- **描述**：获取当前登录用户的详细信息
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "username": "john",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "realName": "John Doe",
      "avatar": "https://example.com/avatar.jpg",
      "role": "初级访问者",
      "lastLogin": "2023-07-01T10:00:00Z",
      "createdAt": "2023-01-01T00:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

#### 6.2.2 更新用户信息

- **接口**：`PUT /api/v1/users/profile`
- **描述**：更新当前登录用户的信息
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "email": "<EMAIL>",
    "phone": "13900139000",
    "realName": "John Smith",
    "avatar": "https://example.com/new-avatar.jpg"
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "更新成功",
    "data": {
      "id": 1,
      "username": "john",
      "email": "<EMAIL>",
      "phone": "13900139000",
      "realName": "John Smith",
      "avatar": "https://example.com/new-avatar.jpg"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 40001：参数验证失败

#### 6.2.3 修改密码

- **接口**：`PUT /api/v1/users/password`
- **描述**：修改当前登录用户的密码
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "oldPassword": "password123",
    "newPassword": "newPassword123",
    "confirmPassword": "newPassword123"
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "密码修改成功",
    "data": null,
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 10005：原密码错误
  - 40001：参数验证失败

### 6.3 内容接口

#### 6.3.1 获取首页内容

- **接口**：`GET /api/v1/content/home`
- **描述**：获取首页展示内容，包括家族历史、革命先辈、纪念活动等
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "banner": {
        "title": "和富家族研究平台",
        "subtitle": "传承红色基因，弘扬革命精神",
        "imageUrl": "https://example.com/banner.jpg"
      },
      "familyHistory": {
        "title": "家族历史长河",
        "description": "葛健豪家族在中国革命和建设过程中的历史贡献",
        "timelineEvents": [
          {
            "id": 1,
            "year": 1920,
            "title": "蔡和森赴法勤工俭学",
            "description": "蔡和森赴法国勤工俭学，开始接触马克思主义",
            "imageUrl": "https://example.com/event1.jpg"
          },
          // 更多事件...
        ]
      },
      "revolutionaryFigures": [
        {
          "id": 1,
          "name": "蔡和森",
          "title": "中国共产党早期领导人",
          "imageUrl": "https://example.com/caihesen.jpg",
          "description": "蔡和森（1895-1931），中国共产党早期领导人之一",
          "link": "/personal-topic/caihesen"
        },
        // 更多人物...
      ],
      "memorialActivities": [
        {
          "id": 1,
          "title": "蔡和森诞辰纪念活动",
          "date": "2023-03-30",
          "location": "湖南长沙",
          "imageUrl": "https://example.com/activity1.jpg",
          "description": "纪念蔡和森同志诞辰128周年"
        },
        // 更多活动...
      ]
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

#### 6.3.2 获取家族专题内容

- **接口**：`GET /api/v1/content/family-topic`
- **描述**：获取家族专题内容，包括家族历史概述和重要事件时间线
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "overview": {
        "title": "葛健豪家族历史概述",
        "content": "葛健豪家族是中国革命历史上的重要家族，家族成员包括蔡和森、向警予、蔡畅、李富春、葛健豪等，他们在中国革命和建设过程中做出了重要贡献...",
        "imageUrl": "https://example.com/family-overview.jpg"
      },
      "timeline": [
        {
          "id": 1,
          "year": 1895,
          "month": 3,
          "day": 30,
          "title": "蔡和森出生",
          "content": "蔡和森出生于湖南省长沙县",
          "imageUrl": "https://example.com/timeline1.jpg"
        },
        // 更多时间线事件...
      ]
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

#### 6.3.3 获取个人专题内容

- **接口**：`GET /api/v1/content/personal-topic/{id}`
- **描述**：获取指定历史人物的个人专题内容
- **路径参数**：
  - `id`：人物ID或标识符
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "basicInfo": {
        "id": 1,
        "name": "蔡和森",
        "birthDate": "1895-03-30",
        "deathDate": "1931-08-04",
        "birthPlace": "湖南省长沙县",
        "portrait": "https://example.com/caihesen-portrait.jpg",
        "introduction": "蔡和森（1895-1931），中国共产党早期领导人之一，中国共产党的创建者之一..."
      },
      "lifeTrajectory": [
        {
          "id": 1,
          "year": 1895,
          "month": 3,
          "day": 30,
          "title": "出生",
          "content": "蔡和森出生于湖南省长沙县",
          "imageUrl": "https://example.com/birth.jpg"
        },
        // 更多人生轨迹...
      ],
      "historicalContributions": [
        {
          "id": 1,
          "title": "创建中国共产党",
          "content": "蔡和森是中国共产党的创建者之一，他在1920年致函毛泽东，提出'明目张胆正式成立一个中国共产党'的主张...",
          "imageUrl": "https://example.com/contribution1.jpg"
        },
        // 更多历史贡献...
      ],
      "relatedMaterials": [
        {
          "id": 1,
          "title": "蔡和森选集",
          "type": "document",
          "fileUrl": "https://example.com/files/caihesen-collection.pdf",
          "description": "收录蔡和森重要文章和书信",
          "uploadDate": "2023-01-15"
        },
        // 更多相关资料...
      ]
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：资源不存在

#### 6.3.4 发表评论

- **接口**：`POST /api/v1/content/comments`
- **描述**：在指定内容下发表评论
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "contentId": 1,
    "content": "这是一条评论内容",
    "parentId": null
  }
  ```
- **响应**：
  ```json
  {
    "code": 201,
    "message": "评论发表成功，等待审核",
    "data": {
      "id": 1,
      "contentId": 1,
      "userId": 1,
      "userName": "john",
      "content": "这是一条评论内容",
      "parentId": null,
      "status": "pending",
      "createdAt": "2023-07-01T12:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：内容不存在
  - 40001：参数验证失败

#### 6.3.5 获取评论列表

- **接口**：`GET /api/v1/content/comments/{contentId}`
- **描述**：获取指定内容的评论列表
- **路径参数**：
  - `contentId`：内容ID
- **查询参数**：
  - `pageSize`：每页条数，默认10
  - `pageNumber`：页码，默认1
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "list": [
        {
          "id": 1,
          "contentId": 1,
          "userId": 1,
          "userName": "john",
          "userAvatar": "https://example.com/avatar.jpg",
          "content": "这是一条评论内容",
          "parentId": null,
          "status": "approved",
          "createdAt": "2023-07-01T10:00:00Z",
          "replies": [
            {
              "id": 2,
              "contentId": 1,
              "userId": 2,
              "userName": "jane",
              "userAvatar": "https://example.com/avatar2.jpg",
              "content": "这是一条回复",
              "parentId": 1,
              "status": "approved",
              "createdAt": "2023-07-01T11:00:00Z"
            }
          ]
        },
        // 更多评论...
      ],
      "pagination": {
        "total": 100,
        "pageSize": 10,
        "pageNumber": 1,
        "totalPages": 10
      }
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

### 6.4 活动接口

#### 6.4.1 获取活动列表

- **接口**：`GET /api/v1/activities`
- **描述**：获取纪念活动列表
- **查询参数**：
  - `pageSize`：每页条数，默认10
  - `pageNumber`：页码，默认1
  - `status`：活动状态，可选值：published（已发布）、archived（已归档）
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "list": [
        {
          "id": 1,
          "title": "蔡和森诞辰纪念活动",
          "description": "纪念蔡和森同志诞辰128周年",
          "startTime": "2023-03-30T09:00:00Z",
          "endTime": "2023-03-30T17:00:00Z",
          "location": "湖南长沙",
          "image": "https://example.com/activity1.jpg",
          "organizer": "湖南省革命历史纪念馆",
          "status": "published",
          "createdAt": "2023-03-01T00:00:00Z"
        },
        // 更多活动...
      ],
      "pagination": {
        "total": 50,
        "pageSize": 10,
        "pageNumber": 1,
        "totalPages": 5
      }
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

#### 6.4.2 获取活动详情

- **接口**：`GET /api/v1/activities/{id}`
- **描述**：获取指定活动的详细信息
- **路径参数**：
  - `id`：活动ID
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "title": "蔡和森诞辰纪念活动",
      "description": "纪念蔡和森同志诞辰128周年，本次活动将举办学术研讨会、文物展览等多项内容...",
      "startTime": "2023-03-30T09:00:00Z",
      "endTime": "2023-03-30T17:00:00Z",
      "location": "湖南长沙市开福区蔡和森纪念馆",
      "image": "https://example.com/activity1.jpg",
      "organizer": "湖南省革命历史纪念馆",
      "status": "published",
      "attachments": [
        {
          "id": 1,
          "name": "活动日程",
          "filePath": "https://example.com/files/schedule.pdf",
          "fileSize": 1024000,
          "fileType": "application/pdf"
        },
        // 更多附件...
      ],
      "creatorId": 1,
      "creatorName": "admin",
      "createdAt": "2023-03-01T00:00:00Z",
      "updatedAt": "2023-03-15T00:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：资源不存在

#### 6.4.3 创建活动

- **接口**：`POST /api/v1/activities`
- **描述**：创建新的纪念活动
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "title": "向警予诞辰纪念活动",
    "description": "纪念向警予同志诞辰125周年",
    "startTime": "2023-09-04T09:00:00Z",
    "endTime": "2023-09-04T17:00:00Z",
    "location": "湖南长沙",
    "image": "https://example.com/activity2.jpg",
    "organizer": "湖南省妇女联合会",
    "status": "draft"
  }
  ```
- **响应**：
  ```json
  {
    "code": 201,
    "message": "活动创建成功",
    "data": {
      "id": 2,
      "title": "向警予诞辰纪念活动",
      "description": "纪念向警予同志诞辰125周年",
      "startTime": "2023-09-04T09:00:00Z",
      "endTime": "2023-09-04T17:00:00Z",
      "location": "湖南长沙",
      "image": "https://example.com/activity2.jpg",
      "organizer": "湖南省妇女联合会",
      "status": "draft",
      "creatorId": 1,
      "creatorName": "admin",
      "createdAt": "2023-07-01T12:00:00Z",
      "updatedAt": "2023-07-01T12:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 30001：权限不足
  - 40001：参数验证失败

#### 6.4.4 更新活动

- **接口**：`PUT /api/v1/activities/{id}`
- **描述**：更新指定活动的信息
- **路径参数**：
  - `id`：活动ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "title": "向警予诞辰纪念活动（更新）",
    "description": "纪念向警予同志诞辰125周年，活动内容已更新",
    "startTime": "2023-09-04T10:00:00Z",
    "endTime": "2023-09-04T18:00:00Z",
    "location": "湖南长沙市岳麓区向警予纪念馆",
    "image": "https://example.com/activity2-updated.jpg",
    "organizer": "湖南省妇女联合会",
    "status": "published"
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "活动更新成功",
    "data": {
      "id": 2,
      "title": "向警予诞辰纪念活动（更新）",
      "description": "纪念向警予同志诞辰125周年，活动内容已更新",
      "startTime": "2023-09-04T10:00:00Z",
      "endTime": "2023-09-04T18:00:00Z",
      "location": "湖南长沙市岳麓区向警予纪念馆",
      "image": "https://example.com/activity2-updated.jpg",
      "organizer": "湖南省妇女联合会",
      "status": "published",
      "updatedAt": "2023-07-01T13:00:00Z"
    },
    "timestamp": "2023-07-01T13:00:00Z"
  }
  ```
- **错误码**：
  - 20001：资源不存在
  - 30001：权限不足
  - 40001：参数验证失败

#### 6.4.5 删除活动

- **接口**：`DELETE /api/v1/activities/{id}`
- **描述**：删除指定活动
- **路径参数**：
  - `id`：活动ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 204,
    "message": "活动删除成功",
    "data": null,
    "timestamp": "2023-07-01T14:00:00Z"
  }
  ```
- **错误码**：
  - 20001：资源不存在
  - 30001：权限不足

#### 6.4.6 上传活动附件

- **接口**：`POST /api/v1/activities/{id}/attachments`
- **描述**：为指定活动上传附件
- **路径参数**：
  - `id`：活动ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  Content-Type: multipart/form-data
  ```
- **请求体**：
  ```
  file: [文件二进制数据]
  name: 活动日程
  ```
- **响应**：
  ```json
  {
    "code": 201,
    "message": "附件上传成功",
    "data": {
      "id": 1,
      "activityId": 2,
      "name": "活动日程",
      "filePath": "https://example.com/files/schedule.pdf",
      "fileSize": 1024000,
      "fileType": "application/pdf",
      "createdAt": "2023-07-01T15:00:00Z"
    },
    "timestamp": "2023-07-01T15:00:00Z"
  }
  ```
- **错误码**：
  - 20001：资源不存在
  - 30001：权限不足
  - 40002：文件上传失败

### 6.5 知识库接口

#### 6.5.1 获取知识库分类

- **接口**：`GET /api/v1/knowledge/categories`
- **描述**：获取知识库资料分类列表
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "name": "历史文献",
        "description": "历史相关文献资料",
        "parentId": null,
        "children": [
          {
            "id": 2,
            "name": "书信",
            "description": "历史人物书信往来",
            "parentId": 1
          },
          {
            "id": 3,
            "name": "文章",
            "description": "历史人物发表的文章",
            "parentId": 1
          }
        ]
      },
      {
        "id": 4,
        "name": "研究成果",
        "description": "相关研究成果",
        "parentId": null,
        "children": []
      }
    ],
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

#### 6.5.2 获取知识库文件列表

- **接口**：`GET /api/v1/knowledge/files`
- **描述**：获取知识库文件列表
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **查询参数**：
  - `pageSize`：每页条数，默认10
  - `pageNumber`：页码，默认1
  - `categoryId`：分类ID，可选
  - `q`：搜索关键词，可选
  - `sort`：排序字段和方向，如`createdAt:desc`
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "list": [
        {
          "id": 1,
          "title": "蔡和森选集",
          "description": "收录蔡和森重要文章和书信",
          "filePath": "https://example.com/files/caihesen-collection.pdf",
          "fileSize": 5242880,
          "fileType": "application/pdf",
          "categoryId": 1,
          "categoryName": "历史文献",
          "uploaderId": 1,
          "uploaderName": "admin",
          "status": "published",
          "viewCount": 120,
          "downloadCount": 45,
          "createdAt": "2023-01-15T00:00:00Z"
        },
        // 更多文件...
      ],
      "pagination": {
        "total": 50,
        "pageSize": 10,
        "pageNumber": 1,
        "totalPages": 5
      }
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

#### 6.5.3 获取知识库文件详情

- **接口**：`GET /api/v1/knowledge/files/{id}`
- **描述**：获取指定知识库文件的详细信息
- **路径参数**：
  - `id`：文件ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "title": "蔡和森选集",
      "description": "收录蔡和森重要文章和书信，包括《致毛泽东的信》等重要历史文献。",
      "filePath": "https://example.com/files/caihesen-collection.pdf",
      "fileSize": 5242880,
      "fileType": "application/pdf",
      "categoryId": 1,
      "categoryName": "历史文献",
      "uploaderId": 1,
      "uploaderName": "admin",
      "status": "published",
      "viewCount": 120,
      "downloadCount": 45,
      "analysisResult": {
        "summary": "本文档收录了蔡和森的重要文章和书信，包括《致毛泽东的信》等...",
        "keywords": ["蔡和森", "中国共产党", "马克思主义", "书信"],
        "topics": ["党的创建", "革命理论", "组织建设"]
      },
      "tags": [
        {
          "id": 1,
          "name": "蔡和森"
        },
        {
          "id": 2,
          "name": "历史文献"
        }
      ],
      "createdAt": "2023-01-15T00:00:00Z",
      "updatedAt": "2023-01-15T00:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：资源不存在
  - 30001：权限不足

#### 6.5.4 上传知识库文件

- **接口**：`POST /api/v1/knowledge/files`
- **描述**：上传新的知识库文件
- **请求头**：
  ```
  Authorization: Bearer {token}
  Content-Type: multipart/form-data
  ```
- **请求体**：
  ```
  file: [文件二进制数据]
  title: 向警予文集
  description: 收录向警予重要文章和讲话
  categoryId: 1
  tags: ["向警予", "历史文献"]
  ```
- **响应**：
  ```json
  {
    "code": 201,
    "message": "文件上传成功",
    "data": {
      "id": 2,
      "title": "向警予文集",
      "description": "收录向警予重要文章和讲话",
      "filePath": "https://example.com/files/xiangjingyu-collection.pdf",
      "fileSize": 4194304,
      "fileType": "application/pdf",
      "categoryId": 1,
      "categoryName": "历史文献",
      "uploaderId": 1,
      "uploaderName": "admin",
      "status": "published",
      "createdAt": "2023-07-01T12:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 30001：权限不足
  - 40001：参数验证失败
  - 40002：文件上传失败

#### 6.5.5 更新知识库文件信息

- **接口**：`PUT /api/v1/knowledge/files/{id}`
- **描述**：更新指定知识库文件的信息（不包括文件本身）
- **路径参数**：
  - `id`：文件ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "title": "向警予文集（修订版）",
    "description": "收录向警予重要文章和讲话，经过修订和补充",
    "categoryId": 1,
    "tags": ["向警予", "历史文献", "女性革命家"]
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "文件信息更新成功",
    "data": {
      "id": 2,
      "title": "向警予文集（修订版）",
      "description": "收录向警予重要文章和讲话，经过修订和补充",
      "categoryId": 1,
      "categoryName": "历史文献",
      "tags": [
        {
          "id": 3,
          "name": "向警予"
        },
        {
          "id": 2,
          "name": "历史文献"
        },
        {
          "id": 4,
          "name": "女性革命家"
        }
      ],
      "updatedAt": "2023-07-01T13:00:00Z"
    },
    "timestamp": "2023-07-01T13:00:00Z"
  }
  ```
- **错误码**：
  - 20001：资源不存在
  - 30001：权限不足
  - 40001：参数验证失败

#### 6.5.6 删除知识库文件

- **接口**：`DELETE /api/v1/knowledge/files/{id}`
- **描述**：删除指定知识库文件
- **路径参数**：
  - `id`：文件ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 204,
    "message": "文件删除成功",
    "data": null,
    "timestamp": "2023-07-01T14:00:00Z"
  }
  ```
- **错误码**：
  - 20001：资源不存在
  - 30001：权限不足

#### 6.5.7 下载知识库文件

- **接口**：`GET /api/v1/knowledge/files/{id}/download`
- **描述**：下载指定知识库文件
- **路径参数**：
  - `id`：文件ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  - 文件二进制数据流
  - 响应头：
    ```
    Content-Type: application/pdf
    Content-Disposition: attachment; filename="xiangjingyu-collection.pdf"
    ```
- **错误码**：
  - 20001：资源不存在
  - 30001：权限不足

### 6.6 数据查询接口

#### 6.6.1 获取数据源列表

- **接口**：`GET /api/v1/data-query/sources`
- **描述**：获取可用的数据源列表
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "name": "家族历史数据库",
        "description": "包含家族历史相关的结构化数据",
        "type": "internal",
        "status": "active"
      },
      {
        "id": 2,
        "name": "革命历史档案库",
        "description": "包含革命历史相关的档案数据",
        "type": "internal",
        "status": "active"
      },
      {
        "id": 3,
        "name": "国家档案馆",
        "description": "国家档案馆数据接口",
        "type": "external",
        "status": "inactive"
      }
    ],
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

#### 6.6.2 执行数据查询

- **接口**：`POST /api/v1/data-query/execute`
- **描述**：执行数据查询
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "dataSourceId": 1,
    "queryContent": "查询蔡和森相关的历史事件",
    "queryParams": {
      "startYear": 1920,
      "endYear": 1930,
      "keywords": ["蔡和森", "中国共产党"]
    }
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "查询成功",
    "data": {
      "queryId": "q123456",
      "results": [
        {
          "id": "event1",
          "title": "蔡和森赴法勤工俭学",
          "date": "1920-10-07",
          "description": "蔡和森赴法国勤工俭学，开始接触马克思主义",
          "source": "《蔡和森传》",
          "relevance": 0.95
        },
        {
          "id": "event2",
          "title": "蔡和森致信毛泽东",
          "date": "1920-12-01",
          "description": "蔡和森致信毛泽东，提出'明目张胆正式成立一个中国共产党'的主张",
          "source": "《建党史料》",
          "relevance": 0.98
        },
        // 更多结果...
      ],
      "totalResults": 15,
      "executionTime": 1200
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：数据源不存在
  - 30001：权限不足
  - 40001：参数验证失败
  - 50002：查询执行失败

#### 6.6.3 获取查询历史

- **接口**：`GET /api/v1/data-query/history`
- **描述**：获取当前用户的查询历史
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **查询参数**：
  - `pageSize`：每页条数，默认10
  - `pageNumber`：页码，默认1
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "list": [
        {
          "id": "q123456",
          "dataSourceId": 1,
          "dataSourceName": "家族历史数据库",
          "queryContent": "查询蔡和森相关的历史事件",
          "resultCount": 15,
          "executionTime": 1200,
          "status": "completed",
          "createdAt": "2023-07-01T12:00:00Z"
        },
        // 更多查询历史...
      ],
      "pagination": {
        "total": 20,
        "pageSize": 10,
        "pageNumber": 1,
        "totalPages": 2
      }
    },
    "timestamp": "2023-07-01T13:00:00Z"
  }
  ```

#### 6.6.4 导出查询结果

- **接口**：`GET /api/v1/data-query/export/{queryId}`
- **描述**：导出指定查询的结果
- **路径参数**：
  - `queryId`：查询ID
- **查询参数**：
  - `format`：导出格式，可选值：csv、excel、pdf，默认excel
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  - 文件二进制数据流
  - 响应头：
    ```
    Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    Content-Disposition: attachment; filename="query-results-q123456.xlsx"
    ```
- **错误码**：
  - 20001：查询不存在
  - 30001：权限不足

### 6.7 AI助手接口

#### 6.7.1 获取AI助手列表

- **接口**：`GET /api/v1/ai/assistants`
- **描述**：获取可用的AI助手列表
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **查询参数**：
  - `type`：助手类型，可选值：assistant（AI研究助手）、personal（个人专题助手）、data-query（数据查询助手）
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "name": "历史研究助手",
        "description": "专注于中国革命历史研究的AI助手",
        "type": "assistant",
        "tags": ["历史", "革命", "研究"],
        "status": "正常"
      },
      {
        "id": 2,
        "name": "蔡和森专题助手",
        "description": "专注于蔡和森生平和贡献研究的AI助手",
        "type": "personal",
        "tags": ["蔡和森", "人物研究"],
        "status": "正常"
      },
      {
        "id": 3,
        "name": "数据查询助手",
        "description": "协助用户进行数据查询的AI助手",
        "type": "data-query",
        "tags": ["数据", "查询"],
        "status": "正常"
      }
    ],
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

#### 6.7.2 获取AI助手详情

- **接口**：`GET /api/v1/ai/assistants/{id}`
- **描述**：获取指定AI助手的详细信息
- **路径参数**：
  - `id`：助手ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "name": "历史研究助手",
      "description": "专注于中国革命历史研究的AI助手，可以回答关于中国革命历史的各类问题，提供相关资料和研究建议。",
      "type": "assistant",
      "tags": ["历史", "革命", "研究"],
      "initialMessage": "您好，我是历史研究助手，专注于中国革命历史研究。请问有什么我可以帮助您的？",
      "status": "正常",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-06-01T00:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：资源不存在

#### 6.7.3 发送对话消息

- **接口**：`POST /api/v1/ai/chat`
- **描述**：向AI助手发送对话消息
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "assistantId": 1,
    "sessionId": "s123456",
    "message": "请介绍一下蔡和森对中国共产党创建的贡献"
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "messageId": "m123456",
      "assistantId": 1,
      "sessionId": "s123456",
      "role": "assistant",
      "content": "蔡和森是中国共产党的创建者之一，他对中国共产党的创建有以下几个重要贡献：\n\n1. 最早提出建党主张：1920年8月，蔡和森在致毛泽东的信中明确提出"明目张胆正式成立一个中国共产党"的主张，这是中国共产党人最早关于建党的明确表述。\n\n2. 系统阐述建党思想：蔡和森在信中系统阐述了建党的必要性、党的性质、组织原则和奋斗目标等问题，为中国共产党的创建提供了理论基础。\n\n3. 参与建党实践：蔡和森积极参与建党的实际工作，是上海共产主义小组的主要成员，参与了中国共产党第一次全国代表大会的筹备工作。\n\n4. 推动马克思主义传播：蔡和森在法国期间系统学习马克思主义，并通过翻译和撰写文章向中国介绍马克思主义理论，为中国共产党的思想基础做出了贡献。\n\n这些贡献使蔡和森成为中国共产党创建过程中的重要人物，他的建党思想和实践对中国共产党的成立产生了深远影响。",
      "createdAt": "2023-07-01T12:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：助手不存在
  - 30001：权限不足
  - 40001：参数验证失败
  - 50003：AI服务调用失败

#### 6.7.4 获取对话历史

- **接口**：`GET /api/v1/ai/chat/history`
- **描述**：获取与AI助手的对话历史
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **查询参数**：
  - `assistantId`：助手ID，可选
  - `sessionId`：会话ID，可选
  - `pageSize`：每页条数，默认20
  - `pageNumber`：页码，默认1
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "sessions": [
        {
          "sessionId": "s123456",
          "assistantId": 1,
          "assistantName": "历史研究助手",
          "lastMessage": "请介绍一下蔡和森对中国共产党创建的贡献",
          "lastMessageTime": "2023-07-01T12:00:00Z",
          "messageCount": 5
        },
        // 更多会话...
      ],
      "messages": [
        {
          "messageId": "m123451",
          "sessionId": "s123456",
          "role": "assistant",
          "content": "您好，我是历史研究助手，专注于中国革命历史研究。请问有什么我可以帮助您的？",
          "createdAt": "2023-07-01T11:50:00Z"
        },
        {
          "messageId": "m123452",
          "sessionId": "s123456",
          "role": "user",
          "content": "你好，我想了解一下蔡和森的生平",
          "createdAt": "2023-07-01T11:51:00Z"
        },
        {
          "messageId": "m123453",
          "sessionId": "s123456",
          "role": "assistant",
          "content": "蔡和森（1895-1931），湖南长沙人，中国共产党的创始人之一...",
          "createdAt": "2023-07-01T11:51:30Z"
        },
        {
          "messageId": "m123454",
          "sessionId": "s123456",
          "role": "user",
          "content": "请介绍一下蔡和森对中国共产党创建的贡献",
          "createdAt": "2023-07-01T12:00:00Z"
        },
        {
          "messageId": "m123455",
          "sessionId": "s123456",
          "role": "assistant",
          "content": "蔡和森是中国共产党的创建者之一，他对中国共产党的创建有以下几个重要贡献：...",
          "createdAt": "2023-07-01T12:00:30Z"
        }
      ],
      "pagination": {
        "total": 5,
        "pageSize": 20,
        "pageNumber": 1,
        "totalPages": 1
      }
    },
    "timestamp": "2023-07-01T12:05:00Z"
  }
  ```

#### 6.7.5 创建新会话

- **接口**：`POST /api/v1/ai/chat/sessions`
- **描述**：创建与AI助手的新会话
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "assistantId": 1
  }
  ```
- **响应**：
  ```json
  {
    "code": 201,
    "message": "会话创建成功",
    "data": {
      "sessionId": "s123457",
      "assistantId": 1,
      "assistantName": "历史研究助手",
      "initialMessage": {
        "messageId": "m123456",
        "role": "assistant",
        "content": "您好，我是历史研究助手，专注于中国革命历史研究。请问有什么我可以帮助您的？",
        "createdAt": "2023-07-01T13:00:00Z"
      },
      "createdAt": "2023-07-01T13:00:00Z"
    },
    "timestamp": "2023-07-01T13:00:00Z"
  }
  ```
- **错误码**：
  - 20001：助手不存在
  - 30001：权限不足

#### 6.7.6 删除会话

- **接口**：`DELETE /api/v1/ai/chat/sessions/{sessionId}`
- **描述**：删除指定的对话会话
- **路径参数**：
  - `sessionId`：会话ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 204,
    "message": "会话删除成功",
    "data": null,
    "timestamp": "2023-07-01T14:00:00Z"
  }
  ```
- **错误码**：
  - 20001：会话不存在
  - 30001：权限不足

### 6.8 系统管理接口

#### 6.8.1 用户管理接口

##### 6.8.1.1 获取用户列表

- **接口**：`GET /api/v1/admin/users`
- **描述**：获取系统用户列表
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **查询参数**：
  - `pageSize`：每页条数，默认10
  - `pageNumber`：页码，默认1
  - `status`：用户状态，可选值：1-正常，0-禁用
  - `q`：搜索关键词，可搜索用户名、邮箱、手机号
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "list": [
        {
          "id": 1,
          "username": "admin",
          "email": "<EMAIL>",
          "phone": "13800138000",
          "realName": "管理员",
          "avatar": "https://example.com/avatar.jpg",
          "status": 1,
          "roleId": 1,
          "roleName": "管理员",
          "lastLogin": "2023-07-01T10:00:00Z",
          "createdAt": "2023-01-01T00:00:00Z"
        },
        {
          "id": 2,
          "username": "user1",
          "email": "<EMAIL>",
          "phone": "13800138001",
          "realName": "用户1",
          "avatar": "https://example.com/avatar1.jpg",
          "status": 1,
          "roleId": 2,
          "roleName": "研究员",
          "lastLogin": "2023-06-30T10:00:00Z",
          "createdAt": "2023-01-02T00:00:00Z"
        },
        // 更多用户...
      ],
      "pagination": {
        "total": 50,
        "pageSize": 10,
        "pageNumber": 1,
        "totalPages": 5
      }
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 30001：权限不足

##### 6.8.1.2 获取用户详情

- **接口**：`GET /api/v1/admin/users/{id}`
- **描述**：获取指定用户的详细信息
- **路径参数**：
  - `id`：用户ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 2,
      "username": "user1",
      "email": "<EMAIL>",
      "phone": "13800138001",
      "realName": "用户1",
      "avatar": "https://example.com/avatar1.jpg",
      "status": 1,
      "password": "********",
      "roleId": 2,
      "roleName": "研究员",
      "lastLogin": "2023-06-30T10:00:00Z",
      "createdAt": "2023-01-02T00:00:00Z",
      "updatedAt": "2023-06-01T00:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：用户不存在
  - 30001：权限不足

##### 6.8.1.3 创建用户

- **接口**：`POST /api/v1/admin/users`
- **描述**：创建新用户
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "username": "newuser",
    "password": "password123",
    "email": "<EMAIL>",
    "phone": "13800138002",
    "realName": "新用户",
    "status": 1,
    "roleId": 2
  }
  ```
- **响应**：
  ```json
  {
    "code": 201,
    "message": "用户创建成功",
    "data": {
      "id": 3,
      "username": "newuser",
      "email": "<EMAIL>",
      "phone": "13800138002",
      "realName": "新用户",
      "status": 1,
      "roleId": 2,
      "roleName": "研究员",
      "createdAt": "2023-07-01T12:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20002：用户名已存在
  - 30001：权限不足
  - 40001：参数验证失败

##### 6.8.1.4 更新用户

- **接口**：`PUT /api/v1/admin/users/{id}`
- **描述**：更新指定用户的信息
- **路径参数**：
  - `id`：用户ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "email": "<EMAIL>",
    "phone": "13800138003",
    "realName": "新用户（已更新）",
    "status": 1,
    "roleId": 3
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "用户更新成功",
    "data": {
      "id": 3,
      "username": "newuser",
      "email": "<EMAIL>",
      "phone": "13800138003",
      "realName": "新用户（已更新）",
      "status": 1,
      "roleId": 3,
      "roleName": "编辑",
      "updatedAt": "2023-07-01T13:00:00Z"
    },
    "timestamp": "2023-07-01T13:00:00Z"
  }
  ```
- **错误码**：
  - 20001：用户不存在
  - 30001：权限不足
  - 40001：参数验证失败

##### 6.8.1.5 删除用户

- **接口**：`DELETE /api/v1/admin/users/{id}`
- **描述**：删除指定用户
- **路径参数**：
  - `id`：用户ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 204,
    "message": "用户删除成功",
    "data": null,
    "timestamp": "2023-07-01T14:00:00Z"
  }
  ```
- **错误码**：
  - 20001：用户不存在
  - 30001：权限不足
  - 40003：系统用户不可删除

##### 6.8.1.6 重置用户密码

- **接口**：`POST /api/v1/admin/users/{id}/reset-password`
- **描述**：重置指定用户的密码
- **路径参数**：
  - `id`：用户ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "newPassword": "newpassword123"
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "密码重置成功",
    "data": null,
    "timestamp": "2023-07-01T15:00:00Z"
  }
  ```
- **错误码**：
  - 20001：用户不存在
  - 30001：权限不足
  - 40001：参数验证失败

#### 6.8.2 角色管理接口

##### ******* 获取角色列表

- **接口**：`GET /api/v1/admin/roles`
- **描述**：获取系统角色列表
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "name": "管理员",
        "description": "系统管理员，拥有所有权限",
        "isSystem": 1,
        "createdAt": "2023-01-01T00:00:00Z"
      },
      {
        "id": 2,
        "name": "研究员",
        "description": "研究人员，拥有内容查看和编辑权限",
        "isSystem": 0,
        "createdAt": "2023-01-01T00:00:00Z"
      },
      {
        "id": 3,
        "name": "编辑",
        "description": "内容编辑，拥有内容编辑权限",
        "isSystem": 0,
        "createdAt": "2023-01-01T00:00:00Z"
      },
      {
        "id": 4,
        "name": "初级访问者",
        "description": "新注册用户默认角色，拥有基本访问权限",
        "isSystem": 1,
        "createdAt": "2023-01-01T00:00:00Z"
      }
    ],
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 30001：权限不足

##### ******* 获取角色详情

- **接口**：`GET /api/v1/admin/roles/{id}`
- **描述**：获取指定角色的详细信息
- **路径参数**：
  - `id`：角色ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 2,
      "name": "研究员",
      "description": "研究人员，拥有内容查看和编辑权限",
      "isSystem": 0,
      "permissions": [
        {
          "id": 1,
          "name": "内容查看",
          "code": "content:view",
          "module": "content"
        },
        {
          "id": 2,
          "name": "内容编辑",
          "code": "content:edit",
          "module": "content"
        },
        {
          "id": 5,
          "name": "知识库查看",
          "code": "knowledge:view",
          "module": "knowledge"
        },
        {
          "id": 6,
          "name": "知识库上传",
          "code": "knowledge:upload",
          "module": "knowledge"
        }
      ],
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：角色不存在
  - 30001：权限不足

##### ******* 创建角色

- **接口**：`POST /api/v1/admin/roles`
- **描述**：创建新角色
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "name": "访客",
    "description": "临时访客，只有查看权限",
    "permissionIds": [1, 5]
  }
  ```
- **响应**：
  ```json
  {
    "code": 201,
    "message": "角色创建成功",
    "data": {
      "id": 5,
      "name": "访客",
      "description": "临时访客，只有查看权限",
      "isSystem": 0,
      "permissions": [
        {
          "id": 1,
          "name": "内容查看",
          "code": "content:view",
          "module": "content"
        },
        {
          "id": 5,
          "name": "知识库查看",
          "code": "knowledge:view",
          "module": "knowledge"
        }
      ],
      "createdAt": "2023-07-01T12:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20002：角色名已存在
  - 30001：权限不足
  - 40001：参数验证失败

##### ******* 更新角色

- **接口**：`PUT /api/v1/admin/roles/{id}`
- **描述**：更新指定角色的信息
- **路径参数**：
  - `id`：角色ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "name": "高级访客",
    "description": "高级临时访客，有更多查看权限",
    "permissionIds": [1, 5, 9]
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "角色更新成功",
    "data": {
      "id": 5,
      "name": "高级访客",
      "description": "高级临时访客，有更多查看权限",
      "isSystem": 0,
      "permissions": [
        {
          "id": 1,
          "name": "内容查看",
          "code": "content:view",
          "module": "content"
        },
        {
          "id": 5,
          "name": "知识库查看",
          "code": "knowledge:view",
          "module": "knowledge"
        },
        {
          "id": 9,
          "name": "数据查询",
          "code": "data:query",
          "module": "data"
        }
      ],
      "updatedAt": "2023-07-01T13:00:00Z"
    },
    "timestamp": "2023-07-01T13:00:00Z"
  }
  ```
- **错误码**：
  - 20001：角色不存在
  - 20002：角色名已存在
  - 30001：权限不足
  - 40001：参数验证失败
  - 40003：系统角色不可修改

##### ******* 删除角色

- **接口**：`DELETE /api/v1/admin/roles/{id}`
- **描述**：删除指定角色
- **路径参数**：
  - `id`：角色ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 204,
    "message": "角色删除成功",
    "data": null,
    "timestamp": "2023-07-01T14:00:00Z"
  }
  ```
- **错误码**：
  - 20001：角色不存在
  - 30001：权限不足
  - 40003：系统角色不可删除
  - 40004：角色已分配给用户，不可删除

##### ******* 获取权限列表

- **接口**：`GET /api/v1/admin/permissions`
- **描述**：获取系统权限列表
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **查询参数**：
  - `module`：权限模块，可选
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "name": "内容查看",
        "code": "content:view",
        "description": "查看内容的权限",
        "module": "content"
      },
      {
        "id": 2,
        "name": "内容编辑",
        "code": "content:edit",
        "description": "编辑内容的权限",
        "module": "content"
      },
      {
        "id": 3,
        "name": "内容删除",
        "code": "content:delete",
        "description": "删除内容的权限",
        "module": "content"
      },
      // 更多权限...
    ],
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 30001：权限不足

#### 6.8.3 留言管理接口

##### ******* 获取留言列表

- **接口**：`GET /api/v1/admin/messages`
- **描述**：获取系统留言列表
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **查询参数**：
  - `pageSize`：每页条数，默认10
  - `pageNumber`：页码，默认1
  - `status`：留言状态，可选值：pending-待审核，approved-已通过，rejected-已驳回
  - `startDate`：开始日期，格式：YYYY-MM-DD
  - `endDate`：结束日期，格式：YYYY-MM-DD
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "list": [
        {
          "id": 1,
          "content": "这是一条留言内容",
          "userId": 2,
          "userName": "user1",
          "contentId": 1,
          "contentTitle": "蔡和森生平",
          "status": "pending",
          "createdAt": "2023-07-01T10:00:00Z"
        },
        {
          "id": 2,
          "content": "这是另一条留言内容",
          "userId": 3,
          "userName": "user2",
          "contentId": 2,
          "contentTitle": "向警予生平",
          "status": "approved",
          "createdAt": "2023-07-01T11:00:00Z"
        },
        // 更多留言...
      ],
      "pagination": {
        "total": 50,
        "pageSize": 10,
        "pageNumber": 1,
        "totalPages": 5
      }
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 30001：权限不足

##### 6.8.3.2 获取留言详情

- **接口**：`GET /api/v1/admin/messages/{id}`
- **描述**：获取指定留言的详细信息
- **路径参数**：
  - `id`：留言ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "content": "这是一条留言内容，我想了解更多关于蔡和森的事迹。",
      "userId": 2,
      "userName": "user1",
      "userEmail": "<EMAIL>",
      "contentId": 1,
      "contentTitle": "蔡和森生平",
      "contentUrl": "/personal-topic/caihesen",
      "status": "pending",
      "rejectReason": null,
      "createdAt": "2023-07-01T10:00:00Z",
      "updatedAt": "2023-07-01T10:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：留言不存在
  - 30001：权限不足

##### 6.8.3.3 审核留言

- **接口**：`PUT /api/v1/admin/messages/{id}/review`
- **描述**：审核指定留言
- **路径参数**：
  - `id`：留言ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "status": "approved",
    "rejectReason": null
  }
  ```
  或
  ```json
  {
    "status": "rejected",
    "rejectReason": "内容不符合规范"
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "留言审核成功",
    "data": {
      "id": 1,
      "status": "approved",
      "updatedAt": "2023-07-01T13:00:00Z"
    },
    "timestamp": "2023-07-01T13:00:00Z"
  }
  ```
- **错误码**：
  - 20001：留言不存在
  - 30001：权限不足
  - 40001：参数验证失败

##### 6.8.3.4 删除留言

- **接口**：`DELETE /api/v1/admin/messages/{id}`
- **描述**：删除指定留言
- **路径参数**：
  - `id`：留言ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 204,
    "message": "留言删除成功",
    "data": null,
    "timestamp": "2023-07-01T14:00:00Z"
  }
  ```
- **错误码**：
  - 20001：留言不存在
  - 30001：权限不足

#### 6.8.4 AI管理接口

##### 6.8.4.1 获取AI助手列表

- **接口**：`GET /api/v1/admin/ai`
- **描述**：获取系统AI助手列表
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **查询参数**：
  - `type`：助手类型，可选值：assistant（AI研究助手）、personal（个人专题助手）、data-query（数据查询助手）、knowledge-file（知识库文件分析助手）
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "name": "历史研究助手",
        "description": "专注于中国革命历史研究的AI助手",
        "type": "assistant",
        "status": "正常",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-06-01T00:00:00Z"
      },
      {
        "id": 2,
        "name": "蔡和森专题助手",
        "description": "专注于蔡和森生平和贡献研究的AI助手",
        "type": "personal",
        "status": "正常",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-06-01T00:00:00Z"
      },
      {
        "id": 3,
        "name": "数据查询助手",
        "description": "协助用户进行数据查询的AI助手",
        "type": "data-query",
        "status": "正常",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-06-01T00:00:00Z"
      },
      {
        "id": 4,
        "name": "知识库文件分析助手",
        "description": "分析知识库文件内容的AI助手",
        "type": "knowledge-file",
        "status": "正常",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-06-01T00:00:00Z"
      }
    ],
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 30001：权限不足

##### ******* 获取AI助手详情

- **接口**：`GET /api/v1/admin/ai/{id}`
- **描述**：获取指定AI助手的详细信息
- **路径参数**：
  - `id`：助手ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "name": "历史研究助手",
      "description": "专注于中国革命历史研究的AI助手，可以回答关于中国革命历史的各类问题，提供相关资料和研究建议。",
      "type": "assistant",
      "apiKey": "sk-********",
      "apiEndpoint": "https://api.dify.ai/v1",
      "appId": "app123456",
      "appCode": "code123456",
      "initialMessage": "您好，我是历史研究助手，专注于中国革命历史研究。请问有什么我可以帮助您的？",
      "status": "正常",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-06-01T00:00:00Z",
      "lastUpdated": "2023-06-01T00:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：助手不存在
  - 30001：权限不足

##### ******* 创建AI助手

- **接口**：`POST /api/v1/admin/ai`
- **描述**：创建新的AI助手
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "name": "新研究助手",
    "description": "新的历史研究AI助手",
    "type": "assistant",
    "apiKey": "sk-abcdef123456",
    "apiEndpoint": "https://api.dify.ai/v1",
    "appId": "app123456",
    "appCode": "code123456",
    "initialMessage": "您好，我是新研究助手，有什么可以帮助您的？",
    "status": "正常"
  }
  ```
- **响应**：
  ```json
  {
    "code": 201,
    "message": "AI助手创建成功",
    "data": {
      "id": 5,
      "name": "新研究助手",
      "description": "新的历史研究AI助手",
      "type": "assistant",
      "status": "正常",
      "createdAt": "2023-07-01T12:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20002：助手名已存在
  - 30001：权限不足
  - 40001：参数验证失败

##### ******* 更新AI助手

- **接口**：`PUT /api/v1/admin/ai/{id}`
- **描述**：更新指定AI助手的信息
- **路径参数**：
  - `id`：助手ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "name": "新研究助手（已更新）",
    "description": "更新后的历史研究AI助手",
    "apiKey": "sk-abcdef123456",
    "apiEndpoint": "https://api.dify.ai/v1",
    "appId": "app123456",
    "appCode": "code123456",
    "initialMessage": "您好，我是更新后的研究助手，有什么可以帮助您的？",
    "status": "正常"
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "AI助手更新成功",
    "data": {
      "id": 5,
      "name": "新研究助手（已更新）",
      "description": "更新后的历史研究AI助手",
      "type": "assistant",
      "status": "正常",
      "updatedAt": "2023-07-01T13:00:00Z",
      "lastUpdated": "2023-07-01T13:00:00Z"
    },
    "timestamp": "2023-07-01T13:00:00Z"
  }
  ```
- **错误码**：
  - 20001：助手不存在
  - 20002：助手名已存在
  - 30001：权限不足
  - 40001：参数验证失败

##### ******* 删除AI助手

- **接口**：`DELETE /api/v1/admin/ai/{id}`
- **描述**：删除指定AI助手
- **路径参数**：
  - `id`：助手ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 204,
    "message": "AI助手删除成功",
    "data": null,
    "timestamp": "2023-07-01T14:00:00Z"
  }
  ```
- **错误码**：
  - 20001：助手不存在
  - 30001：权限不足
  - 40003：系统助手不可删除

#### 6.8.5 系统配置接口

##### ******* 获取系统配置

- **接口**：`GET /api/v1/admin/config`
- **描述**：获取系统配置信息
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "security": {
        "passwordPolicy": {
          "minLength": 8,
          "requireUppercase": true,
          "requireLowercase": true,
          "requireNumbers": true,
          "requireSpecialChars": true
        },
        "loginAttempts": {
          "maxAttempts": 5,
          "lockoutDuration": 30
        }
      },
      "system": {
        "siteName": "和富家族研究平台",
        "siteDescription": "传承红色基因，弘扬革命精神",
        "contactEmail": "<EMAIL>",
        "fileUploadLimit": 10485760
      }
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 30001：权限不足

##### ******* 更新系统配置

- **接口**：`PUT /api/v1/admin/config`
- **描述**：更新系统配置信息
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **请求体**：
  ```json
  {
    "security": {
      "passwordPolicy": {
        "minLength": 10,
        "requireUppercase": true,
        "requireLowercase": true,
        "requireNumbers": true,
        "requireSpecialChars": true
      },
      "loginAttempts": {
        "maxAttempts": 3,
        "lockoutDuration": 60
      }
    },
    "system": {
      "siteName": "和富家族研究平台",
      "siteDescription": "传承红色基因，弘扬革命精神",
      "contactEmail": "<EMAIL>",
      "fileUploadLimit": 20971520
    }
  }
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "系统配置更新成功",
    "data": {
      "updatedAt": "2023-07-01T13:00:00Z"
    },
    "timestamp": "2023-07-01T13:00:00Z"
  }
  ```
- **错误码**：
  - 30001：权限不足
  - 40001：参数验证失败

### 6.9 通知接口

#### 6.9.1 获取通知列表

- **接口**：`GET /api/v1/notifications`
- **描述**：获取当前用户的通知列表
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **查询参数**：
  - `pageSize`：每页条数，默认10
  - `pageNumber`：页码，默认1
  - `isRead`：是否已读，可选值：0-未读，1-已读
  - `type`：通知类型，可选值：system-系统通知，file-文件通知，comment-评论通知
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "list": [
        {
          "id": 1,
          "title": "文件上传成功",
          "content": "您上传的文件《蔡和森选集》已成功上传并通过审核。",
          "type": "file",
          "isRead": 0,
          "relatedId": 1,
          "createdAt": "2023-07-01T10:00:00Z"
        },
        {
          "id": 2,
          "title": "评论已通过审核",
          "content": "您在《蔡和森生平》下的评论已通过审核。",
          "type": "comment",
          "isRead": 1,
          "relatedId": 1,
          "createdAt": "2023-07-01T09:00:00Z"
        },
        {
          "id": 3,
          "title": "系统维护通知",
          "content": "系统将于2023年7月10日进行维护升级，届时可能会有短暂服务中断。",
          "type": "system",
          "isRead": 0,
          "relatedId": null,
          "createdAt": "2023-07-01T08:00:00Z"
        }
      ],
      "pagination": {
        "total": 20,
        "pageSize": 10,
        "pageNumber": 1,
        "totalPages": 2
      },
      "unreadCount": 5
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

#### 6.9.2 获取通知详情

- **接口**：`GET /api/v1/notifications/{id}`
- **描述**：获取指定通知的详细信息
- **路径参数**：
  - `id`：通知ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "title": "文件上传成功",
      "content": "您上传的文件《蔡和森选集》已成功上传并通过审核。您现在可以在知识库中查看该文件，或者点击下方链接直接访问：\n\nhttps://example.com/knowledge/files/1",
      "type": "file",
      "isRead": 1,
      "relatedId": 1,
      "relatedUrl": "/knowledge/files/1",
      "createdAt": "2023-07-01T10:00:00Z",
      "updatedAt": "2023-07-01T12:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：通知不存在
  - 30001：权限不足

#### 6.9.3 标记通知为已读

- **接口**：`PUT /api/v1/notifications/{id}/read`
- **描述**：标记指定通知为已读
- **路径参数**：
  - `id`：通知ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "标记成功",
    "data": {
      "id": 1,
      "isRead": 1,
      "updatedAt": "2023-07-01T12:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：通知不存在
  - 30001：权限不足

#### 6.9.4 标记所有通知为已读

- **接口**：`PUT /api/v1/notifications/read-all`
- **描述**：标记当前用户的所有通知为已读
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 200,
    "message": "标记成功",
    "data": {
      "count": 5,
      "updatedAt": "2023-07-01T12:00:00Z"
    },
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```

#### 6.9.5 删除通知

- **接口**：`DELETE /api/v1/notifications/{id}`
- **描述**：删除指定通知
- **路径参数**：
  - `id`：通知ID
- **请求头**：
  ```
  Authorization: Bearer {token}
  ```
- **响应**：
  ```json
  {
    "code": 204,
    "message": "删除成功",
    "data": null,
    "timestamp": "2023-07-01T12:00:00Z"
  }
  ```
- **错误码**：
  - 20001：通知不存在
  - 30001：权限不足

## 7. API安全性考虑

### 7.1 身份验证与授权

1. **JWT令牌**：
   - 使用JWT进行身份验证，令牌包含用户ID、角色和权限信息
   - 令牌有效期为24小时，过期后需要使用刷新令牌获取新令牌
   - 令牌使用HMAC SHA-256算法签名，确保不被篡改

2. **权限控制**：
   - 基于角色的访问控制（RBAC）
   - 每个API接口都有对应的权限要求
   - 用户只能访问其角色拥有权限的接口

3. **敏感操作保护**：
   - 敏感操作（如删除资源、修改密码）需要二次验证
   - 管理员操作记录日志，便于审计

### 7.2 数据安全

1. **数据传输安全**：
   - 所有API请求通过HTTPS加密传输
   - 敏感数据（如密码）在传输过程中加密

2. **数据存储安全**：
   - 密码使用bcrypt算法加密存储
   - API密钥等敏感信息加密存储
   - 个人敏感信息脱敏处理

3. **输入验证**：
   - 所有用户输入进行严格验证
   - 防止SQL注入、XSS等攻击

### 7.3 API限流与防护

1. **请求限流**：
   - 基于IP的限流：每IP每分钟最多60次请求
   - 基于用户的限流：每用户每分钟最多120次请求
   - 特定接口的限流：敏感接口有更严格的限制

2. **防护措施**：
   - 防止暴力破解：登录失败次数限制
   - 防止CSRF攻击：使用CSRF令牌
   - 防止重放攻击：请求时间戳验证

## 8. API版本管理

### 8.1 版本控制策略

1. **URL路径版本控制**：
   - 在URL路径中包含版本号：`/api/v1/resource`
   - 主版本号变更表示不兼容的API变更
   - 次版本号变更表示向后兼容的功能添加

2. **版本生命周期**：
   - 新版本发布后，旧版本至少维护6个月
   - 版本废弃前至少提前3个月通知
   - 废弃版本的API会返回410 Gone状态码

### 8.2 兼容性维护

1. **向后兼容原则**：
   - 不删除字段，只添加新字段
   - 不改变字段类型和语义
   - 不改变错误码含义

2. **API变更通知**：
   - 重大变更提前通知
   - 提供迁移指南
   - 在响应头中提示新版本信息

## 9. 总结

本文档详细描述了和富家族研究平台的API设计，包括API架构、接口规范、认证机制、错误处理和具体接口定义等内容。API设计遵循RESTful风格，提供简单、一致、安全的接口，满足前端应用与后端服务的交互需求。

API接口按功能模块组织，包括用户管理、内容管理、活动管理、知识库管理、数据查询、AI助手、系统管理和通知等模块。每个接口都有详细的请求参数、响应格式和错误码说明，便于开发人员理解和使用。

在安全性方面，API采用JWT进行身份验证，基于角色的访问控制进行授权，并实施了多种安全措施保护数据传输和存储安全。同时，API实施了版本控制策略，确保系统升级时的平滑过渡。

本文档将随系统开发和迭代不断更新，以反映最新的API设计和变更。