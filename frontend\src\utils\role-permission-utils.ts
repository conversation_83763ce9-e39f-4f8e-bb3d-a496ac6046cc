/**
 * 角色权限工具函数
 * 
 * 提供与角色权限相关的工具函数
 */

import axios from 'axios';

// 获取API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5001/api';

// 权限类型
export interface Permission {
  id: number;
  code: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
}

/**
 * 获取所有权限
 * @returns 权限列表
 */
export async function getAllPermissions(): Promise<Permission[]> {
  try {
    const token = localStorage.getItem('hefamily_token');
    if (!token) {
      throw new Error('未找到认证token');
    }

    const response = await axios.get(`${API_BASE_URL}/permissions`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('获取所有权限响应:', response.data);
    return response.data.data || [];
  } catch (error) {
    console.error('获取所有权限失败:', error);
    throw error;
  }
}

/**
 * 获取角色的权限
 * @param roleId 角色ID
 * @returns 权限列表
 */
export async function getRolePermissions(roleId: number): Promise<Permission[]> {
  try {
    const token = localStorage.getItem('hefamily_token');
    if (!token) {
      throw new Error('未找到认证token');
    }

    console.log(`获取角色权限: roleId=${roleId}`);
    const response = await axios.get(`${API_BASE_URL}/roles/${roleId}/permissions`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('获取角色权限响应:', response.data);
    return response.data.data || [];
  } catch (error) {
    console.error('获取角色权限失败:', error);
    throw error;
  }
}

/**
 * 设置角色权限
 * @param roleId 角色ID
 * @param permissionIds 权限ID列表
 * @returns 操作结果
 */
export async function setRolePermissions(roleId: number, permissionIds: number[]): Promise<{ success: boolean; message: string }> {
  try {
    console.log(`调用 setRolePermissions API: roleId=${roleId}, permissionIds=`, permissionIds);
    
    const token = localStorage.getItem('hefamily_token');
    if (!token) {
      throw new Error('未找到认证token');
    }

    // 根据后端API路由定义，应该使用PUT方法
    const response = await axios.put(
      `${API_BASE_URL}/roles/${roleId}/permissions`,
      { permissions: permissionIds },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      }
    );

    console.log('设置角色权限API响应:', response.data);
    return response.data;
  } catch (error) {
    console.error('设置角色权限失败:', error);
    throw error;
  }
}
