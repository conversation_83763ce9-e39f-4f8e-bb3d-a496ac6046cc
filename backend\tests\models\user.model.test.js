/**
 * 用户模型测试
 *
 * 测试用户模型的字段、验证和方法
 */

const { User, Role } = require('../../src/models');
const bcrypt = require('bcryptjs');

describe('用户模型', () => {
  let testUser;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    try {
      // 设置测试数据库
      await global.setupTestDatabase();

      // 创建测试用户
      testUser = await User.create({
        username: 'modeluser',
        password: 'Password123!',
        email: '<EMAIL>',
        phone: '13800000010',
        role: 'basic_user',
        is_active: true
      });

      console.log('测试用户创建成功:', testUser.id);
    } catch (error) {
      console.error('创建测试用户失败:', error);
      console.error('错误详情:', error.message);
      if (error.original) {
        console.error('原始错误:', error.original.message);
      }
    }
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    try {
      if (testUser && testUser.id) {
        await User.destroy({ where: { id: testUser.id } });
        console.log('测试用户删除成功');
      }

      // 清理测试数据库
      await global.cleanupTestDatabase();
    } catch (error) {
      console.error('清理测试数据失败:', error);
      console.error('错误详情:', error.message);
    }
  });

  // 测试用户创建
  describe('用户创建', () => {
    test('应该成功创建用户', async () => {
      const user = await User.findByPk(testUser.id);

      expect(user).toBeDefined();
      expect(user.username).toBe('modeluser');
      expect(user.email).toBe('<EMAIL>');
      expect(user.phone).toBe('13800000010');
      expect(user.role).toBe('basic_user');
      expect(user.is_active).toBe(true);
    });

    test('应该对密码进行哈希处理', async () => {
      const user = await User.findByPk(testUser.id);

      // 密码应该被哈希处理，不等于原始密码
      expect(user.password).not.toBe('Password123!');

      // 验证哈希密码
      const isMatch = await bcrypt.compare('Password123!', user.password);
      expect(isMatch).toBe(true);
    });

    test('不应该创建重复用户名的用户', async () => {
      try {
        await User.create({
          username: 'modeluser', // 重复的用户名
          password: 'Password123!',
          email: '<EMAIL>',
          phone: '13800000011',
          role: 'basic_user',
          is_active: true
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建重复邮箱的用户', async () => {
      try {
        await User.create({
          username: 'anotheruser',
          password: 'Password123!',
          email: '<EMAIL>', // 重复的邮箱
          phone: '13800000011',
          role: 'basic_user',
          is_active: true
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建无效邮箱的用户', async () => {
      try {
        await User.create({
          username: 'invaliduser',
          password: 'Password123!',
          email: 'invalid-email', // 无效的邮箱
          phone: '13800000011',
          role: 'basic_user',
          is_active: true
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建无效手机号的用户', async () => {
      try {
        await User.create({
          username: 'invaliduser',
          password: 'Password123!',
          email: '<EMAIL>',
          phone: '123456', // 无效的手机号
          role: 'basic_user',
          is_active: true
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  // 测试用户更新
  describe('用户更新', () => {
    test('应该成功更新用户信息', async () => {
      await testUser.update({
        email: '<EMAIL>',
        phone: '13800000012'
      });

      const updatedUser = await User.findByPk(testUser.id);
      expect(updatedUser.email).toBe('<EMAIL>');
      expect(updatedUser.phone).toBe('13800000012');

      // 恢复原始数据
      await testUser.update({
        email: '<EMAIL>',
        phone: '13800000010'
      });
    });

    test('应该对更新的密码进行哈希处理', async () => {
      await testUser.update({
        password: 'NewPassword123!'
      });

      const updatedUser = await User.findByPk(testUser.id);

      // 密码应该被哈希处理，不等于原始密码
      expect(updatedUser.password).not.toBe('NewPassword123!');

      // 验证哈希密码
      const isMatch = await bcrypt.compare('NewPassword123!', updatedUser.password);
      expect(isMatch).toBe(true);

      // 恢复原始密码
      await testUser.update({
        password: 'Password123!'
      });
    });
  });

  // 测试用户关联
  describe('用户关联', () => {
    test('用户应该关联到角色', async () => {
      // 检查用户模型是否有角色关联
      // 注意：关联名称是'userRole'而不是'role'
      expect(User.associations).toHaveProperty('userRole');
    });
  });

  // 测试用户实例方法
  describe('用户实例方法', () => {
    test('comparePassword方法应该正确比较密码', async () => {
      // 假设用户模型有comparePassword方法
      if (typeof testUser.comparePassword === 'function') {
        const isMatch = await testUser.comparePassword('Password123!');
        expect(isMatch).toBe(true);

        const isNotMatch = await testUser.comparePassword('WrongPassword');
        expect(isNotMatch).toBe(false);
      } else {
        // 如果没有该方法，跳过测试
        console.log('用户模型没有comparePassword方法，跳过测试');
      }
    });
  });

  // 测试用户类方法
  describe('用户类方法', () => {
    test('findByUsername方法应该返回正确的用户', async () => {
      // 假设用户模型有findByUsername静态方法
      if (typeof User.findByUsername === 'function') {
        const user = await User.findByUsername('modeluser');
        expect(user).toBeDefined();
        expect(user.id).toBe(testUser.id);

        const nonExistentUser = await User.findByUsername('nonexistent');
        expect(nonExistentUser).toBeNull();
      } else {
        // 如果没有该方法，跳过测试
        console.log('用户模型没有findByUsername方法，跳过测试');
      }
    });
  });
});
