/**
 * 直接查询数据库表
 */

// 加载环境变量
require('dotenv').config();

// 导入Sequelize
const { Sequelize } = require('sequelize');

// 创建Sequelize实例
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: 'mysql',
    logging: false
  }
);

// 查询数据库
async function queryDatabase() {
  try {
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查询知识库表
    const [knowledgeBases] = await sequelize.query('SELECT * FROM knowledge_bases');
    console.log('知识库表数据:');
    console.log(knowledgeBases);

    // 查询文件表
    const [files] = await sequelize.query('SELECT * FROM files');
    console.log('\n文件表数据:');
    console.log(files);

  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  }
}

// 执行查询
queryDatabase();
