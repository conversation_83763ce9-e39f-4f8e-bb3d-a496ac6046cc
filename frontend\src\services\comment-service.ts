/**
 * 评论服务
 * 
 * 提供与评论/留言相关的API调用
 */

import axios from 'axios';

// 获取API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5001/api';

// 评论状态类型
export type CommentStatus = 'pending' | 'approved' | 'rejected';

// 评论用户类型
export interface CommentUser {
  id: number;
  username: string;
  email: string;
}

// 评论类型
export interface Comment {
  id: string;
  user_id: string;
  user: CommentUser;
  content: string;
  topic_id: string;
  topic_type: string;
  topic_title: string;
  status: CommentStatus;
  reviewer_id?: string;
  review_time?: string;
  reject_reason?: string;
  created_at: string;
  updated_at: string;
}

// 评论列表响应
export interface CommentsResponse {
  comments: Comment[];
  total: number;
  page: number;
  limit: number;
}

// 评论查询参数
export interface CommentQueryParams {
  page?: number;
  limit?: number;
  status?: CommentStatus;
  topic_type?: string;
  topic_id?: string;
  user_id?: string;
}

// 评论审核请求
export interface ReviewCommentRequest {
  status: 'approved' | 'rejected';
  reject_reason?: string;
}

// 评论服务
const commentService = {
  /**
   * 获取评论列表
   * @param params 查询参数
   * @returns 评论列表响应
   */
  async getComments(params: CommentQueryParams): Promise<CommentsResponse> {
    try {
      const response = await axios.get(`${API_BASE_URL}/comments`, { params });
      return response.data.data;
    } catch (error) {
      console.error('获取评论列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取评论详情
   * @param id 评论ID
   * @returns 评论详情
   */
  async getCommentById(id: string): Promise<Comment> {
    try {
      const response = await axios.get(`${API_BASE_URL}/comments/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('获取评论详情失败:', error);
      throw error;
    }
  },

  /**
   * 创建评论
   * @param data 评论数据
   * @returns 创建的评论
   */
  async createComment(data: { topic_id: string; topic_type: string; content: string }): Promise<Comment> {
    try {
      const response = await axios.post(`${API_BASE_URL}/comments`, data);
      return response.data.data;
    } catch (error) {
      console.error('创建评论失败:', error);
      throw error;
    }
  },

  /**
   * 审核评论
   * @param id 评论ID
   * @param data 审核数据
   * @returns 审核结果
   */
  async reviewComment(id: string, data: ReviewCommentRequest): Promise<Comment> {
    try {
      const response = await axios.put(`${API_BASE_URL}/comments/${id}/review`, data);
      return response.data.data;
    } catch (error) {
      console.error('审核评论失败:', error);
      throw error;
    }
  },

  /**
   * 批量审核评论
   * @param ids 评论ID数组
   * @param data 审核数据
   * @returns 审核结果
   */
  async batchReviewComments(ids: string[], data: ReviewCommentRequest): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.put(`${API_BASE_URL}/comments/batch-review`, { ids, ...data });
      return response.data;
    } catch (error) {
      console.error('批量审核评论失败:', error);
      throw error;
    }
  },

  /**
   * 删除评论
   * @param id 评论ID
   * @returns 删除结果
   */
  async deleteComment(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.delete(`${API_BASE_URL}/comments/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除评论失败:', error);
      throw error;
    }
  }
};

export default commentService;
