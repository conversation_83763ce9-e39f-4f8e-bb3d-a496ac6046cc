/**
 * 检查文件状态统计脚本
 * 
 * 用于查询所有文件的状态统计，帮助分析文件数量不一致的原因
 */

const { sequelize, File } = require('./src/models');

async function checkFileStatus() {
  try {
    console.log('开始检查文件状态统计...');
    console.log('='.repeat(80));
    
    // 获取所有文件的总数
    const totalFiles = await File.count();
    console.log(`总文件数量: ${totalFiles}`);
    
    // 获取不同状态的文件数量
    const approvedFiles = await File.count({ where: { status: 'approved' } });
    const pendingFiles = await File.count({ where: { status: 'pending' } });
    const rejectedFiles = await File.count({ where: { status: 'rejected' } });
    
    console.log(`已批准文件数量: ${approvedFiles}`);
    console.log(`待审核文件数量: ${pendingFiles}`);
    console.log(`已拒绝文件数量: ${rejectedFiles}`);
    
    // 检查总数是否一致
    const sumByStatus = approvedFiles + pendingFiles + rejectedFiles;
    if (totalFiles !== sumByStatus) {
      console.log(`[警告] 文件总数(${totalFiles})与各状态文件数量之和(${sumByStatus})不一致`);
    } else {
      console.log(`[正常] 文件总数(${totalFiles})与各状态文件数量之和(${sumByStatus})一致`);
    }
    
    console.log('='.repeat(80));
    
    // 获取每个知识库的文件数量
    const knowledgeBaseFiles = await sequelize.query(`
      SELECT 
        knowledge_base_id,
        COUNT(*) as total_files,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_files,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_files,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_files
      FROM files
      GROUP BY knowledge_base_id
      ORDER BY knowledge_base_id
    `, { type: sequelize.QueryTypes.SELECT });
    
    console.log('各知识库文件状态统计:');
    console.log('-'.repeat(80));
    console.log('知识库ID | 总文件数 | 已批准 | 待审核 | 已拒绝');
    console.log('-'.repeat(80));
    
    knowledgeBaseFiles.forEach(kb => {
      console.log(`${kb.knowledge_base_id.toString().padEnd(8)} | ${kb.total_files.toString().padEnd(8)} | ${kb.approved_files.toString().padEnd(7)} | ${kb.pending_files.toString().padEnd(7)} | ${kb.rejected_files}`);
    });
    
    console.log('='.repeat(80));
    
    // 关闭数据库连接
    await sequelize.close();
    console.log('检查完成');
  } catch (error) {
    console.error('检查文件状态统计失败:', error);
    process.exit(1);
  }
}

// 执行检查
checkFileStatus();
