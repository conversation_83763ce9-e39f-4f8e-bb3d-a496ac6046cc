/**
 * 通知相关路由
 *
 * 处理通知的查询、标记已读等请求
 */

const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notification.controller');
const authMiddleware = require('../middlewares/authMiddleware');

// 获取当前用户的通知列表 (需要认证)
router.get('/',
  authMiddleware,
  notificationController.getNotifications
);

// 获取当前用户的未读通知数量 (需要认证)
router.get('/unread-count',
  authMiddleware,
  notificationController.getUnreadNotificationCount
);

// 获取通知详情 (需要认证)
router.get('/:id',
  authMiddleware,
  notificationController.getNotificationById
);

// 创建通知 (需要认证和管理员权限)
router.post('/',
  authMiddleware,
  notificationController.createNotification
);

// 标记通知为已读 (需要认证)
router.put('/:id/read',
  authMiddleware,
  notificationController.markNotificationAsRead
);

// 标记所有通知为已读 (需要认证)
router.put('/read-all',
  authMiddleware,
  notificationController.markAllNotificationsAsRead
);

// 删除通知 (需要认证)
router.delete('/:id',
  authMiddleware,
  notificationController.deleteNotification
);

// 删除所有通知 (需要认证)
router.delete('/',
  authMiddleware,
  notificationController.deleteAllNotifications
);

module.exports = router;
