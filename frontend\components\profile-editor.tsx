"use client"

import type React from "react"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Check } from "lucide-react"

export function ProfileEditor() {
  const [profile, setProfile] = useState({
    name: "张志明",
    email: "<EMAIL>",
    phone: "13812345678",
    department: "研究部",
    position: "主任",
    bio: "从事家族研究工作多年，专注于历史文献整理与分析。",
  })

  const [isEditing, setIsEditing] = useState(false)
  const [showSuccessToast, setShowSuccessToast] = useState(false)

  const handleChange = (field: string, value: string) => {
    setProfile((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // 这里可以添加表单验证逻辑

    // 模拟保存成功
    setTimeout(() => {
      setIsEditing(false)
      setShowSuccessToast(true)

      setTimeout(() => {
        setShowSuccessToast(false)
      }, 3000)
    }, 500)
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-bold">个人资料</h2>
        {!isEditing && (
          <Button onClick={() => setIsEditing(true)} className="bg-[#f5a623] hover:bg-[#f5a623]/90">
            编辑资料
          </Button>
        )}
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="name">姓名</Label>
            <Input
              id="name"
              value={profile.name}
              onChange={(e) => handleChange("name", e.target.value)}
              disabled={!isEditing}
              className={!isEditing ? "bg-gray-50" : ""}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">邮箱</Label>
            <Input
              id="email"
              type="email"
              value={profile.email}
              onChange={(e) => handleChange("email", e.target.value)}
              disabled={!isEditing}
              className={!isEditing ? "bg-gray-50" : ""}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">手机号</Label>
            <Input
              id="phone"
              value={profile.phone}
              onChange={(e) => handleChange("phone", e.target.value)}
              disabled={!isEditing}
              className={!isEditing ? "bg-gray-50" : ""}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="department">部门</Label>
            {isEditing ? (
              <Select value={profile.department} onValueChange={(value) => handleChange("department", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择部门" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="研究部">研究部</SelectItem>
                  <SelectItem value="档案部">档案部</SelectItem>
                  <SelectItem value="数据部">数据部</SelectItem>
                  <SelectItem value="行政部">行政部</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <Input id="department" value={profile.department} disabled className="bg-gray-50" />
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="position">职位</Label>
            {isEditing ? (
              <Select value={profile.position} onValueChange={(value) => handleChange("position", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择职位" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="主任">主任</SelectItem>
                  <SelectItem value="副主任">副主任</SelectItem>
                  <SelectItem value="研究员">研究员</SelectItem>
                  <SelectItem value="助理研究员">助理研究员</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <Input id="position" value={profile.position} disabled className="bg-gray-50" />
            )}
          </div>

          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="bio">个人简介</Label>
            <textarea
              id="bio"
              value={profile.bio}
              onChange={(e) => handleChange("bio", e.target.value)}
              disabled={!isEditing}
              className={`w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent ${
                !isEditing ? "bg-gray-50" : ""
              }`}
              rows={4}
            />
          </div>
        </div>

        {isEditing && (
          <div className="flex justify-end space-x-3 mt-6">
            <Button type="button" variant="outline" onClick={() => setIsEditing(false)}>
              取消
            </Button>
            <Button type="submit" className="bg-[#f5a623] hover:bg-[#f5a623]/90">
              保存
            </Button>
          </div>
        )}
      </form>

      {/* 成功提示框 */}
      {showSuccessToast && (
        <div className="fixed bottom-8 right-8 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md z-50 animate-fade-in-up">
          <div className="flex items-center">
            <Check className="h-5 w-5 mr-2" />
            <p>个人资料已成功更新！</p>
          </div>
        </div>
      )}
    </div>
  )
}
