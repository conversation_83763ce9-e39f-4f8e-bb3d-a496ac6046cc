# 和富家族研究平台用例文档

## 1. 引言

本文档描述了和富家族研究平台的主要用例，详细说明系统的功能需求和用户与系统的交互方式。用例按照功能模块进行组织，每个用例包括用例名称、参与者、前置条件、后置条件、主要流程和备选流程等内容。

## 2. 用户管理模块

### 2.1 用户注册

**用例编号**：UC-001
**用例名称**：用户注册
**参与者**：访客
**前置条件**：访客未登录系统
**后置条件**：创建新用户账号，显示登录弹窗

**主要流程**：
1. 访客点击导航栏中的"注册"按钮
2. 系统显示注册表单
3. 访客填写用户名、密码、确认密码、手机号和邮箱
4. 访客点击"注册"按钮
5. 系统验证表单信息
6. 系统创建新用户账号
7. 系统显示注册成功提示
8. 系统显示登录弹窗

**备选流程**：
- 5a. 表单信息不完整
  1. 系统显示错误提示
  2. 返回步骤3
- 5b. 用户名已存在
  1. 系统显示错误提示
  2. 返回步骤3
- 5c. 密码不符合安全要求
  1. 系统显示错误提示
  2. 返回步骤3
- 5d. 两次密码输入不一致
  1. 系统显示错误提示
  2. 返回步骤3

### 2.2 用户登录

**用例编号**：UC-002
**用例名称**：用户登录
**参与者**：访客
**前置条件**：访客已注册账号但未登录
**后置条件**：用户成功登录系统

**主要流程**：
1. 访客点击导航栏中的"登录"按钮
2. 系统显示登录表单
3. 访客输入用户名/邮箱和密码
4. 访客点击"登录"按钮
5. 系统验证登录信息
6. 系统更新用户登录状态
7. 系统关闭登录弹窗
8. 系统更新导航栏显示用户信息

**备选流程**：
- 5a. 用户名/邮箱不存在
  1. 系统显示错误提示
  2. 返回步骤3
- 5b. 密码错误
  1. 系统显示错误提示
  2. 返回步骤3

### 2.3 修改个人信息

**用例编号**：UC-003
**用例名称**：修改个人信息
**参与者**：注册用户
**前置条件**：用户已登录系统
**后置条件**：用户个人信息更新

**主要流程**：
1. 用户点击导航栏中的用户头像
2. 用户选择"个人中心"
3. 系统显示个人中心页面
4. 用户点击"编辑个人资料"
5. 系统显示编辑表单
6. 用户修改个人信息（如手机号、邮箱等）
7. 用户点击"保存"按钮
8. 系统验证并保存修改后的信息
9. 系统显示保存成功提示

**备选流程**：
- 8a. 信息格式不正确
  1. 系统显示错误提示
  2. 返回步骤6

### 2.4 修改密码

**用例编号**：UC-004
**用例名称**：修改密码
**参与者**：注册用户
**前置条件**：用户已登录系统
**后置条件**：用户密码更新

**主要流程**：
1. 用户点击导航栏中的用户头像
2. 用户选择"账号设置"
3. 系统显示账号设置页面
4. 用户点击"修改密码"
5. 系统显示修改密码表单
6. 用户输入当前密码、新密码和确认新密码
7. 用户点击"保存"按钮
8. 系统验证密码信息
9. 系统更新用户密码
10. 系统显示修改成功提示

**备选流程**：
- 8a. 当前密码错误
  1. 系统显示错误提示
  2. 返回步骤6
- 8b. 新密码不符合安全要求
  1. 系统显示错误提示
  2. 返回步骤6
- 8c. 两次新密码输入不一致
  1. 系统显示错误提示
  2. 返回步骤6

### 2.5 退出登录

**用例编号**：UC-005
**用例名称**：退出登录
**参与者**：注册用户
**前置条件**：用户已登录系统
**后置条件**：用户退出登录状态

**主要流程**：
1. 用户点击导航栏中的用户头像
2. 用户选择"退出登录"
3. 系统清除用户登录状态
4. 系统重定向到首页
5. 系统更新导航栏显示登录/注册按钮

## 3. 内容展示模块

### 3.1 浏览首页

**用例编号**：UC-006
**用例名称**：浏览首页
**参与者**：访客/注册用户
**前置条件**：无
**后置条件**：显示首页内容

**主要流程**：
1. 用户访问系统首页
2. 系统显示导航栏
3. 系统显示英雄区域
4. 系统显示家族历史长河
5. 系统显示革命先辈事迹
6. 系统显示纪念活动
7. 系统显示页脚

### 3.2 浏览家族专题

**用例编号**：UC-007
**用例名称**：浏览家族专题
**参与者**：访客/注册用户
**前置条件**：无
**后置条件**：显示家族专题内容

**主要流程**：
1. 用户点击导航栏中的"家族专题"
2. 系统显示家族专题页面
3. 系统显示英雄区域
4. 系统显示家族历史概述
5. 系统显示家族重要事件时间线

### 3.3 浏览个人专题

**用例编号**：UC-008
**用例名称**：浏览个人专题
**参与者**：访客/注册用户
**前置条件**：无
**后置条件**：显示个人专题内容

**主要流程**：
1. 用户点击导航栏中的"个人专题"下拉菜单
2. 用户选择一个历史人物（如"蔡和森"）
3. 系统显示该人物的个人专题页面
4. 系统显示个人基本信息
5. 系统显示人生轨迹
6. 系统显示历史贡献
7. 系统显示相关资料

### 3.4 查看活动详情

**用例编号**：UC-009
**用例名称**：查看活动详情
**参与者**：访客/注册用户
**前置条件**：无
**后置条件**：显示活动详情

**主要流程**：
1. 用户在首页浏览纪念活动列表
2. 用户点击某个活动卡片
3. 系统显示活动详情弹窗
4. 用户查看活动详情（标题、日期、描述、图片、附件等）
5. 用户点击关闭按钮
6. 系统关闭活动详情弹窗

## 4. 活动管理模块

### 4.1 管理活动

**用例编号**：UC-010
**用例名称**：管理活动
**参与者**：管理员
**前置条件**：用户已登录且具有管理权限
**后置条件**：进入活动管理模式

**主要流程**：
1. 管理员在首页浏览纪念活动列表
2. 管理员点击"管理活动"按钮
3. 系统切换到活动管理模式
4. 系统显示所有活动（包括草稿和已下架）
5. 系统显示活动管理选项（添加、编辑、删除、状态切换）

### 4.2 添加活动

**用例编号**：UC-011
**用例名称**：添加活动
**参与者**：管理员
**前置条件**：管理员已进入活动管理模式
**后置条件**：新活动添加到系统

**主要流程**：
1. 管理员点击"新增活动"按钮
2. 系统显示添加活动弹窗
3. 管理员填写活动信息（标题、日期、描述等）
4. 管理员上传活动图片
5. 管理员上传活动附件（可选）
6. 管理员选择活动状态（草稿/已发布）
7. 管理员点击"保存"按钮
8. 系统验证并保存活动信息
9. 系统关闭弹窗并更新活动列表

**备选流程**：
- 8a. 活动信息不完整
  1. 系统显示错误提示
  2. 返回步骤3

### 4.3 编辑活动

**用例编号**：UC-012
**用例名称**：编辑活动
**参与者**：管理员
**前置条件**：管理员已进入活动管理模式
**后置条件**：活动信息更新

**主要流程**：
1. 管理员点击活动卡片上的编辑图标
2. 系统显示编辑活动弹窗，预填充现有活动信息
3. 管理员修改活动信息
4. 管理员点击"保存"按钮
5. 系统验证并保存修改后的活动信息
6. 系统关闭弹窗并更新活动列表

### 4.4 删除活动

**用例编号**：UC-013
**用例名称**：删除活动
**参与者**：管理员
**前置条件**：管理员已进入活动管理模式
**后置条件**：活动从系统中删除

**主要流程**：
1. 管理员点击活动卡片上的删除图标
2. 系统显示删除确认弹窗
3. 管理员点击"确认删除"按钮
4. 系统删除活动
5. 系统关闭弹窗并更新活动列表

**备选流程**：
- 3a. 管理员点击"取消"按钮
  1. 系统关闭弹窗
  2. 活动保持不变

## 5. 知识库管理模块

### 5.1 浏览知识库

**用例编号**：UC-014
**用例名称**：浏览知识库
**参与者**：注册用户
**前置条件**：用户已登录系统
**后置条件**：显示知识库内容

**主要流程**：
1. 用户点击导航栏中的"知识库"
2. 系统验证用户登录状态
3. 系统显示知识库页面
4. 系统显示资料分类导航
5. 系统显示资料列表
6. 用户浏览资料列表

**备选流程**：
- 2a. 用户未登录
  1. 系统显示登录提示
  2. 用户登录后继续

### 5.2 上传资料

**用例编号**：UC-015
**用例名称**：上传资料
**参与者**：注册用户
**前置条件**：用户已登录系统并进入知识库页面
**后置条件**：新资料添加到系统

**主要流程**：
1. 用户点击"上传资料"按钮
2. 系统显示上传资料表单
3. 用户填写资料信息（标题、描述、分类等）
4. 用户选择并上传文件
5. 用户点击"提交"按钮
6. 系统验证表单信息
7. 系统上传文件
8. 系统使用知识库文件分析助手分析文件内容
9. 系统保存资料信息
10. 系统显示上传成功提示
11. 系统通知用户上传结果

**备选流程**：
- 6a. 表单信息不完整
  1. 系统显示错误提示
  2. 返回步骤3
- 7a. 文件上传失败
  1. 系统显示错误提示
  2. 返回步骤4
- 8a. 文件分析失败
  1. 系统记录错误信息
  2. 继续步骤9，但不包含分析结果

### 5.3 搜索资料

**用例编号**：UC-016
**用例名称**：搜索资料
**参与者**：注册用户
**前置条件**：用户已登录系统并进入知识库页面
**后置条件**：显示搜索结果

**主要流程**：
1. 用户在搜索框中输入关键词
2. 用户点击搜索按钮
3. 系统执行搜索
4. 系统显示搜索结果列表
5. 用户浏览搜索结果

**备选流程**：
- 4a. 没有匹配的结果
  1. 系统显示"未找到匹配的资料"提示
  2. 提供搜索建议

### 5.4 查看资料详情

**用例编号**：UC-017
**用例名称**：查看资料详情
**参与者**：注册用户
**前置条件**：用户已登录系统并进入知识库页面
**后置条件**：显示资料详情

**主要流程**：
1. 用户点击资料列表中的某个资料
2. 系统显示资料详情页面
3. 用户查看资料信息（标题、描述、上传者、上传时间等）
4. 用户查看或下载资料文件

## 6. 数据查询模块

### 6.1 执行数据查询

**用例编号**：UC-018
**用例名称**：执行数据查询
**参与者**：注册用户
**前置条件**：用户已登录系统
**后置条件**：显示查询结果

**主要流程**：
1. 用户点击导航栏中的"数据查询"
2. 系统显示数据查询页面
3. 用户选择数据源
4. 用户设置查询条件
5. 用户点击"查询"按钮
6. 系统执行查询
7. 系统显示查询结果
8. 用户查看结果

**备选流程**：
- 3a. 用户选择系统外数据源
  1. 系统显示"正在对接中"提示
  2. 返回步骤3
- 7a. 没有匹配的结果
  1. 系统显示"未找到匹配的数据"提示
  2. 提供查询建议

### 6.2 使用AI查询助手

**用例编号**：UC-019
**用例名称**：使用AI查询助手
**参与者**：注册用户
**前置条件**：用户已登录系统并进入数据查询页面
**后置条件**：AI助手提供查询帮助

**主要流程**：
1. 用户点击"AI查询助手"按钮
2. 系统显示AI查询助手对话框
3. 用户输入查询问题
4. 用户发送问题
5. 系统将知识库ID推送到Dify
6. 系统调用Dify API获取回答
7. 系统显示AI助手回答
8. 用户继续对话或关闭对话框

**备选流程**：
- 6a. API调用失败
  1. 系统显示错误提示
  2. 提示用户稍后再试

### 6.3 导出查询结果

**用例编号**：UC-020
**用例名称**：导出查询结果
**参与者**：注册用户
**前置条件**：用户已执行查询并获得结果
**后置条件**：查询结果导出为文件

**主要流程**：
1. 用户查看查询结果
2. 用户点击"导出"按钮
3. 系统显示导出选项（如CSV、Excel等）
4. 用户选择导出格式
5. 系统生成导出文件
6. 系统提供文件下载链接
7. 用户下载文件

## 7. AI研究助手模块

### 7.1 选择AI研究助手

**用例编号**：UC-021
**用例名称**：选择AI研究助手
**参与者**：注册用户
**前置条件**：用户已登录系统
**后置条件**：打开选定的AI助手对话窗口

**主要流程**：
1. 用户点击导航栏中的"AI研究助手"
2. 系统验证用户登录状态
3. 系统显示AI研究助手页面
4. 系统显示助手列表
5. 用户浏览助手列表
6. 用户点击某个助手卡片
7. 系统打开该助手的对话窗口
8. 系统显示初始对话内容（如果有）

**备选流程**：
- 2a. 用户未登录
  1. 系统显示登录提示
  2. 用户登录后继续

### 7.2 与AI助手对话

**用例编号**：UC-022
**用例名称**：与AI助手对话
**参与者**：注册用户
**前置条件**：用户已选择AI助手并打开对话窗口
**后置条件**：AI助手回答用户问题

**主要流程**：
1. 用户在输入框中输入问题
2. 用户点击发送按钮
3. 系统将用户问题添加到对话历史
4. 系统调用Dify API获取回答
5. 系统将AI回答添加到对话历史
6. 用户查看回答
7. 用户可以继续输入新问题（返回步骤1）或关闭对话窗口

**备选流程**：
- 4a. API调用失败
  1. 系统显示错误提示
  2. 提示用户稍后再试

## 8. 系统管理模块

### 8.1 管理用户

**用例编号**：UC-023
**用例名称**：管理用户
**参与者**：管理员
**前置条件**：管理员已登录系统
**后置条件**：用户信息更新

**主要流程**：
1. 管理员点击导航栏中的"系统管理"
2. 系统显示系统管理页面
3. 管理员选择"用户管理"标签
4. 系统显示用户列表
5. 管理员可以查看、添加、编辑或删除用户
6. 管理员可以重置用户密码
7. 管理员可以启用或禁用用户账号

### 8.2 管理角色权限

**用例编号**：UC-024
**用例名称**：管理角色权限
**参与者**：管理员
**前置条件**：管理员已登录系统并进入系统管理页面
**后置条件**：角色权限更新

**主要流程**：
1. 管理员选择"权限管理"标签
2. 系统显示角色列表
3. 管理员可以添加、编辑或删除角色
4. 管理员点击"权限设置"按钮
5. 系统显示权限设置弹窗
6. 管理员设置角色权限
7. 管理员点击"保存"按钮
8. 系统保存权限设置

### 8.3 管理留言

**用例编号**：UC-025
**用例名称**：管理留言
**参与者**：管理员
**前置条件**：管理员已登录系统并进入系统管理页面
**后置条件**：留言状态更新

**主要流程**：
1. 管理员选择"留言管理"标签
2. 系统显示留言列表
3. 管理员可以查看、审核或删除留言
4. 管理员点击"审核"按钮
5. 系统显示审核选项（通过/驳回）
6. 管理员选择审核结果
7. 系统更新留言状态
8. 系统通知留言发布者审核结果

### 8.4 配置系统参数

**用例编号**：UC-026
**用例名称**：配置系统参数
**参与者**：管理员
**前置条件**：管理员已登录系统并进入系统管理页面
**后置条件**：系统参数更新

**主要流程**：
1. 管理员选择"系统配置"标签
2. 系统显示系统配置选项
3. 管理员设置安全配置（如密码规则）
4. 管理员点击"保存配置"按钮
5. 系统保存配置参数
6. 系统显示保存成功提示

### 8.5 管理AI助手

**用例编号**：UC-027
**用例名称**：管理AI助手
**参与者**：管理员
**前置条件**：管理员已登录系统并进入系统管理页面
**后置条件**：AI助手配置更新

**主要流程**：
1. 管理员选择"AI管理"标签
2. 系统显示AI助手列表
3. 管理员可以查看、编辑AI助手
4. 管理员可以添加或删除AI研究助手
5. 管理员点击"编辑"按钮
6. 系统显示编辑AI助手弹窗
7. 管理员修改AI助手配置（如API设置、初始对话内容等）
8. 管理员点击"保存"按钮
9. 系统保存AI助手配置
10. 系统显示保存成功提示

## 9. 通知模块

### 9.1 查看通知

**用例编号**：UC-028
**用例名称**：查看通知
**参与者**：注册用户
**前置条件**：用户已登录系统
**后置条件**：显示通知列表

**主要流程**：
1. 用户点击导航栏中的通知图标
2. 系统显示通知下拉菜单
3. 系统显示通知列表（最多显示4条）
4. 用户查看通知列表
5. 用户可以点击"查看全部通知"链接
6. 系统显示通知页面，展示所有通知

### 9.2 查看通知详情

**用例编号**：UC-029
**用例名称**：查看通知详情
**参与者**：注册用户
**前置条件**：用户已登录系统并打开通知列表
**后置条件**：显示通知详情，将未读通知标记为已读

**主要流程**：
1. 用户点击通知列表中的某个通知
2. 系统显示通知详情模态框
3. 系统显示完整通知内容
4. 系统将未读通知标记为已读
5. 用户查看通知详情
6. 用户点击关闭按钮
7. 系统关闭通知详情模态框

### 9.3 标记所有通知为已读

**用例编号**：UC-030
**用例名称**：标记所有通知为已读
**参与者**：注册用户
**前置条件**：用户已登录系统并有未读通知
**后置条件**：所有通知标记为已读

**主要流程**：
1. 用户点击导航栏中的通知图标
2. 系统显示通知下拉菜单
3. 用户点击"全部标为已读"按钮
4. 系统将所有未读通知标记为已读
5. 系统更新通知列表显示
6. 系统移除通知图标上的红点