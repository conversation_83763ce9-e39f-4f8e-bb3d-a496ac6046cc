/**
 * 知识库组件
 *
 * 用于展示和管理知识库
 */

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from '@/components/ui/use-toast'
import { LoadingState } from '@/components/ui/loading-state'

// 知识库类型
export interface KnowledgeBase {
  id: number
  name: string
  description: string
  type: string
  created_at: string
  updated_at: string
  owner_id: number
  owner_name: string
  file_count: number
}

// 知识库列表组件
export const KnowledgeBaseList = ({
  knowledgeBases,
  onSelect,
  isLoading,
  error
}: {
  knowledgeBases: KnowledgeBase[]
  onSelect: (kb: KnowledgeBase) => void
  isLoading: boolean
  error: string | null
}) => {
  if (isLoading) {
    return <LoadingState status="loading" loadingText="正在加载知识库..." />
  }

  if (error) {
    return <LoadingState status="error" errorText={error} />
  }

  if (knowledgeBases.length === 0) {
    return <div className="text-center p-4">暂无知识库</div>
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {knowledgeBases.map((kb) => (
        <Card key={kb.id} className="cursor-pointer hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle>{kb.name}</CardTitle>
            <CardDescription>类型: {kb.type}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500">{kb.description}</p>
            <p className="text-xs text-gray-400 mt-2">文件数量: {kb.file_count}</p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={() => onSelect(kb)}>查看详情</Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}

// 知识库详情组件
export const KnowledgeBaseDetail = ({
  knowledgeBase,
  onBack
}: {
  knowledgeBase: KnowledgeBase | null
  onBack: () => void
}) => {
  if (!knowledgeBase) {
    return <div className="text-center p-4">未选择知识库</div>
  }

  return (
    <div>
      <div className="flex items-center mb-4">
        <Button variant="ghost" onClick={onBack} className="mr-2">返回</Button>
        <h2 className="text-2xl font-bold">{knowledgeBase.name}</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>知识库详情</CardTitle>
          <CardDescription>创建于 {new Date(knowledgeBase.created_at).toLocaleString()}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium">描述</h3>
              <p>{knowledgeBase.description}</p>
            </div>
            <div>
              <h3 className="font-medium">类型</h3>
              <p>{knowledgeBase.type}</p>
            </div>
            <div>
              <h3 className="font-medium">创建者</h3>
              <p>{knowledgeBase.owner_name}</p>
            </div>
            <div>
              <h3 className="font-medium">文件数量</h3>
              <p>{knowledgeBase.file_count}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// 知识库组件
export const KnowledgeBase = () => {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([])
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<KnowledgeBase | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  // 加载知识库列表
  useEffect(() => {
    const fetchKnowledgeBases = async () => {
      setIsLoading(true)
      setError(null)
      try {
        // 模拟API调用 - 使用axios或其他方法
        // 这里我们直接模拟数据
        const mockData = {
          data: [
            {
              id: 1,
              name: '测试知识库',
              description: '这是一个测试用的知识库',
              type: '系统知识库',
              created_at: '2023-01-01T00:00:00Z',
              updated_at: '2023-01-01T00:00:00Z',
              owner_id: 1,
              owner_name: '系统管理员',
              file_count: 5
            },
            {
              id: 2,
              name: '个人知识库',
              description: '用户个人知识库',
              type: '个人知识库',
              created_at: '2023-01-02T00:00:00Z',
              updated_at: '2023-01-02T00:00:00Z',
              owner_id: 1,
              owner_name: '当前用户',
              file_count: 3
            }
          ]
        }

        // 模拟网络延迟
        setTimeout(() => {
          setKnowledgeBases(mockData.data || [])
          setIsLoading(false)
        }, 500)
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载知识库失败')
        toast({
          title: '加载失败',
          description: err instanceof Error ? err.message : '加载知识库失败',
          variant: 'destructive'
        })
        setIsLoading(false)
      }
    }

    fetchKnowledgeBases()
  }, [])

  // 过滤知识库
  const filteredKnowledgeBases = knowledgeBases.filter(kb =>
    kb.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    kb.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 处理选择知识库
  const handleSelectKnowledgeBase = (kb: KnowledgeBase) => {
    setSelectedKnowledgeBase(kb)
  }

  // 处理返回列表
  const handleBack = () => {
    setSelectedKnowledgeBase(null)
  }

  return (
    <div className="container mx-auto p-4">
      {selectedKnowledgeBase ? (
        <KnowledgeBaseDetail
          knowledgeBase={selectedKnowledgeBase}
          onBack={handleBack}
        />
      ) : (
        <div>
          <h1 className="text-3xl font-bold mb-6">知识库</h1>

          <div className="mb-6">
            <Input
              placeholder="搜索知识库..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-md"
            />
          </div>

          <Tabs defaultValue="all">
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="my">我的知识库</TabsTrigger>
              <TabsTrigger value="shared">共享知识库</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-4">
              <KnowledgeBaseList
                knowledgeBases={filteredKnowledgeBases}
                onSelect={handleSelectKnowledgeBase}
                isLoading={isLoading}
                error={error}
              />
            </TabsContent>

            <TabsContent value="my" className="mt-4">
              <KnowledgeBaseList
                knowledgeBases={filteredKnowledgeBases.filter(kb => kb.owner_id === 1)} // 假设当前用户ID为1
                onSelect={handleSelectKnowledgeBase}
                isLoading={isLoading}
                error={error}
              />
            </TabsContent>

            <TabsContent value="shared" className="mt-4">
              <KnowledgeBaseList
                knowledgeBases={filteredKnowledgeBases.filter(kb => kb.owner_id !== 1)} // 假设当前用户ID为1
                onSelect={handleSelectKnowledgeBase}
                isLoading={isLoading}
                error={error}
              />
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  )
}

export default KnowledgeBase
