/**
 * 测试用的认证上下文提供者
 */

import React, { createContext, useContext, useState, ReactNode } from "react";

// 定义用户数据类型
export interface UserData {
  id: number;
  username: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: string;
  role_id?: number;
  role_name?: string;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

// 定义认证上下文类型
interface AuthContextType {
  isLoggedIn: boolean;
  userData: UserData | null;
  hasPermission: (permission: string) => boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  updateUserData: () => Promise<void>;
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 认证上下文提供者组件
export function MockAuthProvider({ children }: { children: ReactNode }) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userData, setUserData] = useState<UserData | null>(null);

  // 检查用户是否有特定权限
  const hasPermission = (permission: string) => {
    if (!isLoggedIn) return false;
    return true; // 在测试中总是返回true
  };

  // 更新用户数据
  const updateUserData = async () => {
    // 在测试中不做任何事情
    return Promise.resolve();
  };

  // 登录处理函数
  const login = async (username: string, password: string): Promise<boolean> => {
    // 模拟登录成功
    setUserData({
      id: 1,
      username: username,
      email: "<EMAIL>",
      role: "user",
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
    setIsLoggedIn(true);
    return true;
  };

  // 登出处理函数
  const logout = () => {
    setIsLoggedIn(false);
    setUserData(null);
  };

  return (
    <AuthContext.Provider value={{ isLoggedIn, userData, hasPermission, login, logout, updateUserData }}>
      {children}
    </AuthContext.Provider>
  );
}

// 自定义钩子，用于访问认证上下文
export function useMockAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useMockAuth must be used within a MockAuthProvider");
  }
  return context;
}
