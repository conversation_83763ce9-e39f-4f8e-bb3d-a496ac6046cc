"use client"

import { useState, useRef, useEffect } from "react"
import { Send, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { PersonalAssistantConfig } from "@/types/ai-assistants"
import axios from "axios"
import { HtmlTypewriter } from "@/components/ui/html-typewriter"

// 获取API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 3600000 // 60分钟超时
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('hefamily_token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 消息类型
interface Message {
  role: "user" | "assistant"
  content: string
}

// 组件属性
interface PersonalAssistantProps extends PersonalAssistantConfig {
  // personalId 已经在 PersonalAssistantConfig 中定义
}

/**
 * 个人专题AI助手组件
 *
 * 用于个人专题页面，如蔡和森、李富春等
 * 在系统管理的AI管理界面中，type="personal"类型的AI助手会显示在此组件中
 *
 * @param personName - 人物名称，用于定制助手内容
 * @param personalId - 关联的个人ID
 * @param assistantId - 关联到系统中的AI助手ID（在实际应用中使用）
 * @param apiKey - API密钥（在实际应用中使用）
 * @param apiEndpoint - API端点（在实际应用中使用）
 */
export function PersonalAssistant({
  name,
  personalId,
  description,
  apiKey,
  apiEndpoint,
  appId,
  initialMessage
}: PersonalAssistantProps & { initialMessage?: string }) {
  // 检查是否有初始对话内容
  const defaultMessage = `您好！我是${name}研究助手，很高兴为您提供关于${name}生平、思想和贡献的信息。请问有什么可以帮助您的吗？`
  const visitorMessage = `您好！我是${name}研究助手。我可以回答关于${name}生平、思想和贡献的问题。请在下方输入您的问题，或者登录后获得更完整的服务。`

  // 检查用户是否登录
  const isUserLoggedIn = localStorage.getItem("isLoggedIn") === "true"

  // 确保initialMessage是字符串类型，并根据登录状态选择初始消息
  const initialMessageStr = typeof initialMessage === 'string' ? initialMessage : (isUserLoggedIn ? defaultMessage : visitorMessage)

  // 根据登录状态选择显示的消息
  const displayMessage = isUserLoggedIn ? initialMessageStr : visitorMessage

  const [messages, setMessages] = useState<Message[]>(
    displayMessage ? [
      {
        role: "assistant",
        content: displayMessage,
      },
    ] : []
  )
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [thinkingDots, setThinkingDots] = useState(".")
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 打字机效果相关状态
  const [showTypewriter, setShowTypewriter] = useState(false)
  const [typewriterText, setTypewriterText] = useState('')
  const [lastMessageIndex, setLastMessageIndex] = useState<number | null>(null)

  // 检查登录状态
  useEffect(() => {
    const checkLoginStatus = () => {
      const loggedIn = localStorage.getItem("isLoggedIn") === "true"
      setIsLoggedIn(loggedIn)
    }

    checkLoginStatus()

    // 监听登录状态变化
    window.addEventListener("storage", checkLoginStatus)

    // 监听登录成功事件
    const handleLoginSuccess = () => {
      console.log("检测到登录成功事件，刷新页面")
      window.location.reload()
    }

    // 添加自定义事件监听器
    window.addEventListener("login-success", handleLoginSuccess)

    return () => {
      window.removeEventListener("storage", checkLoginStatus)
      window.removeEventListener("login-success", handleLoginSuccess)
    }
  }, [])

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  // 思考动画效果
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isLoading) {
      interval = setInterval(() => {
        setThinkingDots(prev => {
          if (prev.length >= 3) return ".";
          return prev + ".";
        });
      }, 500);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isLoading])

  // 打字机效果完成后的回调函数
  const handleTypewriterComplete = () => {
    console.log('打字机效果完成')
    setShowTypewriter(false)
    setIsLoading(false)
  }

  /**
   * 发送消息
   * 调用后端API获取AI助手回复
   */
  const handleSendMessage = async () => {
    if (!input.trim()) return

    // 保存用户输入
    const userInput = input.trim()

    // 清空输入框
    setInput("")

    // 检查用户是否登录
    if (!isLoggedIn) {
      setError("请先登录后再使用AI助手")

      // 添加用户消息和助手回复，提示用户登录
      setMessages([
        ...messages,
        { role: "user", content: userInput },
        {
          role: "assistant",
          content: `抱歉，您需要登录后才能使用${name}研究助手的完整功能。请点击下方的"登录"按钮进行登录，登录后您将获得更完整的服务体验。`
        }
      ])

      // 滚动到底部
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
      }, 100)

      return
    }

    // 清除错误
    setError(null)

    // 重置打字机状态
    setShowTypewriter(false)
    setTypewriterText('')

    // 添加用户消息并保存到变量中，以便后续使用
    const updatedMessages = [...messages, { role: "user", content: userInput }];
    setMessages(updatedMessages);
    setIsLoading(true);

    try {
      // 调用后端API获取回复
      if (apiKey && apiEndpoint) {
        console.log(`调用AI助手API: ${apiEndpoint}, apiKey: ${apiKey ? '已设置' : '未设置'}, appId: ${appId || '未设置'}`)

        try {
          // 直接调用后端API
          console.log('调用个人专题助手API - 参数:', {
            userInput,
            personalId,
            personalName: name
          });

          // 使用apiClient发送请求到后端
          console.log('发送请求到后端 - 请求参数:', {
            query: userInput,
            personal_id: personalId ? Number(personalId) : undefined,
            personal_name: name
          });

          // 检查token是否存在
          const token = localStorage.getItem('hefamily_token');
          console.log('当前token状态:', token ? '已设置' : '未设置');

          // 发送请求
          const response = await apiClient.post('/ai/personal/query', {
            query: userInput,
            personal_id: personalId ? Number(personalId) : undefined,
            personal_name: name
          });

          console.log('个人专题助手API响应:', response.data);

          // 添加助手回复 - 处理后端返回的数据格式
          console.log('后端返回的完整响应:', response.data);

          if (response.data && response.data.success && response.data.data) {
            // 后端返回的数据格式是 { success: true, data: ... }
            const responseData = response.data.data;

            // 检查是否是流式响应格式（字符串且包含 data: 前缀）
            if (typeof responseData === 'string' && responseData.includes('data:')) {
              console.log('检测到流式响应格式，开始解析...');

              try {
                // 解析所有事件
                const events = responseData.split('\n\n')
                  .filter(line => line.trim().startsWith('data:'))
                  .map(line => {
                    try {
                      return JSON.parse(line.substring(line.indexOf('data:') + 5).trim());
                    } catch (e) {
                      console.error('解析事件失败:', e, line);
                      return null;
                    }
                  })
                  .filter(event => event !== null);

                console.log(`解析了 ${events.length} 个流式事件`);

                if (events.length === 0) {
                  console.warn('未找到有效的流式事件');
                  throw new Error('未找到有效的流式事件');
                }

                // 提取所有消息内容并拼接
                let fullAnswer = '';

                // 查找所有 message 事件并提取 answer 字段
                events.forEach(event => {
                  if (event.event === 'message' && event.answer) {
                    fullAnswer += event.answer;
                  }
                });

                // 如果没有找到任何消息内容，尝试查找 message_end 事件
                if (!fullAnswer) {
                  const messageEndEvent = events.find(event => event.event === 'message_end' && event.answer);
                  if (messageEndEvent) {
                    fullAnswer = messageEndEvent.answer;
                  }
                }

                // 如果仍然没有找到任何消息内容，尝试查找任何包含 answer 字段的事件
                if (!fullAnswer) {
                  const anyEventWithAnswer = events.find(event => event.answer);
                  if (anyEventWithAnswer) {
                    fullAnswer = anyEventWithAnswer.answer;
                  }
                }

                // 如果仍然没有找到任何消息内容，返回错误
                if (!fullAnswer) {
                  console.warn('未能从流式事件中提取回答内容');
                  throw new Error('未能从流式事件中提取回答内容');
                }

                console.log('从流式事件中提取的完整回答:', fullAnswer);

                // 检查是否包含思考过程（details标签）
                let cleanedAnswer = fullAnswer;

                // 如果包含思考过程，提取出正式回复部分
                if (fullAnswer.includes('<details') && fullAnswer.includes('</details>')) {
                  const detailsEndIndex = fullAnswer.indexOf('</details>') + 10;
                  cleanedAnswer = fullAnswer.substring(detailsEndIndex).trim();
                  console.log('提取出正式回复部分，长度:', cleanedAnswer.length);
                }

                // 移除所有HTML标签，只保留纯文本
                const textOnly = cleanedAnswer.replace(/<[^>]*>/g, '');
                console.log('移除HTML标签后的纯文本，长度:', textOnly.length);

                // 设置打字机文本内容
                setTypewriterText(textOnly);

                // 添加消息到列表
                const newMessages = [
                  ...updatedMessages,
                  {
                    role: "assistant",
                    content: textOnly // 使用纯文本内容
                  }
                ];

                setMessages(newMessages);

                // 保存最后一条消息索引，用于打字机效果
                setLastMessageIndex(newMessages.length - 1);

                // 显示打字机效果
                setShowTypewriter(true);

                // 尝试提取会话ID
                const eventWithConversationId = events.find(event => event.conversation_id);
                if (eventWithConversationId && eventWithConversationId.conversation_id) {
                  console.log('从流式事件中提取的会话ID:', eventWithConversationId.conversation_id);
                  // 这里可以保存会话ID，如果需要的话
                }

                return; // 成功返回，结束函数
              } catch (parseError) {
                console.error('解析流式响应失败:', parseError);
                // 解析失败，继续尝试其他格式
              }
            }

            // 非流式响应格式处理（原有逻辑）
            // 尝试多种可能的字段名
            const answer = responseData.answer || responseData.text ||
                          (responseData.data && (responseData.data.answer || responseData.data.text));

            if (answer) {
              console.log('找到有效的回答:', answer);

              // 检查是否包含思考过程（details标签）
              let cleanedAnswer = answer;

              // 如果包含思考过程，提取出正式回复部分
              if (answer.includes('<details') && answer.includes('</details>')) {
                const detailsEndIndex = answer.indexOf('</details>') + 10;
                cleanedAnswer = answer.substring(detailsEndIndex).trim();
                console.log('提取出正式回复部分，长度:', cleanedAnswer.length);
              }

              // 移除所有HTML标签，只保留纯文本
              const textOnly = cleanedAnswer.replace(/<[^>]*>/g, '');
              console.log('移除HTML标签后的纯文本，长度:', textOnly.length);

              // 设置打字机文本内容
              setTypewriterText(textOnly);

              // 添加消息到列表
              const newMessages = [
                ...updatedMessages,
                {
                  role: "assistant",
                  content: textOnly // 使用纯文本内容
                }
              ];

              setMessages(newMessages);

              // 保存最后一条消息索引，用于打字机效果
              setLastMessageIndex(newMessages.length - 1);

              // 显示打字机效果
              setShowTypewriter(true);

              return; // 成功返回，结束函数
            } else {
              console.warn("无法从响应中提取回答内容:", responseData);
            }
          } else {
            console.warn("API返回的响应格式不正确，尝试直接调用Dify API")
          }
        } catch (backendError: any) {
          console.error("通过后端调用AI助手失败，尝试直接调用Dify API:", backendError)

          // 记录更详细的错误信息
          if (backendError.response) {
            console.error('后端API错误响应:', {
              status: backendError.response.status,
              statusText: backendError.response.statusText,
              data: backendError.response.data
            });
          } else if (backendError.request) {
            console.error('后端API请求已发送但无响应:', {
              method: backendError.config?.method,
              url: backendError.config?.url
            });
          } else {
            console.error('后端API请求配置错误:', backendError.message);
          }

          // 如果后端调用失败，尝试直接调用Dify API
          try {
            console.log("尝试直接调用Dify API")

            // 使用已导入的axios实例
            // 不需要动态导入，因为我们已经在文件顶部导入了axios

            // 构建API URL
            let apiUrl = apiEndpoint
            if (apiUrl.endsWith('/')) {
              apiUrl = apiUrl.slice(0, -1)
            }

            // 尝试多种可能的API URL格式
            const possibleApiUrls = [
              `${apiUrl}/api/chat-messages`,
              `${apiUrl}/chat-messages`,
              `${apiUrl}/v1/chat-messages`
            ];

            // 选择第一个URL
            apiUrl = possibleApiUrls[0];

            console.log('前端 - 可能的API URL:', possibleApiUrls);
            console.log('前端 - 使用API URL:', apiUrl);

            // 构建请求体 - 添加人物信息作为上下文
            const requestBody = {
              inputs: {
                // 添加人物ID和名称作为上下文信息
                personal_id: personalId ? personalId.toString() : undefined,
                personal_name: name,
                // 添加助手名称作为上下文信息
                assistant_name: `${name}专题助手`
              },
              query: userInput,
              response_mode: 'blocking',
              user: 'frontend-user'
            }

            // 添加app_id和app_code字段
            if (appId) {
              requestBody.app_id = appId;
            }

            console.log('前端 - 直接调用Dify API请求体:', {
              ...requestBody,
              inputs: requestBody.inputs
            });

            // 发送请求 - 使用已导入的axios实例
            console.log('直接调用Dify API - 发送请求:', {
              url: apiUrl,
              headers: {
                'Authorization': 'Bearer ' + (apiKey ? '已设置' : '未设置'),
                'Content-Type': 'application/json'
              }
            });

            const response = await axios.post(
              apiUrl,
              requestBody,
              {
                headers: {
                  'Authorization': `Bearer ${apiKey}`,
                  'Content-Type': 'application/json'
                },
                timeout: 1800000 // 30分钟超时
              }
            )

            // 处理响应
            if (response.data && (response.data.answer || response.data.text)) {
              const answer = response.data.answer || response.data.text

              // 检查是否包含思考过程（details标签）
              let cleanedAnswer = answer;

              // 如果包含思考过程，提取出正式回复部分
              if (answer.includes('<details') && answer.includes('</details>')) {
                const detailsEndIndex = answer.indexOf('</details>') + 10;
                cleanedAnswer = answer.substring(detailsEndIndex).trim();
                console.log('提取出正式回复部分，长度:', cleanedAnswer.length);
              }

              // 移除所有HTML标签，只保留纯文本
              const textOnly = cleanedAnswer.replace(/<[^>]*>/g, '');
              console.log('移除HTML标签后的纯文本，长度:', textOnly.length);

              // 设置打字机文本内容
              setTypewriterText(textOnly);

              // 添加消息到列表
              const newMessages = [
                ...updatedMessages,
                {
                  role: "assistant",
                  content: textOnly // 使用纯文本内容
                }
              ];

              setMessages(newMessages);

              // 保存最后一条消息索引，用于打字机效果
              setLastMessageIndex(newMessages.length - 1);

              // 显示打字机效果
              setShowTypewriter(true);

              return; // 成功返回，结束函数
            } else {
              console.warn("直接调用Dify API返回的响应格式不正确:", response.data)
              throw new Error("API返回的响应格式不正确")
            }
          } catch (directError) {
            console.error("直接调用Dify API失败:", directError)
            throw directError // 重新抛出错误，让外层catch处理
          }
        }
      } else {
        // 检查用户是否有使用AI助手的权限
        try {
          // 从localStorage获取权限
          const permissionsStr = localStorage.getItem('hefamily_user_permissions');
          const permissions = permissionsStr ? JSON.parse(permissionsStr) : [];

          // 检查是否有personal:ai_use权限
          const hasAiPermission = permissions.includes('personal:ai_use') || permissions.includes('*');

          if (!hasAiPermission) {
            // 用户没有权限，显示权限不足提示
            console.warn("用户没有使用AI助手的权限");
            setError("您没有使用AI助手的权限，请联系管理员开通权限");

            setMessages([
              ...updatedMessages,
              {
                role: "assistant",
                content: "抱歉，您没有使用AI助手的权限。请联系管理员在系统管理-权限管理中为您的角色添加'使用AI助手'(personal:ai_use)权限。",
              },
            ]);
          } else {
            // 用户有权限，但API未配置
            console.warn("用户有权限，但API未配置");

            // 检查用户是否是管理员 - 统一使用hefamily_user_data
            const userDataStr = localStorage.getItem('hefamily_user_data');
            const userData = userDataStr ? JSON.parse(userDataStr) : null;
            const isAdmin = userData && userData.role === 'admin';

            // 如果未找到用户数据，尝试从其他可能的存储位置获取
            if (!userData) {
              // 尝试从hefamily_user_info获取
              const userInfoStr = localStorage.getItem('hefamily_user_info');
              if (userInfoStr) {
                const userInfo = JSON.parse(userInfoStr);
                if (userInfo && userInfo.role === 'admin') {
                  console.log("从hefamily_user_info中找到管理员信息");
                  isAdmin = true;
                }
              }

              // 尝试从hefamily_user获取
              const userStr = localStorage.getItem('hefamily_user');
              if (userStr) {
                const user = JSON.parse(userStr);
                if (user && user.role === 'admin') {
                  console.log("从hefamily_user中找到管理员信息");
                  isAdmin = true;
                }
              }
            }

            if (isAdmin) {
              // 管理员用户，使用默认提示
              console.log("管理员可以正常使用");
              setMessages([
                ...updatedMessages,
                {
                  role: "assistant",
                  content: `我无法回答这个问题，因为您提供的内容与${name}人物研究无关。如果您有关于${name}的具体问题，我很乐意为您提供专业的研究分析。`,
                },
              ]);
            } else {
              // 非管理员用户，显示API未配置提示
              setMessages([
                ...updatedMessages,
                {
                  role: "assistant",
                  content: `抱歉，系统管理员尚未配置${name}专题助手的API信息。请联系管理员在系统管理-AI管理中配置个人专题助手的API信息。`,
                },
              ]);
            }
          }
        } catch (error) {
          console.error("检查权限时出错:", error);

          setMessages([
            ...updatedMessages,
            {
              role: "assistant",
              content: `抱歉，无法检查您的权限。请联系管理员确认您是否有使用AI助手的权限。`,
            },
          ]);
        }
      }
    } catch (err: any) {
      console.error("获取AI回复失败:", err)

      // 根据错误类型提供不同的错误信息
      let errorMessage = "获取回复失败，请重试"
      let assistantMessage = "抱歉，我暂时无法回答您的问题。"

      if (err.code === 'ECONNABORTED' || err.message?.includes('timeout')) {
        errorMessage = "请求超时，AI服务器正在生成回复，但需要较长时间。请耐心等待或稍后再试。"
        assistantMessage = "抱歉，我正在思考这个问题，但需要较长时间。对于复杂问题，AI生成回复可能需要几分钟甚至更长时间。请稍后再试，或者尝试提出更简短、更具体的问题。"
      } else if (err.response) {
        // 服务器返回了错误状态码
        if (err.response.status === 404) {
          errorMessage = "AI服务API地址不正确，请联系管理员检查配置"
          assistantMessage = "抱歉，系统无法连接到AI服务。请联系管理员检查AI助手的API地址配置。"
        } else if (err.response.status === 401 || err.response.status === 403) {
          errorMessage = "AI服务API密钥无效，请联系管理员检查配置"
          assistantMessage = "抱歉，系统无法授权访问AI服务。请联系管理员检查AI助手的API密钥配置。"
        } else if (err.response.status === 500) {
          // 服务器内部错误
          console.error('服务器内部错误详情:', err.response.data);
          errorMessage = "服务器内部错误，请联系管理员检查后端日志"
          assistantMessage = "抱歉，服务器发生内部错误。请联系管理员检查后端日志，可能是AI助手配置问题或权限问题。"
        } else {
          errorMessage = `服务器错误 (${err.response.status})，请联系管理员`
          assistantMessage = `抱歉，AI服务返回了错误 (${err.response.status})。请联系管理员检查AI助手配置。`
        }
      } else if (err.request) {
        // 请求已发送但没有收到响应
        errorMessage = "无法连接到服务器，请检查网络连接"
        assistantMessage = "抱歉，系统无法连接到AI服务器。请检查您的网络连接，或联系管理员确认服务器是否正常运行。"
      }

      setError(errorMessage)

      // 添加错误消息
      setMessages([
        ...updatedMessages,
        {
          role: "assistant",
          content: assistantMessage
        }
      ])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="flex flex-col h-full bg-[#fdf9f1]">
      {/* 顶部标题 */}
      <div className="p-4 border-b border-[#1e7a43]/20 bg-white">
        <h2 className="text-lg font-medium text-center">{name} AI助手</h2>
        {description && (
          <p className="text-xs text-gray-500 text-center mt-1">{description}</p>
        )}
      </div>

      {/* 对话内容区 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message, index) => (
          <div key={index} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.role === "user" ? "bg-[#1e7a43] text-white" : "bg-white border border-[#1e7a43]/20"
              }`}
            >
              {showTypewriter && index === lastMessageIndex ? (
                <HtmlTypewriter
                  html={typewriterText}
                  className="text-lg whitespace-pre-line"
                  speed={30}
                  onComplete={handleTypewriterComplete}
                />
              ) : (
                <div className="whitespace-pre-line">{message.content}</div>
              )}
            </div>
          </div>
        ))}
        {isLoading && !showTypewriter && (
          <div className="flex justify-start">
            <div className="max-w-[80%] rounded-lg p-3 bg-white border border-[#1e7a43]/20">
              <div className="flex flex-col space-y-2">
                <div className="flex space-x-2">
                  <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"></div>
                  <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce [animation-delay:0.2s]"></div>
                  <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce [animation-delay:0.4s]"></div>
                </div>
                <div className="text-sm text-gray-500">
                  正在思考中{thinkingDots}
                </div>
                <div className="text-xs text-gray-400">
                  复杂问题可能需要较长时间，请耐心等待
                </div>
              </div>
            </div>
          </div>
        )}
        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 底部输入框 */}
      <div className="p-4 border-t border-[#1e7a43]/20 bg-white relative">
        {/* 未登录用户显示登录提示覆盖层 */}
        {!isLoggedIn && (
          <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
            <div className="w-full h-full bg-[#fff9e6] flex flex-col items-center justify-center">
              <p className="text-center text-[#8B6914] mb-2">请先登录后再使用AI助手</p>
              <Button
                className="bg-[#1e7a43] hover:bg-[#165a32]"
                onClick={() => {
                  // 触发登录弹窗
                  const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
                  window.dispatchEvent(event);
                }}
              >
                登录
              </Button>
            </div>
          </div>
        )}

        {/* 输入框和发送按钮 - 始终显示但会被覆盖 */}
        <div className="flex items-center space-x-2">
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={`请输入关于${name}的问题...`}
            className="flex-1 border rounded-md p-2 focus:outline-none focus:ring-1 focus:ring-[#1e7a43] resize-none h-10 max-h-32 overflow-y-auto"
            rows={1}
            disabled={isLoading}
          />
          <Button
            onClick={handleSendMessage}
            className="bg-[#1e7a43] hover:bg-[#165a32]"
            disabled={isLoading || !input.trim()}
          >
            {isLoading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
