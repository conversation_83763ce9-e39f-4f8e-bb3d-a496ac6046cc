/**
 * AI研究助手权限种子数据
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查assistant:use权限是否已存在
    const [existingPermissions] = await queryInterface.sequelize.query(
      `SELECT id FROM permissions WHERE code = 'assistant:use'`
    );

    // 如果权限不存在，创建它
    if (existingPermissions.length === 0) {
      // 创建assistant:use权限
      await queryInterface.bulkInsert('permissions', [{
        name: '使用AI研究助手',
        code: 'assistant:use',
        description: '使用AI研究助手功能',
        module: 'assistant',
        created_at: new Date(),
        updated_at: new Date()
      }], {});

      // 获取新创建的权限ID
      const [permissions] = await queryInterface.sequelize.query(
        `SELECT id FROM permissions WHERE code = 'assistant:use'`
      );

      if (permissions.length > 0) {
        // 获取访问者角色ID
        const [roles] = await queryInterface.sequelize.query(
          `SELECT id FROM roles WHERE name = '访问者'`
        );

        if (roles.length > 0) {
          // 为访问者角色分配assistant:use权限
          await queryInterface.bulkInsert('role_permissions', [{
            role_id: roles[0].id,
            permission_id: permissions[0].id,
            created_at: new Date(),
            updated_at: new Date()
          }], {});
        }
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 获取assistant:use权限ID
    const [permissions] = await queryInterface.sequelize.query(
      `SELECT id FROM permissions WHERE code = 'assistant:use'`
    );

    if (permissions.length > 0) {
      // 删除角色权限关联
      await queryInterface.bulkDelete('role_permissions', {
        permission_id: permissions[0].id
      }, {});

      // 删除权限
      await queryInterface.bulkDelete('permissions', {
        code: 'assistant:use'
      }, {});
    }
  }
};
