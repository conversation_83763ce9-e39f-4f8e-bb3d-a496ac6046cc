/**
 * ResponsiveContainer组件测试
 */

import React from 'react'
import { render, screen } from '../test-utils'
import { ResponsiveContainer } from '@/components/ui/responsive-container'

// 模拟window.innerWidth
const setWindowWidth = (width: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  })
  window.dispatchEvent(new Event('resize'))
}

describe('ResponsiveContainer组件', () => {
  // 在每个测试后重置window.innerWidth
  afterEach(() => {
    jest.restoreAllMocks()
  })

  // 测试基本渲染
  test('应该正确渲染默认内容', () => {
    render(
      <ResponsiveContainer>
        <div data-testid="default-content">默认内容</div>
      </ResponsiveContainer>
    )
    
    expect(screen.getByTestId('default-content')).toBeInTheDocument()
  })

  // 测试响应式类名
  test('应该根据屏幕尺寸应用不同的类名', () => {
    // 模拟移动端屏幕
    setWindowWidth(500)
    
    const { rerender } = render(
      <ResponsiveContainer
        className="base-class"
        mobileClassName="mobile-class"
        tabletClassName="tablet-class"
        desktopClassName="desktop-class"
        data-testid="container"
      >
        <div>内容</div>
      </ResponsiveContainer>
    )
    
    // 检查移动端类名
    expect(screen.getByTestId('container')).toHaveClass('base-class')
    expect(screen.getByTestId('container')).toHaveClass('mobile-class')
    
    // 模拟平板屏幕
    setWindowWidth(800)
    rerender(
      <ResponsiveContainer
        className="base-class"
        mobileClassName="mobile-class"
        tabletClassName="tablet-class"
        desktopClassName="desktop-class"
        data-testid="container"
      >
        <div>内容</div>
      </ResponsiveContainer>
    )
    
    // 检查平板类名
    expect(screen.getByTestId('container')).toHaveClass('base-class')
    expect(screen.getByTestId('container')).toHaveClass('tablet-class')
    
    // 模拟桌面屏幕
    setWindowWidth(1200)
    rerender(
      <ResponsiveContainer
        className="base-class"
        mobileClassName="mobile-class"
        tabletClassName="tablet-class"
        desktopClassName="desktop-class"
        data-testid="container"
      >
        <div>内容</div>
      </ResponsiveContainer>
    )
    
    // 检查桌面类名
    expect(screen.getByTestId('container')).toHaveClass('base-class')
    expect(screen.getByTestId('container')).toHaveClass('desktop-class')
  })

  // 测试响应式内容
  test('应该根据屏幕尺寸显示不同的内容', () => {
    // 模拟移动端屏幕
    setWindowWidth(500)
    
    const { rerender } = render(
      <ResponsiveContainer
        mobileContent={<div data-testid="mobile-content">移动端内容</div>}
        tabletContent={<div data-testid="tablet-content">平板内容</div>}
        desktopContent={<div data-testid="desktop-content">桌面内容</div>}
      >
        <div data-testid="default-content">默认内容</div>
      </ResponsiveContainer>
    )
    
    // 检查移动端内容
    expect(screen.getByTestId('mobile-content')).toBeInTheDocument()
    expect(screen.queryByTestId('default-content')).not.toBeInTheDocument()
    
    // 模拟平板屏幕
    setWindowWidth(800)
    rerender(
      <ResponsiveContainer
        mobileContent={<div data-testid="mobile-content">移动端内容</div>}
        tabletContent={<div data-testid="tablet-content">平板内容</div>}
        desktopContent={<div data-testid="desktop-content">桌面内容</div>}
      >
        <div data-testid="default-content">默认内容</div>
      </ResponsiveContainer>
    )
    
    // 检查平板内容
    expect(screen.getByTestId('tablet-content')).toBeInTheDocument()
    expect(screen.queryByTestId('mobile-content')).not.toBeInTheDocument()
    
    // 模拟桌面屏幕
    setWindowWidth(1200)
    rerender(
      <ResponsiveContainer
        mobileContent={<div data-testid="mobile-content">移动端内容</div>}
        tabletContent={<div data-testid="tablet-content">平板内容</div>}
        desktopContent={<div data-testid="desktop-content">桌面内容</div>}
      >
        <div data-testid="default-content">默认内容</div>
      </ResponsiveContainer>
    )
    
    // 检查桌面内容
    expect(screen.getByTestId('desktop-content')).toBeInTheDocument()
    expect(screen.queryByTestId('tablet-content')).not.toBeInTheDocument()
  })

  // 测试自定义断点
  test('应该支持自定义断点', () => {
    // 模拟自定义断点下的移动端屏幕
    setWindowWidth(400)
    
    const { rerender } = render(
      <ResponsiveContainer
        breakpoints={{ tablet: 500, desktop: 900 }}
        mobileContent={<div data-testid="mobile-content">移动端内容</div>}
        tabletContent={<div data-testid="tablet-content">平板内容</div>}
        desktopContent={<div data-testid="desktop-content">桌面内容</div>}
      >
        <div data-testid="default-content">默认内容</div>
      </ResponsiveContainer>
    )
    
    // 检查移动端内容
    expect(screen.getByTestId('mobile-content')).toBeInTheDocument()
    
    // 模拟自定义断点下的平板屏幕
    setWindowWidth(600)
    rerender(
      <ResponsiveContainer
        breakpoints={{ tablet: 500, desktop: 900 }}
        mobileContent={<div data-testid="mobile-content">移动端内容</div>}
        tabletContent={<div data-testid="tablet-content">平板内容</div>}
        desktopContent={<div data-testid="desktop-content">桌面内容</div>}
      >
        <div data-testid="default-content">默认内容</div>
      </ResponsiveContainer>
    )
    
    // 检查平板内容
    expect(screen.getByTestId('tablet-content')).toBeInTheDocument()
    
    // 模拟自定义断点下的桌面屏幕
    setWindowWidth(1000)
    rerender(
      <ResponsiveContainer
        breakpoints={{ tablet: 500, desktop: 900 }}
        mobileContent={<div data-testid="mobile-content">移动端内容</div>}
        tabletContent={<div data-testid="tablet-content">平板内容</div>}
        desktopContent={<div data-testid="desktop-content">桌面内容</div>}
      >
        <div data-testid="default-content">默认内容</div>
      </ResponsiveContainer>
    )
    
    // 检查桌面内容
    expect(screen.getByTestId('desktop-content')).toBeInTheDocument()
  })

  // 测试部分响应式内容
  test('应该支持只提供部分响应式内容', () => {
    // 模拟移动端屏幕
    setWindowWidth(500)
    
    const { rerender } = render(
      <ResponsiveContainer
        mobileContent={<div data-testid="mobile-content">移动端内容</div>}
        // 不提供tabletContent
        // 不提供desktopContent
      >
        <div data-testid="default-content">默认内容</div>
      </ResponsiveContainer>
    )
    
    // 检查移动端内容
    expect(screen.getByTestId('mobile-content')).toBeInTheDocument()
    
    // 模拟平板屏幕
    setWindowWidth(800)
    rerender(
      <ResponsiveContainer
        mobileContent={<div data-testid="mobile-content">移动端内容</div>}
        // 不提供tabletContent
        // 不提供desktopContent
      >
        <div data-testid="default-content">默认内容</div>
      </ResponsiveContainer>
    )
    
    // 检查默认内容（因为没有提供tabletContent）
    expect(screen.getByTestId('default-content')).toBeInTheDocument()
  })
})
