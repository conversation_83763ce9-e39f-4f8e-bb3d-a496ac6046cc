/**
 * 检查知识库文件数量脚本
 * 
 * 用于检查知识库文件数量是否正确，并分析不同状态的文件数量
 */

const { sequelize, KnowledgeBase, File } = require('./src/models');

async function checkFileCount() {
  try {
    console.log('开始检查知识库文件数量...');
    console.log('='.repeat(80));
    
    // 获取所有知识库
    const knowledgeBases = await KnowledgeBase.findAll({
      order: [['id', 'ASC']]
    });
    
    console.log(`找到 ${knowledgeBases.length} 个知识库`);
    console.log('='.repeat(80));
    
    let totalFiles = 0;
    let totalApprovedFiles = 0;
    let totalPendingFiles = 0;
    let totalRejectedFiles = 0;
    
    // 检查每个知识库的文件数量
    for (const kb of knowledgeBases) {
      // 获取所有状态的文件数量
      const allFilesCount = await File.count({
        where: {
          knowledge_base_id: kb.id
        }
      });
      
      // 获取已批准的文件数量
      const approvedFilesCount = await File.count({
        where: {
          knowledge_base_id: kb.id,
          status: 'approved'
        }
      });
      
      // 获取待审核的文件数量
      const pendingFilesCount = await File.count({
        where: {
          knowledge_base_id: kb.id,
          status: 'pending'
        }
      });
      
      // 获取已拒绝的文件数量
      const rejectedFilesCount = await File.count({
        where: {
          knowledge_base_id: kb.id,
          status: 'rejected'
        }
      });
      
      // 更新统计数据
      totalFiles += allFilesCount;
      totalApprovedFiles += approvedFilesCount;
      totalPendingFiles += pendingFilesCount;
      totalRejectedFiles += rejectedFilesCount;
      
      // 输出知识库信息
      console.log(`知识库 #${kb.id}: ${kb.name} (${kb.type})`);
      console.log(`  - 当前记录的文件数量: ${kb.file_count}`);
      console.log(`  - 实际文件数量统计:`);
      console.log(`    - 所有状态文件: ${allFilesCount}`);
      console.log(`    - 已批准文件: ${approvedFilesCount}`);
      console.log(`    - 待审核文件: ${pendingFilesCount}`);
      console.log(`    - 已拒绝文件: ${rejectedFilesCount}`);
      
      // 检查是否有不一致
      if (kb.file_count !== approvedFilesCount) {
        console.log(`  - [警告] 文件数量不一致: 记录数量=${kb.file_count}, 实际已批准文件数量=${approvedFilesCount}`);
      }
      
      console.log('-'.repeat(80));
    }
    
    // 输出总计
    console.log('='.repeat(80));
    console.log('总计:');
    console.log(`  - 知识库数量: ${knowledgeBases.length}`);
    console.log(`  - 所有状态文件总数: ${totalFiles}`);
    console.log(`  - 已批准文件总数: ${totalApprovedFiles}`);
    console.log(`  - 待审核文件总数: ${totalPendingFiles}`);
    console.log(`  - 已拒绝文件总数: ${totalRejectedFiles}`);
    console.log('='.repeat(80));
    
    // 提供解释
    console.log('说明:');
    console.log('1. 知识库的 file_count 字段只统计已批准(approved)状态的文件');
    console.log('2. 管理员在文件列表中可以看到所有状态的文件(approved, pending, rejected)');
    console.log('3. 如果文件列表显示的文件数量与知识库显示的文件数量不一致，这是正常的');
    console.log('   因为文件列表包含了所有状态的文件，而知识库只显示已批准的文件数量');
    console.log('='.repeat(80));
    
    // 关闭数据库连接
    await sequelize.close();
    console.log('检查完成');
  } catch (error) {
    console.error('检查知识库文件数量失败:', error);
    process.exit(1);
  }
}

// 执行检查
checkFileCount();
