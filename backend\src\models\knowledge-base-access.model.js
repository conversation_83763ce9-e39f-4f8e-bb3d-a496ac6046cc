/**
 * 知识库访问权限模型
 * 用于管理用户对知识库的访问权限
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} KnowledgeBaseAccess模型
 */
module.exports = (sequelize, DataTypes) => {
  const KnowledgeBaseAccess = sequelize.define('KnowledgeBaseAccess', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    knowledge_base_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'knowledge_bases',
        key: 'id'
      }
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    access_type: {
      type: DataTypes.ENUM('read', 'write', 'admin'),
      allowNull: false,
      defaultValue: 'read'
    },
    granted_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'knowledge_base_access',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  KnowledgeBaseAccess.associate = (models) => {
    KnowledgeBaseAccess.belongsTo(models.KnowledgeBase, {
      foreignKey: 'knowledge_base_id',
      as: 'knowledgeBase'
    });
    
    KnowledgeBaseAccess.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    
    KnowledgeBaseAccess.belongsTo(models.User, {
      foreignKey: 'granted_by',
      as: 'grantor'
    });
  };

  return KnowledgeBaseAccess;
};
