/**
 * 测试工具函数
 *
 * 提供测试辅助函数和自定义渲染器
 */

import React, { ReactElement } from 'react'
import { render as rtlRender, RenderOptions } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

// 创建一个简单的包装器组件
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>
}

// 自定义渲染器
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => {
  return {
    ...rtlRender(ui, {
      wrapper: AllTheProviders,
      ...options,
    }),
    user: userEvent.setup(),
  }
}

// 创建模拟响应
const createMockResponse = (data: any, status = 200) => {
  return {
    ok: status >= 200 && status < 300,
    status,
    json: async () => data,
    text: async () => JSON.stringify(data),
    headers: new Headers({
      'Content-Type': 'application/json',
    }),
  }
}

// 模拟fetch
const mockFetch = (mockData: any, status = 200) => {
  global.fetch = jest.fn().mockResolvedValue(createMockResponse(mockData, status))

  // 模拟axios请求
  jest.mock('axios', () => ({
    create: jest.fn(() => ({
      get: jest.fn().mockResolvedValue({ data: mockData }),
      post: jest.fn().mockResolvedValue({ data: mockData }),
      put: jest.fn().mockResolvedValue({ data: mockData }),
      delete: jest.fn().mockResolvedValue({ data: mockData }),
      patch: jest.fn().mockResolvedValue({ data: mockData }),
      interceptors: {
        request: { use: jest.fn(), eject: jest.fn() },
        response: { use: jest.fn(), eject: jest.fn() }
      }
    }))
  }))

  // 模拟API服务
  jest.mock('@/services/api-service', () => ({
    __esModule: true,
    default: {
      get: jest.fn().mockResolvedValue(mockData),
      post: jest.fn().mockResolvedValue(mockData),
      put: jest.fn().mockResolvedValue(mockData),
      del: jest.fn().mockResolvedValue(mockData),
      delete: jest.fn().mockResolvedValue(mockData),
      upload: jest.fn().mockResolvedValue(mockData),
      download: jest.fn().mockResolvedValue(mockData),
      postFormData: jest.fn().mockResolvedValue(mockData),
      putFormData: jest.fn().mockResolvedValue(mockData)
    }
  }))

  // 模拟认证上下文 - 在测试文件中单独模拟，避免全局模拟导致的问题
}

// 重置模拟fetch
const resetMockFetch = () => {
  global.fetch = jest.fn()
  jest.resetAllMocks()
}

// 等待元素渲染
const waitForRender = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms))

// 导出
export {
  customRender as render,
  mockFetch,
  resetMockFetch,
  waitForRender,
  createMockResponse,
}

// 导出所有testing-library的函数
export * from '@testing-library/react'
