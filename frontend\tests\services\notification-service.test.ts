/**
 * 通知服务测试
 */

import {
  getNotificationList,
  getNotification,
  markAsRead,
  markAllAsRead,
  deleteNotification
} from '@/services/notification-service'
import apiService from '@/services/api-service'

// 模拟API服务
jest.mock('@/services/api-service')

describe('通知服务', () => {
  // 在每个测试前后重置模拟
  beforeEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  afterEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  // 测试获取通知列表
  describe('getNotificationList', () => {
    test('应该返回通知列表', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        data: {
          notifications: [
            {
              id: 1,
              title: '系统通知',
              content: '欢迎使用和富家族研究平台',
              type: 'system',
              status: 'unread',
              created_at: '2023-01-01T00:00:00Z'
            },
            {
              id: 2,
              title: '评论通知',
              content: '您的评论已被审核通过',
              type: 'comment',
              status: 'read',
              created_at: '2023-01-02T00:00:00Z'
            }
          ],
          pagination: {
            total: 2,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        }
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse.data)

      // 调用获取通知列表函数
      const result = await getNotificationList({ status: 'all' })

      // 验证结果
      expect(result).toBeDefined()
      expect(result.notifications).toHaveLength(2)
      expect(result.notifications[0].title).toBe('系统通知')
      expect(result.notifications[1].title).toBe('评论通知')
    })

    test('获取通知列表失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('获取通知列表失败')

      // 模拟API服务的get方法抛出错误
      apiService.get = jest.fn().mockRejectedValue(error)

      // 调用获取通知列表函数并捕获错误
      await expect(getNotificationList({ status: 'all' })).rejects.toThrow()
    })
  })

  // 测试获取通知详情
  describe('getNotification', () => {
    test('应该返回通知详情', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        data: {
          id: 1,
          title: '系统通知',
          content: '欢迎使用和富家族研究平台',
          type: 'system',
          status: 'unread',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse.data)

      // 调用获取通知详情函数
      const result = await getNotification(1)

      // 验证结果
      expect(result).toBeDefined()
      expect(result.id).toBe(1)
      expect(result.title).toBe('系统通知')
      expect(result.content).toBe('欢迎使用和富家族研究平台')
    })

    test('获取不存在的通知应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('通知不存在')

      // 模拟API服务的get方法抛出错误
      apiService.get = jest.fn().mockRejectedValue(error)

      // 调用获取通知详情函数并捕获错误
      await expect(getNotification(999)).rejects.toThrow()
    })
  })

  // 测试标记通知为已读
  describe('markAsRead', () => {
    test('应该成功标记通知为已读', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        message: '通知已标记为已读',
        data: {
          id: 1,
          status: 'read',
          updated_at: '2023-01-03T00:00:00Z'
        }
      }

      // 模拟API服务的put方法
      apiService.put = jest.fn().mockResolvedValue(mockResponse.data)

      // 调用标记通知为已读函数
      const result = await markAsRead(1)

      // 验证结果
      expect(result).toBeDefined()
      expect(result.status).toBe('read')
    })

    test('标记不存在的通知应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('通知不存在')

      // 模拟API服务的put方法抛出错误
      apiService.put = jest.fn().mockRejectedValue(error)

      // 调用标记通知为已读函数并捕获错误
      await expect(markAsRead(999)).rejects.toThrow()
    })
  })

  // 测试标记所有通知为已读
  describe('markAllAsRead', () => {
    test('应该成功标记所有通知为已读', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        message: '所有通知已标记为已读',
        data: {
          count: 3
        }
      }

      // 模拟API服务的put方法
      apiService.put = jest.fn().mockResolvedValue(mockResponse)

      // 调用标记所有通知为已读函数
      const result = await markAllAsRead()

      // 验证结果
      expect(result).toBeDefined()
      expect(result.success).toBe(true)
      expect(result.message).toBe('所有通知已标记为已读')
      expect(result.data.count).toBe(3)
    })

    test('标记所有通知失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('操作失败')

      // 模拟API服务的put方法抛出错误
      apiService.put = jest.fn().mockRejectedValue(error)

      // 调用标记所有通知为已读函数并捕获错误
      await expect(markAllAsRead()).rejects.toThrow()
    })
  })

  // 测试删除通知
  describe('deleteNotification', () => {
    test('应该成功删除通知', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        message: '通知删除成功'
      }

      // 模拟API服务的del方法
      apiService.del = jest.fn().mockResolvedValue(mockResponse)

      // 调用删除通知函数
      const result = await deleteNotification(1)

      // 验证结果
      expect(result).toBeDefined()
      expect(result.success).toBe(true)
      expect(result.message).toBe('通知删除成功')
    })

    test('删除不存在的通知应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('通知不存在')

      // 模拟API服务的del方法抛出错误
      apiService.del = jest.fn().mockRejectedValue(error)

      // 调用删除通知函数并捕获错误
      await expect(deleteNotification(999)).rejects.toThrow()
    })
  })
});
