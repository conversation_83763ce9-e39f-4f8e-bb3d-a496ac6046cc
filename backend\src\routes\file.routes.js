/**
 * 文件相关路由
 *
 * 处理文件的上传、下载、查询、审核等请求
 */

const express = require('express');
const router = express.Router();
const fileController = require('../controllers/file.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 配置文件上传存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = process.env.UPLOAD_DIR || 'uploads';
    const fullPath = path.join(process.cwd(), uploadDir);

    // 确保上传目录存在
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }

    cb(null, fullPath);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

// 中文文件名处理中间件
const handleChineseFilenames = (req, res, next) => {
  // 创建一个映射来存储原始文件名和解码后的文件名
  req.decodedFileNames = {};

  // 如果有文件，尝试解码文件名
  if (req.file) {
    try {
      const decodedName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');
      req.decodedFileNames[req.file.originalname] = decodedName;
      console.log(`文件名解码: ${req.file.originalname} -> ${decodedName}`);
    } catch (e) {
      console.error('解码文件名失败:', e);
    }
  }

  // 如果有多个文件
  if (req.files) {
    Object.keys(req.files).forEach(key => {
      const files = Array.isArray(req.files[key]) ? req.files[key] : [req.files[key]];
      files.forEach(file => {
        try {
          const decodedName = Buffer.from(file.originalname, 'latin1').toString('utf8');
          req.decodedFileNames[file.originalname] = decodedName;
          console.log(`文件名解码: ${file.originalname} -> ${decodedName}`);
        } catch (e) {
          console.error('解码文件名失败:', e);
        }
      });
    });
  }

  next();
};

// 配置multer - 设置非常大的文件大小限制（实质上无限制）
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 1024 * 1024 * 1024 // 1GB
  }
});

// 上传文件 (需要认证)
router.post('/upload/:knowledgeBaseId',
  authMiddleware,
  upload.single('file'),
  handleChineseFilenames,
  fileController.uploadFile
);

// 获取文件列表 (需要认证)
router.get('/',
  authMiddleware,
  fileController.getFiles
);

// 获取知识库文件列表 (需要认证)
router.get('/knowledge-base/:knowledgeBaseId',
  authMiddleware,
  fileController.getFilesByKnowledgeBase
);

// 获取文件详情 (需要认证)
router.get('/:id',
  authMiddleware,
  fileController.getFileById
);

// 下载文件 (需要认证)
router.get('/:id/download',
  authMiddleware,
  fileController.downloadFile
);

// 审核文件 (需要认证和权限)
router.put('/:id/review',
  authMiddleware,
  checkPermission('file:review'),
  fileController.reviewFile
);

// 上传文件到Dify知识库 (需要认证)
router.post('/:id/upload-to-dify',
  authMiddleware,
  fileController.uploadFileToDify
);

// 删除文件 (需要认证和权限)
router.delete('/:id',
  authMiddleware,
  fileController.deleteFile
);

// Dify回调接口 (不需要认证，由Dify服务器调用)
router.post('/dify-callback/:fileId',
  fileController.handleDifyCallback
);

module.exports = router;
