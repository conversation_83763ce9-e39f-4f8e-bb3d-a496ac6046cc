import type { AIAgent, ResearchAssistant, ResearchResult } from "@/types/ai-assistants"
import { <PERSON> } from "lucide-react"
import axios from 'axios'
import { logger, sanitizeData } from "@/utils/logger"

/**
 * AI助手服务
 *
 * 调用后端API获取AI助手数据
 */

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 3600000 // 增加超时时间到60分钟
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('hefamily_token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

/**
 * 获取系统中所有AI代理
 */
export async function getAllAIAgents(): Promise<AIAgent[]> {
  try {
    const response = await apiClient.get('/ai')

    // 转换后端数据格式为前端所需格式
    return response.data.data.map((item: any) => ({
      id: item.id,
      name: item.name,
      type: item.type,
      description: item.description,
      tags: item.tags ? item.tags.split(',') : [],
      status: item.status === 'active' ? '正常' : '停用',
      lastUpdated: new Date(item.updated_at).toLocaleString(),
      // 管理员可以看到的敏感信息
      ...(item.api_key && {
        apiKey: item.api_key,
        apiEndpoint: item.api_endpoint,
        model: item.app_code
      })
    }))
  } catch (error) {
    logger.error('获取AI助手列表失败:', sanitizeData(error))
    return []
  }
}

/**
 * 获取AI研究助手列表
 * 只返回type="assistant"类型的助手
 */
export async function getResearchAssistants(): Promise<ResearchAssistant[]> {
  try {
    const response = await apiClient.get('/ai', {
      params: { type: 'assistant' }
    })

    // 转换后端数据格式为前端所需格式
    const assistants: ResearchAssistant[] = response.data.data.map((item: any) => ({
      id: item.id,
      name: item.name,
      description: item.description,
      type: item.type,
      status: item.status === 'active' ? '正常' : '停用',
      tags: item.tags ? item.tags.split(',') : [],
      lastUpdated: new Date(item.updated_at).toLocaleString(),
      // 添加UI所需的额外属性
      icon: "brain",
      category: "research",
      isPopular: item.is_popular || false
    }))

    return assistants
  } catch (error) {
    logger.error('获取AI研究助手列表失败:', sanitizeData(error))
    return []
  }
}

/**
 * 获取指定类型的AI助手列表
 * @param type 助手类型，如'personal', 'data-query', 'knowledge-file'等
 */
export async function getAIAssistants(type: string): Promise<AIAgent[]> {
  try {
    logger.debug(`获取${type}类型AI助手列表 - 开始请求`)
    const response = await apiClient.get('/ai', {
      params: { type }
    })

    logger.debug(`获取${type}类型AI助手列表 - 响应数据:`, sanitizeData(response.data))

    if (!response.data || !response.data.data || !Array.isArray(response.data.data)) {
      logger.error(`获取${type}类型AI助手列表 - 响应格式不正确:`, sanitizeData(response.data))
      return []
    }

    // 记录所有助手的基本信息
    logger.debug(`获取${type}类型AI助手列表 - 助手数量:`, response.data.data.length)
    response.data.data.forEach((item: any, index: number) => {
      logger.debug(`${type}助手 ${index + 1}:`, {
        id: item.id,
        name: item.name,
        type: item.type,
        status: item.status,
        hasApiKey: !!item.api_key,
        hasApiEndpoint: !!item.api_endpoint,
        appId: item.app_id || '未设置',
        appCode: item.app_code || '未设置'
      })
    })

    // 转换后端数据格式为前端所需格式
    return response.data.data.map((item: any) => ({
      id: item.id,
      name: item.name,
      type: item.type,
      description: item.description,
      tags: item.tags ? item.tags.split(',') : [],
      status: item.status === 'active' ? '正常' : '停用',
      lastUpdated: new Date(item.updated_at).toLocaleString(),
      apiKey: item.api_key,
      apiEndpoint: item.api_endpoint,
      appId: item.app_id,
      appCode: item.app_code,
      enabled: item.status === 'active'
    }))
  } catch (error) {
    logger.error(`获取${type}类型AI助手列表失败:`, sanitizeData(error))
    return []
  }
}

/**
 * 根据ID获取AI代理
 */
export async function getAIAgentById(id: string): Promise<AIAgent | null> {
  try {
    const response = await apiClient.get(`/ai/${id}`)

    const item = response.data.data

    // 转换后端数据格式为前端所需格式
    return {
      id: item.id,
      name: item.name,
      type: item.type,
      description: item.description,
      tags: item.tags ? item.tags.split(',') : [],
      status: item.status === 'active' ? '正常' : '停用',
      lastUpdated: new Date(item.updated_at).toLocaleString(),
      // 管理员可以看到的敏感信息
      ...(item.api_key && {
        apiKey: item.api_key,
        apiEndpoint: item.api_endpoint,
        model: item.app_code
      })
    }
  } catch (error) {
    logger.error(`获取AI助手(ID: ${id})失败:`, sanitizeData(error))
    return null
  }
}

/**
 * 更新AI代理
 */
export async function saveAIAgent(agent: AIAgent): Promise<AIAgent> {
  try {
    // 转换前端数据格式为后端所需格式
    const data = {
      name: agent.name,
      description: agent.description,
      api_key: agent.apiKey,
      api_endpoint: agent.apiEndpoint,
      // 确保即使是空字符串也发送
      app_id: agent.appId !== undefined ? agent.appId : '',
      app_code: agent.appCode !== undefined ? agent.appCode : '',
      tags: Array.isArray(agent.tags) ? agent.tags.join(',') : agent.tags,
      status: agent.status === '正常' ? 'active' : 'inactive'
    }

    logger.debug('更新AI助手 - 发送数据:', {
      ...sanitizeData(data),
      api_key: data.api_key ? '已设置' : '未设置' // 不在日志中显示完整的API密钥
    })

    const response = await apiClient.put(`/ai/${agent.id}`, data)

    const item = response.data.data

    // 转换后端响应为前端所需格式
    return {
      id: item.id,
      name: item.name,
      type: item.type,
      description: item.description,
      tags: item.tags ? item.tags.split(',') : [],
      status: item.status === 'active' ? '正常' : '停用',
      lastUpdated: new Date(item.updated_at).toLocaleString(),
      apiKey: item.api_key,
      apiEndpoint: item.api_endpoint,
      model: item.app_code
    }
  } catch (error) {
    logger.error('更新AI助手失败:', sanitizeData(error))
    throw error
  }
}

/**
 * 查询个人专题助手
 * @param query 查询内容
 * @param conversationId 会话ID
 * @param personalId 人物ID
 * @param personalName 人物名称
 */
export async function queryPersonalAssistant(
  query: string,
  conversationId?: string,
  personalId?: number,
  personalName?: string
): Promise<any> {
  try {
    logger.debug('前端 - 查询个人专题助手:', { query, conversationId, personalId, personalName });
    const response = await apiClient.post('/ai/personal/query', {
      query,
      conversation_id: conversationId,
      personal_id: personalId,
      personal_name: personalName
    });

    logger.debug('前端 - 个人专题助手响应:', sanitizeData(response.data));

    // 使用统一的响应处理函数
    const processedResponse = processAIResponse(response.data, conversationId);

    // 转换为个人专题助手所需的格式
    return {
      answer: processedResponse.answer,
      conversation_id: processedResponse.conversation_id || null
    };
  } catch (error: any) {
    logger.error('查询个人专题助手失败:', sanitizeData(error));
    // 添加更多错误信息
    if (error.response) {
      logger.error('错误响应:', {
        status: error.response.status,
        data: sanitizeData(error.response.data)
      });
    }
    throw error;
  }
}

/**
 * 查询数据查询助手
 */
export async function queryDataAssistant(query: string, conversationId?: string): Promise<any> {
  try {
    logger.debug('前端 - 查询数据查询助手:', { query, conversationId });

    const response = await apiClient.post('/ai/data-query/query', {
      query,
      conversation_id: conversationId
    })

    logger.debug('前端 - 数据查询助手响应:', sanitizeData(response.data));

    // 使用统一的响应处理函数
    return processAIResponse(response.data, conversationId);
  } catch (error) {
    logger.error('查询数据查询助手失败:', sanitizeData(error))
    throw error
  }
}

/**
 * 设置数据查询助手知识库
 */
export async function setDataAssistantKnowledgeBase(knowledgeBaseId: string): Promise<any> {
  try {
    const response = await apiClient.post('/ai/data-query/set-knowledge-base', {
      knowledge_base_id: knowledgeBaseId
    })

    return response.data.data
  } catch (error) {
    logger.error('设置数据查询助手知识库失败:', sanitizeData(error))
    throw error
  }
}

/**
 * 获取AI助手对话历史
 */
export async function getConversationHistory(assistantType: string, conversationId?: string): Promise<any> {
  try {
    const params: any = {}
    if (conversationId) {
      params.conversation_id = conversationId
    }

    const response = await apiClient.get(`/ai/conversations/${assistantType}`, { params })

    return response.data.data
  } catch (error) {
    logger.error('获取AI助手对话历史失败:', sanitizeData(error))
    throw error
  }
}

/**
 * 清除AI助手对话历史
 */
export async function clearConversationHistory(assistantType: string, conversationId?: string): Promise<boolean> {
  try {
    const data: any = {}
    if (conversationId) {
      data.conversation_id = conversationId
    }

    await apiClient.delete(`/ai/conversations/${assistantType}`, { data })

    return true
  } catch (error) {
    logger.error('清除AI助手对话历史失败:', sanitizeData(error))
    return false
  }
}

/**
 * 统一处理AI助手响应
 * 处理不同格式的响应（JSON或流式响应）
 */
function processAIResponse(responseData: any, conversationId?: string): any {
  // 如果响应是字符串且看起来像流式响应
  if (typeof responseData === 'string' && responseData.startsWith('data: ')) {
    logger.debug('前端 - 检测到流式响应格式 (SSE)');
    try {
      // 尝试解析所有事件
      const events = responseData.split('\n\n')
        .filter(line => line.startsWith('data: '))
        .map(line => {
          try {
            return JSON.parse(line.substring(6)); // 去掉 "data: " 前缀
          } catch (e) {
            logger.error('解析事件失败:', sanitizeData(e), line);
            return null;
          }
        })
        .filter(event => event !== null);

      logger.debug(`前端 - 解析了 ${events.length} 个流式事件`);

      // 如果没有有效事件，返回默认消息
      if (events.length === 0) {
        return {
          id: `ai-${Date.now()}`,
          answer: 'AI助手正在处理您的请求，请稍候...',
          conversation_id: conversationId || '',
          created_at: new Date().toISOString()
        };
      }

      // 查找最后一个包含完整消息的事件
      let finalMessage = '';
      let finalConversationId = '';

      // 首先查找 message_end 事件
      const messageEndEvent = events.find(event => event.event === 'message_end' && event.message);
      if (messageEndEvent) {
        finalMessage = messageEndEvent.message;
        finalConversationId = messageEndEvent.conversation_id || conversationId || '';
        logger.debug('前端 - 找到 message_end 事件，使用其消息内容');
      } else {
        // 如果没有 message_end 事件，查找最后一个 message_stream 事件
        const messageStreamEvents = events.filter(event => event.event === 'message_stream' && event.message);
        if (messageStreamEvents.length > 0) {
          const lastMessageStreamEvent = messageStreamEvents[messageStreamEvents.length - 1];
          finalMessage = lastMessageStreamEvent.message;
          finalConversationId = lastMessageStreamEvent.conversation_id || conversationId || '';
          logger.debug('前端 - 找到 message_stream 事件，使用最后一个事件的消息内容');
        } else {
          // 如果没有包含消息的事件，使用第一个事件的类型作为提示
          const firstEvent = events[0];

          // 根据不同事件类型提供更具体的消息
          switch (firstEvent.event) {
            case 'workflow_started':
              finalMessage = '正在处理您的请求，请稍候...';
              break;
            case 'node_started':
              finalMessage = '正在思考您的问题，请稍候...';
              break;
            case 'message_start':
              finalMessage = '开始生成回复，请稍候...';
              break;
            default:
              finalMessage = `正在处理您的请求 (${firstEvent.event})，请稍候...`;
          }

          finalConversationId = firstEvent.conversation_id || conversationId || '';
          logger.debug(`前端 - 未找到包含消息的事件，使用事件类型 ${firstEvent.event} 作为提示`);
        }
      }

      // 返回处理后的响应
      return {
        id: `ai-${Date.now()}`,
        answer: finalMessage,
        conversation_id: finalConversationId,
        created_at: new Date().toISOString()
      };
    } catch (parseError) {
      logger.error('前端 - 解析流式响应失败:', sanitizeData(parseError));
    }

    // 如果解析失败或没有找到有效事件，返回默认消息
    return {
      id: `ai-${Date.now()}`,
      answer: 'AI助手正在处理您的请求，请稍候...',
      conversation_id: conversationId || '',
      created_at: new Date().toISOString()
    };
  }

  // 处理标准JSON响应
  if (responseData && responseData.data) {
    // 如果data字段是字符串且看起来像流式响应
    if (typeof responseData.data === 'string' && responseData.data.startsWith('data: ')) {
      // 递归调用自身处理流式响应
      return processAIResponse(responseData.data, conversationId);
    }

    // 处理标准JSON响应格式
    if (responseData.data.answer) {
      return {
        id: responseData.data.id || `ai-${Date.now()}`,
        answer: responseData.data.answer,
        conversation_id: responseData.data.conversation_id || conversationId || '',
        created_at: responseData.data.created_at || new Date().toISOString(),
        references: responseData.data.references || []
      };
    } else if (responseData.data.text) {
      return {
        id: responseData.data.id || `ai-${Date.now()}`,
        answer: responseData.data.text,
        conversation_id: responseData.data.conversation_id || conversationId || '',
        created_at: responseData.data.created_at || new Date().toISOString(),
        references: responseData.data.references || []
      };
    } else if (responseData.data.event) {
      // 处理嵌套的事件对象
      return processAIResponse({ event: responseData.data.event, message: responseData.data.message, conversation_id: responseData.data.conversation_id }, conversationId);
    }
  }

  // 如果无法识别响应格式，返回友好的错误消息
  logger.warn('前端 - 无法识别的响应格式:', sanitizeData(responseData));
  return {
    id: `ai-${Date.now()}`,
    answer: '收到了响应，但格式无法解析。请尝试简化您的问题或稍后再试。',
    conversation_id: conversationId || '',
    created_at: new Date().toISOString()
  };
}

/**
 * 查询AI研究助手 - 同步模式
 */
export async function queryResearchAssistant(query: string, conversationId?: string, assistantId?: string): Promise<any> {
  try {
    logger.debug('前端 - 查询AI研究助手:', { query, conversationId, assistantId });

    // 构建请求URL
    let url = '/ai/assistant/query';
    if (assistantId) {
      url = `/ai/assistant/${assistantId}/query`;
    }

    // 构建请求数据
    const requestData = {
      query,
      conversation_id: conversationId
    };

    logger.debug('前端 - 发送请求:', { url, requestData });

    // 设置较长的超时时间
    const timeout = 3600000; // 60分钟

    // 发送请求
    const response = await apiClient.post(url, requestData, { timeout });

    logger.debug('前端 - 收到响应:', sanitizeData(response.data));

    // 使用统一的响应处理函数
    return processAIResponse(response.data, conversationId);
  } catch (error: any) {
    logger.error('查询AI研究助手失败:', sanitizeData(error));
    // 添加更多错误信息
    if (error.response) {
      logger.error('错误响应:', {
        status: error.response.status,
        data: sanitizeData(error.response.data)
      });

      // 如果后端返回了详细错误信息，使用它
      if (error.response.data && error.response.data.message) {
        error.message = error.response.data.message;

        // 添加错误类型信息
        if (error.response.data.errorType) {
          error.errorType = error.response.data.errorType;
        }
      }
    }

    // 添加更友好的错误消息
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      error.friendlyMessage = "请求超时，AI服务器正在生成回复，但需要较长时间。请耐心等待或稍后再试。";
      error.isProcessing = true; // 标记为正在处理中
    } else if (error.response && error.response.status === 404) {
      error.friendlyMessage = "AI助手不存在或未激活，请联系管理员。";
    } else if (error.response && error.response.status === 500) {
      // 检查是否是复杂助手正在处理的错误
      if (error.response.data &&
          typeof error.response.data.message === 'string' &&
          (error.response.data.message.includes('正在处理') ||
           error.response.data.message.includes('生成中') ||
           error.response.data.message.includes('复杂问题'))) {
        error.friendlyMessage = "AI服务正在处理您的复杂问题，请稍后再试。对于论文标题和课题分析等复杂任务，可能需要较长时间。";
        error.isProcessing = true; // 标记为正在处理中
      } else {
        error.friendlyMessage = "服务器内部错误，可能是AI服务正在处理复杂问题。请稍后再试。";
      }
    } else {
      error.friendlyMessage = "调用AI助手失败，请稍后再试。";
    }

    throw error;
  }
}

/**
 * 搜索研究资料
 */
export async function searchResearchMaterials(query: string, filter: string = 'all'): Promise<ResearchResult[]> {
  try {
    const response = await apiClient.get('/ai/assistant/search', {
      params: { query, filter }
    })

    return response.data.data || []
  } catch (error) {
    logger.error('搜索研究资料失败:', sanitizeData(error))
    return []
  }
}

/**
 * 分析知识库文件
 */
export async function analyzeKnowledgeFile(fileId: string, fileName: string, assistantId?: string): Promise<{ summary: string, detailed_description: string }> {
  try {
    const endpoint = assistantId
      ? `/ai/knowledge-file/${assistantId}/analyze`
      : '/ai/knowledge-file/analyze'

    const response = await apiClient.post(endpoint, {
      file_id: fileId,
      file_name: fileName
    })

    return response.data.data
  } catch (error) {
    logger.error('分析知识库文件失败:', sanitizeData(error))
    throw error
  }
}
