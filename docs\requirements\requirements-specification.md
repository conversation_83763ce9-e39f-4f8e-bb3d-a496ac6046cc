# 和富家族研究平台需求规格说明书

## 1. 引言

### 1.1 目的

本文档旨在详细描述和富家族研究平台的功能需求和非功能需求，为系统设计、开发和测试提供基础。文档面向项目开发团队、测试团队和项目管理人员，明确系统的范围、功能和约束。

### 1.2 范围

和富家族研究平台是一个专注于葛健豪家族历史研究和展示的Web应用系统，包括首页、家族专题、个人专题、知识库、数据查询和AI研究助手等模块。系统支持用户注册登录、资料上传查询、活动管理、AI辅助研究等功能。

### 1.3 定义、缩略语和缩写

- **平台**：和富家族研究平台
- **用户**：系统的使用者，包括访客、注册用户、管理员等
- **资料**：与葛健豪家族相关的文档、图片、视频等多媒体内容
- **AI助手**：基于人工智能技术的研究辅助工具
- **API**：应用程序接口(Application Programming Interface)
- **UI**：用户界面(User Interface)
- **UX**：用户体验(User Experience)

### 1.4 参考文献

1. 和富家族研究平台业务需求文档
2. 系统说明文档(system-description.md)
3. React官方文档: https://reactjs.org/docs/getting-started.html
4. Node.js官方文档: https://nodejs.org/en/docs/
5. MySQL官方文档: https://dev.mysql.com/doc/

## 2. 总体描述

### 2.1 产品前景

和富家族研究平台将成为记录和传承葛健豪家族革命历史的重要工具，通过整合历史资料、提供研究工具和促进交流互动，实现对红色基因的传承和弘扬。系统面向家族成员、历史研究者、教育工作者和普通公众，提供全面的家族历史研究和展示服务。

### 2.2 产品功能

1. 用户管理：用户注册、登录、个人信息管理
2. 内容展示：首页、家族专题、个人专题展示
3. 活动管理：纪念活动的展示和管理
4. 知识库管理：资料上传、分类、审核、检索
5. 数据查询：多条件查询、结果展示、导出
6. AI研究助手：智能问答、研究辅助
7. 系统管理：用户管理、角色权限管理、AI管理、系统配置

### 2.3 用户特征

1. **访客**：未登录用户，可浏览公开内容，但无法使用需要登录的功能
2. **注册用户**：已注册并登录的普通用户，可使用基本功能
3. **研究员**：具有更高权限的用户，可访问更多研究资料
4. **管理员**：系统管理人员，负责内容审核、用户管理等
5. **超级管理员**：具有最高权限，可进行所有系统管理操作

### 2.4 约束

1. **技术约束**：
   - 前端使用React框架
   - 后端使用Node.js
   - 数据库使用MySQL
   - 系统应支持主流Web浏览器

2. **硬件约束**：
   - 服务器配置适中，满足100-200用户同时在线需求
   - 存储空间足够存储系统资料

3. **软件约束**：
   - 操作系统：支持Windows、Linux、macOS
   - 浏览器：支持Chrome、Firefox、Safari、Edge等主流浏览器

4. **安全约束**：
   - 用户密码加密存储
   - 敏感数据传输加密
   - 防止SQL注入、XSS等常见Web安全问题

### 2.5 假设和依赖

1. **假设**：
   - 用户具备基本的计算机和互联网使用能力
   - 系统运行环境稳定，网络连接可靠
   - 用户上传的资料内容合法合规

2. **依赖**：
   - 依赖Dify平台提供AI服务
   - 依赖第三方存储服务存储上传的文件
   - 依赖邮件服务发送通知

## 3. 详细需求

### 3.1 外部接口需求

#### 3.1.1 用户界面

1. **通用界面要求**：
   - 响应式设计，适配不同屏幕尺寸
   - 主题色：绿色(#1e7a43)、橙色(#f5a623)
   - 背景色：浅米色(#fdf9f1)
   - 统一的导航栏和页脚

2. **首页界面**：
   - 导航栏
   - 英雄区域
   - 家族历史长河
   - 革命先辈事迹
   - 纪念活动
   - 页脚

3. **家族专题界面**：
   - 英雄区域
   - 家族历史概述
   - 家族重要事件时间线

4. **个人专题界面**：
   - 个人基本信息
   - 人生轨迹
   - 历史贡献
   - 相关资料
   - AI助手对话框

5. **知识库界面**：
   - 资料分类导航
   - 资料列表
   - 资料详情
   - 上传界面
   - 搜索界面

6. **数据查询界面**：
   - 查询条件设置
   - 结果展示
   - AI查询助手

7. **AI研究助手界面**：
   - 助手列表
   - 对话界面

8. **系统管理界面**：
   - 用户管理
   - 权限管理
   - 留言管理
   - 系统配置
   - AI管理

#### 3.1.2 硬件接口

系统为Web应用，不需要特定的硬件接口，用户通过标准的计算机设备和浏览器访问系统。

#### 3.1.3 软件接口

1. **前端框架**：
   - React.js
   - Next.js
   - Tailwind CSS

2. **后端框架**：
   - Node.js
   - Express.js

3. **数据库**：
   - MySQL

4. **外部服务接口**：
   - Dify API：用于AI助手功能
   - 文件存储服务：用于存储上传的文件
   - 邮件服务：用于发送通知

#### 3.1.4 通信接口

1. **HTTP/HTTPS**：系统使用HTTP/HTTPS协议进行前后端通信
2. **WebSocket**：用于实时通信功能（如通知推送）
3. **RESTful API**：系统内部模块间通信采用RESTful风格的API

### 3.2 功能需求

#### 3.2.1 用户管理

1. **用户注册**
   - 描述：新用户通过填写注册表单创建账号
   - 输入：用户名、密码、手机号、邮箱
   - 处理：验证信息有效性，创建用户账号
   - 输出：注册成功提示，显示登录弹窗

2. **用户登录**
   - 描述：已注册用户通过账号密码登录系统
   - 输入：用户名/邮箱、密码
   - 处理：验证用户身份
   - 输出：登录成功，更新导航栏显示

3. **个人中心**
   - 描述：用户查看和管理个人信息
   - 功能：查看个人资料、修改密码、查看通知、管理收藏

4. **权限控制**
   - 描述：根据用户角色控制功能访问权限
   - 功能：角色分配、权限设置、权限检查

#### 3.2.2 内容展示

1. **首页展示**
   - 描述：展示系统主要内容和功能入口
   - 功能：展示家族历史、革命先辈、纪念活动等

2. **家族专题**
   - 描述：展示家族历史概述和重要事件
   - 功能：历史概述、事件时间线展示

3. **个人专题**
   - 描述：展示家族成员的个人信息和贡献
   - 功能：基本信息、人生轨迹、历史贡献、相关资料展示

#### 3.2.3 活动管理

1. **活动展示**
   - 描述：展示与家族相关的纪念活动
   - 功能：活动列表、活动详情查看

2. **活动管理**
   - 描述：管理员管理纪念活动
   - 功能：添加、编辑、删除活动，设置活动状态

#### 3.2.4 知识库管理

1. **资料上传**
   - 描述：用户上传与家族相关的资料
   - 功能：文件上传、信息填写、自动分析

2. **资料分类**
   - 描述：对资料进行分类管理
   - 功能：分类创建、资料归类、分类导航

3. **资料检索**
   - 描述：多维度检索资料
   - 功能：关键词搜索、条件筛选、结果排序

#### 3.2.5 数据查询

1. **查询界面**
   - 描述：提供用户友好的查询界面
   - 功能：条件设置、数据源选择

2. **结果展示**
   - 描述：展示查询结果
   - 功能：表格/图表展示、导出功能

3. **AI辅助查询**
   - 描述：AI辅助用户进行复杂查询
   - 功能：自然语言查询、查询建议

#### 3.2.6 AI研究助手

1. **助手选择**
   - 描述：用户选择合适的AI助手
   - 功能：助手列表、助手详情

2. **智能对话**
   - 描述：用户与AI助手进行对话
   - 功能：问题输入、回答展示、对话历史

#### 3.2.7 系统管理

1. **用户管理**
   - 描述：管理系统用户
   - 功能：用户列表、添加/编辑/删除用户、密码重置

2. **角色权限管理**
   - 描述：管理用户角色和权限
   - 功能：角色列表、权限设置、权限分配

3. **留言管理**
   - 描述：管理用户留言
   - 功能：留言列表、审核/删除留言

4. **系统配置**
   - 描述：管理系统基本配置
   - 功能：安全设置、参数配置

5. **AI管理**
   - 描述：管理系统中的AI助手
   - 功能：AI助手列表、添加/编辑/删除AI助手、API配置

### 3.3 性能需求

1. **响应时间**：
   - 页面加载时间不超过3秒
   - 数据查询响应时间不超过5秒
   - AI助手回复时间不超过10秒

2. **并发用户**：
   - 支持100-200名用户同时在线
   - 日访问量在几百次

3. **数据容量**：
   - 支持存储数万条历史记录
   - 支持存储数千个文件资料

### 3.4 安全需求

1. **用户认证**：
   - 用户密码加密存储
   - 登录失败次数限制
   - 会话超时自动登出

2. **数据安全**：
   - 敏感数据传输加密
   - 数据库定期备份
   - 防止SQL注入攻击

3. **权限控制**：
   - 基于角色的访问控制
   - 操作日志记录
   - 敏感操作二次确认

### 3.5 软件质量属性

1. **可用性**：
   - 系统7*24小时可用
   - 计划内维护时间提前通知
   - 系统故障恢复时间不超过4小时

2. **可靠性**：
   - 系统平均无故障时间不少于30天
   - 数据丢失率为零

3. **可维护性**：
   - 模块化设计，便于维护和升级
   - 完善的日志记录，便于问题定位
   - 代码注释完善，便于理解

4. **可扩展性**：
   - 支持功能模块的扩展
   - 支持用户规模的增长
   - 支持数据量的增长

5. **易用性**：
   - 直观的用户界面
   - 清晰的操作指引
   - 一致的交互模式

## 4. 验收标准

### 4.1 功能验收标准

1. **用户管理**：
   - 用户可以成功注册账号
   - 用户可以使用账号密码登录
   - 用户可以查看和修改个人信息

2. **内容展示**：
   - 首页正确展示所有内容
   - 家族专题页面展示完整的历史概述和事件时间线
   - 个人专题页面展示完整的个人信息和贡献

3. **活动管理**：
   - 活动列表正确显示已发布活动
   - 管理员可以添加、编辑、删除活动
   - 活动详情可以正确查看

4. **知识库管理**：
   - 用户可以上传资料
   - 资料可以正确分类
   - 资料可以通过多种条件检索

5. **数据查询**：
   - 查询界面可以设置多种条件
   - 查询结果正确显示
   - 结果可以导出

6. **AI研究助手**：
   - 用户可以选择不同类型的AI助手
   - AI助手可以回答用户问题
   - 对话历史可以正确保存

7. **系统管理**：
   - 管理员可以管理用户
   - 管理员可以设置角色权限
   - 管理员可以配置系统参数
   - 管理员可以管理AI助手

### 4.2 性能验收标准

1. 在100用户同时在线的情况下，系统响应时间不超过3秒
2. 数据查询在数据量为1万条的情况下，响应时间不超过5秒
3. 系统在连续运行30天后，功能正常，无明显性能下降

### 4.3 安全验收标准

1. 未授权用户无法访问需要登录的功能
2. 普通用户无法访问管理功能
3. 密码在数据库中以加密形式存储
4. 系统能够抵御常见的Web安全攻击（如SQL注入、XSS等）

## 5. 附录

### 5.1 术语表

| 术语 | 定义 |
|------|------|
| 和富家族 | 以葛健豪为代表的革命家族，包括蔡和森、向警予、蔡畅、李富春等成员 |
| AI助手 | 基于人工智能技术的研究辅助工具，包括个人专题助手、数据查询助手、AI研究助手、知识库文件分析助手 |
| Dify | 提供AI服务的第三方平台 |
| 知识库 | 存储和管理与家族相关资料的系统模块 |

### 5.2 分析模型

1. **用户角色模型**：描述系统中的用户角色及其权限
2. **功能模块图**：描述系统的主要功能模块及其关系
3. **数据流图**：描述系统中的数据流向
4. **状态转换图**：描述系统中重要对象的状态变化

### 5.3 待解决问题

1. AI助手的具体实现方式和与Dify平台的集成细节
2. 大量历史资料的存储和检索优化方案
3. 系统扩展性与性能平衡的具体策略