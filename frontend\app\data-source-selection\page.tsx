"use client"

import { useState } from "react"
import { Navbar } from "@/components/navbar"
import { MultiSelectKnowledgeBase } from "@/components/multi-select-knowledge-base"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Database, Filter, ArrowRight } from "lucide-react"

// 示例知识库数据
const knowledgeBases = [
  {
    id: "SYS001",
    name: "革命先辈资料库",
    type: "系统" as const,
    description: "包含革命先辈的历史资料、照片和文献",
  },
  {
    id: "SYS002",
    name: "家族历史档案",
    type: "系统" as const,
    description: "记录家族历史发展的重要文献和资料",
  },
  {
    id: "SYS003",
    name: "学术研究资料",
    type: "系统" as const,
    description: "与家族相关的学术研究论文和资料",
  },
  {
    id: "SYS004",
    name: "媒体资源库",
    type: "系统" as const,
    description: "包含视频、音频和图片等多媒体资源",
  },
  {
    id: "KB001",
    name: "个人研究资料",
    type: "用户" as const,
    description: "个人研究相关的资料和笔记",
  },
  {
    id: "KB002",
    name: "项目文档",
    type: "用户" as const,
    description: "工作项目相关的文档和资料",
  },
]

export default function DataSourceSelectionPage() {
  const [selectedKnowledgeBaseIds, setSelectedKnowledgeBaseIds] = useState<string[]>([])

  const handleSubmit = () => {
    // 在实际应用中，这里会处理表单提交
    console.log("Selected knowledge bases:", selectedKnowledgeBaseIds)
    alert(`已选择 ${selectedKnowledgeBaseIds.length} 个知识库作为数据源`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">数据源选择</h1>
          <p className="text-gray-500 mb-8">选择您要查询或分析的数据源</p>

          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2 text-emerald-600" />
                选择知识库
              </CardTitle>
              <CardDescription>
                您可以选择一个或多个知识库作为数据源。系统将从选定的知识库中检索和分析数据。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">知识库选择</label>
                  <MultiSelectKnowledgeBase
                    knowledgeBases={knowledgeBases}
                    selectedIds={selectedKnowledgeBaseIds}
                    onChange={setSelectedKnowledgeBaseIds}
                    placeholder="请选择一个或多个知识库..."
                  />
                  <p className="text-xs text-gray-500">提示：您可以选择多个知识库进行跨库查询和分析</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">高级筛选</label>
                    <Button variant="ghost" size="sm" className="h-8 text-emerald-600">
                      <Filter className="h-3.5 w-3.5 mr-1" />
                      添加筛选条件
                    </Button>
                  </div>
                  <div className="bg-gray-50 border border-gray-200 rounded-md p-4 text-center text-sm text-gray-500">
                    暂无筛选条件，点击"添加筛选条件"自定义数据范围
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between border-t pt-6">
              <Button variant="outline">取消</Button>
              <Button
                className="bg-emerald-600 hover:bg-emerald-700"
                onClick={handleSubmit}
                disabled={selectedKnowledgeBaseIds.length === 0}
              >
                下一步
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>

          {selectedKnowledgeBaseIds.length > 0 && (
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="text-sm font-medium text-blue-800 mb-2">已选择的知识库</h3>
              <div className="flex flex-wrap gap-2">
                {knowledgeBases
                  .filter((kb) => selectedKnowledgeBaseIds.includes(kb.id))
                  .map((kb) => (
                    <div
                      key={kb.id}
                      className={`px-3 py-1.5 rounded-full text-sm ${
                        kb.type === "系统" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800"
                      }`}
                    >
                      {kb.name}
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
