/**
 * 用户服务
 *
 * 提供与用户相关的API调用
 */

import axios from 'axios';

// 获取API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';

// 用户类型
export interface User {
  id: number;
  username: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: string;
  role_id?: number;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

// 登录请求
export interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应
export interface LoginResponse {
  token: string;
  user: User;
}

// 注册请求
export interface RegisterRequest {
  username: string;
  password: string;
  email: string;
  phone?: string;
}

// 更新用户请求
export interface UpdateUserRequest {
  email?: string;
  phone?: string;
  role_id?: number;
  is_active?: boolean;
}

// 修改密码请求
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// 用户服务
const userService = {
  /**
   * 用户登录
   * @param data 登录请求数据
   * @returns 登录响应
   */
  async login(data: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await axios.post(`${API_BASE_URL}/users/login`, data);

      // 保存令牌到本地存储
      if (response.data.data.token) {
        localStorage.setItem('token', response.data.data.token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.data.token}`;
      }

      return response.data.data;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  /**
   * 用户注册
   * @param data 注册请求数据
   * @returns 注册响应
   */
  async register(data: RegisterRequest): Promise<User> {
    try {
      const response = await axios.post(`${API_BASE_URL}/users/register`, data);
      return response.data.data;
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  },

  /**
   * 获取当前用户信息
   * @returns 用户信息
   */
  async getCurrentUser(): Promise<User> {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/me`);
      return response.data.data;
    } catch (error) {
      console.error('获取当前用户信息失败:', error);
      throw error;
    }
  },

  /**
   * 更新当前用户信息
   * @param data 更新请求数据
   * @returns 更新后的用户信息
   */
  async updateCurrentUser(data: { email?: string; phone?: string }): Promise<User> {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/me`, data);
      return response.data.data;
    } catch (error) {
      console.error('更新当前用户信息失败:', error);
      throw error;
    }
  },

  /**
   * 修改密码
   * @param data 修改密码请求数据
   * @returns 操作结果
   */
  async changePassword(data: ChangePasswordRequest): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/change-password`, data);
      return response.data;
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    }
  },

  /**
   * 获取所有用户
   * @returns 用户列表
   */
  async getAllUsers(): Promise<User[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/users`);
      return response.data.data;
    } catch (error) {
      console.error('获取所有用户失败:', error);
      throw error;
    }
  },

  /**
   * 获取用户详情
   * @param id 用户ID
   * @returns 用户详情
   */
  async getUserById(id: number): Promise<User> {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('获取用户详情失败:', error);
      throw error;
    }
  },

  /**
   * 更新用户
   * @param id 用户ID
   * @param data 更新请求数据
   * @returns 更新后的用户信息
   */
  async updateUser(id: number, data: UpdateUserRequest): Promise<User> {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/${id}`, data);
      return response.data.data;
    } catch (error) {
      console.error('更新用户失败:', error);
      throw error;
    }
  },

  /**
   * 删除用户
   * @param id 用户ID
   * @returns 操作结果
   */
  async deleteUser(id: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.delete(`${API_BASE_URL}/users/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    }
  },

  /**
   * 重置用户密码
   * @param id 用户ID
   * @param newPassword 新密码
   * @returns 操作结果
   */
  async resetUserPassword(id: number, newPassword: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/${id}/reset-password`, {
        new_password: newPassword
      });
      return response.data;
    } catch (error) {
      console.error('重置用户密码失败:', error);
      throw error;
    }
  },

  /**
   * 用户登出
   */
  logout(): void {
    localStorage.removeItem('token');
    delete axios.defaults.headers.common['Authorization'];
  },

  /**
   * 设置认证令牌
   * @param token 认证令牌
   */
  setAuthToken(token: string): void {
    localStorage.setItem('token', token);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  },

  /**
   * 获取认证令牌
   * @returns 认证令牌
   */
  getAuthToken(): string | null {
    return localStorage.getItem('token');
  },

  /**
   * 检查是否已认证
   * @returns 是否已认证
   */
  isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }
};

export default userService;
