"use client"

import { FileText, Download, Database, User, Clock, Tag } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { KnowledgeFile } from "@/components/knowledge/types"

// 组件属性
interface KnowledgeFileDetailDialogProps {
  isOpen: boolean
  onClose: () => void
  file: KnowledgeFile | null
  onDownload: (fileId: string) => void
}

/**
 * 知识库文件详情对话框组件
 * 
 * 用于显示知识库文件的详细信息
 */
export function KnowledgeFileDetailDialog({
  isOpen,
  onClose,
  file,
  onDownload
}: KnowledgeFileDetailDialogProps) {
  if (!file) return null

  // 获取文件图标
  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case "pdf":
        return <FileText className="text-red-500" />
      case "docx":
      case "doc":
        return <FileText className="text-blue-500" />
      case "xlsx":
      case "xls":
        return <FileText className="text-green-500" />
      case "pptx":
      case "ppt":
        return <FileText className="text-orange-500" />
      case "png":
      case "jpg":
      case "jpeg":
        return <FileText className="text-purple-500" />
      case "mp4":
      case "mov":
        return <FileText className="text-indigo-500" />
      case "zip":
      case "rar":
        return <FileText className="text-yellow-500" />
      default:
        return <FileText className="text-gray-500" />
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center">
            <div className="h-8 w-8 rounded-md bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0 shadow-sm border border-gray-200">
              {getFileIcon(file.file_type)}
            </div>
            {file.name}
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-wrap items-center text-sm text-gray-500 mb-4">
          <div className="flex items-center mr-4 mb-2">
            <Database className="h-4 w-4 mr-1 text-emerald-500" />
            <span>知识库: {file.knowledge_base_name}</span>
          </div>
          <div className="flex items-center mr-4 mb-2">
            <Clock className="h-4 w-4 mr-1 text-gray-500" />
            <span>上传时间: {file.created_at}</span>
          </div>
          <div className="flex items-center mr-4 mb-2">
            <User className="h-4 w-4 mr-1 text-gray-500" />
            <span>上传者: {file.creator_name}</span>
          </div>
          <div className="flex items-center mr-4 mb-2">
            <FileText className="h-4 w-4 mr-1 text-gray-500" />
            <span>文件大小: {file.file_size}</span>
          </div>
          <div className="flex items-center mb-2">
            <Tag className="h-4 w-4 mr-1 text-gray-500" />
            <span>文件类型: {file.file_type}</span>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-md mb-4">
          <h3 className="font-bold text-lg mb-2">文件摘要</h3>
          <p className="text-gray-700 mb-4">{file.summary || "无摘要信息"}</p>

          <h3 className="font-bold text-lg mb-2">详细描述</h3>
          <p className="text-gray-700 whitespace-pre-line">{file.detailed_description || "无详细描述"}</p>
        </div>

        {file.status === "已驳回" && file.reject_reason && (
          <div className="bg-red-50 p-4 rounded-md mb-4 border border-red-200">
            <h3 className="font-bold text-lg mb-2 text-red-800">驳回原因</h3>
            <p className="text-red-700">{file.reject_reason}</p>
          </div>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            className="mr-2"
            onClick={onClose}
          >
            关闭
          </Button>
          <Button
            className="bg-[#f5a623] hover:bg-[#f5a623]/90 flex items-center"
            onClick={() => onDownload(file.id)}
          >
            <Download className="h-4 w-4 mr-2" />
            下载文件
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
