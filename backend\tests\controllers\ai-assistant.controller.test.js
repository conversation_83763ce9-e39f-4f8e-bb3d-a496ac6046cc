/**
 * AI助手控制器测试
 * 
 * 测试AI助手相关的API端点和业务逻辑
 */

const request = require('supertest');
const app = require('../../src/app');
const { AIAssistant, User } = require('../../src/models');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

describe('AI助手控制器', () => {
  let testUser;
  let adminUser;
  let testAssistant;
  let userToken;
  let adminToken;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试用户
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash('testpassword', salt);
    
    testUser = await User.create({
      username: 'aiuser',
      password: passwordHash,
      email: '<EMAIL>',
      phone: '13800000001',
      role: 'basic_user',
      is_active: true
    });

    // 创建管理员用户
    const adminPasswordHash = await bcrypt.hash('adminpassword', salt);
    
    adminUser = await User.create({
      username: 'aiadmin',
      password: adminPasswordHash,
      email: '<EMAIL>',
      phone: '13900000001',
      role: 'admin',
      is_active: true
    });

    // 创建测试AI助手
    testAssistant = await AIAssistant.create({
      name: 'Test Assistant',
      type: 'personal',
      description: 'Test AI assistant for unit testing',
      api_endpoint: 'https://api.example.com',
      api_key: 'test_api_key',
      status: 'active',
      creator_id: adminUser.id,
      last_updated_by: adminUser.id
    });

    // 生成测试用的JWT令牌
    userToken = jwt.sign(
      { id: testUser.id, role: testUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );

    adminToken = jwt.sign(
      { id: adminUser.id, role: adminUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await AIAssistant.destroy({ where: { id: testAssistant.id } });
    await User.destroy({ where: { id: testUser.id } });
    await User.destroy({ where: { id: adminUser.id } });
  });

  // 测试获取AI助手列表
  describe('获取AI助手列表', () => {
    test('应该返回所有活跃的AI助手', async () => {
      const response = await request(app)
        .get('/api/ai')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      
      // 验证返回的助手数据
      const assistant = response.body.data.find(a => a.id === testAssistant.id);
      expect(assistant).toBeDefined();
      expect(assistant).toHaveProperty('name', 'Test Assistant');
      expect(assistant).toHaveProperty('type', 'personal');
      expect(assistant).toHaveProperty('description', 'Test AI assistant for unit testing');
      expect(assistant).not.toHaveProperty('api_key'); // 普通用户不应看到API密钥
    });

    test('应该根据类型筛选AI助手', async () => {
      const response = await request(app)
        .get('/api/ai?type=personal')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      
      // 验证所有返回的助手都是personal类型
      response.body.data.forEach(assistant => {
        expect(assistant.type).toBe('personal');
      });
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/ai');
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('未授权');
    });
  });

  // 测试获取AI助手详情
  describe('获取AI助手详情', () => {
    test('应该返回特定AI助手的详情', async () => {
      const response = await request(app)
        .get(`/api/ai/${testAssistant.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', testAssistant.id);
      expect(response.body.data).toHaveProperty('name', 'Test Assistant');
      expect(response.body.data).toHaveProperty('type', 'personal');
      expect(response.body.data).not.toHaveProperty('api_key'); // 普通用户不应看到API密钥
    });

    test('管理员应该能看到完整的AI助手信息', async () => {
      const response = await request(app)
        .get(`/api/ai/${testAssistant.id}`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', testAssistant.id);
      expect(response.body.data).toHaveProperty('api_key', 'test_api_key'); // 管理员应该能看到API密钥
      expect(response.body.data).toHaveProperty('api_endpoint', 'https://api.example.com');
    });

    test('应该返回404当AI助手不存在', async () => {
      const response = await request(app)
        .get('/api/ai/9999') // 不存在的ID
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('不存在');
    });
  });

  // 测试更新AI助手
  describe('更新AI助手', () => {
    test('管理员应该能更新AI助手', async () => {
      const response = await request(app)
        .put(`/api/ai/${testAssistant.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Updated Assistant',
          description: 'Updated description',
          api_key: 'updated_api_key'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', 'AI助手更新成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('name', 'Updated Assistant');
      expect(response.body.data).toHaveProperty('description', 'Updated description');
      
      // 恢复原始数据
      await testAssistant.update({
        name: 'Test Assistant',
        description: 'Test AI assistant for unit testing',
        api_key: 'test_api_key'
      });
    });

    test('普通用户不应该能更新AI助手', async () => {
      const response = await request(app)
        .put(`/api/ai/${testAssistant.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          name: 'Unauthorized Update',
          description: 'This should not work'
        });
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test('应该验证更新数据', async () => {
      const response = await request(app)
        .put(`/api/ai/${testAssistant.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: '', // 空名称
          description: 'Invalid update'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('名称不能为空');
    });
  });

  // 测试查询AI助手
  describe('查询AI助手', () => {
    test('应该能使用个人专题助手', async () => {
      // 注意：这个测试可能需要模拟Dify API调用
      const response = await request(app)
        .post('/api/ai/personal/query')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          query: 'Test query',
          assistant_id: testAssistant.id
        });
      
      // 由于实际调用可能会失败，我们只检查API是否正确接收请求
      expect(response.status).not.toBe(401); // 不应该是未授权错误
      expect(response.status).not.toBe(403); // 不应该是权限错误
    });

    test('应该能使用数据查询助手', async () => {
      // 注意：这个测试可能需要模拟Dify API调用
      const response = await request(app)
        .post('/api/ai/data/query')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          query: 'Test data query',
          assistant_id: testAssistant.id,
          knowledge_base_id: 1 // 假设的知识库ID
        });
      
      // 由于实际调用可能会失败，我们只检查API是否正确接收请求
      expect(response.status).not.toBe(401); // 不应该是未授权错误
      expect(response.status).not.toBe(403); // 不应该是权限错误
    });

    test('应该拒绝未认证的查询请求', async () => {
      const response = await request(app)
        .post('/api/ai/personal/query')
        .send({
          query: 'Unauthorized query',
          assistant_id: testAssistant.id
        });
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('未授权');
    });
  });
});
