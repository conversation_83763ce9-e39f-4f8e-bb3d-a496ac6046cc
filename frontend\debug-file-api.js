/**
 * 调试文件API调用
 */
const axios = require('axios');

// API基础URL
const API_BASE_URL = 'http://localhost:5001/api';

// 模拟API调用
async function debugFileApi() {
  try {
    console.log('调试文件API调用...');
    
    // 使用已知的知识库ID
    const knowledgeBaseId = 75;
    
    // 调用API
    console.log(`调用知识库文件列表API: GET /files/knowledge-base/${knowledgeBaseId}`);
    const response = await axios.get(`${API_BASE_URL}/files/knowledge-base/${knowledgeBaseId}`, {
      headers: {
        'Authorization': 'Bearer YOUR_TOKEN_HERE' // 替换为有效的token
      }
    });
    
    console.log('API响应状态码:', response.status);
    
    // 检查响应数据结构
    if (response.data && response.data.success) {
      const { data } = response.data;
      
      console.log('\n知识库信息:');
      console.log('  ID:', data.knowledgeBase.id);
      console.log('  名称:', data.knowledgeBase.name);
      console.log('  类型:', data.knowledgeBase.type);
      
      console.log('\n文件列表:');
      if (data.files && data.files.length > 0) {
        console.log(`找到 ${data.files.length} 个文件`);
        
        data.files.forEach((file, index) => {
          console.log(`\n文件 ${index + 1}:`);
          console.log('  ID:', file.id);
          console.log('  名称:', file.original_name);
          console.log('  类型:', file.type);
          console.log('  大小:', file.size, '字节');
          console.log('  上传者ID:', file.uploader_id);
          
          // 检查上传者信息
          console.log('  上传者信息类型:', typeof file.uploader);
          console.log('  上传者信息是否为null:', file.uploader === null);
          
          if (file.uploader) {
            console.log('  上传者信息:');
            console.log('    ID:', file.uploader.id);
            console.log('    用户名:', file.uploader.username);
            console.log('    邮箱:', file.uploader.email);
          } else {
            console.log('  上传者信息: 无法获取');
          }
        });
        
        // 模拟前端处理
        console.log('\n模拟前端处理:');
        const processedFiles = data.files.map(file => {
          console.log(`处理文件: ${file.id}, ${file.original_name}`);
          
          // 如果上传者信息不是对象或为null，但有上传者ID，尝试创建上传者对象
          if ((!file.uploader || typeof file.uploader !== 'object') && file.uploader_id) {
            console.log('  文件上传者信息无效但有上传者ID，尝试创建上传者对象');
            return {
              ...file,
              uploader: {
                id: file.uploader_id,
                username: `用户ID: ${file.uploader_id}`,
                email: ''
              }
            };
          }
          // 如果上传者信息不是对象或为null，设置为默认对象
          else if (!file.uploader || typeof file.uploader !== 'object') {
            console.log('  文件上传者信息无效，设置为默认值');
            return {
              ...file,
              uploader: { id: null, username: '未知用户', email: '' }
            };
          }
          
          console.log('  文件上传者信息有效:', file.uploader.username);
          return file;
        });
        
        // 检查处理后的文件
        console.log('\n处理后的文件:');
        processedFiles.forEach((file, index) => {
          console.log(`文件 ${index + 1}: ${file.id}, ${file.original_name}`);
          console.log('  上传者:', file.uploader ? file.uploader.username : '未知');
        });
      } else {
        console.log('没有找到文件');
      }
    } else {
      console.error('API响应错误:', response.data);
    }
  } catch (error) {
    console.error('API调用失败:', error.message);
    if (error.response) {
      console.error('错误响应:', error.response.data);
    }
  }
}

debugFileApi();
