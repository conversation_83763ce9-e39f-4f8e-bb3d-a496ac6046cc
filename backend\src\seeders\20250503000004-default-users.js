/**
 * 默认用户种子数据
 */
'use strict';
const bcrypt = require('bcryptjs');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 获取角色ID
    const [roles] = await queryInterface.sequelize.query(
      `SELECT id, name FROM roles WHERE name IN ('管理员', '访问者')`
    );

    const adminRole = roles.find(role => role.name === '管理员');
    const basicRole = roles.find(role => role.name === '访问者');

    if (!adminRole || !basicRole) {
      console.error('未找到所需角色，请先运行角色种子数据');
      return;
    }

    // 生成密码哈希
    const salt = await bcrypt.genSalt(10);
    const adminPasswordHash = await bcrypt.hash('admin123', salt);
    const userPasswordHash = await bcrypt.hash('user123', salt);

    await queryInterface.bulkInsert('users', [
      {
        username: 'admin',
        password: adminPasswordHash,
        email: '<EMAIL>',
        phone: '13800000000',
        role: 'admin',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        username: 'user',
        password: userPasswordHash,
        email: '<EMAIL>',
        phone: '13900000000',
        role: 'basic_user',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('users', {
      username: {
        [Sequelize.Op.in]: ['admin', 'user']
      }
    }, {});
  }
};
