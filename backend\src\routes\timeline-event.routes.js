/**
 * 时间轴事件相关路由
 *
 * 处理时间轴事件的创建、查询、更新、删除等请求
 */

const express = require('express');
const router = express.Router();
const timelineEventController = require('../controllers/timeline-event.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../public/uploads/timeline-icons');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'icon-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/svg+xml'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型，只允许上传JPG、PNG、GIF和SVG图片'), false);
  }
};

// 配置multer - 设置非常大的文件大小限制（实质上无限制）
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 1024 * 1024 * 1024 // 1GB
  }
});

// 获取时间轴事件列表 (所有人可访问)
router.get('/',
  timelineEventController.getTimelineEvents
);

// 获取时间轴事件详情 (所有人可访问)
router.get('/:id',
  timelineEventController.getTimelineEventById
);

// 创建时间轴事件 (需要认证和权限)
router.post('/',
  authMiddleware,
  checkPermission('edit_timeline'),
  timelineEventController.createTimelineEvent
);

// 上传时间轴事件图标 (需要认证和权限)
router.post('/upload-icon',
  authMiddleware,
  checkPermission('edit_timeline'),
  upload.single('icon'),
  timelineEventController.uploadTimelineEventIcon
);

// 更新时间轴事件 (需要认证和权限)
router.put('/:id',
  authMiddleware,
  checkPermission('edit_timeline'),
  timelineEventController.updateTimelineEvent
);

// 删除时间轴事件 (需要认证和权限)
router.delete('/:id',
  authMiddleware,
  checkPermission('edit_timeline'),
  timelineEventController.deleteTimelineEvent
);

module.exports = router;
