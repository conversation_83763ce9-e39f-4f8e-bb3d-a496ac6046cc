/**
 * 将现有文件的状态更改为approved，然后查看是否自动推送到Dify
 */

const { Sequelize } = require('sequelize');
require('dotenv').config();

// 连接数据库
const sequelize = new Sequelize(
  process.env.DB_NAME || 'hefamily_dev',
  process.env.DB_USERNAME || 'root',
  process.env.DB_PASSWORD || 'Misaya9987.',
  {
    host: process.env.DB_HOST || 'localhost',
    dialect: 'mysql',
    logging: console.log
  }
);

// 定义文件模型
const File = sequelize.define('File', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: Sequelize.STRING,
  original_name: Sequelize.STRING,
  path: Sequelize.STRING,
  type: Sequelize.STRING,
  size: Sequelize.INTEGER,
  status: Sequelize.STRING,
  dify_task_id: Sequelize.STRING,
  analysis_status: Sequelize.STRING,
  analysis_error: Sequelize.TEXT
}, {
  tableName: 'files',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 主函数
async function main() {
  try {
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 获取最新上传的文件
    const fileId = process.argv[2]; // 从命令行参数获取文件ID
    console.log(`命令行参数文件ID: ${fileId || '未指定'}`);
    
    let file;
    if (fileId) {
      // 如果指定了文件ID，则获取该文件
      console.log(`尝试获取ID为 ${fileId} 的文件...`);
      file = await File.findByPk(fileId);
      if (!file) {
        console.error(`未找到ID为 ${fileId} 的文件`);
        process.exit(1);
      }
    } else {
      // 否则获取最新上传的文件
      console.log('尝试获取最新上传的文件...');
      file = await File.findOne({
        order: [['created_at', 'DESC']]
      });
      
      if (!file) {
        console.error('未找到任何文件');
        process.exit(1);
      }
    }
    
    console.log(`找到文件: ${file.id}, ${file.original_name}`);
    console.log(`文件状态: ${file.status}`);
    console.log(`文件路径: ${file.path}`);
    console.log(`文件大小: ${file.size}`);
    console.log(`文件类型: ${file.type}`);
    console.log(`Dify任务ID: ${file.dify_task_id || '无'}`);
    console.log(`分析状态: ${file.analysis_status || '无'}`);
    
    // 更新文件状态为approved
    if (file.status !== 'approved') {
      console.log(`将文件状态从 ${file.status} 更新为 approved...`);
      await file.update({ status: 'approved' });
      console.log('文件状态已更新为approved');
      console.log('请检查后端日志，看是否自动推送到Dify');
    } else {
      console.log('文件已经是approved状态');
      
      // 如果文件已经是approved状态，但没有Dify任务ID，则手动触发上传
      if (!file.dify_task_id) {
        console.log('文件没有Dify任务ID，尝试手动触发上传...');
        
        // 将文件状态临时更改为pending，然后再改回approved，以触发上传
        await file.update({ status: 'pending' });
        console.log('文件状态已临时更改为pending');
        
        // 等待1秒
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 将文件状态改回approved
        await file.update({ status: 'approved' });
        console.log('文件状态已改回approved');
        console.log('请检查后端日志，看是否自动推送到Dify');
      } else {
        console.log(`文件已有Dify任务ID: ${file.dify_task_id}`);
      }
    }
    
    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('发生错误:', error);
    process.exit(1);
  }
}

// 执行主函数
main().then(() => {
  console.log('测试完成');
  process.exit(0);
}).catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
