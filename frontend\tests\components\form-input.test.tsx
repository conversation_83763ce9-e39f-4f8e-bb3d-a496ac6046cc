/**
 * FormInput组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { FormInput } from '@/components/ui/form-input'
import { validateRequired, validateEmail } from '@/lib/form-validation'

describe('FormInput组件', () => {
  // 测试基本渲染
  test('应该正确渲染带标签的输入框', () => {
    render(<FormInput id="test-input" label="测试标签" placeholder="请输入" />)
    
    expect(screen.getByLabelText('测试标签')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('请输入')).toBeInTheDocument()
  })

  // 测试值变化
  test('应该正确处理值变化', () => {
    const handleChange = jest.fn()
    render(
      <FormInput
        id="test-input"
        label="测试标签"
        onChange={handleChange}
      />
    )
    
    const input = screen.getByLabelText('测试标签')
    fireEvent.change(input, { target: { value: '测试值' } })
    
    expect(handleChange).toHaveBeenCalled()
    expect(input).toHaveValue('测试值')
  })

  // 测试验证
  test('应该在失去焦点时进行验证', async () => {
    render(
      <FormInput
        id="test-input"
        label="测试标签"
        validators={[validateRequired]}
      />
    )
    
    const input = screen.getByLabelText('测试标签')
    fireEvent.focus(input)
    fireEvent.blur(input)
    
    await waitFor(() => {
      expect(screen.getByText('此字段不能为空')).toBeInTheDocument()
    })
  })

  // 测试多个验证器
  test('应该支持多个验证器', async () => {
    render(
      <FormInput
        id="email-input"
        label="邮箱"
        validators={[
          (value) => validateRequired(value, '邮箱'),
          validateEmail
        ]}
      />
    )
    
    const input = screen.getByLabelText('邮箱')
    
    // 测试空值
    fireEvent.focus(input)
    fireEvent.blur(input)
    await waitFor(() => {
      expect(screen.getByText('邮箱不能为空')).toBeInTheDocument()
    })
    
    // 测试无效邮箱
    fireEvent.change(input, { target: { value: 'invalid-email' } })
    fireEvent.blur(input)
    await waitFor(() => {
      expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument()
    })
    
    // 测试有效邮箱
    fireEvent.change(input, { target: { value: '<EMAIL>' } })
    fireEvent.blur(input)
    await waitFor(() => {
      expect(screen.queryByText('请输入有效的邮箱地址')).not.toBeInTheDocument()
    })
  })

  // 测试密码类型
  test('应该支持密码类型和可见性切换', () => {
    render(
      <FormInput
        id="password-input"
        label="密码"
        type="password"
      />
    )
    
    const input = screen.getByLabelText('密码')
    expect(input).toHaveAttribute('type', 'password')
    
    // 点击切换按钮
    const toggleButton = screen.getByRole('button')
    fireEvent.click(toggleButton)
    
    expect(input).toHaveAttribute('type', 'text')
    
    // 再次点击切换回密码
    fireEvent.click(toggleButton)
    expect(input).toHaveAttribute('type', 'password')
  })

  // 测试成功状态
  test('应该在验证成功时显示成功指示器', async () => {
    render(
      <FormInput
        id="test-input"
        label="测试标签"
        validators={[validateRequired]}
        showSuccessIndicator={true}
      />
    )
    
    const input = screen.getByLabelText('测试标签')
    
    // 输入有效值
    fireEvent.change(input, { target: { value: '有效值' } })
    fireEvent.blur(input)
    
    // 检查成功指示器
    await waitFor(() => {
      const successIcon = document.querySelector('svg.text-green-500')
      expect(successIcon).toBeInTheDocument()
    })
  })

  // 测试禁用状态
  test('应该支持禁用状态', () => {
    render(
      <FormInput
        id="test-input"
        label="测试标签"
        disabled
      />
    )
    
    const input = screen.getByLabelText('测试标签')
    expect(input).toBeDisabled()
  })
})
