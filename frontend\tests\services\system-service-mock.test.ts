/**
 * 系统服务模拟测试
 */

import {
  getSystemConfig,
  updateSystemConfig,
  getSystemStats
} from '@/services/system-service';
import apiService from '@/services/api-service';

// 模拟API服务
jest.mock('@/services/api-service', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  del: jest.fn()
}));

describe('系统服务', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    jest.clearAllMocks();
  });

  // 测试获取系统配置
  describe('getSystemConfig', () => {
    test('应该返回系统配置', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        data: {
          id: 1,
          system_name: '和富家族研究平台',
          system_logo: '/logo.png',
          system_description: '和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。',
          password_min_length: 8,
          password_require_uppercase: true,
          password_require_lowercase: true,
          password_require_number: true,
          password_require_special: true,
          max_login_attempts: 5,
          lockout_duration: 30,
          session_timeout: 60,
          file_upload_max_size: 10,
          allowed_file_types: 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      };

      // 设置模拟返回值
      (apiService.get as jest.Mock).mockResolvedValue(mockResponse);

      // 调用获取系统配置函数
      const result = await getSystemConfig();

      // 验证API服务被调用
      expect(apiService.get).toHaveBeenCalledWith('/system/config');

      // 验证结果
      expect(result).toEqual(mockResponse.data);
      expect(result.system_name).toBe('和富家族研究平台');
      expect(result.password_min_length).toBe(8);
      expect(result.allowed_file_types).toBe('pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif');
    });

    test('获取系统配置失败应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '权限不足'
      };

      // 设置模拟返回值
      (apiService.get as jest.Mock).mockResolvedValue(mockResponse);

      // 调用获取系统配置函数并捕获错误
      await expect(getSystemConfig()).rejects.toThrow('权限不足');

      // 验证API服务被调用
      expect(apiService.get).toHaveBeenCalledWith('/system/config');
    });
  });

  // 测试更新系统配置
  describe('updateSystemConfig', () => {
    test('应该成功更新系统配置', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        message: '系统配置更新成功',
        data: {
          id: 1,
          system_name: '更新后的系统名称',
          system_description: '更新后的系统描述',
          password_min_length: 10,
          password_require_uppercase: true,
          password_require_lowercase: true,
          password_require_number: true,
          password_require_special: true,
          max_login_attempts: 5,
          lockout_duration: 30,
          session_timeout: 60,
          file_upload_max_size: 10,
          allowed_file_types: 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif',
          updated_at: '2023-01-02T00:00:00Z'
        }
      };

      // 设置模拟返回值
      (apiService.put as jest.Mock).mockResolvedValue(mockResponse);

      // 调用更新系统配置函数
      const result = await updateSystemConfig({
        system_name: '更新后的系统名称',
        system_description: '更新后的系统描述',
        password_min_length: 10
      });

      // 验证API服务被调用
      expect(apiService.put).toHaveBeenCalledWith('/system/config', {
        system_name: '更新后的系统名称',
        system_description: '更新后的系统描述',
        password_min_length: 10
      });

      // 验证结果
      expect(result).toEqual(mockResponse.data);
      expect(result.system_name).toBe('更新后的系统名称');
      expect(result.system_description).toBe('更新后的系统描述');
      expect(result.password_min_length).toBe(10);
    });

    test('更新系统配置失败应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '权限不足'
      };

      // 设置模拟返回值
      (apiService.put as jest.Mock).mockResolvedValue(mockResponse);

      // 调用更新系统配置函数并捕获错误
      await expect(updateSystemConfig({
        system_name: '未授权的更新',
        system_description: '这不应该成功'
      })).rejects.toThrow('权限不足');

      // 验证API服务被调用
      expect(apiService.put).toHaveBeenCalledWith('/system/config', {
        system_name: '未授权的更新',
        system_description: '这不应该成功'
      });
    });
  });

  // 测试获取系统统计信息
  describe('getSystemStats', () => {
    test('应该返回系统统计信息', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        data: {
          user_count: 50,
          active_user_count: 45,
          knowledge_base_count: 10,
          system_knowledge_base_count: 3,
          user_knowledge_base_count: 7,
          file_count: 100,
          approved_file_count: 80,
          pending_file_count: 20,
          activity_count: 15,
          published_activity_count: 12,
          draft_activity_count: 3,
          comment_count: 200,
          approved_comment_count: 180,
          pending_comment_count: 20,
          storage_usage: 1073741824, // 1GB
          last_updated: '2023-01-03T00:00:00Z'
        }
      };

      // 设置模拟返回值
      (apiService.get as jest.Mock).mockResolvedValue(mockResponse);

      // 调用获取系统统计信息函数
      const result = await getSystemStats();

      // 验证API服务被调用
      expect(apiService.get).toHaveBeenCalledWith('/system/stats');

      // 验证结果
      expect(result).toEqual(mockResponse.data);
      expect(result.user_count).toBe(50);
      expect(result.knowledge_base_count).toBe(10);
      expect(result.file_count).toBe(100);
      expect(result.activity_count).toBe(15);
      expect(result.comment_count).toBe(200);
      expect(result.storage_usage).toBe(1073741824);
    });

    test('获取系统统计信息失败应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '权限不足'
      };

      // 设置模拟返回值
      (apiService.get as jest.Mock).mockResolvedValue(mockResponse);

      // 调用获取系统统计信息函数并捕获错误
      await expect(getSystemStats()).rejects.toThrow('权限不足');

      // 验证API服务被调用
      expect(apiService.get).toHaveBeenCalledWith('/system/stats');
    });
  });
});
