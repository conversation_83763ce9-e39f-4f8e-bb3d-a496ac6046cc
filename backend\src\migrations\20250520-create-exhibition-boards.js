'use strict';

/**
 * 创建展板图片表的迁移文件
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('exhibition_boards', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '展板ID'
      },
      type: {
        type: Sequelize.ENUM('home', 'family', 'personal'),
        allowNull: false,
        comment: '展板类型：home-首页, family-家族专题, personal-个人专题'
      },
      sub_type: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '子类型，如个人专题中的具体人物ID'
      },
      image_url: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: '图片URL'
      },
      title: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: '标题'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '描述'
      },
      button_text: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '按钮文本'
      },
      button_link: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '按钮链接'
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '排序顺序'
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive'),
        allowNull: false,
        defaultValue: 'active',
        comment: '状态：active-启用, inactive-禁用'
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '创建者ID',
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      updated_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '更新者ID',
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: '创建时间'
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: '更新时间'
      }
    });

    // 添加索引
    await queryInterface.addIndex('exhibition_boards', ['type']);
    await queryInterface.addIndex('exhibition_boards', ['sub_type']);
    await queryInterface.addIndex('exhibition_boards', ['status']);
    await queryInterface.addIndex('exhibition_boards', ['order']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('exhibition_boards');
  }
};
