/**
 * 创建知识库访问权限表迁移文件
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('knowledge_base_access', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      knowledge_base_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'knowledge_bases',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      access_type: {
        type: Sequelize.ENUM('read', 'write', 'admin'),
        allowNull: false,
        defaultValue: 'read',
        comment: '访问权限类型：read-只读, write-读写, admin-管理'
      },
      granted_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加唯一索引，确保一个用户对一个知识库只有一条访问记录
    await queryInterface.addIndex('knowledge_base_access', ['knowledge_base_id', 'user_id'], {
      unique: true,
      name: 'knowledge_base_access_kb_id_user_id_unique'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('knowledge_base_access');
  }
};
