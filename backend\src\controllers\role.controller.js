/**
 * 角色控制器
 * 
 * 处理角色相关的业务逻辑，如创建、查询、更新、删除角色等
 */

const { Role, Permission, User } = require('../models');
const { Op } = require('sequelize');

/**
 * 获取角色列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRoles = async (req, res) => {
  try {
    const { search } = req.query;
    
    // 构建查询条件
    const whereConditions = {};
    
    // 根据名称或描述搜索
    if (search) {
      whereConditions[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    // 查询角色
    const roles = await Role.findAll({
      where: whereConditions,
      include: [
        {
          model: Permission,
          as: 'permissions',
          attributes: ['id', 'name', 'code'],
          through: { attributes: [] }
        }
      ],
      order: [['name', 'ASC']]
    });

    // 获取每个角色的用户数量
    const rolesWithUserCount = await Promise.all(roles.map(async (role) => {
      const userCount = await User.count({
        where: { role_id: role.id }
      });
      
      const roleData = role.toJSON();
      roleData.user_count = userCount;
      
      return roleData;
    }));

    res.status(200).json({
      success: true,
      data: rolesWithUserCount
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取角色列表失败',
      error: error.message
    });
  }
};

/**
 * 获取角色详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRoleById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询角色
    const role = await Role.findByPk(id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          attributes: ['id', 'name', 'code', 'module'],
          through: { attributes: [] }
        }
      ]
    });
    
    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }

    // 获取角色的用户数量
    const userCount = await User.count({
      where: { role_id: id }
    });
    
    const roleData = role.toJSON();
    roleData.user_count = userCount;

    res.status(200).json({
      success: true,
      data: roleData
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取角色详情失败',
      error: error.message
    });
  }
};

/**
 * 创建角色
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createRole = async (req, res) => {
  try {
    const { name, description, permissions } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: '角色名称不能为空'
      });
    }

    // 检查角色名称是否已存在
    const existingRole = await Role.findOne({
      where: { name }
    });
    
    if (existingRole) {
      return res.status(400).json({
        success: false,
        message: '角色名称已存在'
      });
    }

    // 创建角色
    const role = await Role.create({
      name,
      description,
      is_system: false
    });

    // 如果提供了权限，添加权限关联
    if (permissions && permissions.length > 0) {
      // 验证权限是否存在
      const existingPermissions = await Permission.findAll({
        where: {
          id: {
            [Op.in]: permissions
          }
        }
      });
      
      if (existingPermissions.length !== permissions.length) {
        return res.status(400).json({
          success: false,
          message: '部分权限不存在'
        });
      }
      
      // 添加权限关联
      await role.setPermissions(existingPermissions);
    }

    // 获取完整的角色信息
    const createdRole = await Role.findByPk(role.id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          attributes: ['id', 'name', 'code', 'module'],
          through: { attributes: [] }
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: '角色创建成功',
      data: createdRole
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建角色失败',
      error: error.message
    });
  }
};

/**
 * 更新角色
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    
    // 查询角色
    const role = await Role.findByPk(id);
    
    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }

    // 系统预设角色不能修改名称
    if (role.is_system && name && name !== role.name) {
      return res.status(400).json({
        success: false,
        message: '系统预设角色不能修改名称'
      });
    }

    // 如果修改名称，检查名称是否已存在
    if (name && name !== role.name) {
      const existingRole = await Role.findOne({
        where: { name }
      });
      
      if (existingRole) {
        return res.status(400).json({
          success: false,
          message: '角色名称已存在'
        });
      }
    }

    // 更新角色
    await role.update({
      name: name || role.name,
      description: description !== undefined ? description : role.description
    });

    // 获取完整的角色信息
    const updatedRole = await Role.findByPk(id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          attributes: ['id', 'name', 'code', 'module'],
          through: { attributes: [] }
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: '角色更新成功',
      data: updatedRole
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新角色失败',
      error: error.message
    });
  }
};

/**
 * 删除角色
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteRole = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询角色
    const role = await Role.findByPk(id);
    
    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }

    // 系统预设角色不能删除
    if (role.is_system) {
      return res.status(400).json({
        success: false,
        message: '系统预设角色不能删除'
      });
    }

    // 检查是否有用户使用该角色
    const userCount = await User.count({
      where: { role_id: id }
    });
    
    if (userCount > 0) {
      return res.status(400).json({
        success: false,
        message: `该角色正在被 ${userCount} 个用户使用，无法删除`
      });
    }

    // 删除角色
    await role.destroy();

    res.status(200).json({
      success: true,
      message: '角色删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除角色失败',
      error: error.message
    });
  }
};

/**
 * 获取角色权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRolePermissions = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询角色
    const role = await Role.findByPk(id);
    
    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }

    // 获取角色权限
    const permissions = await role.getPermissions({
      attributes: ['id', 'name', 'code', 'module', 'description']
    });

    res.status(200).json({
      success: true,
      data: permissions
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取角色权限失败',
      error: error.message
    });
  }
};

/**
 * 更新角色权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateRolePermissions = async (req, res) => {
  try {
    const { id } = req.params;
    const { permissions } = req.body;
    
    if (!permissions || !Array.isArray(permissions)) {
      return res.status(400).json({
        success: false,
        message: '权限列表不能为空且必须是数组'
      });
    }

    // 查询角色
    const role = await Role.findByPk(id);
    
    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }

    // 验证权限是否存在
    const existingPermissions = await Permission.findAll({
      where: {
        id: {
          [Op.in]: permissions
        }
      }
    });
    
    if (existingPermissions.length !== permissions.length) {
      return res.status(400).json({
        success: false,
        message: '部分权限不存在'
      });
    }

    // 更新角色权限
    await role.setPermissions(existingPermissions);

    // 获取更新后的角色权限
    const updatedPermissions = await role.getPermissions({
      attributes: ['id', 'name', 'code', 'module', 'description']
    });

    res.status(200).json({
      success: true,
      message: '角色权限更新成功',
      data: updatedPermissions
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新角色权限失败',
      error: error.message
    });
  }
};
