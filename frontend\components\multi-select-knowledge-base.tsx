"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Check, ChevronDown, X, Database, Search } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface KnowledgeBase {
  id: string
  name: string
  type: "系统" | "用户"
  description?: string
  hasAccess?: boolean
}

interface MultiSelectKnowledgeBaseProps {
  knowledgeBases: KnowledgeBase[]
  selectedIds: string[]
  onChange: (selectedIds: string[]) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function MultiSelectKnowledgeBase({
  knowledgeBases,
  selectedIds,
  onChange,
  placeholder = "选择知识库",
  className,
  disabled = false,
}: MultiSelectKnowledgeBaseProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const containerRef = useRef<HTMLDivElement>(null)

  // Filter knowledge bases based on search query
  const filteredKnowledgeBases = knowledgeBases.filter(
    (kb) =>
      kb.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      kb.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (kb.description && kb.description.toLowerCase().includes(searchQuery.toLowerCase())),
  )

  // Group knowledge bases by type
  const systemKnowledgeBases = filteredKnowledgeBases.filter((kb) => kb.type === "系统")
  const userKnowledgeBases = filteredKnowledgeBases.filter((kb) => kb.type === "用户")

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Toggle selection of a knowledge base
  const toggleSelection = (id: string) => {
    // 如果是取消选择，直接处理
    if (selectedIds.includes(id)) {
      onChange(selectedIds.filter((selectedId) => selectedId !== id))
      return
    }

    // 如果是选择，需要检查权限
    const kb = knowledgeBases.find(kb => kb.id === id)

    // 如果是用户知识库，且没有访问权限，显示提示
    if (kb && kb.type === "用户" && kb.hasAccess === false) {
      // 导入toast组件
      import("@/components/ui/use-toast").then(({ toast }) => {
        toast({
          title: "无访问权限",
          description: `您没有访问"${kb.name}"知识库的权限，请联系管理员申请权限。`,
          variant: "destructive",
        })
      })
      return
    }

    // 通过权限检查或是系统知识库，允许选择
    onChange([...selectedIds, id])
  }

  // Remove a selected knowledge base
  const removeSelection = (id: string, e: React.MouseEvent) => {
    e.stopPropagation()
    onChange(selectedIds.filter((selectedId) => selectedId !== id))
  }

  // Clear all selections
  const clearSelections = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange([])
  }

  // Get selected knowledge bases objects
  const selectedKnowledgeBases = knowledgeBases.filter((kb) => selectedIds.includes(kb.id))

  return (
    <div className={cn("relative", className)} ref={containerRef}>
      {/* Dropdown trigger */}
      <div
        className={cn(
          "flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
          isOpen && "ring-2 ring-ring ring-offset-2",
          disabled && "cursor-not-allowed opacity-50",
          "cursor-pointer",
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className="flex flex-wrap gap-1 items-center">
          {selectedKnowledgeBases.length > 0 ? (
            <>
              {selectedKnowledgeBases.map((kb) => (
                <Badge
                  key={kb.id}
                  variant="secondary"
                  className={cn(
                    "flex items-center gap-1 py-1 pl-2 pr-1",
                    kb.type === "系统" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800",
                  )}
                >
                  <span className="max-w-[150px] truncate">{kb.name}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-transparent"
                    onClick={(e) => removeSelection(kb.id, e)}
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">移除 {kb.name}</span>
                  </Button>
                </Badge>
              ))}
              {selectedKnowledgeBases.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-5 w-5 p-0 rounded-full hover:bg-gray-200"
                  onClick={clearSelections}
                >
                  <X className="h-3 w-3" />
                  <span className="sr-only">清除所有选择</span>
                </Button>
              )}
            </>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
        </div>
        <ChevronDown className={cn("h-4 w-4 opacity-50 transition", isOpen && "rotate-180")} />
      </div>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute z-50 mt-1 w-full rounded-md border border-gray-200 bg-white shadow-lg">
          {/* Search input */}
          <div className="p-2 border-b border-gray-100">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
              <input
                type="text"
                placeholder="搜索知识库..."
                className="w-full rounded-md border border-gray-200 py-1.5 pl-8 pr-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>

          <div className="max-h-60 overflow-y-auto p-1">
            {/* System knowledge bases */}
            {systemKnowledgeBases.length > 0 && (
              <div className="mb-2">
                <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 bg-gray-50 rounded-sm">系统知识库</div>
                {systemKnowledgeBases.map((kb) => (
                  <div
                    key={kb.id}
                    className={cn(
                      "flex items-center px-2 py-1.5 text-sm rounded-sm cursor-pointer",
                      selectedIds.includes(kb.id) ? "bg-emerald-50" : "hover:bg-gray-50",
                    )}
                    onClick={() => toggleSelection(kb.id, kb)}
                  >
                    <div
                      className={cn(
                        "mr-2 h-4 w-4 rounded-sm border flex items-center justify-center",
                        selectedIds.includes(kb.id)
                          ? "border-emerald-500 bg-emerald-500 text-white"
                          : "border-gray-300",
                      )}
                    >
                      {selectedIds.includes(kb.id) && <Check className="h-3 w-3" />}
                    </div>
                    <div className="flex items-center">
                      <Database className="h-4 w-4 mr-2 text-blue-500" />
                      <span className="font-medium">{kb.name}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* User knowledge bases */}
            {userKnowledgeBases.length > 0 && (
              <div>
                <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 bg-gray-50 rounded-sm">用户知识库</div>
                {userKnowledgeBases.map((kb) => (
                  <div
                    key={kb.id}
                    className={cn(
                      "flex items-center px-2 py-1.5 text-sm rounded-sm cursor-pointer",
                      selectedIds.includes(kb.id) ? "bg-emerald-50" : "hover:bg-gray-50",
                    )}
                    onClick={() => toggleSelection(kb.id)}
                  >
                    <div
                      className={cn(
                        "mr-2 h-4 w-4 rounded-sm border flex items-center justify-center",
                        selectedIds.includes(kb.id)
                          ? "border-emerald-500 bg-emerald-500 text-white"
                          : "border-gray-300",
                      )}
                    >
                      {selectedIds.includes(kb.id) && <Check className="h-3 w-3" />}
                    </div>
                    <div className="flex items-center">
                      <Database className="h-4 w-4 mr-2 text-green-500" />
                      <span className="font-medium">{kb.name}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {filteredKnowledgeBases.length === 0 && (
              <div className="px-2 py-4 text-center text-sm text-gray-500">未找到匹配的知识库</div>
            )}
          </div>

          {/* Actions */}
          <div className="border-t border-gray-100 p-2 flex justify-between">
            <Button
              variant="outline"
              size="sm"
              className="text-xs"
              onClick={(e) => {
                e.stopPropagation()
                onChange([])
              }}
            >
              清除
            </Button>
            <Button
              size="sm"
              className="text-xs bg-emerald-600 hover:bg-emerald-700 text-white"
              onClick={(e) => {
                e.stopPropagation()
                setIsOpen(false)
              }}
            >
              确定 ({selectedIds.length})
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
