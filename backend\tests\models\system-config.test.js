/**
 * 系统配置模型测试
 * 
 * 测试系统配置模型的字段和验证
 */

const { SystemConfig } = require('../../src/models');

describe('系统配置模型', () => {
  let testSystemConfig;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 获取或创建系统配置
    testSystemConfig = await SystemConfig.findOne({ where: { id: 1 } });
    if (!testSystemConfig) {
      testSystemConfig = await SystemConfig.create({
        id: 1,
        system_name: '测试系统名称',
        system_logo: '/test-logo.png',
        system_description: '这是一个测试系统描述',
        password_min_length: 8,
        password_require_uppercase: true,
        password_require_lowercase: true,
        password_require_number: true,
        password_require_special: true,
        max_login_attempts: 5,
        lockout_duration: 30,
        session_timeout: 60,
        file_upload_max_size: 10,
        allowed_file_types: 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif'
      });
    }
  });

  // 在所有测试后恢复原始数据
  afterAll(async () => {
    // 如果是新创建的配置，则删除
    if (testSystemConfig && testSystemConfig.system_name === '测试系统名称') {
      await testSystemConfig.destroy();
    }
  });

  // 测试系统配置获取
  describe('系统配置获取', () => {
    test('应该能获取系统配置', async () => {
      const systemConfig = await SystemConfig.findOne({ where: { id: 1 } });
      
      expect(systemConfig).toBeDefined();
      expect(systemConfig.id).toBe(1);
      expect(systemConfig.system_name).toBeDefined();
      expect(systemConfig.password_min_length).toBeDefined();
      expect(systemConfig.allowed_file_types).toBeDefined();
    });
  });

  // 测试系统配置更新
  describe('系统配置更新', () => {
    test('应该能更新系统配置', async () => {
      // 保存原始值
      const originalName = testSystemConfig.system_name;
      const originalLogo = testSystemConfig.system_logo;
      
      // 更新系统配置
      await testSystemConfig.update({
        system_name: '更新的系统名称',
        system_logo: '/updated-logo.png'
      });
      
      // 重新获取系统配置
      const updatedConfig = await SystemConfig.findOne({ where: { id: 1 } });
      
      expect(updatedConfig.system_name).toBe('更新的系统名称');
      expect(updatedConfig.system_logo).toBe('/updated-logo.png');
      
      // 恢复原始值
      await testSystemConfig.update({
        system_name: originalName,
        system_logo: originalLogo
      });
    });
    
    test('应该验证密码长度设置', async () => {
      // 保存原始值
      const originalLength = testSystemConfig.password_min_length;
      
      // 尝试设置无效的密码长度
      try {
        await testSystemConfig.update({
          password_min_length: 0
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
      
      // 恢复原始值
      await testSystemConfig.update({
        password_min_length: originalLength
      });
    });
  });

  // 测试系统配置类方法
  describe('系统配置类方法', () => {
    test('getInstance方法应该返回系统配置实例', async () => {
      // 假设系统配置模型有getInstance静态方法
      if (typeof SystemConfig.getInstance === 'function') {
        const instance = await SystemConfig.getInstance();
        
        expect(instance).toBeDefined();
        expect(instance.id).toBe(1);
        expect(instance.system_name).toBeDefined();
      } else {
        // 如果没有该方法，跳过测试
        console.log('系统配置模型没有getInstance方法，跳过测试');
      }
    });

    test('getDefaultConfig方法应该返回默认配置', async () => {
      // 假设系统配置模型有getDefaultConfig静态方法
      if (typeof SystemConfig.getDefaultConfig === 'function') {
        const defaultConfig = SystemConfig.getDefaultConfig();
        
        expect(defaultConfig).toBeDefined();
        expect(defaultConfig.system_name).toBeDefined();
        expect(defaultConfig.password_min_length).toBeDefined();
        expect(defaultConfig.allowed_file_types).toBeDefined();
      } else {
        // 如果没有该方法，跳过测试
        console.log('系统配置模型没有getDefaultConfig方法，跳过测试');
      }
    });
  });
});
