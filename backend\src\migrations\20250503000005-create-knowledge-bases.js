/**
 * 创建知识库表迁移文件
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('knowledge_bases', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      type: {
        type: Sequelize.ENUM('system', 'user'),
        allowNull: false,
        defaultValue: 'user',
        comment: '知识库类型：系统知识库或用户知识库'
      },
      creator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      contact_name: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '联系人姓名'
      },
      contact_phone: {
        type: Sequelize.STRING(20),
        allowNull: true,
        comment: '联系人电话'
      },
      storage_size: {
        type: Sequelize.BIGINT,
        defaultValue: 0,
        comment: '知识库存储大小（字节）'
      },
      file_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: '知识库文件数量'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('knowledge_bases');
  }
};
