/**
 * 性能优化工具函数
 * 
 * 提供各种性能优化工具函数，用于提高应用性能
 */

/**
 * 防抖函数
 * 
 * 延迟执行函数，如果在延迟时间内再次调用，则重新计时
 * 
 * @param fn 要执行的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖处理后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null
  
  return function(this: any, ...args: Parameters<T>): void {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      fn.apply(this, args)
      timeoutId = null
    }, delay)
  }
}

/**
 * 节流函数
 * 
 * 限制函数的执行频率，每隔一段时间执行一次
 * 
 * @param fn 要执行的函数
 * @param limit 时间间隔（毫秒）
 * @returns 节流处理后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  fn: T,
  limit: number = 300
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return function(this: any, ...args: Parameters<T>): void {
    const now = Date.now()
    
    if (now - lastCall >= limit) {
      fn.apply(this, args)
      lastCall = now
    }
  }
}

/**
 * 缓存函数结果
 * 
 * 缓存函数的计算结果，避免重复计算
 * 
 * @param fn 要缓存的函数
 * @param maxSize 缓存最大大小
 * @returns 缓存处理后的函数
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  maxSize: number = 100
): (...args: Parameters<T>) => ReturnType<T> {
  const cache = new Map<string, ReturnType<T>>()
  
  return function(this: any, ...args: Parameters<T>): ReturnType<T> {
    const key = JSON.stringify(args)
    
    if (cache.has(key)) {
      return cache.get(key) as ReturnType<T>
    }
    
    const result = fn.apply(this, args)
    
    // 如果缓存已满，删除最早的条目
    if (cache.size >= maxSize) {
      const firstKey = cache.keys().next().value
      cache.delete(firstKey)
    }
    
    cache.set(key, result)
    
    return result
  }
}

/**
 * 延迟加载函数
 * 
 * 延迟执行函数，用于非关键操作
 * 
 * @param fn 要执行的函数
 * @param delay 延迟时间（毫秒）
 */
export function lazyLoad(fn: () => void, delay: number = 0): void {
  setTimeout(fn, delay)
}

/**
 * 批量处理函数
 * 
 * 将多个操作合并为一个批处理，减少重渲染
 * 
 * @param fn 批处理函数
 * @returns 批处理后的函数
 */
export function batchProcess<T extends (...args: any[]) => void>(
  fn: T
): (...args: Parameters<T>) => void {
  let isBatching = false
  let lastArgs: Parameters<T> | null = null
  
  return function(this: any, ...args: Parameters<T>): void {
    lastArgs = args
    
    if (isBatching) {
      return
    }
    
    isBatching = true
    
    // 使用requestAnimationFrame在下一帧执行
    requestAnimationFrame(() => {
      if (lastArgs) {
        fn.apply(this, lastArgs)
      }
      
      isBatching = false
      lastArgs = null
    })
  }
}

/**
 * 预加载图片
 * 
 * 预加载图片，提高用户体验
 * 
 * @param src 图片URL
 * @returns Promise，图片加载完成后解析
 */
export function preloadImage(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = src
  })
}

/**
 * 预加载多个图片
 * 
 * 预加载多个图片，提高用户体验
 * 
 * @param srcs 图片URL数组
 * @returns Promise，所有图片加载完成后解析
 */
export function preloadImages(srcs: string[]): Promise<HTMLImageElement[]> {
  return Promise.all(srcs.map(preloadImage))
}

/**
 * 触发路由变化开始事件
 */
export function triggerRouteChangeStart(): void {
  window.dispatchEvent(new Event('route-change-start'))
}

/**
 * 触发路由变化完成事件
 */
export function triggerRouteChangeComplete(): void {
  window.dispatchEvent(new Event('route-change-complete'))
}
