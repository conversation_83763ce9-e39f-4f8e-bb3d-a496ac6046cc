/**
 * 评论服务测试
 */

import {
  getCommentList,
  getTopicComments,
  createComment,
  reviewComment,
  deleteComment
} from '@/services/comment-service'
import apiService from '@/services/api-service'

// 模拟API服务
jest.mock('@/services/api-service')

describe('评论服务', () => {
  // 在每个测试前后重置模拟
  beforeEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  afterEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  // 测试获取评论列表
  describe('getCommentList', () => {
    test('应该返回评论列表', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        data: {
          comments: [
            {
              id: 1,
              content: '这是一条测试评论',
              topic_type: 'personal',
              topic_id: 1,
              creator: {
                id: 2,
                username: 'user1'
              },
              status: 'approved',
              created_at: '2023-01-01T00:00:00Z'
            },
            {
              id: 2,
              content: '这是另一条测试评论',
              topic_type: 'personal',
              topic_id: 1,
              creator: {
                id: 3,
                username: 'user2'
              },
              status: 'pending',
              created_at: '2023-01-02T00:00:00Z'
            }
          ],
          pagination: {
            total: 2,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        }
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取评论列表函数
      const result = await getCommentList({ status: 'all' })

      // 验证结果
      expect(result).toEqual(mockResponse.data)
      expect(result.comments).toHaveLength(2)
      expect(result.comments[0].content).toBe('这是一条测试评论')
      expect(result.comments[1].content).toBe('这是另一条测试评论')
    })

    test('获取评论列表失败应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '获取评论列表失败'
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取评论列表函数并捕获错误
      await expect(getCommentList({ status: 'all' })).rejects.toThrow('获取评论列表失败')
    })
  })

  // 测试获取主题评论
  describe('getTopicComments', () => {
    test('应该返回主题的评论列表', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        data: {
          comments: [
            {
              id: 1,
              content: '这是一条关于个人专题的评论',
              topic_type: 'personal',
              topic_id: 1,
              creator: {
                id: 2,
                username: 'user1'
              },
              status: 'approved',
              created_at: '2023-01-01T00:00:00Z'
            },
            {
              id: 3,
              content: '这是另一条关于同一个人专题的评论',
              topic_type: 'personal',
              topic_id: 1,
              creator: {
                id: 3,
                username: 'user2'
              },
              status: 'approved',
              created_at: '2023-01-03T00:00:00Z'
            }
          ],
          pagination: {
            total: 2,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        }
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取主题评论函数
      const result = await getTopicComments('personal', 1)

      // 验证结果
      expect(result).toEqual(mockResponse.data)
      expect(result.comments).toHaveLength(2)
      expect(result.comments[0].topic_type).toBe('personal')
      expect(result.comments[0].topic_id).toBe(1)
      expect(result.comments[1].topic_type).toBe('personal')
      expect(result.comments[1].topic_id).toBe(1)
    })

    test('获取主题评论失败应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '获取主题评论失败'
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取主题评论函数并捕获错误
      await expect(getTopicComments('personal', 999)).rejects.toThrow('获取主题评论失败')
    })
  })

  // 测试创建评论
  describe('createComment', () => {
    test('应该成功创建评论', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        message: '评论创建成功',
        data: {
          id: 4,
          content: '新评论',
          topic_type: 'personal',
          topic_id: 1,
          creator_id: 2,
          status: 'pending',
          created_at: '2023-01-04T00:00:00Z'
        }
      }

      // 模拟API服务的post方法
      apiService.post = jest.fn().mockResolvedValue(mockResponse)

      // 调用创建评论函数
      const result = await createComment({
        content: '新评论',
        topic_type: 'personal',
        topic_id: 1
      })

      // 验证结果
      expect(result).toEqual(mockResponse.data)
      expect(result.id).toBe(4)
      expect(result.content).toBe('新评论')
      expect(result.status).toBe('pending')
    })

    test('创建评论失败应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '内容不能为空'
      }

      // 模拟API服务的post方法
      apiService.post = jest.fn().mockResolvedValue(mockResponse)

      // 调用创建评论函数并捕获错误
      await expect(createComment({
        content: '',
        topic_type: 'personal',
        topic_id: 1
      })).rejects.toThrow('内容不能为空')
    })
  })

  // 测试审核评论
  describe('reviewComment', () => {
    test('应该成功审核评论', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        message: '评论审核成功',
        data: {
          id: 2,
          status: 'approved',
          review_comment: '评论已审核通过',
          reviewer_id: 1,
          updated_at: '2023-01-05T00:00:00Z'
        }
      }

      // 模拟API服务的put方法
      apiService.put = jest.fn().mockResolvedValue(mockResponse)

      // 调用审核评论函数
      const result = await reviewComment(2, 'approved', '评论已审核通过')

      // 验证结果
      expect(result).toEqual(mockResponse.data)
      expect(result.status).toBe('approved')
      expect(result.review_comment).toBe('评论已审核通过')
    })

    test('审核评论失败应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '权限不足'
      }

      // 模拟API服务的put方法
      apiService.put = jest.fn().mockResolvedValue(mockResponse)

      // 调用审核评论函数并捕获错误
      await expect(reviewComment(2, 'approved', '未授权的审核')).rejects.toThrow('权限不足')
    })
  })

  // 测试删除评论
  describe('deleteComment', () => {
    test('应该成功删除评论', async () => {
      // 模拟成功的响应
      const mockResponse = {
        success: true,
        message: '评论删除成功'
      }

      // 模拟API服务的del方法
      apiService.del = jest.fn().mockResolvedValue(mockResponse)

      // 调用删除评论函数
      const result = await deleteComment(1)

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.success).toBe(true)
      expect(result.message).toBe('评论删除成功')
    })

    test('删除评论失败应该抛出错误', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        message: '评论不存在'
      }

      // 模拟API服务的del方法
      apiService.del = jest.fn().mockResolvedValue(mockResponse)

      // 调用删除评论函数并捕获错误
      await expect(deleteComment(999)).rejects.toThrow('评论不存在')
    })
  })
});
