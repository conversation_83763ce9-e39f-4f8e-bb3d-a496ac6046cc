/**
 * 权限模型
 *
 * 定义系统权限数据结构，包括权限名称、代码和描述等信息
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} Permission模型
 */
module.exports = (sequelize, DataTypes) => {
  const Permission = sequelize.define('Permission', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true
      }
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true
      },
      comment: '权限代码，如content:view, content:edit等'
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    module: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '权限所属模块，如content, user, system等'
    }
  }, {
    tableName: 'permissions',
    timestamps: true
  });

  // 实例方法
  Permission.prototype.getCode = function() {
    return this.code;
  };

  // 静态方法
  Permission.findByModule = async function(module) {
    return await this.findAll({
      where: {
        module: module
      }
    });
  };

  // 关联关系
  Permission.associate = (models) => {
    // 权限与角色的多对多关系
    Permission.belongsToMany(models.Role, {
      through: 'role_permissions',
      foreignKey: 'permission_id',
      otherKey: 'role_id',
      as: 'roles'
    });
  };

  return Permission;
};
