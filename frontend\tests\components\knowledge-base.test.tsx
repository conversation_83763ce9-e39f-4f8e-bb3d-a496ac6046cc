/**
 * 知识库组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { KnowledgeBase } from '@/components/knowledge/KnowledgeBase'
import { mockFetch, resetMockFetch } from '../test-utils'

// 模拟知识库数据
const mockKnowledgeBase = {
  id: 1,
  name: '测试知识库',
  description: '这是一个测试用的知识库',
  type: 'system',
  creator: {
    id: 1,
    username: 'admin'
  },
  file_count: 5,
  created_at: '2023-01-01T00:00:00Z'
}

// 模拟文件列表数据
const mockFiles = [
  {
    id: 1,
    name: '测试文件1.pdf',
    original_name: '测试文件1.pdf',
    type: 'pdf',
    size: 1024,
    status: 'approved',
    created_at: '2023-01-02T00:00:00Z',
    uploader: {
      id: 1,
      username: 'admin'
    }
  },
  {
    id: 2,
    name: '测试文件2.docx',
    original_name: '测试文件2.docx',
    type: 'docx',
    size: 2048,
    status: 'approved',
    created_at: '2023-01-03T00:00:00Z',
    uploader: {
      id: 1,
      username: 'admin'
    }
  }
]

describe('KnowledgeBase组件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch()
    jest.clearAllMocks()
  })

  // 测试基本渲染
  test('应该正确渲染知识库组件', () => {
    render(
      <KnowledgeBase
        knowledgeBase={mockKnowledgeBase}
        files={mockFiles}
        isLoading={false}
        onFileUpload={jest.fn()}
        onFileDownload={jest.fn()}
        onFileDelete={jest.fn()}
      />
    )
    
    expect(screen.getByText('测试知识库')).toBeInTheDocument()
    expect(screen.getByText('这是一个测试用的知识库')).toBeInTheDocument()
    expect(screen.getByText('系统知识库')).toBeInTheDocument()
    expect(screen.getByText('文件数量: 5')).toBeInTheDocument()
    expect(screen.getByText('创建者: admin')).toBeInTheDocument()
    
    // 验证文件列表
    expect(screen.getByText('测试文件1.pdf')).toBeInTheDocument()
    expect(screen.getByText('测试文件2.docx')).toBeInTheDocument()
  })

  // 测试加载状态
  test('应该显示加载状态', () => {
    render(
      <KnowledgeBase
        knowledgeBase={mockKnowledgeBase}
        files={[]}
        isLoading={true}
        onFileUpload={jest.fn()}
        onFileDownload={jest.fn()}
        onFileDelete={jest.fn()}
      />
    )
    
    expect(screen.getByText('加载中...')).toBeInTheDocument()
  })

  // 测试文件上传
  test('应该调用文件上传函数', async () => {
    const handleFileUpload = jest.fn()
    
    render(
      <KnowledgeBase
        knowledgeBase={mockKnowledgeBase}
        files={mockFiles}
        isLoading={false}
        onFileUpload={handleFileUpload}
        onFileDownload={jest.fn()}
        onFileDelete={jest.fn()}
      />
    )
    
    // 点击上传按钮
    const uploadButton = screen.getByText('上传文件')
    fireEvent.click(uploadButton)
    
    // 模拟文件选择
    const file = new File(['file content'], 'test.pdf', { type: 'application/pdf' })
    const fileInput = screen.getByTestId('file-input')
    fireEvent.change(fileInput, { target: { files: [file] } })
    
    // 验证上传函数被调用
    await waitFor(() => {
      expect(handleFileUpload).toHaveBeenCalledWith(
        expect.any(File),
        mockKnowledgeBase.id
      )
    })
  })

  // 测试文件下载
  test('应该调用文件下载函数', () => {
    const handleFileDownload = jest.fn()
    
    render(
      <KnowledgeBase
        knowledgeBase={mockKnowledgeBase}
        files={mockFiles}
        isLoading={false}
        onFileUpload={jest.fn()}
        onFileDownload={handleFileDownload}
        onFileDelete={jest.fn()}
      />
    )
    
    // 点击下载按钮
    const downloadButtons = screen.getAllByText('下载')
    fireEvent.click(downloadButtons[0])
    
    // 验证下载函数被调用
    expect(handleFileDownload).toHaveBeenCalledWith(mockFiles[0].id)
  })

  // 测试文件删除
  test('应该调用文件删除函数', async () => {
    const handleFileDelete = jest.fn()
    
    render(
      <KnowledgeBase
        knowledgeBase={mockKnowledgeBase}
        files={mockFiles}
        isLoading={false}
        onFileUpload={jest.fn()}
        onFileDownload={jest.fn()}
        onFileDelete={handleFileDelete}
        isAdmin={true} // 启用删除功能
      />
    )
    
    // 点击删除按钮
    const deleteButtons = screen.getAllByText('删除')
    fireEvent.click(deleteButtons[0])
    
    // 确认删除
    const confirmButton = screen.getByText('确认')
    fireEvent.click(confirmButton)
    
    // 验证删除函数被调用
    await waitFor(() => {
      expect(handleFileDelete).toHaveBeenCalledWith(mockFiles[0].id)
    })
  })

  // 测试文件搜索
  test('应该根据关键词筛选文件', () => {
    render(
      <KnowledgeBase
        knowledgeBase={mockKnowledgeBase}
        files={mockFiles}
        isLoading={false}
        onFileUpload={jest.fn()}
        onFileDownload={jest.fn()}
        onFileDelete={jest.fn()}
      />
    )
    
    // 输入搜索关键词
    const searchInput = screen.getByPlaceholderText('搜索文件...')
    fireEvent.change(searchInput, { target: { value: '文件1' } })
    
    // 验证筛选结果
    expect(screen.getByText('测试文件1.pdf')).toBeInTheDocument()
    expect(screen.queryByText('测试文件2.docx')).not.toBeInTheDocument()
  })

  // 测试文件类型筛选
  test('应该根据文件类型筛选文件', () => {
    render(
      <KnowledgeBase
        knowledgeBase={mockKnowledgeBase}
        files={mockFiles}
        isLoading={false}
        onFileUpload={jest.fn()}
        onFileDownload={jest.fn()}
        onFileDelete={jest.fn()}
      />
    )
    
    // 选择文件类型
    const typeSelect = screen.getByLabelText('文件类型')
    fireEvent.change(typeSelect, { target: { value: 'pdf' } })
    
    // 验证筛选结果
    expect(screen.getByText('测试文件1.pdf')).toBeInTheDocument()
    expect(screen.queryByText('测试文件2.docx')).not.toBeInTheDocument()
  })

  // 测试空文件列表
  test('应该显示空文件列表提示', () => {
    render(
      <KnowledgeBase
        knowledgeBase={mockKnowledgeBase}
        files={[]}
        isLoading={false}
        onFileUpload={jest.fn()}
        onFileDownload={jest.fn()}
        onFileDelete={jest.fn()}
      />
    )
    
    expect(screen.getByText('暂无文件')).toBeInTheDocument()
  })
});
