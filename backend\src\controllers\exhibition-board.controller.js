/**
 * 展板图片控制器
 *
 * 处理展板图片的CRUD操作
 */
const { ExhibitionBoard, User } = require('../models');
const fs = require('fs');
const path = require('path');
const { Op } = require('sequelize');

/**
 * 获取展板图片列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getExhibitionBoards = async (req, res) => {
  try {
    console.log('获取展板图片列表请求:', req.query);

    const { type, sub_type, status } = req.query;

    // 构建查询条件
    const whereClause = {};

    if (type) {
      whereClause.type = type;
    }

    if (sub_type) {
      whereClause.sub_type = sub_type;
    }

    if (status) {
      whereClause.status = status;
    } else {
      // 默认只返回启用的展板
      whereClause.status = 'active';
    }

    console.log('查询条件:', whereClause);

    // 检查模型是否存在
    if (!ExhibitionBoard) {
      console.error('ExhibitionBoard 模型不存在');
      return res.status(500).json({
        success: false,
        message: 'ExhibitionBoard 模型不存在'
      });
    }

    // 查询展板图片
    const exhibitionBoards = await ExhibitionBoard.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username']
        }
      ],
      order: [
        ['order', 'ASC'],
        ['created_at', 'DESC']
      ]
    });

    console.log('查询结果:', exhibitionBoards);

    res.status(200).json({
      success: true,
      data: exhibitionBoards
    });
  } catch (error) {
    console.error('获取展板图片列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取展板图片列表失败',
      error: error.message
    });
  }
};

/**
 * 获取展板图片详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getExhibitionBoardById = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询展板图片
    const exhibitionBoard = await ExhibitionBoard.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username']
        }
      ]
    });

    if (!exhibitionBoard) {
      return res.status(404).json({
        success: false,
        message: '展板图片不存在'
      });
    }

    res.status(200).json({
      success: true,
      data: exhibitionBoard
    });
  } catch (error) {
    console.error('获取展板图片详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取展板图片详情失败',
      error: error.message
    });
  }
};

/**
 * 创建展板图片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createExhibitionBoard = async (req, res) => {
  try {
    const {
      type,
      sub_type,
      image_url,
      title,
      description,
      button_text,
      button_link,
      order,
      status
    } = req.body;

    // 验证必填字段
    if (!type || !image_url) {
      return res.status(400).json({
        success: false,
        message: '类型和图片URL为必填字段'
      });
    }

    // 创建展板图片
    const exhibitionBoard = await ExhibitionBoard.create({
      type,
      sub_type: sub_type || null,
      image_url,
      title: title || null,
      description: description || null,
      button_text: button_text || null,
      button_link: button_link || null,
      order: order || 0,
      status: status || 'active',
      created_by: req.user.id,
      updated_by: req.user.id
    });

    // 获取完整的展板图片信息
    const fullExhibitionBoard = await ExhibitionBoard.findByPk(exhibitionBoard.id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: '展板图片创建成功',
      data: fullExhibitionBoard
    });
  } catch (error) {
    console.error('创建展板图片失败:', error);
    res.status(500).json({
      success: false,
      message: '创建展板图片失败',
      error: error.message
    });
  }
};

/**
 * 更新展板图片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateExhibitionBoard = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      type,
      sub_type,
      image_url,
      title,
      description,
      button_text,
      button_link,
      order,
      status
    } = req.body;

    // 查询展板图片
    const exhibitionBoard = await ExhibitionBoard.findByPk(id);

    if (!exhibitionBoard) {
      return res.status(404).json({
        success: false,
        message: '展板图片不存在'
      });
    }

    // 更新展板图片
    await exhibitionBoard.update({
      type: type !== undefined ? type : exhibitionBoard.type,
      sub_type: sub_type !== undefined ? sub_type : exhibitionBoard.sub_type,
      image_url: image_url !== undefined ? image_url : exhibitionBoard.image_url,
      title: title !== undefined ? title : exhibitionBoard.title,
      description: description !== undefined ? description : exhibitionBoard.description,
      button_text: button_text !== undefined ? button_text : exhibitionBoard.button_text,
      button_link: button_link !== undefined ? button_link : exhibitionBoard.button_link,
      order: order !== undefined ? order : exhibitionBoard.order,
      status: status !== undefined ? status : exhibitionBoard.status,
      updated_by: req.user.id
    });

    // 获取更新后的展板图片信息
    const updatedExhibitionBoard = await ExhibitionBoard.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: '展板图片更新成功',
      data: updatedExhibitionBoard
    });
  } catch (error) {
    console.error('更新展板图片失败:', error);
    res.status(500).json({
      success: false,
      message: '更新展板图片失败',
      error: error.message
    });
  }
};

/**
 * 删除展板图片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteExhibitionBoard = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询展板图片
    const exhibitionBoard = await ExhibitionBoard.findByPk(id);

    if (!exhibitionBoard) {
      return res.status(404).json({
        success: false,
        message: '展板图片不存在'
      });
    }

    // 删除展板图片
    await exhibitionBoard.destroy();

    res.status(200).json({
      success: true,
      message: '展板图片删除成功'
    });
  } catch (error) {
    console.error('删除展板图片失败:', error);
    res.status(500).json({
      success: false,
      message: '删除展板图片失败',
      error: error.message
    });
  }
};

/**
 * 上传展板图片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.uploadExhibitionImage = async (req, res) => {
  try {
    console.log('开始处理展板图片上传请求');
    console.log('请求头:', req.headers);
    console.log('请求方法:', req.method);
    console.log('请求URL:', req.originalUrl);
    console.log('请求体:', req.body);

    // 检查用户认证信息
    if (!req.user) {
      console.error('上传失败: 用户未认证');
      console.error('Authorization头:', req.headers.authorization);
      return res.status(401).json({
        success: false,
        message: '未授权，请先登录'
      });
    }

    console.log('用户认证信息:', {
      userId: req.user.id,
      username: req.user.username,
      role: req.user.role,
      permissions: req.user.permissions
    });

    // 检查文件是否上传
    if (!req.file) {
      console.error('上传失败: 未找到上传的文件');
      console.error('请求中的文件:', req.files);
      console.error('请求中的表单数据:', req.body);
      return res.status(400).json({
        success: false,
        message: '未上传图片'
      });
    }

    console.log('文件信息:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: req.file.path,
      fieldname: req.file.fieldname
    });

    // 处理文件路径，确保使用相对路径
    const relativePath = req.file.path.replace(/\\/g, '/').replace(/^.*[\/\\]public[\/\\]/, '');

    // 构建URL
    const fileUrl = `${req.protocol}://${req.get('host')}/${relativePath}`;

    console.log('上传展板图片成功:', {
      originalPath: req.file.path,
      relativePath: relativePath,
      url: fileUrl
    });

    // 检查文件是否真的存在
    if (!fs.existsSync(req.file.path)) {
      console.error('文件上传失败: 文件不存在于磁盘上');
      return res.status(500).json({
        success: false,
        message: '文件上传失败: 文件不存在于磁盘上'
      });
    }

    // 检查文件大小
    const stats = fs.statSync(req.file.path);
    console.log('文件大小:', stats.size, 'bytes');

    // 检查文件权限
    try {
      fs.accessSync(req.file.path, fs.constants.R_OK | fs.constants.W_OK);
      console.log('文件权限正常，可读可写');
    } catch (err) {
      console.error('文件权限异常:', err);
    }

    res.status(200).json({
      success: true,
      message: '展板图片上传成功',
      data: {
        path: relativePath,
        url: fileUrl
      }
    });
  } catch (error) {
    console.error('上传展板图片失败:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({
      success: false,
      message: '上传展板图片失败',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * 更新展板图片顺序
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateExhibitionBoardOrder = async (req, res) => {
  try {
    const { orders } = req.body;

    if (!orders || !Array.isArray(orders)) {
      return res.status(400).json({
        success: false,
        message: '无效的顺序数据'
      });
    }

    // 批量更新顺序
    for (const item of orders) {
      if (!item.id || item.order === undefined) continue;

      await ExhibitionBoard.update(
        { order: item.order, updated_by: req.user.id },
        { where: { id: item.id } }
      );
    }

    res.status(200).json({
      success: true,
      message: '展板图片顺序更新成功'
    });
  } catch (error) {
    console.error('更新展板图片顺序失败:', error);
    res.status(500).json({
      success: false,
      message: '更新展板图片顺序失败',
      error: error.message
    });
  }
};
