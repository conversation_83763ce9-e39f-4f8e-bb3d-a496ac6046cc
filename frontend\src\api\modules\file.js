/**
 * 文件相关API
 *
 * 处理文件的上传、下载、查询、审核等请求
 */

import request from '../index';

/**
 * 获取文件列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.search - 搜索关键词
 * @param {string} params.status - 文件状态
 * @param {string} params.type - 文件类型
 * @returns {Promise} 文件列表
 */
export function getFileList(params) {
  return request({
    url: '/files',
    method: 'get',
    params
  });
}

/**
 * 获取知识库文件列表
 * @param {string} knowledgeBaseId - 知识库ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.search - 搜索关键词
 * @param {string} params.status - 文件状态
 * @param {string} params.type - 文件类型
 * @returns {Promise} 文件列表
 */
export function getKnowledgeBaseFileList(knowledgeBaseId, params) {
  return request({
    url: `/files/knowledge-base/${knowledgeBaseId}`,
    method: 'get',
    params
  });
}

/**
 * 获取文件详情
 * @param {string} id - 文件ID
 * @returns {Promise} 文件详情
 */
export function getFileById(id) {
  return request({
    url: `/files/${id}`,
    method: 'get'
  });
}

/**
 * 上传文件
 * @param {string} knowledgeBaseId - 知识库ID
 * @param {FormData} formData - 文件表单数据
 * @param {Function} onUploadProgress - 上传进度回调
 * @returns {Promise} 上传结果
 */
export function uploadFile(knowledgeBaseId, formData, onUploadProgress) {
  return request({
    url: `/files/upload/${knowledgeBaseId}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress
  });
}

/**
 * 下载文件
 * @param {string} id - 文件ID
 * @returns {Promise} 下载结果
 */
export function downloadFile(id) {
  return request({
    url: `/files/${id}/download`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 审核文件
 * @param {string} id - 文件ID
 * @param {Object} data - 审核信息
 * @param {string} data.status - 审核状态
 * @param {string} data.reject_reason - 拒绝原因
 * @returns {Promise} 审核结果
 */
export function reviewFile(id, data) {
  return request({
    url: `/files/${id}/review`,
    method: 'put',
    data
  });
}

/**
 * 分析文件
 * @param {string} id - 文件ID
 * @returns {Promise} 分析结果
 */
export function analyzeFile(id) {
  return request({
    url: `/files/${id}/analyze`,
    method: 'post'
  });
}

/**
 * 删除文件
 * @param {string} id - 文件ID
 * @returns {Promise} 删除结果
 */
export function deleteFile(id) {
  return request({
    url: `/files/${id}`,
    method: 'delete'
  });
}

export default {
  getFileList,
  getKnowledgeBaseFileList,
  getFileById,
  uploadFile,
  downloadFile,
  reviewFile,
  analyzeFile,
  deleteFile
};
