"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Search } from "lucide-react"

// 模拟登录历史数据
const loginHistoryData = [
  {
    id: "1",
    time: "2024-04-12 10:30:15",
    ip: "*************",
    location: "北京市",
    device: "Chrome 122.0.6261.112 / Windows 10",
    status: "成功",
  },
  {
    id: "2",
    time: "2024-04-11 16:45:22",
    ip: "*************",
    location: "北京市",
    device: "Chrome 122.0.6261.112 / Windows 10",
    status: "成功",
  },
  {
    id: "3",
    time: "2024-04-10 09:12:05",
    ip: "*************",
    location: "北京市",
    device: "Chrome 122.0.6261.112 / Windows 10",
    status: "成功",
  },
  {
    id: "4",
    time: "2024-04-09 14:30:45",
    ip: "*************",
    location: "北京市",
    device: "Chrome 122.0.6261.112 / Windows 10",
    status: "成功",
  },
  {
    id: "5",
    time: "2024-04-08 11:05:33",
    ip: "*************",
    location: "北京市",
    device: "Chrome 122.0.6261.112 / Windows 10",
    status: "成功",
  },
  {
    id: "6",
    time: "2024-04-07 17:22:18",
    ip: "*************",
    location: "北京市",
    device: "Safari 17.3.1 / macOS",
    status: "失败",
  },
  {
    id: "7",
    time: "2024-04-07 17:20:05",
    ip: "*************",
    location: "北京市",
    device: "Safari 17.3.1 / macOS",
    status: "失败",
  },
]

export function LoginHistory() {
  const [searchQuery, setSearchQuery] = useState("")

  // 过滤登录历史
  const filteredHistory = loginHistoryData.filter((item) => {
    return (
      item.time.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.ip.includes(searchQuery) ||
      item.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.device.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.status.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-bold">登录历史</h2>
      </div>

      <div className="mb-6 flex flex-col md:flex-row gap-4">
        <div className="relative md:w-64">
          <input
            type="text"
            placeholder="搜索登录记录"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                登录时间
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                IP地址
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                登录地点
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                设备信息
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                状态
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredHistory.map((item) => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.time}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.ip}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.location}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.device}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      item.status === "成功" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                    }`}
                  >
                    {item.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredHistory.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">没有找到匹配的登录记录</p>
        </div>
      )}

      <div className="mt-6 flex justify-between items-center">
        <div className="text-sm text-gray-700">
          共 <span className="font-medium">{filteredHistory.length}</span> 条记录
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" className="flex items-center">
            <ChevronLeft className="h-4 w-4 mr-1" />
            上一页
          </Button>
          <Button className="bg-[#1e7a43] hover:bg-[#1e7a43]/90 h-8 w-8 p-0">1</Button>
          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
            2
          </Button>
          <Button variant="outline" size="sm" className="flex items-center">
            下一页
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>
    </div>
  )
}
