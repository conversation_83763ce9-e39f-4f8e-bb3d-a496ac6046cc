"use client"

import { FileDown, ExternalLink } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface PDFFallbackProps {
  pdfUrl: string
  materialTitle: string
}

export default function PDFFallback({ pdfUrl, materialTitle }: PDFFallbackProps) {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-gray-50">
      <div className="text-amber-500 mb-4">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <line x1="10" y1="9" x2="8" y2="9"></line>
        </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{materialTitle}</h3>
      <p className="text-gray-600 mb-4">PDF文档可通过以下方式查看</p>
      <div className="flex gap-3">
        <Button onClick={() => window.open(pdfUrl, "_blank")} className="flex items-center gap-2">
          <ExternalLink className="h-4 w-4" />
          在新窗口打开
        </Button>
        <Button
          variant="outline"
          onClick={() => {
            const link = document.createElement("a")
            link.href = pdfUrl
            link.download = materialTitle || "document.pdf"
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          }}
          className="flex items-center gap-2"
        >
          <FileDown className="h-4 w-4" />
          下载PDF
        </Button>
      </div>
    </div>
  )
}
