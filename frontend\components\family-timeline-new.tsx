"use client"

import type React from "react"

import { But<PERSON> } from "@/components/ui/button"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Edit, Plus, Trash2, Check, Upload, Loader2 } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "@/components/ui/use-toast"
import timelineService, { TimelineEvent, EventLevel, CreateTimelineEventParams, UpdateTimelineEventParams } from "@/services/timeline-service"
import { useModal, ModalContent } from "@/components/modal-provider"
import { logger, sanitizeData } from "@/utils/logger"

/**
 * 获取图片的完整URL
 * @param iconPath 图片路径
 * @returns 完整的图片URL
 */
function getImageUrl(iconPath: string | undefined): string {
  if (!iconPath) return '';

  // 如果已经是完整URL，直接返回
  if (iconPath.startsWith('http')) {
    // 检查是否是localhost:5001的URL，如果是，替换为正确的后端地址
    if (iconPath.includes('localhost:5001')) {
      // 在开发环境中，后端服务器地址是localhost:5001
      // 在生产环境中，需要使用正确的后端服务器地址
      return iconPath;
    }
    return iconPath;
  }

  // 使用后端服务器地址
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

  // 如果是相对路径，添加服务器地址
  // 如果路径以/uploads开头，直接添加后端服务器地址
  if (iconPath.startsWith('/uploads')) {
    return `${apiBaseUrl}${iconPath}`;
  }

  // 如果路径以/public开头，使用前端服务器地址
  if (iconPath.startsWith('/public')) {
    const frontendBaseUrl = typeof window !== 'undefined'
      ? window.location.origin
      : '';
    return `${frontendBaseUrl}${iconPath}`;
  }

  // 其他情况，添加后端服务器地址
  return `${apiBaseUrl}${iconPath}`;
}

// 限制为三种颜色
const colorPalette = [
  "#1e7a43", // 绿色
  "#f5a623", // 橙色
  "#3498db", // 蓝色
]

// 生成年份选项
const generateYearOptions = () => {
  const years = []
  for (let year = 1800; year <= 2200; year++) {
    years.push(year)
  }
  return years
}

/**
 * 家族时间轴组件
 *
 * 展示家族重要事件的时间轴，支持查看、添加、编辑和删除事件
 * 数据通过API与后端交互，实现持久化存储
 */
export function FamilyTimeline() {
  // 状态管理
  const [events, setEvents] = useState<TimelineEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [selectedEvent, setSelectedEvent] = useState<TimelineEvent | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [editingEvent, setEditingEvent] = useState<TimelineEvent | null>(null)
  const [isAddingEvent, setIsAddingEvent] = useState(false)
  const [newEvent, setNewEvent] = useState<Partial<TimelineEvent>>({
    year: "",
    title: "",
    description: "",
    content: "",
    level: EventLevel.Personal,
  })
  const [isUploading, setIsUploading] = useState(false)

  // 使用Auth上下文获取用户权限信息
  const { isLoggedIn, hasPermission } = useAuth()

  // 使用模态框
  const { openModal, closeModal } = useModal()

  // 检查用户是否有管理时间轴的权限
  // 简化权限检查逻辑，只使用useAuth钩子
  const canEditTimeline = isLoggedIn && hasPermission("edit_timeline");

  // 调试信息
  logger.debug("权限检查 - 是否登录:", isLoggedIn);
  logger.debug("权限检查 - 是否有edit_timeline权限:", hasPermission("edit_timeline"));
  logger.debug("权限检查 - 最终结果 canEditTimeline:", canEditTimeline);

  // 移除了图片查看模态框相关的状态和函数

  // 加载时间轴事件数据
  useEffect(() => {
    const loadEvents = async () => {
      setLoading(true)
      setError(null)

      try {
        logger.debug("开始加载时间轴事件...");

        // 设置超时处理
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('请求超时，请检查后端服务是否正常运行')), 10000)
        );

        // 使用Promise.race来处理超时
        const fetchPromise = timelineService.getTimelineEvents();

        // 尝试从API获取数据，但设置超时
        const data = await Promise.race([fetchPromise, timeoutPromise]);

        logger.debug("成功获取时间轴事件:", sanitizeData(data));
        setEvents(data);
      } catch (err: any) {
        logger.error("加载时间轴事件失败:", sanitizeData(err));

        // 提供更详细的错误信息
        let errorMessage = "加载时间轴事件失败";

        if (err.message === 'Network Error') {
          errorMessage = "网络错误，无法连接到后端服务器，请检查后端服务是否正常运行";
        } else if (err.message.includes('timeout')) {
          errorMessage = "请求超时，请检查后端服务是否正常运行";
        } else if (err.response) {
          errorMessage = `服务器错误 (${err.response.status}): ${err.response.data?.message || err.message}`;
        } else {
          errorMessage = err.message || "未知错误，请检查控制台获取详细信息";
        }

        setError(errorMessage);

        // 显示错误提示
        toast({
          title: "加载失败",
          description: errorMessage,
          variant: "destructive"
        });
      } finally {
        // 即使出错也设置loading为false，确保不会一直显示加载状态
        setLoading(false);
      }
    };

    loadEvents();
  }, []);

  // 按年份排序事件并确保一左一右分布
  const sortedEvents = [...events]
    .sort((a, b) => {
      // 提取年份数字进行比较
      const yearA = Number.parseInt(a.year.replace(/[^0-9]/g, ""))
      const yearB = Number.parseInt(b.year.replace(/[^0-9]/g, ""))
      return yearA - yearB
    })
    .map((event, index) => {
      // 确保严格一左一右分布
      return {
        ...event,
        position: index % 2 === 0 ? "left" : "right",
      }
    })

  // 处理事件点击
  const handleEventClick = (event: TimelineEvent) => {
    setSelectedEvent(event)
  }

  // 处理关闭事件详情
  const handleCloseDetails = () => {
    setSelectedEvent(null);
  }

  // 处理编辑事件
  const handleEditEvent = (event: TimelineEvent) => {
    setEditingEvent({ ...event })
    setIsEditing(true)
  }

  // 处理保存编辑
  const handleSaveEdit = async () => {
    if (!editingEvent) return

    try {
      // 调用API更新事件
      const updatedEvent = await timelineService.updateTimelineEvent(editingEvent.id, {
        year: editingEvent.year,
        title: editingEvent.title,
        description: editingEvent.description,
        content: editingEvent.content,
        level: editingEvent.level,
        icon: editingEvent.icon
      })

      // 更新本地状态
      setEvents(events.map(e => e.id === updatedEvent.id ? updatedEvent : e))

      // 如果当前正在查看这个事件，也更新选中的事件
      if (selectedEvent && selectedEvent.id === updatedEvent.id) {
        setSelectedEvent(updatedEvent)
      }

      // 显示成功提示
      toast({
        title: "更新成功",
        description: "事件已成功更新",
      })
    } catch (err: any) {
      logger.error("更新事件失败:", sanitizeData(err))

      // 显示错误提示
      toast({
        title: "更新失败",
        description: err.message || "更新事件失败，请稍后再试",
        variant: "destructive"
      })
    } finally {
      // 关闭编辑模式
      setIsEditing(false)
      setEditingEvent(null)
    }
  }

  // 处理删除事件
  const handleDeleteEvent = async (eventId: number) => {
    if (!confirm("确定要删除这个事件吗？此操作不可撤销。")) return

    try {
      // 调用API删除事件
      await timelineService.deleteTimelineEvent(eventId)

      // 更新本地状态
      setEvents(events.filter(e => e.id !== eventId))

      // 如果当前正在查看这个事件，关闭详情面板
      if (selectedEvent && selectedEvent.id === eventId) {
        setSelectedEvent(null)
      }

      // 显示成功提示
      toast({
        title: "删除成功",
        description: "事件已成功删除",
      })
    } catch (err: any) {
      logger.error("删除事件失败:", sanitizeData(err))

      // 显示错误提示
      toast({
        title: "删除失败",
        description: err.message || "删除事件失败，请稍后再试",
        variant: "destructive"
      })
    }
  }

  // 处理添加新事件
  const handleAddEvent = () => {
    logger.debug("添加事件按钮被点击");
    // 重置新事件表单
    setNewEvent({
      year: "",
      title: "",
      description: "",
      content: "",
      level: EventLevel.Personal,
    });

    // 确保关闭其他对话框
    setSelectedEvent(null);
    setEditingEvent(null);
    setIsEditing(false);

    // 打开添加事件对话框
    setIsAddingEvent(true);
    logger.debug("isAddingEvent设置为true");
  }

  // 处理保存新事件
  const handleSaveNewEvent = async () => {
    try {
      // 验证必填字段
      if (!newEvent.year || !newEvent.title || !newEvent.description || !newEvent.content) {
        toast({
          title: "验证失败",
          description: "请填写所有必填字段",
          variant: "destructive"
        })
        return
      }

      // 调用API创建事件
      const createdEvent = await timelineService.createTimelineEvent({
        year: newEvent.year,
        title: newEvent.title,
        description: newEvent.description,
        content: newEvent.content,
        level: newEvent.level as EventLevel,
        icon: newEvent.icon
      })

      // 更新本地状态
      setEvents([...events, createdEvent])

      // 显示成功提示
      toast({
        title: "创建成功",
        description: "事件已成功创建",
      })
    } catch (err: any) {
      logger.error("创建事件失败:", sanitizeData(err))

      // 显示错误提示
      toast({
        title: "创建失败",
        description: err.message || "创建事件失败，请稍后再试",
        variant: "destructive"
      })
    } finally {
      // 关闭添加模式
      setIsAddingEvent(false)
    }
  }

  // 处理上传图标
  const handleUploadIcon = async (event: React.ChangeEvent<HTMLInputElement>, isNewEvent: boolean) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploading(true)

    try {
      // 调用API上传图标
      const result = await timelineService.uploadTimelineEventIcon(file)

      // 获取API返回的URL
      let iconUrl = result.url;
      logger.debug("API返回的图标URL:", iconUrl);

      // 不需要修改URL，后端已经返回正确格式
      logger.debug("使用的图标URL:", iconUrl);

      // 更新状态
      if (isNewEvent) {
        setNewEvent({ ...newEvent, icon: iconUrl })
      } else if (editingEvent) {
        setEditingEvent({ ...editingEvent, icon: iconUrl })
      }

      // 显示成功提示
      toast({
        title: "上传成功",
        description: "图标已成功上传",
      })
    } catch (err: any) {
      logger.error("上传图标失败:", sanitizeData(err))

      // 显示错误提示
      toast({
        title: "上传失败",
        description: err.message || "上传图标失败，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsUploading(false)
    }
  }

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="flex flex-col items-center">
          <Loader2 className="h-8 w-8 animate-spin text-[#1e7a43]" />
          <p className="mt-2 text-gray-600">加载家族时间轴...</p>
        </div>
      </div>
    )
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center max-w-lg">
          <div className="text-red-500 mb-4 text-xl">加载失败</div>
          <p className="text-gray-700 mb-4">{error}</p>
          <div className="flex justify-center space-x-4">
            <Button
              onClick={() => window.location.reload()}
              className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
            >
              刷新页面
            </Button>
            <Button
              onClick={() => {
                setLoading(true);
                setError(null);
                // 重新加载数据
                timelineService.getTimelineEvents()
                  .then(data => {
                    setEvents(data);
                    setLoading(false);
                  })
                  .catch(err => {
                    setError(err.message || "重新加载失败，请刷新页面");
                    setLoading(false);
                  });
              }}
              variant="outline"
            >
              重试加载
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // 渲染空状态
  if (sortedEvents.length === 0) {
    return (
      <div className="relative">
        {canEditTimeline && (
          <div className="absolute top-0 right-0 z-10">
            <Button
              onClick={handleAddEvent}
              className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
              type="button"
            >
              <Plus className="h-4 w-4 mr-2" />
              添加事件
            </Button>
          </div>
        )}
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-gray-500 mb-2">暂无家族时间轴事件</p>
            {canEditTimeline && (
              <Button
                onClick={handleAddEvent}
                className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                type="button"
              >
                <Plus className="h-4 w-4 mr-2" />
                添加第一个事件
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      {/* 添加事件按钮 */}
      {canEditTimeline && (
        <div className="absolute top-0 right-0 z-10">
          <Button
            onClick={handleAddEvent}
            className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
            type="button"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加事件
          </Button>
        </div>
      )}

      {/* 时间轴 */}
      <div className="relative container mx-auto">
        {/* 中心线 */}
        <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-[#1e7a43]/20 rounded-full shadow-sm"></div>

        {/* 事件列表 */}
        <div className="relative">
          {sortedEvents.map((event, index) => {
            // 根据事件级别选择颜色
            const colorIndex = Object.values(EventLevel).indexOf(event.level)
            const color = colorPalette[colorIndex % colorPalette.length]

            return (
              <div
                key={event.id}
                className={cn(
                  "relative mb-8 md:flex items-center",
                  event.position === "left" ? "justify-start" : "justify-end",
                  "block md:block" // 在移动设备上始终显示为块级元素
                )}
              >
                {/* 左侧事件 */}
                {event.position === "left" && (
                  <div className="md:w-5/12 w-full md:pr-8 mb-4 md:mb-0 ml-8 md:ml-0">
                    <div
                      className="p-4 rounded-lg shadow-md cursor-pointer hover:shadow-lg transition-all hover:translate-x-1 bg-white"
                      style={{ borderLeft: `4px solid ${color}` }}
                      onClick={() => handleEventClick(event)}
                    >
                      <div className="font-bold text-lg text-[#1e7a43]">{event.year}</div>
                      <div className="font-semibold text-gray-800">{event.title}</div>
                      <div className="text-sm text-gray-600 mt-1">{event.description}</div>
                    </div>
                  </div>
                )}

                {/* 中心点 - 简化版，不显示图片 */}
                <div className="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-2 border-[#1e7a43] z-10 shadow-md"></div>

                {/* 右侧事件 */}
                {event.position === "right" && (
                  <div className="md:w-5/12 w-full md:pl-8 mb-4 md:mb-0 ml-8 md:ml-auto">
                    <div
                      className="p-4 rounded-lg shadow-md cursor-pointer hover:shadow-lg transition-all hover:-translate-x-1 bg-white"
                      style={{ borderRight: `4px solid ${color}` }}
                      onClick={() => handleEventClick(event)}
                    >
                      <div className="font-bold text-lg text-[#1e7a43]">{event.year}</div>
                      <div className="font-semibold text-gray-800">{event.title}</div>
                      <div className="text-sm text-gray-600 mt-1">{event.description}</div>
                    </div>
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* 事件详情对话框 */}
      {selectedEvent && (
        <Dialog
          open={!!selectedEvent}
          onOpenChange={(open) => {
            // 只有当open为false时才关闭对话框
            if (!open) {
              handleCloseDetails();
            }
          }}
        >
          <DialogContent className="max-w-3xl" hideCloseButton={true}>

            <DialogHeader>
              <DialogTitle>
                {selectedEvent.year} - {selectedEvent.title}
              </DialogTitle>
              {/* 自定义操作按钮组 */}
              <div className="absolute right-4 top-4 flex items-center space-x-4">
                {canEditTimeline && (
                  <>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditEvent(selectedEvent);
                      }}
                      className="opacity-70 hover:opacity-100"
                      title="编辑事件"
                    >
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">编辑</span>
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteEvent(selectedEvent.id);
                      }}
                      className="opacity-70 hover:opacity-100 text-red-500"
                      title="删除事件"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">删除</span>
                    </button>
                  </>
                )}
                <DialogPrimitive.Close className="opacity-70 hover:opacity-100">
                  <X className="h-4 w-4" />
                  <span className="sr-only">关闭</span>
                </DialogPrimitive.Close>
              </div>
            </DialogHeader>
            <div className="grid grid-cols-[auto_1fr] gap-4 items-start">
              {selectedEvent.icon && (
                <div className="col-span-1">
                  <div className="relative w-[200px] h-[200px] rounded-lg border border-gray-200 bg-gray-100 flex items-center justify-center overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-xs placeholder-text">
                      {selectedEvent.title?.charAt(0) || "图"}
                    </div>
                    <img
                      src={getImageUrl(selectedEvent.icon)}
                      alt={selectedEvent.title}
                      width={200}
                      height={200}
                      className="relative z-10 object-cover"
                      onLoad={(e) => {
                        // 图片加载成功时，隐藏文字占位符
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          const placeholder = parent.querySelector('.placeholder-text');
                          if (placeholder) {
                            placeholder.classList.add('hidden');
                          }
                        }
                      }}
                      onError={(e) => {
                        // 图片加载失败时，使用文字占位符
                        console.log("尝试加载图片:", selectedEvent.icon);
                        const fullUrl = getImageUrl(selectedEvent.icon);
                        console.log("完整URL:", fullUrl);

                        // 尝试使用备用URL
                        const backupUrl = selectedEvent.icon?.replace('localhost:5001', 'localhost:3000/api');
                        if (backupUrl && backupUrl !== fullUrl) {
                          console.log("尝试使用备用URL:", backupUrl);
                          e.currentTarget.src = backupUrl;
                          return;
                        }

                        // 如果备用URL也失败，隐藏图片，显示文字占位符
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                </div>
              )}
              <div className={selectedEvent.icon ? "col-span-1" : "col-span-2"}>
                <h3 className="font-semibold text-lg">{selectedEvent.description}</h3>
                <div className="mt-4 text-gray-700 whitespace-pre-line">{selectedEvent.content}</div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* 编辑事件对话框 */}
      {isEditing && editingEvent && (
        <Dialog open={isEditing} onOpenChange={(open) => !open && setIsEditing(false)}>
          <DialogContent className="max-w-3xl" hideCloseButton={true}>
            <DialogHeader>
              <DialogTitle>编辑事件</DialogTitle>
              <div className="absolute right-4 top-4 flex items-center">
                <DialogPrimitive.Close className="opacity-70 hover:opacity-100">
                  <X className="h-4 w-4" />
                  <span className="sr-only">关闭</span>
                </DialogPrimitive.Close>
              </div>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-year">年份</Label>
                  <Input
                    id="edit-year"
                    value={editingEvent.year}
                    onChange={(e) => setEditingEvent({ ...editingEvent, year: e.target.value })}
                    placeholder="例如：1921年"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-title">标题</Label>
                  <Input
                    id="edit-title"
                    value={editingEvent.title}
                    onChange={(e) => setEditingEvent({ ...editingEvent, title: e.target.value })}
                    placeholder="事件标题"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-description">简短描述</Label>
                  <Input
                    id="edit-description"
                    value={editingEvent.description}
                    onChange={(e) => setEditingEvent({ ...editingEvent, description: e.target.value })}
                    placeholder="简短描述（显示在时间轴上）"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-level">影响范围</Label>
                  <Select
                    value={editingEvent.level}
                    onValueChange={(value) => setEditingEvent({ ...editingEvent, level: value as EventLevel })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择影响范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={EventLevel.National}>国家</SelectItem>
                      <SelectItem value={EventLevel.Family}>家族</SelectItem>
                      <SelectItem value={EventLevel.Personal}>个人</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-content">详细内容</Label>
                  <Textarea
                    id="edit-content"
                    value={editingEvent.content}
                    onChange={(e) => setEditingEvent({ ...editingEvent, content: e.target.value })}
                    placeholder="详细内容（显示在详情页）"
                    className="min-h-[150px]"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-icon">事件图标</Label>
                  <div className="flex items-center space-x-4 mt-2">
                    {editingEvent.icon && (
                      <div className="relative w-[60px] h-[60px] rounded-full border border-gray-200 overflow-hidden">
                        <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-xs placeholder-text">
                          {editingEvent.title?.charAt(0) || "图"}
                        </div>
                        <img
                          src={getImageUrl(editingEvent.icon)}
                          alt="事件图标"
                          width={60}
                          height={60}
                          className="relative z-10 object-cover"
                          onLoad={(e) => {
                            // 图片加载成功时，隐藏文字占位符
                            const parent = e.currentTarget.parentElement;
                            if (parent) {
                              const placeholder = parent.querySelector('.placeholder-text');
                              if (placeholder) {
                                placeholder.classList.add('hidden');
                              }
                            }
                          }}
                          onError={(e) => {
                            // 图片加载失败时的处理
                            console.error("图片加载失败:", editingEvent.icon);
                            const fullUrl = getImageUrl(editingEvent.icon);
                            console.log("完整URL:", fullUrl);

                            // 尝试使用备用URL
                            const backupUrl = editingEvent.icon?.replace('localhost:5001', 'localhost:3000/api');
                            if (backupUrl && backupUrl !== fullUrl) {
                              console.log("尝试使用备用URL:", backupUrl);
                              e.currentTarget.src = backupUrl;
                              return;
                            }

                            // 将图片元素隐藏
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </div>
                    )}
                    <div className="relative">
                      <Input
                        id="edit-icon"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => handleUploadIcon(e, false)}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById("edit-icon")?.click()}
                        disabled={isUploading}
                      >
                        {isUploading ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            上传中...
                          </>
                        ) : (
                          <>
                            <Upload className="h-4 w-4 mr-2" />
                            上传图标
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                取消
              </Button>
              <Button onClick={handleSaveEdit} className="bg-[#1e7a43] hover:bg-[#1e7a43]/90">
                保存
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* 添加事件对话框 */}
      <Dialog
        open={isAddingEvent}
        onOpenChange={(open) => {
          if (!open) setIsAddingEvent(false);
        }}
      >
        <DialogContent className="max-w-3xl" hideCloseButton={true}>
          <DialogHeader>
            <DialogTitle>添加新事件</DialogTitle>
            <div className="absolute right-4 top-4 flex items-center">
              <DialogPrimitive.Close className="opacity-70 hover:opacity-100">
                <X className="h-4 w-4" />
                <span className="sr-only">关闭</span>
              </DialogPrimitive.Close>
            </div>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="new-year">年份</Label>
                <Input
                  id="new-year"
                  value={newEvent.year}
                  onChange={(e) => setNewEvent({ ...newEvent, year: e.target.value })}
                  placeholder="例如：1921年"
                />
              </div>
              <div>
                <Label htmlFor="new-title">标题</Label>
                <Input
                  id="new-title"
                  value={newEvent.title}
                  onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                  placeholder="事件标题"
                />
              </div>
              <div>
                <Label htmlFor="new-description">简短描述</Label>
                <Input
                  id="new-description"
                  value={newEvent.description}
                  onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                  placeholder="简短描述（显示在时间轴上）"
                />
              </div>
              <div>
                <Label htmlFor="new-level">影响范围</Label>
                <Select
                  value={newEvent.level as string}
                  onValueChange={(value) => setNewEvent({ ...newEvent, level: value as EventLevel })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择影响范围" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={EventLevel.National}>国家</SelectItem>
                    <SelectItem value={EventLevel.Family}>家族</SelectItem>
                    <SelectItem value={EventLevel.Personal}>个人</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <Label htmlFor="new-content">详细内容</Label>
                <Textarea
                  id="new-content"
                  value={newEvent.content}
                  onChange={(e) => setNewEvent({ ...newEvent, content: e.target.value })}
                  placeholder="详细内容（显示在详情页）"
                  className="min-h-[150px]"
                />
              </div>
              <div>
                <Label htmlFor="new-icon">事件图标</Label>
                <div className="flex items-center space-x-4 mt-2">
                  {newEvent.icon && (
                    <div className="relative w-[60px] h-[60px] rounded-full border border-gray-200 overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-xs placeholder-text">
                        {newEvent.title?.charAt(0) || "图"}
                      </div>
                      <img
                        src={getImageUrl(newEvent.icon)}
                        alt="事件图标"
                        width={60}
                        height={60}
                        className="relative z-10 object-cover"
                        onLoad={(e) => {
                          // 图片加载成功时，隐藏文字占位符
                          const parent = e.currentTarget.parentElement;
                          if (parent) {
                            const placeholder = parent.querySelector('.placeholder-text');
                            if (placeholder) {
                              placeholder.classList.add('hidden');
                            }
                          }
                        }}
                        onError={(e) => {
                          // 图片加载失败时的处理
                          console.error("图片加载失败:", newEvent.icon);
                          const fullUrl = getImageUrl(newEvent.icon);
                          console.log("完整URL:", fullUrl);

                          // 尝试使用备用URL
                          const backupUrl = newEvent.icon?.replace('localhost:5001', 'localhost:3000/api');
                          if (backupUrl && backupUrl !== fullUrl) {
                            console.log("尝试使用备用URL:", backupUrl);
                            e.currentTarget.src = backupUrl;
                            return;
                          }

                          // 将图片元素隐藏
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                  <div className="relative">
                    <Input
                      id="new-icon"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={(e) => handleUploadIcon(e, true)}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => document.getElementById("new-icon")?.click()}
                      disabled={isUploading}
                    >
                      {isUploading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          上传中...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4 mr-2" />
                          上传图标
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingEvent(false)}>
              取消
            </Button>
            <Button onClick={handleSaveNewEvent} className="bg-[#1e7a43] hover:bg-[#1e7a43]/90">
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 已移除图片查看模态框 */}
    </div>
  )
}
