/**
 * 数据库直接访问路由
 * 
 * 注意：这些路由仅用于开发和测试环境，生产环境应该禁用
 */

const express = require('express');
const router = express.Router();
const { User, Role } = require('../models');
const authMiddleware = require('../middlewares/authMiddleware');

/**
 * 获取所有用户数据
 * 仅用于开发和测试环境
 */
router.get('/users', authMiddleware, async (req, res) => {
  try {
    // 检查是否为管理员
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 检查是否为开发或测试环境
    if (process.env.NODE_ENV !== 'development' && process.env.NODE_ENV !== 'test') {
      return res.status(403).json({
        success: false,
        message: '此路由仅在开发和测试环境可用'
      });
    }

    const users = await User.findAll({
      attributes: { exclude: ['password'] },
      include: [
        {
          model: Role,
          as: 'userRole',
          attributes: ['id', 'name']
        }
      ]
    });

    res.status(200).json(users);
  } catch (error) {
    console.error('获取用户数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户数据失败',
      error: error.message
    });
  }
});

module.exports = router;
