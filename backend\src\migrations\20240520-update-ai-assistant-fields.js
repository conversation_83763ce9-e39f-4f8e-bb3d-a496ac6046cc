'use strict';

/**
 * 更新AI助手模型字段
 * 
 * 1. 更新app_id字段注释为"系统知识库数据集ID"
 * 2. 更新app_code字段注释为"用户知识库数据集ID"
 * 3. 更新upload_api_path字段注释
 * 4. 更新analysis_api_path字段注释
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 更新字段注释
    await queryInterface.changeColumn('ai_assistants', 'app_id', {
      type: Sequelize.STRING(100),
      allowNull: false,
      comment: '系统知识库数据集ID（知识库文件分析助手）或Dify应用ID（其他助手）'
    });

    await queryInterface.changeColumn('ai_assistants', 'app_code', {
      type: Sequelize.STRING(100),
      allowNull: false,
      comment: '用户知识库数据集ID（知识库文件分析助手）或Dify应用代码（其他助手）'
    });

    await queryInterface.changeColumn('ai_assistants', 'upload_api_path', {
      type: Sequelize.STRING(255),
      allowNull: true,
      comment: '文件上传API路径（仅知识库文件分析助手使用）'
    });

    await queryInterface.changeColumn('ai_assistants', 'analysis_api_path', {
      type: Sequelize.STRING(255),
      allowNull: true,
      comment: '文件分析API路径（仅知识库文件分析助手使用，已弃用）'
    });

    return Promise.resolve();
  },

  down: async (queryInterface, Sequelize) => {
    // 恢复原始字段注释
    await queryInterface.changeColumn('ai_assistants', 'app_id', {
      type: Sequelize.STRING(100),
      allowNull: false,
      comment: 'Dify应用ID'
    });

    await queryInterface.changeColumn('ai_assistants', 'app_code', {
      type: Sequelize.STRING(100),
      allowNull: false,
      comment: 'Dify应用代码'
    });

    await queryInterface.changeColumn('ai_assistants', 'upload_api_path', {
      type: Sequelize.STRING(255),
      allowNull: true,
      comment: '文件上传API路径（仅知识库文件分析助手使用）'
    });

    await queryInterface.changeColumn('ai_assistants', 'analysis_api_path', {
      type: Sequelize.STRING(255),
      allowNull: true,
      comment: '文件分析API路径（仅知识库文件分析助手使用）'
    });

    return Promise.resolve();
  }
};
