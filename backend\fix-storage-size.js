const { sequelize, KnowledgeBase, File } = require('./src/models');

async function fixStorageSize() {
  try {
    const knowledgeBases = await KnowledgeBase.findAll();
    console.log('开始修复知识库存储大小...');
    
    for (const kb of knowledgeBases) {
      // 获取已批准的文件总大小
      const approvedFilesResult = await File.findAll({
        where: { 
          knowledge_base_id: kb.id,
          status: 'approved'
        },
        attributes: [
          [sequelize.fn('SUM', sequelize.col('size')), 'total_size']
        ],
        raw: true
      });
      
      const approvedFilesSize = approvedFilesResult[0].total_size || 0;
      
      console.log(`知识库 ${kb.id} (${kb.name}): 当前存储大小=${kb.storage_size}, 实际已批准文件大小=${approvedFilesSize}`);
      
      if (kb.storage_size !== approvedFilesSize) {
        await kb.update({
          storage_size: approvedFilesSize
        });
        console.log(`已更新知识库 ${kb.id} 的存储大小为 ${approvedFilesSize} 字节`);
      }
    }
    
    console.log('知识库存储大小修复完成');
    process.exit(0);
  } catch (error) {
    console.error('修复知识库存储大小失败:', error);
    process.exit(1);
  }
}

fixStorageSize();
