/**
 * 文件控制器
 *
 * 处理文件相关的业务逻辑，如上传、下载、查询、审核等
 */

const db = require('../models');
const { File, KnowledgeBase, User, KnowledgeBaseAccess, sequelize } = db;
const { Op } = require('sequelize');
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const notificationService = require('../services/notification.service');

/**
 * 上传文件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.uploadFile = async (req, res) => {
  try {
    const { knowledgeBaseId } = req.params;
    // 获取是否使用Dify分析的参数，默认为true
    const useDify = req.body.use_dify !== 'false';

    console.log(`开始处理文件上传请求，知识库ID: ${knowledgeBaseId}`, {
      headers: req.headers,
      file: req.file ? {
        originalname: req.file.originalname,
        filename: req.file.filename,
        mimetype: req.file.mimetype,
        size: req.file.size,
        path: req.file.path
      } : null,
      decodedFileNames: req.decodedFileNames || {},
      useDify: useDify
    });

    // 检查文件是否上传
    if (!req.file) {
      console.error('文件上传失败: 未上传文件');
      return res.status(400).json({
        success: false,
        message: '未上传文件'
      });
    }

    // 查询知识库
    console.log(`查询知识库: ${knowledgeBaseId}`);
    const knowledgeBase = await KnowledgeBase.findByPk(knowledgeBaseId);

    if (!knowledgeBase) {
      console.error(`文件上传失败: 知识库不存在 (ID: ${knowledgeBaseId})`);
      // 删除已上传的文件
      fs.unlinkSync(req.file.path);

      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    console.log(`找到知识库:`, {
      id: knowledgeBase.id,
      name: knowledgeBase.name,
      type: knowledgeBase.type,
      creator_id: knowledgeBase.creator_id
    });

    // 检查上传权限
    let hasPermission = false;

    // 系统知识库：所有人都可以上传，但需要管理员审核
    if (knowledgeBase.type === 'system') {
      hasPermission = true;
      console.log('系统知识库：允许所有人上传文件，但需要管理员审核');
    }
    // 用户知识库：管理员、创建者和有写入/管理权限的用户可以上传
    else {
      // 管理员可以上传到任何知识库
      if (req.user.role === 'admin') {
        hasPermission = true;
        console.log('管理员：允许上传到任何知识库');
      }
      // 创建者可以上传到自己的知识库
      else if (knowledgeBase.creator_id === req.user.id) {
        hasPermission = true;
        console.log('知识库创建者：允许上传到自己的知识库');
      }
      // 检查是否有写入或管理权限
      else {
        const access = await KnowledgeBaseAccess.findOne({
          where: {
            knowledge_base_id: knowledgeBaseId,
            user_id: req.user.id,
            access_type: {
              [Op.in]: ['write', 'admin']
            }
          }
        });

        if (access) {
          hasPermission = true;
          console.log('用户有写入/管理权限：允许上传文件');
        }
      }
    }

    if (!hasPermission) {
      // 删除已上传的文件
      fs.unlinkSync(req.file.path);

      return res.status(403).json({
        success: false,
        message: '您没有权限上传文件到该知识库'
      });
    }

    // 确定文件状态
    // 系统知识库的文件需要审核，除非上传者是管理员
    // 用户知识库的文件，如果上传者是创建者或管理员，则不需要审核
    let status = 'pending';

    if (knowledgeBase.type === 'system') {
      // 系统知识库：只有管理员上传的文件自动批准，其他人上传需要审核
      if (req.user.role === 'admin') {
        status = 'approved';
        console.log('管理员上传到系统知识库：文件自动批准');
      } else {
        console.log('普通用户上传到系统知识库：文件需要审核');
      }
    } else {
      // 用户知识库：创建者或管理员上传的文件自动批准
      if (req.user.role === 'admin' || knowledgeBase.creator_id === req.user.id) {
        status = 'approved';
        console.log('管理员或知识库创建者上传到用户知识库：文件自动批准');
      } else {
        console.log('普通用户上传到用户知识库：文件需要审核');
      }
    }

    // 获取解码后的原始文件名
    let originalName = req.file.originalname;
    if (req.decodedFileNames && req.decodedFileNames[req.file.originalname]) {
      originalName = req.decodedFileNames[req.file.originalname];
    } else {
      // 如果没有从中间件获取到解码的文件名，尝试在这里解码
      try {
        originalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');
      } catch (e) {
        console.error('解码文件名失败:', e);
      }
    }

    // 获取正确的文件扩展名
    const fileExtension = path.extname(originalName).substring(1);

    // 创建文件记录
    console.log(`准备创建文件记录:`, {
      filename: req.file.filename,
      originalName,
      path: req.file.path,
      fileExtension,
      mimetype: req.file.mimetype,
      size: req.file.size,
      knowledgeBaseId,
      uploaderId: req.user.id,
      status
    });

    const file = await File.create({
      name: req.file.filename,
      original_name: originalName,
      path: req.file.path,
      type: fileExtension,
      mime_type: req.file.mimetype,
      size: req.file.size,
      knowledge_base_id: knowledgeBaseId,
      uploader_id: req.user.id,
      status,
      reviewer_id: status === 'approved' ? req.user.id : null,
      review_time: status === 'approved' ? new Date() : null
    });

    console.log(`文件记录创建成功:`, {
      id: file.id,
      name: file.name,
      original_name: file.original_name,
      type: file.type,
      size: file.size,
      status: file.status
    });

    // 只有已批准的文件才更新知识库存储大小和文件数量
    if (status === 'approved') {
      console.log(`文件已批准，更新知识库存储信息:`, {
        id: knowledgeBase.id,
        oldStorageSize: knowledgeBase.storage_size,
        newStorageSize: knowledgeBase.storage_size + req.file.size,
        oldFileCount: knowledgeBase.file_count,
        newFileCount: knowledgeBase.file_count + 1
      });

      await knowledgeBase.update({
        storage_size: knowledgeBase.storage_size + req.file.size,
        file_count: knowledgeBase.file_count + 1
      });
    } else {
      console.log(`文件待审核，不更新知识库存储信息`);
    }

    // 如果文件已自动批准，则上传到Dify知识库
    if (status === 'approved') {
      console.log(`【重要】文件已自动批准，准备上传到Dify知识库: ${file.id}`);

      try {
        // 直接调用上传函数，但不等待结果，避免阻塞响应
        console.log(`【重要】直接调用uploadFileToDifyDataset函数，文件ID: ${file.id}`);

        // 使用setTimeout确保在响应返回后执行上传
        setTimeout(async () => {
          try {
            console.log(`【重要】开始上传文件到Dify知识库: ${file.id}, 文件名: ${file.original_name}`);
            console.log(`【重要】文件路径: ${file.path}, 文件是否存在: ${fs.existsSync(file.path)}`);

            // 重新从数据库获取文件信息，确保数据是最新的
            const freshFile = await File.findByPk(file.id);
            if (!freshFile) {
              console.error(`【错误】无法从数据库获取文件信息，文件ID: ${file.id}`);
              return;
            }

            console.log(`【重要】从数据库获取的文件信息:`, {
              id: freshFile.id,
              name: freshFile.name,
              original_name: freshFile.original_name,
              path: freshFile.path,
              type: freshFile.type,
              size: freshFile.size,
              status: freshFile.status
            });

            // 调用上传函数
            const result = await uploadFileToDifyDataset(freshFile);

            if (result) {
              console.log(`【成功】文件 ${freshFile.id} 已成功上传到Dify知识库`);
            } else {
              console.warn(`【失败】文件 ${freshFile.id} 上传到Dify知识库失败`);
            }
          } catch (error) {
            console.error(`【错误】文件上传到Dify知识库失败:`, error);
            console.error(`【错误】错误详情:`, {
              message: error.message,
              stack: error.stack
            });
          }
        }, 100);

        console.log(`【重要】已安排文件 ${file.id} 上传到Dify知识库任务`);
      } catch (error) {
        console.error(`【错误】安排文件 ${file.id} 上传到Dify知识库失败:`, error);
        console.error(`【错误】错误详情:`, {
          message: error.message,
          stack: error.stack
        });
        // 上传失败不影响文件上传到服务器
      }
    } else if (status === 'pending') {
      // 如果文件需要审核，发送通知给管理员
      try {
        console.log(`文件需要审核，准备发送通知给管理员: ${file.id}`);

        // 获取上传者信息
        const uploader = await User.findByPk(req.user.id);

        if (uploader) {
          // 创建文件审核通知
          const notifications = await notificationService.createFileReviewNotification(file, knowledgeBase, uploader);
          console.log(`已为 ${notifications.length} 个管理员创建文件审核通知`);
        } else {
          console.warn(`未找到上传者信息，无法发送通知: ${req.user.id}`);
        }
      } catch (error) {
        console.error(`发送文件审核通知失败:`, error);
        // 通知失败不影响文件上传
      }
    }

    console.log(`文件上传处理完成，返回响应:`, {
      success: true,
      message: status === 'approved' ? '文件上传成功' : '文件上传成功，等待审核',
      data: {
        id: file.id,
        name: file.name,
        original_name: file.original_name,
        type: file.type,
        size: file.size,
        status: file.status
      }
    });

    res.status(201).json({
      success: true,
      message: status === 'approved' ? '文件上传成功' : '文件上传成功，等待审核',
      data: {
        id: file.id,
        name: file.name,
        original_name: file.original_name,
        type: file.type,
        size: file.size,
        status: file.status
      }
    });
  } catch (error) {
    // 如果文件已上传但创建记录失败，删除文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: '上传文件失败',
      error: error.message
    });
  }
};

/**
 * 获取文件列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getFiles = async (req, res) => {
  try {
    const { status, type, search, size_min, size_max, knowledge_base_type } = req.query;

    // 修复知识库ID参数解析
    let knowledge_base_ids = req.query['knowledge_base_ids[]']; // 获取知识库ID数组
    let knowledge_base_id = req.query.knowledge_base_id; // 获取单个知识库ID

    // 打印原始请求对象，帮助调试
    console.log('原始请求查询参数:', req.query);

    // 如果knowledge_base_ids是undefined，尝试从req.query中获取
    if (knowledge_base_ids === undefined) {
      // 检查是否有knowledge_base_ids参数
      if (req.query.knowledge_base_ids) {
        knowledge_base_ids = req.query.knowledge_base_ids;
      }
    }

    // 如果有单个知识库ID参数，将其转换为数组格式
    if (knowledge_base_id && (!knowledge_base_ids || (Array.isArray(knowledge_base_ids) && knowledge_base_ids.length === 0))) {
      knowledge_base_ids = [knowledge_base_id];
      console.log('将单个知识库ID转换为数组:', knowledge_base_ids);
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    console.log('获取文件列表请求参数(处理后):', {
      status,
      type,
      search,
      knowledge_base_ids,
      page,
      limit,
      offset,
      userId: req.user?.id,
      userRole: req.user?.role
    });

    // 如果没有指定知识库ID，获取用户有权限访问的所有知识库的文件
    if (!knowledge_base_ids || (Array.isArray(knowledge_base_ids) && knowledge_base_ids.length === 0)) {
      console.log('未指定知识库ID，获取用户有权限访问的所有知识库的文件');

      // 获取用户有权限的知识库ID
      let accessibleKnowledgeBaseIds = [];

      try {
        // 如果是管理员，获取所有知识库ID
        if (req.user.role === 'admin') {
          console.log('用户是管理员，获取所有知识库ID');
          const allKnowledgeBases = await KnowledgeBase.findAll({
            attributes: ['id']
          });
          accessibleKnowledgeBaseIds = allKnowledgeBases.map(kb => kb.id);
          console.log(`管理员可访问的所有知识库ID: ${accessibleKnowledgeBaseIds.join(', ')}`);
        } else {
          // 获取系统知识库ID
          const systemKnowledgeBases = await KnowledgeBase.findAll({
            where: { type: 'system' },
            attributes: ['id']
          });

          accessibleKnowledgeBaseIds = systemKnowledgeBases.map(kb => kb.id);

          // 获取用户创建的知识库ID
          const userCreatedKnowledgeBases = await KnowledgeBase.findAll({
            where: { creator_id: req.user.id },
            attributes: ['id']
          });

          // 获取用户有访问权限的知识库ID
          const userAccessKnowledgeBases = await KnowledgeBaseAccess.findAll({
            where: { user_id: req.user.id },
            attributes: ['knowledge_base_id']
          });

          // 合并所有有权限的知识库ID
          accessibleKnowledgeBaseIds = [
            ...accessibleKnowledgeBaseIds,
            ...userCreatedKnowledgeBases.map(kb => kb.id),
            ...userAccessKnowledgeBases.map(access => access.knowledge_base_id)
          ];
        }

        // 去重
        accessibleKnowledgeBaseIds = [...new Set(accessibleKnowledgeBaseIds)];

        console.log('用户有权限访问的知识库ID:', accessibleKnowledgeBaseIds);

        // 如果没有有权限的知识库，返回空结果
        if (accessibleKnowledgeBaseIds.length === 0) {
          return res.status(200).json({
            success: true,
            data: {
              files: [],
              pagination: {
                total: 0,
                page,
                limit,
                totalPages: 0
              }
            }
          });
        }

        // 设置知识库ID条件
        knowledge_base_ids = accessibleKnowledgeBaseIds;
      } catch (error) {
        console.error('获取用户有权限的知识库ID失败:', error);
        // 如果出错，返回空结果
        return res.status(200).json({
          success: true,
          data: {
            files: [],
            pagination: {
              total: 0,
              page,
              limit,
              totalPages: 0
            }
          }
        });
      }
    }

    // 打印所有知识库，帮助调试
    try {
      const allKnowledgeBases = await KnowledgeBase.findAll({
        attributes: ['id', 'name', 'type', 'creator_id']
      });

      console.log('系统中所有知识库:', allKnowledgeBases.map(kb => ({
        id: kb.id,
        name: kb.name,
        type: kb.type,
        creator_id: kb.creator_id
      })));
    } catch (e) {
      console.error('获取所有知识库失败:', e);
    }

    // 构建查询条件
    let whereConditions = {};

    // 根据知识库ID筛选 - 这是必须的条件
    if (Array.isArray(knowledge_base_ids)) {
      // 确保所有ID都是数字或字符串
      const processedIds = knowledge_base_ids.map(id => {
        // 尝试将ID转换为数字
        const numId = Number(id);
        return isNaN(numId) ? id : numId;
      });

      whereConditions.knowledge_base_id = {
        [Op.in]: processedIds
      };

      console.log(`按多个知识库ID筛选:`, {
        原始IDs: knowledge_base_ids,
        处理后IDs: processedIds,
        SQL条件: JSON.stringify(whereConditions)
      });

      // 查询这些ID对应的知识库是否存在
      try {
        const foundKnowledgeBases = await KnowledgeBase.findAll({
          where: {
            id: {
              [Op.in]: processedIds
            }
          },
          attributes: ['id', 'name', 'type']
        });

        console.log(`找到的知识库:`, foundKnowledgeBases.map(kb => ({
          id: kb.id,
          name: kb.name,
          type: kb.type
        })));

        if (foundKnowledgeBases.length !== processedIds.length) {
          console.warn(`警告: 请求的知识库ID数量(${processedIds.length})与找到的知识库数量(${foundKnowledgeBases.length})不匹配`);

          // 找出哪些ID没有找到对应的知识库
          const foundIds = foundKnowledgeBases.map(kb => kb.id);
          const missingIds = processedIds.filter(id => !foundIds.includes(id));
          console.warn(`未找到的知识库ID: ${missingIds.join(', ')}`);
        }
      } catch (e) {
        console.error('查询知识库失败:', e);
      }
    } else {
      // 处理单个ID
      const processedId = isNaN(Number(knowledge_base_ids)) ? knowledge_base_ids : Number(knowledge_base_ids);
      whereConditions.knowledge_base_id = processedId;
      console.log(`按单个知识库ID筛选:`, {
        原始ID: knowledge_base_ids,
        处理后ID: processedId,
        SQL条件: JSON.stringify(whereConditions)
      });

      // 查询这个ID对应的知识库是否存在
      try {
        const knowledgeBase = await KnowledgeBase.findByPk(processedId);
        if (knowledgeBase) {
          console.log(`找到知识库:`, {
            id: knowledgeBase.id,
            name: knowledgeBase.name,
            type: knowledgeBase.type
          });
        } else {
          console.warn(`警告: 未找到ID为${processedId}的知识库`);
        }
      } catch (e) {
        console.error('查询知识库失败:', e);
      }
    }

    // 根据状态筛选
    if (status) {
      whereConditions.status = status;
    } else {
      // 管理员可以看到所有状态的文件，非管理员只能看到已批准的文件
      if (req.user.role !== 'admin') {
        whereConditions.status = 'approved';
        console.log('非管理员用户，只显示已批准的文件');
      } else {
        console.log('管理员用户，显示所有状态的文件');
      }
    }

    // 根据类型筛选
    if (type) {
      whereConditions.type = type;
    }

    // 根据文件大小筛选
    if (size_min || size_max) {
      const sizeCondition = {};

      if (size_min) {
        sizeCondition[Op.gte] = parseInt(size_min);
      }

      if (size_max) {
        sizeCondition[Op.lte] = parseInt(size_max);
      }

      if (Object.keys(sizeCondition).length > 0) {
        whereConditions.size = sizeCondition;
        console.log('添加文件大小筛选条件:', JSON.stringify(sizeCondition));
      }
    }

    // 根据知识库类型筛选
    if (knowledge_base_type) {
      console.log('添加知识库类型筛选条件:', knowledge_base_type);

      // 需要联表查询知识库表
      const knowledgeBaseIds = await KnowledgeBase.findAll({
        where: { type: knowledge_base_type },
        attributes: ['id']
      }).then(kbs => kbs.map(kb => kb.id));

      console.log('找到的知识库IDs:', knowledgeBaseIds);

      if (knowledgeBaseIds.length > 0) {
        // 如果已经有知识库ID筛选条件，需要取交集
        if (whereConditions.knowledge_base_id) {
          // 如果是数组条件
          if (whereConditions.knowledge_base_id[Op.in]) {
            const existingIds = whereConditions.knowledge_base_id[Op.in];
            const intersection = existingIds.filter(id => knowledgeBaseIds.includes(id));
            whereConditions.knowledge_base_id = { [Op.in]: intersection };
          }
          // 如果是单个ID
          else {
            if (!knowledgeBaseIds.includes(whereConditions.knowledge_base_id)) {
              // 如果交集为空，返回空结果
              return res.status(200).json({
                success: true,
                data: {
                  files: [],
                  pagination: {
                    total: 0,
                    page,
                    limit,
                    totalPages: 0
                  }
                }
              });
            }
          }
        } else {
          whereConditions.knowledge_base_id = { [Op.in]: knowledgeBaseIds };
        }
      } else {
        // 如果没有找到符合条件的知识库，返回空结果
        return res.status(200).json({
          success: true,
          data: {
            files: [],
            pagination: {
              total: 0,
              page,
              limit,
              totalPages: 0
            }
          }
        });
      }
    }

    // 根据名称搜索
    if (search) {
      // 创建一个新的条件对象，保留知识库ID和状态筛选
      const baseConditions = { ...whereConditions };

      // 使用AND和OR组合条件
      whereConditions = {
        [Op.and]: [
          baseConditions,
          {
            [Op.or]: [
              { original_name: { [Op.like]: `%${search}%` } }
            ]
          }
        ]
      };
    }

    // 非管理员的权限控制
    if (req.user.role !== 'admin') {
      // 创建一个新的条件对象，保留之前的所有条件
      const baseConditions = { ...whereConditions };

      // 使用AND和OR组合条件，确保用户只能看到自己上传的文件和已批准的文件
      whereConditions = {
        [Op.and]: [
          baseConditions,
          {
            [Op.or]: [
              { uploader_id: req.user.id },
              { status: 'approved' }
            ]
          }
        ]
      };
      console.log('非管理员用户，添加权限过滤条件');
    } else {
      console.log('管理员用户，不添加额外的权限过滤条件');
    }

    // 记录最终查询条件
    console.log('文件列表最终查询条件:', JSON.stringify(whereConditions));

    // 查询文件
    console.log(`执行文件查询:`, {
      whereConditions,
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    // 临时测试：直接查询数据库中的所有文件
    try {
      const allFiles = await File.findAll({
        limit: 100,
        include: [
          {
            model: KnowledgeBase,
            as: 'knowledgeBase',
            attributes: ['id', 'name', 'type']
          },
          {
            model: User,
            as: 'uploader',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      console.log('数据库中的所有文件(ORM查询):', {
        文件数量: allFiles.length,
        文件列表: allFiles.map(file => ({
          id: file.id,
          name: file.name,
          original_name: file.original_name,
          type: file.type,
          status: file.status,
          knowledge_base_id: file.knowledge_base_id,
          knowledge_base: file.knowledgeBase ? {
            id: file.knowledgeBase.id,
            name: file.knowledgeBase.name,
            type: file.knowledgeBase.type
          } : null,
          uploader: file.uploader ? {
            id: file.uploader.id,
            username: file.uploader.username
          } : null
        }))
      });
    } catch (e) {
      console.error('查询所有文件失败:', e);
    }

    // 尝试使用原始SQL查询，以便更好地调试
    try {
      // 先查询所有文件，不加任何条件，看看数据库中是否有文件
      const [allFiles, allMetadata] = await db.sequelize.query(
        `SELECT f.id, f.name, f.original_name, f.type, f.size, f.status, f.knowledge_base_id,
                k.name as knowledge_base_name, k.type as knowledge_base_type,
                u.username as uploader_username
         FROM files f
         LEFT JOIN knowledge_bases k ON f.knowledge_base_id = k.id
         LEFT JOIN users u ON f.uploader_id = u.id
         LIMIT 100`
      );

      console.log(`数据库中所有文件:`, {
        查询到的文件数: allFiles.length,
        文件列表: allFiles.map(f => ({
          id: f.id,
          name: f.name,
          original_name: f.original_name,
          type: f.type,
          status: f.status,
          knowledge_base_id: f.knowledge_base_id,
          knowledge_base_name: f.knowledge_base_name
        }))
      });

      // 再查询指定知识库的文件，但不限制状态
      const [kbFiles, kbMetadata] = await db.sequelize.query(
        `SELECT f.id, f.name, f.original_name, f.type, f.size, f.status, f.knowledge_base_id,
                k.name as knowledge_base_name, k.type as knowledge_base_type,
                u.username as uploader_username
         FROM files f
         LEFT JOIN knowledge_bases k ON f.knowledge_base_id = k.id
         LEFT JOIN users u ON f.uploader_id = u.id
         WHERE f.knowledge_base_id IN (${Array.isArray(knowledge_base_ids) ? knowledge_base_ids.join(',') : knowledge_base_ids})
         LIMIT 100`
      );

      console.log(`指定知识库的所有文件(不限状态):`, {
        查询到的文件数: kbFiles.length,
        文件列表: kbFiles.map(f => ({
          id: f.id,
          name: f.name,
          original_name: f.original_name,
          type: f.type,
          status: f.status,
          knowledge_base_id: f.knowledge_base_id,
          knowledge_base_name: f.knowledge_base_name
        }))
      });

      // 最后查询符合所有条件的文件
      const [rawFiles, metadata] = await db.sequelize.query(
        `SELECT f.id, f.name, f.original_name, f.type, f.size, f.status, f.knowledge_base_id,
                k.name as knowledge_base_name, k.type as knowledge_base_type,
                u.username as uploader_username
         FROM files f
         LEFT JOIN knowledge_bases k ON f.knowledge_base_id = k.id
         LEFT JOIN users u ON f.uploader_id = u.id
         WHERE f.knowledge_base_id IN (${Array.isArray(knowledge_base_ids) ? knowledge_base_ids.join(',') : knowledge_base_ids})
         AND f.status = 'approved'
         ORDER BY f.created_at DESC
         LIMIT ${limit} OFFSET ${offset}`
      );

      console.log(`原始SQL查询结果(符合所有条件):`, {
        查询到的文件数: rawFiles.length,
        文件列表: rawFiles.map(f => ({
          id: f.id,
          name: f.name,
          original_name: f.original_name,
          type: f.type,
          status: f.status,
          knowledge_base_id: f.knowledge_base_id,
          knowledge_base_name: f.knowledge_base_name
        }))
      });
    } catch (sqlError) {
      console.error('原始SQL查询失败:', sqlError);
    }

    // 恢复正常的查询条件
    console.log('使用正常查询条件，应用知识库ID和搜索关键词过滤');

    // 构建查询条件
    let finalWhereConditions = {};

    // 1. 添加知识库ID条件 - 这是必须的
    if (Array.isArray(knowledge_base_ids)) {
      finalWhereConditions.knowledge_base_id = {
        [Op.in]: knowledge_base_ids.map(id => isNaN(Number(id)) ? id : Number(id))
      };
    } else if (knowledge_base_ids) {
      finalWhereConditions.knowledge_base_id = isNaN(Number(knowledge_base_ids)) ?
        knowledge_base_ids : Number(knowledge_base_ids);
    }

    // 2. 添加搜索条件
    if (search) {
      finalWhereConditions = {
        [Op.and]: [
          finalWhereConditions,
          {
            [Op.or]: [
              { original_name: { [Op.like]: `%${search}%` } }
            ]
          }
        ]
      };
    }

    // 3. 添加状态条件
    if (status) {
      // 如果指定了状态，使用指定的状态
      if (finalWhereConditions[Op.and]) {
        finalWhereConditions[Op.and].push({ status });
      } else {
        finalWhereConditions = {
          [Op.and]: [
            finalWhereConditions,
            { status }
          ]
        };
      }
    } else {
      // 如果没有指定状态
      // 管理员可以看到所有状态的文件，非管理员只能看到已批准的文件
      if (req.user.role !== 'admin') {
        // 非管理员只能看到已批准的文件
        if (finalWhereConditions[Op.and]) {
          finalWhereConditions[Op.and].push({ status: 'approved' });
        } else {
          finalWhereConditions = {
            [Op.and]: [
              finalWhereConditions,
              { status: 'approved' }
            ]
          };
        }
        console.log('最终查询条件：非管理员用户，只显示已批准的文件');
      } else {
        console.log('最终查询条件：管理员用户，显示所有状态的文件');
      }
    }

    console.log('最终查询条件:', JSON.stringify(finalWhereConditions));

    // 非管理员的权限控制（最终查询）
    if (req.user.role !== 'admin') {
      // 创建一个新的条件对象，保留之前的所有条件
      const baseFinalConditions = { ...finalWhereConditions };

      // 查询所有知识库，以便区分系统知识库和用户知识库
      const knowledgeBases = await KnowledgeBase.findAll({
        attributes: ['id', 'type', 'creator_id']
      });

      // 获取系统知识库ID列表
      const systemKnowledgeBaseIds = knowledgeBases
        .filter(kb => kb.type === 'system')
        .map(kb => kb.id);

      // 获取用户创建的知识库ID列表
      const userCreatedKnowledgeBaseIds = knowledgeBases
        .filter(kb => kb.type === 'user' && kb.creator_id === req.user.id)
        .map(kb => kb.id);

      console.log('系统知识库ID列表:', systemKnowledgeBaseIds);
      console.log('用户创建的知识库ID列表:', userCreatedKnowledgeBaseIds);

      // 使用AND和OR组合条件，确保用户只能看到：
      // 1. 系统知识库中状态为approved的文件
      // 2. 自己创建的用户知识库中的所有文件
      // 3. 其他用户知识库中状态为approved的文件
      finalWhereConditions = {
        [Op.and]: [
          baseFinalConditions,
          {
            [Op.or]: [
              // 系统知识库中的已批准文件
              {
                [Op.and]: [
                  { knowledge_base_id: { [Op.in]: systemKnowledgeBaseIds } },
                  { status: 'approved' }
                ]
              },
              // 自己创建的用户知识库中的所有文件
              {
                [Op.and]: [
                  { knowledge_base_id: { [Op.in]: userCreatedKnowledgeBaseIds } }
                ]
              },
              // 其他用户知识库中的已批准文件
              {
                [Op.and]: [
                  { knowledge_base_id: { [Op.notIn]: [...systemKnowledgeBaseIds, ...userCreatedKnowledgeBaseIds] } },
                  { status: 'approved' }
                ]
              }
            ]
          }
        ]
      };
      console.log('最终查询：非管理员用户，添加权限过滤条件');
    } else {
      console.log('最终查询：管理员用户，不添加额外的权限过滤条件');
    }

    console.log('最终查询条件（添加权限过滤后）:', JSON.stringify(finalWhereConditions));

    // 管理员特殊处理 - 直接查询所有文件
    if (req.user.role === 'admin') {
      console.log('管理员用户，直接查询所有文件，不使用复杂的查询条件');

      // 只保留知识库ID和搜索条件，移除其他所有条件
      let adminWhereConditions = {};

      // 1. 添加知识库ID条件（如果有）
      if (Array.isArray(knowledge_base_ids) && knowledge_base_ids.length > 0) {
        adminWhereConditions.knowledge_base_id = {
          [Op.in]: knowledge_base_ids.map(id => isNaN(Number(id)) ? id : Number(id))
        };
      } else if (knowledge_base_ids) {
        adminWhereConditions.knowledge_base_id = isNaN(Number(knowledge_base_ids)) ?
          knowledge_base_ids : Number(knowledge_base_ids);
      }

      // 2. 添加搜索条件（如果有）
      if (search) {
        adminWhereConditions = {
          [Op.and]: [
            adminWhereConditions,
            {
              [Op.or]: [
                { original_name: { [Op.like]: `%${search}%` } }
              ]
            }
          ]
        };
      }

      // 3. 添加状态条件（如果有）
      if (status) {
        if (adminWhereConditions[Op.and]) {
          adminWhereConditions[Op.and].push({ status });
        } else if (Object.keys(adminWhereConditions).length > 0) {
          adminWhereConditions = {
            [Op.and]: [
              adminWhereConditions,
              { status }
            ]
          };
        } else {
          adminWhereConditions.status = status;
        }
      }

      console.log('管理员查询条件:', JSON.stringify(adminWhereConditions));

      // 使用简化的查询条件
      const { count, rows: files } = await File.findAndCountAll({
        where: adminWhereConditions,
        include: [
          {
            model: KnowledgeBase,
            as: 'knowledgeBase',
            attributes: ['id', 'name', 'type']
          },
          {
            model: User,
            as: 'uploader',
            attributes: ['id', 'username', 'email']
          },
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'username', 'email']
          }
        ],
        limit,
        offset,
        order: [['created_at', 'DESC']]
      });

      console.log(`管理员查询结果:`, {
        totalCount: count,
        filesCount: files.length
      });

      return res.status(200).json({
        success: true,
        data: {
          files,
          pagination: {
            total: count,
            page,
            limit,
            totalPages: Math.ceil(count / limit)
          }
        }
      });
    }

    // 非管理员用户使用原有查询逻辑
    console.log('非管理员用户，使用原有查询逻辑');
    const { count, rows: files } = await File.findAndCountAll({
      where: finalWhereConditions,
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name', 'type']
        },
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'username', 'email']
        }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    console.log(`查询结果:`, {
      totalCount: count,
      filesCount: files.length
    });

    // 检查文件上传者信息
    files.forEach((file, index) => {
      console.log(`文件 ${index + 1}:`, {
        id: file.id,
        name: file.name,
        original_name: file.original_name,
        type: file.type,
        size: file.size,
        status: file.status,
        knowledge_base_id: file.knowledge_base_id,
        uploader_id: file.uploader_id,
        uploader: file.uploader ? {
          id: file.uploader.id,
          username: file.uploader.username
        } : null
      });
    });

    // 计算总页数
    const totalPages = Math.ceil(count / limit);

    res.status(200).json({
      success: true,
      data: {
        files,
        pagination: {
          total: count,
          page,
          limit,
          totalPages
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取文件列表失败',
      error: error.message
    });
  }
};

/**
 * 获取知识库文件列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getFilesByKnowledgeBase = async (req, res) => {
  try {
    const { knowledgeBaseId } = req.params;
    const { status, type, search, size_min, size_max } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    console.log(`获取知识库文件列表请求:`, {
      knowledgeBaseId,
      status,
      type,
      search,
      page,
      limit,
      offset,
      userId: req.user?.id,
      userRole: req.user?.role
    });

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(knowledgeBaseId);

    if (!knowledgeBase) {
      console.error(`获取知识库文件列表失败: 知识库不存在 (ID: ${knowledgeBaseId})`);
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    console.log(`找到知识库:`, {
      id: knowledgeBase.id,
      name: knowledgeBase.name,
      type: knowledgeBase.type,
      creator_id: knowledgeBase.creator_id
    });

    // 检查访问权限
    let hasPermission = false;

    // 管理员可以访问任何知识库
    if (req.user.role === 'admin') {
      hasPermission = true;
    }
    // 系统知识库所有人可以访问
    else if (knowledgeBase.type === 'system') {
      hasPermission = true;
    }
    // 创建者可以访问自己的知识库
    else if (knowledgeBase.creator_id === req.user.id) {
      hasPermission = true;
    }
    // 检查是否有访问权限
    else {
      const access = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id: knowledgeBaseId,
          user_id: req.user.id
        }
      });

      if (access) {
        hasPermission = true;
      }
    }

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '您没有权限访问该知识库'
      });
    }

    // 构建查询条件
    const whereConditions = {
      knowledge_base_id: knowledgeBaseId
    };

    // 根据状态筛选
    if (status) {
      whereConditions.status = status;
    } else {
      // 系统知识库：非管理员只能看到已批准的文件
      if (knowledgeBase.type === 'system') {
        if (req.user.role !== 'admin') {
          whereConditions.status = 'approved';
          console.log('系统知识库：非管理员用户只能看到已批准的文件');
        } else {
          console.log('系统知识库：管理员可以看到所有状态的文件');
        }
      }
      // 用户知识库：创建者可以看到所有状态的文件，其他用户只能看到已批准的文件
      else {
        if (req.user.role !== 'admin' && knowledgeBase.creator_id !== req.user.id) {
          whereConditions.status = 'approved';
          console.log('用户知识库：普通用户只能看到已批准的文件');
        } else {
          console.log('用户知识库：管理员或创建者可以看到所有状态的文件');
        }
      }
    }

    // 记录查询条件
    console.log('文件查询条件:', JSON.stringify(whereConditions));

    // 根据类型筛选
    if (type) {
      whereConditions.type = type;
    }

    // 根据文件大小筛选
    if (size_min || size_max) {
      const sizeCondition = {};

      if (size_min) {
        sizeCondition[Op.gte] = parseInt(size_min);
      }

      if (size_max) {
        sizeCondition[Op.lte] = parseInt(size_max);
      }

      if (Object.keys(sizeCondition).length > 0) {
        whereConditions.size = sizeCondition;
        console.log('添加文件大小筛选条件:', JSON.stringify(sizeCondition));
      }
    }

    // 根据名称搜索
    if (search) {
      whereConditions[Op.or] = [
        { original_name: { [Op.like]: `%${search}%` } }
      ];
    }

    // 查询文件
    console.log(`查询知识库文件:`, {
      whereConditions,
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    const { count, rows: files } = await File.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'username', 'email']
        }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    console.log(`查询结果:`, {
      totalCount: count,
      filesCount: files.length
    });

    // 检查文件上传者信息
    files.forEach((file, index) => {
      console.log(`文件 ${index + 1}:`, {
        id: file.id,
        name: file.name,
        original_name: file.original_name,
        type: file.type,
        size: file.size,
        status: file.status,
        uploader_id: file.uploader_id,
        uploader: file.uploader ? {
          id: file.uploader.id,
          username: file.uploader.username
        } : null
      });

      // 如果上传者信息缺失，尝试修复
      if (!file.uploader && file.uploader_id) {
        console.warn(`文件 ${file.id} 缺少上传者信息，但有上传者ID: ${file.uploader_id}`);
      }
    });

    // 计算总页数
    const totalPages = Math.ceil(count / limit);

    console.log(`返回知识库文件列表:`, {
      knowledgeBase: {
        id: knowledgeBase.id,
        name: knowledgeBase.name,
        type: knowledgeBase.type
      },
      filesCount: files.length,
      pagination: {
        total: count,
        page,
        limit,
        totalPages
      }
    });

    res.status(200).json({
      success: true,
      data: {
        knowledgeBase: {
          id: knowledgeBase.id,
          name: knowledgeBase.name,
          type: knowledgeBase.type
        },
        files,
        pagination: {
          total: count,
          page,
          limit,
          totalPages
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取知识库文件列表失败',
      error: error.message
    });
  }
};

/**
 * 获取文件详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getFileById = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询文件
    const file = await File.findByPk(id, {
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name', 'type']
        },
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    if (!file) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 检查访问权限
    let hasPermission = false;

    // 管理员可以访问任何文件
    if (req.user.role === 'admin') {
      hasPermission = true;
    }
    // 上传者可以访问自己的文件
    else if (file.uploader_id === req.user.id) {
      hasPermission = true;
    }
    // 已批准的文件，检查知识库访问权限
    else if (file.status === 'approved') {
      // 系统知识库所有人可以访问
      if (file.knowledgeBase.type === 'system') {
        hasPermission = true;
      }
      // 用户知识库需要检查访问权限
      else {
        const access = await KnowledgeBaseAccess.findOne({
          where: {
            knowledge_base_id: file.knowledge_base_id,
            user_id: req.user.id
          }
        });

        if (access) {
          hasPermission = true;
        }
      }
    }

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '您没有权限访问该文件'
      });
    }

    res.status(200).json({
      success: true,
      data: file
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取文件详情失败',
      error: error.message
    });
  }
};

/**
 * 下载文件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.downloadFile = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询文件
    const file = await File.findByPk(id, {
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['id', 'name', 'type']
        }
      ]
    });

    if (!file) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 检查访问权限
    let hasPermission = false;

    // 管理员可以下载任何文件
    if (req.user.role === 'admin') {
      hasPermission = true;
    }
    // 上传者可以下载自己的文件
    else if (file.uploader_id === req.user.id) {
      hasPermission = true;
    }
    // 已批准的文件，检查知识库访问权限
    else if (file.status === 'approved') {
      // 系统知识库所有人可以访问
      if (file.knowledgeBase.type === 'system') {
        hasPermission = true;
      }
      // 用户知识库需要检查访问权限
      else {
        const access = await KnowledgeBaseAccess.findOne({
          where: {
            knowledge_base_id: file.knowledge_base_id,
            user_id: req.user.id
          }
        });

        if (access) {
          hasPermission = true;
        }
      }
    }

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '您没有权限下载该文件'
      });
    }

    // 检查文件是否存在
    if (!fs.existsSync(file.path)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在或已被删除'
      });
    }

    // 设置响应头
    res.setHeader('Content-Type', file.mime_type);

    // 正确处理文件名编码
    // 使用RFC 5987编码，支持UTF-8字符
    const encodedFilename = encodeURIComponent(file.original_name).replace(/['()]/g, escape);
    res.setHeader('Content-Disposition', `attachment; filename="${encodedFilename}"; filename*=UTF-8''${encodedFilename}`);

    // 添加额外的缓存控制和类型头
    res.setHeader('Cache-Control', 'private, no-transform');

    // 发送文件
    const fileStream = fs.createReadStream(file.path);
    fileStream.pipe(res);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '下载文件失败',
      error: error.message
    });
  }
};

/**
 * 审核文件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewFile = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reject_reason } = req.body;

    // 验证状态
    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态，必须是 approved 或 rejected'
      });
    }

    // 查询文件
    const file = await File.findByPk(id, {
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase'
        },
        {
          model: User,
          as: 'uploader'
        }
      ]
    });

    if (!file) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 检查文件是否已审核
    if (file.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: '文件已审核'
      });
    }

    // 更新文件状态
    await file.update({
      status,
      reviewer_id: req.user.id,
      review_time: new Date(),
      reject_reason: status === 'rejected' ? reject_reason : null
    });

    // 获取知识库
    const knowledgeBase = await KnowledgeBase.findByPk(file.knowledge_base_id);

    if (knowledgeBase) {
      // 获取已批准的文件数量
      const approvedFilesCount = await File.count({
        where: {
          knowledge_base_id: file.knowledge_base_id,
          status: 'approved'
        }
      });

      // 获取已批准的文件总大小
      const approvedFilesResult = await File.findAll({
        where: {
          knowledge_base_id: file.knowledge_base_id,
          status: 'approved'
        },
        attributes: [
          [sequelize.fn('SUM', sequelize.col('size')), 'total_size']
        ],
        raw: true
      });

      const approvedFilesSize = approvedFilesResult[0].total_size || 0;

      // 更新知识库的文件计数和存储大小
      await knowledgeBase.update({
        file_count: approvedFilesCount,
        storage_size: approvedFilesSize
      });

      console.log(`已更新知识库 ${knowledgeBase.id} 的文件计数为 ${approvedFilesCount}，存储大小为 ${approvedFilesSize} 字节`);
    }

    // 如果批准文件，上传到Dify知识库
    if (status === 'approved') {
      console.log(`【重要】文件审核通过，准备上传到Dify知识库: ${file.id}`);

      try {
        // 使用setTimeout确保在响应返回后执行上传
        setTimeout(async () => {
          try {
            console.log(`【重要】开始上传文件到Dify知识库: ${file.id}, 文件名: ${file.original_name}`);
            console.log(`【重要】文件路径: ${file.path}, 文件是否存在: ${fs.existsSync(file.path)}`);

            // 重新从数据库获取文件信息，确保数据是最新的
            const freshFile = await File.findByPk(file.id);
            if (!freshFile) {
              console.error(`【错误】无法从数据库获取文件信息，文件ID: ${file.id}`);
              return;
            }

            console.log(`【重要】从数据库获取的文件信息:`, {
              id: freshFile.id,
              name: freshFile.name,
              original_name: freshFile.original_name,
              path: freshFile.path,
              type: freshFile.type,
              size: freshFile.size,
              status: freshFile.status
            });

            // 调用上传函数
            const result = await uploadFileToDifyDataset(freshFile);

            if (result) {
              console.log(`【成功】文件 ${freshFile.id} 已成功上传到Dify知识库`);
            } else {
              console.warn(`【失败】文件 ${freshFile.id} 上传到Dify知识库失败`);
            }
          } catch (error) {
            console.error(`【错误】文件上传到Dify知识库失败:`, error);
            console.error(`【错误】错误详情:`, {
              message: error.message,
              stack: error.stack
            });
          }
        }, 100);

        console.log(`【重要】已安排文件 ${file.id} 上传到Dify知识库任务`);
      } catch (error) {
        console.error(`【错误】安排文件 ${file.id} 上传到Dify知识库失败:`, error);
        console.error(`【错误】错误详情:`, {
          message: error.message,
          stack: error.stack
        });
      }
    }

    // 发送通知给上传者
    try {
      console.log(`准备发送审核结果通知给上传者: ${file.uploader_id}`);

      // 获取审核者信息
      const reviewer = await User.findByPk(req.user.id);

      if (reviewer) {
        // 创建文件审核结果通知
        const notification = await notificationService.createFileReviewResultNotification(
          file,
          knowledgeBase,
          reviewer,
          status,
          reject_reason
        );

        if (notification) {
          console.log(`已为上传者 ${file.uploader_id} 创建文件审核结果通知`);
        } else {
          console.warn(`创建文件审核结果通知失败`);
        }
      } else {
        console.warn(`未找到审核者信息，无法发送通知: ${req.user.id}`);
      }
    } catch (error) {
      console.error(`发送文件审核结果通知失败:`, error);
      // 通知失败不影响文件审核
    }

    res.status(200).json({
      success: true,
      message: status === 'approved' ? '文件已批准' : '文件已拒绝',
      data: {
        id: file.id,
        status: file.status,
        reviewer_id: file.reviewer_id,
        review_time: file.review_time,
        reject_reason: file.reject_reason
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '审核文件失败',
      error: error.message
    });
  }
};

/**
 * 上传文件到Dify知识库
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.uploadFileToDify = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询文件
    const file = await File.findByPk(id, {
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase'
        }
      ]
    });

    if (!file) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 检查文件是否已批准
    if (file.status !== 'approved') {
      return res.status(400).json({
        success: false,
        message: '只能分析已批准的文件'
      });
    }

    // 检查访问权限
    let hasPermission = false;

    // 管理员可以分析任何文件
    if (req.user.role === 'admin') {
      hasPermission = true;
    }
    // 上传者可以分析自己的文件
    else if (file.uploader_id === req.user.id) {
      hasPermission = true;
    }
    // 知识库创建者可以分析知识库中的文件
    else if (file.knowledgeBase.creator_id === req.user.id) {
      hasPermission = true;
    }
    // 检查是否有知识库管理权限
    else {
      const access = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id: file.knowledge_base_id,
          user_id: req.user.id,
          access_type: 'admin'
        }
      });

      if (access) {
        hasPermission = true;
      }
    }

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '您没有权限分析该文件'
      });
    }

    // 调用上传到Dify知识库API
    const result = await uploadFileToDifyDataset(file);

    if (!result) {
      throw new Error('上传文件到Dify知识库失败');
    }

    res.status(200).json({
      success: true,
      message: '文件已成功上传到Dify知识库',
      data: {
        id: file.id,
        dify_task_id: file.dify_task_id
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '上传文件到Dify知识库失败',
      error: error.message
    });
  }
};

/**
 * 删除文件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteFile = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询文件
    const file = await File.findByPk(id, {
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase'
        }
      ]
    });

    if (!file) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 检查删除权限
    let hasPermission = false;

    // 管理员可以删除任何文件
    if (req.user.role === 'admin') {
      hasPermission = true;
    }
    // 上传者可以删除自己的文件
    else if (file.uploader_id === req.user.id) {
      hasPermission = true;
    }
    // 知识库创建者可以删除知识库中的文件
    else if (file.knowledgeBase.creator_id === req.user.id) {
      hasPermission = true;
    }
    // 检查是否有知识库管理权限
    else {
      const access = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id: file.knowledge_base_id,
          user_id: req.user.id,
          access_type: 'admin'
        }
      });

      if (access) {
        hasPermission = true;
      }
    }

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '您没有权限删除该文件'
      });
    }

    // 获取知识库
    const knowledgeBase = await KnowledgeBase.findByPk(file.knowledge_base_id);

    // 删除文件
    if (fs.existsSync(file.path)) {
      fs.unlinkSync(file.path);
    }

    // 更新知识库存储大小和文件数量
    if (knowledgeBase) {
      await knowledgeBase.update({
        storage_size: Math.max(0, knowledgeBase.storage_size - file.size),
        file_count: Math.max(0, knowledgeBase.file_count - 1)
      });
    }

    // 删除文件记录
    await file.destroy();

    res.status(200).json({
      success: true,
      message: '文件删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除文件失败',
      error: error.message
    });
  }
};

/**
 * 在后台分析文件
 * @param {number} fileId - 文件ID
 * @returns {Promise<boolean>} 分析是否成功
 */
exports.analyzeFileInBackground = async (fileId) => {
  try {
    console.log(`开始后台分析文件 ${fileId}`);

    // 先更新文件状态，标记为正在分析
    await File.update(
      {
        analysis_status: 'processing',
        analysis_started_at: new Date()
      },
      { where: { id: fileId } }
    );

    // 查询文件
    const file = await File.findByPk(fileId, {
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase'
        }
      ]
    });

    if (!file) {
      console.error(`文件 ${fileId} 不存在`);
      return false;
    }

    if (file.status !== 'approved') {
      console.error(`文件 ${fileId} 未批准，无法分析`);

      // 更新文件状态，标记为分析失败
      await file.update({
        analysis_status: 'failed',
        analysis_error: '文件未批准，无法分析'
      });

      return false;
    }

    console.log(`文件 ${fileId} 开始上传到Dify知识库`);

    // 调用上传到Dify知识库API
    const result = await uploadFileToDifyDataset(file);

    if (!result) {
      console.warn(`文件 ${fileId} 上传到Dify知识库失败`);
      return false;
    }

    console.log(`文件 ${fileId} 已成功上传到Dify知识库`);
    return true;
  } catch (error) {
    console.error(`文件 ${fileId} 上传到Dify知识库失败:`, error);

    // 更新文件状态，标记为分析失败
    try {
      await File.update(
        {
          analysis_status: 'failed',
          analysis_error: error.message || '上传到Dify知识库失败'
        },
        { where: { id: fileId } }
      );
    } catch (updateError) {
      console.error(`更新文件 ${fileId} 分析状态失败:`, updateError);
    }

    return false;
  }
};

/**
 * 将文件上传到Dify知识库
 * @param {Object} file - 文件对象
 * @returns {Promise<boolean>} 上传是否成功
 */
const uploadFileToDifyDataset = async (file) => {
  console.log(`【Dify上传】开始处理文件上传到Dify知识库，文件ID: ${file?.id || '未知'}`);

  try {
    // 检查文件对象是否有效
    if (!file) {
      console.error('【Dify上传】文件对象为空');
      return false;
    }

    console.log(`【Dify上传】文件信息:`, {
      id: file.id,
      name: file.name,
      original_name: file.original_name,
      path: file.path,
      type: file.type,
      size: file.size,
      status: file.status,
      knowledge_base_id: file.knowledge_base_id
    });

    // 检查文件是否存在
    if (!fs.existsSync(file.path)) {
      console.error(`【Dify上传】文件不存在: ${file.path}`);
      throw new Error(`文件不存在: ${file.path}`);
    }

    // 获取知识库信息，以确定使用哪个Dify知识库
    const knowledgeBase = await file.getKnowledgeBase();
    if (!knowledgeBase) {
      console.error(`【Dify上传】无法获取知识库信息，文件ID: ${file.id}`);
      throw new Error(`无法获取知识库信息`);
    }

    console.log(`【Dify上传】知识库信息:`, {
      id: knowledgeBase.id,
      name: knowledgeBase.name,
      type: knowledgeBase.type
    });

    // 准备FormData
    const FormData = require('form-data');
    const formData = new FormData();

    // 获取文件类型
    const fileType = file.type.toLowerCase();

    // 根据文件类型进行不同处理
    let fileToUpload = file.path;
    let fileNameToUpload = file.original_name;

    // 文本类文件和结构化文档可以直接上传
    const directSupportedTypes = ['txt', 'md', 'csv', 'pdf', 'docx'];

    if (directSupportedTypes.includes(fileType)) {
      console.log(`【Dify上传】文件类型 ${fileType} 可以直接上传`);
    }
    // 旧版Office文档需要转换为文本
    else if (['doc', 'xls', 'ppt'].includes(fileType)) {
      console.log(`【Dify上传】检测到旧版Office文件 ${fileType}，尝试转换为文本文件`);

      try {
        // 创建文本文件路径
        const textFilePath = path.join(path.dirname(file.path), `${path.basename(file.path, `.${fileType}`)}.txt`);

        // 读取文件内容
        const fileContent = fs.readFileSync(file.path);

        // 提取可读文本
        let textContent = '';
        for (let i = 0; i < fileContent.length; i++) {
          const char = String.fromCharCode(fileContent[i]);
          if (char.match(/[a-zA-Z0-9\s\p{Script=Han}]/u)) {
            textContent += char;
          }
        }

        // 写入文本文件
        fs.writeFileSync(textFilePath, textContent);
        console.log(`【Dify上传】已创建文本文件: ${textFilePath}`);

        // 使用文本文件
        fileToUpload = textFilePath;
        fileNameToUpload = `${file.original_name}.txt`;
      } catch (conversionError) {
        console.error(`【Dify上传】转换文件失败:`, conversionError);
        console.log(`【Dify上传】将使用原始文件上传`);
      }
    }
    // 图片文件转换为Base64并添加描述
    else if (['png', 'jpg', 'jpeg', 'gif', 'bmp'].includes(fileType)) {
      console.log(`【Dify上传】检测到图片文件 ${fileType}，转换为Base64并添加描述`);

      try {
        // 读取图片文件
        const imageBuffer = fs.readFileSync(file.path);

        // 转换为Base64
        const base64Image = imageBuffer.toString('base64');
        console.log(`【Dify上传】已将图片转换为Base64，长度: ${base64Image.length}`);

        // 创建包含Base64和描述的文件
        const htmlFilePath = path.join(path.dirname(file.path), `${path.basename(file.path, `.${fileType}`)}_with_image.html`);

        // 创建HTML内容，包含图片和描述
        const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>${file.original_name}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .image-container { margin: 20px 0; }
        .image-info { margin: 20px 0; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>图片文件: ${file.original_name}</h1>

    <div class="image-container">
        <h2>图片预览</h2>
        <img src="data:image/${fileType};base64,${base64Image}" alt="${file.original_name}" style="max-width: 100%; max-height: 500px;">
    </div>

    <div class="image-info">
        <h2>图片信息</h2>
        <table>
            <tr><th>属性</th><th>值</th></tr>
            <tr><td>文件名</td><td>${file.original_name}</td></tr>
            <tr><td>文件类型</td><td>图片文件 (${fileType})</td></tr>
            <tr><td>文件大小</td><td>${file.size} 字节</td></tr>
            <tr><td>上传时间</td><td>${file.created_at}</td></tr>
            <tr><td>描述</td><td>${file.summary || '无描述'}</td></tr>
            <tr><td>详细信息</td><td>${file.detailed_description || '无详细信息'}</td></tr>
        </table>
    </div>

    <div class="base64-data" style="display: none;">
        <h2>Base64数据</h2>
        <textarea style="width: 100%; height: 200px;">${base64Image}</textarea>
    </div>
</body>
</html>
        `;

        // 写入HTML文件
        fs.writeFileSync(htmlFilePath, htmlContent);
        console.log(`【Dify上传】已创建包含Base64图片的HTML文件: ${htmlFilePath}`);

        // 使用HTML文件
        fileToUpload = htmlFilePath;
        fileNameToUpload = `${file.original_name}.html`;

        // 同时创建纯文本版本作为备用
        const textFilePath = path.join(path.dirname(file.path), `${path.basename(file.path, `.${fileType}`)}_with_image.txt`);

        // 创建文本内容，包含描述和Base64（截断以避免文件过大）
        const textContent = `
图片文件: ${file.original_name}

图片信息:
- 文件名: ${file.original_name}
- 文件类型: 图片文件 (${fileType})
- 文件大小: ${file.size} 字节
- 上传时间: ${file.created_at}
- 描述: ${file.summary || '无描述'}
- 详细信息: ${file.detailed_description || '无详细信息'}

Base64图片数据 (前1000字符):
${base64Image.substring(0, 1000)}...
        `;

        // 写入文本文件
        fs.writeFileSync(textFilePath, textContent);
        console.log(`【Dify上传】已创建包含Base64图片的文本文件作为备用: ${textFilePath}`);
      } catch (imageError) {
        console.error(`【Dify上传】处理图片失败:`, imageError);

        // 创建备用描述文件
        const descFilePath = path.join(path.dirname(file.path), `${path.basename(file.path, `.${fileType}`)}_description.txt`);

        // 创建详细描述
        const description = `
文件名: ${file.original_name}
文件类型: 图片文件 (${fileType})
文件大小: ${file.size} 字节
上传时间: ${file.created_at}
描述: ${file.summary || '无描述'}
详细信息: ${file.detailed_description || '无详细信息'}
        `;

        // 写入描述文件
        fs.writeFileSync(descFilePath, description);
        console.log(`【Dify上传】已创建图片描述文件作为备用: ${descFilePath}`);

        // 使用描述文件作为备用
        fileToUpload = descFilePath;
        fileNameToUpload = `${file.original_name}_description.txt`;
      }
    }
    // 多媒体文件需要添加描述
    else if (['mp4', 'mp3', 'avi', 'mov', 'wmv'].includes(fileType)) {
      console.log(`【Dify上传】检测到多媒体文件 ${fileType}，添加描述信息`);

      // 创建描述文件
      const descFilePath = path.join(path.dirname(file.path), `${path.basename(file.path, `.${fileType}`)}_description.txt`);

      // 创建简单描述
      const description = `
文件名: ${file.original_name}
文件类型: 多媒体文件 (${fileType})
文件大小: ${file.size} 字节
上传时间: ${file.created_at}
描述: ${file.summary || '无描述'}
详细信息: ${file.detailed_description || '无详细信息'}
      `;

      // 写入描述文件
      fs.writeFileSync(descFilePath, description);
      console.log(`【Dify上传】已创建多媒体描述文件: ${descFilePath}`);

      // 使用描述文件
      fileToUpload = descFilePath;
      fileNameToUpload = `${file.original_name}_description.txt`;
    }
    // 其他类型文件，创建描述文件
    else {
      console.log(`【Dify上传】检测到其他类型文件 ${fileType}，创建描述文件`);

      // 创建描述文件
      const descFilePath = path.join(path.dirname(file.path), `${path.basename(file.path, `.${fileType}`)}_description.txt`);

      // 创建简单描述
      const description = `
文件名: ${file.original_name}
文件类型: ${fileType}
文件大小: ${file.size} 字节
上传时间: ${file.created_at}
描述: ${file.summary || '无描述'}
详细信息: ${file.detailed_description || '无详细信息'}
      `;

      // 写入描述文件
      fs.writeFileSync(descFilePath, description);
      console.log(`【Dify上传】已创建文件描述文件: ${descFilePath}`);

      // 使用描述文件
      fileToUpload = descFilePath;
      fileNameToUpload = `${file.original_name}_description.txt`;
    }

    // 添加文件
    formData.append('file', fs.createReadStream(fileToUpload), fileNameToUpload);
    console.log(`【Dify上传】添加文件到FormData: ${fileToUpload}, 名称: ${fileNameToUpload}`);

    // 添加处理规则
    const processRule = {
      indexing_technique: "high_quality",
      process_rule: {
        rules: {
          pre_processing_rules: [
            { id: "remove_extra_spaces", enabled: true },
            { id: "remove_urls_emails", enabled: true }
          ],
          segmentation: {
            separator: "###",
            max_tokens: 500
          }
        },
        mode: "custom"
      }
    };

    formData.append('data', JSON.stringify(processRule), { type: 'text/plain' });

    // 根据知识库类型选择不同的AI平台知识库
    let datasetId, apiKey;

    // 获取AI助手配置
    const AIAssistant = require('../models').AIAssistant;

    // 首先尝试查找特定类型的知识库文件助手
    let knowledgeFileAssistant;

    if (knowledgeBase.type === 'system') {
      // 查找系统知识库文件助手
      knowledgeFileAssistant = await AIAssistant.findOne({
        where: {
          type: 'system-knowledge-file',
          status: 'active'
        }
      });

      // 如果没有找到特定类型的助手，则查找通用知识库文件分析助手
      if (!knowledgeFileAssistant) {
        knowledgeFileAssistant = await AIAssistant.findOne({
          where: {
            type: 'knowledge-file',
            status: 'active'
          }
        });
      }

      // 系统知识库 - 使用系统知识库数据集ID
      datasetId = '77199451-730a-4d79-a1c9-9b9e6bfcd747';
      apiKey = "dataset-DLFJlUe25VUHOwMO4HbO4hQk";

      // 如果有配置，优先使用配置中的值
      if (knowledgeFileAssistant) {
        // 使用app_id字段作为系统知识库数据集ID
        if (knowledgeFileAssistant.app_id) {
          datasetId = knowledgeFileAssistant.app_id;
          console.log(`【AI平台上传】从配置获取系统知识库数据集ID: ${datasetId}`);
        }

        if (knowledgeFileAssistant.api_key) {
          apiKey = knowledgeFileAssistant.api_key;
          console.log(`【AI平台上传】从配置获取API密钥: ${apiKey.substring(0, 5)}...`);
        }
      }

      console.log(`【AI平台上传】系统知识库文件，使用系统知识库数据集: ${datasetId}`);
    } else {
      // 查找用户知识库文件助手
      knowledgeFileAssistant = await AIAssistant.findOne({
        where: {
          type: 'user-knowledge-file',
          status: 'active'
        }
      });

      // 如果没有找到特定类型的助手，则查找通用知识库文件分析助手
      if (!knowledgeFileAssistant) {
        knowledgeFileAssistant = await AIAssistant.findOne({
          where: {
            type: 'knowledge-file',
            status: 'active'
          }
        });
      }

      // 用户知识库 - 使用用户知识库数据集ID
      datasetId = '602d59cf-3384-4105-bf91-e1481b30b6b2';
      apiKey = "dataset-DLFJlUe25VUHOwMO4HbO4hQk";

      // 如果有配置，优先使用配置中的值
      if (knowledgeFileAssistant) {
        // 使用app_code字段作为用户知识库数据集ID
        if (knowledgeFileAssistant.app_code) {
          datasetId = knowledgeFileAssistant.app_code;
          console.log(`【AI平台上传】从配置获取用户知识库数据集ID: ${datasetId}`);
        }

        if (knowledgeFileAssistant.api_key) {
          apiKey = knowledgeFileAssistant.api_key;
          console.log(`【AI平台上传】从配置获取API密钥: ${apiKey.substring(0, 5)}...`);
        }
      }

      console.log(`【AI平台上传】用户知识库文件，使用用户知识库数据集: ${datasetId}`);
    }

    const apiEndpoint = `https://ai.glab.vip/v1/datasets/${datasetId}/document/create-by-file`;

    console.log(`【AI平台上传】准备发送请求到: ${apiEndpoint}`);
    console.log(`【AI平台上传】使用API密钥: ${apiKey ? apiKey.substring(0, 5) + '...' : '未设置'}`);

    // 发送请求
    const uploadResponse = await axios.post(
      apiEndpoint,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          ...formData.getHeaders()
        },
        timeout: 120000 // 120秒超时
      }
    );

    console.log(`【AI平台上传】请求已发送，状态码: ${uploadResponse.status}`);
    console.log(`【AI平台上传】响应数据:`, uploadResponse.data);

    if (!uploadResponse.data || !uploadResponse.data.document) {
      console.error(`【AI平台上传】响应数据不包含document字段:`, uploadResponse.data);
      throw new Error('上传文件到AI平台知识库失败，响应数据不包含document字段');
    }

    // 更新文件记录，保存Dify文档ID
    const documentId = uploadResponse.data.document.id;
    await file.update({
      dify_task_id: documentId,
      analysis_status: 'completed',
      analysis_completed_at: new Date()
    });

    console.log(`【Dify上传成功】文件 ${file.id} 已成功上传到Dify知识库，文档ID: ${documentId}`);
    return true;
  } catch (error) {
    console.error(`【Dify上传失败】上传文件到Dify知识库失败 (文件ID: ${file?.id}):`, error.message);

    // 详细记录错误信息
    if (error.response) {
      console.error('【Dify上传失败】响应状态码:', error.response.status);
      console.error('【Dify上传失败】响应数据:', error.response.data);

      // 记录请求信息
      console.error('【Dify上传失败】请求URL:', error.config?.url);
      console.error('【Dify上传失败】请求方法:', error.config?.method);
      console.error('【Dify上传失败】请求头:', error.config?.headers);
    } else if (error.request) {
      console.error('【Dify上传失败】请求已发送但未收到响应:', error.request);
    } else {
      console.error('【Dify上传失败】错误详情:', error.stack);
    }

    // 更新文件状态，标记为上传失败
    if (file) {
      try {
        // 构建详细的错误信息
        let errorMessage = error.message || '上传到Dify知识库失败';
        if (error.response) {
          errorMessage += ` (状态码: ${error.response.status})`;
          if (error.response.data && error.response.data.error) {
            errorMessage += ` - ${error.response.data.error}`;
          }
        }

        await file.update({
          analysis_status: 'failed',
          analysis_error: errorMessage
        });

        console.log(`【Dify上传失败】已更新文件状态为failed，错误信息: ${errorMessage}`);
      } catch (updateError) {
        console.error(`【Dify上传失败】更新文件状态失败:`, updateError);
      }
    }

    // 尝试清理临时文件
    try {
      if (fileToUpload !== file.path && fs.existsSync(fileToUpload)) {
        fs.unlinkSync(fileToUpload);
        console.log(`【Dify上传失败】已清理临时文件: ${fileToUpload}`);
      }
    } catch (cleanupError) {
      console.error(`【Dify上传失败】清理临时文件失败:`, cleanupError);
    }

    return false;
  }
};

/**
 * 处理Dify回调
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.handleDifyCallback = async (req, res) => {
  try {
    const fileId = req.params.fileId;
    console.log(`收到Dify回调，文件ID: ${fileId}`, req.body);

    // 验证请求体
    if (!req.body || (!req.body.summary && !req.body.detailed_description)) {
      console.error(`Dify回调数据无效，文件ID: ${fileId}`, req.body);
      return res.status(400).json({
        success: false,
        message: '回调数据无效，缺少必要字段'
      });
    }

    // 根据文件ID查询文件
    const file = await File.findByPk(fileId);

    if (!file) {
      console.error(`文件不存在，ID: ${fileId}`);
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    console.log(`找到文件: ${file.id}`);

    // 更新文件摘要和详细描述
    await file.update({
      summary: req.body.summary || '',
      detailed_description: req.body.detailed_description || '',
      analysis_status: 'completed',
      analysis_completed_at: new Date()
    });

    console.log(`文件 ${file.id} 分析结果已更新`);

    res.status(200).json({
      success: true,
      message: '分析结果已更新',
      file_id: file.id
    });
  } catch (error) {
    console.error(`处理Dify回调失败:`, error);
    res.status(500).json({
      success: false,
      message: '处理回调失败',
      error: error.message
    });
  }
};

// 导出 uploadFileToDifyDataset 函数
exports.uploadFileToDifyDataset = uploadFileToDifyDataset;