/**
 * 知识库文件管理组件类型定义
 */

// 知识库类型
export type KnowledgeBaseType = '系统' | '用户';

// 知识库状态
export type KnowledgeBaseStatus = '正常' | '禁用';

// 知识库文件状态
export type KnowledgeFileStatus = '待审核' | '已通过' | '已驳回' | '正常';

// 知识库
export interface KnowledgeBase {
  id: string;
  name: string;
  description: string;
  type: KnowledgeBaseType;
  creator_id: string;
  creator_name: string;
  file_count: number;
  status: KnowledgeBaseStatus;
  created_at: string;
  updated_at: string;
}

// 知识库文件
export interface KnowledgeFile {
  id: string;
  name: string;
  original_name: string;
  knowledge_base_id: string;
  knowledge_base_name: string;
  file_type: string;
  file_size: number;
  file_path: string;
  summary: string;
  detailed_description: string;
  creator_id: string;
  creator_name: string;
  status: KnowledgeFileStatus;
  reviewer_id?: string;
  reviewer_name?: string;
  review_time?: string;
  reject_reason?: string;
  created_at: string;
  updated_at: string;
}

// 知识库表格分页
export interface KnowledgeBasePagination {
  current: number;
  pageSize: number;
  total: number;
}

// 知识库表格筛选
export interface KnowledgeBaseFilter {
  search: string;
  type: string;
  status: string;
}

// 知识库表格排序
export interface KnowledgeBaseSorter {
  field: string;
  order: 'ascend' | 'descend' | null;
}

// 知识库表格参数
export interface KnowledgeBaseTableParams {
  pagination: KnowledgeBasePagination;
  filters: KnowledgeBaseFilter;
  sorter: KnowledgeBaseSorter;
}

// 知识库表格数据
export interface KnowledgeBaseTableData {
  list: KnowledgeBase[];
  pagination: KnowledgeBasePagination;
}

// 知识库文件表格分页
export interface KnowledgeFilePagination {
  current: number;
  pageSize: number;
  total: number;
}

// 知识库文件表格筛选
export interface KnowledgeFileFilter {
  search: string;
  knowledge_base_id: string;
  status: string;
}

// 知识库文件表格排序
export interface KnowledgeFileSorter {
  field: string;
  order: 'ascend' | 'descend' | null;
}

// 知识库文件表格参数
export interface KnowledgeFileTableParams {
  pagination: KnowledgeFilePagination;
  filters: KnowledgeFileFilter;
  sorter: KnowledgeFileSorter;
}

// 知识库文件表格数据
export interface KnowledgeFileTableData {
  list: KnowledgeFile[];
  pagination: KnowledgeFilePagination;
}

// 知识库表格操作
export type KnowledgeBaseAction = 'view' | 'edit' | 'delete' | 'enable' | 'disable';

// 知识库文件表格操作
export type KnowledgeFileAction = 'view' | 'download' | 'approve' | 'reject' | 'delete';

// 知识库表单字段
export interface KnowledgeBaseFormFields {
  id?: string;
  name: string;
  description: string;
  type: KnowledgeBaseType;
  status: KnowledgeBaseStatus;
}

// 知识库文件上传字段
export interface KnowledgeFileUploadFields {
  knowledge_base_id: string;
  file: File;
  summary?: string;
  detailed_description?: string;
}

// 知识库文件审核字段
export interface KnowledgeFileReviewFields {
  status: 'approved' | 'rejected';
  reject_reason?: string;
}

// 知识库表单属性
export interface KnowledgeBaseFormProps {
  loading: boolean;
  knowledgeBase?: KnowledgeBase;
  onSubmit: (values: KnowledgeBaseFormFields) => void;
  onCancel: () => void;
}

// 知识库文件上传属性
export interface KnowledgeFileUploadProps {
  loading: boolean;
  knowledgeBases: KnowledgeBase[];
  onSubmit: (values: KnowledgeFileUploadFields) => void;
  onCancel: () => void;
}

// 知识库文件审核属性
export interface KnowledgeFileReviewProps {
  loading: boolean;
  file: KnowledgeFile;
  onSubmit: (values: KnowledgeFileReviewFields) => void;
  onCancel: () => void;
}

// 知识库文件详情属性
export interface KnowledgeFileDetailProps {
  file: KnowledgeFile;
  onClose: () => void;
  onDownload?: () => void;
}
