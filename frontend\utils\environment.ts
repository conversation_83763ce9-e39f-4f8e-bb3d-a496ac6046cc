/**
 * 环境检测工具
 * 
 * 提供更准确的环境判断方法，区分生产环境、测试环境和开发环境
 */

/**
 * 环境类型枚举
 */
export enum EnvironmentType {
  DEVELOPMENT = 'development',
  TEST = 'test',
  STAGING = 'staging',
  PRODUCTION = 'production',
  UNKNOWN = 'unknown'
}

/**
 * 获取当前环境类型
 * 
 * 综合考虑多种因素，包括：
 * 1. NODE_ENV 环境变量
 * 2. 自定义环境变量 NEXT_PUBLIC_APP_ENV
 * 3. 主机名特征
 * 4. URL 特征
 * 
 * @returns 当前环境类型
 */
export function getEnvironmentType(): EnvironmentType {
  // 1. 首先检查自定义环境变量
  const customEnv = process.env.NEXT_PUBLIC_APP_ENV;
  if (customEnv) {
    if (customEnv.toLowerCase() === 'production') return EnvironmentType.PRODUCTION;
    if (customEnv.toLowerCase() === 'staging') return EnvironmentType.STAGING;
    if (customEnv.toLowerCase() === 'test') return EnvironmentType.TEST;
    if (customEnv.toLowerCase() === 'development') return EnvironmentType.DEVELOPMENT;
  }

  // 2. 检查 NODE_ENV
  if (process.env.NODE_ENV === 'production') {
    // 在生产构建中，需要进一步区分是真正的生产环境还是测试/预发布环境
    
    // 只在客户端环境中检查主机名和URL
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      
      // 3. 检查主机名特征
      if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return EnvironmentType.DEVELOPMENT;
      }
      
      // 4. 检查URL特征 - 可以根据实际情况调整
      if (hostname.includes('test.') || hostname.includes('-test.')) {
        return EnvironmentType.TEST;
      }
      
      if (hostname.includes('staging.') || hostname.includes('-staging.')) {
        return EnvironmentType.STAGING;
      }
      
      // 如果是IP地址格式，可能是测试环境
      if (/^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
        return EnvironmentType.TEST;
      }
      
      // 其他情况视为生产环境
      return EnvironmentType.PRODUCTION;
    }
    
    // 服务器端渲染时，默认视为生产环境
    return EnvironmentType.PRODUCTION;
  }
  
  // 默认视为开发环境
  return EnvironmentType.DEVELOPMENT;
}

/**
 * 检查是否是开发环境
 * @returns 是否是开发环境
 */
export function isDevelopment(): boolean {
  return getEnvironmentType() === EnvironmentType.DEVELOPMENT;
}

/**
 * 检查是否是测试环境
 * @returns 是否是测试环境
 */
export function isTest(): boolean {
  return getEnvironmentType() === EnvironmentType.TEST;
}

/**
 * 检查是否是预发布环境
 * @returns 是否是预发布环境
 */
export function isStaging(): boolean {
  return getEnvironmentType() === EnvironmentType.STAGING;
}

/**
 * 检查是否是生产环境
 * @returns 是否是生产环境
 */
export function isProduction(): boolean {
  return getEnvironmentType() === EnvironmentType.PRODUCTION;
}

/**
 * 检查是否是非生产环境（开发、测试或预发布）
 * @returns 是否是非生产环境
 */
export function isNonProduction(): boolean {
  const env = getEnvironmentType();
  return env === EnvironmentType.DEVELOPMENT || 
         env === EnvironmentType.TEST || 
         env === EnvironmentType.STAGING;
}

/**
 * 获取当前环境的显示名称
 * @returns 环境显示名称
 */
export function getEnvironmentName(): string {
  const env = getEnvironmentType();
  switch (env) {
    case EnvironmentType.DEVELOPMENT:
      return '开发环境';
    case EnvironmentType.TEST:
      return '测试环境';
    case EnvironmentType.STAGING:
      return '预发布环境';
    case EnvironmentType.PRODUCTION:
      return '生产环境';
    default:
      return '未知环境';
  }
}

/**
 * 环境工具对象
 */
export const Environment = {
  getType: getEnvironmentType,
  isDevelopment,
  isTest,
  isStaging,
  isProduction,
  isNonProduction,
  getName: getEnvironmentName,
  Type: EnvironmentType
};

export default Environment;
