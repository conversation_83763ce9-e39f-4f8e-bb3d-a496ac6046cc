/**
 * 数据库初始化脚本
 * 用于在容器首次启动时初始化数据库结构和基础数据
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');
const fs = require('fs');
const path = require('path');

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'db',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'family_platform_user',
  password: process.env.DB_PASSWORD || 'change_this_password',
  database: process.env.DB_NAME || 'family_platform_db'
};

// 初始化管理员账户
const adminUser = {
  username: 'admin',
  password: 'admin123', // 生产环境应使用强密码
  email: '<EMAIL>',
  role: 'admin'
};

// AI助手配置
const aiConfigs = [
  {
    name: 'cai_hesen',
    description: '蔡和森研究助手',
    config: {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      max_tokens: 2000
    }
  },
  {
    name: 'cai_chang',
    description: '蔡畅研究助手',
    config: {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      max_tokens: 2000
    }
  },
  {
    name: 'ge_jianhao',
    description: '葛健豪研究助手',
    config: {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      max_tokens: 2000
    }
  },
  {
    name: 'xiang_jingyu',
    description: '向警予研究助手',
    config: {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      max_tokens: 2000
    }
  },
  {
    name: 'li_fuchun',
    description: '李富春研究助手',
    config: {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      max_tokens: 2000
    }
  },
  {
    name: 'data_query',
    description: '数据查询助手',
    config: {
      model: 'gpt-3.5-turbo',
      temperature: 0.3,
      max_tokens: 2000
    }
  }
];

async function initDatabase() {
  console.log('开始初始化数据库...');
  let connection;

  try {
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 检查用户表是否存在
    const [tables] = await connection.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users'
    `, [dbConfig.database]);

    if (tables.length === 0) {
      console.log('用户表不存在，创建数据库表...');
      
      // 创建用户表
      await connection.query(`
        CREATE TABLE IF NOT EXISTS users (
          id INT NOT NULL AUTO_INCREMENT,
          username VARCHAR(50) NOT NULL,
          password VARCHAR(255) NOT NULL,
          email VARCHAR(100) DEFAULT NULL,
          role ENUM('admin', 'visitor') NOT NULL DEFAULT 'visitor',
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY username (username),
          UNIQUE KEY email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      // 创建文件表
      await connection.query(`
        CREATE TABLE IF NOT EXISTS files (
          id INT NOT NULL AUTO_INCREMENT,
          filename VARCHAR(255) NOT NULL,
          original_filename VARCHAR(255) NOT NULL,
          file_path VARCHAR(255) NOT NULL,
          file_type VARCHAR(50) NOT NULL,
          file_size INT NOT NULL,
          status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
          uploaded_by INT NOT NULL,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          KEY uploaded_by (uploaded_by),
          CONSTRAINT files_ibfk_1 FOREIGN KEY (uploaded_by) REFERENCES users (id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      // 创建AI配置表
      await connection.query(`
        CREATE TABLE IF NOT EXISTS ai_configs (
          id INT NOT NULL AUTO_INCREMENT,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          config JSON NOT NULL,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY name (name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      // 创建活动表
      await connection.query(`
        CREATE TABLE IF NOT EXISTS events (
          id INT NOT NULL AUTO_INCREMENT,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          event_date DATETIME DEFAULT NULL,
          created_by INT NOT NULL,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          KEY created_by (created_by),
          CONSTRAINT events_ibfk_1 FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      // 创建活动附件表
      await connection.query(`
        CREATE TABLE IF NOT EXISTS event_attachments (
          id INT NOT NULL AUTO_INCREMENT,
          event_id INT NOT NULL,
          file_id INT NOT NULL,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          KEY event_id (event_id),
          KEY file_id (file_id),
          CONSTRAINT event_attachments_ibfk_1 FOREIGN KEY (event_id) REFERENCES events (id) ON DELETE CASCADE,
          CONSTRAINT event_attachments_ibfk_2 FOREIGN KEY (file_id) REFERENCES files (id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      // 创建留言表
      await connection.query(`
        CREATE TABLE IF NOT EXISTS comments (
          id INT NOT NULL AUTO_INCREMENT,
          content TEXT NOT NULL,
          user_id INT NOT NULL,
          page_type ENUM('personal', 'family') NOT NULL,
          page_id VARCHAR(50) NOT NULL,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          KEY user_id (user_id),
          CONSTRAINT comments_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      console.log('数据库表创建完成');
    } else {
      console.log('数据库表已存在，跳过创建步骤');
    }

    // 检查是否已有管理员账户
    const [adminUsers] = await connection.query('SELECT * FROM users WHERE role = ?', ['admin']);
    
    if (adminUsers.length === 0) {
      console.log('创建管理员账户...');
      
      // 加密密码
      const hashedPassword = await bcrypt.hash(adminUser.password, 10);
      
      // 插入管理员账户
      await connection.query(
        'INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)',
        [adminUser.username, hashedPassword, adminUser.email, adminUser.role]
      );
      
      console.log('管理员账户创建成功');
    } else {
      console.log('管理员账户已存在，跳过创建步骤');
    }

    // 检查AI配置
    const [existingConfigs] = await connection.query('SELECT name FROM ai_configs');
    const existingConfigNames = existingConfigs.map(config => config.name);
    
    for (const config of aiConfigs) {
      if (!existingConfigNames.includes(config.name)) {
        console.log(`创建AI配置: ${config.name}`);
        await connection.query(
          'INSERT INTO ai_configs (name, description, config) VALUES (?, ?, ?)',
          [config.name, config.description, JSON.stringify(config.config)]
        );
      }
    }
    
    console.log('AI配置检查/创建完成');

    // 创建必要的目录
    const directories = [
      path.join(__dirname, '..', 'uploads'),
      path.join(__dirname, '..', 'logs')
    ];
    
    for (const dir of directories) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`创建目录: ${dir}`);
      }
    }

    console.log('数据库初始化完成');
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行初始化
initDatabase();
