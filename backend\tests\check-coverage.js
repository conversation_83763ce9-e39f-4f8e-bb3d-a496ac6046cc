/**
 * 测试覆盖率检查脚本
 * 
 * 用于检查测试覆盖率是否达到目标
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 目标覆盖率
const TARGET_COVERAGE = {
  lines: 90,
  functions: 90,
  branches: 80,
  statements: 90
};

// 覆盖率报告路径
const COVERAGE_SUMMARY_PATH = path.join(__dirname, '..', 'coverage', 'coverage-summary.json');

// 运行测试并生成覆盖率报告
console.log('运行测试并生成覆盖率报告...');
try {
  execSync('npx jest --coverage', { stdio: 'inherit' });
} catch (error) {
  console.error('测试失败:', error.message);
  process.exit(1);
}

// 检查覆盖率报告是否存在
if (!fs.existsSync(COVERAGE_SUMMARY_PATH)) {
  console.error('覆盖率报告不存在');
  process.exit(1);
}

// 读取覆盖率报告
const coverageSummary = JSON.parse(fs.readFileSync(COVERAGE_SUMMARY_PATH, 'utf8'));
const total = coverageSummary.total;

// 打印覆盖率摘要
console.log('\n覆盖率摘要:');
console.log(`行覆盖率: ${total.lines.pct.toFixed(2)}% (目标: ${TARGET_COVERAGE.lines}%)`);
console.log(`函数覆盖率: ${total.functions.pct.toFixed(2)}% (目标: ${TARGET_COVERAGE.functions}%)`);
console.log(`分支覆盖率: ${total.branches.pct.toFixed(2)}% (目标: ${TARGET_COVERAGE.branches}%)`);
console.log(`语句覆盖率: ${total.statements.pct.toFixed(2)}% (目标: ${TARGET_COVERAGE.statements}%)`);

// 检查是否达到目标覆盖率
const isCoverageMet = 
  total.lines.pct >= TARGET_COVERAGE.lines &&
  total.functions.pct >= TARGET_COVERAGE.functions &&
  total.branches.pct >= TARGET_COVERAGE.branches &&
  total.statements.pct >= TARGET_COVERAGE.statements;

if (isCoverageMet) {
  console.log('\n✅ 所有覆盖率目标已达成!');
  process.exit(0);
} else {
  console.error('\n❌ 未达到覆盖率目标!');
  
  // 检查哪些指标未达标
  if (total.lines.pct < TARGET_COVERAGE.lines) {
    console.error(`- 行覆盖率未达标: ${total.lines.pct.toFixed(2)}% < ${TARGET_COVERAGE.lines}%`);
  }
  if (total.functions.pct < TARGET_COVERAGE.functions) {
    console.error(`- 函数覆盖率未达标: ${total.functions.pct.toFixed(2)}% < ${TARGET_COVERAGE.functions}%`);
  }
  if (total.branches.pct < TARGET_COVERAGE.branches) {
    console.error(`- 分支覆盖率未达标: ${total.branches.pct.toFixed(2)}% < ${TARGET_COVERAGE.branches}%`);
  }
  if (total.statements.pct < TARGET_COVERAGE.statements) {
    console.error(`- 语句覆盖率未达标: ${total.statements.pct.toFixed(2)}% < ${TARGET_COVERAGE.statements}%`);
  }
  
  // 找出覆盖率最低的文件
  console.log('\n覆盖率最低的文件:');
  const files = Object.keys(coverageSummary).filter(key => key !== 'total');
  
  // 按行覆盖率排序
  const sortedByLines = files.sort((a, b) => 
    coverageSummary[a].lines.pct - coverageSummary[b].lines.pct
  ).slice(0, 5);
  
  console.log('\n行覆盖率最低的文件:');
  sortedByLines.forEach(file => {
    console.log(`- ${file}: ${coverageSummary[file].lines.pct.toFixed(2)}%`);
  });
  
  // 按函数覆盖率排序
  const sortedByFunctions = files.sort((a, b) => 
    coverageSummary[a].functions.pct - coverageSummary[b].functions.pct
  ).slice(0, 5);
  
  console.log('\n函数覆盖率最低的文件:');
  sortedByFunctions.forEach(file => {
    console.log(`- ${file}: ${coverageSummary[file].functions.pct.toFixed(2)}%`);
  });
  
  // 按分支覆盖率排序
  const sortedByBranches = files.sort((a, b) => 
    coverageSummary[a].branches.pct - coverageSummary[b].branches.pct
  ).slice(0, 5);
  
  console.log('\n分支覆盖率最低的文件:');
  sortedByBranches.forEach(file => {
    console.log(`- ${file}: ${coverageSummary[file].branches.pct.toFixed(2)}%`);
  });
  
  console.log('\n请增加测试用例以提高覆盖率');
  process.exit(1);
}
