"use client"

import type React from "react"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Check, AlertCircle } from "lucide-react"

export function PasswordChange() {
  const [passwords, setPasswords] = useState({
    current: "",
    new: "",
    confirm: "",
  })

  const [errors, setErrors] = useState({
    current: "",
    new: "",
    confirm: "",
  })

  const [showSuccessToast, setShowSuccessToast] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState(0)
  const [passwordFeedback, setPasswordFeedback] = useState("")

  // 系统密码要求
  const passwordRequirements = {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
  }

  const handleChange = (field: string, value: string) => {
    setPasswords((prev) => ({
      ...prev,
      [field]: value,
    }))

    // 清除错误
    setErrors((prev) => ({
      ...prev,
      [field]: "",
    }))

    // 如果是新密码，检查密码强度
    if (field === "new") {
      checkPasswordStrength(value)
    }

    // 如果是确认密码，检查是否匹配
    if (field === "confirm") {
      if (value !== passwords.new) {
        setErrors((prev) => ({
          ...prev,
          confirm: "两次输入的密码不一致",
        }))
      }
    }
  }

  const checkPasswordStrength = (password: string) => {
    let strength = 0
    let feedback = ""

    // 检查长度
    if (password.length >= passwordRequirements.minLength) {
      strength += 1
    } else {
      feedback = `密码长度至少为${passwordRequirements.minLength}位`
      setPasswordFeedback(feedback)
      setPasswordStrength(strength)
      return
    }

    // 检查大写字母
    if (passwordRequirements.requireUppercase && /[A-Z]/.test(password)) {
      strength += 1
    } else if (passwordRequirements.requireUppercase) {
      feedback = "密码需要包含大写字母"
      setPasswordFeedback(feedback)
      setPasswordStrength(strength)
      return
    }

    // 检查小写字母
    if (passwordRequirements.requireLowercase && /[a-z]/.test(password)) {
      strength += 1
    } else if (passwordRequirements.requireLowercase) {
      feedback = "密码需要包含小写字母"
      setPasswordFeedback(feedback)
      setPasswordStrength(strength)
      return
    }

    // 检查数字
    if (passwordRequirements.requireNumbers && /\d/.test(password)) {
      strength += 1
    } else if (passwordRequirements.requireNumbers) {
      feedback = "密码需要包含数字"
      setPasswordFeedback(feedback)
      setPasswordStrength(strength)
      return
    }

    // 检查特殊字符
    if (passwordRequirements.requireSpecialChars && /[^A-Za-z0-9]/.test(password)) {
      strength += 1
    } else if (passwordRequirements.requireSpecialChars) {
      feedback = "密码需要包含特殊字符"
      setPasswordFeedback(feedback)
      setPasswordStrength(strength)
      return
    }

    // 设置反馈
    if (strength < 3) {
      feedback = "密码强度较弱"
    } else if (strength < 5) {
      feedback = "密码强度中等"
    } else {
      feedback = "密码强度很强"
    }

    setPasswordStrength(strength)
    setPasswordFeedback(feedback)
  }

  const validateForm = () => {
    let isValid = true
    const newErrors = { current: "", new: "", confirm: "" }

    // 验证当前密码
    if (!passwords.current) {
      newErrors.current = "请输入当前密码"
      isValid = false
    }

    // 验证新密码
    if (!passwords.new) {
      newErrors.new = "请输入新密码"
      isValid = false
    } else if (passwords.new.length < passwordRequirements.minLength) {
      newErrors.new = `密码长度至少为${passwordRequirements.minLength}位`
      isValid = false
    } else if (passwordRequirements.requireUppercase && !/[A-Z]/.test(passwords.new)) {
      newErrors.new = "密码需要包含大写字母"
      isValid = false
    } else if (passwordRequirements.requireLowercase && !/[a-z]/.test(passwords.new)) {
      newErrors.new = "密码需要包含小写字母"
      isValid = false
    } else if (passwordRequirements.requireNumbers && !/\d/.test(passwords.new)) {
      newErrors.new = "密码需要包含数字"
      isValid = false
    } else if (passwordRequirements.requireSpecialChars && !/[^A-Za-z0-9]/.test(passwords.new)) {
      newErrors.new = "密码需要包含特殊字符"
      isValid = false
    }

    // 验证确认密码
    if (!passwords.confirm) {
      newErrors.confirm = "请确认新密码"
      isValid = false
    } else if (passwords.confirm !== passwords.new) {
      newErrors.confirm = "两次输入的密码不一致"
      isValid = false
    }

    setErrors(newErrors)
    return isValid
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    // 模拟密码修改成功
    setTimeout(() => {
      setPasswords({
        current: "",
        new: "",
        confirm: "",
      })
      setPasswordStrength(0)
      setPasswordFeedback("")
      setShowSuccessToast(true)

      setTimeout(() => {
        setShowSuccessToast(false)
      }, 3000)
    }, 500)
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-bold">修改密码</h2>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="space-y-6 max-w-md">
          <div className="space-y-2">
            <Label htmlFor="current-password">当前密码</Label>
            <Input
              id="current-password"
              type="password"
              value={passwords.current}
              onChange={(e) => handleChange("current", e.target.value)}
            />
            {errors.current && <p className="text-red-500 text-sm mt-1">{errors.current}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="new-password">新密码</Label>
            <Input
              id="new-password"
              type="password"
              value={passwords.new}
              onChange={(e) => handleChange("new", e.target.value)}
            />
            {errors.new && <p className="text-red-500 text-sm mt-1">{errors.new}</p>}
            {passwords.new && (
              <div className="mt-2">
                <div className="flex items-center mb-1">
                  <div className="h-2 flex-1 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${
                        passwordStrength < 3 ? "bg-red-500" : passwordStrength < 5 ? "bg-yellow-500" : "bg-green-500"
                      }`}
                      style={{ width: `${(passwordStrength / 5) * 100}%` }}
                    ></div>
                  </div>
                  <span className="ml-2 text-sm text-gray-500">{passwordFeedback}</span>
                </div>
                <div className="text-xs text-gray-500">
                  密码要求：至少{passwordRequirements.minLength}位
                  {passwordRequirements.requireUppercase && "，包含大写字母"}
                  {passwordRequirements.requireLowercase && "，包含小写字母"}
                  {passwordRequirements.requireNumbers && "，包含数字"}
                  {passwordRequirements.requireSpecialChars && "，包含特殊字符"}
                </div>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirm-password">确认新密码</Label>
            <Input
              id="confirm-password"
              type="password"
              value={passwords.confirm}
              onChange={(e) => handleChange("confirm", e.target.value)}
            />
            {errors.confirm && <p className="text-red-500 text-sm mt-1">{errors.confirm}</p>}
          </div>

          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">修改密码后，系统将自动退出登录，请使用新密码重新登录。</p>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button type="submit" className="bg-[#f5a623] hover:bg-[#f5a623]/90">
              修改密码
            </Button>
          </div>
        </div>
      </form>

      {/* 成功提示框 */}
      {showSuccessToast && (
        <div className="fixed bottom-8 right-8 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md z-50 animate-fade-in-up">
          <div className="flex items-center">
            <Check className="h-5 w-5 mr-2" />
            <p>密码已成功修改！请使用新密码重新登录。</p>
          </div>
        </div>
      )}
    </div>
  )
}
