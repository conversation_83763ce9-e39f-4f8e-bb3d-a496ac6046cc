const db = require('./src/models');

console.log('开始更新知识库文件分析助手...');

(async () => {
  try {
    console.log('查找知识库文件分析助手...');
    // 查找知识库文件分析助手
    const assistant = await db.AIAssistant.findOne({
      where: { type: 'knowledge-file' }
    });

    if (!assistant) {
      console.error('未找到知识库文件分析助手，无法更新');
      process.exit(1);
      return;
    }

    console.log('找到知识库文件分析助手，ID:', assistant.id);
    console.log('当前配置:', JSON.stringify(assistant, null, 2));

    // 更新知识库文件分析助手
    await assistant.update({
      name: '知识库文件分析助手',
      description: '用于分析上传到知识库的文件，支持系统知识库和用户知识库',
      api_key: 'dataset-DLFJlUe25VUHOwMO4HbO4hQk',
      api_endpoint: 'https://ai.glab.vip',
      app_id: '77199451-730a-4d79-a1c9-9b9e6bfcd747', // 系统知识库数据集ID
      app_code: '602d59cf-3384-4105-bf91-e1481b30b6b2', // 用户知识库数据集ID
      upload_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
      analysis_api_path: '/v1/datasets/{datasetId}/document/create-by-file',
      tags: '知识库,文件分析,Dify',
      initial_message: '我是知识库文件分析助手，可以帮助您分析上传的文件。',
      is_system: true,
      status: 'active'
    });

    console.log('知识库文件分析助手更新成功');
    
    // 重新获取更新后的助手
    const updatedAssistant = await db.AIAssistant.findByPk(assistant.id);
    console.log('更新后的配置:', JSON.stringify(updatedAssistant, null, 2));
  } catch (e) {
    console.error('更新知识库文件分析助手失败:', e);
  } finally {
    process.exit();
  }
})();
