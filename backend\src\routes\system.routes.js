/**
 * 系统配置相关路由
 * 
 * 处理系统配置的查询、更新等请求
 */

const express = require('express');
const router = express.Router();
const systemController = require('../controllers/system.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');

// 获取系统配置 (需要认证和权限)
router.get('/config', 
  authMiddleware, 
  checkPermission('system:access'),
  systemController.getSystemConfig
);

// 更新系统配置 (需要认证和权限)
router.put('/config', 
  authMiddleware, 
  checkPermission('security:manage'),
  systemController.updateSystemConfig
);

// 获取系统统计信息 (需要认证和权限)
router.get('/stats', 
  authMiddleware, 
  checkPermission('system:access'),
  systemController.getSystemStats
);

module.exports = router;
