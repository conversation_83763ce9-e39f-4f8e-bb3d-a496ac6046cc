/**
 * 权限中间件
 *
 * 处理权限检查相关的中间件函数
 */

const { User, Role, Permission } = require('../models');

/**
 * 检查用户是否有指定权限
 * @param {string|string[]} permissionNames 权限名称或权限名称数组
 * @returns {Function} 中间件函数
 */
const permissionMiddleware = (resource, action) => {
  // 支持多种参数格式
  let permissionNames = [];

  if (Array.isArray(resource)) {
    // 如果第一个参数是数组，则视为权限名称数组
    permissionNames = resource;
  } else if (typeof resource === 'string' && typeof action === 'string') {
    // 如果两个参数都是字符串，则组合成权限名称
    permissionNames = [`${resource}:${action}`];
  } else if (typeof resource === 'string' && action === undefined) {
    // 如果只有一个字符串参数，则视为完整的权限名称
    permissionNames = [resource];
  }

  return async (req, res, next) => {
    try {
      // 确保用户已认证
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '未授权，请先登录'
        });
      }

      // 获取用户角色
      const role = await Role.findOne({
        where: { id: req.user.role_id },
        include: [{
          model: Permission,
          as: 'permissions'
        }]
      });

      if (!role) {
        return res.status(403).json({
          success: false,
          message: '角色不存在'
        });
      }

      // 检查用户是否是管理员
      if (role.name === 'admin' || req.user.role === 'admin') {
        // 管理员拥有所有权限
        return next();
      }

      // 检查用户是否有指定权限
      const permissions = role.permissions || [];
      const permissionCodes = permissions.map(p => p.code);

      // 检查是否拥有所有需要的权限
      const hasAllPermissions = permissionNames.every(name =>
        permissionCodes.includes(name)
      );

      if (hasAllPermissions) {
        return next();
      }

      // 用户没有权限
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    } catch (error) {
      console.error('权限检查错误:', error);

      // 处理特定错误
      if (error.message && error.message.includes('WHERE parameter "id" has invalid')) {
        return res.status(403).json({
          success: false,
          message: '角色不存在'
        });
      }

      return res.status(500).json({
        success: false,
        message: '服务器错误'
      });
    }
  };
};

/**
 * 检查用户是否是管理员
 * @returns {Function} 中间件函数
 */
const isAdmin = async (req, res, next) => {
  try {
    // 确保用户已认证
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未授权，请先登录'
      });
    }

    // 获取用户角色
    const user = await User.findByPk(req.user.id, {
      include: [{
        model: Role,
        as: 'userRole'
      }]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查用户是否是管理员
    if (user.userRole && user.userRole.name === 'admin') {
      return next();
    }

    // 用户不是管理员
    return res.status(403).json({
      success: false,
      message: '需要管理员权限'
    });
  } catch (error) {
    console.error('管理员检查错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

/**
 * 检查用户是否是资源所有者
 * @param {Function} getResourceOwnerId 获取资源所有者ID的函数
 * @returns {Function} 中间件函数
 */
const isResourceOwner = (getResourceOwnerId) => {
  return async (req, res, next) => {
    try {
      // 确保用户已认证
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '未授权，请先登录'
        });
      }

      // 获取资源所有者ID
      const ownerId = await getResourceOwnerId(req);

      // 检查用户是否是资源所有者
      if (req.user.id === ownerId) {
        return next();
      }

      // 检查用户是否是管理员
      const user = await User.findByPk(req.user.id, {
        include: [{
          model: Role,
          as: 'userRole'
        }]
      });

      if (user && user.userRole && user.userRole.name === 'admin') {
        return next();
      }

      // 用户不是资源所有者也不是管理员
      return res.status(403).json({
        success: false,
        message: '没有权限访问此资源'
      });
    } catch (error) {
      console.error('资源所有者检查错误:', error);
      return res.status(500).json({
        success: false,
        message: '服务器错误'
      });
    }
  };
};

module.exports = permissionMiddleware;

// 为了向后兼容，保留原有导出
module.exports.isAdmin = isAdmin;
module.exports.isResourceOwner = isResourceOwner;
