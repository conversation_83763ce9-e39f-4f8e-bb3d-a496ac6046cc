/**
 * 知识库文件详情组件
 *
 * 显示知识库文件的详细信息，包括基本信息、摘要、详细描述和驳回原因（如果有）
 */

import React from 'react'
import { KnowledgeFile, KnowledgeFileDetailProps } from './types'

/**
 * 知识库文件详情组件
 * @param file 文件对象
 * @param onClose 关闭回调函数
 */
export const KnowledgeFileDetail: React.FC<KnowledgeFileDetailProps> = ({ file, onClose }) => {
  return (
    <div>
      <div className="flex flex-wrap items-center text-sm text-gray-500 mb-4">
        <span className="mr-4 mb-1">知识库: {file.knowledge_base_name}</span>
        <span className="mr-4 mb-1">上传时间: {file.created_at}</span>
        <span className="mr-4 mb-1">上传者: {file.creator_name}</span>
        <span className="mr-4 mb-1">文件大小: {file.file_size}</span>
        <span className="mb-1">文件类型: {file.file_type}</span>
      </div>

      {/* 移除概述和详细描述部分 */}

      {file.status === "已驳回" && file.reject_reason && (
        <div className="bg-red-50 p-4 rounded-md mb-4 border border-red-200">
          <h3 className="font-bold text-lg mb-2 text-red-800">驳回原因</h3>
          <p className="text-red-700">{file.reject_reason}</p>
        </div>
      )}
    </div>
  )
}

export default KnowledgeFileDetail
