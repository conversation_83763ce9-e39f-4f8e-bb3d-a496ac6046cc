/**
 * 测试辅助工具
 *
 * 提供测试中常用的辅助函数
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { User, Role, Permission, KnowledgeBase, File, Activity, Comment, Notification } = require('../../src/models');

/**
 * 创建测试用户
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} - 创建的用户对象
 */
const createTestUser = async (userData = {}) => {
  // 生成随机后缀，避免数据冲突
  const randomSuffix = Math.floor(Math.random() * 10000);

  const defaultUserData = {
    username: `testuser_${randomSuffix}`,
    email: `testuser_${randomSuffix}@example.com`,
    password: await bcrypt.hash('password123', 10),
    phone: `138${randomSuffix.toString().padStart(8, '0')}`,
    is_active: true,
    role: 'basic_user',
    role_id: 1
  };

  return await User.create({
    ...defaultUserData,
    ...userData
  }, { transaction: global.testTransaction });
};

/**
 * 创建测试管理员
 * @param {Object} adminData - 管理员数据
 * @returns {Promise<Object>} - 创建的管理员对象
 */
const createTestAdmin = async (adminData = {}) => {
  // 生成随机后缀，避免数据冲突
  const randomSuffix = Math.floor(Math.random() * 10000);

  const defaultAdminData = {
    username: `testadmin_${randomSuffix}`,
    email: `testadmin_${randomSuffix}@example.com`,
    password: await bcrypt.hash('admin123', 10),
    phone: `139${randomSuffix.toString().padStart(8, '0')}`,
    is_active: true,
    role: 'admin',
    role_id: 2 // 假设2是管理员角色ID
  };

  return await User.create({
    ...defaultAdminData,
    ...adminData
  }, { transaction: global.testTransaction });
};

/**
 * 创建测试角色
 * @param {Object} roleData - 角色数据
 * @returns {Promise<Object>} - 创建的角色对象
 */
const createTestRole = async (roleData = {}) => {
  // 生成随机后缀，避免数据冲突
  const randomSuffix = Math.floor(Math.random() * 10000);

  const defaultRoleData = {
    name: `testrole_${randomSuffix}`,
    description: `Test role ${randomSuffix} for testing`
  };

  return await Role.create({
    ...defaultRoleData,
    ...roleData
  }, { transaction: global.testTransaction });
};

/**
 * 创建测试权限
 * @param {Object} permissionData - 权限数据
 * @returns {Promise<Object>} - 创建的权限对象
 */
const createTestPermission = async (permissionData = {}) => {
  // 生成随机后缀，避免数据冲突
  const randomSuffix = Math.floor(Math.random() * 10000);

  const defaultPermissionData = {
    name: `test:permission:${randomSuffix}`,
    description: `Test permission ${randomSuffix} for testing`,
    module: 'test',
    code: `test:permission:${randomSuffix}`
  };

  try {
    return await Permission.create({
      ...defaultPermissionData,
      ...permissionData
    });
  } catch (error) {
    console.error('创建测试权限失败:', error);
    throw error;
  }
};

/**
 * 创建测试知识库
 * @param {Object} knowledgeBaseData - 知识库数据
 * @returns {Promise<Object>} - 创建的知识库对象
 */
const createTestKnowledgeBase = async (knowledgeBaseData = {}) => {
  // 生成随机后缀，避免数据冲突
  const randomSuffix = Math.floor(Math.random() * 10000);

  const defaultKnowledgeBaseData = {
    name: `Test Knowledge Base ${randomSuffix}`,
    description: `Test knowledge base ${randomSuffix} for testing`,
    type: 'user',
    creator_id: 1
  };

  return await KnowledgeBase.create({
    ...defaultKnowledgeBaseData,
    ...knowledgeBaseData
  }, { transaction: global.testTransaction });
};

/**
 * 创建测试文件
 * @param {Object} fileData - 文件数据
 * @returns {Promise<Object>} - 创建的文件对象
 */
const createTestFile = async (fileData = {}) => {
  // 生成随机后缀，避免数据冲突
  const randomSuffix = Math.floor(Math.random() * 10000);

  const defaultFileData = {
    name: `test-file-${randomSuffix}.pdf`,
    original_name: `test-file-${randomSuffix}.pdf`,
    path: `/uploads/test-file-${randomSuffix}.pdf`,
    size: 1024,
    type: 'pdf',
    mime_type: 'application/pdf',
    knowledge_base_id: 1,
    uploader_id: 1,
    status: 'pending'
  };

  return await File.create({
    ...defaultFileData,
    ...fileData
  }, { transaction: global.testTransaction });
};

/**
 * 创建测试活动
 * @param {Object} activityData - 活动数据
 * @returns {Promise<Object>} - 创建的活动对象
 */
const createTestActivity = async (activityData = {}) => {
  // 生成随机后缀，避免数据冲突
  const randomSuffix = Math.floor(Math.random() * 10000);

  const defaultActivityData = {
    title: `Test Activity ${randomSuffix}`,
    description: `Test activity ${randomSuffix} for testing`,
    date: new Date(),
    status: 'published',
    creator_id: 1
  };

  return await Activity.create({
    ...defaultActivityData,
    ...activityData
  }, { transaction: global.testTransaction });
};

/**
 * 创建测试评论
 * @param {Object} commentData - 评论数据
 * @returns {Promise<Object>} - 创建的评论对象
 */
const createTestComment = async (commentData = {}) => {
  // 生成随机后缀，避免数据冲突
  const randomSuffix = Math.floor(Math.random() * 10000);

  const defaultCommentData = {
    content: `Test comment ${randomSuffix}`,
    topic_type: 'personal',
    topic_id: 1,
    user_id: 1,
    status: 'pending'
  };

  return await Comment.create({
    ...defaultCommentData,
    ...commentData
  }, { transaction: global.testTransaction });
};

/**
 * 创建测试通知
 * @param {Object} notificationData - 通知数据
 * @returns {Promise<Object>} - 创建的通知对象
 */
const createTestNotification = async (notificationData = {}) => {
  // 生成随机后缀，避免数据冲突
  const randomSuffix = Math.floor(Math.random() * 10000);

  const defaultNotificationData = {
    title: `Test Notification ${randomSuffix}`,
    content: `Test notification content ${randomSuffix}`,
    type: 'system',
    user_id: 1,
    is_read: false
  };

  return await Notification.create({
    ...defaultNotificationData,
    ...notificationData
  }, { transaction: global.testTransaction });
};

/**
 * 生成测试JWT令牌
 * @param {Object} user - 用户对象
 * @returns {string} - JWT令牌
 */
const generateTestToken = (user) => {
  return jwt.sign(
    { id: user.id, username: user.username, role_id: user.role_id, role: user.role },
    process.env.JWT_SECRET || 'test-secret',
    { expiresIn: '1h' }
  );
};

module.exports = {
  createTestUser,
  createTestAdmin,
  createTestRole,
  createTestPermission,
  createTestKnowledgeBase,
  createTestFile,
  createTestActivity,
  createTestComment,
  createTestNotification,
  generateTestToken
};
