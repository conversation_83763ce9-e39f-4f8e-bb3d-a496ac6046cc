# 和富家族研究平台前端

这是和富家族研究平台的前端应用，基于Next.js开发。

## 技术栈

- Next.js
- React
- Tailwind CSS
- TypeScript

## 目录结构

```
frontend/
├── app/                # Next.js 页面和路由
├── components/         # React 组件
├── contexts/           # React 上下文
├── hooks/              # 自定义 React 钩子
├── lib/                # 工具函数和库
├── public/             # 静态资源
├── styles/             # CSS 样式
├── types/              # TypeScript 类型定义
├── package.json        # 项目依赖
└── README.md           # 项目文档
```

## 安装与运行

1. 安装依赖：
   ```
   npm install
   ```

2. 启动开发服务器：
   ```
   npm run dev
   ```

3. 构建生产版本：
   ```
   npm run build
   ```

4. 启动生产服务器：
   ```
   npm start
   ```

## 页面说明

- 首页 (/)：系统入口页面
- 家族专题 (/family-topic)：展示家族历史概述和重要事件时间线
- 个人专题 (/personal/[slug])：展示家族成员的详细个人资料
- 知识库 (/knowledge)：知识管理中心
- 数据查询 (/data-mining)：数据检索和分析中心
- AI研究助手 (/ai-assistant)：提供多种专业的智能助手
- 系统管理 (/system-management)：管理员和拥有系统管理权限的用户可访问的页面
- 个人中心 (/personal-center)：用户管理个人资料和账号设置的中心

## 许可证

[MIT](LICENSE)
