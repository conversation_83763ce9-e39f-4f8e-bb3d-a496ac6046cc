# 和富家族研究平台Docker部署文档

本文档详细说明如何使用Docker将和富家族研究平台部署到CentOS Stream 9服务器上。

## 一、环境要求

- 服务器：CentOS Stream 9
- IP地址：**********
- 已安装Docker和Docker Compose
- 开放端口：3000(前端)、5001(后端)、3306(MySQL)

## 二、部署准备

### 1. 安装Docker和Docker Compose（如未安装）

```bash
# 安装Docker
sudo dnf install -y dnf-plugins-core
sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo dnf install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.18.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 创建项目目录

```bash
mkdir -p /opt/family-research-platform
cd /opt/family-research-platform
```

### 3. 下载项目代码

将项目代码复制到服务器，或从版本控制系统克隆：

```bash
# 如果使用Git
git clone <项目仓库地址> .
```

## 三、Docker配置文件说明

项目包含以下Docker配置文件：

1. `docker-compose.yml` - 定义服务组合
2. `Dockerfile.frontend` - 前端服务构建配置
3. `Dockerfile.backend` - 后端服务构建配置
4. `.env` - 环境变量配置
5. `nginx.conf` - Nginx配置文件

## 四、部署步骤

### 1. 配置环境变量

编辑`.env`文件，设置必要的环境变量：

```bash
# 使用示例配置或根据实际情况修改
cp .env.example .env
vi .env
```

确保以下关键配置正确：
- 数据库连接信息
- Dify API密钥和数据集ID
- 服务器IP地址和端口

### 2. 构建和启动容器

```bash
# 在项目根目录执行
docker-compose up -d
```

此命令将：
- 构建前端和后端镜像
- 创建并启动MySQL数据库容器
- 创建并启动前端和后端服务容器
- 设置容器间的网络连接

### 3. 初始化数据库

```bash
# 进入后端容器
docker exec -it family-platform-backend bash

# 执行数据库初始化脚本
node scripts/init-db.js

# 退出容器
exit
```

### 4. 验证部署

访问以下URL验证服务是否正常运行：

- 前端：http://**********:3000
- 后端API：http://**********:5001/api/health

## 五、维护操作

### 1. 查看容器状态

```bash
docker-compose ps
```

### 2. 查看服务日志

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs frontend
docker-compose logs backend
docker-compose logs db
```

### 3. 重启服务

```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart frontend
docker-compose restart backend
```

### 4. 更新部署

当代码有更新时，执行以下步骤更新部署：

```bash
# 拉取最新代码
git pull

# 重新构建并启动容器
docker-compose down
docker-compose build
docker-compose up -d
```

### 5. 备份数据

```bash
# 备份MySQL数据
docker exec family-platform-db mysqldump -u root -p<密码> --all-databases > backup_$(date +%Y%m%d).sql
```

## 六、故障排除

1. **容器无法启动**
   - 检查日志：`docker-compose logs`
   - 确认端口未被占用：`netstat -tulpn | grep <端口号>`

2. **无法连接到服务**
   - 检查防火墙设置：`sudo firewall-cmd --list-all`
   - 确认服务器IP配置正确

3. **数据库连接问题**
   - 检查数据库容器状态：`docker ps | grep db`
   - 验证数据库连接参数：`docker exec -it family-platform-db mysql -u root -p`

## 七、安全建议

1. 修改默认数据库密码
2. 配置HTTPS
3. 设置适当的防火墙规则
4. 定期更新Docker镜像和系统补丁
5. 实施定期备份策略

## 八、联系支持

如遇到部署问题，请联系技术支持团队：
- 邮箱：<EMAIL>
- 电话：XXX-XXXX-XXXX
