'use strict';

/**
 * 更新知识库文件分析助手数据
 * 
 * 1. 将现有的knowledge-file类型助手复制为system-knowledge-file和user-knowledge-file类型
 * 2. 更新字段描述和名称
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 查找现有的knowledge-file类型助手
    const [assistants] = await queryInterface.sequelize.query(
      `SELECT * FROM ai_assistants WHERE type = 'knowledge-file' AND status = 'active' LIMIT 1`
    );

    if (assistants.length === 0) {
      console.log('未找到知识库文件分析助手，跳过迁移');
      return Promise.resolve();
    }

    const assistant = assistants[0];
    console.log('找到知识库文件分析助手:', assistant.id);

    // 更新现有助手名称和描述
    await queryInterface.sequelize.query(
      `UPDATE ai_assistants 
       SET name = '知识库文件同步配置', 
           description = '用于将上传的文件同步到AI平台知识库，支持系统知识库和用户知识库' 
       WHERE id = ?`,
      {
        replacements: [assistant.id]
      }
    );

    // 检查是否已存在system-knowledge-file类型助手
    const [systemAssistants] = await queryInterface.sequelize.query(
      `SELECT * FROM ai_assistants WHERE type = 'system-knowledge-file' LIMIT 1`
    );

    if (systemAssistants.length === 0) {
      // 创建系统知识库文件助手
      await queryInterface.sequelize.query(
        `INSERT INTO ai_assistants (
          name, type, description, api_key, api_endpoint, app_id, app_code, 
          upload_api_path, analysis_api_path, tags, initial_message, 
          is_system, status, creator_id, last_updated_by, created_at, updated_at
        ) VALUES (
          '系统知识库文件助手', 'system-knowledge-file', '用于将上传的文件同步到系统知识库', 
          ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )`,
        {
          replacements: [
            assistant.api_key,
            assistant.api_endpoint,
            assistant.app_id,
            assistant.app_id, // 系统知识库助手的app_code与app_id相同
            assistant.upload_api_path,
            assistant.analysis_api_path,
            '系统知识库,文件同步,AI平台',
            '我是系统知识库文件助手，可以帮助您将文件同步到系统知识库。',
            1, // is_system
            'active',
            assistant.creator_id,
            assistant.last_updated_by,
            new Date(),
            new Date()
          ]
        }
      );
      console.log('创建了系统知识库文件助手');
    }

    // 检查是否已存在user-knowledge-file类型助手
    const [userAssistants] = await queryInterface.sequelize.query(
      `SELECT * FROM ai_assistants WHERE type = 'user-knowledge-file' LIMIT 1`
    );

    if (userAssistants.length === 0) {
      // 创建用户知识库文件助手
      await queryInterface.sequelize.query(
        `INSERT INTO ai_assistants (
          name, type, description, api_key, api_endpoint, app_id, app_code, 
          upload_api_path, analysis_api_path, tags, initial_message, 
          is_system, status, creator_id, last_updated_by, created_at, updated_at
        ) VALUES (
          '用户知识库文件助手', 'user-knowledge-file', '用于将上传的文件同步到用户知识库', 
          ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )`,
        {
          replacements: [
            assistant.api_key,
            assistant.api_endpoint,
            assistant.app_code, // 用户知识库助手的app_id使用原助手的app_code
            assistant.app_code,
            assistant.upload_api_path,
            assistant.analysis_api_path,
            '用户知识库,文件同步,AI平台',
            '我是用户知识库文件助手，可以帮助您将文件同步到用户知识库。',
            1, // is_system
            'active',
            assistant.creator_id,
            assistant.last_updated_by,
            new Date(),
            new Date()
          ]
        }
      );
      console.log('创建了用户知识库文件助手');
    }

    return Promise.resolve();
  },

  down: async (queryInterface, Sequelize) => {
    // 恢复知识库文件分析助手名称和描述
    await queryInterface.sequelize.query(
      `UPDATE ai_assistants 
       SET name = '知识库文件分析助手', 
           description = '用于分析上传到知识库的文件，支持系统知识库和用户知识库' 
       WHERE type = 'knowledge-file'`
    );

    // 删除系统知识库文件助手
    await queryInterface.sequelize.query(
      `DELETE FROM ai_assistants WHERE type = 'system-knowledge-file'`
    );

    // 删除用户知识库文件助手
    await queryInterface.sequelize.query(
      `DELETE FROM ai_assistants WHERE type = 'user-knowledge-file'`
    );

    return Promise.resolve();
  }
};
