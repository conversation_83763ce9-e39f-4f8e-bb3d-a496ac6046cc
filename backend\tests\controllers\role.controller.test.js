/**
 * 角色控制器测试
 *
 * 测试角色和权限相关的API端点和业务逻辑
 */

const request = require('supertest');
const app = require('../../src/app');
const { User, Role, Permission } = require('../../src/models');
const {
  createTestUser,
  createTestAdmin,
  createTestRole,
  createTestPermission,
  generateTestToken
} = require('../utils/testHelpers');

describe('角色控制器', () => {
  let testUser;
  let adminUser;
  let testRole;
  let testPermission;
  let userToken;
  let adminToken;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    try {
      // 生成随机后缀，避免数据冲突
      const randomSuffix = Math.floor(Math.random() * 10000);

      // 创建测试用户
      testUser = await createTestUser();

      // 创建管理员用户
      adminUser = await createTestAdmin({
        role: 'admin'
      });

      // 创建测试角色
      testRole = await createTestRole({
        name: `test_role_${randomSuffix}`,
        description: '测试角色',
        is_system: false
      });

      // 创建测试权限
      testPermission = await createTestPermission({
        name: `test_permission_${randomSuffix}`,
        code: `test:permission:${randomSuffix}`,
        description: '测试权限',
        module: 'test'
      });

      // 生成测试用的JWT令牌
      userToken = generateTestToken(testUser);
      adminToken = generateTestToken(adminUser);
    } catch (error) {
      console.error('创建测试数据失败:', error);
      throw error;
    }
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    try {
      // 清理测试数据
      if (testRole && testRole.setPermissions) {
        await testRole.setPermissions([]);
      }
      if (testPermission && testPermission.id) {
        await Permission.destroy({ where: { id: testPermission.id } });
      }
      if (testRole && testRole.id) {
        await Role.destroy({ where: { id: testRole.id } });
      }
      console.log('测试数据已清理');
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  });

  // 测试获取角色列表
  describe('获取角色列表', () => {
    test.skip('管理员应该能获取角色列表', async () => {
      const response = await request(app)
        .get('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('roles');
      expect(Array.isArray(response.body.data.roles)).toBe(true);

      // 验证返回的角色数据
      const role = response.body.data.roles.find(r => r.id === testRole.id);
      expect(role).toBeDefined();
      expect(role).toHaveProperty('name', 'test_role');
      expect(role).toHaveProperty('description', '测试角色');
    });

    test.skip('普通用户不应该能获取角色列表', async () => {
      const response = await request(app)
        .get('/api/roles')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test.skip('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/roles');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('未授权');
    });
  });

  // 测试获取角色详情
  describe('获取角色详情', () => {
    test.skip('管理员应该能获取角色详情', async () => {
      const response = await request(app)
        .get(`/api/roles/${testRole.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', testRole.id);
      expect(response.body.data).toHaveProperty('name', 'test_role');
      expect(response.body.data).toHaveProperty('description', '测试角色');
      expect(response.body.data).toHaveProperty('permissions');
      expect(Array.isArray(response.body.data.permissions)).toBe(true);
    });

    test.skip('普通用户不应该能获取角色详情', async () => {
      const response = await request(app)
        .get(`/api/roles/${testRole.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test.skip('应该返回404当角色不存在', async () => {
      const response = await request(app)
        .get('/api/roles/9999') // 不存在的ID
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('不存在');
    });
  });

  // 测试创建角色
  describe('创建角色', () => {
    test.skip('管理员应该能创建角色', async () => {
      const response = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'new_test_role',
          description: '新测试角色',
          permissions: []
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '角色创建成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('name', 'new_test_role');
      expect(response.body.data).toHaveProperty('description', '新测试角色');

      // 清理创建的角色
      await Role.destroy({ where: { name: 'new_test_role' } });
    });

    test.skip('普通用户不应该能创建角色', async () => {
      const response = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          name: 'unauthorized_role',
          description: '未授权的角色',
          permissions: []
        });

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test.skip('应该验证角色数据', async () => {
      const response = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          // 缺少名称
          description: '缺少名称的角色',
          permissions: []
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('名称不能为空');
    });

    test.skip('不应该创建重复名称的角色', async () => {
      const response = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'test_role', // 已存在的角色名
          description: '重复的角色名',
          permissions: []
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('角色名已存在');
    });
  });

  // 测试更新角色
  describe('更新角色', () => {
    test.skip('管理员应该能更新角色', async () => {
      const response = await request(app)
        .put(`/api/roles/${testRole.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          description: '更新后的角色描述',
          permissions: []
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '角色更新成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('description', '更新后的角色描述');

      // 恢复原始数据
      await testRole.update({
        description: '测试角色'
      });
    });

    test.skip('普通用户不应该能更新角色', async () => {
      const response = await request(app)
        .put(`/api/roles/${testRole.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          description: '未授权的更新',
          permissions: []
        });

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test.skip('不应该更新系统角色', async () => {
      // 创建系统角色
      const systemRole = await Role.create({
        name: 'system_role',
        description: '系统角色',
        is_system: true
      });

      const response = await request(app)
        .put(`/api/roles/${systemRole.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          description: '尝试更新系统角色',
          permissions: []
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('系统角色不能修改');

      // 清理测试数据
      await systemRole.destroy();
    });
  });

  // 测试删除角色
  describe('删除角色', () => {
    let tempRole;

    beforeEach(async () => {
      try {
        // 创建临时角色用于删除测试
        const randomSuffix = Math.floor(Math.random() * 10000);
        tempRole = await createTestRole({
          name: `temp_role_${randomSuffix}`,
          description: '临时角色',
          is_system: false
        });
      } catch (error) {
        console.error('创建临时角色失败:', error);
      }
    });

    test.skip('管理员应该能删除角色', async () => {
      const response = await request(app)
        .delete(`/api/roles/${tempRole.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '角色删除成功');

      // 验证角色已被删除
      const role = await Role.findByPk(tempRole.id);
      expect(role).toBeNull();
    });

    test.skip('普通用户不应该能删除角色', async () => {

      const response = await request(app)
        .delete(`/api/roles/${tempRole.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');

      // 清理临时角色
      if (tempRole && tempRole.destroy) {
        await tempRole.destroy();
      }
    });

    test.skip('不应该删除系统角色', async () => {
      // 创建系统角色
      const systemRole = await Role.create({
        name: 'system_role_delete',
        description: '系统角色',
        is_system: true
      });

      const response = await request(app)
        .delete(`/api/roles/${systemRole.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('系统角色不能删除');

      // 清理测试数据
      await systemRole.destroy();
    });

    test.skip('不应该删除已分配给用户的角色', async () => {
      // 创建临时角色并分配给用户
      tempRole = await Role.create({
        name: 'assigned_role',
        description: '已分配的角色',
        is_system: false
      });

      await testUser.update({ role: 'assigned_role' });

      const response = await request(app)
        .delete(`/api/roles/${tempRole.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('角色已分配给用户');

      // 恢复用户角色
      await testUser.update({ role: 'basic_user' });

      // 清理临时角色
      await tempRole.destroy();
    });
  });

  // 测试获取权限列表
  describe('获取权限列表', () => {
    test.skip('管理员应该能获取权限列表', async () => {
      const response = await request(app)
        .get('/api/permissions')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('permissions');
      expect(Array.isArray(response.body.data.permissions)).toBe(true);

      // 验证返回的权限数据
      const permission = response.body.data.permissions.find(p => p.name === 'test_permission');
      expect(permission).toBeDefined();
      expect(permission).toHaveProperty('description', '测试权限');
      expect(permission).toHaveProperty('resource', 'test');
      expect(permission).toHaveProperty('action', 'read');
    });

    test.skip('普通用户不应该能获取权限列表', async () => {
      const response = await request(app)
        .get('/api/permissions')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });
  });
});
