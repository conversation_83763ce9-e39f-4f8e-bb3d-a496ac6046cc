/**
 * 系统相关API
 * 
 * 处理系统配置、角色、权限等请求
 */

import request from '../index';

// 系统配置API
/**
 * 获取系统配置
 * @returns {Promise} 系统配置
 */
export function getSystemConfig() {
  return request({
    url: '/system/config',
    method: 'get'
  });
}

/**
 * 更新系统配置
 * @param {Object} data - 系统配置信息
 * @returns {Promise} 更新结果
 */
export function updateSystemConfig(data) {
  return request({
    url: '/system/config',
    method: 'put',
    data
  });
}

/**
 * 获取系统统计信息
 * @returns {Promise} 系统统计信息
 */
export function getSystemStats() {
  return request({
    url: '/system/stats',
    method: 'get'
  });
}

// 角色API
/**
 * 获取角色列表
 * @param {Object} params - 查询参数
 * @param {string} params.search - 搜索关键词
 * @returns {Promise} 角色列表
 */
export function getRoleList(params) {
  return request({
    url: '/roles',
    method: 'get',
    params
  });
}

/**
 * 获取角色详情
 * @param {string} id - 角色ID
 * @returns {Promise} 角色详情
 */
export function getRoleById(id) {
  return request({
    url: `/roles/${id}`,
    method: 'get'
  });
}

/**
 * 创建角色
 * @param {Object} data - 角色信息
 * @param {string} data.name - 角色名称
 * @param {string} data.description - 角色描述
 * @param {Array} data.permissions - 权限ID列表
 * @returns {Promise} 创建结果
 */
export function createRole(data) {
  return request({
    url: '/roles',
    method: 'post',
    data
  });
}

/**
 * 更新角色
 * @param {string} id - 角色ID
 * @param {Object} data - 角色信息
 * @returns {Promise} 更新结果
 */
export function updateRole(id, data) {
  return request({
    url: `/roles/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除角色
 * @param {string} id - 角色ID
 * @returns {Promise} 删除结果
 */
export function deleteRole(id) {
  return request({
    url: `/roles/${id}`,
    method: 'delete'
  });
}

/**
 * 获取角色权限
 * @param {string} id - 角色ID
 * @returns {Promise} 角色权限
 */
export function getRolePermissions(id) {
  return request({
    url: `/roles/${id}/permissions`,
    method: 'get'
  });
}

/**
 * 更新角色权限
 * @param {string} id - 角色ID
 * @param {Object} data - 权限信息
 * @param {Array} data.permissions - 权限ID列表
 * @returns {Promise} 更新结果
 */
export function updateRolePermissions(id, data) {
  return request({
    url: `/roles/${id}/permissions`,
    method: 'put',
    data
  });
}

// 权限API
/**
 * 获取权限列表
 * @param {Object} params - 查询参数
 * @param {string} params.module - 权限模块
 * @param {string} params.search - 搜索关键词
 * @returns {Promise} 权限列表
 */
export function getPermissionList(params) {
  return request({
    url: '/permissions',
    method: 'get',
    params
  });
}

/**
 * 获取权限详情
 * @param {string} id - 权限ID
 * @returns {Promise} 权限详情
 */
export function getPermissionById(id) {
  return request({
    url: `/permissions/${id}`,
    method: 'get'
  });
}

/**
 * 获取权限模块列表
 * @returns {Promise} 权限模块列表
 */
export function getPermissionModules() {
  return request({
    url: '/permissions/modules/list',
    method: 'get'
  });
}

export default {
  // 系统配置
  getSystemConfig,
  updateSystemConfig,
  getSystemStats,
  
  // 角色
  getRoleList,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getRolePermissions,
  updateRolePermissions,
  
  // 权限
  getPermissionList,
  getPermissionById,
  getPermissionModules
};
