/**
 * AI助手组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { AIAssistant } from '@/components/ai/AIAssistant'
import { mockFetch, resetMockFetch } from '../test-utils'

// 模拟AI助手数据
const mockAssistant = {
  id: 1,
  name: '测试助手',
  description: '这是一个测试用的AI助手',
  type: 'personal',
  status: 'active',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
}

// 模拟对话数据
const mockMessages = [
  { role: 'assistant', content: '您好，我是AI助手，有什么可以帮助您的？' }
]

describe('AIAssistant组件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch()
    jest.clearAllMocks()
  })

  // 测试基本渲染
  test('应该正确渲染AI助手组件', () => {
    render(
      <AIAssistant
        assistant={mockAssistant}
        initialMessages={mockMessages}
        onSendMessage={jest.fn()}
      />
    )
    
    expect(screen.getByText('测试助手')).toBeInTheDocument()
    expect(screen.getByText('这是一个测试用的AI助手')).toBeInTheDocument()
    expect(screen.getByText('您好，我是AI助手，有什么可以帮助您的？')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('输入您的问题...')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '发送' })).toBeInTheDocument()
  })

  // 测试发送消息
  test('应该能发送消息并显示用户输入', async () => {
    const handleSendMessage = jest.fn().mockResolvedValue({
      role: 'assistant',
      content: '这是AI助手的回复'
    })
    
    render(
      <AIAssistant
        assistant={mockAssistant}
        initialMessages={mockMessages}
        onSendMessage={handleSendMessage}
      />
    )
    
    // 输入消息
    const input = screen.getByPlaceholderText('输入您的问题...')
    fireEvent.change(input, { target: { value: '测试问题' } })
    
    // 点击发送按钮
    const sendButton = screen.getByRole('button', { name: '发送' })
    fireEvent.click(sendButton)
    
    // 验证处理函数被调用
    expect(handleSendMessage).toHaveBeenCalledWith('测试问题', undefined)
    
    // 等待用户消息显示
    await waitFor(() => {
      expect(screen.getByText('测试问题')).toBeInTheDocument()
    })
    
    // 等待AI回复显示
    await waitFor(() => {
      expect(screen.getByText('这是AI助手的回复')).toBeInTheDocument()
    })
  })

  // 测试空消息验证
  test('不应该发送空消息', () => {
    const handleSendMessage = jest.fn()
    
    render(
      <AIAssistant
        assistant={mockAssistant}
        initialMessages={mockMessages}
        onSendMessage={handleSendMessage}
      />
    )
    
    // 不输入消息，直接点击发送按钮
    const sendButton = screen.getByRole('button', { name: '发送' })
    fireEvent.click(sendButton)
    
    // 验证处理函数未被调用
    expect(handleSendMessage).not.toHaveBeenCalled()
  })

  // 测试加载状态
  test('发送消息时应该显示加载状态', async () => {
    // 创建一个延迟解析的Promise
    const handleSendMessage = jest.fn().mockImplementation(() => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            role: 'assistant',
            content: '这是延迟的回复'
          })
        }, 100)
      })
    })
    
    render(
      <AIAssistant
        assistant={mockAssistant}
        initialMessages={mockMessages}
        onSendMessage={handleSendMessage}
      />
    )
    
    // 输入消息并发送
    const input = screen.getByPlaceholderText('输入您的问题...')
    fireEvent.change(input, { target: { value: '测试问题' } })
    
    const sendButton = screen.getByRole('button', { name: '发送' })
    fireEvent.click(sendButton)
    
    // 验证输入框被禁用
    await waitFor(() => {
      expect(input).toBeDisabled()
    })
    
    // 验证发送按钮被禁用或显示加载状态
    await waitFor(() => {
      expect(sendButton).toBeDisabled()
    })
    
    // 等待回复完成
    await waitFor(() => {
      expect(screen.getByText('这是延迟的回复')).toBeInTheDocument()
    }, { timeout: 200 })
    
    // 验证输入框和按钮恢复可用
    await waitFor(() => {
      expect(input).not.toBeDisabled()
      expect(sendButton).not.toBeDisabled()
    })
  })

  // 测试错误处理
  test('应该处理发送消息时的错误', async () => {
    // 模拟发送消息失败
    const handleSendMessage = jest.fn().mockRejectedValue(new Error('发送失败'))
    
    render(
      <AIAssistant
        assistant={mockAssistant}
        initialMessages={mockMessages}
        onSendMessage={handleSendMessage}
      />
    )
    
    // 输入消息并发送
    const input = screen.getByPlaceholderText('输入您的问题...')
    fireEvent.change(input, { target: { value: '测试问题' } })
    
    const sendButton = screen.getByRole('button', { name: '发送' })
    fireEvent.click(sendButton)
    
    // 验证错误消息显示
    await waitFor(() => {
      expect(screen.getByText(/发送失败|无法获取回复|出错了/)).toBeInTheDocument()
    })
    
    // 验证输入框和按钮恢复可用
    await waitFor(() => {
      expect(input).not.toBeDisabled()
      expect(sendButton).not.toBeDisabled()
    })
  })
});
