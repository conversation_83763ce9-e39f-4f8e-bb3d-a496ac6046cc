# 和富家族研究平台环境配置说明

本文档详细说明了和富家族研究平台的环境配置要求和设置方法，包括开发环境、测试环境和生产环境的配置。

## 1. 环境变量配置

和富家族研究平台使用环境变量来配置不同环境下的参数。项目分为前端和后端两部分，各自有独立的环境变量配置。

### 1.1 后端环境变量 (.env)

后端环境变量文件位于`backend/.env`，包含以下配置项：

| 变量名 | 说明 | 示例值 | 是否必需 |
|--------|------|--------|----------|
| NODE_ENV | 运行环境 | development, test, production | 是 |
| PORT | 服务器端口 | 5000 | 是 |
| DB_HOST | 数据库主机 | localhost | 是 |
| DB_PORT | 数据库端口 | 3306 | 是 |
| DB_USERNAME | 数据库用户名 | root | 是 |
| DB_PASSWORD | 数据库密码 | password | 是 |
| DB_NAME | 数据库名称 | hefamily_dev | 是 |
| JWT_SECRET | JWT密钥 | your_jwt_secret_key | 是 |
| JWT_EXPIRES_IN | JWT过期时间 | 7d | 是 |
| DIFY_API_KEY | Dify API密钥 | your_dify_api_key | 是 |
| DIFY_API_ENDPOINT | Dify API端点 | https://api.dify.ai | 是 |
| DIFY_APP_ID | Dify应用ID | your_dify_app_id | 是 |
| UPLOAD_DIR | 文件上传目录 | uploads | 是 |
| MAX_FILE_SIZE | 最大文件大小(字节) | 10485760 | 是 |
| SESSION_SECRET | 会话密钥 | your_session_secret_key | 是 |
| CORS_ORIGIN | 跨域来源 | http://localhost:3000 | 是 |

### 1.2 前端环境变量 (.env.local)

前端环境变量文件位于`frontend/.env.local`，包含以下配置项：

| 变量名 | 说明 | 示例值 | 是否必需 |
|--------|------|--------|----------|
| NEXT_PUBLIC_API_BASE_URL | API基础URL | http://localhost:5000/api | 是 |
| NEXT_PUBLIC_TOKEN_KEY | 令牌存储键名 | hefamily_token | 是 |
| NEXT_PUBLIC_APP_NAME | 应用名称 | 和富家族研究平台 | 是 |
| NEXT_PUBLIC_APP_DESCRIPTION | 应用描述 | 和富家族研究平台是一个... | 否 |
| NEXT_PUBLIC_MAX_FILE_SIZE | 最大文件大小(字节) | 10485760 | 是 |
| NEXT_PUBLIC_ALLOWED_FILE_TYPES | 允许的文件类型 | pdf,doc,docx,... | 是 |

## 2. 数据库配置

系统使用MySQL数据库，通过Sequelize ORM进行数据库操作。数据库配置文件位于`backend/config/database.js`。

### 2.1 开发环境数据库

```javascript
development: {
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'hefamily_dev',
  host: process.env.DB_HOST || '127.0.0.1',
  port: process.env.DB_PORT || 3306,
  dialect: 'mysql',
  logging: console.log,
  define: {
    timestamps: true,
    underscored: true,
    underscoredAll: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  },
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
}
```

### 2.2 测试环境数据库

```javascript
test: {
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'hefamily_test',
  host: process.env.DB_HOST || '127.0.0.1',
  port: process.env.DB_PORT || 3306,
  dialect: 'mysql',
  logging: false,
  define: {
    timestamps: true,
    underscored: true,
    underscoredAll: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
}
```

### 2.3 生产环境数据库

```javascript
production: {
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT || 3306,
  dialect: 'mysql',
  logging: false,
  define: {
    timestamps: true,
    underscored: true,
    underscoredAll: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  },
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
}
```

## 3. 外部服务配置

### 3.1 Dify API配置

系统使用Dify API提供AI助手功能，需要配置以下参数：

1. **API密钥**: 在Dify平台获取的API密钥
2. **API端点**: Dify API的基础URL
3. **应用ID**: Dify平台上创建的应用ID

配置步骤：

1. 注册并登录[Dify平台](https://dify.ai)
2. 创建新应用
3. 获取API密钥和应用ID
4. 将这些值填入后端环境变量文件

### 3.2 文件存储配置

系统默认使用本地文件系统存储上传的文件，配置如下：

1. **上传目录**: 文件存储的本地路径
2. **最大文件大小**: 允许上传的最大文件大小
3. **允许的文件类型**: 允许上传的文件扩展名列表

## 4. 安全配置

### 4.1 JWT配置

系统使用JWT进行用户认证，配置如下：

1. **密钥**: 用于签名JWT的密钥，应该是一个强随机字符串
2. **过期时间**: JWT的有效期，例如"7d"表示7天

### 4.2 会话配置

系统使用Express会话中间件，配置如下：

1. **会话密钥**: 用于签名会话ID cookie的密钥
2. **会话过期时间**: 会话的有效期，单位为毫秒

### 4.3 CORS配置

系统配置了跨域资源共享(CORS)，允许前端应用访问后端API：

1. **允许的来源**: 允许访问API的前端应用URL

## 5. 不同环境的配置示例

### 5.1 开发环境

#### 后端 (.env)

```
NODE_ENV=development
PORT=5000
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=
DB_NAME=hefamily_dev
JWT_SECRET=dev_jwt_secret_key
JWT_EXPIRES_IN=7d
DIFY_API_KEY=dev_dify_api_key
DIFY_API_ENDPOINT=https://api.dify.ai
DIFY_APP_ID=dev_dify_app_id
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
SESSION_SECRET=dev_session_secret
CORS_ORIGIN=http://localhost:3000
```

#### 前端 (.env.local)

```
NEXT_PUBLIC_API_BASE_URL=http://localhost:5000/api
NEXT_PUBLIC_TOKEN_KEY=hefamily_token
NEXT_PUBLIC_APP_NAME=和富家族研究平台
NEXT_PUBLIC_APP_DESCRIPTION=和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif
```

### 5.2 生产环境

#### 后端 (.env)

```
NODE_ENV=production
PORT=5000
DB_HOST=production-db-host
DB_PORT=3306
DB_USERNAME=production_user
DB_PASSWORD=strong_production_password
DB_NAME=hefamily_prod
JWT_SECRET=strong_production_jwt_secret
JWT_EXPIRES_IN=7d
DIFY_API_KEY=production_dify_api_key
DIFY_API_ENDPOINT=https://api.dify.ai
DIFY_APP_ID=production_dify_app_id
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
SESSION_SECRET=strong_production_session_secret
CORS_ORIGIN=https://your-production-domain.com
```

#### 前端 (.env.local)

```
NEXT_PUBLIC_API_BASE_URL=https://your-production-domain.com/api
NEXT_PUBLIC_TOKEN_KEY=hefamily_token
NEXT_PUBLIC_APP_NAME=和富家族研究平台
NEXT_PUBLIC_APP_DESCRIPTION=和富家族研究平台是一个展示家族历史、个人专题、知识库管理和AI研究助手的综合平台。
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif
```

## 6. 环境变量管理最佳实践

1. **不要提交敏感信息**: 不要将包含敏感信息的环境变量文件提交到版本控制系统
2. **使用环境变量模板**: 提供一个`.env.example`文件作为模板
3. **使用不同的环境文件**: 为不同环境使用不同的环境变量文件
4. **定期更新密钥**: 定期更新JWT密钥和会话密钥
5. **使用强密码**: 为数据库和外部服务使用强密码
6. **限制访问权限**: 限制对环境变量文件的访问权限

## 7. 故障排除

### 7.1 环境变量未加载

如果环境变量未正确加载，请检查：

1. 环境变量文件是否存在于正确的位置
2. 文件格式是否正确
3. 应用是否正确加载环境变量文件

### 7.2 数据库连接失败

如果数据库连接失败，请检查：

1. 数据库服务是否运行
2. 数据库凭据是否正确
3. 数据库主机和端口是否可访问
4. 数据库用户是否有足够的权限

### 7.3 外部服务连接失败

如果外部服务连接失败，请检查：

1. API密钥和端点是否正确
2. 网络连接是否正常
3. 外部服务是否可用
4. 请求格式是否正确
