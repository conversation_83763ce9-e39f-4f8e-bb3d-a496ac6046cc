/**
 * AI助手控制器
 *
 * 处理AI助手相关的业务逻辑，如配置、查询等
 */

const { AIAssistant, User, KnowledgeBase } = require('../models');
const axios = require('axios');
const { Op } = require('sequelize');

// 所有助手都使用流式响应处理逻辑，不再区分复杂助手

/**
 * 获取AI助手列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAIAssistants = async (req, res) => {
  try {
    const { type } = req.query;
    console.log('获取AI助手列表 - 请求参数:', { type, user: req.user ? req.user.username : '未登录' });

    // 构建查询条件
    const whereConditions = {};

    // 如果不是管理员，只返回状态为active的助手
    if (req.user && req.user.role !== 'admin') {
      whereConditions.status = 'active';
    }

    // 根据类型筛选
    if (type) {
      whereConditions.type = type;
    }

    console.log('获取AI助手列表 - 查询条件:', whereConditions);

    // 查询AI助手
    const assistants = await AIAssistant.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'lastUpdatedBy',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['type', 'ASC'], ['name', 'ASC']]
    });

    console.log('获取AI助手列表 - 查询结果:', assistants.map(a => ({
      id: a.id,
      name: a.name,
      type: a.type,
      status: a.status
    })));

    // 过滤敏感信息
    const filteredAssistants = assistants.map(assistant => {
      const data = assistant.toJSON();

      // 检查用户是否有使用AI助手的权限
      const hasAiPermission = req.user.permissions &&
        (req.user.permissions.includes('personal:ai_use') ||
         req.user.permissions.includes('data:ai_query') ||
         req.user.permissions.includes('assistant:use') ||
         req.user.role === 'admin');

      // 如果用户没有权限，则不显示API密钥和端点
      if (!hasAiPermission) {
        delete data.api_key;
        delete data.api_endpoint;
        delete data.app_id;
        delete data.app_code;
        delete data.upload_api_path;
        delete data.analysis_api_path;
      }

      return data;
    });

    console.log('获取AI助手列表 - 返回数据:', filteredAssistants.map(a => ({
      id: a.id,
      name: a.name,
      type: a.type,
      status: a.status,
      hasApiKey: !!a.api_key,
      hasApiEndpoint: !!a.api_endpoint
    })));

    res.status(200).json({
      success: true,
      data: filteredAssistants
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取AI助手列表失败',
      error: error.message
    });
  }
};

/**
 * 创建AI助手
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createAIAssistant = async (req, res) => {
  try {
    const {
      name,
      type,
      description,
      api_key,
      api_endpoint,
      app_id,
      app_code,
      upload_api_path,
      analysis_api_path,
      tags,
      initial_message,
      status
    } = req.body;

    console.log('创建AI助手 - 请求参数:', {
      name,
      type,
      description: description ? '有值' : '未提供',
      api_key: api_key ? '有值' : '未提供',
      api_endpoint,
      app_id,
      app_code,
      upload_api_path: upload_api_path ? '有值' : '未提供',
      analysis_api_path: analysis_api_path ? '有值' : '未提供',
      tags,
      initial_message: initial_message ? '有值' : '未提供',
      status
    });

    // 验证必填字段
    if (!name || !type) {
      return res.status(400).json({
        success: false,
        message: '名称和类型为必填字段'
      });
    }

    // 创建AI助手
    const assistant = await AIAssistant.create({
      name,
      type,
      description,
      api_key,
      api_endpoint,
      app_id,
      app_code,
      upload_api_path,
      analysis_api_path,
      tags,
      initial_message,
      status: status || 'active',
      is_system: false, // 新创建的助手不是系统预设助手
      creator_id: req.user.id,
      last_updated_by: req.user.id
    });

    // 获取创建后的助手信息
    const createdAssistant = await AIAssistant.findByPk(assistant.id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'lastUpdatedBy',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    console.log('创建AI助手 - 创建成功:', {
      id: createdAssistant.id,
      name: createdAssistant.name,
      type: createdAssistant.type,
      status: createdAssistant.status
    });

    res.status(201).json({
      success: true,
      message: 'AI助手创建成功',
      data: createdAssistant
    });
  } catch (error) {
    console.error('创建AI助手失败:', error);
    res.status(500).json({
      success: false,
      message: '创建AI助手失败',
      error: error.message
    });
  }
};

/**
 * 获取AI助手详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAIAssistantById = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询AI助手
    const assistant = await AIAssistant.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'lastUpdatedBy',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        message: 'AI助手不存在'
      });
    }

    // 过滤敏感信息
    const data = assistant.toJSON();

    // 检查用户是否有使用AI助手的权限
    const hasAiPermission = req.user.permissions &&
      (req.user.permissions.includes('personal:ai_use') ||
       req.user.permissions.includes('data:ai_query') ||
       req.user.permissions.includes('assistant:use') ||
       req.user.role === 'admin');

    // 如果用户没有权限，则不显示API密钥和端点
    if (!hasAiPermission) {
      delete data.api_key;
      delete data.api_endpoint;
      delete data.app_id;
      delete data.app_code;
      delete data.upload_api_path;
      delete data.analysis_api_path;
    }

    res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取AI助手详情失败',
      error: error.message
    });
  }
};

/**
 * 根据名称获取AI助手
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAIAssistantByName = async (req, res) => {
  try {
    const { name } = req.query;
    const { Op } = require('sequelize');

    console.log('根据名称获取AI助手 - 请求参数:', { name, user: req.user ? req.user.username : '未登录' });

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '名称参数不能为空'
      });
    }

    // 查询AI助手
    const assistant = await AIAssistant.findOne({
      where: {
        name: {
          [Op.like]: `%${name}%`
        }
      },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'lastUpdatedBy',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    console.log('根据名称获取AI助手 - 查询结果:', assistant ? {
      id: assistant.id,
      name: assistant.name,
      type: assistant.type,
      status: assistant.status
    } : '未找到');

    if (!assistant) {
      return res.status(404).json({
        success: false,
        message: `未找到名称包含"${name}"的AI助手`
      });
    }

    // 过滤敏感信息
    const data = assistant.toJSON();

    // 检查用户是否有使用AI助手的权限
    const hasAiPermission = req.user && req.user.permissions &&
      (req.user.permissions.includes('personal:ai_use') ||
       req.user.permissions.includes('data:ai_query') ||
       req.user.permissions.includes('assistant:use') ||
       req.user.role === 'admin');

    // 如果用户没有权限，则不显示API密钥和端点
    if (!hasAiPermission) {
      delete data.api_key;
      delete data.api_endpoint;
      delete data.app_id;
      delete data.app_code;
      delete data.upload_api_path;
      delete data.analysis_api_path;
    }

    res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    console.error('根据名称获取AI助手失败:', error);
    res.status(500).json({
      success: false,
      message: '根据名称获取AI助手失败',
      error: error.message
    });
  }
};

/**
 * 更新AI助手
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateAIAssistant = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      api_key,
      api_endpoint,
      app_id,
      app_code,
      upload_api_path,
      analysis_api_path,
      tags,
      initial_message,
      status
    } = req.body;

    // 查询AI助手
    const assistant = await AIAssistant.findByPk(id);

    if (!assistant) {
      return res.status(404).json({
        success: false,
        message: 'AI助手不存在'
      });
    }

    // 系统预设助手不能修改类型
    if (assistant.is_system && req.body.type && req.body.type !== assistant.type) {
      return res.status(400).json({
        success: false,
        message: '系统预设助手不能修改类型'
      });
    }

    // 记录更新前的助手信息
    console.log('更新AI助手 - 更新前:', {
      id: assistant.id,
      name: assistant.name,
      type: assistant.type,
      api_endpoint: assistant.api_endpoint,
      app_id: assistant.app_id,
      app_code: assistant.app_code,
      status: assistant.status
    });

    // 记录请求中的字段
    console.log('更新AI助手 - 请求字段:', {
      name,
      description: description !== undefined ? '有值' : '未提供',
      api_key: api_key ? '有值' : '未提供',
      api_endpoint,
      app_id,
      app_code,
      upload_api_path: upload_api_path !== undefined ? '有值' : '未提供',
      analysis_api_path: analysis_api_path !== undefined ? '有值' : '未提供',
      tags,
      initial_message: initial_message !== undefined ? '有值' : '未提供',
      status
    });

    // 更新AI助手 - 特别处理空字符串
    const updateData = {
      name: name || assistant.name,
      description: description !== undefined ? description : assistant.description,
      api_key: api_key || assistant.api_key,
      api_endpoint: api_endpoint || assistant.api_endpoint,
      // 特别处理app_id和app_code，允许设置为空字符串
      app_id: app_id !== undefined ? app_id : assistant.app_id,
      app_code: app_code !== undefined ? app_code : assistant.app_code,
      upload_api_path: upload_api_path !== undefined ? upload_api_path : assistant.upload_api_path,
      analysis_api_path: analysis_api_path !== undefined ? analysis_api_path : assistant.analysis_api_path,
      tags: tags !== undefined ? tags : assistant.tags,
      initial_message: initial_message !== undefined ? initial_message : assistant.initial_message,
      status: status || assistant.status,
      last_updated_by: req.user.id
    };

    console.log('更新AI助手 - 最终更新数据:', {
      ...updateData,
      api_key: updateData.api_key ? '有值' : '无值'
    });

    await assistant.update(updateData);

    // 获取更新后的助手信息
    const updatedAssistant = await AIAssistant.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'lastUpdatedBy',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: 'AI助手更新成功',
      data: updatedAssistant
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新AI助手失败',
      error: error.message
    });
  }
};

/**
 * 个人专题助手查询
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.queryPersonalAssistant = async (req, res) => {
  try {
    console.log('个人专题助手查询 - 请求体:', req.body);
    const { query, conversation_id } = req.body;

    if (!query) {
      console.log('个人专题助手查询 - 错误: 查询内容为空');
      return res.status(400).json({
        success: false,
        message: '查询内容不能为空'
      });
    }

    console.log('个人专题助手查询 - 查询数据库中的助手配置');

    // 从请求中获取人物ID和名称
    const personal_id = req.body.personal_id ? parseInt(req.body.personal_id) : null;
    const personal_name = req.body.personal_name || null;

    console.log('个人专题助手查询 - 人物信息:', { personal_id, personal_name });

    // 初始化助手变量
    let assistant = null;

    // 如果提供了人物ID或名称，尝试查找特定人物的助手配置
    if (personal_id || personal_name) {
      console.log('个人专题助手查询 - 尝试查找特定人物的助手配置');

      // 如果提供了人物名称，直接查找名称中包含该名称的助手
      if (personal_name) {
        console.log(`个人专题助手查询 - 尝试查找名称中包含"${personal_name}"的助手`);

        // 查询所有个人专题助手，不限制status，以便查看所有助手
        const allAssistants = await AIAssistant.findAll({
          where: {
            type: 'personal'
          }
        });

        console.log('个人专题助手查询 - 找到的所有个人专题助手:',
          allAssistants.map(a => ({ id: a.id, name: a.name, type: a.type, status: a.status }))
        );

        // 查找名称中包含人物名称的助手
        const matchingAssistant = allAssistants.find(a =>
          a.name.includes(personal_name)
        );

        if (matchingAssistant) {
          console.log(`个人专题助手查询 - 找到名称中包含"${personal_name}"的助手:`, {
            id: matchingAssistant.id,
            name: matchingAssistant.name,
            type: matchingAssistant.type,
            status: matchingAssistant.status,
            api_endpoint: matchingAssistant.api_endpoint,
            api_key: matchingAssistant.api_key ? '已设置' : '未设置',
            app_id: matchingAssistant.app_id,
            app_code: matchingAssistant.app_code
          });

          // 检查助手状态
          if (matchingAssistant.status !== 'active') {
            console.log(`个人专题助手查询 - 警告: "${personal_name}"助手状态不是active，而是${matchingAssistant.status}`);
          }

          assistant = matchingAssistant;
        } else {
          console.log(`个人专题助手查询 - 未找到名称中包含"${personal_name}"的助手`);
        }
      }
      // 如果提供了人物ID，根据ID查找对应的助手
      else if (personal_id) {
        console.log(`个人专题助手查询 - 尝试根据人物ID ${personal_id} 查找助手`);

        // 人物ID到名称的映射
        const idToName = {
          1: '蔡和森',
          2: '蔡畅',
          3: '李富春',
          4: '葛健豪',
          5: '向警予'
        };

        const personalName = idToName[personal_id];
        if (personalName) {
          console.log(`个人专题助手查询 - 人物ID ${personal_id} 对应的名称是 ${personalName}`);

          // 查询所有个人专题助手
          const allAssistants = await AIAssistant.findAll({
            where: {
              type: 'personal',
              status: 'active'
            }
          });

          // 查找名称中包含人物名称的助手
          const matchingAssistant = allAssistants.find(a =>
            a.name.includes(personalName)
          );

          if (matchingAssistant) {
            console.log(`个人专题助手查询 - 找到名称中包含"${personalName}"的助手:`, {
              id: matchingAssistant.id,
              name: matchingAssistant.name,
              type: matchingAssistant.type
            });

            assistant = matchingAssistant;
          } else {
            console.log(`个人专题助手查询 - 未找到名称中包含"${personalName}"的助手`);
          }
        } else {
          console.log(`个人专题助手查询 - 未找到人物ID ${personal_id} 对应的名称`);
        }
      }
    }

    // 如果没有找到特定人物的助手配置，则使用通用个人专题助手
    if (!assistant) {
      console.log('个人专题助手查询 - 未找到特定人物的助手配置，使用通用个人专题助手');

      // 先尝试查找名称为"个人专题助手"的配置
      assistant = await AIAssistant.findOne({
        where: {
          type: 'personal',
          name: '个人专题助手'
        }
      });

      // 如果找不到，则查找任何状态为active的个人专题助手
      if (!assistant) {
        assistant = await AIAssistant.findOne({
          where: {
            type: 'personal',
            status: 'active'
          }
        });
      }

      // 如果仍然找不到，则查找任何个人专题助手
      if (!assistant) {
        assistant = await AIAssistant.findOne({
          where: {
            type: 'personal'
          }
        });
      }
    }

    // 如果找到了助手但状态不是active，尝试使用它
    if (assistant && assistant.status !== 'active') {
      console.log(`个人专题助手查询 - 警告: 使用的助手"${assistant.name}"状态不是active，而是${assistant.status}`);
    }

    console.log('个人专题助手查询 - 助手配置:', assistant ? {
      id: assistant.id,
      name: assistant.name,
      type: assistant.type,
      api_endpoint: assistant.api_endpoint,
      app_id: assistant.app_id,
      app_code: assistant.app_code,
      status: assistant.status
    } : 'null');

    if (!assistant) {
      console.log('个人专题助手查询 - 错误: 助手不存在或未激活');
      return res.status(404).json({
        success: false,
        message: '个人专题助手不存在或未激活'
      });
    }

    // 检查API配置是否完整
    if (!assistant.api_key || !assistant.api_endpoint) {
      console.log('个人专题助手查询 - 错误: API配置不完整');
      return res.status(500).json({
        success: false,
        message: 'API配置不完整，请联系管理员配置个人专题助手'
      });
    }

    console.log('个人专题助手查询 - 准备调用外部API:', assistant.api_endpoint);
    // 调用Dify API
    try {
      // 根据您提供的请求头信息构建API请求
      // 从请求头中可以看到，API端点是 https://ai.glab.vip
      // 路径是 /api/chat-messages

      // 尝试多种可能的API URL格式
      const possibleApiUrls = [];

      // 1. 使用配置的API端点 + /api/chat-messages
      let apiUrl1 = assistant.api_endpoint;
      if (apiUrl1.endsWith('/')) {
        apiUrl1 = apiUrl1.slice(0, -1);
      }
      possibleApiUrls.push(`${apiUrl1}/api/chat-messages`);

      // 2. 直接使用配置的API端点 + /chat-messages
      let apiUrl2 = assistant.api_endpoint;
      if (apiUrl2.endsWith('/')) {
        apiUrl2 = apiUrl2.slice(0, -1);
      }
      possibleApiUrls.push(`${apiUrl2}/chat-messages`);

      // 3. 使用硬编码的URL（根据请求头）
      possibleApiUrls.push('https://ai.glab.vip/api/chat-messages');

      // 选择第一个URL作为主要URL
      const apiUrl = possibleApiUrls[0];

      console.log('个人专题助手查询 - 可能的API URL:', possibleApiUrls);
      console.log('个人专题助手查询 - 使用API URL:', apiUrl);

      // 构建请求体 - 添加人物信息
      const apiRequestBody = {
        inputs: {
          // 添加人物ID和名称作为上下文信息
          personal_id: personal_id ? personal_id.toString() : undefined,
          personal_name: personal_name || undefined,
          // 添加助手名称作为上下文信息，确保Dify知道是哪个助手
          assistant_name: assistant.name
        },
        query,
        response_mode: 'streaming', // 使用streaming模式，与其他助手保持一致
        conversation_id,
        user: req.user ? req.user.id.toString() : 'anonymous'
      };

      console.log('个人专题助手查询 - 请求体中的人物信息:', {
        personal_id: personal_id ? personal_id.toString() : undefined,
        personal_name: personal_name || undefined,
        assistant_name: assistant.name
      });

      // 添加app_id和app_code字段
      if (assistant.app_id) {
        apiRequestBody.app_id = assistant.app_id;
      }
      if (assistant.app_code) {
        apiRequestBody.app_code = assistant.app_code;
      }

      console.log('个人专题助手查询 - 添加app_id和app_code字段:', {
        app_id: assistant.app_id,
        app_code: assistant.app_code
      });

      console.log('个人专题助手查询 - API请求体:', JSON.stringify(apiRequestBody, null, 2));
      console.log('个人专题助手查询 - API密钥前10个字符:', assistant.api_key.substring(0, 10) + '...');

      // 发送请求
      let response;
      let error;

      // 尝试所有可能的URL
      for (const url of possibleApiUrls) {
        try {
          console.log(`个人专题助手查询 - 尝试URL: ${url}`);
          console.log(`个人专题助手查询 - 开始调用API: ${url} - ${new Date().toISOString()}`);
          response = await axios.post(
            url,
            apiRequestBody,
            {
              headers: {
                'Authorization': `Bearer ${assistant.api_key}`,
                'Content-Type': 'application/json'
              },
              timeout: 3600000 // 增加到60分钟超时
            }
          );
          console.log(`个人专题助手查询 - API调用完成: ${url} - ${new Date().toISOString()}`);
          console.log(`个人专题助手查询 - URL ${url} 成功!`);
          break; // 如果成功，跳出循环
        } catch (err) {
          console.error(`个人专题助手查询 - URL ${url} 失败:`, err.message);

          // 记录更详细的错误信息
          if (err.response) {
            // 服务器返回了错误状态码
            console.error(`个人专题助手查询 - URL ${url} 错误响应:`, {
              status: err.response.status,
              statusText: err.response.statusText,
              data: err.response.data,
              headers: err.response.headers
            });
          } else if (err.request) {
            // 请求已发送但没有收到响应
            console.error(`个人专题助手查询 - URL ${url} 请求已发送但无响应:`, {
              method: err.config?.method,
              url: err.config?.url,
              timeout: err.config?.timeout
            });
          } else if (err.code === 'ECONNABORTED') {
            // 请求超时
            console.error(`个人专题助手查询 - URL ${url} 请求超时:`, {
              timeout: err.config?.timeout,
              message: err.message
            });
          }

          error = err;
        }
      }

      // 如果所有URL都失败，抛出最后一个错误
      if (!response) {
        throw error || new Error('所有API URL都调用失败');
      }

      console.log('个人专题助手查询 - API调用成功');
      res.status(200).json({
        success: true,
        data: response.data
      });
    } catch (apiError) {
      console.error('个人专题助手查询 - API调用失败:', apiError.message);
      if (apiError.response) {
        console.error('个人专题助手查询 - API错误响应:', {
          status: apiError.response.status,
          data: apiError.response.data
        });
      }
      throw apiError; // 重新抛出错误，让外层catch处理
    }
  } catch (error) {
    console.error('个人专题助手查询 - 处理失败:', error.message);
    res.status(500).json({
      success: false,
      message: '查询个人专题助手失败',
      error: error.message
    });
  }
};

/**
 * 数据查询助手查询
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.queryDataAssistant = async (req, res) => {
  try {
    console.log('数据查询助手查询 - 请求体:', req.body);
    const {
      query,
      conversation_id,
      knowledge_base_ids,
      // 从前端传递的API配置信息
      api_key,
      api_endpoint,
      app_id,
      app_code
    } = req.body;

    if (!query) {
      console.log('数据查询助手查询 - 错误: 查询内容为空');
      return res.status(400).json({
        success: false,
        message: '查询内容不能为空'
      });
    }

    // 记录API配置信息
    console.log('数据查询助手查询 - API配置信息:', {
      api_key: api_key ? '已提供' : '未提供',
      api_endpoint: api_endpoint || '未提供',
      app_id: app_id || '未提供',
      app_code: app_code || '未提供'
    });

    // 优先使用前端传递的API配置信息
    let assistantApiKey = api_key;
    let assistantApiEndpoint = api_endpoint;
    let assistantAppId = app_id;
    let assistantAppCode = app_code;

    // 如果前端没有传递API配置信息，则从数据库中查询
    if (!assistantApiKey || !assistantApiEndpoint) {
      console.log('数据查询助手查询 - 前端未提供完整API配置，尝试从数据库查询');

      // 查询数据查询助手
      const assistant = await AIAssistant.findOne({
        where: {
          type: 'data-query',
          status: 'active'
        }
      });

      if (!assistant) {
        console.log('数据查询助手查询 - 错误: 助手不存在或未激活');
        return res.status(404).json({
          success: false,
          message: '数据查询助手不存在或未激活'
        });
      }

      // 使用数据库中的配置
      assistantApiKey = assistant.api_key;
      assistantApiEndpoint = assistant.api_endpoint;
      assistantAppId = assistant.app_id;
      assistantAppCode = assistant.app_code;

      console.log('数据查询助手查询 - 从数据库获取的API配置:', {
        api_key: assistantApiKey ? '已设置' : '未设置',
        api_endpoint: assistantApiEndpoint || '未设置',
        app_id: assistantAppId || '未设置',
        app_code: assistantAppCode || '未设置'
      });
    }

    // 验证API配置是否完整
    if (!assistantApiKey || !assistantApiEndpoint) {
      console.log('数据查询助手查询 - 错误: API配置不完整');
      return res.status(500).json({
        success: false,
        message: 'API配置不完整，请联系管理员配置数据查询助手'
      });
    }

    // 从会话存储中获取知识库ID
    const knowledgeBaseId = req.session?.dataAssistant?.knowledgeBaseId;

    // 准备API URL
    let apiUrl = assistantApiEndpoint;

    // 确保URL格式正确
    if (apiUrl.endsWith('/')) {
      apiUrl = apiUrl.slice(0, -1);
    }

    // 如果URL不包含chat-messages，添加它
    if (!apiUrl.includes('chat-messages')) {
      apiUrl = `${apiUrl}/chat-messages`;
    }

    console.log('数据查询助手查询 - 最终API URL:', apiUrl);

    // 构建请求体
    const apiRequestBody = {
      inputs: {
        // 如果有知识库ID，添加到请求中
        knowledge_base_id: knowledgeBaseId
      },
      query,
      response_mode: 'streaming',
      conversation_id,
      user: req.user.id.toString()
    };

    // 添加app_id和app_code字段
    if (assistantAppId) {
      apiRequestBody.app_id = assistantAppId;
    }
    if (assistantAppCode) {
      apiRequestBody.app_code = assistantAppCode;
    }

    // 如果前端传递了知识库IDs，添加到请求中
    if (knowledge_base_ids && knowledge_base_ids.length > 0) {
      apiRequestBody.inputs.knowledge_base_ids = knowledge_base_ids;
    }

    console.log('数据查询助手查询 - API请求体:', JSON.stringify(apiRequestBody, null, 2));
    console.log('数据查询助手查询 - API密钥前10个字符:', assistantApiKey.substring(0, 10) + '...');

    // 调用Dify API
    const response = await axios.post(
      apiUrl,
      apiRequestBody,
      {
        headers: {
          'Authorization': `Bearer ${assistantApiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 3600000 // 60分钟超时
      }
    );

    console.log('数据查询助手查询 - API调用成功');

    // 所有助手都使用流式响应处理逻辑
    if (response.data) {
      console.log('数据查询助手查询 - 处理流式响应');
      console.log('响应数据类型:', typeof response.data);

      // 打印完整的响应数据以便调试
      const responseStr = JSON.stringify(response.data);
      console.log('完整响应数据:', responseStr.substring(0, 200) + '...');

      // 尝试从流式响应中提取有用信息
      let extractedAnswer = '';
      let conversationId = conversation_id;
      let finalAnswer = null;

      // 直接解析流式响应，不使用阻塞模式
      console.log('直接解析流式响应数据');

      // 首先检查是否是Dify的流式响应格式
      const isDifyStreamingResponse =
        (typeof response.data === 'string' && response.data.includes('data:')) ||
        (response.data.data && typeof response.data.data === 'string' && response.data.data.includes('data:'));

      if (isDifyStreamingResponse) {
        console.log('检测到Dify流式响应格式');

        // 获取原始流式数据
        const rawStreamData = typeof response.data === 'string'
          ? response.data
          : (response.data.data && typeof response.data.data === 'string' ? response.data.data : null);

        if (rawStreamData) {
          console.log('原始流式数据前200字符:', rawStreamData.substring(0, 200));

          // 解析流式响应
          const lines = rawStreamData.split('\n').filter(line => line.trim());
          console.log(`找到 ${lines.length} 行数据`);

          // 存储所有消息
          const messages = [];
          // 存储累积的消息内容
          let accumulatedContent = '';

          // 解析每一行
          for (const line of lines) {
            if (line.startsWith('data:')) {
              try {
                const jsonStr = line.substring(5).trim();
                // 跳过空行
                if (!jsonStr || jsonStr === '[DONE]') continue;

                const eventData = JSON.parse(jsonStr);
                messages.push(eventData);

                // 如果是message_end事件，提取最终答案
                if (eventData.event === 'message_end') {
                  finalAnswer = eventData.answer || eventData.text || eventData.message || eventData.content;
                  if (eventData.conversation_id) {
                    conversationId = eventData.conversation_id;
                  }
                }

                // 如果是message事件，提取消息内容
                if (eventData.event === 'message') {
                  const messageContent = eventData.message || eventData.content || eventData.text || eventData.answer;
                  if (messageContent) {
                    // 累积消息内容
                    accumulatedContent += messageContent;
                  }
                }

                // 如果是text_generation事件，提取生成的文本
                if (eventData.event === 'text_generation') {
                  const textContent = eventData.text || eventData.content || eventData.message || eventData.answer;
                  if (textContent) {
                    accumulatedContent += textContent;
                  }
                }
              } catch (parseError) {
                console.error('解析流式响应行失败:', parseError, '行内容:', line.substring(0, 100));
              }
            }
          }

          console.log(`解析了 ${messages.length} 条消息`);

          // 如果有累积的内容，使用它
          if (accumulatedContent) {
            console.log('使用累积的消息内容:', accumulatedContent.substring(0, 100) + '...');
            extractedAnswer = accumulatedContent;
          }
          // 如果找到了最终答案，使用它
          else if (finalAnswer) {
            console.log('找到最终答案:', finalAnswer.substring(0, 100) + '...');
            extractedAnswer = finalAnswer;
          } else if (messages.length > 0) {
            // 如果没有找到message_end事件，但有其他消息，尝试从最后一条消息中提取内容
            const lastMessage = messages[messages.length - 1];
            console.log('使用最后一条消息:', JSON.stringify(lastMessage).substring(0, 100));

            extractedAnswer = lastMessage.answer || lastMessage.text || lastMessage.message || lastMessage.content || '无法提取回复内容';
          } else {
            // 如果没有找到任何有效消息，使用默认消息
            console.log('未找到有效消息');
            extractedAnswer = '您的请求已处理，但无法提取回复内容。请稍后再试。';
          }
        } else {
          console.log('无法获取原始流式数据');
          extractedAnswer = '无法获取响应数据。请稍后再试。';
        }
      }
      // 检查响应数据类型
      else if (typeof response.data === 'string') {
        try {
          // 尝试解析JSON字符串
          const jsonData = JSON.parse(response.data);
          console.log('解析JSON字符串:', JSON.stringify(jsonData).substring(0, 100));

          if (jsonData.event === 'message_end') {
            extractedAnswer = jsonData.answer || jsonData.text || '处理完成，但无法提取回复内容';
            if (jsonData.conversation_id) {
              conversationId = jsonData.conversation_id;
            }
          } else {
            extractedAnswer = '您的请求正在处理中，请稍候...';
          }
        } catch (e) {
          console.error('解析JSON字符串失败:', e);
          // 如果不是有效的JSON，直接使用字符串
          extractedAnswer = response.data;
        }
      }
      // 如果是对象，检查是否包含event字段
      else if (typeof response.data === 'object') {
        // 如果响应包含event字段，说明是流式响应的事件
        if (response.data.event) {
          console.log('检测到事件对象:', response.data.event);

          if (response.data.event === 'message_end') {
            extractedAnswer = response.data.answer || response.data.text || '处理完成，但无法提取回复内容';
          } else if (response.data.event === 'message_start') {
            extractedAnswer = '开始生成回复，请稍候...';
          } else if (response.data.event === 'workflow_started' || response.data.event === 'node_started') {
            extractedAnswer = '正在处理您的请求，请稍候...';
          } else {
            extractedAnswer = `收到事件: ${response.data.event}，请稍候...`;
          }

          // 尝试从事件数据中提取会话ID
          if (response.data.conversation_id) {
            conversationId = response.data.conversation_id;
          } else if (response.data.data && response.data.data.conversation_id) {
            conversationId = response.data.data.conversation_id;
          }
        }
        // 如果响应包含answer或text字段，直接使用
        else if (response.data.answer) {
          console.log('检测到answer字段');
          extractedAnswer = response.data.answer;
          if (response.data.conversation_id) {
            conversationId = response.data.conversation_id;
          }
        } else if (response.data.text) {
          console.log('检测到text字段');
          extractedAnswer = response.data.text;
          if (response.data.conversation_id) {
            conversationId = response.data.conversation_id;
          }
        } else if (response.data.message) {
          console.log('检测到message字段');
          extractedAnswer = response.data.message;
          if (response.data.conversation_id) {
            conversationId = response.data.conversation_id;
          }
        } else {
          // 如果无法识别响应格式，返回友好的消息
          console.log('数据查询助手查询 - 无法识别的响应格式:', JSON.stringify(response.data).substring(0, 200) + '...');
          extractedAnswer = '您的请求已处理，但返回的数据格式无法直接显示。请尝试简化您的问题或稍后再试。';
        }
      }

      // 返回处理后的响应
      res.status(200).json({
        success: true,
        data: {
          answer: extractedAnswer,
          conversation_id: conversationId
        }
      });
    } else {
      // 非流式响应，直接返回
      console.log('数据查询助手查询 - 返回标准响应');
      res.status(200).json({
        success: true,
        data: response.data
      });
    }
  } catch (error) {
    console.error('数据查询助手查询 - 处理失败:', error.message);

    // 记录更详细的错误信息
    if (error.response) {
      // 服务器返回了错误状态码
      console.error('数据查询助手查询 - 错误响应:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('数据查询助手查询 - 请求已发送但无响应:', {
        method: error.config?.method,
        url: error.config?.url,
        timeout: error.config?.timeout
      });
    }

    res.status(500).json({
      success: false,
      message: '查询数据查询助手失败',
      error: error.message
    });
  }
};

/**
 * 设置数据查询助手知识库
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.setDataAssistantKnowledgeBase = async (req, res) => {
  try {
    const { knowledge_base_id } = req.body;

    if (!knowledge_base_id) {
      return res.status(400).json({
        success: false,
        message: '知识库ID不能为空'
      });
    }

    // 查询知识库
    const knowledgeBase = await KnowledgeBase.findByPk(knowledge_base_id);

    if (!knowledgeBase) {
      return res.status(404).json({
        success: false,
        message: '知识库不存在'
      });
    }

    // 检查访问权限
    let hasPermission = false;

    // 管理员可以访问任何知识库
    if (req.user.role === 'admin') {
      hasPermission = true;
    }
    // 系统知识库所有人可以访问
    else if (knowledgeBase.type === 'system') {
      hasPermission = true;
    }
    // 创建者可以访问自己的知识库
    else if (knowledgeBase.creator_id === req.user.id) {
      hasPermission = true;
    }
    // 检查是否有访问权限
    else {
      const { KnowledgeBaseAccess } = require('../models');
      const access = await KnowledgeBaseAccess.findOne({
        where: {
          knowledge_base_id,
          user_id: req.user.id
        }
      });

      if (access) {
        hasPermission = true;
      }
    }

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '您没有权限访问该知识库'
      });
    }

    // 将知识库ID存储在会话中
    if (!req.session) {
      req.session = {};
    }

    if (!req.session.dataAssistant) {
      req.session.dataAssistant = {};
    }

    req.session.dataAssistant.knowledgeBaseId = knowledge_base_id;

    res.status(200).json({
      success: true,
      message: '设置数据查询助手知识库成功',
      data: {
        knowledge_base_id,
        knowledge_base_name: knowledgeBase.name
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '设置数据查询助手知识库失败',
      error: error.message
    });
  }
};

/**
 * 获取AI助手对话历史
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getConversationHistory = async (req, res) => {
  try {
    const { assistantType } = req.params;
    const { conversation_id } = req.query;

    // 验证助手类型
    if (!['personal', 'data-query'].includes(assistantType)) {
      return res.status(400).json({
        success: false,
        message: '无效的助手类型'
      });
    }

    // 查询AI助手
    const assistant = await AIAssistant.findOne({
      where: {
        type: assistantType,
        status: 'active'
      }
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        message: 'AI助手不存在或未激活'
      });
    }

    // 如果没有提供会话ID，则获取会话列表
    if (!conversation_id) {
      const response = await axios.get(
        `${assistant.api_endpoint}/conversations`,
        {
          headers: {
            'Authorization': `Bearer ${assistant.api_key}`
          },
          params: {
            user: req.user.id.toString(),
            limit: 20,
            first_id: null
          }
        }
      );

      return res.status(200).json({
        success: true,
        data: response.data
      });
    }

    // 获取特定会话的消息历史
    const response = await axios.get(
      `${assistant.api_endpoint}/messages`,
      {
        headers: {
          'Authorization': `Bearer ${assistant.api_key}`
        },
        params: {
          conversation_id,
          user: req.user.id.toString(),
          limit: 100,
          first_id: null
        }
      }
    );

    res.status(200).json({
      success: true,
      data: response.data
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取AI助手对话历史失败',
      error: error.message
    });
  }
};

/**
 * 删除AI助手
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteAIAssistant = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('删除AI助手 - 请求参数:', { id, user: req.user ? req.user.username : '未登录' });

    // 查询AI助手
    const assistant = await AIAssistant.findByPk(id);

    if (!assistant) {
      return res.status(404).json({
        success: false,
        message: 'AI助手不存在'
      });
    }

    // 注释掉系统预设助手检查，允许删除所有助手
    // if (assistant.is_system) {
    //   return res.status(400).json({
    //     success: false,
    //     message: '系统预设助手不能删除'
    //   });
    // }

    // 记录删除前的助手信息
    console.log('删除AI助手 - 删除前:', {
      id: assistant.id,
      name: assistant.name,
      type: assistant.type,
      status: assistant.status
    });

    // 删除AI助手
    await assistant.destroy();

    res.status(200).json({
      success: true,
      message: 'AI助手删除成功'
    });
  } catch (error) {
    console.error('删除AI助手失败:', error);
    res.status(500).json({
      success: false,
      message: '删除AI助手失败',
      error: error.message
    });
  }
};

/**
 * 查询AI研究助手
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.queryResearchAssistant = async (req, res) => {
  try {
    console.log('AI研究助手查询 - 请求体:', req.body);
    const { query, conversation_id } = req.body;

    if (!query) {
      console.log('AI研究助手查询 - 错误: 查询内容为空');
      return res.status(400).json({
        success: false,
        message: '查询内容不能为空'
      });
    }

    // 获取请求中的助手ID
    const assistantId = req.params.id;
    console.log('AI研究助手查询 - 助手ID:', assistantId);

    // 查询条件
    const whereCondition = {
      type: 'assistant'
    };

    // 如果提供了助手ID，则查询特定ID的助手
    if (assistantId) {
      whereCondition.id = assistantId;
    }

    console.log('AI研究助手查询 - 查询条件:', whereCondition);

    // 查询AI研究助手
    const assistant = await AIAssistant.findOne({
      where: whereCondition
    });

    if (!assistant) {
      console.log('AI研究助手查询 - 错误: 助手不存在或未激活');
      return res.status(404).json({
        success: false,
        message: 'AI研究助手不存在或未激活'
      });
    }

    // 验证助手配置是否完整
    if (!assistant.api_key || !assistant.api_endpoint) {
      console.log('AI研究助手查询 - 错误: 助手配置不完整', {
        hasApiKey: !!assistant.api_key,
        hasApiEndpoint: !!assistant.api_endpoint
      });
      return res.status(500).json({
        success: false,
        message: 'AI研究助手配置不完整，请联系管理员检查配置'
      });
    }

    console.log('AI研究助手查询 - 找到助手:', {
      id: assistant.id,
      name: assistant.name,
      type: assistant.type,
      status: assistant.status,
      api_endpoint: assistant.api_endpoint,
      api_key: assistant.api_key ? '已设置' : '未设置',
      app_id: assistant.app_id,
      app_code: assistant.app_code
    });

    // 检查API配置是否完整
    if (!assistant.api_key || !assistant.api_endpoint) {
      console.log('AI研究助手查询 - 错误: API配置不完整', {
        api_key: assistant.api_key ? '已设置' : '未设置',
        api_endpoint: assistant.api_endpoint ? '已设置' : '未设置'
      });

      // 使用默认配置
      console.log('AI研究助手查询 - 使用默认配置');
      assistant.api_key = 'app-cQtzQZWksPXMJqpvG8at1o4z';
      assistant.api_endpoint = 'http://ai.glab.vip/v1';
    }

    console.log('AI研究助手查询 - 准备调用外部API:', assistant.api_endpoint);

    // 直接使用配置的API端点
    let apiUrl = assistant.api_endpoint;

    // 确保URL格式正确
    if (apiUrl.endsWith('/')) {
      apiUrl = apiUrl.slice(0, -1);
    }

    // 如果URL不包含chat-messages，添加它
    if (!apiUrl.includes('chat-messages')) {
      apiUrl = `${apiUrl}/chat-messages`;
    }

    console.log('AI研究助手查询 - 最终API URL:', apiUrl);

    console.log('AI研究助手查询 - 使用API URL:', apiUrl);

    // 构建请求体
    const apiRequestBody = {
      inputs: {
        // 添加助手名称作为上下文信息
        assistant_name: assistant.name
      },
      query,
      // 始终使用流式模式，让后端解析流式响应
      response_mode: 'streaming',
      conversation_id,
      user: req.user ? req.user.id.toString() : 'anonymous'
    };

    // 添加app_id和app_code字段
    if (assistant.app_id) {
      apiRequestBody.app_id = assistant.app_id;
    }
    if (assistant.app_code) {
      apiRequestBody.app_code = assistant.app_code;
    }

    console.log('AI研究助手查询 - 添加app_id和app_code字段:', {
      app_id: assistant.app_id,
      app_code: assistant.app_code
    });

    console.log('AI研究助手查询 - API请求体:', JSON.stringify(apiRequestBody, null, 2));
    console.log('AI研究助手查询 - API密钥前10个字符:', assistant.api_key.substring(0, 10) + '...');

    // 发送请求
    let response;

    try {
      console.log(`AI研究助手查询 - 开始调用API: ${apiUrl} - ${new Date().toISOString()}`);
      console.log('AI研究助手查询 - 请求体:', JSON.stringify(apiRequestBody, null, 2));

      response = await axios.post(
        apiUrl,
        apiRequestBody,
        {
          headers: {
            'Authorization': `Bearer ${assistant.api_key}`,
            'Content-Type': 'application/json'
          },
          timeout: 3600000 // 增加到60分钟超时
        }
      );

      console.log(`AI研究助手查询 - API调用完成: ${apiUrl} - ${new Date().toISOString()}`);
    } catch (error) {
      console.error(`AI研究助手查询 - API调用失败:`, error.message);

      // 记录更详细的错误信息
      if (error.response) {
        // 服务器返回了错误状态码
        console.error(`AI研究助手查询 - 错误响应:`, {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        });
      } else if (error.request) {
        // 请求已发送但没有收到响应
        console.error(`AI研究助手查询 - 请求已发送但无响应:`, {
          method: error.config?.method,
          url: error.config?.url,
          timeout: error.config?.timeout
        });
      }

      // 根据错误类型返回不同的响应
      if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        // 超时错误 - 告知前端这可能是因为Dify正在处理复杂问题
        return res.status(500).json({
          success: false,
          message: '调用AI研究助手API超时，Dify可能正在处理复杂问题，请稍后再试',
          error: error.message,
          errorType: 'timeout'
        });
      } else if (error.response && error.response.status === 500) {
        // 服务器内部错误 - 可能是Dify正在处理
        return res.status(500).json({
          success: false,
          message: '调用AI研究助手API时服务器返回内部错误，Dify可能正在处理复杂问题，请稍后再试',
          error: error.message,
          errorType: 'server_error'
        });
      } else {
        // 其他错误
        return res.status(500).json({
          success: false,
          message: '调用AI研究助手API失败',
          error: error.message,
          errorType: 'general_error'
        });
      }
    }

    console.log('AI研究助手查询 - API调用成功');

    // 所有助手都使用流式响应处理逻辑
    if (response.data) {
      console.log('AI研究助手查询 - 处理流式响应');
      console.log('响应数据类型:', typeof response.data);

      // 打印完整的响应数据以便调试
      const responseStr = JSON.stringify(response.data);
      console.log('完整响应数据:', responseStr);

      // 尝试从流式响应中提取有用信息
      let extractedAnswer = '';
      let conversationId = conversation_id;
      let finalAnswer = null;

      // 直接解析流式响应，不使用阻塞模式
      console.log('直接解析流式响应数据');

      // 首先检查是否是Dify的流式响应格式
      const isDifyStreamingResponse =
        (typeof response.data === 'string' && response.data.includes('data:')) ||
        (response.data.data && typeof response.data.data === 'string' && response.data.data.includes('data:'));

      if (isDifyStreamingResponse) {
        console.log('检测到Dify流式响应格式');

        // 获取原始流式数据
        const rawStreamData = typeof response.data === 'string'
          ? response.data
          : (response.data.data && typeof response.data.data === 'string' ? response.data.data : null);

        if (rawStreamData) {
          console.log('原始流式数据前200字符:', rawStreamData.substring(0, 200));

          // 解析流式响应
          const lines = rawStreamData.split('\n').filter(line => line.trim());
          console.log(`找到 ${lines.length} 行数据`);

          // 存储所有消息
          const messages = [];
          // 存储累积的消息内容
          let accumulatedContent = '';

          // 解析每一行
          for (const line of lines) {
            if (line.startsWith('data:')) {
              try {
                const jsonStr = line.substring(5).trim();
                // 跳过空行
                if (!jsonStr || jsonStr === '[DONE]') continue;

                const eventData = JSON.parse(jsonStr);
                messages.push(eventData);

                // 如果是message_end事件，提取最终答案
                if (eventData.event === 'message_end') {
                  finalAnswer = eventData.answer || eventData.text || eventData.message || eventData.content;
                  if (eventData.conversation_id) {
                    conversationId = eventData.conversation_id;
                  }
                }

                // 如果是message事件，提取消息内容
                if (eventData.event === 'message') {
                  const messageContent = eventData.message || eventData.content || eventData.text || eventData.answer;
                  if (messageContent) {
                    // 累积消息内容
                    accumulatedContent += messageContent;
                  }
                }

                // 如果是text_generation事件，提取生成的文本
                if (eventData.event === 'text_generation') {
                  const textContent = eventData.text || eventData.content || eventData.message || eventData.answer;
                  if (textContent) {
                    accumulatedContent += textContent;
                  }
                }
              } catch (parseError) {
                console.error('解析流式响应行失败:', parseError, '行内容:', line.substring(0, 100));
              }
            }
          }

          console.log(`解析了 ${messages.length} 条消息`);

          // 如果有累积的内容，使用它
          if (accumulatedContent) {
            console.log('使用累积的消息内容:', accumulatedContent.substring(0, 100) + '...');
            extractedAnswer = accumulatedContent;
          }
          // 如果找到了最终答案，使用它
          else if (finalAnswer) {
            console.log('找到最终答案:', finalAnswer.substring(0, 100) + '...');
            extractedAnswer = finalAnswer;
          } else if (messages.length > 0) {
            // 如果没有找到message_end事件，但有其他消息，尝试从最后一条消息中提取内容
            const lastMessage = messages[messages.length - 1];
            console.log('使用最后一条消息:', JSON.stringify(lastMessage).substring(0, 100));

            extractedAnswer = lastMessage.answer || lastMessage.text || lastMessage.message || lastMessage.content || '无法提取回复内容';
          } else {
            // 如果没有找到任何有效消息，使用默认消息
            console.log('未找到有效消息');
            extractedAnswer = '您的请求已处理，但无法提取回复内容。请稍后再试。';
          }
        } else {
          console.log('无法获取原始流式数据');
          extractedAnswer = '无法获取响应数据。请稍后再试。';
        }
      }
      // 检查响应数据类型
      else if (typeof response.data === 'string') {
        try {
          // 尝试解析JSON字符串
          const jsonData = JSON.parse(response.data);
          console.log('解析JSON字符串:', JSON.stringify(jsonData).substring(0, 100));

          if (jsonData.event === 'message_end') {
            extractedAnswer = jsonData.answer || jsonData.text || '处理完成，但无法提取回复内容';
            if (jsonData.conversation_id) {
              conversationId = jsonData.conversation_id;
            }
          } else {
            extractedAnswer = '您的请求正在处理中，请稍候...';
          }
        } catch (e) {
          console.error('解析JSON字符串失败:', e);
          // 如果不是有效的JSON，直接使用字符串
          extractedAnswer = response.data;
        }
      }
      // 如果是对象，检查是否包含event字段
      else if (typeof response.data === 'object') {
        // 如果响应包含event字段，说明是流式响应的事件
        if (response.data.event) {
          console.log('检测到事件对象:', response.data.event);

          if (response.data.event === 'message_end') {
            extractedAnswer = response.data.answer || response.data.text || '处理完成，但无法提取回复内容';
          } else if (response.data.event === 'message_start') {
            extractedAnswer = '开始生成回复，请稍候...';
          } else if (response.data.event === 'workflow_started' || response.data.event === 'node_started') {
            extractedAnswer = '正在处理您的请求，请稍候...';
          } else {
            extractedAnswer = `收到事件: ${response.data.event}，请稍候...`;
          }

          // 尝试从事件数据中提取会话ID
          if (response.data.conversation_id) {
            conversationId = response.data.conversation_id;
          } else if (response.data.data && response.data.data.conversation_id) {
            conversationId = response.data.data.conversation_id;
          }
        }
        // 如果响应包含answer或text字段，直接使用
        else if (response.data.answer) {
          console.log('检测到answer字段');
          extractedAnswer = response.data.answer;
          if (response.data.conversation_id) {
            conversationId = response.data.conversation_id;
          }
        } else if (response.data.text) {
          console.log('检测到text字段');
          extractedAnswer = response.data.text;
          if (response.data.conversation_id) {
            conversationId = response.data.conversation_id;
          }
        } else if (response.data.message) {
          console.log('检测到message字段');
          extractedAnswer = response.data.message;
          if (response.data.conversation_id) {
            conversationId = response.data.conversation_id;
          }
        } else {
          // 如果无法识别响应格式，返回友好的消息
          console.log('AI研究助手查询 - 无法识别的响应格式:', JSON.stringify(response.data).substring(0, 200) + '...');
          extractedAnswer = '您的请求已处理，但返回的数据格式无法直接显示。请尝试简化您的问题或稍后再试。';
        }
      }

      // 返回处理后的响应
      res.status(200).json({
        success: true,
        data: {
          answer: extractedAnswer,
          conversation_id: conversationId
        }
      });
    } else {
      // 非流式响应，直接返回
      console.log('AI研究助手查询 - 返回标准响应');
      res.status(200).json({
        success: true,
        data: response.data
      });
    }
  } catch (error) {
    console.error('AI研究助手查询 - 处理失败:', error.message);
    res.status(500).json({
      success: false,
      message: '查询AI研究助手失败',
      error: error.message
    });
  }
};

/**
 * 异步查询AI研究助手
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.asyncQueryResearchAssistant = async (req, res) => {
  try {
    console.log('异步AI研究助手查询 - 请求体:', req.body);
    const { query, conversation_id } = req.body;
    const userId = req.user.id;

    // 验证请求参数
    if (!query) {
      console.log('异步AI研究助手查询 - 错误: 查询内容为空');
      return res.status(400).json({
        success: false,
        message: '查询内容不能为空'
      });
    }

    // 获取AI研究助手配置
    const assistant = await AIAssistant.findOne({
      where: {
        type: 'assistant',
        status: 'active'
      }
    });

    if (!assistant) {
      console.log('异步AI研究助手查询 - 错误: 助手不存在或未激活');
      return res.status(404).json({
        success: false,
        message: 'AI研究助手不存在或未激活'
      });
    }

    // 生成一个唯一的任务ID
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    // 将任务信息存储在内存中（实际生产环境应使用Redis或数据库）
    if (!global.aiTasks) {
      global.aiTasks = {};
    }

    global.aiTasks[taskId] = {
      status: 'pending',
      query,
      userId,
      conversation_id,
      assistant,
      createdAt: new Date(),
      result: null,
      error: null
    };

    console.log(`异步AI研究助手查询 - 创建任务: ${taskId}`);

    // 启动异步处理任务
    setTimeout(() => {
      processAIResearchTask(taskId);
    }, 0);

    // 立即返回任务ID
    res.status(202).json({
      success: true,
      message: '请求已接受，正在处理中',
      data: {
        task_id: taskId,
        status: 'pending'
      }
    });
  } catch (error) {
    console.error('创建异步AI研究助手查询任务失败:', error);
    res.status(500).json({
      success: false,
      message: '创建异步AI研究助手查询任务失败',
      error: error.message
    });
  }
};

/**
 * 异步处理AI研究任务
 * @param {string} taskId - 任务ID
 */
async function processAIResearchTask(taskId) {
  if (!global.aiTasks || !global.aiTasks[taskId]) {
    console.error(`任务不存在: ${taskId}`);
    return;
  }

  const task = global.aiTasks[taskId];

  try {
    // 更新任务状态
    task.status = 'processing';
    console.log(`处理AI研究任务 - 开始处理: ${taskId}`);

    // 直接使用配置的API端点
    let apiUrl = task.assistant.api_endpoint;

    // 确保URL格式正确
    if (apiUrl.endsWith('/')) {
      apiUrl = apiUrl.slice(0, -1);
    }

    // 如果URL不包含chat-messages，添加它
    if (!apiUrl.includes('chat-messages')) {
      apiUrl = `${apiUrl}/chat-messages`;
    }

    // 构建请求体
    const apiRequestBody = {
      inputs: {
        // 添加助手名称作为上下文信息
        assistant_name: task.assistant.name
      },
      query: task.query,
      response_mode: 'streaming', // 使用streaming模式，与其他助手保持一致
      user: task.userId.toString()
    };

    // 如果有会话ID，添加到请求中
    if (task.conversation_id) {
      apiRequestBody.conversation_id = task.conversation_id;
    }

    // 添加app_id和app_code字段
    if (task.assistant.app_id) {
      apiRequestBody.app_id = task.assistant.app_id;
    }
    if (task.assistant.app_code) {
      apiRequestBody.app_code = task.assistant.app_code;
    }

    console.log(`处理AI研究任务 - 调用API: ${apiUrl}`);
    console.log('处理AI研究任务 - 请求体:', JSON.stringify(apiRequestBody, null, 2));

    // 调用Dify API
    const response = await axios.post(
      apiUrl,
      apiRequestBody,
      {
        headers: {
          'Authorization': `Bearer ${task.assistant.api_key}`,
          'Content-Type': 'application/json'
        },
        timeout: 3600000 // 60分钟超时
      }
    );

    console.log(`处理AI研究任务 - API调用成功: ${taskId}`);

    // 更新任务状态和结果
    task.status = 'completed';
    task.result = response.data;
    task.completedAt = new Date();

    // 任务完成后保留结果30分钟，然后清理
    setTimeout(() => {
      if (global.aiTasks && global.aiTasks[taskId]) {
        delete global.aiTasks[taskId];
        console.log(`已清理任务: ${taskId}`);
      }
    }, 30 * 60 * 1000); // 30分钟
  } catch (error) {
    console.error(`处理AI研究任务 - API调用失败: ${taskId}`, error.message);

    // 更新任务状态和错误信息
    task.status = 'failed';
    task.error = {
      message: error.message,
      code: error.code,
      response: error.response ? {
        status: error.response.status,
        data: error.response.data
      } : null
    };

    // 确定错误类型
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      task.errorType = 'timeout';
    } else if (error.response && error.response.status === 500) {
      task.errorType = 'server_error';
    } else {
      task.errorType = 'general_error';
    }
  }
}

/**
 * 获取AI研究助手查询任务状态
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getResearchTaskStatus = async (req, res) => {
  try {
    const { taskId } = req.params;

    // 检查任务是否存在
    if (!global.aiTasks || !global.aiTasks[taskId]) {
      return res.status(404).json({
        success: false,
        message: '任务不存在或已过期'
      });
    }

    const task = global.aiTasks[taskId];

    // 根据任务状态返回不同的响应
    if (task.status === 'completed') {
      return res.status(200).json({
        success: true,
        data: {
          status: 'completed',
          result: task.result
        }
      });
    } else if (task.status === 'failed') {
      return res.status(500).json({
        success: false,
        message: '任务处理失败',
        error: task.error.message,
        errorType: task.errorType
      });
    } else {
      // 任务仍在处理中
      return res.status(200).json({
        success: true,
        data: {
          status: task.status,
          message: '任务正在处理中'
        }
      });
    }
  } catch (error) {
    console.error('获取AI研究助手查询任务状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务状态失败',
      error: error.message
    });
  }
};

/**
 * 清除AI助手对话历史
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.clearConversationHistory = async (req, res) => {
  try {
    const { assistantType } = req.params;
    const { conversation_id } = req.body;

    // 验证助手类型
    if (!['personal', 'data-query'].includes(assistantType)) {
      return res.status(400).json({
        success: false,
        message: '无效的助手类型'
      });
    }

    // 查询AI助手
    const assistant = await AIAssistant.findOne({
      where: {
        type: assistantType,
        status: 'active'
      }
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        message: 'AI助手不存在或未激活'
      });
    }

    // 如果提供了会话ID，则删除特定会话
    if (conversation_id) {
      await axios.delete(
        `${assistant.api_endpoint}/conversations/${conversation_id}`,
        {
          headers: {
            'Authorization': `Bearer ${assistant.api_key}`
          },
          params: {
            user: req.user.id.toString()
          }
        }
      );

      return res.status(200).json({
        success: true,
        message: '会话删除成功'
      });
    }

    // 否则删除所有会话
    await axios.delete(
      `${assistant.api_endpoint}/conversations`,
      {
        headers: {
          'Authorization': `Bearer ${assistant.api_key}`
        },
        params: {
          user: req.user.id.toString()
        }
      }
    );

    res.status(200).json({
      success: true,
      message: '所有会话删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '清除AI助手对话历史失败',
      error: error.message
    });
  }
};
