/**
 * 默认AI助手种子数据
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 获取管理员用户ID
    const [users] = await queryInterface.sequelize.query(
      `SELECT id FROM users WHERE username = 'admin' LIMIT 1`
    );
    
    if (users.length === 0) {
      console.error('未找到管理员用户，请先运行用户种子数据');
      return;
    }

    const adminId = users[0].id;

    await queryInterface.bulkInsert('ai_assistants', [
      {
        name: '个人专题助手',
        type: 'personal',
        description: '用于个人专题页面的AI助手，可以回答关于个人生平、家族历史等问题',
        api_key: 'your_dify_api_key',
        api_endpoint: 'https://api.dify.ai/v1',
        app_id: 'app_personal',
        app_code: 'personal_assistant',
        initial_message: '您好，我是个人专题助手，有什么可以帮助您的吗？',
        is_system: true,
        status: 'active',
        creator_id: adminId,
        last_updated_by: adminId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '数据查询助手',
        type: 'data-query',
        description: '用于数据查询页面的AI助手，可以帮助用户查询和分析数据',
        api_key: 'your_dify_api_key',
        api_endpoint: 'https://api.dify.ai/v1',
        app_id: 'app_data_query',
        app_code: 'data_query_assistant',
        initial_message: '您好，我是数据查询助手，请问您想查询什么数据？',
        is_system: true,
        status: 'active',
        creator_id: adminId,
        last_updated_by: adminId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'AI研究助手',
        type: 'assistant',
        description: '用于研究和分析的AI助手，可以帮助用户进行深入研究',
        api_key: 'your_dify_api_key',
        api_endpoint: 'https://api.dify.ai/v1',
        app_id: 'app_research',
        app_code: 'research_assistant',
        tags: '研究,分析,学术',
        initial_message: '您好，我是AI研究助手，有什么研究问题需要我协助吗？',
        is_system: true,
        status: 'active',
        creator_id: adminId,
        last_updated_by: adminId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '知识库文件分析助手',
        type: 'knowledge-file',
        description: '用于分析知识库文件的AI助手，可以提取文件摘要和详细描述',
        api_key: 'your_dify_api_key',
        api_endpoint: 'https://api.dify.ai/v1',
        app_id: 'app_file_analysis',
        app_code: 'file_analysis_assistant',
        upload_api_path: '/files/upload',
        analysis_api_path: '/files/analyze',
        initial_message: '我将帮助您分析文件内容',
        is_system: true,
        status: 'active',
        creator_id: adminId,
        last_updated_by: adminId,
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('ai_assistants', {
      is_system: true
    }, {});
  }
};
