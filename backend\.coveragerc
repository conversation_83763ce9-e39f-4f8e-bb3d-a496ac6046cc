# 测试覆盖率配置文件

[run]
# 忽略的文件和目录
omit =
    */node_modules/*
    */config/*
    */models/index.js
    */app.js
    */tests/*

[report]
# 排除的行
exclude_lines =
    # 跳过防御性编程的断言
    pragma: no cover
    
    # 跳过调试代码
    def __repr__
    if self.debug
    
    # 跳过未运行的代码
    if 0:
    if __name__ == .__main__.:
    
    # 跳过导入错误处理
    except ImportError:
    
    # 跳过未实现的代码
    raise NotImplementedError
    
    # 跳过无法测试的代码
    pass
    
    # 跳过抽象方法
    @(abc\.)?abstractmethod
