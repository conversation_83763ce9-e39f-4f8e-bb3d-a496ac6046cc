"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { KnowledgeFileReviewProps, KnowledgeFileReviewFields } from './types'
import { KnowledgeFileDetail } from './KnowledgeFileDetail'

/**
 * 知识库文件审核组件
 * 
 * 用于审核知识库文件
 */
export function KnowledgeFileReview({ loading, file, onSubmit, onCancel }: KnowledgeFileReviewProps) {
  const [formData, setFormData] = useState<KnowledgeFileReviewFields>({
    status: 'approved',
    reject_reason: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 处理状态变化
  const handleStatusChange = (status: 'approved' | 'rejected') => {
    setFormData({ ...formData, status })
  }

  // 处理文本输入
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })
    
    // 清除错误
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' })
    }
  }

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    
    // 验证驳回原因（仅在驳回时必填）
    if (formData.status === 'rejected' && !formData.reject_reason) {
      newErrors.reject_reason = '请输入驳回原因'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      onSubmit(formData)
    }
  }

  return (
    <div className="space-y-6">
      <KnowledgeFileDetail file={file} onClose={onCancel} />
      
      <div className="border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">审核决定</h3>
        
        <div className="flex gap-4 mb-4">
          <Button
            type="button"
            variant={formData.status === 'approved' ? 'default' : 'outline'}
            className={formData.status === 'approved' ? 'bg-green-600 hover:bg-green-700' : ''}
            onClick={() => handleStatusChange('approved')}
            disabled={loading}
          >
            通过
          </Button>
          <Button
            type="button"
            variant={formData.status === 'rejected' ? 'default' : 'outline'}
            className={formData.status === 'rejected' ? 'bg-red-600 hover:bg-red-700' : ''}
            onClick={() => handleStatusChange('rejected')}
            disabled={loading}
          >
            驳回
          </Button>
        </div>
        
        {formData.status === 'rejected' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              驳回原因
            </label>
            <Textarea
              name="reject_reason"
              value={formData.reject_reason}
              onChange={handleTextChange}
              placeholder="请输入驳回原因"
              className={`min-h-[100px] ${errors.reject_reason ? 'border-red-500' : ''}`}
              disabled={loading}
            />
            {errors.reject_reason && <p className="mt-1 text-xs text-red-500">{errors.reject_reason}</p>}
          </div>
        )}
        
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={onCancel} disabled={loading}>
            取消
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={loading}
            className={formData.status === 'approved' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
          >
            {loading ? '提交中...' : formData.status === 'approved' ? '确认通过' : '确认驳回'}
          </Button>
        </div>
      </div>
    </div>
  )
}
