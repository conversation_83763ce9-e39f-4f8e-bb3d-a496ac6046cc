/**
 * 自定义测试报告生成脚本
 * 
 * 用于生成美观的HTML测试报告
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const Handlebars = require('handlebars');

// 报告目录
const REPORT_DIR = path.join(__dirname, '..', 'reports');
const COVERAGE_DIR = path.join(__dirname, '..', 'coverage');
const TEMPLATE_DIR = path.join(__dirname, 'templates');
const CUSTOM_REPORT_FILE = path.join(REPORT_DIR, 'custom-coverage-report.html');

// 确保报告目录存在
if (!fs.existsSync(REPORT_DIR)) {
  fs.mkdirSync(REPORT_DIR, { recursive: true });
}

// 运行测试并生成覆盖率报告
console.log('运行测试并生成覆盖率报告...');
try {
  execSync('npx jest --coverage', { stdio: 'inherit' });
} catch (error) {
  console.error('测试失败:', error.message);
  process.exit(1);
}

// 检查覆盖率报告是否生成
const COVERAGE_SUMMARY_PATH = path.join(COVERAGE_DIR, 'coverage-summary.json');
if (!fs.existsSync(COVERAGE_SUMMARY_PATH)) {
  console.error('覆盖率报告不存在');
  process.exit(1);
}

// 读取覆盖率报告
const coverageSummary = JSON.parse(fs.readFileSync(COVERAGE_SUMMARY_PATH, 'utf8'));
const total = coverageSummary.total;

// 读取模板
const templatePath = path.join(TEMPLATE_DIR, 'coverage-report-template.html');
if (!fs.existsSync(templatePath)) {
  console.error('模板文件不存在');
  process.exit(1);
}

const templateSource = fs.readFileSync(templatePath, 'utf8');
const template = Handlebars.compile(templateSource);

// 获取覆盖率等级
function getCoverageClass(coverage) {
  if (coverage >= 80) return 'green';
  if (coverage >= 50) return 'yellow';
  return 'red';
}

// 处理文件数据
const files = [];
Object.keys(coverageSummary).forEach(key => {
  if (key !== 'total') {
    const file = coverageSummary[key];
    files.push({
      path: key,
      linesCoverage: file.lines.pct.toFixed(2),
      linesCoverageClass: getCoverageClass(file.lines.pct),
      functionsCoverage: file.functions.pct.toFixed(2),
      functionsCoverageClass: getCoverageClass(file.functions.pct),
      branchesCoverage: file.branches.pct.toFixed(2),
      branchesCoverageClass: getCoverageClass(file.branches.pct),
      statementsCoverage: file.statements.pct.toFixed(2),
      statementsCoverageClass: getCoverageClass(file.statements.pct)
    });
  }
});

// 获取覆盖率最低的文件
const lowestLinesCoverage = [...files]
  .sort((a, b) => parseFloat(a.linesCoverage) - parseFloat(b.linesCoverage))
  .slice(0, 5)
  .map(file => ({
    path: file.path,
    coverage: file.linesCoverage,
    coverageClass: file.linesCoverageClass
  }));

const lowestFunctionsCoverage = [...files]
  .sort((a, b) => parseFloat(a.functionsCoverage) - parseFloat(b.functionsCoverage))
  .slice(0, 5)
  .map(file => ({
    path: file.path,
    coverage: file.functionsCoverage,
    coverageClass: file.functionsCoverageClass
  }));

const lowestBranchesCoverage = [...files]
  .sort((a, b) => parseFloat(a.branchesCoverage) - parseFloat(b.branchesCoverage))
  .slice(0, 5)
  .map(file => ({
    path: file.path,
    coverage: file.branchesCoverage,
    coverageClass: file.branchesCoverageClass
  }));

// 生成报告数据
const reportData = {
  timestamp: new Date().toLocaleString(),
  linesCoverage: total.lines.pct.toFixed(2),
  linesCoverageClass: getCoverageClass(total.lines.pct),
  functionsCoverage: total.functions.pct.toFixed(2),
  functionsCoverageClass: getCoverageClass(total.functions.pct),
  branchesCoverage: total.branches.pct.toFixed(2),
  branchesCoverageClass: getCoverageClass(total.branches.pct),
  statementsCoverage: total.statements.pct.toFixed(2),
  statementsCoverageClass: getCoverageClass(total.statements.pct),
  files,
  lowestLinesCoverage,
  lowestFunctionsCoverage,
  lowestBranchesCoverage
};

// 生成报告
const reportHtml = template(reportData);
fs.writeFileSync(CUSTOM_REPORT_FILE, reportHtml);

console.log(`自定义覆盖率报告已生成: ${CUSTOM_REPORT_FILE}`);
console.log('\n报告生成完成');
