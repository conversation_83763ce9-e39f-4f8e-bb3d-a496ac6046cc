'use strict';

/**
 * 修改用户表的avatar字段类型
 * 
 * 将avatar字段从VARCHAR(255)修改为TEXT，以支持存储更长的base64编码图片数据
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('users', 'avatar', {
      type: Sequelize.TEXT,
      allowNull: true
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('users', 'avatar', {
      type: Sequelize.STRING(255),
      allowNull: true
    });
  }
};
