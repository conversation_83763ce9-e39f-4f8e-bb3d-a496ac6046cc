/**
 * 数据库全量备份脚本
 * 使用Node.js和MySQL2库连接数据库并执行备份
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

// 创建备份目录
const backupDir = path.join(__dirname, 'mysql', 'backup');
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true });
}

// 获取当前时间戳
const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-');
const backupFile = path.join(backupDir, `hefamily_dev_${timestamp}.sql`);

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'Misaya9987.',
  database: 'hefamily_dev'
};

// 尝试使用不同的连接配置
const connectionConfigs = [
  // 配置1: 从.env文件
  {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'Misaya9987.',
    database: 'hefamily_dev'
  },
  // 配置2: 默认root无密码
  {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '',
    database: 'hefamily_dev'
  },
  // 配置3: 使用.env.example中的配置
  {
    host: 'localhost',
    port: 3306,
    user: 'family_platform_user',
    password: 'change_this_password',
    database: 'family_platform_db'
  },
  // 配置4: 使用Docker配置
  {
    host: 'db',
    port: 3306,
    user: 'family_platform_user',
    password: 'change_this_password',
    database: 'family_platform_db'
  }
];

// 尝试使用mysqldump命令进行备份
async function backupWithMysqldump(config) {
  console.log(`尝试使用mysqldump备份数据库 ${config.database}...`);
  
  const command = `mysqldump -h ${config.host} -P ${config.port} -u ${config.user} ${config.password ? `-p${config.password}` : ''} ${config.database} > "${backupFile}"`;
  
  try {
    await execPromise(command);
    console.log(`数据库备份成功: ${backupFile}`);
    return true;
  } catch (error) {
    console.error(`使用mysqldump备份失败:`, error.message);
    return false;
  }
}

// 使用MySQL2库连接数据库并获取表结构和数据
async function backupWithNodejs(config) {
  console.log(`尝试使用Node.js连接数据库 ${config.database}...`);
  
  let connection;
  try {
    // 连接数据库
    connection = await mysql.createConnection(config);
    console.log('数据库连接成功');
    
    // 获取所有表
    const [tables] = await connection.query('SHOW TABLES');
    console.log(`找到 ${tables.length} 个表`);
    
    // 创建备份文件
    const stream = fs.createWriteStream(backupFile);
    
    // 写入备份文件头
    stream.write(`-- 和富家族研究平台数据库备份\n`);
    stream.write(`-- 创建时间: ${new Date().toISOString()}\n`);
    stream.write(`-- 数据库: ${config.database}\n\n`);
    stream.write(`SET FOREIGN_KEY_CHECKS=0;\n\n`);
    
    // 遍历所有表
    for (const tableRow of tables) {
      const tableName = tableRow[`Tables_in_${config.database}`];
      console.log(`处理表: ${tableName}`);
      
      // 获取表结构
      const [createTable] = await connection.query(`SHOW CREATE TABLE \`${tableName}\``);
      const createTableSql = createTable[0]['Create Table'];
      
      // 写入表结构
      stream.write(`-- 表结构: ${tableName}\n`);
      stream.write(`DROP TABLE IF EXISTS \`${tableName}\`;\n`);
      stream.write(`${createTableSql};\n\n`);
      
      // 获取表数据
      const [rows] = await connection.query(`SELECT * FROM \`${tableName}\``);
      
      if (rows.length > 0) {
        // 写入表数据
        stream.write(`-- 表数据: ${tableName}\n`);
        
        // 分批处理数据，每1000行一批
        const batchSize = 1000;
        for (let i = 0; i < rows.length; i += batchSize) {
          const batch = rows.slice(i, i + batchSize);
          
          // 构建INSERT语句
          let insertSql = `INSERT INTO \`${tableName}\` VALUES `;
          const values = [];
          
          for (const row of batch) {
            const rowValues = [];
            for (const key in row) {
              if (row[key] === null) {
                rowValues.push('NULL');
              } else if (typeof row[key] === 'number') {
                rowValues.push(row[key]);
              } else if (typeof row[key] === 'object' && row[key] instanceof Date) {
                rowValues.push(`'${row[key].toISOString().slice(0, 19).replace('T', ' ')}'`);
              } else if (typeof row[key] === 'object') {
                rowValues.push(`'${JSON.stringify(row[key]).replace(/'/g, "''")}'`);
              } else {
                rowValues.push(`'${String(row[key]).replace(/'/g, "''")}'`);
              }
            }
            values.push(`(${rowValues.join(', ')})`);
          }
          
          insertSql += values.join(',\n') + ';\n\n';
          stream.write(insertSql);
        }
      }
    }
    
    // 写入备份文件尾
    stream.write(`SET FOREIGN_KEY_CHECKS=1;\n`);
    stream.end();
    
    console.log(`数据库备份成功: ${backupFile}`);
    return true;
  } catch (error) {
    console.error(`使用Node.js备份失败:`, error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 主函数
async function main() {
  console.log('开始数据库备份...');
  
  // 首先尝试使用mysqldump
  for (const config of connectionConfigs) {
    console.log(`尝试配置: ${config.user}@${config.host}:${config.port}/${config.database}`);
    
    if (await backupWithMysqldump(config)) {
      console.log('备份成功完成');
      return;
    }
  }
  
  // 如果mysqldump失败，尝试使用Node.js
  console.log('mysqldump备份失败，尝试使用Node.js备份...');
  
  for (const config of connectionConfigs) {
    console.log(`尝试配置: ${config.user}@${config.host}:${config.port}/${config.database}`);
    
    if (await backupWithNodejs(config)) {
      console.log('备份成功完成');
      return;
    }
  }
  
  console.error('所有备份尝试均失败');
}

// 执行主函数
main().catch(error => {
  console.error('备份过程中发生错误:', error);
});
