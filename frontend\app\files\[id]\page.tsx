"use client"

import React, { useEffect } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { FileDetail } from "@/components/file-detail"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "@/components/ui/use-toast"

/**
 * 文件详情页面
 * 
 * 显示单个文件的详细信息
 */
export default function FileDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { isLoggedIn } = useAuth()
  
  // 检查用户是否已登录
  useEffect(() => {
    if (!isLoggedIn) {
      toast({
        title: "需要登录",
        description: "请先登录后再查看文件详情",
        variant: "destructive"
      })
      router.push("/")
    }
  }, [isLoggedIn, router])
  
  // 返回上一页
  const handleBack = () => {
    router.back()
  }
  
  if (!isLoggedIn) {
    return null // 等待重定向
  }
  
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-1 container mx-auto px-4 py-8 mt-16">
        <div className="mb-6">
          <Button
            variant="ghost"
            className="flex items-center text-gray-600 hover:text-gray-900"
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
        </div>
        
        <div className="mb-8">
          <h1 className="text-2xl font-bold">文件详情</h1>
          <p className="text-gray-500 mt-1">查看文件的详细信息</p>
        </div>
        
        <FileDetail fileId={params.id} />
      </main>
      
      <Footer />
    </div>
  )
}
