/**
 * 认证服务测试
 */

import { login, register, logout, isLoggedIn, getCurrentUser } from '@/services/auth-service'
import apiService from '@/services/api-service'

// 模拟API服务
jest.mock('@/services/api-service')

// 模拟localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', { value: localStorageMock })

describe('认证服务', () => {
  // 在每个测试前后重置模拟
  beforeEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  afterEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  // 测试登录功能
  describe('login', () => {
    test('成功登录应该存储token并返回用户信息', async () => {
      // 模拟成功的登录响应
      const mockResponse = {
        success: true,
        message: '登录成功',
        data: {
          token: 'test_token',
          user: {
            id: '1',
            username: 'testuser',
            email: '<EMAIL>',
            role: 'basic_user',
            permissions: ['read:all'],
            created_at: '2023-01-01T00:00:00Z'
          }
        }
      }

      // 模拟API服务的post方法
      apiService.post = jest.fn().mockResolvedValue(mockResponse)

      // 调用登录函数
      const result = await login({
        username: 'testuser',
        password: 'password123'
      })

      // 验证结果
      expect(result).toBeDefined()
      expect(result.token).toBeDefined()
      expect(result.user).toBeDefined()
      expect(localStorageMock.setItem).toHaveBeenCalled()
    })

    test('登录失败应该抛出错误', async () => {
      // 模拟失败的登录响应
      const error = new Error('用户名或密码不正确')

      // 模拟API服务的post方法抛出错误
      apiService.post = jest.fn().mockRejectedValue(error)

      // 调用登录函数并捕获错误
      await expect(login({
        username: 'wronguser',
        password: 'wrongpass'
      })).rejects.toThrow()

      // 验证localStorage未被调用
      expect(localStorageMock.setItem).not.toHaveBeenCalled()
    })
  })

  // 测试注册功能
  describe('register', () => {
    test('成功注册应该返回用户信息', async () => {
      // 模拟成功的注册响应
      const mockResponse = {
        success: true,
        message: '注册成功',
        data: {
          id: '2',
          username: 'newuser',
          email: '<EMAIL>',
          role: 'basic_user'
        }
      }

      // 模拟API服务的post方法
      apiService.post = jest.fn().mockResolvedValue(mockResponse)

      // 调用注册函数
      const result = await register({
        username: 'newuser',
        password: 'Password123!',
        email: '<EMAIL>',
        phone: '13800138000'
      })

      // 验证结果
      expect(result).toBeDefined()
      expect(result.user).toBeDefined()
    })

    test('注册失败应该抛出错误', async () => {
      // 模拟失败的注册响应
      const error = new Error('用户名已存在')

      // 模拟API服务的post方法抛出错误
      apiService.post = jest.fn().mockRejectedValue(error)

      // 调用注册函数并捕获错误
      await expect(register({
        username: 'existinguser',
        password: 'Password123!',
        email: '<EMAIL>',
        phone: '13800138001'
      })).rejects.toThrow()

      // 验证localStorage未被调用
      expect(localStorageMock.setItem).not.toHaveBeenCalled()
    })
  })

  // 测试登出功能
  describe('logout', () => {
    test('登出应该清除localStorage中的认证信息', () => {
      // 调用登出函数
      logout()

      // 验证localStorage的removeItem被调用
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('hefamily_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('hefamily_user_info')
    })
  })

  // 测试检查登录状态
  describe('isLoggedIn', () => {
    test('有token时应该返回true', () => {
      // 模拟localStorage返回token
      localStorageMock.getItem.mockReturnValueOnce('test_token')

      // 调用isLoggedIn函数
      const result = isLoggedIn()

      // 验证结果
      expect(result).toBe(true)
      expect(localStorageMock.getItem).toHaveBeenCalledWith('hefamily_token')
    })

    test('没有token时应该返回false', () => {
      // 模拟localStorage返回null
      localStorageMock.getItem.mockReturnValueOnce(null)

      // 调用isLoggedIn函数
      const result = isLoggedIn()

      // 验证结果
      expect(result).toBe(false)
      expect(localStorageMock.getItem).toHaveBeenCalledWith('hefamily_token')
    })
  })

  // 测试获取当前用户
  describe('getCurrentUser', () => {
    test('有用户信息时应该返回用户对象', async () => {
      // 模拟用户信息
      const mockUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        role: 'basic_user',
        permissions: ['read:all'],
        created_at: '2023-01-01T00:00:00Z'
      }

      // 模拟localStorage返回用户信息
      localStorageMock.getItem.mockReturnValueOnce(JSON.stringify(mockUser))

      // 调用getCurrentUser函数
      const result = await getCurrentUser()

      // 验证结果
      expect(result).toEqual(mockUser)
      expect(localStorageMock.getItem).toHaveBeenCalledWith('hefamily_user_info')
    })

    test('没有用户信息时应该从API获取', async () => {
      // 模拟localStorage返回null
      localStorageMock.getItem.mockReturnValueOnce(null)

      // 模拟API响应
      const mockUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        role: 'basic_user',
        permissions: ['read:all'],
        created_at: '2023-01-01T00:00:00Z'
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockUser)

      // 调用getCurrentUser函数
      const result = await getCurrentUser()

      // 验证结果
      expect(result).toBeDefined()
      expect(localStorageMock.getItem).toHaveBeenCalledWith('hefamily_user_info')
    })
  })
});
