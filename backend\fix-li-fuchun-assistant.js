const { sequelize } = require('./src/models');

async function fixLiFuchunAssistant() {
  try {
    console.log('开始修复李富春专题助手配置...');
    
    // 更新李富春专题助手的配置
    const [updateResult] = await sequelize.query(`
      UPDATE ai_assistants 
      SET 
        api_key = "app-cQtzQZWksPXMJqpvG8at1o4z",
        api_endpoint = "http://ai.glab.vip/v1"
      WHERE 
        id = 7 AND name = "李富春专题助手"
    `);
    
    console.log('更新结果:', updateResult);
    
    // 查询更新后的配置
    const [results] = await sequelize.query('SELECT * FROM ai_assistants WHERE id = 7');
    console.log('更新后的李富春专题助手配置:');
    console.log(JSON.stringify(results, null, 2));
    
    console.log('修复完成!');
  } catch (err) {
    console.error('修复过程中出错:', err);
  } finally {
    await sequelize.close();
  }
}

fixLiFuchunAssistant();
