"use client"

import React, { useState, useRef, useCallback } from "react"
import { Upload, X, File, CheckCircle, AlertCircle, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { toast } from "@/components/ui/use-toast"
import { uploadFile } from "@/services/file-service"
import { useAuth } from "@/contexts/auth-context"
import { handleAuthenticatedAction } from "@/utils/auth-utils"

/**
 * 文件上传组件属性
 */
interface FileUploadProps {
  knowledgeBaseId: string
  onUploadComplete?: (file: any) => void
  onUploadError?: (error: any) => void
  maxSize?: number // 最大文件大小，单位MB
  allowedTypes?: string[] // 允许的文件类型
}

/**
 * 文件上传组件
 *
 * 支持拖拽上传和点击选择文件
 * 显示上传进度和状态
 */
export function FileUpload({
  knowledgeBaseId,
  onUploadComplete,
  onUploadError,
  maxSize = 50, // 默认最大50MB
  allowedTypes = [] // 默认允许所有类型
}: FileUploadProps) {
  // 状态
  const [isDragging, setIsDragging] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle")
  const [errorMessage, setErrorMessage] = useState("")

  // 文件输入引用
  const fileInputRef = useRef<HTMLInputElement>(null)

  /**
   * 处理拖拽事件
   */
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
  }, [])

  /**
   * 处理文件拖放
   */
  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      validateAndSetFile(files[0])
    }
  }, [])

  /**
   * 处理文件选择
   */
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      validateAndSetFile(files[0])
    }
  }, [])

  /**
   * 验证并设置文件
   */
  const validateAndSetFile = useCallback((file: File) => {
    // 重置状态
    setUploadStatus("idle")
    setUploadProgress(0)
    setErrorMessage("")

    // 验证文件大小
    const fileSizeInMB = file.size / (1024 * 1024)
    if (maxSize && fileSizeInMB > maxSize) {
      setErrorMessage(`文件大小超过限制（最大${maxSize}MB）`)
      setUploadStatus("error")
      return
    }

    // 验证文件类型
    if (allowedTypes.length > 0) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || ''
      if (!allowedTypes.includes(fileExtension)) {
        setErrorMessage(`不支持的文件类型，请上传${allowedTypes.join(', ')}格式的文件`)
        setUploadStatus("error")
        return
      }
    }

    // 创建一个新的File对象，确保文件名正确编码
    // 这样做是为了确保中文文件名能够正确处理
    try {
      // 设置选中的文件
      setSelectedFile(file)
    } catch (error) {
      console.error("处理文件时出错:", error)
      setErrorMessage("处理文件时出错，请重试")
      setUploadStatus("error")
    }
  }, [maxSize, allowedTypes])

  /**
   * 触发文件选择对话框
   */
  const triggerFileInput = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }, [])

  /**
   * 清除选中的文件
   */
  const clearSelectedFile = useCallback(() => {
    setSelectedFile(null)
    setUploadStatus("idle")
    setUploadProgress(0)
    setErrorMessage("")

    // 清除文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  // 获取认证状态
  const { isLoggedIn } = useAuth()

  /**
   * 上传文件
   */
  const handleUpload = useCallback(() => {
    if (!selectedFile || !knowledgeBaseId) return

    // 使用通用的认证操作处理函数
    handleAuthenticatedAction(
      isLoggedIn,
      async () => {
        try {
          setUploadStatus("uploading")

          // 调用上传API，确保使用Dify分析
          const uploadedFile = await uploadFile(
            knowledgeBaseId,
            selectedFile,
            (progress) => {
              setUploadProgress(progress)
            },
            true // 强制使用Dify分析
          )

          // 上传成功
          setUploadStatus("success")

          // 调用成功回调
          if (onUploadComplete) {
            onUploadComplete(uploadedFile)
          }

          // 显示成功提示
          toast({
            title: "上传成功",
            description: `文件 ${selectedFile.name} 已成功上传并通过AI分析`,
          })

          // 延迟清除选中的文件
          setTimeout(() => {
            clearSelectedFile()
          }, 2000)

        } catch (error: any) {
          // 上传失败
          setUploadStatus("error")

          // 获取错误消息
          let errorMsg = "文件上传失败，请稍后再试"
          let errorTitle = "上传失败"

          // 处理权限错误
          if (error.name === 'PermissionError' || error.response?.status === 403) {
            errorMsg = error.message || error.response?.data?.message || "您没有权限上传文件到该知识库"
            errorTitle = "权限不足"

            // 记录权限错误但不作为严重错误
            console.info("文件上传权限错误:", errorMsg)
          } else {
            // 其他错误正常记录
            console.error("文件上传失败:", error)
            errorMsg = error.message || error.response?.data?.message || "文件上传失败，请稍后再试"
          }

          setErrorMessage(errorMsg)

          // 调用错误回调
          if (onUploadError) {
            onUploadError(error)
          }

          // 显示错误提示
          toast({
            title: errorTitle,
            description: errorMsg,
            variant: "destructive"
          })
        }
      },
      "请先登录后再上传文件"
    )
  }, [selectedFile, knowledgeBaseId, onUploadComplete, onUploadError, clearSelectedFile, isLoggedIn])

  /**
   * 获取文件图标
   */
  const getFileIcon = useCallback(() => {
    if (!selectedFile) return null

    const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase()

    // 根据文件类型返回不同图标
    switch (fileExtension) {
      case 'pdf':
        return <File className="h-8 w-8 text-red-500" />
      case 'doc':
      case 'docx':
        return <File className="h-8 w-8 text-blue-500" />
      case 'xls':
      case 'xlsx':
        return <File className="h-8 w-8 text-green-500" />
      case 'ppt':
      case 'pptx':
        return <File className="h-8 w-8 text-orange-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <File className="h-8 w-8 text-purple-500" />
      default:
        return <File className="h-8 w-8 text-gray-500" />
    }
  }, [selectedFile])

  /**
   * 获取状态图标
   */
  const getStatusIcon = useCallback(() => {
    switch (uploadStatus) {
      case "uploading":
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "error":
        return <AlertCircle className="h-5 w-5 text-red-500" />
      default:
        return null
    }
  }, [uploadStatus])

  return (
    <div className="w-full">
      {/* 隐藏的文件输入 */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* 拖放区域 */}
      {!selectedFile && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragging
              ? "border-[#1e7a43] bg-[#1e7a43]/10"
              : "border-gray-300 hover:border-[#1e7a43] hover:bg-[#1e7a43]/5"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={triggerFileInput}
        >
          <div className="flex flex-col items-center justify-center space-y-4">
            <Upload className="h-10 w-10 text-gray-400" />
            <div>
              <p className="text-sm font-medium">
                拖放文件到此处或{" "}
                <span className="text-[#1e7a43]">点击选择文件</span>
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {allowedTypes.length > 0
                  ? `支持 ${allowedTypes.join(", ")} 格式，最大 ${maxSize}MB`
                  : `最大 ${maxSize}MB`}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 选中的文件 */}
      {selectedFile && (
        <div className="border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getFileIcon()}
              <div>
                <p className="font-medium text-sm truncate max-w-[200px]">
                  {selectedFile.name}
                </p>
                <p className="text-xs text-gray-500">
                  {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
                </p>
              </div>
              {getStatusIcon()}
            </div>

            {uploadStatus !== "uploading" && (
              <Button
                variant="ghost"
                size="icon"
                onClick={clearSelectedFile}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* 上传进度 */}
          {uploadStatus === "uploading" && (
            <div className="mt-3">
              <Progress value={uploadProgress} className="h-2" />
              <p className="text-xs text-gray-500 mt-1 text-right">
                {uploadProgress}%
              </p>
            </div>
          )}

          {/* 错误信息 */}
          {uploadStatus === "error" && errorMessage && (
            <p className="text-xs text-red-500 mt-2">{errorMessage}</p>
          )}

          {/* 上传按钮 */}
          {uploadStatus === "idle" && (
            <div className="mt-3">
              <Button
                onClick={handleUpload}
                className="w-full bg-[#1e7a43] hover:bg-[#1e7a43]/90"
              >
                上传文件
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default FileUpload
