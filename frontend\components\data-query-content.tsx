"use client"

import { useState, useEffect } from "react"
import {
  Search,
  X,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Download,
  FileText,
  Database,
  Check,
  CheckSquare,
  Clock,
  User,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useModal, ModalContent } from "@/components/modal-provider"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { downloadFile } from "@/services/file-service"
import { logger, sanitizeData } from "@/utils/logger"

// Update the SearchResult interface to match the new requirements
interface SearchResult {
  id: number
  title: string
  description: string
  source: string // Knowledge base name
  sourceType: "internal" | "external"
  sourceOrg: string
  contentType: "text" | "image" | "pdf"
  fullContent?: string
  fileSize?: string
  fileType?: string
  downloadUrl?: string
  uploadTime?: string
  uploadedBy?: string // Add uploader field
  externalUrl?: string
  imageUrls?: string[]
  knowledgeBaseId?: string
}

interface KnowledgeBase {
  id: string
  name: string
  type: "系统" | "用户"
  description?: string
  hasAccess?: boolean
}

interface DataQueryContentProps {
  searchQuery?: string
  selectedKnowledgeBases?: string[]
  onKnowledgeBasesChange?: (selectedIds: string[]) => void
}

export function DataQueryContent({
  searchQuery,
  selectedKnowledgeBases: externalSelectedKnowledgeBases = [],
  onKnowledgeBasesChange
}: DataQueryContentProps) {
  const { openModal, closeModal } = useModal()
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [totalResults, setTotalResults] = useState(128)
  const [resultsPerPage, setResultsPerPage] = useState(50)
  const [currentPage, setCurrentPage] = useState(1)
  const [localSearchQuery, setLocalSearchQuery] = useState("")
  const [internalSelectedKnowledgeBases, setInternalSelectedKnowledgeBases] = useState<string[]>([])

  // 使用外部传入的知识库选择状态，如果有的话
  const selectedKnowledgeBases = externalSelectedKnowledgeBases.length > 0
    ? externalSelectedKnowledgeBases
    : internalSelectedKnowledgeBases

  // 处理知识库选择变化 - 简化版本
  const handleKnowledgeBasesChange = (ids: string[]) => {
    // 确保所有ID都是字符串类型
    const processedIds = ids.map(id => String(id))

    logger.debug('知识库选择变化:', {
      '当前选择': selectedKnowledgeBases,
      '新选择': ids,
      '处理后选择': processedIds,
      '是否使用外部状态': !!onKnowledgeBasesChange
    })

    // 直接更新状态，不进行DOM操作
    if (onKnowledgeBasesChange) {
      onKnowledgeBasesChange(processedIds)
    } else {
      setInternalSelectedKnowledgeBases(processedIds)
    }
  }

  // 修改数据平台状态，并默认选择和富家族研究平台
  const [selectedDataPlatforms, setSelectedDataPlatforms] = useState<string[]>(["internal"])
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([])
  const [isLoadingKnowledgeBases, setIsLoadingKnowledgeBases] = useState(false)

  // 从API获取知识库数据
  useEffect(() => {
    const fetchKnowledgeBases = async () => {
      setIsLoadingKnowledgeBases(true)
      try {
        // 获取认证token - 检查所有可能的存储键
        const token = localStorage.getItem('hefamily_token') || localStorage.getItem('token') || sessionStorage.getItem('token')

        // 输出调试信息
        logger.debug('获取知识库时的登录状态检查:', {
          'localStorage.hefamily_token': localStorage.getItem('hefamily_token'),
          'localStorage.token': localStorage.getItem('token'),
          'sessionStorage.token': sessionStorage.getItem('token'),
          'token存在': !!token
        })

        if (!token) {
          logger.warn('未找到认证token，无法获取知识库列表')
          // 不抛出错误，而是使用默认数据
          // 未登录用户只显示系统知识库，不显示用户知识库
          setKnowledgeBases([
            { id: "kb-1", name: "蔡和森研究", type: "系统" as const, hasAccess: true },
            { id: "kb-2", name: "红色家族史", type: "系统" as const, hasAccess: true },
            { id: "kb-3", name: "革命历史资料", type: "系统" as const, hasAccess: true },
            { id: "kb-4", name: "中共建党史", type: "系统" as const, hasAccess: true },
            { id: "kb-5", name: "政治思想研究", type: "系统" as const, hasAccess: true }
          ])
          setIsLoadingKnowledgeBases(false)
          return
        }

        // 使用API服务获取知识库列表，并添加认证头
        const response = await fetch('/api/knowledge', {
          headers: {
            'Authorization': token.startsWith('Bearer ') ? token : `Bearer ${token}`
          }
        })

        if (!response.ok) {
          throw new Error('获取知识库列表失败')
        }

        const data = await response.json()
        if (data.success && data.data && data.data.knowledgeBases) {
          // 获取当前用户信息 - 统一使用hefamily_user_data
          const userDataStr = localStorage.getItem('hefamily_user_data')
          let userData = null
          let isAdmin = false
          let username = ''
          let userId = null

          try {
            if (userDataStr) {
              userData = JSON.parse(userDataStr)
              // 确保userData是一个对象
              if (userData && typeof userData === 'object') {
                isAdmin = userData.role === 'admin'
                username = userData.username || ''
                userId = userData.id
              } else {
                logger.warn('用户数据格式不正确:', sanitizeData(userData))
              }
            } else {
              logger.warn('未找到用户信息，尝试从其他存储位置获取')

              // 尝试从hefamily_user_info获取
              const userInfoStr = localStorage.getItem('hefamily_user_info');
              if (userInfoStr) {
                try {
                  const userInfo = JSON.parse(userInfoStr);
                  if (userInfo && typeof userInfo === 'object') {
                    userData = userInfo;
                    isAdmin = userInfo.role === 'admin';
                    username = userInfo.username || '';
                    userId = userInfo.id;

                    // 同步到hefamily_user_data
                    localStorage.setItem('hefamily_user_data', JSON.stringify(userInfo));
                    logger.debug('从hefamily_user_info同步用户数据到hefamily_user_data');
                  }
                } catch (e) {
                  logger.error('解析hefamily_user_info失败:', sanitizeData(e));
                }
              }

              // 如果还是没有，尝试从hefamily_user获取
              if (!userData) {
                const userStr = localStorage.getItem('hefamily_user');
                if (userStr) {
                  try {
                    const user = JSON.parse(userStr);
                    if (user && typeof user === 'object') {
                      userData = user;
                      isAdmin = user.role === 'admin';
                      username = user.username || '';
                      userId = user.id;

                      // 同步到hefamily_user_data
                      localStorage.setItem('hefamily_user_data', JSON.stringify(user));
                      logger.debug('从hefamily_user同步用户数据到hefamily_user_data');
                    }
                  } catch (e) {
                    logger.error('解析hefamily_user失败:', sanitizeData(e));
                  }
                }
              }

              // 如果还是没有，尝试从token中解析
              if (!userData) {
                try {
                  // JWT token通常由三部分组成，用.分隔
                  const tokenParts = token.split('.');
                  if (tokenParts.length === 3) {
                    // 第二部分是payload，包含用户信息
                    const payload = JSON.parse(atob(tokenParts[1]));
                    logger.debug('获取知识库时从token解析的用户信息:', sanitizeData(payload));

                    // 如果payload中包含用户ID和角色信息，可以构建一个简单的用户对象
                    if (payload.id && payload.role) {
                      userData = {
                        id: payload.id,
                        role: payload.role,
                        role_name: payload.role_name || '用户',
                        username: payload.username || 'user_' + payload.id
                      };

                      // 设置用户信息变量
                      isAdmin = userData.role === 'admin'
                      username = userData.username || ''
                      userId = userData.id

                      // 将用户信息存储到localStorage中 - 使用hefamily_user_data
                      localStorage.setItem('hefamily_user_data', JSON.stringify(userData));
                      logger.debug('获取知识库时已从token中提取并保存用户信息到hefamily_user_data');
                    }
                  }
                } catch (tokenError) {
                  logger.error('获取知识库时解析token失败:', sanitizeData(tokenError));
                }
              }
            }
          } catch (e) {
            logger.error('解析用户信息失败:', sanitizeData(e))
          }

          logger.debug('当前用户信息:', { isAdmin, username, userId, userData: sanitizeData(userData) })
          logger.debug('API返回的知识库数据:', sanitizeData(data.data.knowledgeBases))

          // 转换API返回的数据格式为组件需要的格式
          const formattedKnowledgeBases = data.data.knowledgeBases.map((kb: any) => {
            // 判断权限:
            // 1. 系统知识库默认所有人都有权限
            const isSystemKb = kb.type === 'system'

            // 2. 用户是知识库的创建者 (检查username和creator_id)
            const creatorUsername = kb.creator?.username
            const creatorId = kb.creator_id
            const isCreatorByName = creatorUsername === username
            const isCreatorById = creatorId && userId && creatorId.toString() === userId.toString()
            const isCreator = isCreatorByName || isCreatorById

            // 3. 用户被明确授予了权限
            const hasExplicitAccess = kb.has_access === true

            // 4. 管理员有所有知识库的访问权限
            // 最终权限判断: 系统知识库、管理员、创建者或明确授权的用户都有权限

            // 重要：管理员必须有所有知识库的访问权限
            const hasAccess = isAdmin ? true : (isSystemKb || isCreator || hasExplicitAccess)

            logger.debug(`知识库 ${kb.name} (ID: ${kb.id}) 权限详情:`, {
              isAdmin,
              isSystemKb,
              creatorUsername,
              creatorId,
              currentUsername: username,
              currentUserId: userId,
              isCreatorByName,
              isCreatorById,
              isCreator,
              hasExplicitAccess: kb.has_access,
              finalHasAccess: hasAccess,
              rawKbData: sanitizeData(kb)
            })

            return {
              id: kb.id.toString(),
              name: kb.name,
              type: kb.type === 'system' ? '系统' : '用户' as const,
              description: kb.description,
              hasAccess: hasAccess,
              creator: kb.creator?.username
            }
          })
          setKnowledgeBases(formattedKnowledgeBases)
        }
      } catch (error) {
        logger.error('获取知识库列表失败:', sanitizeData(error))
        // 如果API调用失败，使用一些默认数据以确保UI不会崩溃
        setKnowledgeBases([
          { id: "kb-1", name: "蔡和森研究", type: "系统" as const, hasAccess: true },
          { id: "kb-2", name: "红色家族史", type: "系统" as const, hasAccess: true },
          { id: "kb-3", name: "革命历史资料", type: "系统" as const, hasAccess: true }
        ])
      } finally {
        setIsLoadingKnowledgeBases(false)
      }
    }

    fetchKnowledgeBases()
  }, [])
  const [availableDataPlatforms, setAvailableDataPlatforms] = useState([
    { id: "all", name: "全部数据平台" },
    { id: "internal", name: "和富家族研究平台" },
    { id: "financial", name: "金融研究中心" },
    { id: "international", name: "国际金融研究所" },
    { id: "investment", name: "投资研究院" },
    { id: "revolution", name: "革命历史研究所" },
    { id: "archives", name: "中央档案馆" },
    { id: "party-history", name: "党史研究中心" },
    { id: "history", name: "历史研究院" },
    { id: "women", name: "妇女研究中心" },
    { id: "publishing", name: "党史研究出版社" },
  ])

  // 使用API获取文件数据

  // 从API获取文件数据
  const fetchSearchResults = async (query: string, selectedKbs: string[]) => {
    try {
      // 如果没有选择知识库，直接返回空结果
      if (!selectedKbs || selectedKbs.length === 0) {
        logger.warn('未选择知识库，返回空结果')
        return []
      }

      // 构建查询参数
      const params = new URLSearchParams()
      if (query) {
        params.append('search', query)
      }

      // 添加知识库ID参数 - 这是必须的
      // 恢复正常的搜索功能，只添加用户选择的知识库ID
      logger.debug('添加用户选择的知识库ID')
      selectedKbs.forEach(kb => params.append('knowledge_base_ids[]', kb))

      // 记录查询参数
      logger.debug('文件搜索参数:', {
        query,
        selectedKbs,
        params: params.toString()
      })

      // 获取认证token - 检查所有可能的存储键
      const token = localStorage.getItem('hefamily_token') || localStorage.getItem('token') || sessionStorage.getItem('token')

      // 输出调试信息
      logger.debug('搜索文件时的登录状态检查:', {
        'localStorage.hefamily_token': localStorage.getItem('hefamily_token'),
        'localStorage.token': localStorage.getItem('token'),
        'sessionStorage.token': sessionStorage.getItem('token'),
        'token存在': !!token
      })

      if (!token) {
        logger.warn('未找到认证token，无法获取文件列表')
        // 不抛出错误，而是返回空数组
        return []
      }

      // 调用API获取文件列表，并添加认证头
      logger.debug(`发送API请求: /api/files?${params.toString()}`)
      const response = await fetch(`/api/files?${params.toString()}`, {
        headers: {
          'Authorization': token.startsWith('Bearer ') ? token : `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('获取文件列表失败')
      }

      const data = await response.json()
      if (data.success && data.data && data.data.files) {
        // 转换API返回的数据格式为组件需要的格式
        const formattedResults = data.data.files.map((file: any) => {
          logger.debug('处理文件数据:', sanitizeData(file));
          return {
            id: file.id,
            title: file.original_name,
            description: file.description || '无描述',
            source: file.knowledgeBase?.name || '未知知识库',
            sourceType: "internal" as const,
            sourceOrg: "和富家族研究平台",
            contentType: "text" as const,
            fullContent: file.summary || file.description || '无内容',
            fileSize: formatFileSize(file.size),
            fileType: file.type?.toUpperCase() || 'UNKNOWN',
            downloadUrl: `/api/files/${file.id}/download`,
            uploadTime: new Date(file.created_at).toLocaleDateString(),
            uploadedBy: file.uploader?.username || '未知用户',
            knowledgeBaseId: file.knowledge_base_id?.toString()
          };
        })
        return formattedResults
      }
      return []
    } catch (error) {
      logger.error('获取文件列表失败:', sanitizeData(error))
      return []
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '未知'
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`
  }

  // 初始化知识库选择
  useEffect(() => {
    // 初始化时不加载任何搜索结果，保持空列表
    setSearchResults([])

    // 如果有系统知识库，默认选中所有系统知识库
    const systemKnowledgeBaseIds = knowledgeBases
      .filter(kb => kb.type === "系统")
      .map(kb => kb.id)

    logger.debug('初始化知识库选择:', {
      所有知识库: knowledgeBases.map(kb => ({ id: kb.id, name: kb.name, type: kb.type })),
      系统知识库IDs: systemKnowledgeBaseIds,
      当前选择: selectedKnowledgeBases
    })

    if (systemKnowledgeBaseIds.length > 0 && selectedKnowledgeBases.length === 0) {
      logger.debug('默认选择所有系统知识库')
      handleKnowledgeBasesChange(systemKnowledgeBaseIds)
    }
  }, [knowledgeBases])

  // 当搜索查询变化时更新结果
  useEffect(() => {
    if (searchQuery) {
      setLocalSearchQuery(searchQuery)

      // 确保有选择知识库
      if (selectedKnowledgeBases.length > 0) {
        logger.debug('搜索查询变化，执行搜索:', {
          searchQuery,
          selectedKnowledgeBases
        })

        // 使用API搜索
        fetchSearchResults(searchQuery, selectedKnowledgeBases).then(results => {
          logger.debug('搜索结果:', {
            resultsCount: results.length,
            selectedKnowledgeBases
          })

          setSearchResults(results)

          // 如果没有找到结果，显示提示
          if (results.length === 0) {
            toast({
              title: "未找到匹配结果",
              description: "尝试使用其他关键词或选择更多知识库",
              variant: "default",
            })
          }
        })
      } else {
        logger.debug('搜索查询变化，但未选择知识库，不执行搜索')
        setSearchResults([])
      }
    }
  }, [searchQuery, selectedKnowledgeBases])

  // 处理本地搜索
  const handleLocalSearch = async () => {
    // 检查用户是否登录 - 检查所有可能的存储键
    const token = localStorage.getItem('hefamily_token') || localStorage.getItem('token') || sessionStorage.getItem('token')
    const userDataStr = localStorage.getItem('hefamily_user_data')

    // 输出调试信息
    logger.debug('搜索前登录状态检查:', {
      'localStorage.hefamily_token': localStorage.getItem('hefamily_token'),
      'localStorage.hefamily_user_data': userDataStr ? '已设置' : '未设置',
      'localStorage.hefamily_user_info': localStorage.getItem('hefamily_user_info') ? '已设置' : '未设置',
      'localStorage.hefamily_user': localStorage.getItem('hefamily_user') ? '已设置' : '未设置',
      'token存在': !!token,
      'userData存在': !!userDataStr
    })

    // 如果没有token，提示用户登录
    if (!token) {
      toast({
        title: "请先登录",
        description: "您需要登录后才能搜索知识库内容",
        variant: "destructive",
      })
      return
    }

    // 如果有token但没有用户信息，尝试从其他存储位置获取
    if (!userDataStr) {
      let userData = null;

      // 尝试从hefamily_user_info获取
      const userInfoStr = localStorage.getItem('hefamily_user_info');
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          if (userInfo && typeof userInfo === 'object') {
            userData = userInfo;

            // 同步到hefamily_user_data
            localStorage.setItem('hefamily_user_data', JSON.stringify(userInfo));
            console.log('搜索时从hefamily_user_info同步用户数据到hefamily_user_data');
          }
        } catch (e) {
          console.error('解析hefamily_user_info失败:', e);
        }
      }

      // 如果还是没有，尝试从hefamily_user获取
      if (!userData) {
        const userStr = localStorage.getItem('hefamily_user');
        if (userStr) {
          try {
            const user = JSON.parse(userStr);
            if (user && typeof user === 'object') {
              userData = user;

              // 同步到hefamily_user_data
              localStorage.setItem('hefamily_user_data', JSON.stringify(user));
              console.log('搜索时从hefamily_user同步用户数据到hefamily_user_data');
            }
          } catch (e) {
            console.error('解析hefamily_user失败:', e);
          }
        }
      }

      // 如果还是没有，尝试从token中解析
      if (!userData) {
        try {
          // JWT token通常由三部分组成，用.分隔
          const tokenParts = token.split('.');
          if (tokenParts.length === 3) {
            // 第二部分是payload，包含用户信息
            const payload = JSON.parse(atob(tokenParts[1]));
            console.log('搜索时从token解析的用户信息:', payload);

            // 如果payload中包含用户ID和角色信息，可以构建一个简单的用户对象
            if (payload.id && payload.role) {
              userData = {
                id: payload.id,
                role: payload.role,
                role_name: payload.role_name || '用户',
                username: payload.username || 'user_' + payload.id
              };

              // 将用户信息存储到localStorage中 - 使用hefamily_user_data
              localStorage.setItem('hefamily_user_data', JSON.stringify(userData));
              console.log('搜索时已从token中提取并保存用户信息到hefamily_user_data');
            }
          }
        } catch (e) {
          console.error('搜索时解析token失败:', e);
          // 即使解析失败，我们仍然允许用户继续，因为有token
        }
      }
    }

    // 如果没有选择任何知识库，提示用户
    if (selectedKnowledgeBases.length === 0) {
      toast({
        title: "请选择知识库",
        description: "请先选择至少一个知识库进行搜索",
        variant: "destructive",
      })
      return
    }

    // 如果没有输入搜索关键词，提示用户
    if (!localSearchQuery.trim()) {
      // 聚焦到搜索框
      const searchInput = document.querySelector('input[placeholder="请输入关键词搜索"]') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
      }

      toast({
        title: "请输入搜索关键词",
        description: "请在搜索框中输入要查询的关键词",
        variant: "default", // 使用默认样式，不使用红色警告样式
      })
      return
    }

    // 记录搜索参数
    console.log('执行本地搜索:', {
      localSearchQuery,
      selectedKnowledgeBases
    })

    // 使用API搜索
    const results = await fetchSearchResults(localSearchQuery, selectedKnowledgeBases)

    console.log('本地搜索结果:', {
      resultsCount: results.length,
      selectedKnowledgeBases,
      results: results.map(r => ({
        id: r.id,
        title: r.title,
        knowledgeBaseId: r.knowledgeBaseId,
        source: r.source
      }))
    })

    // 更新搜索结果
    setSearchResults(results)

    // 如果没有找到结果，显示提示
    if (results.length === 0) {
      toast({
        title: "未找到匹配结果",
        description: "尝试使用其他关键词或选择更多知识库",
        variant: "default",
      })
    } else {
      toast({
        title: "搜索完成",
        description: `找到 ${results.length} 条匹配结果`,
        variant: "default",
      })
    }
  }

  // 处理筛选器点击
  const handleFilterClick = (filter: string) => {
    if (activeFilters.includes(filter)) {
      setActiveFilters(activeFilters.filter((f) => f !== filter))
    } else {
      setActiveFilters([...activeFilters, filter])
    }
  }

  // 处理移除筛选器
  const handleRemoveFilter = (filter: string) => {
    setActiveFilters(activeFilters.filter((f) => f !== filter))
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // 更新查看详情函数，使用统一的模态框格式，移除概述和详细描述
  const handleViewDetail = (result: SearchResult) => {
    openModal({
      title: result.title,
      content: (
        <div>
          <div className="flex flex-wrap items-center text-sm text-gray-500 mb-4">
            <span className="mr-4 mb-1">知识库: {result.source}</span>
            <span className="mr-4 mb-1">上传时间: {result.uploadTime}</span>
            <span className="mr-4 mb-1">上传者: {result.uploadedBy}</span>
            {result.fileSize && <span className="mr-4 mb-1">文件大小: {result.fileSize}</span>}
            {result.fileType && <span className="mb-1">文件类型: {result.fileType}</span>}
          </div>
        </div>
      ),
      footer: (
        <div className="flex justify-end">
          <Button variant="outline" className="flex items-center mr-2" onClick={closeModal}>
            关闭
          </Button>
          <Button
            className="bg-[#f5a623] hover:bg-[#f5a623]/90 flex items-center"
            onClick={() => alert(`正在下载: ${result.title}`)}
          >
            <Download className="h-4 w-4 mr-2" />
            下载文件
          </Button>
        </div>
      ),
    });
  }

// 知识库选择模态框组件
const KnowledgeBaseSelectionModal = ({
  knowledgeBases,
  isLoadingKnowledgeBases,
  initialSelectedKnowledgeBases,
  onConfirm,
  onCancel,
}: {
  knowledgeBases: KnowledgeBase[];
  isLoadingKnowledgeBases: boolean;
  initialSelectedKnowledgeBases: string[];
  onConfirm: (selectedIds: string[]) => void;
  onCancel: () => void;
}) => {
  // 使用本地状态来跟踪选择
  const [tempSelectedKnowledgeBases, setTempSelectedKnowledgeBases] = useState<string[]>(initialSelectedKnowledgeBases);

  // 处理系统知识库点击
  const handleSystemKnowledgeBaseClick = (kb: KnowledgeBase) => {
    // 确保ID是字符串类型
    const kbId = String(kb.id);

    console.log('点击系统知识库:', {
      知识库: {
        id: kb.id,
        stringId: kbId,
        name: kb.name,
        type: kb.type
      },
      当前选择: tempSelectedKnowledgeBases
    });

    if (tempSelectedKnowledgeBases.includes(kbId)) {
      // 如果已选中，则取消选择
      const newIds = tempSelectedKnowledgeBases.filter((id) => id !== kbId);
      setTempSelectedKnowledgeBases(newIds);
    } else {
      // 如果未选中，则选择
      const newIds = [...tempSelectedKnowledgeBases, kbId];
      setTempSelectedKnowledgeBases(newIds);
    }
  };

  // 处理用户知识库点击
  const handleUserKnowledgeBaseClick = (kb: KnowledgeBase) => {
    // 确保ID是字符串类型
    const kbId = String(kb.id);

    console.log('点击用户知识库:', {
      知识库: {
        id: kb.id,
        stringId: kbId,
        name: kb.name,
        type: kb.type,
        hasAccess: kb.hasAccess
      },
      当前选择: tempSelectedKnowledgeBases,
      权限检查: kb.type === "用户" && kb.hasAccess === false ? "无权限" : "有权限"
    });

    // 如果是用户知识库，且没有访问权限，显示提示
    if (kb.type === "用户" && kb.hasAccess === false) {
      console.log(`用户尝试选择无权限知识库: ${kb.name} (ID: ${kb.id})`);
      toast({
        title: "无访问权限",
        description: `您没有访问"${kb.name}"知识库的权限，请联系管理员申请权限。`,
        variant: "destructive",
      });
      return;
    }

    if (tempSelectedKnowledgeBases.includes(kbId)) {
      // 如果已选中，则取消选择
      const newIds = tempSelectedKnowledgeBases.filter((id) => id !== kbId);
      setTempSelectedKnowledgeBases(newIds);
    } else {
      // 如果未选中，则选择
      const newIds = [...tempSelectedKnowledgeBases, kbId];
      setTempSelectedKnowledgeBases(newIds);
    }
  };

  // 处理系统知识库全选
  const handleSelectAllSystemKnowledgeBases = () => {
    // 确保所有ID都是字符串类型
    const systemIds = knowledgeBases
      .filter((kb) => kb.type === "系统")
      .map((kb) => String(kb.id));

    console.log('系统知识库全选:', {
      系统知识库IDs: systemIds,
      当前选择: tempSelectedKnowledgeBases
    });

    const allSelected = systemIds.every((id) => tempSelectedKnowledgeBases.includes(id));

    if (allSelected) {
      // 如果全部已选，则取消全选
      const newIds = tempSelectedKnowledgeBases.filter((id) => !systemIds.includes(id));
      console.log('取消全选系统知识库:', newIds);
      setTempSelectedKnowledgeBases(newIds);
    } else {
      // 如果未全选，则全选
      const newIds = [...tempSelectedKnowledgeBases];
      systemIds.forEach((id) => {
        if (!newIds.includes(id)) {
          newIds.push(id);
        }
      });
      console.log('全选系统知识库:', newIds);
      setTempSelectedKnowledgeBases(newIds);
    }
  };

  // 处理用户知识库全选
  const handleSelectAllUserKnowledgeBases = () => {
    // 确保所有ID都是字符串类型
    const accessibleUserIds = knowledgeBases
      .filter((kb) => kb.type === "用户" && kb.hasAccess === true)
      .map((kb) => String(kb.id));

    // 获取无权限的知识库
    const inaccessibleUserKbs = knowledgeBases
      .filter((kb) => kb.type === "用户" && kb.hasAccess === false);

    console.log('用户知识库全选:', {
      可访问用户知识库IDs: accessibleUserIds,
      无权限用户知识库: inaccessibleUserKbs.map(kb => ({ id: kb.id, name: kb.name })),
      当前选择: tempSelectedKnowledgeBases
    });

    // 如果有无权限的知识库，显示提示
    if (inaccessibleUserKbs.length > 0) {
      const inaccessibleNames = inaccessibleUserKbs.map(kb => kb.name).join(', ');
      console.log(`用户无权访问以下知识库: ${inaccessibleNames}`);
    }

    const allSelected = accessibleUserIds.every((id) => tempSelectedKnowledgeBases.includes(id));

    if (allSelected) {
      // 如果全部已选，则取消全选
      const newIds = tempSelectedKnowledgeBases.filter((id) => !accessibleUserIds.includes(id));
      console.log('取消全选用户知识库:', newIds);
      setTempSelectedKnowledgeBases(newIds);
    } else {
      // 如果未全选，则全选有权限的知识库
      const newIds = [...tempSelectedKnowledgeBases];
      accessibleUserIds.forEach((id) => {
        if (!newIds.includes(id)) {
          newIds.push(id);
        }
      });
      console.log('全选用户知识库:', newIds);
      setTempSelectedKnowledgeBases(newIds);
    }
  };

  // 确认选择
  const confirmSelection = () => {
    onConfirm(tempSelectedKnowledgeBases);
  };

  return (
    <>
      <div className="p-4">
        <div className="mb-4">
          <div className="text-sm text-gray-500 mb-2">请选择您要查询的知识库，可以选择多个知识库进行跨库检索</div>
          <div className="text-sm font-medium text-gray-700 mt-2">已选择 {tempSelectedKnowledgeBases.length} 个知识库</div>
        </div>

        {isLoadingKnowledgeBases ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1e7a43]"></div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* 系统知识库 */}
            {knowledgeBases.filter((kb) => kb.type === "系统").length > 0 && (
              <div>
                <div className="flex justify-between items-center px-2 py-1 bg-gray-50 rounded-sm mb-2">
                  <div className="text-xs font-semibold text-gray-500">系统知识库</div>
                  <button
                    onClick={handleSelectAllSystemKnowledgeBases}
                    className="flex items-center text-xs text-blue-600 hover:text-blue-800"
                  >
                    <CheckSquare className="h-3 w-3 mr-1" />
                    全选
                  </button>
                </div>
                <div className="space-y-1">
                  {knowledgeBases
                    .filter((kb) => kb.type === "系统")
                    .map((kb) => {
                      // 确保ID是字符串类型
                      const kbId = String(kb.id);
                      return (
                        <div
                          id={`kb-item-${kbId}`}
                          key={kbId}
                          className={cn(
                            "flex items-center px-2 py-2 text-sm rounded-sm cursor-pointer",
                            tempSelectedKnowledgeBases.includes(kbId) ? "bg-emerald-50" : "hover:bg-gray-50",
                          )}
                          onClick={() => handleSystemKnowledgeBaseClick(kb)}
                        >
                          <div
                            className={cn(
                              "mr-2 h-4 w-4 rounded-sm border flex items-center justify-center",
                              tempSelectedKnowledgeBases.includes(kbId)
                                ? "border-emerald-500 bg-emerald-500 text-white"
                                : "border-gray-300",
                            )}
                          >
                            {tempSelectedKnowledgeBases.includes(kbId) && <Check className="h-3 w-3" />}
                          </div>
                          <div className="flex items-center">
                            <Database className="h-3 w-3 mr-2 text-blue-500" />
                            <span className="text-sm">{kb.name}</span>
                            <span className="text-xs text-gray-500 ml-2">(ID: {kbId})</span>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}

            {/* 用户知识库 */}
            {knowledgeBases.filter((kb) => kb.type === "用户").length > 0 && (
              <div>
                <div className="flex justify-between items-center px-2 py-1 bg-gray-50 rounded-sm mb-2">
                  <div className="text-xs font-semibold text-gray-500">用户知识库</div>
                  <button
                    onClick={handleSelectAllUserKnowledgeBases}
                    className="flex items-center text-xs text-blue-600 hover:text-blue-800"
                  >
                    <CheckSquare className="h-3 w-3 mr-1" />
                    全选
                  </button>
                </div>
                <div className="space-y-1">
                  {knowledgeBases
                    .filter((kb) => kb.type === "用户")
                    .map((kb) => {
                      // 确保ID是字符串类型
                      const kbId = String(kb.id);
                      return (
                        <div
                          id={`kb-item-${kbId}`}
                          key={kbId}
                          className={cn(
                            "flex items-center px-2 py-2 text-sm rounded-sm",
                            // 使用已经计算好的hasAccess属性
                            kb.hasAccess ? "cursor-pointer" : "cursor-not-allowed opacity-70",
                            tempSelectedKnowledgeBases.includes(kbId) ? "bg-emerald-50" : "hover:bg-gray-50",
                            !kb.hasAccess && "border border-red-200 bg-red-50"
                          )}
                          onClick={() => handleUserKnowledgeBaseClick(kb)}
                        >
                          <div
                            className={cn(
                              "mr-2 h-4 w-4 rounded-sm border flex items-center justify-center",
                              tempSelectedKnowledgeBases.includes(kbId)
                                ? "border-emerald-500 bg-emerald-500 text-white"
                                : "border-gray-300",
                              !kb.hasAccess && "opacity-50"
                            )}
                          >
                            {tempSelectedKnowledgeBases.includes(kbId) && <Check className="h-3 w-3" />}
                          </div>
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center">
                              <Database className="h-3 w-3 mr-2 text-green-500" />
                              <span className="text-sm">{kb.name}</span>
                              <span className="text-xs text-gray-500 ml-2">(ID: {kbId})</span>
                            </div>
                            {!kb.hasAccess && (
                              <span className="text-xs text-red-500 ml-2">无权限</span>
                            )}
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      <div className="flex justify-end space-x-2 p-4 border-t">
        <Button variant="outline" onClick={onCancel}>
          取消
        </Button>
        <Button
          className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
          onClick={confirmSelection}
        >
          确定
        </Button>
      </div>
    </>
  );
};

// 打开知识库选择模态框
const openKnowledgeBaseModal = () => {
  // 检查用户是否登录 - 检查所有可能的存储键
  const token = localStorage.getItem('hefamily_token') || localStorage.getItem('token') || sessionStorage.getItem('token')
  const userDataStr = localStorage.getItem('hefamily_user_data')

  // 输出调试信息
  console.log('登录状态检查:', {
    'localStorage.hefamily_token': localStorage.getItem('hefamily_token'),
    'localStorage.hefamily_user_data': userDataStr ? '已设置' : '未设置',
    'localStorage.hefamily_user_info': localStorage.getItem('hefamily_user_info') ? '已设置' : '未设置',
    'localStorage.hefamily_user': localStorage.getItem('hefamily_user') ? '已设置' : '未设置',
    'token存在': !!token,
    'userData存在': !!userDataStr
  })

  // 如果没有token，提示用户登录
  if (!token) {
    toast({
      title: "请先登录",
      description: "您需要登录后才能选择和访问知识库",
      variant: "destructive",
    })

    // 可以选择跳转到登录页面
    // window.location.href = '/login'
    return
  }

  // 如果有token但没有用户信息，尝试从其他存储位置获取
  if (!userDataStr) {
    let userData = null;

    // 尝试从hefamily_user_info获取
    const userInfoStr = localStorage.getItem('hefamily_user_info');
    if (userInfoStr) {
      try {
        const userInfo = JSON.parse(userInfoStr);
        if (userInfo && typeof userInfo === 'object') {
          userData = userInfo;

          // 同步到hefamily_user_data
          localStorage.setItem('hefamily_user_data', JSON.stringify(userInfo));
          console.log('从hefamily_user_info同步用户数据到hefamily_user_data');
        }
      } catch (e) {
        console.error('解析hefamily_user_info失败:', e);
      }
    }

    // 如果还是没有，尝试从hefamily_user获取
    if (!userData) {
      const userStr = localStorage.getItem('hefamily_user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          if (user && typeof user === 'object') {
            userData = user;

            // 同步到hefamily_user_data
            localStorage.setItem('hefamily_user_data', JSON.stringify(user));
            console.log('从hefamily_user同步用户数据到hefamily_user_data');
          }
        } catch (e) {
          console.error('解析hefamily_user失败:', e);
        }
      }
    }

    // 如果还是没有，尝试从token中解析
    if (!userData) {
      try {
        // JWT token通常由三部分组成，用.分隔
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          // 第二部分是payload，包含用户信息
          const payload = JSON.parse(atob(tokenParts[1]));
          console.log('从token解析的用户信息:', payload);

          // 如果payload中包含用户ID和角色信息，可以构建一个简单的用户对象
          if (payload.id && payload.role) {
            userData = {
              id: payload.id,
              role: payload.role,
              role_name: payload.role_name || '用户',
              username: payload.username || 'user_' + payload.id
            };

            // 将用户信息存储到localStorage中 - 使用hefamily_user_data
            localStorage.setItem('hefamily_user_data', JSON.stringify(userData));
            console.log('已从token中提取并保存用户信息到hefamily_user_data');
          }
        }
      } catch (e) {
        console.error('解析token失败:', e);
        // 即使解析失败，我们仍然允许用户继续，因为有token
      }
    }
  }

  // 确认选择
  const handleConfirm = (selectedIds: string[]) => {
    console.log('确认知识库选择:', {
      selectedIds,
      知识库信息: selectedIds.map(id => {
        const kb = knowledgeBases.find(kb => kb.id === id);
        return kb ? { id: kb.id, name: kb.name, type: kb.type } : { id, name: '未知知识库', type: '未知' };
      })
    });

    // 将临时选择应用到实际选择
    handleKnowledgeBasesChange(selectedIds);
    closeModal();

    // 显示一个成功提示
    if (selectedIds.length > 0) {
      // 获取选中知识库的名称
      const selectedNames = selectedIds.map(id => {
        const kb = knowledgeBases.find(kb => kb.id === id);
        return kb ? kb.name : `知识库 ${id}`;
      }).join(", ");

      toast({
        title: "知识库选择成功",
        description: `已选择 ${selectedIds.length} 个知识库: ${selectedNames}，请输入关键词进行搜索`,
        variant: "default",
      });

      // 清空搜索结果，等待新的搜索
      setSearchResults([]);
    }
  };

  openModal({
    title: "选择要查询的知识库",
    content: (
      <KnowledgeBaseSelectionModal
        knowledgeBases={knowledgeBases}
        isLoadingKnowledgeBases={isLoadingKnowledgeBases}
        initialSelectedKnowledgeBases={selectedKnowledgeBases}
        onConfirm={handleConfirm}
        onCancel={closeModal}
      />
    ),
    showFooter: false, // 不显示默认的页脚，因为我们在组件内部已经添加了页脚
  });
}

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case "pdf":
        return <FileText className="h-5 w-5 text-red-500" />
      case "docx":
      case "doc":
        return <FileText className="h-5 w-5 text-blue-500" />
      case "zip":
      case "rar":
        return <FileText className="h-5 w-5 text-yellow-500" />
      default:
        return <FileText className="h-5 w-5 text-gray-500" />
    }
  }

  // Update the render method to show files in a list format
  return (
    <div className="py-6 px-6">
      {/* 搜索框 */}
      <div className="mb-8">
        <div className="relative">
          <input
            type="text"
            value={localSearchQuery}
            onChange={(e) => setLocalSearchQuery(e.target.value)}
            placeholder="请输入关键词搜索"
            className="w-full border border-gray-300 rounded-md py-2 pl-4 pr-12 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
          />
          <button
            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-[#f5a623] text-white p-1.5 rounded-md"
            onClick={handleLocalSearch}
          >
            <Search className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* 添加知识库选择按钮 */}
      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center">
          <Button
            onClick={openKnowledgeBaseModal}
            variant="outline"
            className="flex items-center gap-2 border-gray-300 text-gray-700"
          >
            <Database className="h-4 w-4 text-[#1e7a43]" />
            选择知识库
            {selectedKnowledgeBases.length > 0 && (
              <span className="bg-[#1e7a43] text-white text-xs rounded-full px-2 py-0.5">
                {selectedKnowledgeBases.length}
              </span>
            )}
          </Button>
          {selectedKnowledgeBases.length > 0 && (
            <div className="ml-3 text-sm text-gray-500">
              已选择 {selectedKnowledgeBases.length} 个知识库
            </div>
          )}
        </div>
      </div>

      {/* 时间范围筛选 */}
      <div className="mb-8">
        <h3 className="text-sm text-gray-500 mb-2">时间范围</h3>
        <div className="flex gap-2">
          <button
            className={`px-3 py-1 rounded-md text-sm ${activeFilters.includes("最近一周") ? "bg-[#1e7a43] text-white" : "bg-gray-100 text-gray-700"}`}
            onClick={() => handleFilterClick("最近一周")}
          >
            最近一周
          </button>
          <button
            className={`px-3 py-1 rounded-md text-sm ${activeFilters.includes("最近一月") ? "bg-[#1e7a43] text-white" : "bg-gray-100 text-gray-700"}`}
            onClick={() => handleFilterClick("最近一月")}
          >
            最近一月
          </button>
          <button
            className={`px-3 py-1 rounded-md text-sm ${activeFilters.includes("最近三月") ? "bg-[#1e7a43] text-white" : "bg-gray-100 text-gray-700"}`}
            onClick={() => handleFilterClick("最近三月")}
          >
            最近三月
          </button>
        </div>
      </div>

      {/* 已选条件 */}
      {activeFilters.length > 0 && (
        <div className="mb-6">
          <h3 className="text-sm text-gray-500 mb-2">已选条件：</h3>
          <div className="flex flex-wrap gap-2">
            {activeFilters.map((filter, index) => (
              <div key={index} className="flex items-center bg-gray-100 rounded-md px-2 py-1 text-sm">
                <span>{filter}</span>
                <button onClick={() => handleRemoveFilter(filter)} className="ml-1 text-gray-500 hover:text-gray-700">
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 搜索结果 - 改为列表形式 */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">搜索结果</h2>
          <div className="flex items-center">
            <span className="text-sm text-gray-500 mr-2">共 {totalResults} 条结果</span>
            <div className="relative">
              <button className="flex items-center text-sm text-gray-700 border border-gray-300 rounded-md px-3 py-1 hover:bg-gray-50">
                时间排序
                <ChevronDown className="h-4 w-4 ml-1" />
              </button>
            </div>
          </div>
        </div>

        {/* 结果列表 - 改为表格形式 */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  文件名
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  知识库
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  文件类型
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  文件大小
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  上传者
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  上传时间
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {searchResults.length > 0 ? (
                searchResults.map((result) => (
                  <tr key={result.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-9 w-9 rounded-md bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0 shadow-sm border border-gray-200">
                          {getFileIcon(result.fileType?.toLowerCase() || "")}
                        </div>
                        <div className="font-medium text-gray-800 truncate max-w-xs">{result.title}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Database className="h-3.5 w-3.5 mr-1.5 text-emerald-500" />
                        <span className="text-sm text-gray-700">{result.source}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge variant="outline" className="bg-gray-50 text-gray-700 font-medium uppercase text-xs">
                        {result.fileType}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-700">{result.fileSize}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-700">
                        <User className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
                        {result.uploadedBy}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-700">
                        <Clock className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
                        {result.uploadTime}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-green-600 hover:text-green-800 hover:bg-green-50"
                          onClick={async () => {
                            try {
                              // 显示下载开始提示
                              toast({
                                title: "下载开始",
                                description: `正在下载: ${result.title}`,
                              });

                              // 调用下载函数
                              await downloadFile(String(result.id), result.title);
                            } catch (error) {
                              console.error("下载文件失败:", error);
                              toast({
                                title: "下载失败",
                                description: "无法下载文件，请稍后再试",
                                variant: "destructive"
                              });
                            }
                          }}
                        >
                          下载
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p className="text-lg font-medium mb-2">未找到文件</p>
                    <p className="text-sm">尝试调整搜索条件或上传新的文件</p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* 分页 */}
        <div className="flex justify-center mt-8">
          <div className="flex items-center space-x-1">
            <button
              className="p-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </button>

            {[1, 2, 3].map((page) => (
              <button
                key={page}
                className={`w-8 h-8 flex items-center justify-center rounded-md ${
                  currentPage === page
                    ? "bg-[#1e7a43] text-white"
                    : "border border-gray-300 text-gray-700 hover:bg-gray-100"
                }`}
                onClick={() => handlePageChange(page)}
              >
                {page}
              </button>
            ))}

            <button
              className="p-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === 3}
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
