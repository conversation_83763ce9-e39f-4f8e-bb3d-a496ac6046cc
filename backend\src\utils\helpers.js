/**
 * 工具函数
 *
 * 提供各种通用的辅助函数
 */

/**
 * 格式化日期为 YYYY-MM-DD HH:MM:SS 格式
 * @param {Date} date - 要格式化的日期
 * @returns {string} - 格式化后的日期字符串
 */
const formatDate = (date) => {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return '';
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 生成指定长度的随机字符串
 * @param {number} length - 字符串长度
 * @returns {string} - 随机字符串
 */
const generateRandomString = (length) => {
  if (length <= 0) {
    return '';
  }

  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters.charAt(randomIndex);
  }

  return result;
};

/**
 * 验证邮箱地址是否有效
 * @param {string} email - 要验证的邮箱地址
 * @returns {boolean} - 是否有效
 */
const validateEmail = (email) => {
  if (!email) {
    return false;
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证中国手机号是否有效
 * @param {string} phone - 要验证的手机号
 * @returns {boolean} - 是否有效
 */
const validatePhone = (phone) => {
  if (!phone) {
    return false;
  }

  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 清理输入字符串中的HTML标签
 * @param {string} input - 要清理的输入
 * @returns {string} - 清理后的字符串
 */
const sanitizeInput = (input) => {
  if (!input) {
    return '';
  }

  // 移除HTML标签，并保留适当的空格
  let result = input
    // 先处理script标签内容（包括标签和内容）
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    // 然后移除其他HTML标签
    .replace(/<[^>]*>/g, ' ')
    // 处理多余的空格
    .replace(/\s+/g, ' ')
    // 去除首尾空格
    .trim();

  return result;
};

/**
 * 对结果进行分页
 * @param {Array} items - 要分页的项目数组
 * @param {number} page - 页码（从1开始）
 * @param {number} pageSize - 每页大小
 * @returns {Object} - 分页结果
 */
const paginateResults = (items, page, pageSize) => {
  // 处理无效输入
  const validItems = Array.isArray(items) ? items : [];
  const validPage = page > 0 ? page : 1;
  const validPageSize = pageSize > 0 ? pageSize : 10;

  // 计算总页数
  const total = validItems.length;
  const totalPages = Math.ceil(total / validPageSize);

  // 计算起始和结束索引
  const startIndex = (validPage - 1) * validPageSize;
  const endIndex = startIndex + validPageSize;

  // 获取当前页的项目
  const paginatedItems = validItems.slice(startIndex, endIndex);

  return {
    items: paginatedItems,
    total,
    page: validPage,
    pageSize: validPageSize,
    totalPages
  };
};

module.exports = {
  formatDate,
  generateRandomString,
  validateEmail,
  validatePhone,
  sanitizeInput,
  paginateResults
};
