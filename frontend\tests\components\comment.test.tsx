/**
 * 评论组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../test-utils'
import { CommentList } from '@/components/comment/CommentList'
import { CommentItem } from '@/components/comment/CommentItem'
import { CommentForm } from '@/components/comment/CommentForm'
import { mockFetch, resetMockFetch } from '../test-utils'

// 模拟评论数据
const mockComments = [
  {
    id: 1,
    content: '这是第一条评论',
    creator: {
      id: 1,
      username: 'user1'
    },
    status: 'approved',
    created_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 2,
    content: '这是第二条评论',
    creator: {
      id: 2,
      username: 'user2'
    },
    status: 'approved',
    created_at: '2023-01-02T00:00:00Z'
  },
  {
    id: 3,
    content: '这是待审核的评论',
    creator: {
      id: 3,
      username: 'user3'
    },
    status: 'pending',
    created_at: '2023-01-03T00:00:00Z'
  }
]

// 模拟当前用户
const mockCurrentUser = {
  id: 1,
  username: 'user1',
  role: 'basic_user'
}

// 模拟管理员用户
const mockAdminUser = {
  id: 4,
  username: 'admin',
  role: 'admin'
}

describe('评论组件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    resetMockFetch()
    jest.clearAllMocks()
  })

  // 测试评论列表组件
  describe('CommentList组件', () => {
    test('应该正确渲染评论列表', () => {
      render(
        <CommentList
          comments={mockComments}
          currentUser={mockCurrentUser}
          isLoading={false}
          isAdmin={false}
          onDelete={jest.fn()}
          onReview={jest.fn()}
        />
      )
      
      // 验证评论内容
      expect(screen.getByText('这是第一条评论')).toBeInTheDocument()
      expect(screen.getByText('这是第二条评论')).toBeInTheDocument()
      
      // 普通用户不应该看到待审核的评论
      expect(screen.queryByText('这是待审核的评论')).not.toBeInTheDocument()
      
      // 验证评论作者
      expect(screen.getByText('user1')).toBeInTheDocument()
      expect(screen.getByText('user2')).toBeInTheDocument()
    })

    test('管理员应该能看到待审核的评论', () => {
      render(
        <CommentList
          comments={mockComments}
          currentUser={mockAdminUser}
          isLoading={false}
          isAdmin={true}
          onDelete={jest.fn()}
          onReview={jest.fn()}
        />
      )
      
      // 验证所有评论都可见
      expect(screen.getByText('这是第一条评论')).toBeInTheDocument()
      expect(screen.getByText('这是第二条评论')).toBeInTheDocument()
      expect(screen.getByText('这是待审核的评论')).toBeInTheDocument()
      
      // 验证审核按钮
      expect(screen.getByText('通过')).toBeInTheDocument()
      expect(screen.getByText('拒绝')).toBeInTheDocument()
    })

    test('应该显示加载状态', () => {
      render(
        <CommentList
          comments={[]}
          currentUser={mockCurrentUser}
          isLoading={true}
          isAdmin={false}
          onDelete={jest.fn()}
          onReview={jest.fn()}
        />
      )
      
      expect(screen.getByText('加载中...')).toBeInTheDocument()
    })

    test('应该显示空评论提示', () => {
      render(
        <CommentList
          comments={[]}
          currentUser={mockCurrentUser}
          isLoading={false}
          isAdmin={false}
          onDelete={jest.fn()}
          onReview={jest.fn()}
        />
      )
      
      expect(screen.getByText('暂无评论')).toBeInTheDocument()
    })
  })

  // 测试评论项组件
  describe('CommentItem组件', () => {
    test('应该正确渲染评论项', () => {
      render(
        <CommentItem
          comment={mockComments[0]}
          currentUser={mockCurrentUser}
          isAdmin={false}
          onDelete={jest.fn()}
          onReview={jest.fn()}
        />
      )
      
      // 验证评论内容
      expect(screen.getByText('这是第一条评论')).toBeInTheDocument()
      
      // 验证评论作者
      expect(screen.getByText('user1')).toBeInTheDocument()
      
      // 验证评论时间
      const timeElement = screen.getByText((content) => {
        return content.includes('2023') && content.includes('01') && content.includes('01');
      });
      expect(timeElement).toBeInTheDocument()
      
      // 验证删除按钮（自己的评论可以删除）
      expect(screen.getByText('删除')).toBeInTheDocument()
    })

    test('不应该显示其他用户评论的删除按钮', () => {
      render(
        <CommentItem
          comment={mockComments[1]} // user2的评论
          currentUser={mockCurrentUser} // 当前用户是user1
          isAdmin={false}
          onDelete={jest.fn()}
          onReview={jest.fn()}
        />
      )
      
      // 验证没有删除按钮
      expect(screen.queryByText('删除')).not.toBeInTheDocument()
    })

    test('管理员应该能看到所有评论的删除按钮', () => {
      render(
        <CommentItem
          comment={mockComments[1]} // user2的评论
          currentUser={mockAdminUser} // 当前用户是admin
          isAdmin={true}
          onDelete={jest.fn()}
          onReview={jest.fn()}
        />
      )
      
      // 验证有删除按钮
      expect(screen.getByText('删除')).toBeInTheDocument()
    })

    test('管理员应该能看到待审核评论的审核按钮', () => {
      render(
        <CommentItem
          comment={mockComments[2]} // 待审核的评论
          currentUser={mockAdminUser} // 当前用户是admin
          isAdmin={true}
          onDelete={jest.fn()}
          onReview={jest.fn()}
        />
      )
      
      // 验证有审核按钮
      expect(screen.getByText('通过')).toBeInTheDocument()
      expect(screen.getByText('拒绝')).toBeInTheDocument()
    })

    test('应该调用删除函数', () => {
      const handleDelete = jest.fn()
      
      render(
        <CommentItem
          comment={mockComments[0]}
          currentUser={mockCurrentUser}
          isAdmin={false}
          onDelete={handleDelete}
          onReview={jest.fn()}
        />
      )
      
      // 点击删除按钮
      const deleteButton = screen.getByText('删除')
      fireEvent.click(deleteButton)
      
      // 验证删除函数被调用
      expect(handleDelete).toHaveBeenCalledWith(mockComments[0].id)
    })

    test('应该调用审核函数', () => {
      const handleReview = jest.fn()
      
      render(
        <CommentItem
          comment={mockComments[2]} // 待审核的评论
          currentUser={mockAdminUser} // 当前用户是admin
          isAdmin={true}
          onDelete={jest.fn()}
          onReview={handleReview}
        />
      )
      
      // 点击通过按钮
      const approveButton = screen.getByText('通过')
      fireEvent.click(approveButton)
      
      // 验证审核函数被调用
      expect(handleReview).toHaveBeenCalledWith(mockComments[2].id, 'approved')
      
      // 点击拒绝按钮
      const rejectButton = screen.getByText('拒绝')
      fireEvent.click(rejectButton)
      
      // 验证审核函数被调用
      expect(handleReview).toHaveBeenCalledWith(mockComments[2].id, 'rejected')
    })
  })

  // 测试评论表单组件
  describe('CommentForm组件', () => {
    test('应该正确渲染评论表单', () => {
      render(
        <CommentForm
          isLoggedIn={true}
          onSubmit={jest.fn()}
        />
      )
      
      // 验证表单元素
      expect(screen.getByPlaceholderText('写下你的评论...')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '提交评论' })).toBeInTheDocument()
    })

    test('未登录时应该显示登录提示', () => {
      render(
        <CommentForm
          isLoggedIn={false}
          onSubmit={jest.fn()}
        />
      )
      
      // 验证登录提示
      expect(screen.getByText('请登录后发表评论')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument()
    })

    test('应该调用提交函数', async () => {
      const handleSubmit = jest.fn().mockResolvedValue({
        id: 4,
        content: '新评论',
        creator: {
          id: 1,
          username: 'user1'
        },
        status: 'pending',
        created_at: new Date().toISOString()
      })
      
      render(
        <CommentForm
          isLoggedIn={true}
          onSubmit={handleSubmit}
        />
      )
      
      // 输入评论内容
      const commentInput = screen.getByPlaceholderText('写下你的评论...')
      fireEvent.change(commentInput, { target: { value: '新评论' } })
      
      // 点击提交按钮
      const submitButton = screen.getByRole('button', { name: '提交评论' })
      fireEvent.click(submitButton)
      
      // 验证提交函数被调用
      expect(handleSubmit).toHaveBeenCalledWith('新评论')
      
      // 等待提交完成，表单应该被重置
      await waitFor(() => {
        expect(commentInput).toHaveValue('')
      })
    })

    test('不应该提交空评论', () => {
      const handleSubmit = jest.fn()
      
      render(
        <CommentForm
          isLoggedIn={true}
          onSubmit={handleSubmit}
        />
      )
      
      // 不输入内容，直接点击提交按钮
      const submitButton = screen.getByRole('button', { name: '提交评论' })
      fireEvent.click(submitButton)
      
      // 验证提交函数没有被调用
      expect(handleSubmit).not.toHaveBeenCalled()
      
      // 验证错误提示
      expect(screen.getByText('评论不能为空')).toBeInTheDocument()
    })

    test('应该限制评论长度', () => {
      const handleSubmit = jest.fn()
      
      render(
        <CommentForm
          isLoggedIn={true}
          onSubmit={handleSubmit}
          maxLength={10}
        />
      )
      
      // 输入超过长度限制的评论
      const commentInput = screen.getByPlaceholderText('写下你的评论...')
      fireEvent.change(commentInput, { target: { value: '这是一条超过长度限制的评论' } })
      
      // 验证长度提示
      expect(screen.getByText('超出字数限制')).toBeInTheDocument()
      
      // 点击提交按钮
      const submitButton = screen.getByRole('button', { name: '提交评论' })
      fireEvent.click(submitButton)
      
      // 验证提交函数没有被调用
      expect(handleSubmit).not.toHaveBeenCalled()
    })
  })
});
