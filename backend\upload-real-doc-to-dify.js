/**
 * 将真实的DOC文件上传到Dify
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');
const { Sequelize } = require('sequelize');
require('dotenv').config();

// 连接数据库
const sequelize = new Sequelize(
  process.env.DB_NAME || 'hefamily_dev',
  process.env.DB_USERNAME || 'root',
  process.env.DB_PASSWORD || 'Misaya9987.',
  {
    host: process.env.DB_HOST || 'localhost',
    dialect: 'mysql',
    logging: false
  }
);

// 定义文件模型
const File = sequelize.define('File', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: Sequelize.STRING,
  original_name: Sequelize.STRING,
  path: Sequelize.STRING,
  type: Sequelize.STRING,
  size: Sequelize.INTEGER,
  status: Sequelize.STRING,
  dify_task_id: Sequelize.STRING,
  analysis_status: Sequelize.STRING,
  analysis_error: Sequelize.TEXT,
  knowledge_base_id: Sequelize.INTEGER,
  uploader_id: Sequelize.INTEGER,
  summary: Sequelize.TEXT,
  detailed_description: Sequelize.TEXT,
  mime_type: Sequelize.STRING
}, {
  tableName: 'files',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 主函数
async function main() {
  try {
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 获取文件ID
    const fileId = process.argv[2]; // 从命令行参数获取文件ID
    if (!fileId) {
      console.error('请指定文件ID');
      process.exit(1);
    }

    // 获取文件
    const file = await File.findByPk(fileId);
    if (!file) {
      console.error(`未找到ID为 ${fileId} 的文件`);
      process.exit(1);
    }

    console.log(`找到文件: ${file.id}, ${file.original_name}`);
    console.log(`文件状态: ${file.status}`);
    console.log(`文件路径: ${file.path}`);

    // 检查文件是否存在
    if (!fs.existsSync(file.path)) {
      console.error(`文件不存在: ${file.path}`);
      process.exit(1);
    }

    console.log(`文件存在: ${file.path}`);

    // 将文件转换为文本文件
    const textFilePath = path.join(path.dirname(file.path), `${path.basename(file.path, '.doc')}.txt`);

    // 读取DOC文件内容
    const docContent = fs.readFileSync(file.path);

    // 提取可读文本（这只是一个简单的方法，不能完全提取DOC文件的文本内容）
    let textContent = '';
    for (let i = 0; i < docContent.length; i++) {
      const char = String.fromCharCode(docContent[i]);
      if (char.match(/[a-zA-Z0-9\s\p{Script=Han}]/u)) {
        textContent += char;
      }
    }

    // 写入文本文件
    fs.writeFileSync(textFilePath, textContent);
    console.log(`已创建文本文件: ${textFilePath}`);

    // 准备FormData
    const formData = new FormData();

    // 使用文本文件而不是DOC文件
    formData.append('file', fs.createReadStream(textFilePath), `${file.original_name}.txt`);

    // 添加处理规则
    const processRule = {
      indexing_technique: "high_quality",
      process_rule: {
        rules: {
          pre_processing_rules: [
            { id: "remove_extra_spaces", enabled: true },
            { id: "remove_urls_emails", enabled: true }
          ],
          segmentation: {
            separator: "###",
            max_tokens: 500
          }
        },
        mode: "custom"
      }
    };

    formData.append('data', JSON.stringify(processRule), { type: 'text/plain' });

    // 设置API端点
    const datasetId = '77199451-730a-4d79-a1c9-9b9e6bfcd747';
    const apiEndpoint = `https://ai.glab.vip/v1/datasets/${datasetId}/document/create-by-file`;
    const apiKey = "dataset-DLFJlUe25VUHOwMO4HbO4hQk";

    console.log(`准备发送请求到: ${apiEndpoint}`);
    console.log(`使用API密钥: ${apiKey}`);

    try {
      // 发送请求
      const response = await axios.post(
        apiEndpoint,
        formData,
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            ...formData.getHeaders()
          },
          timeout: 120000 // 120秒超时
        }
      );

      console.log(`请求已发送，状态码: ${response.status}`);
      console.log(`响应数据:`, response.data);

      if (response.data && response.data.document) {
        const documentId = response.data.document.id;
        console.log(`文件上传成功，文档ID: ${documentId}`);

        // 更新文件记录
        await file.update({
          dify_task_id: documentId,
          analysis_status: 'completed',
          analysis_completed_at: new Date()
        });

        console.log(`文件记录已更新，Dify任务ID: ${documentId}`);
      } else {
        console.error('上传失败，响应数据不包含document字段');
      }
    } catch (error) {
      console.error('上传文件到Dify知识库失败:');

      if (error.response) {
        // 服务器响应了错误状态码
        console.error('响应状态码:', error.response.status);
        console.error('响应数据:', error.response.data);
      } else if (error.request) {
        // 请求已发送但没有收到响应
        console.error('未收到响应:', error.request);
      } else {
        // 设置请求时发生错误
        console.error('错误消息:', error.message);
      }

      // 更新文件状态
      await file.update({
        analysis_status: 'failed',
        analysis_error: error.message || '上传到Dify知识库失败'
      });

      console.log('文件状态已更新为failed');
    }

    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('发生错误:', error);
    process.exit(1);
  }
}

// 执行主函数
main().then(() => {
  console.log('测试完成');
  process.exit(0);
}).catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
