/**
 * 认证集成测试
 */

const request = require('supertest');
const app = require('../../src/app');
const { User, Role } = require('../../src/models');
const bcrypt = require('bcryptjs');
const { createTestRole } = require('../utils/testHelpers');

describe('认证集成测试', () => {
  let userRole;

  beforeAll(async () => {
    // 创建测试角色
    userRole = await createTestRole({
      name: 'user',
      description: 'Regular user role'
    });
  });

  describe('注册-登录-获取用户信息流程', () => {
    it('应该能完成完整的注册-登录-获取用户信息流程', async () => {
      // 1. 注册新用户
      const newUser = {
        username: 'integrationuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: '13812345678'
      };

      const registerResponse = await request(app)
        .post('/api/users/register')
        .send(newUser);

      expect(registerResponse.status).toBe(201);
      expect(registerResponse.body.success).toBe(true);
      expect(registerResponse.body.data).toHaveProperty('id');
      expect(registerResponse.body.data.username).toBe(newUser.username);

      // 2. 使用新注册的用户登录
      const loginResponse = await request(app)
        .post('/api/users/login')
        .send({
          username: newUser.username,
          password: newUser.password
        });

      expect(loginResponse.status).toBe(200);
      expect(loginResponse.body.success).toBe(true);
      expect(loginResponse.body.data).toHaveProperty('token');
      expect(loginResponse.body.data).toHaveProperty('user');
      expect(loginResponse.body.data.user.username).toBe(newUser.username);

      const token = loginResponse.body.data.token;

      // 3. 使用令牌获取用户信息
      const userInfoResponse = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${token}`);

      expect(userInfoResponse.status).toBe(200);
      expect(userInfoResponse.body.success).toBe(true);
      expect(userInfoResponse.body.data).toHaveProperty('id');
      expect(userInfoResponse.body.data.username).toBe(newUser.username);
      expect(userInfoResponse.body.data.email).toBe(newUser.email);
      expect(userInfoResponse.body.data).not.toHaveProperty('password');
    });
  });

  describe('密码重置流程', () => {
    it('应该能完成密码修改流程', async () => {
      // 1. 创建测试用户
      const password = await bcrypt.hash('oldpassword', 10);
      const user = await User.create({
        username: 'passworduser',
        email: '<EMAIL>',
        password,
        phone: '13887654321',
        status: 'active',
        roleId: userRole.id
      }, { transaction: global.testTransaction });

      // 2. 登录
      const loginResponse = await request(app)
        .post('/api/users/login')
        .send({
          username: 'passworduser',
          password: 'oldpassword'
        });

      expect(loginResponse.status).toBe(200);
      const token = loginResponse.body.data.token;

      // 3. 修改密码
      const changePasswordResponse = await request(app)
        .put('/api/users/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send({
          currentPassword: 'oldpassword',
          newPassword: 'newpassword123',
          confirmPassword: 'newpassword123'
        });

      expect(changePasswordResponse.status).toBe(200);
      expect(changePasswordResponse.body.success).toBe(true);
      expect(changePasswordResponse.body.message).toContain('密码修改成功');

      // 4. 使用旧密码登录（应该失败）
      const oldPasswordLoginResponse = await request(app)
        .post('/api/users/login')
        .send({
          username: 'passworduser',
          password: 'oldpassword'
        });

      expect(oldPasswordLoginResponse.status).toBe(401);
      expect(oldPasswordLoginResponse.body.success).toBe(false);

      // 5. 使用新密码登录（应该成功）
      const newPasswordLoginResponse = await request(app)
        .post('/api/users/login')
        .send({
          username: 'passworduser',
          password: 'newpassword123'
        });

      expect(newPasswordLoginResponse.status).toBe(200);
      expect(newPasswordLoginResponse.body.success).toBe(true);
      expect(newPasswordLoginResponse.body.data).toHaveProperty('token');
    });
  });

  describe('用户信息更新流程', () => {
    it('应该能完成用户信息更新流程', async () => {
      // 1. 创建测试用户
      const password = await bcrypt.hash('updatepassword', 10);
      const user = await User.create({
        username: 'updateuser',
        email: '<EMAIL>',
        password,
        phone: '13866667777',
        status: 'active',
        roleId: userRole.id
      }, { transaction: global.testTransaction });

      // 2. 登录
      const loginResponse = await request(app)
        .post('/api/users/login')
        .send({
          username: 'updateuser',
          password: 'updatepassword'
        });

      expect(loginResponse.status).toBe(200);
      const token = loginResponse.body.data.token;

      // 3. 更新用户信息
      const updatedInfo = {
        phone: '13999998888',
        email: '<EMAIL>'
      };

      const updateResponse = await request(app)
        .put('/api/users/me')
        .set('Authorization', `Bearer ${token}`)
        .send(updatedInfo);

      expect(updateResponse.status).toBe(200);
      expect(updateResponse.body.success).toBe(true);
      expect(updateResponse.body.message).toContain('更新成功');
      expect(updateResponse.body.data.phone).toBe(updatedInfo.phone);
      expect(updateResponse.body.data.email).toBe(updatedInfo.email);

      // 4. 获取用户信息验证更新
      const userInfoResponse = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${token}`);

      expect(userInfoResponse.status).toBe(200);
      expect(userInfoResponse.body.data.phone).toBe(updatedInfo.phone);
      expect(userInfoResponse.body.data.email).toBe(updatedInfo.email);
    });
  });
});
