/**
 * 文件模型
 *
 * 定义文件数据结构，包括文件名称、类型、大小、路径等信息
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} File模型
 */
module.exports = (sequelize, DataTypes) => {
  const File = sequelize.define('File', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    original_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '原始文件名'
    },
    path: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '文件存储路径'
    },
    type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '文件类型，如pdf, doc, jpg等'
    },
    mime_type: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '文件MIME类型'
    },
    size: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '文件大小（字节）'
    },
    knowledge_base_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'knowledge_bases',
        key: 'id'
      }
    },
    uploader_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected'),
      allowNull: false,
      defaultValue: 'pending',
      comment: '文件状态：待审核、已批准、已拒绝'
    },
    reviewer_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '审核人ID'
    },
    review_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '审核时间'
    },
    reject_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '拒绝原因'
    },
    summary: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '文件内容摘要（由AI生成）'
    },
    detailed_description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '文件详细描述（由AI生成）'
    },
    dify_task_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Dify分析任务ID'
    },
    analysis_status: {
      type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed'),
      allowNull: true,
      defaultValue: 'pending',
      comment: '分析状态：待分析、处理中、已完成、失败'
    },
    analysis_started_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '分析开始时间'
    },
    analysis_completed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '分析完成时间'
    },
    analysis_error: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '分析错误信息'
    }
  }, {
    tableName: 'files',
    timestamps: true
  });

  // 实例方法
  File.prototype.isImage = function() {
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    return imageTypes.includes(this.type.toLowerCase());
  };

  File.prototype.getFullPath = function() {
    const path = require('path');
    return path.join(process.cwd(), this.path);
  };

  // 静态方法
  File.findByType = async function(type) {
    return await this.findAll({
      where: {
        type: type
      }
    });
  };

  File.findByStatus = async function(status) {
    return await this.findAll({
      where: {
        status: status
      }
    });
  };

  // 关联关系
  File.associate = (models) => {
    // 文件与知识库的多对一关系
    File.belongsTo(models.KnowledgeBase, {
      foreignKey: 'knowledge_base_id',
      as: 'knowledgeBase'
    });

    // 文件与上传者的多对一关系
    File.belongsTo(models.User, {
      foreignKey: 'uploader_id',
      as: 'uploader'
    });

    // 文件与审核者的多对一关系
    File.belongsTo(models.User, {
      foreignKey: 'reviewer_id',
      as: 'reviewer'
    });
  };

  return File;
};
