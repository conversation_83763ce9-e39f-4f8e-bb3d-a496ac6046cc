/**
 * 创建文件表迁移文件
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('files', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      original_name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: '原始文件名'
      },
      path: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: '文件存储路径'
      },
      type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '文件类型，如pdf, doc, jpg等'
      },
      mime_type: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '文件MIME类型'
      },
      size: {
        type: Sequelize.BIGINT,
        allowNull: false,
        comment: '文件大小（字节）'
      },
      knowledge_base_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'knowledge_bases',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      uploader_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      status: {
        type: Sequelize.ENUM('pending', 'approved', 'rejected'),
        allowNull: false,
        defaultValue: 'pending',
        comment: '文件状态：待审核、已批准、已拒绝'
      },
      reviewer_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      review_time: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '审核时间'
      },
      reject_reason: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '拒绝原因'
      },
      summary: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '文件内容摘要（由AI生成）'
      },
      detailed_description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '文件详细描述（由AI生成）'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('files');
  }
};
