"use client"

import { useState, useEffect } from 'react'
import { User, Key, Clock, AlertCircle, CheckCircle } from 'lucide-react'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ProfileForm } from './ProfileForm'
import { ChangePasswordForm } from './ChangePasswordForm'
import { ActivityRecordList } from './ActivityRecordList'
import { ProfileCenterProps, ProfileFormFields, ChangePasswordFormFields, ActivityRecord } from './types'
import userService from '@/services/user-service'

/**
 * 个人中心组件
 * 
 * 用于显示和编辑个人信息，修改密码，查看活动记录
 */
export function ProfileCenter({ user }: ProfileCenterProps) {
  const [loading, setLoading] = useState(false)
  const [activityRecords, setActivityRecords] = useState<ActivityRecord[]>([])
  const [loadingRecords, setLoadingRecords] = useState(false)
  const [showSuccessToast, setShowSuccessToast] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')
  const [showErrorToast, setShowErrorToast] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')

  // 获取活动记录
  const fetchActivityRecords = async () => {
    setLoadingRecords(true)
    try {
      // 在实际应用中，这里应该调用API获取活动记录
      // 目前使用模拟数据
      const mockRecords: ActivityRecord[] = [
        {
          id: '1',
          type: 'login',
          description: '登录系统',
          ip_address: '***********',
          created_at: new Date(Date.now() - 1000 * 60 * 5).toISOString()
        },
        {
          id: '2',
          type: 'file_upload',
          description: '上传文件"蔡和森生平简介.pdf"',
          ip_address: '***********',
          created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString()
        },
        {
          id: '3',
          type: 'comment',
          description: '在"蔡和森专题"页面发表评论',
          ip_address: '***********',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString()
        },
        {
          id: '4',
          type: 'download',
          description: '下载文件"蔡和森与中国共产党创建.pdf"',
          ip_address: '***********',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString()
        },
        {
          id: '5',
          type: 'view',
          description: '浏览"蔡和森专题"页面',
          ip_address: '***********',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString()
        }
      ]
      
      setActivityRecords(mockRecords)
    } catch (error) {
      console.error('获取活动记录失败:', error)
      showError('获取活动记录失败，请重试')
    } finally {
      setLoadingRecords(false)
    }
  }

  // 初始化
  useEffect(() => {
    fetchActivityRecords()
  }, [])

  // 处理个人信息提交
  const handleProfileSubmit = async (values: ProfileFormFields) => {
    setLoading(true)
    try {
      // 在实际应用中，这里应该调用API更新个人信息
      // 目前使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      showSuccess('个人信息更新成功')
    } catch (error) {
      console.error('更新个人信息失败:', error)
      showError('更新个人信息失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理修改密码提交
  const handleChangePasswordSubmit = async (values: ChangePasswordFormFields) => {
    setLoading(true)
    try {
      // 在实际应用中，这里应该调用API修改密码
      // 目前使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      showSuccess('密码修改成功')
    } catch (error) {
      console.error('修改密码失败:', error)
      showError('修改密码失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 显示成功提示
  const showSuccess = (message: string) => {
    setSuccessMessage(message)
    setShowSuccessToast(true)
    setTimeout(() => {
      setShowSuccessToast(false)
    }, 3000)
  }

  // 显示错误提示
  const showError = (message: string) => {
    setErrorMessage(message)
    setShowErrorToast(true)
    setTimeout(() => {
      setShowErrorToast(false)
    }, 3000)
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">个人中心</h1>
      
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid grid-cols-3 w-full max-w-md">
          <TabsTrigger value="profile" className="flex items-center">
            <User className="h-4 w-4 mr-2" />
            个人信息
          </TabsTrigger>
          <TabsTrigger value="password" className="flex items-center">
            <Key className="h-4 w-4 mr-2" />
            修改密码
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            活动记录
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile" className="bg-white p-6 rounded-md shadow-sm">
          <h2 className="text-xl font-semibold mb-4">个人信息</h2>
          <ProfileForm
            user={user}
            loading={loading}
            onSubmit={handleProfileSubmit}
          />
        </TabsContent>
        
        <TabsContent value="password" className="bg-white p-6 rounded-md shadow-sm">
          <h2 className="text-xl font-semibold mb-4">修改密码</h2>
          <ChangePasswordForm
            loading={loading}
            onSubmit={handleChangePasswordSubmit}
          />
        </TabsContent>
        
        <TabsContent value="activity" className="bg-white p-6 rounded-md shadow-sm">
          <h2 className="text-xl font-semibold mb-4">活动记录</h2>
          <ActivityRecordList
            records={activityRecords}
            loading={loadingRecords}
          />
        </TabsContent>
      </Tabs>
      
      {/* 成功提示 */}
      {showSuccessToast && (
        <div className="fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md z-50 animate-fade-in-up">
          <div className="flex">
            <div className="py-1">
              <CheckCircle className="h-6 w-6 text-green-500 mr-4" />
            </div>
            <div>
              <p className="font-bold">成功</p>
              <p className="text-sm">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {showErrorToast && (
        <div className="fixed bottom-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-md z-50 animate-fade-in-up">
          <div className="flex">
            <div className="py-1">
              <AlertCircle className="h-6 w-6 text-red-500 mr-4" />
            </div>
            <div>
              <p className="font-bold">错误</p>
              <p className="text-sm">{errorMessage}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
