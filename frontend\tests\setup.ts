/**
 * 测试环境设置
 *
 * 用于配置测试环境和全局设置
 */

// 导入测试库
import '@testing-library/jest-dom'
import axios from 'axios'

// 设置axios默认配置，使其能够连接到后端API
axios.defaults.baseURL = 'http://localhost:5001/api'
axios.defaults.headers.common['Content-Type'] = 'application/json'
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status < 500 // 默认只有2xx状态码才会resolve
}

// 设置测试超时时间
jest.setTimeout(30000) // 30秒

// 解决React版本不兼容问题
jest.mock('react', () => {
  const originalReact = jest.requireActual('react');
  return {
    ...originalReact,
    // 确保使用一致的React版本
    createElement: originalReact.createElement,
    Fragment: originalReact.Fragment,
  };
});

// 模拟全局对象
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// 模拟window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // 已弃用
    removeListener: jest.fn(), // 已弃用
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// 模拟IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// 模拟localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {}
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString()
    },
    removeItem: (key: string) => {
      delete store[key]
    },
    clear: () => {
      store = {}
    },
  }
})()

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// 模拟sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: localStorageMock,
})

// 模拟URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url')
global.URL.revokeObjectURL = jest.fn()

// 不模拟console.error，以便查看完整错误信息
// const originalConsoleError = console.error
// console.error = (...args: any[]) => {
//   // 过滤掉一些常见的React测试警告
//   if (
//     typeof args[0] === 'string' &&
//     (args[0].includes('Warning:') || args[0].includes('Error:'))
//   ) {
//     return
//   }
//   originalConsoleError(...args)
// }
