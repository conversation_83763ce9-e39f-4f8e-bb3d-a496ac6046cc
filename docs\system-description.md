# 和富家族研究平台系统说明文档

## 首页 (/)

### 页面概述
首页是系统的入口页面，展示系统的主要内容和功能入口，以红色革命精神为主题，突出家族研究的核心价值。首页主要包括导航栏、英雄区域、家族历史长河、革命先辈事迹、纪念活动和页脚等部分。

### 访问权限
- **可访问用户**：所有用户（包括未登录用户）
- **特殊权限**：无需特殊权限，但纪念活动的管理功能需要登录且具有管理权限

### 页面内容

#### 1. 导航栏 (Navbar)
- **位置**：页面顶部固定
- **内容**：
  - 系统Logo："和富家族研究平台"文字标识（绿色 #1e8e3e）
  - 主导航菜单：
    - 首页：链接到首页 (/)
    - 家族专题：链接到家族专题页面 (/family-topic)
    - 个人专题：下拉菜单，包含以下选项：
      - 蔡和森：链接到蔡和森个人页面 (/personal/cai-hesen)
      - 向警予：链接到向警予个人页面 (/personal/xiang-jingyu)
      - 蔡畅：链接到蔡畅个人页面 (/personal/cai-chang)
      - 李富春：链接到李富春个人页面 (/personal/li-fuchun)
      - 葛健豪：链接到葛健豪个人页面 (/personal/ge-jianhao)
    - 知识库：链接到知识库页面 (/knowledge)
    - 数据查询：链接到数据查询页面 (/data-mining)
    - AI研究助手：链接到AI研究助手页面 (/ai-assistant)
    - 系统管理：链接到系统管理页面 (/system-management)
  - 用户操作区：
    - 未登录状态：
      - 登录按钮：绿色边框，白色背景
      - 注册按钮：橙色背景，白色文字
    - 已登录状态：
      - 通知图标：铃铛图标，右上角有红点表示有未读通知
      - 用户头像：圆形头像或默认用户图标
- **字段来源**：
  - 导航菜单项：组件内硬编码的navLinks数组
  - 用户信息：从localStorage获取的"isLoggedIn"和"userData"
- **功能说明**：
  - Logo点击：跳转到首页
  - 导航菜单项点击：跳转到对应页面
  - 个人专题下拉菜单：
    - 触发方式：点击"个人专题"或悬停（桌面端）
    - 显示内容：5个历史人物的链接
    - 点击行为：跳转到对应的个人专题页面
  - 登录按钮点击：
    - 触发方式：点击"登录"按钮
    - 行为：设置isLoginModalOpen为true，打开登录弹窗
  - 注册按钮点击：
    - 触发方式：点击"注册"按钮
    - 行为：设置isLoginModalOpen和isRegisterModalOpen为true，打开登录弹窗并显示注册表单
  - 通知图标点击（已登录状态）：
    - 触发方式：点击铃铛图标
    - 显示内容：通知下拉菜单，包含：
      - 标题："通知"
      - "全部标为已读"按钮（当有未读通知时显示）
      - 通知列表（最多显示4条）：
        - 系统通知
        - 活动通知
        - 消息通知
      - 底部链接："查看全部通知"，链接到/notifications页面
    - 每个通知项包含：
      - 通知标题
      - 通知时间
      - 通知内容摘要
      - 背景色：未读通知为浅蓝色背景
    - 点击通知项：
      - 打开通知详情模态框
      - 显示完整通知内容
      - 将未读通知标记为已读
  - 用户头像点击（已登录状态）：
    - 触发方式：点击用户头像
    - 显示内容：用户下拉菜单，包含：
      - 用户信息区域：
        - 用户名：从userData获取，默认为"张文浩"
        - 用户角色："研究员"（硬编码）
      - 菜单项：
        - 个人中心：链接到/personal-center页面
        - 账号设置：链接到/personal-center/settings页面
        - 分隔线
        - 退出登录：点击后调用handleLogout函数
    - handleLogout函数行为：
      - 设置isLoggedIn为false
      - 设置userData为null
      - 从localStorage移除"isLoggedIn"和"userData"
      - 关闭菜单
      - 重定向到首页
- **响应式设计**：
  - 桌面端（md及以上屏幕）：
    - 水平排列的导航菜单
    - 下拉菜单通过悬停或点击触发
    - 完整显示用户操作区
  - 移动端（小于md屏幕）：
    - 折叠为汉堡菜单按钮
    - 点击汉堡菜单按钮展开垂直排列的导航菜单
    - 个人专题通过点击展开子菜单
    - 用户操作区显示在展开菜单的底部

#### 2. 英雄区域 (Hero Section)
- **位置**：导航栏下方
- **内容**：
  - 标题：传承红色基因，弘扬革命精神
  - 副标题：铭记革命先辈的丰功伟绩，传承红色基因，弘扬革命精神，为实现中华民族伟大复兴的中国梦而不懈奋斗。
  - 按钮：了解更多
- **字段来源**：组件内硬编码
- **功能说明**：
  - "了解更多"按钮点击：
    - 触发方式：点击按钮
    - 行为：跳转到家族专题页面 (/family-topic)
    - 可点击对象：所有用户
- **样式特点**：
  - 背景色：绿色 (#1e7a43)
  - 文字颜色：白色
  - 按钮颜色：橙色 (#f5a623)
  - 内容居左对齐
  - 内边距：垂直方向24px，水平方向自适应

#### 3. 家族历史长河 (History Timeline)
- **位置**：英雄区域下方
- **内容**：
  - 标题：家族历史长河
  - 三个历史事件卡片，每个包含：
    1. 第一个事件：
       - 标题：家族集体赴法勤工俭学，奠定革命思想基础（1919-1921年）
       - 描述：葛健豪突破年龄限制赴法，时年55岁的她变卖首饰筹款，与儿子蔡和森、女儿蔡畅及儿媳向警予一同赴法勤工俭学，成为留法群体中年龄最大的女性。蔡和森在法国期间系统研究马克思主义，于1920年致信毛泽东提出"正式成立中国共产党"，成为中共名称的首倡者。向警予与蔡畅的革命觉醒，形成"向蔡同盟"与"李蔡同盟"，成为革命伴侣的典范。这一时期不仅完成了家族成员的思想启蒙，更通过集体行动将马克思主义引入中国，直接推动了中国共产党的早期组织建设。
    2. 第二个事件：
       - 标题：向警予与蔡和森牺牲，家族以信仰延续革命（1928-1931年）
       - 描述：1928年，向警予因叛徒出卖在武汉被捕牺牲，成为党史上首位女性中央委员的殉道者。蔡和森闻讯后撰写《向警予同志传》，称其为"中国无产阶级永远的爱人"。1931年，蔡和森在香港被叛徒顾顺章指认，遭国民党逮捕，狱中受酷刑仍高呼革命口号，最终被钉墙虐杀，年仅36岁。面对儿子蔡麓仙、蔡林蒸的牺牲及向警予的离世，葛健豪以"革命母亲"的信念支撑家族，掩护党组织活动，抚养烈士遗孤。家族成员以生命践行信仰，将革命火种传递给第三代。
    3. 第三个事件：
       - 标题：建国后家族成员的国家建设与信仰传承（1949年后）
       - 描述：蔡畅历任全国妇联主席，推动妇女运动理论与实践；李富春担任国务院副总理，主导社会主义经济建设，成为"正国级"领导人。第三代革命者的成长：孙女刘昂任周总理秘书，李特特（蔡畅之女）成为冶金专家，家族成员延续了"为革命奉献"的家风。2015年"信仰家族纪念活动"在北京举办，总结葛健豪等人的历史贡献，强调"共同信仰"的家族联结本质，展现了信仰超越血缘纽带的力量。
- **字段来源**：组件内硬编码
- **功能说明**：
  - 纯展示内容，无交互功能
  - 不可点击，不可编辑
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 轻微阴影效果
  - 垂直排列的时间线布局
  - 每个事件卡片之间有间距

#### 4. 革命先辈事迹 (Revolutionary Pioneers)
- **位置**：家族历史长河下方
- **内容**：
  - 标题：革命先辈事迹
  - 五个革命先辈卡片，每个包含：
    1. 蔡和森：
       - 头像：/images/cai-hesen.png
       - 姓名：蔡和森
       - 生卒年份：1895—1931
       - 简介：中国共产党早期重要领导人、杰出的无产阶级革命家、理论家和宣传家，"中国共产党"名称的首倡者
    2. 向警予：
       - 头像：/images/xiang-jingyu.png
       - 姓名：向警予
       - 生卒年份：1895—1928
       - 简介：中国共产党早期领导人及创始人之一，杰出的无产阶级革命家、妇女解放运动先驱
    3. 李富春：
       - 头像：/images/li-fuchun.png
       - 姓名：李富春
       - 生卒年份：1900—1975
       - 简介：中国共产党创建时期入党的老党员，杰出的无产阶级革命家，中国社会主义经济建设的奠基者
    4. 蔡畅：
       - 头像：/images/cai-chang.png
       - 姓名：蔡畅
       - 生卒年份：1900—1990
       - 简介：中国共产党最早的党员之一，中国妇女运动的先驱和卓越领导者、国际进步妇女运动的著名活动家
    5. 葛健豪：
       - 头像：/images/ge-jianhao.png
       - 姓名：葛健豪
       - 生卒年份：1865—1943
       - 简介：中国近代史上罕见的"革命母亲"，培养出四位中央委员，唯一以非党员身份立传的杰出女性
- **字段来源**：组件内定义的pioneers数组
- **功能说明**：
  - 点击任意先辈卡片：
    - 触发方式：点击整个卡片区域
    - 行为：跳转到对应的个人专题页面（如/personal/cai-hesen）
    - 可点击对象：所有用户均可点击
- **样式特点**：
  - 网格布局：
    - 桌面端（lg及以上）：3列
    - 平板（md）：2列
    - 手机（小于md）：1列
  - 白色背景卡片
  - 圆形头像（左侧）
  - 文字信息（右侧）
  - 悬停效果：阴影加深
  - 卡片内部布局：
    - 桌面端：头像在左，文字在右
    - 移动端：头像在上，文字在下（居中对齐）

#### 5. 纪念活动 (Memorial Activities)
- **位置**：革命先辈事迹下方
- **内容**：
  - 标题：纪念活动
  - 管理模式切换按钮（仅对有管理权限的用户显示）
  - 活动卡片列表，默认显示3个活动：
    1. 第一个活动：
       - 标题：革命先烈纪念日活动
       - 日期：2024年4月5日
       - 描述：缅怀革命先烈，传承红色基因，组织家族成员参观革命纪念馆，重温入党誓词。
       - 图片：占位图片
       - 状态：已发布（published）
       - 附件：活动方案.pdf（2.3 MB）
    2. 第二个活动：
       - 标题：红色教育基地参观
       - 日期：2024年5月15日
       - 描述：组织第一代革命家后裔参观，了解家族革命历史，传承红色基因。
       - 图片：占位图片
       - 状态：已发布（published）
       - 附件：无
    3. 第三个活动：
       - 标题：革命史料讨论会
       - 日期：2024年6月20日
       - 描述：邀请历史学家和相关研究者聚集研讨，整理相关史料，传承革命精神。
       - 图片：占位图片
       - 状态：草稿（draft）
       - 附件：无
- **字段来源**：组件内定义的initialActivities数组
- **功能说明**：
  - 普通模式（默认）：
    - 只显示状态为"published"的活动
    - 点击活动卡片：
      - 触发方式：点击卡片任意区域
      - 行为：打开活动详情弹窗（ViewActivityModal）
      - 弹窗内容：
        - 活动标题
        - 活动图片（大图）
        - 活动日期
        - 活动描述（完整）
        - 附件列表（如有）：
          - 附件名称
          - 附件类型图标
          - 附件大小
          - 下载按钮
        - 关闭按钮（X）
  - 管理模式（仅对有管理权限的用户显示）：
    - 切换按钮：
      - 触发方式：点击"管理活动"/"退出管理"按钮
      - 行为：切换isManagementMode状态
      - 按钮文本：管理模式下显示"退出管理"，否则显示"管理活动"
    - 显示所有活动（包括草稿和已下架）
    - 添加活动按钮：
      - 触发方式：点击"新增活动"按钮（仅在管理模式下显示）
      - 行为：打开添加活动弹窗（EditActivityModal，isNew=true）
      - 弹窗内容：
        - 标题："添加新的活动"
        - 表单字段：
          - 活动标题（文本输入框）
          - 活动日期（日期选择器）
          - 活动描述（多行文本框）
          - 活动状态（下拉选择：草稿/已发布）
          - 上传图片按钮
          - 上传附件按钮
        - 操作按钮：
          - 取消
          - 保存
    - 活动卡片管理选项：
      - 状态标签：显示活动状态（已发布/草稿/已下架）
      - 编辑按钮：
        - 触发方式：点击卡片上的编辑图标
        - 行为：打开编辑活动弹窗（EditActivityModal，isNew=false）
        - 弹窗内容：同添加活动，但预填充现有数据
      - 删除按钮：
        - 触发方式：点击卡片上的删除图标
        - 行为：打开删除确认弹窗
        - 弹窗内容：
          - 提示文本："您确定要删除活动 [活动标题] 吗？此操作不可撤销。"
          - 操作按钮：
            - 取消
            - 确认删除（红色）
      - 状态切换按钮：
        - 触发方式：点击卡片上的眼睛/眼睛关闭图标
        - 行为：切换活动状态（已发布/已下架）
- **样式特点**：
  - 网格布局：
    - 桌面端（md及以上）：3列
    - 移动端（小于md）：1列
  - 白色背景卡片
  - 活动图片占据卡片上半部分
  - 活动信息占据卡片下半部分
  - 管理模式下卡片右上角显示状态标签
  - 管理模式下卡片底部显示操作按钮

#### 6. 页脚 (Footer)
- **位置**：页面底部
- **内容**：
  - 系统简介：创新家族档案馆在中国革命和建设过程中，为劳动人民解放事业做出重大贡献的革命先辈及其后代们的成就与事迹，同时承袭革命传统，弘扬红色精神，在新时代继续发扬先辈们的作风。
  - 联系信息：
    - 地址：上海市某某区域
    - 电话：010-12345678
    - 邮箱：<EMAIL>
  - 版权信息：© 2025 和富家族研究平台 版权所有
- **字段来源**：组件内硬编码
- **功能说明**：
  - 纯展示内容，无交互功能
- **样式特点**：
  - 深色背景 (#111827)
  - 白色和浅灰色文字
  - 分隔线分割不同区域
  - 响应式布局：
    - 桌面端：联系信息并排显示
    - 移动端：联系信息垂直堆叠

### 登录弹窗 (LoginModal)
- **触发方式**：点击导航栏中的"登录"或"注册"按钮
- **内容**：
  - 标题：用户登录/注册账号（根据isRegister状态切换）
  - 登录方式：账号密码登录
    - 用户名/邮箱输入框
    - 密码输入框
  - 注册表单（当isRegister为true时显示）：
    - 用户名输入框
    - 密码输入框
    - 确认密码输入框
    - 手机号输入框
    - 邮箱输入框
  - 操作按钮：
    - 登录/注册按钮
    - 关闭按钮（X）
- **字段来源**：组件内状态管理
- **功能说明**：
  - 登录表单提交：
    - 触发方式：点击"登录"按钮
    - 行为：调用handleLogin函数，验证用户名和密码
    - 结果：
      - 成功：调用onLogin回调，传入用户数据，关闭弹窗
      - 失败：
        - 用户不存在：显示错误信息，提示用户注册
        - 密码错误：显示错误信息
  - 注册表单提交：
    - 触发方式：点击"注册"按钮
    - 行为：调用handleRegister函数，验证用户名、密码、手机号和邮箱
    - 结果：
      - 成功：显示登录弹窗，提示用户登录
      - 失败：显示错误信息
  - 关闭弹窗：
    - 触发方式：点击关闭按钮(X)或弹窗外区域
    - 行为：调用onClose函数，关闭弹窗
- **样式特点**：
  - 白色背景
  - 圆角边框
  - 居中显示
  - 最大宽度限制
  - 弹窗外区域半透明黑色遮罩

### 通知详情模态框 (NotificationDetailModal)
- **触发方式**：点击通知下拉菜单中的通知项
- **内容**：
  - 标题：通知标题
  - 通知类型图标：根据通知类型显示不同颜色的图标
  - 通知信息：
    - 类型标签：系统通知/活动通知/消息通知
    - 时间：通知发送时间
  - 通知内容：完整通知内容
  - 详细内容（如有）：额外的详细信息，显示在灰色背景区域
- **字段来源**：从选中的通知对象(notification)获取
- **功能说明**：
  - 关闭模态框：
    - 触发方式：点击关闭按钮或模态框外区域
    - 行为：调用onClose函数，关闭模态框
- **样式特点**：
  - 白色背景
  - 圆角边框
  - 居中显示
  - 最大宽度限制
  - 模态框外区域半透明黑色遮罩

### 技术实现
- **页面组件**：app/page.tsx
- **主要子组件**：
  - HeroSection：components/hero-section.tsx
  - HistoryTimeline：components/history-timeline.tsx
  - RevolutionaryPioneers：components/revolutionary-pioneers.tsx
  - MemorialActivities：components/memorial-activities.tsx
  - Footer：components/footer.tsx
  - Navbar：components/navbar.tsx（在HeroSection中引入）
  - LoginModal：components/login-modal.tsx（在Navbar中引入）
  - NotificationDetailModal：components/notification-detail-modal.tsx（在通知功能中使用）
- **样式实现**：
  - 使用Tailwind CSS进行样式设计
  - 背景色：浅米色 (#fdf9f1)
  - 主题色：绿色 (#1e7a43)、橙色 (#f5a623)
  - 响应式设计，适配不同屏幕尺寸
- **状态管理**：
  - 使用React的useState和useEffect钩子管理组件状态
  - 使用localStorage存储和获取用户登录状态和信息

### 数据流向
- **页面加载**：
  1. 加载app/page.tsx组件
  2. 渲染HeroSection、HistoryTimeline、RevolutionaryPioneers、MemorialActivities和Footer组件
  3. HeroSection组件内渲染Navbar组件
  4. Navbar组件检查localStorage中的用户登录状态
  5. 根据登录状态显示不同的用户操作区
- **用户登录**：
  1. 用户点击登录按钮，打开登录弹窗
  2. 用户输入用户名和密码
  3. 点击登录按钮，调用handleLogin函数
  4. 验证成功后，调用onLogin回调，更新isLoggedIn和userData状态
  5. 将登录状态和用户数据保存到localStorage
  6. 关闭登录弹窗，更新导航栏显示
- **用户注册**：
  1. 用户点击注册按钮，打开注册弹窗
  2. 用户输入用户名、密码等信息
  3. 点击注册按钮，调用handleRegister函数
  4. 注册成功后，显示登录弹窗
- **查看活动详情**：
  1. 用户点击活动卡片
  2. 设置selectedActivity为当前活动，modalState.type为"view"
  3. 渲染ViewActivityModal组件，显示活动详情
  4. 用户点击关闭按钮，调用closeModal函数，关闭弹窗
- **管理活动**（仅对有管理权限的用户显示）：
  1. 用户点击"管理活动"按钮，切换到管理模式
  2. 用户可以添加、编辑、删除活动
  3. 添加活动：打开EditActivityModal，用户填写表单，点击保存
  4. 编辑活动：打开EditActivityModal，预填充活动数据，用户修改后点击保存
  5. 删除活动：打开DeleteConfirmModal，用户确认后删除活动
  6. 所有操作在前端状态中更新，实际应用中应调用API与后端交互

### 特殊说明
1. **数据持久化**：
   - 当前实现中，活动数据存储在前端状态中，页面刷新后会重置为初始数据
   - 用户登录状态通过localStorage持久化，但这只是前端模拟
   - 实际应用中，所有数据操作应通过API与后端数据库交互
2. **图片和附件处理**：
   - 当前实现中，图片和附件上传只在前端模拟
   - 实际应用中，应实现文件上传到服务器的功能
3. **响应式设计**：
   - 页面布局会根据屏幕尺寸自动调整
   - 移动端使用汉堡菜单代替水平导航
   - 卡片布局从多列变为单列
4. **通知功能**：
   - 通知数据为模拟数据，实际应用中应从后端API获取
   - 通知状态（已读/未读）在前端状态中管理，实际应用中应与后端同步
5. **权限控制**：
   - 纪念活动的管理功能仅对有管理权限的用户显示
   - 当前实现中，权限控制通过前端状态模拟，实际应用中应基于后端权限系统
6. **AI助手配置**：
   - 系统预设了多种AI助手，包括：
     - 5个个人专题助手（对应5个革命先辈）
     - 1个数据查询助手
     - 1个知识库文件分析助手
   - 这些预设助手可以在系统管理页面的AI管理标签页中编辑配置
   - 知识库文件上传时会调用知识库文件分析助手配置的两个API接口

## 家族专题页面 (/family-topic)

### 页面概述
家族专题页面展示家族历史概述和重要事件时间线，以时间轴的形式呈现家族成员的重要历史事件，突出家族在中国革命历史中的重要作用。该页面为用户提供了对家族历史的全面了解，并支持管理员编辑和添加历史事件。

### 访问权限
- **可访问用户**：所有用户（包括未登录用户）
- **特殊权限**：时间轴编辑功能需要管理员权限

### 页面内容

#### 1. 导航栏 (Navbar)
- **位置**：页面顶部固定
- **内容**：与首页相同的导航栏组件
- **功能说明**：与首页相同

#### 2. 英雄区域
- **位置**：导航栏下方
- **内容**：
  - 标题：家族大事记
  - 副标题：追溯革命先辈的光辉足迹，传承红色基因，弘扬革命精神，为实现中华民族伟大复兴的中国梦而不懈奋斗。
- **字段来源**：组件内硬编码
- **功能说明**：
  - 纯展示内容，无交互功能
- **样式特点**：
  - 背景色：绿色 (#1e7a43)
  - 文字颜色：白色
  - 内容居左对齐
  - 内边距：垂直方向16px，水平方向自适应

#### 3. 家族历史概述
- **位置**：英雄区域下方
- **内容**：
  - 标题：家族历史概述
  - 内容段落：
    - 第一段：葛健豪家族是中国革命史上一个特殊的家族，其成员在中国共产党的创建和发展过程中发挥了重要作用。葛健豪作为"革命母亲"，培养了蔡和森、蔡畅、向警予、李富春等杰出的革命家，他们在中国革命和建设的不同阶段都做出了重要贡献。
    - 第二段：这个家族的革命历程，从五四运动前后开始，经历了建党、大革命、土地革命、抗日战争、解放战争，直至新中国成立后的社会主义建设，展现了中国共产党人的革命精神和家国情怀。
- **字段来源**：组件内硬编码
- **功能说明**：
  - 纯展示内容，无交互功能
- **样式特点**：
  - 白色背景
  - 圆角边框
  - 轻微阴影效果
  - 居中标题
  - 内容最大宽度限制，确保良好的阅读体验

#### 4. 家族重要事件时间线 (FamilyTimeline)
- **位置**：家族历史概述下方
- **内容**：
  - 标题：家族重要事件时间线
  - 编辑按钮（仅对有管理权限的用户显示）：
    - 编辑模式下显示"完成编辑"
    - 非编辑模式下显示"编辑时间轴"
  - 添加事件按钮（仅在编辑模式下显示）
  - 时间轴：
    - 中心线：绿色垂直线
    - 事件节点：根据事件ID分配不同颜色（绿色、橙色、蓝色）
    - 事件卡片：左右交替排列
- **字段来源**：组件内定义的timelineEvents数组
- **功能说明**：
  - 查看事件详情：
    - 触发方式：点击事件卡片
    - 行为：打开事件详情模态框
    - 模态框内容：
      - 事件年份
      - 事件标题
      - 事件详细内容
      - 关闭按钮
    - 可点击对象：所有用户
  - 编辑时间轴（仅对管理员显示）：
    - 触发方式：点击"编辑时间轴"按钮
    - 行为：切换isEditing状态
    - 编辑模式下显示：
      - "完成编辑"按钮
      - "添加事件"按钮
      - 每个事件卡片上的编辑和删除按钮
  - 添加事件（仅在编辑模式下）：
    - 触发方式：点击"添加事件"按钮
    - 行为：打开添加事件弹窗
    - 弹窗内容：
      - 年份选择器
      - 标题输入框
      - 描述输入框
      - 详细内容输入框
      - 事件级别（个人级影响、家族级影响、国家级影响）
      - 上传图标按钮
      - 保存按钮
  - 编辑事件（仅在编辑模式下）：
    - 触发方式：点击事件卡片上的编辑按钮
    - 行为：打开编辑事件弹窗
    - 弹窗内容：同添加事件，但预填充现有数据
  - 删除事件（仅在编辑模式下）：
    - 触发方式：点击事件卡片上的删除按钮
    - 行为：显示确认提示，确认后删除事件
- **样式特点**：
  - 白色背景
  - 圆角边框
  - 轻微阴影效果
  - 垂直时间轴设计
  - 事件卡片左右交替排列
  - 每个事件使用不同颜色标识（绿色、橙色、蓝色）
  - 响应式设计：
    - 桌面端：左右交替排列的事件卡片
    - 移动端：单列排列的事件卡片

### 技术实现
- **页面组件**：app/family-topic/page.tsx
- **主要子组件**：
  - Navbar：components/navbar.tsx
  - FamilyTimeline：components/family-timeline.tsx
- **样式实现**：
  - 使用Tailwind CSS进行样式设计
  - 背景色：浅米色 (#fdf9f1)
  - 主题色：绿色 (#1e7a43)
  - 响应式设计，适配不同屏幕尺寸
- **状态管理**：
  - 使用React的useState钩子管理组件状态
  - 时间轴事件数据存储在组件内的timelineEvents数组中

### 数据流向
- **页面加载**：
  1. 加载app/family-topic/page.tsx组件
  2. 渲染Navbar和FamilyTimeline组件
  3. FamilyTimeline组件从内部定义的timelineEvents数组加载事件数据
- **查看事件详情**：
  1. 用户点击事件卡片
  2. 设置selectedEvent为当前事件
  3. 渲染事件详情模态框
  4. 用户点击关闭按钮，调用closeModal函数，关闭模态框
- **编辑时间轴**（仅对有管理权限的用户显示）：
  1. 有管理权限的用户点击"编辑时间轴"按钮
  2. 设置isEditing为true
  3. 显示编辑界面元素（添加事件按钮、事件编辑和删除按钮）
  4. 有管理权限的用户可以添加、编辑或删除事件
  5. 有管理权限的用户点击"完成编辑"按钮，设置isEditing为false

### 特殊说明
1. **数据持久化**：
   - 当前实现中，时间轴事件数据存储在前端状态中，页面刷新后会重置为初始数据
   - 实际应用中，所有数据操作应通过API与后端数据库交互
2. **图片上传处理**：
   - 当前实现中，事件图标上传只在前端模拟
   - 实际应用中，应实现图片上传到服务器的功能
3. **权限控制**：
   - 时间轴编辑功能仅对有管理权限的用户显示
   - 当前实现中，权限控制通过前端状态模拟，实际应用中应基于后端权限系统
4. **响应式设计**：
   - 桌面端：左右交替排列的事件卡片
   - 移动端：单列排列的事件卡片，时间轴位于左侧

## 个人专题页面 (/personal/[slug])

### 页面概述
个人专题页面展示家族成员的详细个人资料、生平事迹、人生轨迹、相关资料和留言互动区域，同时集成了AI研究助手功能，为用户提供针对特定历史人物的智能问答服务。页面采用左右分栏布局，左侧为AI助手，右侧为个人资料展示。

### 访问权限
- **可访问用户**：所有用户（包括未登录用户）
- **特殊权限**：资料管理功能需要管理员权限，留言功能需要登录

### 页面内容

#### 1. 导航栏 (Navbar)
- **位置**：页面顶部固定
- **内容**：与首页相同的导航栏组件
- **功能说明**：与首页相同

#### 2. 页面布局
- **左侧区域**：
  - 宽度：桌面端固定40%宽度，移动端100%宽度
  - 内容：AI研究助手
  - 位置：桌面端固定在左侧，移动端位于个人资料上方
- **右侧区域**：
  - 宽度：桌面端60%宽度并右移40%，移动端100%宽度
  - 内容：个人资料展示
  - 位置：桌面端位于右侧，可滚动，移动端位于AI助手下方

#### 3. AI研究助手 (AIAssistant)
- **位置**：页面左侧
- **内容**：
  - 标题：[人物名称]研究助手
  - 对话区域：显示与AI助手的对话历史
  - 输入框：用于输入问题
  - 发送按钮：发送问题给AI助手
- **字段来源**：
  - 人物名称：从URL参数slug获取对应的人物数据
  - 对话历史：组件内状态管理
  - AI配置：从系统设置-AI管理中获取对应个人专题助手的配置
- **功能说明**：
  - 初始消息：
    - 内容：根据助手配置决定是否显示欢迎消息
    - 来源：从系统设置-AI管理中获取对应个人专题助手的配置
    - 显示规则：只有当助手配置中设置了初始消息时才显示
  - 发送消息：
    - 触发方式：点击发送按钮或按Enter键
    - 行为：
      - 已登录用户：将用户输入添加到对话历史，调用Dify API获取回复
      - 未登录用户：显示登录弹窗，提示需要登录后才能使用AI助手
    - API调用参数：
      - Authorization: Bearer token（从系统设置中配置）
      - app_id: 应用ID（从系统设置中配置）
      - app_code: 应用代码（从系统设置中配置）
      - end_user_id: 当前登录用户ID
      - Content-Type: application/json
    - 请求地址：/api/chat-messages（从系统设置中配置基础URL）
  - 对话滚动：
    - 新消息自动滚动到可见区域
- **样式特点**：
  - 白色背景
  - 用户消息右对齐，浅绿色背景
  - AI助手消息左对齐，灰色背景
  - 固定高度，可滚动查看历史消息
  - 底部固定输入区域

#### 4. 个人资料 (PersonalProfile)
- **位置**：页面右侧
- **内容**：分为多个区块展示人物信息
- **字段来源**：从URL参数slug获取对应的人物数据

##### 4.1 基本信息区
- **位置**：个人资料顶部
- **内容**：
  - 人物头像：大图展示
  - 姓名：中文名
  - 英文名：英文名或拼音
  - 生卒年月：出生和去世日期
  - 籍贯：出生地或祖籍
  - 学历：教育背景
  - 简介：人物简要介绍
- **字段来源**：person对象的相应属性
- **功能说明**：
  - 纯展示内容，无交互功能
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 轻微阴影效果
  - 左侧头像，右侧信息
  - 响应式布局：移动端头像在上，信息在下

##### 4.2 人生轨迹区
- **位置**：基本信息区下方
- **内容**：
  - 标题：人生轨迹
  - 时间线：按年、月、日的时间顺序展示人物生平重要事件
- **字段来源**：
  - 简单时间线：person.timeline数组
  - 详细时间线：person.detailedTimeline数组（如果存在）
- **数据结构**：
  - **简单时间线**：
    - `year`: 事件发生的年份（字符串格式，如"1895年3月30日"）
    - `event`: 事件描述
  - **详细时间线**（三级层级结构）：
    - 年份级别：
      - `year`: 年份（数字格式）
      - `title`: 年份标题
      - `months`: 月份事件数组
    - 月份级别：
      - `month`: 月份（数字格式，1-12）
      - `title`: 月份标题
      - `days`: 日期事件数组
    - 日期级别：
      - `day`: 日期（数字格式，1-31）
      - `content`: 事件内容
- **功能说明**：
  - **简单时间线模式**（当不存在detailedTimeline时）：
    - 点击事件卡片：
      - 触发方式：点击任意事件卡片
      - 行为：显示事件详情弹窗
      - 弹窗内容：事件年份、事件标题、详细描述、相关历史图片
      - 可点击对象：所有用户
  - **详细时间线模式**（当存在detailedTimeline时）：
    - 年份操作：
      - 触发方式：点击年份行
      - 行为：展开/折叠该年份下的所有月份
      - 可点击对象：所有用户
    - 月份操作：
      - 触发方式：点击月份行
      - 行为：展开/折叠该月份下的所有日期
      - 可点击对象：所有用户
    - 日期操作：
      - 触发方式：点击日期
      - 行为：显示日期详情弹窗
      - 弹窗内容：完整日期（年月日）、事件详细内容、相关历史图片
      - 可点击对象：所有用户
- **样式特点**：
  - **简单时间线**：
    - 白色背景卡片
    - 圆角边框
    - 轻微阴影效果
    - 垂直排列的时间线
    - 每个事件卡片左侧有绿色边框标识
  - **详细时间线**：
    - 年份级别：
      - 白色背景卡片
      - 左侧绿色边框
      - 展开/折叠图标
    - 月份级别：
      - 浅灰色背景
      - 日历图标
      - 展开/折叠图标
    - 日期级别：
      - 网格布局
      - 边框圆角
      - 悬停效果

##### 4.3 相关资料区
- **位置**：人生轨迹区下方
- **内容**：
  - 标题：相关资料
  - 管理按钮（仅对有相应权限的用户显示）：
    - 管理模式下显示"退出管理"
    - 非管理模式下显示"管理资料"
  - 添加资料按钮（仅在管理模式下显示）
  - 资料卡片列表：
    - 资料图片（创建资料时上传的图片）
    - 资料标题
    - 资料描述
    - PDF文档标识（如果有PDF文件）
- **字段来源**：person.materials数组
- **数据结构**：
  - `id`: 资料唯一标识符
  - `title`: 资料标题
  - `description`: 资料描述
  - `image`: 资料封面图片URL
  - `pdfUrl`: PDF文件URL（可选）
- **功能说明**：
  - 查看资料详情：
    - 触发方式：点击资料卡片
    - 行为：打开资料详情弹窗
    - 弹窗内容：
      - 资料标题
      - 资料概述
      - 详细描述
      - 下载PDF按钮（如果有PDF文件）
      - 关闭按钮
    - 可点击对象：所有用户
  - 下载PDF文件：
    - 触发方式：点击下载PDF按钮
    - 行为：直接下载对应的PDF文件到用户本地
    - 权限控制：
      - 已登录用户：可以下载PDF文件
      - 未登录用户：显示提示信息，要求登录后才能下载
    - 下载方式：直接下载文件，不提供在线预览功能
  - 管理资料（仅对管理员显示）：
    - 触发方式：点击"管理资料"按钮
    - 行为：切换isManagementMode状态
    - 管理模式下显示：
      - "退出管理"按钮
      - "添加资料"按钮
      - 每个资料卡片上的编辑和删除按钮
  - 添加资料（仅在管理模式下）：
    - 触发方式：点击"添加资料"按钮
    - 行为：打开添加资料弹窗
    - 弹窗内容：
      - 标题输入框
      - 描述输入框
      - 上传图片按钮
      - 上传PDF按钮
      - 保存按钮
  - 编辑资料（仅在管理模式下）：
    - 触发方式：点击资料卡片上的编辑按钮
    - 行为：打开编辑资料弹窗
    - 弹窗内容：同添加资料，但预填充现有数据
  - 删除资料（仅在管理模式下）：
    - 触发方式：点击资料卡片上的删除按钮
    - 行为：显示确认提示，确认后删除资料
- **样式特点**：
  - 网格布局：
    - 桌面端：2列
    - 移动端：1列
  - 白色背景卡片
  - 圆角边框
  - 轻微阴影效果
  - 资料图片占据卡片上半部分
  - 资料信息占据卡片下半部分
  - 管理模式下卡片有虚线边框

##### 4.4 留言互动区
- **位置**：相关资料区下方
- **内容**：
  - 标题：留言互动
  - 留言列表：
    - 用户名
    - 留言时间
    - 留言内容
  - 留言输入框：
    - 文本区域
    - 发表留言按钮
- **字段来源**：person.comments数组
- **功能说明**：
  - 查看留言：
    - 所有用户可见
    - 按时间倒序排列
  - 发表留言：
    - 触发方式：输入留言内容，点击"发表留言"按钮
    - 行为：显示提交成功提示，留言提交后等待审核
    - 可操作对象：已登录用户
  - 留言审核：
    - 管理员在后台审核留言
    - 审核通过后留言显示在页面上
    - 审核结果通过系统通知推送给留言发布者
    - 通知内容包含留言已通过审核的信息
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 轻微阴影效果
  - 留言项之间有分隔线
  - 留言输入框在底部

### 技术实现
- **页面组件**：app/personal/[slug]/page.tsx
- **主要子组件**：
  - Navbar：components/navbar.tsx
  - AIAssistant：components/ai-assistant.tsx
  - PersonalProfile：components/personal-profile.tsx
  - PDFViewer：components/pdf-viewer.tsx（动态导入）
- **样式实现**：
  - 使用Tailwind CSS进行样式设计
  - 背景色：浅米色 (#fdf9f1)
  - 主题色：绿色 (#1e7a43)、橙色 (#f5a623)
  - 响应式设计，适配不同屏幕尺寸
- **状态管理**：
  - 使用React的useState和useEffect钩子管理组件状态
  - 人物数据从URL参数获取并映射到预定义的数据对象

### 数据流向
- **页面加载**：
  1. 从URL参数获取slug
  2. 根据slug从personDataMap中获取对应的人物数据
  3. 渲染Navbar、AIAssistant和PersonalProfile组件
  4. AIAssistant组件初始化对话历史
  5. PersonalProfile组件显示人物资料、时间线、相关资料和留言
- **AI助手交互**：
  1. 用户输入问题
  2. 将用户问题添加到对话历史
  3. 模拟AI回复（实际应用中应调用API）
  4. 更新对话历史显示
- **资料管理**（仅对管理员显示）：
  1. 管理员点击"管理资料"按钮
  2. 切换到管理模式
  3. 管理员可以添加、编辑或删除资料
  4. 所有操作在前端状态中更新，实际应用中应调用API与后端交互

### 特殊说明
1. **数据持久化**：
   - 当前实现中，人物数据存储在前端代码中的personDataMap对象中
   - 资料管理和留言功能只在前端状态中模拟，页面刷新后会重置
   - 实际应用中，所有数据操作应通过API与后端数据库交互
2. **PDF文件处理**：
   - 个人专题页面中的PDF文件只能下载，不提供在线预览功能
   - 只有已登录用户可以下载PDF文件
   - 未登录用户点击下载按钮会显示提示信息，要求登录后才能下载
   - 下载功能通过创建临时链接元素实现直接下载
   - 实际应用中，PDF文件应存储在服务器或云存储中，并通过安全链接提供下载

3. **AI助手功能**：
   - 通过Dify API实现智能问答功能
   - 配置信息在系统设置-AI管理中进行编辑
   - 需要配置的字段包括：
     - API基础URL（如https://ai.glab.vip）
     - API路径（/api/chat-messages）
     - Bearer Token（授权令牌）
     - app_id（应用ID）
     - app_code（应用代码）
   - 只有登录用户才能使用AI助手功能
   - 未登录用户点击发送按钮会显示登录弹窗
4. **权限控制**：
   - 资料管理功能仅对有相应权限的用户显示
   - 留言功能需要用户登录
   - AI助手功能需要用户登录
   - 权限控制通过后端权限系统实现
5. **响应式设计**：
   - 桌面端：左右分栏布局，左侧AI助手固定，右侧个人资料可滚动
   - 移动端：上下布局，AI助手在上，个人资料在下

## 知识库页面 (/knowledge)

### 页面概述
知识库页面是系统的知识管理中心，提供知识库和文件的浏览、搜索、创建、上传和管理功能。页面分为知识库列表和文件列表两个主要标签页，支持系统知识库和用户知识库的区分管理，以及文件的上传和审核流程。

### 访问权限
- **可访问用户**：仅登录用户
- **特殊权限**：
  - 创建用户知识库：所有登录用户
  - 创建系统知识库：仅管理员
  - 上传文件到系统知识库：所有登录用户（需要审核）
  - 上传文件到用户知识库：仅知识库创建者（无需审核）
  - 文件审核：仅管理员

### 页面内容

#### 1. 导航栏 (Navbar)
- **位置**：页面顶部固定
- **内容**：与首页相同的导航栏组件
- **功能说明**：与首页相同

#### 2. 页面标题和操作按钮
- **位置**：导航栏下方
- **内容**：
  - 标题：知识库管理
  - 副标题：管理和组织您的知识资源，提高信息检索效率
  - 操作按钮：
    - 创建知识库按钮
    - 文件审核按钮（仅管理员可见）
    - 上传文件按钮
- **字段来源**：组件内硬编码
- **功能说明**：
  - 创建知识库按钮：
    - 触发方式：点击按钮
    - 行为：打开创建知识库弹窗
    - 可点击对象：所有登录用户
  - 文件审核按钮：
    - 触发方式：点击按钮
    - 行为：跳转到文件审核页面 (/knowledge/review)
    - 可点击对象：仅管理员
    - 显示待审核文件数量（如有）
  - 上传文件按钮：
    - 触发方式：点击按钮
    - 行为：打开上传文件弹窗
    - 可点击对象：所有登录用户
- **样式特点**：
  - 创建知识库按钮：绿色背景 (#1e7a43)
  - 文件审核按钮：带有红色徽章显示待审核数量
  - 上传文件按钮：橙色背景 (#f5a623)

#### 3. 标签页切换
- **位置**：页面标题下方
- **内容**：
  - 知识库列表标签
  - 文件列表标签
- **字段来源**：组件内硬编码
- **功能说明**：
  - 切换标签：
    - 触发方式：点击标签
    - 行为：切换显示内容
    - 可点击对象：所有用户
- **样式特点**：
  - 白色背景卡片
  - 活动标签底部有绿色边框
  - 标签图标：知识库图标和文件图标

#### 4. 知识库列表标签页
- **位置**：标签页内容区域
- **内容**：
  - 搜索和筛选区域：
    - 搜索框
    - 创建者筛选下拉框
    - 知识库类型筛选下拉框
    - 创建时间筛选下拉框
  - 系统知识库区域：
    - 标题：系统知识库
    - 知识库卡片列表
  - 用户知识库区域：
    - 标题：用户知识库
    - 知识库卡片列表
- **字段来源**：
  - 系统知识库：systemKnowledgeBases数组
  - 用户知识库：userKnowledgeBases数组
- **功能说明**：
  - 搜索知识库：
    - 触发方式：在搜索框输入关键词
    - 行为：根据知识库名称和描述筛选知识库
    - 可操作对象：所有用户
  - 筛选知识库：
    - 触发方式：选择筛选条件
    - 行为：根据创建者、类型和时间筛选知识库
    - 可操作对象：所有用户
  - 查看知识库：
    - 触发方式：点击知识库卡片或卡片上的"查看"选项
    - 行为：跳转到知识库详情页面 (/knowledge/view/[id])
    - 可点击对象：所有用户（系统知识库）或有权限的用户（用户知识库）
  - 知识库设置：
    - 触发方式：点击知识库卡片上的"设置"选项
    - 行为：跳转到知识库设置页面 (/knowledge/settings/[id])
    - 可点击对象：知识库创建者或管理员
  - 删除知识库：
    - 触发方式：点击知识库卡片上的"删除"选项
    - 行为：显示删除确认弹窗，确认后删除知识库
    - 可点击对象：知识库创建者或管理员
- **样式特点**：
  - 知识库卡片：
    - 白色背景
    - 圆角边框
    - 轻微阴影效果
    - 悬停效果：边框变色和阴影加深
    - 系统知识库：蓝色标签
    - 用户知识库：绿色标签
  - 知识库信息：
    - 知识库名称
    - 知识库描述
    - 创建者
    - 文档数量（带进度条）
    - 存储大小（带进度条）
    - 创建时间

#### 5. 文件列表标签页
- **位置**：标签页内容区域
- **内容**：
  - 搜索和筛选区域：
    - 搜索框
    - 文件类型筛选下拉框
    - 知识库筛选下拉框
    - 知识库类型筛选下拉框
    - 文件大小筛选下拉框
  - 文件列表：
    - 文件卡片或表格
- **字段来源**：files数组（根据用户权限过滤）
- **功能说明**：
  - 文件显示范围：
    - 管理员：所有知识库的所有文件
    - 普通用户：自己创建的知识库的所有文件、系统知识库的所有文件以及已被授权访问的知识库的所有文件
  - 搜索文件：
    - 触发方式：在搜索框输入关键词
    - 行为：根据文件名筛选文件
    - 可操作对象：所有登录用户
  - 筛选文件：
    - 触发方式：选择筛选条件
    - 行为：根据文件类型、知识库、知识库类型和文件大小筛选文件
    - 可操作对象：所有登录用户
  - 下载文件：
    - 触发方式：点击文件卡片上的下载按钮
    - 行为：下载文件
    - 可点击对象：有权限的用户
  - 查看文件：
    - 触发方式：点击文件卡片
    - 行为：预览文件内容
    - 可点击对象：有权限的用户
- **样式特点**：
  - 文件卡片：
    - 白色背景
    - 圆角边框
    - 轻微阴影效果
    - 文件类型图标
    - 文件名称
    - 文件大小
    - 上传者和上传时间
    - 所属知识库
    - 下载按钮

#### 6. 创建知识库弹窗
- **位置**：页面中央弹出
- **内容**：
  - 标题：创建新知识库
  - 表单字段：
    - 知识库名称（必填）
    - 知识库描述
    - 知识库类型（系统/用户，仅管理员可选）
    - 联系人
    - 联系电话
  - 操作按钮：
    - 取消
    - 创建
- **字段来源**：组件内状态管理
- **功能说明**：
  - 创建知识库：
    - 触发方式：填写表单，点击"创建"按钮
    - 行为：创建新知识库，关闭弹窗，刷新知识库列表
    - 可操作对象：所有登录用户（创建用户知识库）或管理员（创建系统知识库）
  - 取消创建：
    - 触发方式：点击"取消"按钮或弹窗外区域
    - 行为：关闭弹窗，不创建知识库
    - 可操作对象：所有用户
- **样式特点**：
  - 白色背景
  - 圆角边框
  - 表单字段垂直排列
  - 创建按钮：绿色背景 (#1e7a43)
  - 弹窗外区域半透明黑色遮罩

#### 7. 上传文件弹窗
- **位置**：页面中央弹出
- **内容**：
  - 标题：上传文件
  - 选择上传目标：
    - 上传到系统知识库（单选）
    - 上传到个人知识库（单选）
    - 创建新知识库（单选）
  - 知识库选择下拉框（根据选择显示）
  - 新知识库表单（如选择创建新知识库）
  - 文件上传区域：
    - 拖拽区域
    - 选择文件按钮
    - 已选文件列表
  - 操作按钮：
    - 取消
    - 上传
- **字段来源**：组件内状态管理
- **功能说明**：
  - 选择上传目标：
    - 触发方式：选择单选按钮
    - 行为：显示对应的表单字段
    - 可操作对象：所有登录用户
  - 选择文件：
    - 触发方式：点击选择文件按钮或拖拽文件到上传区域
    - 行为：添加文件到上传列表
    - 可操作对象：所有登录用户
  - 上传文件：
    - 触发方式：点击"上传"按钮
    - 行为：
      - 上传到系统知识库：
        1. 调用Dify的upload接口上传文件
        2. 调用Dify的run接口进行文件分析
        3. Dify通过API回传分析结果（概要和详细信息）
        4. 系统存储文件和分析结果到数据库
        5. 文件进入审核流程
      - 上传到个人知识库：
        1. 调用Dify的upload接口上传文件
        2. 调用Dify的run接口进行文件分析
        3. Dify通过API回传分析结果（概要和详细信息）
        4. 系统存储文件和分析结果到数据库
        5. 文件直接可见（无需审核）
      - 创建新知识库：先创建知识库，再执行上述上传流程
    - 可操作对象：
      - 上传到系统知识库：所有登录用户
      - 上传到个人知识库：仅知识库创建者
  - 取消上传：
    - 触发方式：点击"取消"按钮或弹窗外区域
    - 行为：关闭弹窗，不上传文件
    - 可操作对象：所有用户
- **样式特点**：
  - 白色背景
  - 圆角边框
  - 拖拽区域：虚线边框，拖拽时变色
  - 文件列表：显示文件名、大小和类型
  - 上传按钮：橙色背景 (#f5a623)
  - 弹窗外区域半透明黑色遮罩

### 技术实现
- **页面组件**：app/knowledge/page.tsx
- **主要子组件**：
  - Navbar：components/navbar.tsx
  - KnowledgeBaseCard：内部组件
  - FileCard：内部组件
- **样式实现**：
  - 使用Tailwind CSS进行样式设计
  - 背景色：浅灰色 (#f9fafb)
  - 主题色：绿色 (#1e7a43)、橙色 (#f5a623)
  - 响应式设计，适配不同屏幕尺寸
- **状态管理**：
  - 使用React的useState和useMemo钩子管理组件状态
  - 使用过滤函数处理搜索和筛选逻辑

### 数据流向
- **页面加载**：
  1. 加载app/knowledge/page.tsx组件
  2. 初始化知识库和文件数据（模拟数据）
  3. 渲染知识库列表或文件列表（根据活动标签）
- **搜索和筛选**：
  1. 用户输入搜索关键词或选择筛选条件
  2. 使用useMemo钩子重新计算过滤后的数据
  3. 更新显示的知识库或文件列表
- **创建知识库**：
  1. 用户点击"创建知识库"按钮
  2. 打开创建知识库弹窗
  3. 用户填写表单并提交
  4. 创建新知识库对象并添加到知识库列表
  5. 关闭弹窗，刷新知识库列表
- **上传文件**：
  1. 用户点击"上传文件"按钮
  2. 打开上传文件弹窗
  3. 用户选择上传目标和文件
  4. 上传文件到选定的知识库
  5. 关闭弹窗，刷新文件列表

### 特殊说明
1. **知识库类型**：
   - 系统知识库：由管理员创建和管理，所有用户可访问
   - 用户知识库：由普通用户创建，仅创建者和被授权用户可访问
2. **文件审核流程**：
   - 上传到系统知识库的文件需要管理员审核
   - 审核通过后文件才会显示在系统知识库中
   - 上传到用户知识库的文件无需审核，直接可见
3. **权限控制**：
   - 页面访问：仅登录用户可访问
   - 创建系统知识库：仅管理员
   - 创建用户知识库：所有登录用户
   - 编辑/删除知识库：知识库创建者或管理员
   - 访问用户知识库：知识库创建者或被授权用户
   - 上传文件到用户知识库：仅知识库创建者
4. **文件处理流程**：
   - 文件上传使用Dify API：
     - 上传接口：/api/files/upload
     - 分析接口：/api/workflows/run
     - 需要的请求头：
       - Authorization: Bearer token
       - app_id: 应用ID
       - app_code: 应用代码
       - end_user_id: 用户ID
   - 文件分析结果：
     - Dify通过API回传分析结果
     - 系统需提供接口接收分析结果
     - 存储文件和分析结果（概要和详细信息）到系统数据库
5. **响应式设计**：
   - 桌面端：知识库卡片3列布局
   - 平板端：知识库卡片2列布局
   - 移动端：知识库卡片1列布局

## 知识库审核页面 (/knowledge/review)

### 页面概述
知识库审核页面是管理员专用的页面，用于审核用户上传到系统知识库的文件。页面提供了待审核、已批准和已拒绝三个标签页，管理员可以查看文件详情，批准或拒绝文件上传，并提供审核意见。

### 访问权限
- **可访问用户**：仅管理员
- **特殊权限**：文件审核权限

### 页面内容

#### 1. 导航栏 (Navbar)
- **位置**：页面顶部固定
- **内容**：与首页相同的导航栏组件
- **功能说明**：与首页相同

#### 2. 页面标题和返回按钮
- **位置**：导航栏下方
- **内容**：
  - 标题：知识库文件审核
  - 副标题：管理上传到系统知识库的文件，确保内容质量和安全
  - 返回按钮：返回知识库页面
- **字段来源**：组件内硬编码
- **功能说明**：
  - 返回按钮：
    - 触发方式：点击按钮
    - 行为：跳转回知识库页面 (/knowledge)
    - 可点击对象：管理员
- **样式特点**：
  - 标题：大号字体，深灰色
  - 副标题：小号字体，浅灰色
  - 返回按钮：轮廓按钮，灰色边框

#### 3. 成功消息提示
- **位置**：页面标题下方，仅在操作成功后显示
- **内容**：
  - 成功图标
  - 成功消息文本
- **字段来源**：操作结果
- **功能说明**：
  - 显示时机：审核操作成功后显示
  - 显示内容：操作结果描述（如"已批准文件：xxx.pdf"）
  - 显示时长：3秒后自动消失
- **样式特点**：
  - 绿色背景
  - 左侧绿色边框
  - 白色文本
  - 滑入动画效果

#### 4. 标签页切换
- **位置**：成功消息下方
- **内容**：
  - 待审核标签：显示待审核文件数量
  - 已批准标签
  - 已拒绝标签
- **字段来源**：组件内状态管理
- **功能说明**：
  - 切换标签：
    - 触发方式：点击标签
    - 行为：切换显示内容
    - 可点击对象：管理员
  - 显示待审核数量：
    - 在待审核标签上显示徽章，指示待审核文件数量
- **样式特点**：
  - 待审核标签：带有琥珀色图标和徽章
  - 已批准标签：带有绿色图标
  - 已拒绝标签：带有红色图标
  - 活动标签底部有对应颜色的边框

#### 5. 搜索和筛选区域
- **位置**：标签页下方
- **内容**：
  - 搜索框
  - 知识库筛选下拉框
  - 文件类型筛选下拉框
- **字段来源**：组件内状态管理
- **功能说明**：
  - 搜索文件：
    - 触发方式：在搜索框输入关键词
    - 行为：根据文件名筛选文件
    - 可操作对象：管理员
  - 筛选文件：
    - 触发方式：选择筛选条件
    - 行为：根据知识库和文件类型筛选文件
    - 可操作对象：管理员
- **样式特点**：
  - 搜索框：带有搜索图标
  - 筛选下拉框：带有筛选图标
  - 灰色背景
  - 圆角边框

#### 6. 待审核文件列表
- **位置**：搜索区域下方，待审核标签页内
- **内容**：
  - 表格列头：
    - 文件名
    - 所属知识库
    - 上传者
    - 上传时间
    - 文件大小
    - 操作
  - 文件行：
    - 文件图标和名称
    - 所属知识库名称
    - 上传者姓名
    - 上传时间
    - 文件大小
    - 批准和拒绝按钮
- **字段来源**：reviewFiles数组中状态为"pending"的文件
- **功能说明**：
  - 批准文件：
    - 触发方式：点击"批准"按钮
    - 行为：打开审核模态框，预选"批准"选项
    - 可点击对象：管理员
  - 拒绝文件：
    - 触发方式：点击"拒绝"按钮
    - 行为：打开审核模态框，预选"拒绝"选项
    - 可点击对象：管理员
- **样式特点**：
  - 表格布局
  - 交替行背景色
  - 批准按钮：绿色背景 (#1e7a43)
  - 拒绝按钮：红色边框和文本
  - 悬停效果：行背景变浅灰色

#### 7. 已批准文件列表
- **位置**：搜索区域下方，已批准标签页内
- **内容**：
  - 表格列头：
    - 文件名
    - 所属知识库
    - 上传者
    - 审核时间
    - 审核人
    - 操作
  - 文件行：
    - 文件图标和名称
    - 所属知识库名称
    - 上传者姓名
    - 审核时间
    - 审核人姓名
    - 查看按钮
- **字段来源**：reviewFiles数组中状态为"approved"的文件
- **功能说明**：
  - 查看文件：
    - 触发方式：点击"查看"按钮
    - 行为：打开文件详情模态框
    - 可点击对象：管理员
- **样式特点**：
  - 表格布局
  - 交替行背景色
  - 查看按钮：灰色背景
  - 悬停效果：行背景变浅灰色

#### 8. 已拒绝文件列表
- **位置**：搜索区域下方，已拒绝标签页内
- **内容**：
  - 表格列头：
    - 文件名
    - 所属知识库
    - 上传者
    - 审核时间
    - 审核人
    - 拒绝原因
    - 操作
  - 文件行：
    - 文件图标和名称
    - 所属知识库名称
    - 上传者姓名
    - 审核时间
    - 审核人姓名
    - 拒绝原因
    - 查看按钮
- **字段来源**：reviewFiles数组中状态为"rejected"的文件
- **功能说明**：
  - 查看文件：
    - 触发方式：点击"查看"按钮
    - 行为：打开文件详情模态框
    - 可点击对象：管理员
- **样式特点**：
  - 表格布局
  - 交替行背景色
  - 查看按钮：灰色背景
  - 悬停效果：行背景变浅灰色

#### 9. 审核模态框
- **位置**：页面中央弹出
- **内容**：
  - 标题：审核文件/文件详情
  - 文件信息：
    - 文件名
    - 文件类型
    - 文件大小
    - 所属知识库
    - 上传者
    - 上传时间
  - 审核意见输入框（仅在审核待审核文件时显示）
  - 操作按钮：
    - 取消
    - 批准（仅在审核待审核文件时显示）
    - 拒绝（仅在审核待审核文件时显示）
- **字段来源**：selectedFile对象
- **功能说明**：
  - 输入审核意见：
    - 触发方式：在输入框中输入文本
    - 行为：更新reviewComment状态
    - 可操作对象：管理员
    - 特殊规则：拒绝文件时必须填写审核意见
  - 批准文件：
    - 触发方式：点击"批准"按钮
    - 行为：调用handleSubmitReview("approve")函数
    - 结果：
      - 更新文件状态为"approved"
      - 记录审核人和审核时间
      - 显示成功消息
      - 向文件上传者发送通知消息（文件已批准）
      - 关闭模态框
    - 可操作对象：管理员
  - 拒绝文件：
    - 触发方式：点击"拒绝"按钮
    - 行为：调用handleSubmitReview("reject")函数
    - 结果：
      - 更新文件状态为"rejected"
      - 记录审核人、审核时间和拒绝原因
      - 显示成功消息
      - 向文件上传者发送通知消息（文件已拒绝，包含拒绝原因）
      - 关闭模态框
    - 可操作对象：管理员
  - 取消审核：
    - 触发方式：点击"取消"按钮或模态框外区域
    - 行为：关闭模态框，不执行审核操作
    - 可操作对象：管理员
- **样式特点**：
  - 白色背景
  - 圆角边框
  - 文件信息区域使用灰色背景
  - 批准按钮：绿色背景 (#1e7a43)
  - 拒绝按钮：红色边框和文本
  - 模态框外区域半透明黑色遮罩

### 技术实现
- **页面组件**：app/knowledge/review/page.tsx
- **主要子组件**：无独立子组件，所有组件内嵌在页面文件中
- **样式实现**：
  - 使用Tailwind CSS进行样式设计
  - 背景色：浅灰色 (#f9fafb)
  - 主题色：
    - 待审核：琥珀色 (#f59e0b)
    - 已批准：绿色 (#1e7a43)
    - 已拒绝：红色 (#ef4444)
  - 响应式设计，适配不同屏幕尺寸
- **状态管理**：
  - 使用React的useState钩子管理组件状态
  - 使用过滤函数处理搜索和筛选逻辑

### 数据流向
- **页面加载**：
  1. 加载app/knowledge/review/page.tsx组件
  2. 初始化审核文件数据（模拟数据）
  3. 渲染待审核、已批准或已拒绝文件列表（根据活动标签）
- **搜索和筛选**：
  1. 用户输入搜索关键词或选择筛选条件
  2. 调用filterFiles函数过滤文件列表
  3. 更新显示的文件列表
- **审核文件**：
  1. 管理员点击"批准"或"拒绝"按钮
  2. 打开审核模态框
  3. 管理员输入审核意见（拒绝时必填）
  4. 管理员点击确认按钮
  5. 调用handleSubmitReview函数更新文件状态
  6. 显示成功消息
  7. 关闭模态框，刷新文件列表

### 特殊说明
1. **访问控制**：
   - 此页面仅限管理员访问
   - 非管理员用户应被重定向到首页或显示权限不足提示
2. **审核流程**：
   - 所有上传到系统知识库的文件都需要审核
   - 审核通过的文件会显示在系统知识库中
   - 审核拒绝的文件不会显示在系统知识库中
   - 拒绝文件时必须提供拒绝原因
   - 审核结果（批准或拒绝）会通过系统通知推送给文件上传者
   - 拒绝通知中会包含拒绝原因
3. **数据持久化**：
   - 当前实现中，审核操作只在前端状态中模拟
   - 实际应用中，审核操作应通过API与后端数据库交互
4. **响应式设计**：
   - 桌面端：完整表格布局
   - 移动端：卡片式布局，每个文件显示为一个卡片

## 数据查询页面 (/data-mining)

### 页面概述
数据查询页面是系统的数据检索和分析中心，提供智能AI助手和高级搜索功能，帮助用户从多个知识库和数据源中查询、检索和分析相关资料。页面采用左右分栏布局，左侧为AI查询助手，右侧为搜索结果展示区域。

### 访问权限
- **可访问用户**：所有用户（包括未登录用户）
- **特殊权限**：
  - AI查询功能：仅登录用户
  - 数据查询功能：仅登录用户

### 页面内容

#### 1. 导航栏 (Navbar)
- **位置**：页面顶部固定
- **内容**：与首页相同的导航栏组件
- **功能说明**：与首页相同

#### 2. 页面布局
- **左侧区域**：
  - 宽度：桌面端固定40%宽度，移动端100%宽度
  - 内容：AI数据查询助手
  - 位置：桌面端固定在左侧，移动端位于搜索结果上方
- **右侧区域**：
  - 宽度：桌面端60%宽度并右移40%，移动端100%宽度
  - 内容：搜索结果展示区域
  - 位置：桌面端位于右侧，可滚动，移动端位于AI助手下方

#### 3. AI数据查询助手 (DataQueryAssistant)
- **位置**：页面左侧
- **内容**：
  - 标题栏：
    - AI图标和标题："AI 数据查询助手"
    - 状态指示器：显示"运行中"或"空闲"
  - 对话区域：显示与AI助手的对话历史
  - 输入框：用于输入问题
  - 发送按钮：发送问题给AI助手
- **字段来源**：
  - 对话历史：组件内状态管理
  - 材料数据库：组件内定义的materialDatabase数组
- **功能说明**：
  - 初始消息：
    - 内容：根据助手配置决定是否显示欢迎消息
    - 来源：从系统设置-AI管理中获取对应数据查询助手的配置
    - 显示规则：只有当助手配置中设置了初始消息时才显示
  - 发送消息：
    - 触发方式：点击发送按钮或按Enter键
    - 行为：
      - 未登录用户：显示登录弹窗，提示需要登录后才能使用
      - 已登录用户：
        - 将用户输入添加到对话历史
        - 调用Dify的AI助手API进行查询
        - API调用参数：
          - 请求路径：/api/workflows/run
          - Authorization: Bearer token（从系统设置中配置）
          - app_id: 应用ID（从系统设置中配置）
          - app_code: 应用代码（从系统设置中配置）
          - end_user_id: 当前登录用户ID
          - 当前选择的知识库ID（如有）
        - 返回查询结果并显示在对话历史中
    - 查询结果：
      - 找到相关资料：显示资料列表
      - 未找到相关资料：显示提示信息
  - 查看资料详情：
    - 触发方式：点击对话中的资料项
    - 行为：打开资料详情模态框
    - 模态框内容：
      - 资料标题
      - 资料来源和元数据（发布日期、上传时间、位置等）
      - 资料摘要
      - 详细内容（根据内容类型显示文本或图片）
      - 下载按钮
  - 下载资料：
    - 触发方式：点击资料详情中的下载按钮
    - 行为：下载对应的资料文件
- **样式特点**：
  - 浅灰色背景
  - 用户消息右对齐，浅绿色背景
  - AI助手消息左对齐，白色背景
  - 资料项使用卡片式布局
  - 固定高度，可滚动查看历史消息
  - 底部固定输入区域

#### 4. 搜索结果展示区域 (DataQueryContent)
- **位置**：页面右侧
- **内容**：
  - 搜索框：
    - 输入框
    - 搜索按钮
  - 搜索结果列表：
    - 结果项卡片
    - 分页控件
- **字段来源**：
  - 搜索结果：组件内定义的mockSearchResults数组
- **功能说明**：
  - 搜索功能：
    - 触发方式：在搜索框输入关键词，点击搜索按钮
    - 行为：
      - 未登录用户：显示登录弹窗，提示需要登录后才能使用
      - 已登录用户：
        - 根据当前选择的数据源和知识库进行查询
        - 系统知识库：查询对应知识库的文件
        - 外部数据源：显示"当前数据源仍在对接期间，暂不能使用"提示
    - 可操作对象：仅登录用户
  - 查看结果详情：
    - 触发方式：点击搜索结果项
    - 行为：打开结果详情模态框
    - 模态框内容：
      - 结果标题
      - 结果元数据（知识库、上传时间、上传者、文件大小、文件类型）
      - 概述
      - 详细描述
      - 下载按钮
  - 下载文件：
    - 触发方式：点击结果详情中的下载按钮
    - 行为：下载对应的文件
  - 分页浏览：
    - 触发方式：点击分页控件
    - 行为：切换显示不同页的搜索结果
- **样式特点**：
  - 白色背景
  - 搜索框使用绿色聚焦效果
  - 搜索按钮使用橙色背景
  - 结果项使用卡片式布局
  - 分页控件位于底部

### 技术实现
- **页面组件**：app/data-mining/page.tsx
- **主要子组件**：
  - Navbar：components/navbar.tsx
  - DataQueryAssistant：components/data-query-assistant.tsx
  - DataQueryContent：components/data-query-content.tsx
  - ModalProvider：components/modal-provider.tsx
- **样式实现**：
  - 使用Tailwind CSS进行样式设计
  - 背景色：白色
  - 主题色：绿色 (#1e7a43)、橙色 (#f5a623)
  - 响应式设计，适配不同屏幕尺寸
- **状态管理**：
  - 使用React的useState和useEffect钩子管理组件状态
  - 使用useRef钩子实现消息自动滚动
  - 使用useMemo钩子优化搜索结果过滤

### 数据流向
- **页面加载**：
  1. 加载app/data-mining/page.tsx组件
  2. 渲染Navbar、DataQueryAssistant和DataQueryContent组件
  3. DataQueryAssistant组件初始化对话历史
  4. DataQueryContent组件初始化搜索结果
- **AI助手交互**：
  1. 用户输入问题
  2. 将用户问题添加到对话历史
  3. 根据问题从材料数据库中查找相关资料
  4. 返回查询结果并显示在对话历史中
  5. 自动滚动到最新消息
- **搜索功能**：
  1. 用户在搜索框输入关键词
  2. 点击搜索按钮
  3. 根据关键词过滤搜索结果
  4. 更新显示的搜索结果列表

### 特殊说明
1. **数据源**：
   - 系统知识库：系统内部的知识库
   - 外部数据源：外部机构的数据库
2. **AI助手功能**：
   - 智能检索：根据用户问题智能检索相关资料
   - 资料推荐：根据用户历史查询推荐相关资料
   - 跨库检索：可以同时从多个知识库和数据源中检索资料
   - 集成Dify：通过Dify的API实现智能问答功能
   - 数据源选择：用户可以选择不同的知识库作为数据源
   - 权限控制：仅登录用户可以使用AI助手功能
3. **搜索功能**：
   - 关键词搜索：根据标题和描述进行全文搜索
   - 筛选功能：可以根据数据源、知识库类型等条件筛选结果
   - 排序功能：可以根据相关度、上传时间等条件排序结果
   - 数据源限制：外部数据源暂不可用，仅系统知识库可查询
   - 权限控制：仅登录用户可以使用搜索功能
4. **响应式设计**：
   - 桌面端：左右分栏布局，左侧AI助手固定，右侧搜索结果可滚动
   - 移动端：上下布局，AI助手在上，搜索结果在下

## 个人中心页面 (/personal-center)

### 页面概述
个人中心页面是用户管理个人资料和账号设置的中心，提供个人信息编辑、密码修改、手机和邮箱绑定等功能。页面采用左右分栏布局，左侧为用户基本信息和导航菜单，右侧为具体的功能区域。

### 访问权限
- **可访问用户**：仅登录用户
- **特殊权限**：无需特殊权限

### 页面内容

#### 1. 导航栏 (Navbar)
- **位置**：页面顶部固定
- **内容**：与首页相同的导航栏组件
- **功能说明**：与首页相同

#### 2. 页面布局
- **左侧区域**：
  - 宽度：桌面端25%宽度，移动端100%宽度
  - 内容：用户头像、用户名、角色、加入时间
  - 位置：桌面端固定在左侧，移动端位于功能区域上方
- **右侧区域**：
  - 宽度：桌面端75%宽度，移动端100%宽度
  - 内容：个人资料编辑区域
  - 位置：桌面端位于右侧，移动端位于左侧区域下方

#### 3. 个人资料编辑区域
- **位置**：页面右侧
- **内容**：
  - 标题：个人资料
  - 编辑按钮：切换编辑模式
  - 个人信息表单：
    - 用户名
    - 手机号（可编辑）
    - 邮箱（可编辑）
  - 保存按钮：保存编辑后的个人信息
- **字段来源**：组件内状态管理的userData对象
- **功能说明**：
  - 编辑个人资料：
    - 触发方式：点击"编辑资料"按钮
    - 行为：切换到编辑模式，表单字段变为可编辑状态
    - 可操作对象：登录用户
  - 保存个人资料：
    - 触发方式：点击"保存"按钮
    - 行为：保存修改后的个人信息，切换回查看模式
    - 可操作对象：登录用户
  - 取消编辑：
    - 触发方式：点击"取消"按钮
    - 行为：放弃修改，恢复原始数据，切换回查看模式
    - 可操作对象：登录用户
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 轻微阴影效果
  - 编辑按钮：橙色背景 (#f5a623)
  - 保存按钮：橙色背景 (#f5a623)
  - 取消按钮：红色边框和文本

### 技术实现
- **页面组件**：app/personal-center/page.tsx
- **主要子组件**：
  - Navbar：components/navbar.tsx
  - Card：components/ui/card.tsx
  - Button：components/ui/button.tsx
  - Dialog：components/ui/dialog.tsx
- **样式实现**：
  - 使用Tailwind CSS进行样式设计
  - 背景色：浅米色 (#fdf9f1)
  - 主题色：绿色 (#1e7a43)、橙色 (#f5a623)
  - 响应式设计，适配不同屏幕尺寸
- **状态管理**：
  - 使用React的useState钩子管理组件状态
  - 使用useEffect钩子处理副作用

### 数据流向
- **页面加载**：
  1. 初始化用户数据（从localStorage或API获取）
  2. 渲染个人资料表单，默认为查看模式
- **编辑个人资料**：
  1. 用户点击"编辑资料"按钮
  2. 切换isEditing状态为true
  3. 表单字段变为可编辑状态
  4. 用户修改个人信息
  5. 用户点击"保存"按钮
  6. 调用handleSaveProfile函数保存修改
  7. 切换isEditing状态为false
  8. 显示成功提示

### 特殊说明
1. **数据持久化**：
   - 当前实现中，用户数据存储在前端状态和localStorage中
   - 实际应用中，应通过API与后端数据库交互
2. **头像上传**：
   - 支持用户上传自定义头像
   - 点击头像或"更换头像"按钮打开头像上传对话框
   - 支持预览和裁剪功能
3. **响应式设计**：
   - 桌面端：左右分栏布局
   - 移动端：上下布局

## 账号设置页面 (/personal-center/settings)

### 页面概述
账号设置页面提供用户管理账号安全和通知设置的功能，包括密码修改、手机绑定、邮箱绑定和通知偏好设置。页面采用左右分栏布局，左侧为导航菜单，右侧为具体的设置区域。

### 访问权限
- **可访问用户**：仅登录用户
- **特殊权限**：无需特殊权限

### 页面内容

#### 1. 导航栏 (Navbar)
- **位置**：页面顶部固定
- **内容**：与首页相同的导航栏组件
- **功能说明**：与首页相同

#### 2. 页面布局
- **左侧区域**：
  - 宽度：桌面端25%宽度，移动端100%宽度
  - 内容：用户头像、用户名、角色、导航菜单
  - 位置：桌面端固定在左侧，移动端位于设置区域上方
- **右侧区域**：
  - 宽度：桌面端75%宽度，移动端100%宽度
  - 内容：账号设置区域
  - 位置：桌面端位于右侧，移动端位于左侧区域下方

#### 3. 安全设置区域
- **位置**：设置区域顶部
- **内容**：
  - 标题：安全设置
  - 登录密码设置：
    - 描述：定期更改密码可以提高账号安全性
    - 修改按钮：打开密码修改对话框
  - 手机绑定设置：
    - 描述：显示当前绑定的手机号
    - 修改按钮：打开手机绑定修改对话框
  - 邮箱绑定设置：
    - 描述：显示当前绑定的邮箱
    - 修改按钮：打开邮箱绑定修改对话框
- **字段来源**：组件内状态管理的userData对象
- **功能说明**：
  - 修改密码：
    - 触发方式：点击"修改"按钮
    - 行为：打开密码修改对话框
    - 对话框内容：
      - 当前密码输入框
      - 新密码输入框
      - 确认新密码输入框
      - 取消按钮
      - 确认按钮
  - 修改手机绑定：
    - 触发方式：点击"修改"按钮
    - 行为：打开手机绑定修改对话框
    - 对话框内容：
      - 新手机号输入框
      - 取消按钮
      - 确认按钮
  - 修改邮箱绑定：
    - 触发方式：点击"修改"按钮
    - 行为：打开邮箱绑定修改对话框
    - 对话框内容：
      - 新邮箱输入框
      - 取消按钮
      - 确认按钮
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 轻微阴影效果
  - 修改按钮：绿色边框和文本

#### 4. 通知设置区域
- **位置**：安全设置区域下方
- **内容**：
  - 标题：通知设置
  - 通知类型开关：
    - 邮件通知：通过邮件接收通知
    - 推送通知：通过浏览器推送接收通知
    - 系统通知：接收系统相关通知
    - 活动通知：接收活动相关通知
  - 保存按钮：保存通知设置
- **字段来源**：组件内状态管理的notifications对象
- **功能说明**：
  - 切换通知设置：
    - 触发方式：点击开关
    - 行为：切换对应通知类型的状态
    - 可操作对象：登录用户
  - 保存通知设置：
    - 触发方式：点击"保存设置"按钮
    - 行为：保存修改后的通知设置
    - 可操作对象：登录用户
  - 用户可接收的通知类型：
    - 系统通知：系统更新、维护等信息
    - 活动通知：新活动发布、活动提醒等
    - 文件审核通知：文件审核结果（成功或失败，附带原因）
    - 评论审核通知：评论审核通过的通知
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 轻微阴影效果
  - 开关组件：绿色激活状态
  - 保存按钮：橙色背景 (#f5a623)

### 技术实现
- **页面组件**：app/personal-center/settings/page.tsx
- **主要子组件**：
  - Navbar：components/navbar.tsx
  - Card：components/ui/card.tsx
  - Button：components/ui/button.tsx
  - Dialog：components/ui/dialog.tsx
  - Switch：components/ui/switch.tsx
- **样式实现**：
  - 使用Tailwind CSS进行样式设计
  - 背景色：浅米色 (#fdf9f1)
  - 主题色：绿色 (#1e7a43)、橙色 (#f5a623)
  - 响应式设计，适配不同屏幕尺寸
- **状态管理**：
  - 使用React的useState钩子管理组件状态
  - 使用useEffect钩子处理副作用

### 数据流向
- **页面加载**：
  1. 初始化用户数据和通知设置（从localStorage或API获取）
  2. 渲染安全设置和通知设置区域
- **修改密码**：
  1. 用户点击"修改"按钮
  2. 打开密码修改对话框
  3. 用户输入当前密码、新密码和确认新密码
  4. 用户点击"确认"按钮
  5. 调用handlePasswordSubmit函数验证和保存新密码
  6. 显示成功提示，关闭对话框
- **修改手机绑定**：
  1. 用户点击"修改"按钮
  2. 打开手机绑定修改对话框
  3. 用户输入新手机号
  4. 用户点击"确认"按钮
  5. 调用handlePhoneSubmit函数保存新手机号
  6. 显示成功提示，关闭对话框
- **修改邮箱绑定**：
  1. 用户点击"修改"按钮
  2. 打开邮箱绑定修改对话框
  3. 用户输入新邮箱
  4. 用户点击"确认"按钮
  5. 调用handleEmailSubmit函数保存新邮箱
  6. 显示成功提示，关闭对话框
- **修改通知设置**：
  1. 用户切换通知类型开关
  2. 更新notifications状态
  3. 用户点击"保存设置"按钮
  4. 调用handleNotificationSave函数保存通知设置
  5. 显示成功提示

### 特殊说明
1. **数据持久化**：
   - 当前实现中，用户数据和通知设置存储在前端状态和localStorage中
   - 实际应用中，应通过API与后端数据库交互
2. **安全考虑**：
   - 密码修改需要验证当前密码
   - 密码规则遵循系统配置中的设置（长度、复杂度等）
3. **通知类型**：
   - 用户可以接收的通知包括：
     - 系统通知：系统更新、维护等信息
     - 活动通知：新活动发布、活动提醒等
     - 文件审核通知：文件审核结果（成功或失败，附带原因）
     - 评论审核通知：评论审核通过的通知
4. **响应式设计**：
   - 桌面端：左右分栏布局
   - 移动端：上下布局

## AI研究助手页面 (/ai-assistant)

### 页面概述
AI研究助手页面提供多种专业的智能助手，帮助用户进行家族历史研究、文档分析、数据挖掘等工作。用户可以选择适合自己研究需求的AI助手，与其进行对话交互，获取专业的研究支持和知识服务。

### 访问权限
- **可访问用户**：所有用户（包括未登录用户）
- **特殊权限**：
  - 使用AI助手功能：仅登录用户

### 页面内容

#### 1. 导航栏 (Navbar)
- **位置**：页面顶部固定
- **内容**：与首页相同的导航栏组件
- **功能说明**：与首页相同

#### 2. 页面标题区域
- **位置**：导航栏下方
- **内容**：
  - 标题：AI研究助手
  - 副标题：选择适合您研究需求的智能助手，获取专业的研究支持和知识服务
- **字段来源**：组件内硬编码
- **功能说明**：
  - 纯展示内容，无交互功能
- **样式特点**：
  - 标题：大号字体，深色
  - 副标题：中等字体，灰色

#### 3. 搜索区域
- **位置**：页面标题下方
- **内容**：
  - 搜索框：带有搜索图标
- **字段来源**：组件内状态管理
- **功能说明**：
  - 搜索助手：
    - 触发方式：在搜索框输入关键词
    - 行为：根据助手名称和描述筛选助手列表
    - 可操作对象：所有用户
- **样式特点**：
  - 搜索框：白色背景，圆角边框
  - 搜索图标：灰色，位于输入框左侧
  - 聚焦效果：绿色边框和轮廓

#### 4. 助手卡片网格
- **位置**：搜索区域下方
- **内容**：
  - 助手卡片列表，每个卡片包含：
    - 助手图标：根据助手类型显示不同颜色和图标
    - 助手名称
    - 助手描述
    - 标签（如"新"或"热门"，如适用）
- **字段来源**：组件内定义的assistants数组
- **功能说明**：
  - 查看助手：
    - 触发方式：点击助手卡片
    - 行为：
      - 未登录用户：显示登录弹窗，提示需要登录后才能使用
      - 已登录用户：打开助手对话模态框
    - 可点击对象：所有用户
- **样式特点**：
  - 网格布局：
    - 桌面端（xl）：4列
    - 大屏（lg）：3列
    - 平板（md）：2列
    - 手机（小于md）：1列
  - 白色背景卡片
  - 圆角边框
  - 轻微阴影效果
  - 悬停效果：阴影加深

#### 5. 助手对话模态框
- **位置**：页面中央弹出
- **内容**：
  - 模态框头部：
    - 助手图标
    - 助手名称
    - 助手描述
    - 关闭按钮
  - 对话区域：
    - 助手消息：左对齐，灰色背景
    - 用户消息：右对齐，绿色背景
  - 输入区域：
    - 文本输入框
    - 发送按钮
- **字段来源**：
  - 助手信息：从选中的助手对象(selectedAssistant)获取
  - 对话历史：组件内状态管理(chatMessages)
- **功能说明**：
  - 发送消息：
    - 触发方式：点击发送按钮或按Enter键
    - 行为：
      - 将用户输入添加到对话历史
      - 根据助手类型生成相应的回复
      - 将回复添加到对话历史
    - 实际应用中：应调用后端AI服务API获取回复
  - 关闭对话：
    - 触发方式：点击关闭按钮或模态框外区域
    - 行为：关闭模态框，清空对话历史
- **样式特点**：
  - 白色背景
  - 圆角边框
  - 固定最大宽度和高度
  - 对话区域可滚动
  - 模态框外区域半透明黑色遮罩

### 技术实现
- **页面组件**：app/ai-assistant/page.tsx
- **主要子组件**：无独立子组件，所有组件内嵌在页面文件中
- **样式实现**：
  - 使用Tailwind CSS进行样式设计
  - 背景色：浅灰色 (#f9fafb)
  - 主题色：
    - 绿色 (#1e7a43)：用于聚焦效果
    - 橙色 (#f5a623)：用于发送按钮
  - 响应式设计，适配不同屏幕尺寸
- **状态管理**：
  - 使用React的useState钩子管理组件状态
  - 使用useEffect钩子实现对话自动滚动

### 数据流向
- **页面加载**：
  1. 加载app/ai-assistant/page.tsx组件
  2. 初始化助手数据（模拟数据）
  3. 渲染助手卡片网格
- **搜索助手**：
  1. 用户在搜索框输入关键词
  2. 使用filter函数过滤助手列表
  3. 更新显示的助手卡片
- **使用助手**：
  1. 用户点击助手卡片
  2. 打开助手对话模态框
  3. 初始化空对话历史
  4. 用户输入问题，点击发送
  5. 将用户问题添加到对话历史
  6. 生成助手回复，添加到对话历史
  7. 自动滚动到最新消息

### 特殊说明
1. **助手类型**：
   - 研究助手：帮助用户进行家族历史研究
   - 文档助手：协助用户分析和整理历史文档
   - 数据挖掘助手：从大量历史数据中发现模式和关联
   - 历史人物助手：专注于革命先辈生平研究
   - 时间线助手：帮助用户构建历史事件时间线
   - 关系网络助手：分析历史人物之间的关系网络
   - 文献对比助手：对比分析不同历史文献
   - 数据可视化助手：将历史数据转化为直观的图表
2. **AI助手功能**：
   - 智能问答：根据用户问题提供专业回答
   - 研究指导：提供研究方法和思路建议
   - 资料推荐：推荐相关研究资料和文献
   - 实际应用中：应连接到后端AI服务API获取真实回复
3. **权限控制**：
   - 页面可以被所有用户访问
   - 但只有登录用户才能使用AI助手功能
   - 未登录用户点击助手卡片会显示登录提示
4. **响应式设计**：
   - 桌面端：多列网格布局
   - 移动端：单列网格布局

## 系统管理页面 (/system-management)

### 页面概述
系统管理页面是管理员和拥有系统管理权限的用户可访问的页面，提供用户管理、权限管理、留言管理、系统配置和AI管理等功能。页面采用标签页布局，用户可以在不同标签页之间切换，进行各种系统管理操作。

### 访问权限
- **可访问用户**：管理员和拥有系统管理权限的用户
- **特殊权限**：系统管理权限

### 页面内容

#### 1. 导航栏 (Navbar)
- **位置**：页面顶部固定
- **内容**：与首页相同的导航栏组件
- **功能说明**：与首页相同

#### 2. 页面标题
- **位置**：导航栏下方
- **内容**：
  - 标题：系统管理
- **字段来源**：组件内硬编码
- **功能说明**：
  - 纯展示内容，无交互功能
- **样式特点**：
  - 大号字体，深色
  - 粗体显示

#### 3. 标签页切换 (Tabs)
- **位置**：页面标题下方
- **内容**：
  - 用户管理标签
  - 权限管理标签
  - 留言管理标签
  - 系统配置标签
  - AI管理标签
- **字段来源**：组件内硬编码
- **功能说明**：
  - 切换标签：
    - 触发方式：点击标签
    - 行为：切换显示内容
    - 可点击对象：管理员和拥有系统管理权限的用户
- **样式特点**：
  - 白色背景
  - 活动标签底部有边框
  - 标签均分宽度

#### 4. 用户管理标签页
- **位置**：标签页内容区域
- **内容**：
  - 标题：用户管理
  - 添加用户按钮
  - 搜索和筛选区域：
    - 搜索框
    - 状态筛选下拉框
  - 用户列表表格：
    - 复选框列
    - 用户名列
    - 手机/邮箱列
    - 密码重置按钮
    - 角色列
    - 状态列
    - 创建时间列
    - 操作列（编辑、删除按钮）
  - 分页控件
- **字段来源**：
  - 用户数据：组件内定义的users数组
- **功能说明**：
  - 搜索用户：
    - 触发方式：在搜索框输入关键词
    - 行为：根据用户名、手机或邮箱筛选用户
    - 可操作对象：管理员和拥有系统管理权限的用户
  - 筛选用户：
    - 触发方式：选择状态筛选条件
    - 行为：根据用户状态筛选用户
    - 可操作对象：管理员和拥有系统管理权限的用户
  - 添加用户：
    - 触发方式：点击"添加用户"按钮
    - 行为：打开用户编辑模态框
    - 可操作对象：管理员和拥有系统管理权限的用户
  - 编辑用户：
    - 触发方式：点击用户行中的"编辑"按钮
    - 行为：打开用户编辑模态框，预填充用户数据
    - 可操作对象：管理员和拥有系统管理权限的用户
  - 删除用户：
    - 触发方式：点击用户行中的"删除"按钮
    - 行为：显示删除确认弹窗，确认后删除用户
    - 可操作对象：管理员和拥有系统管理权限的用户
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 表格布局
  - 交替行背景色
  - 添加用户按钮：橙色背景 (#f5a623)

#### 5. 权限管理标签页
- **位置**：标签页内容区域
- **内容**：
  - 标题：权限管理
  - 描述文本
  - 添加角色按钮
  - 角色列表表格：
    - 角色名称列
    - 角色描述列
    - 用户数量列
    - 创建时间列
    - 操作列（编辑、删除按钮）
- **字段来源**：
  - 角色数据：组件内定义的roles数组
- **功能说明**：
  - 添加角色：
    - 触发方式：点击"添加角色"按钮
    - 行为：打开角色编辑模态框
    - 可操作对象：管理员和拥有系统管理权限的用户
  - 编辑角色：
    - 触发方式：点击角色行中的"编辑"按钮
    - 行为：打开角色编辑模态框，预填充角色数据
    - 可操作对象：管理员
  - 删除角色：
    - 触发方式：点击角色行中的"删除"按钮
    - 行为：显示删除确认弹窗，确认后删除角色
    - 可操作对象：管理员
  - 角色权限设置：
    - 内容：在编辑模态框中显示所有页面及其功能按钮的权限设置
    - 行为：选中对应页面和功能按钮，赋予该角色相应权限
    - 效果：拥有该角色的用户可以访问被选中的页面和使用被选中的功能按钮
- **系统默认角色**：
  - 管理员角色：
    - 权限：拥有所有页面和所有功能按钮的权限
    - 特点：不可编辑
  - 初级访问者角色：
    - 权限：拥有所有页面的基础功能，如AI对话使用、数据查询、创建知识库、申请访问知识库、上传文件等
    - 特点：新注册用户默认分配此角色
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 表格布局
  - 交替行背景色
  - 添加角色按钮：橙色背景 (#f5a623)

#### 6. 留言管理标签页
- **位置**：标签页内容区域
- **内容**：
  - 标题：留言管理
  - 搜索和筛选区域：
    - 搜索框
    - 状态筛选下拉框
  - 留言列表表格：
    - 复选框列
    - 用户名列
    - 留言内容列
    - 留言时间列
    - 状态列
    - 操作列（审核、删除按钮）
  - 批量操作按钮：
    - 批量审核
    - 批量删除
  - 分页控件
- **字段来源**：
  - 留言数据：组件内定义的messages数组
- **功能说明**：
  - 搜索留言：
    - 触发方式：在搜索框输入关键词
    - 行为：根据用户名或留言内容筛选留言
    - 可操作对象：管理员
  - 筛选留言：
    - 触发方式：选择状态筛选条件
    - 行为：根据留言状态筛选留言
    - 可操作对象：管理员
  - 审核留言：
    - 触发方式：点击留言行中的"审核"按钮
    - 行为：打开留言审核模态框
    - 可操作对象：管理员
  - 删除留言：
    - 触发方式：点击留言行中的"删除"按钮
    - 行为：显示删除确认弹窗，确认后删除留言
    - 可操作对象：管理员
  - 批量操作：
    - 触发方式：选择多个留言，点击批量操作按钮
    - 行为：对选中的留言执行批量审核或删除操作
    - 可操作对象：管理员
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 表格布局
  - 交替行背景色
  - 批量操作按钮：橙色背景 (#f5a623)

#### 7. 系统配置标签页
- **位置**：标签页内容区域
- **内容**：
  - 标题：系统配置
  - 安全配置区域：
    - 密码最小长度输入框
    - 要求大写字母复选框
    - 要求小写字母复选框
    - 要求数字复选框
    - 要求特殊字符复选框
  - 保存按钮
- **字段来源**：
  - 配置数据：组件内状态管理
- **功能说明**：
  - 修改配置：
    - 触发方式：在输入框中修改值或切换复选框
    - 行为：更新对应的状态值
    - 可操作对象：管理员
  - 保存配置：
    - 触发方式：点击"保存配置"按钮
    - 行为：保存当前配置到系统
    - 可操作对象：管理员
  - 密码规则应用：
    - 应用场景：用户注册和修改密码时
    - 行为：根据设置的密码规则验证用户输入的密码
    - 效果：规范用户密码设置，提高系统安全性
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 分组布局
  - 保存按钮：橙色背景 (#f5a623)

#### 8. AI管理标签页
- **位置**：标签页内容区域
- **内容**：
  - 标题：AI代理管理
  - 添加AI研究助手按钮
  - 搜索和筛选区域：
    - 搜索框
    - 类型筛选下拉框
    - 状态筛选下拉框
  - AI代理卡片网格：
    - 代理名称
    - 代理类型标签（个人专题助手、数据查询助手、AI研究助手、知识库文件分析助手）
    - 代理描述
    - 代理标签
    - 代理状态
    - 最后更新时间
    - 操作按钮（查看、编辑、删除）
- **字段来源**：
  - AI代理数据：组件内定义的aiAgents数组
- **功能说明**：
  - 搜索AI代理：
    - 触发方式：在搜索框输入关键词
    - 行为：根据代理名称、描述或标签筛选AI代理
    - 可操作对象：管理员
  - 筛选AI代理：
    - 触发方式：选择类型或状态筛选条件
    - 行为：根据代理类型或状态筛选AI代理
    - 可操作对象：管理员
  - 添加AI研究助手：
    - 触发方式：点击"添加AI研究助手"按钮
    - 行为：打开AI代理编辑模态框
    - 可操作对象：管理员和拥有系统管理权限的用户
    - 特殊说明：只能添加AI研究助手类型的代理，其他类型（个人专题助手、数据查询助手、知识库文件分析助手）不可新增
  - 查看AI代理：
    - 触发方式：点击AI代理卡片中的"查看"按钮
    - 行为：打开AI代理查看模态框
    - 可操作对象：管理员
  - 编辑AI代理：
    - 触发方式：点击AI代理卡片中的"编辑"按钮
    - 行为：打开AI代理编辑模态框，预填充代理数据
    - 可操作对象：管理员
  - 删除AI代理：
    - 触发方式：点击AI代理卡片中的"删除"按钮（仅对AI研究助手类型显示）
    - 行为：显示删除确认弹窗，确认后删除AI代理
    - 可操作对象：管理员和拥有系统管理权限的用户
    - 特殊限制：
      - 个人专题助手：不可删除，只能编辑
      - 数据查询助手：不可删除，只能编辑
      - 知识库文件分析助手：不可删除，只能编辑
- **样式特点**：
  - 白色背景卡片
  - 圆角边框
  - 卡片网格布局
  - 添加AI研究助手按钮：橙色背景 (#f5a623)
  - 代理类型标签：不同类型使用不同颜色
    - 个人专题助手：蓝色 (#3b82f6)
    - 数据查询助手：绿色 (#10b981)
    - AI研究助手：紫色 (#8b5cf6)
    - 知识库文件分析助手：橙色 (#f59e0b)
  - 代理状态标签：不同状态使用不同颜色

#### 9. AI代理编辑模态框
- **位置**：页面中央弹出
- **内容**：
  - 标题：编辑AI代理/新增AI研究助手
  - 表单字段（根据代理类型显示不同字段）：
    - 通用字段（所有类型共有）：
      - 代理名称输入框
      - 代理类型选择框（个人专题助手、数据查询助手、AI研究助手、知识库文件分析助手）
      - API密钥输入框
      - API端点输入框
      - 应用ID输入框（Dify应用ID）
      - 应用代码输入框（Dify应用代码）
      - 代理描述输入框
      - 状态选择框（启用/禁用）
    - 个人专题助手特有字段：
      - 无特有字段
    - 数据查询助手特有字段：
      - 无特有字段
    - AI研究助手特有字段：
      - 标签输入框（用于分类和筛选）
    - 知识库文件分析助手特有字段：
      - 上传接口路径输入框（如/api/files/upload）
      - 分析接口路径输入框（如/api/workflows/run）
  - 操作按钮：
    - 取消
    - 保存
- **字段来源**：
  - 代理数据：从选中的代理对象(currentAiAgent)获取
- **功能说明**：
  - 修改代理信息：
    - 触发方式：在表单字段中输入或选择值
    - 行为：更新currentAiAgent状态
    - 可操作对象：管理员
  - 保存代理：
    - 触发方式：点击"保存"按钮
    - 行为：
      - 如果是编辑现有代理：更新aiAgents数组中对应的代理
      - 如果是添加新代理：将新代理添加到aiAgents数组
      - 显示成功提示
      - 关闭模态框
    - 可操作对象：管理员
  - 取消编辑：
    - 触发方式：点击"取消"按钮或模态框外区域
    - 行为：关闭模态框，不保存更改
    - 可操作对象：管理员
- **样式特点**：
  - 白色背景
  - 圆角边框
  - 表单字段垂直排列
  - 保存按钮：橙色背景 (#f5a623)
  - 模态框外区域半透明黑色遮罩

### 技术实现
- **页面组件**：app/system-management/page.tsx
- **主要子组件**：无独立子组件，所有组件内嵌在页面文件中
- **样式实现**：
  - 使用Tailwind CSS进行样式设计
  - 背景色：浅灰色 (#f9fafb)
  - 主题色：橙色 (#f5a623)、绿色 (#1e7a43)
  - 响应式设计，适配不同屏幕尺寸
- **状态管理**：
  - 使用React的useState钩子管理组件状态
  - 使用过滤函数处理搜索和筛选逻辑

### 数据流向
- **页面加载**：
  1. 加载app/system-management/page.tsx组件
  2. 初始化各个标签页的数据（模拟数据）
  3. 渲染当前活动标签页的内容
- **标签页切换**：
  1. 用户点击标签
  2. 更新activeTab状态
  3. 渲染对应标签页的内容
- **搜索和筛选**：
  1. 用户输入搜索关键词或选择筛选条件
  2. 使用过滤函数过滤数据
  3. 更新显示的数据列表
- **编辑操作**：
  1. 用户点击添加或编辑按钮
  2. 打开编辑模态框
  3. 用户修改表单字段
  4. 用户点击保存按钮
  5. 更新数据列表
  6. 显示成功提示
  7. 关闭模态框

### 特殊说明
1. **AI代理类型**：
   - 个人专题助手(personal)：
     - 显示在个人专题页面
     - 系统预设5个（对应5个革命先辈），不可删除，只能编辑
     - 配置字段：
       - 代理名称：如"蔡和森研究助手"
       - API密钥：Dify API的授权令牌
       - API端点：Dify API的基础URL（如https://ai.glab.vip）
       - 应用ID：Dify应用的唯一标识符
       - 应用代码：Dify应用的代码
       - 代理描述：助手的功能描述
       - 状态：启用/禁用
   - 数据查询助手(data-query)：
     - 显示在数据查询页面
     - 系统预设1个，不可删除，只能编辑
     - 配置字段：
       - 代理名称：如"数据查询助手"
       - API密钥：Dify API的授权令牌
       - API端点：Dify API的基础URL（如https://ai.glab.vip）
       - 应用ID：Dify应用的唯一标识符
       - 应用代码：Dify应用的代码
       - 代理描述：助手的功能描述
       - 状态：启用/禁用
   - AI研究助手(assistant)：
     - 显示在AI研究助手页面
     - 可以添加、编辑和删除
     - 配置字段：
       - 代理名称：如"家族历史研究助手"
       - API密钥：Dify API的授权令牌
       - API端点：Dify API的基础URL（如https://ai.glab.vip）
       - 应用ID：Dify应用的唯一标识符
       - 应用代码：Dify应用的代码
       - 代理描述：助手的功能描述
       - 代理标签：用于分类和筛选的标签
       - 状态：启用/禁用
   - 知识库文件分析助手(knowledge-file)：
     - 用于知识库文件上传分析
     - 系统预设1个，不可删除，只能编辑
     - 配置字段：
       - 代理名称：如"知识库文件分析助手"
       - API密钥：Dify API的授权令牌
       - API端点：Dify API的基础URL（如https://ai.glab.vip）
       - 应用ID：Dify应用的唯一标识符
       - 应用代码：Dify应用的代码
       - 上传接口路径：文件上传API路径（如/api/files/upload）
       - 分析接口路径：文件分析API路径（如/api/workflows/run）
       - 代理描述：助手的功能描述
       - 状态：启用/禁用
2. **AI代理数据流向**：
   - 系统管理页面可以添加/编辑/删除AI代理（根据代理类型有不同限制）
   - 保存后的数据会通过API同步到其他页面
   - 不同类型的代理会显示在对应的页面
   - 知识库文件分析助手配置会应用于知识库文件上传流程
   - 文件上传时会调用知识库文件分析助手配置的两个Dify API接口：
     - 上传接口：用于将文件上传到Dify
     - 分析接口：用于分析上传的文件并返回结果
3. **权限控制**：
   - 此页面可由管理员和拥有系统管理权限的用户访问
   - 无权限用户应被重定向到首页或显示权限不足提示
   - 角色管理功能用于控制用户对系统各功能的访问权限
4. **用户管理**：
   - 用户密码不可查看，只能通过重置功能修改
   - 新注册用户默认分配"初级访问者"角色
   - 登录验证时，如果用户不存在，会提示用户注册
5. **安全配置**：
   - 密码规则设置会在用户注册和修改密码时生效
   - 可设置密码最小长度、是否要求大小写字母、数字和特殊字符
6. **响应式设计**：
   - 桌面端：表格布局，AI代理使用网格布局
   - 移动端：卡片式布局，表格转为垂直列表
