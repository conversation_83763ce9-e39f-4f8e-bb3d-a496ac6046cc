/**
 * 角色和权限模型测试
 *
 * 测试角色和权限模型的字段、验证和关联
 */

const { Role, Permission, User } = require('../../src/models');

describe('角色和权限模型', () => {
  let testRole;
  let adminRole;
  let testPermission;
  let testUser;

  // 生成随机后缀，避免数据冲突
  const randomSuffix = Math.floor(Math.random() * 10000);

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    try {
      // 创建测试角色
      testRole = await Role.create({
        name: `test_role_${randomSuffix}`,
        description: '测试角色',
        is_system: false
      }, { transaction: global.testTransaction });

      // 创建管理员角色
      adminRole = await Role.create({
        name: `test_admin_role_${randomSuffix}`,
        description: '测试管理员角色',
        is_system: true
      }, { transaction: global.testTransaction });

      // 创建测试权限
      testPermission = await Permission.create({
        name: `test_permission_${randomSuffix}`,
        code: `test:permission:${randomSuffix}`,
        description: '测试权限',
        module: 'test'
      }, { transaction: global.testTransaction });

      // 关联角色和权限
      await testRole.addPermission(testPermission, { transaction: global.testTransaction });
      await adminRole.addPermission(testPermission, { transaction: global.testTransaction });

      // 创建测试用户
      testUser = await User.create({
        username: `roleuser_${randomSuffix}`,
        password: 'Password123!',
        email: `roleuser_${randomSuffix}@example.com`,
        phone: `138${randomSuffix.toString().padStart(8, '0')}`,
        role_id: testRole.id,
        is_active: true
      }, { transaction: global.testTransaction });
    } catch (error) {
      console.error('创建测试数据失败:', error);
      throw error;
    }
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    // 事务会自动回滚，不需要手动清理数据
    console.log('测试数据已通过事务回滚');
  });

  // 测试角色模型
  describe('角色模型', () => {
    test('应该成功创建角色', async () => {
      const role = await Role.findByPk(testRole.id, { transaction: global.testTransaction });

      expect(role).toBeDefined();
      expect(role.name).toBe(`test_role_${randomSuffix}`);
      expect(role.description).toBe('测试角色');
      expect(role.is_system).toBe(false);
    });

    test('不应该创建重复名称的角色', async () => {
      try {
        await Role.create({
          name: `test_role_${randomSuffix}`, // 重复的名称
          description: '重复的角色名',
          is_system: false
        }, { transaction: global.testTransaction });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建没有名称的角色', async () => {
      try {
        await Role.create({
          // 缺少名称
          description: '缺少名称的角色',
          is_system: false
        }, { transaction: global.testTransaction });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('应该成功更新角色信息', async () => {
      await Role.update({
        description: '更新后的角色描述'
      }, {
        where: { id: testRole.id },
        transaction: global.testTransaction
      });

      const updatedRole = await Role.findByPk(testRole.id, { transaction: global.testTransaction });
      expect(updatedRole.description).toBe('更新后的角色描述');

      // 恢复原始数据
      await Role.update({
        description: '测试角色'
      }, {
        where: { id: testRole.id },
        transaction: global.testTransaction
      });
    });

    test('角色应该关联到权限', async () => {
      // 检查角色模型是否有权限关联
      expect(Role.associations).toHaveProperty('permissions');

      // 获取角色的权限
      const role = await Role.findByPk(testRole.id, {
        include: ['permissions'],
        transaction: global.testTransaction
      });

      expect(role.permissions).toBeDefined();
      expect(role.permissions.length).toBeGreaterThan(0);
      expect(role.permissions[0].id).toBe(testPermission.id);
      expect(role.permissions[0].name).toBe(`test_permission_${randomSuffix}`);
    });

    test('角色应该关联到用户', async () => {
      // 检查角色模型是否有用户关联
      expect(Role.associations).toHaveProperty('users');

      // 获取角色的用户
      const role = await Role.findByPk(testRole.id, {
        include: ['users'],
        transaction: global.testTransaction
      });

      expect(role.users).toBeDefined();
      expect(role.users.length).toBeGreaterThan(0);
      expect(role.users[0].id).toBe(testUser.id);
      expect(role.users[0].username).toBe(`roleuser_${randomSuffix}`);
    });
  });

  // 测试权限模型
  describe('权限模型', () => {
    test('应该成功创建权限', async () => {
      const permission = await Permission.findByPk(testPermission.id, { transaction: global.testTransaction });

      expect(permission).toBeDefined();
      expect(permission.name).toBe(`test_permission_${randomSuffix}`);
      expect(permission.description).toBe('测试权限');
      expect(permission.code).toBe(`test:permission:${randomSuffix}`);
      expect(permission.module).toBe('test');
    });

    test('不应该创建重复名称的权限', async () => {
      try {
        await Permission.create({
          name: `test_permission_${randomSuffix}`, // 重复的名称
          code: `test:permission:new:${randomSuffix}`,
          description: '重复的权限名',
          module: 'test'
        }, { transaction: global.testTransaction });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建没有名称的权限', async () => {
      try {
        await Permission.create({
          // 缺少名称
          code: `test:permission:noname:${randomSuffix}`,
          description: '缺少名称的权限',
          module: 'test'
        }, { transaction: global.testTransaction });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建没有代码的权限', async () => {
      try {
        await Permission.create({
          name: `no_code_permission_${randomSuffix}`,
          // 缺少代码
          description: '缺少代码的权限',
          module: 'test'
        }, { transaction: global.testTransaction });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('不应该创建没有模块的权限', async () => {
      try {
        await Permission.create({
          name: `no_module_permission_${randomSuffix}`,
          code: `test:permission:nomodule:${randomSuffix}`,
          description: '缺少模块的权限'
          // 缺少模块
        }, { transaction: global.testTransaction });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('应该成功更新权限信息', async () => {
      await Permission.update({
        description: '更新后的权限描述'
      }, {
        where: { id: testPermission.id },
        transaction: global.testTransaction
      });

      const updatedPermission = await Permission.findByPk(testPermission.id, { transaction: global.testTransaction });
      expect(updatedPermission.description).toBe('更新后的权限描述');

      // 恢复原始数据
      await Permission.update({
        description: '测试权限'
      }, {
        where: { id: testPermission.id },
        transaction: global.testTransaction
      });
    });

    test('权限应该关联到角色', async () => {
      // 检查权限模型是否有角色关联
      expect(Permission.associations).toHaveProperty('roles');

      // 获取权限的角色
      const permission = await Permission.findByPk(testPermission.id, {
        include: ['roles'],
        transaction: global.testTransaction
      });

      expect(permission.roles).toBeDefined();
      expect(permission.roles.length).toBeGreaterThan(0);

      // 验证返回的角色中包含我们创建的测试角色
      const foundTestRole = permission.roles.some(role => role.id === testRole.id);
      const foundAdminRole = permission.roles.some(role => role.id === adminRole.id);

      expect(foundTestRole).toBe(true);
      expect(foundAdminRole).toBe(true);
    });
  });

  // 测试角色实例方法
  describe('角色实例方法', () => {
    test('hasPermission方法应该正确判断角色是否有指定权限', async () => {
      // 测试角色有test:read权限
      const hasReadPermission = await testRole.hasPermission('test', 'read');
      expect(hasReadPermission).toBe(true);

      // 测试角色没有test:write权限
      const hasWritePermission = await testRole.hasPermission('test', 'write');
      expect(hasWritePermission).toBe(false);
    });

    test('isSystem方法应该正确判断角色是否为系统角色', async () => {
      // 测试角色不是系统角色
      expect(testRole.isSystem()).toBe(false);

      // 管理员角色是系统角色
      expect(adminRole.isSystem()).toBe(true);
    });
  });

  // 测试角色类方法
  describe('角色类方法', () => {
    test('findByName方法应该返回指定名称的角色', async () => {
      const role = await Role.findByName(`test_role_${randomSuffix}`);
      expect(role).toBeDefined();
      expect(role.id).toBe(testRole.id);
      expect(role.name).toBe(`test_role_${randomSuffix}`);

      const nonExistentRole = await Role.findByName('nonexistent_role');
      expect(nonExistentRole).toBeNull();
    });

    test('getSystemRoles方法应该返回所有系统角色', async () => {
      const systemRoles = await Role.getSystemRoles();
      expect(Array.isArray(systemRoles)).toBe(true);

      // 所有返回的角色都应该是系统角色
      systemRoles.forEach(role => {
        expect(role.is_system).toBe(true);
      });

      // 验证返回的角色中包含我们创建的管理员角色
      const foundAdminRole = systemRoles.some(role => role.id === adminRole.id);
      expect(foundAdminRole).toBe(true);
    });
  });

  // 测试权限实例方法
  describe('权限实例方法', () => {
    test('getFullName方法应该返回完整的权限名称', async () => {
      const fullName = testPermission.getFullName();
      expect(fullName).toBe('test:read');
    });
  });

  // 测试权限类方法
  describe('权限类方法', () => {
    test('findByResource方法应该返回指定资源的权限列表', async () => {
      const permissions = await Permission.findByResource('test');
      expect(Array.isArray(permissions)).toBe(true);

      // 所有返回的权限都应该属于指定资源
      permissions.forEach(permission => {
        expect(permission.resource).toBe('test');
      });

      // 验证返回的权限中包含我们创建的测试权限
      const foundTestPermission = permissions.some(permission => permission.id === testPermission.id);
      expect(foundTestPermission).toBe(true);
    });

    test('parsePermissionString方法应该正确解析权限字符串', async () => {
      const { resource, action } = Permission.parsePermissionString('test:read');
      expect(resource).toBe('test');
      expect(action).toBe('read');

      // 测试无效的权限字符串
      try {
        Permission.parsePermissionString('invalid_permission_string');
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });
});
