"use client"

import { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { X, User, Search, ChevronLeft, ChevronRight, AlertCircle, CheckCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { RolePermissionModal } from "./role-permission-modal"
import { getRolePermissions } from "@/utils/role-permission-utils"
import authService from "@/services/auth-service"
import { toast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import axios from "axios"

// 在文件顶部导入类型定义
import type { AIAgent } from "@/types/ai-assistants"
import commentService, { Comment as ApiComment } from "@/services/comment-service"
import { UserManager } from "@/components/user"
import apiService from "@/services/api-service"
// 角色权限相关的工具函数将直接在组件中定义
import { useAuth } from "@/contexts/auth-context"
import { logger, sanitizeData } from "@/utils/logger"

// 获取API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api'

// 权限类型
interface Permission {
  id: number;
  code: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
}

/**
 * 获取所有权限
 * @returns 权限列表
 */
async function getAllPermissions(): Promise<Permission[]> {
  try {
    const token = localStorage.getItem('hefamily_token');
    if (!token) {
      throw new Error('未找到认证token');
    }

    const response = await axios.get(`${API_BASE_URL}/permissions`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    logger.debug('获取所有权限响应:', sanitizeData(response.data));

    // 输出所有权限代码，帮助调试
    if (response.data && response.data.data && response.data.data.length > 0) {
      logger.debug('系统中所有权限代码:', response.data.data.map(p => p.code).sort());
    }

    return response.data.data || [];
  } catch (error) {
    logger.error('获取所有权限失败:', sanitizeData(error));
    throw error;
  }
}

// 角色权限相关函数已移至 utils/role-permission-utils.ts

// 用户管理数据
interface UserType {
  id: string
  name: string
  phone: string
  email: string
  roleId: string // 角色ID，一个用户只能有一个角色
  roleName: string // 角色名称，用于显示
  status: "正常" | "待审核" | "已禁用" | "待激活"
  createdAt: string
}

// 角色管理数据
interface Role {
  id: string
  name: string
  description: string
  userCount: number
  createdAt: string
  isPreset?: boolean // 是否为预设角色，预设角色不可删除
}

// 修改留言管理数据接口中的状态类型
interface Message {
  id: string
  userId: string
  userName: string
  content: string
  target: string
  time: string
  status: string // 允许任何状态值，包括"待审核"、"已通过"、"已驳回"等
}

// 修改 AiAgent 接口定义为导入的类型
// 删除原有的 interface AiAgent {...} 定义

// Add this after the AiAgent interface definition:
function ApiKeyDisplay({ apiKey }: { apiKey: string }) {
  const [showKey, setShowKey] = useState(false)

  return (
    <div>
      <div className="text-sm font-medium text-gray-700 mb-1">API密钥</div>
      <div className="flex items-center">
        <div className="text-sm text-gray-900 break-all flex-1">{showKey ? apiKey : apiKey.replace(/./g, "*")}</div>
        <button onClick={() => setShowKey(!showKey)} className="ml-2 text-xs text-blue-600 hover:text-blue-800">
          {showKey ? "隐藏" : "显示"}
        </button>
      </div>
    </div>
  )
}

export default function SystemManagementPage() {
  const router = useRouter()
  const { isLoggedIn, userData, hasPermission } = useAuth()

  // 当前活动的标签页
  const [activeTab, setActiveTab] = useState("user-management")

  // 检查用户是否有权限访问系统管理页面
  useEffect(() => {
    // 如果用户未登录，重定向到首页
    if (!isLoggedIn) {
      toast({
        title: "访问受限",
        description: "请先登录",
        variant: "destructive"
      })
      router.push('/')
      return
    }

    // 检查用户是否有系统管理权限
    const hasSystemAccess = hasPermission('system:access')

    if (!hasSystemAccess) {
      toast({
        title: "权限不足",
        description: "您没有权限访问系统管理页面",
        variant: "destructive"
      })
      router.push('/')
      return
    }

    // 如果用户有权限，则获取角色和用户列表
    fetchRoles()
    // 其他初始化操作...
  }, [isLoggedIn, userData, router])

  // 用户管理状态已移至UserManager组件

  // 角色管理状态
  const [roleSearchQuery, setRoleSearchQuery] = useState("")
  const [showRoleModal, setShowRoleModal] = useState(false)
  const [currentRole, setCurrentRole] = useState<Role | null>(null)

  // 留言管理状态
  const [messageSearchQuery, setMessageSearchQuery] = useState("")
  const [messageStatusFilter, setMessageStatusFilter] = useState("全部状态")
  const [selectedMessages, setSelectedMessages] = useState<string[]>([])

  // 系统配置状态
  const [databaseConfig, setDatabaseConfig] = useState({
    host: "localhost:3306",
    dbName: "faith_family_db",
    username: "admin",
    password: "********",
  })

  const [systemParams, setSystemParams] = useState({
    systemName: "信仰家族研究平台",
    logRetentionDays: "30",
    maxLoginAttempts: "5",
    sessionTimeout: "30",
  })

  const [emailConfig, setEmailConfig] = useState({
    smtpServer: "smtp.example.com",
    port: "587",
    senderEmail: "<EMAIL>",
    password: "********",
  })

  const [securityConfig, setSecurityConfig] = useState({
    passwordMinLength: "8",
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
  })

  // 在 aiAgents 状态定义前添加注释
  /**
   * AI代理数据
   * 这些数据在实际应用中应从后端API获取
   *
   * 数据流向:
   * 1. 系统管理页面可以添加/编辑/删除AI代理
   * 2. type="personal"的代理会显示在个人专题页面
   * 3. type="data-query"的代理会显示在数据查询页面
   * 4. type="assistant"的代理会显示在AI研究助手页面
   */
  // AI管理状态
  const [aiAgents, setAiAgents] = useState<AIAgent[]>([
    {
      id: "1",
      name: "蔡和森专题AI助手",
      type: "personal",
      apiKey: "sk-***************************",
      apiEndpoint: "https://ai.glab.vip",
      appId: "600760dd-16b1-411c-87c9-ba1ac06a2741",
      appCode: "8jmDMR8Ukc8UUfi3",
      description: "为用户提供关于蔡和森生平、成就和历史背景的智能问答服务",
      tags: ["个人专题", "蔡和森", "历史研究"],
      status: "正常",
      lastUpdated: "2024-03-15 14:30",
    },
    {
      id: "2",
      name: "葛健豪专题AI助手",
      type: "personal",
      apiKey: "sk-***************************",
      apiEndpoint: "https://ai.glab.vip",
      appId: "600760dd-16b1-411c-87c9-ba1ac06a2742",
      appCode: "8jmDMR8Ukc8UUfi4",
      description: "为用户提供关于葛健豪生平、成就和历史背景的智能问答服务",
      tags: ["个人专题", "葛健豪", "历史研究"],
      status: "正常",
      lastUpdated: "2024-03-14 10:15",
    },
    {
      id: "3",
      name: "李富春专题AI助手",
      type: "personal",
      apiKey: "sk-***************************",
      apiEndpoint: "https://ai.glab.vip",
      appId: "600760dd-16b1-411c-87c9-ba1ac06a2743",
      appCode: "8jmDMR8Ukc8UUfi5",
      description: "为用户提供关于李富春生平、成就和历史背景的智能问答服务",
      tags: ["个人专题", "李富春", "历史研究"],
      status: "正常",
      lastUpdated: "2024-03-13 16:45",
    },
    {
      id: "4",
      name: "向警予专题AI助手",
      type: "personal",
      apiKey: "sk-***************************",
      apiEndpoint: "https://ai.glab.vip",
      appId: "600760dd-16b1-411c-87c9-ba1ac06a2744",
      appCode: "8jmDMR8Ukc8UUfi6",
      description: "为用户提供关于向警予生平、成就和历史背景的智能问答服务",
      tags: ["个人专题", "向警予", "历史研究"],
      status: "正常",
      lastUpdated: "2024-03-12 09:30",
    },
    {
      id: "5",
      name: "蔡畅专题AI助手",
      type: "personal",
      apiKey: "sk-***************************",
      apiEndpoint: "https://ai.glab.vip",
      appId: "600760dd-16b1-411c-87c9-ba1ac06a2745",
      appCode: "8jmDMR8Ukc8UUfi7",
      description: "为用户提供关于蔡畅生平、成就和历史背景的智能问答服务",
      tags: ["个人专题", "蔡畅", "历史研究"],
      status: "正常",
      lastUpdated: "2024-03-11 11:20",
    },
    {
      id: "6",
      name: "数据查询助手",
      type: "data-query",
      apiKey: "sk-***************************",
      apiEndpoint: "https://ai.glab.vip",
      appId: "b79a23b5-a49b-4d1e-8a27-2dc26dc5f097",
      appCode: "fVCnu68sGODWbJau",
      description: "协助用户进行复杂数据查询，理解自然语言并转换为结构化查询语言",
      tags: ["数据查询", "自然语言处理", "SQL生成"],
      status: "正常",
      lastUpdated: "2024-03-10 09:15",
    },
    {
      id: "7",
      name: "AI研究助手",
      type: "assistant",
      apiKey: "sk-***************************",
      apiEndpoint: "https://ai.glab.vip",
      appId: "600760dd-16b1-411c-87c9-ba1ac06a2747",
      appCode: "8jmDMR8Ukc8UUfi9",
      description: "全能型AI助手，可以回答关于信仰家族的各类问题，提供研究建议和资料整理",
      tags: ["全能助手", "研究辅助", "资料整理"],
      status: "正常",
      lastUpdated: "2024-03-05 16:45",
    },
    {
      id: "9", // 修改ID，避免重复
      name: "知识库文件分析助手",
      type: "knowledge-file",
      apiKey: "sk-***************************",
      apiEndpoint: "https://ai.glab.vip",
      appId: "600760dd-16b1-411c-87c9-ba1ac06a2748",
      appCode: "8jmDMR8Ukc8UUfi0",
      uploadApiPath: "/api/files/upload",
      analysisApiPath: "/api/workflows/run",
      description: "用于知识库文件上传分析，自动提取文件概要和详细信息",
      tags: ["文件分析", "知识库", "自动提取"],
      status: "正常",
      lastUpdated: "2024-03-01 10:00",
    },
  ])

  const [aiSearchQuery, setAiSearchQuery] = useState("")
  const [showAiAgentModal, setShowAiAgentModal] = useState(false)
  const [currentAiAgent, setCurrentAiAgent] = useState<AIAgent | null>(null)
  const [aiTypeFilter, setAiTypeFilter] = useState("全部类型")
  const [showDeleteAiAgentModal, setShowDeleteAiAgentModal] = useState(false)
  const [aiAgentToDelete, setAiAgentToDelete] = useState<AIAgent | null>(null)

  // 角色管理状态
  const [showDeleteRoleModal, setShowDeleteRoleModal] = useState(false)
  const [roleToSetPermission, setRoleToSetPermission] = useState<Role | null>(null)
  const [showRolePermissionModal, setShowRolePermissionModal] = useState(false)
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null)
  const [showSuccessToast, setShowSuccessToast] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")

  // 用户相关状态（用于其他模态框）
  const [showPassword, setShowPassword] = useState(false)
  const [currentUser, setCurrentUser] = useState<UserType | null>(null)
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false)
  const [userToReset, setUserToReset] = useState<UserType | null>(null)
  const [showDeleteUserModal, setShowDeleteUserModal] = useState(false)
  const [userToDelete, setUserToDelete] = useState<UserType | null>(null)

  // 评论服务已在顶部导入

  // 创建留言数据状态
  const [messages, setMessages] = useState<Message[]>([])
  const [loadingMessages, setLoadingMessages] = useState(false)
  const [loading, setLoading] = useState(false)

  // 获取留言列表
  const fetchMessages = async () => {
    try {
      setLoadingMessages(true)

      // 检查是否有认证token
      const token = localStorage.getItem('hefamily_token')
      if (!token) {
        logger.warn('未找到认证token，使用空留言列表')
        setMessages([])
        return
      }

      // 确保评论服务使用正确的认证信息
      const response = await commentService.getComments({
        page: 1,
        limit: 10,
      })

      // 将API返回的评论格式转换为本地格式
      // 添加空值检查，防止数据格式不符合预期导致的错误
      logger.debug('获取到的评论数据:', sanitizeData(response));

      const formattedMessages = response && response.comments ? response.comments.map(comment => ({
        id: comment.id.toString(),
        userId: comment.user_id ? comment.user_id.toString() : '',
        userName: comment.user?.username || '未知用户',
        content: comment.content || '',
        target: comment.topic_title || '未知主题',
        time: comment.created_at ? new Date(comment.created_at).toLocaleString('zh-CN') : '未知时间',
        status: comment.status === 'pending' ? '待审核' :
                comment.status === 'approved' ? '已通过' : '已驳回'
      })) : []

      logger.debug('格式化后的评论数据:', sanitizeData(formattedMessages));
      setMessages(formattedMessages)
    } catch (error) {
      logger.error("获取留言列表失败:", sanitizeData(error))

      // 如果是403错误，显示权限不足提示
      if (error.response && error.response.status === 403) {
        toast({
          title: "权限不足",
          description: "您没有权限查看留言列表",
          variant: "destructive"
        })
      } else {
        // 显示错误提示，但不强制登录
        toast({
          title: "获取留言列表失败",
          description: "请检查网络连接或稍后重试",
          variant: "destructive"
        })
      }

      // 设置空留言列表
      setMessages([])
    } finally {
      setLoadingMessages(false)
    }
  }

  // 获取AI助手列表
  const fetchAIAssistants = async () => {
    try {
      logger.debug('开始获取AI助手列表')
      setLoading(true)

      // 检查是否有认证token
      const token = localStorage.getItem('hefamily_token')
      if (!token) {
        logger.warn('未找到认证token，使用默认AI助手数据')
        return
      }

      // 直接使用axios调用API
      logger.debug('直接调用AI助手API')
      const response = await axios.get(`${API_BASE_URL}/ai`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      logger.debug('获取到的AI助手列表响应:', sanitizeData(response))

      if (response.data && response.data.data) {
        const assistants = response.data.data
        logger.debug('获取到的AI助手列表:', sanitizeData(assistants))

        if (assistants && assistants.length > 0) {
          // 转换后端数据格式为前端所需格式
          const formattedAssistants = assistants.map(item => ({
            id: item.id,
            name: item.name,
            type: item.type,
            description: item.description || '',
            tags: item.tags ? item.tags.split(',') : [],
            status: item.status === 'active' ? '正常' : '停用',
            lastUpdated: new Date(item.updated_at).toLocaleString(),
            apiKey: item.api_key || '',
            apiEndpoint: item.api_endpoint || '',
            appId: item.app_id || '',
            appCode: item.app_code || '',
            uploadApiPath: item.upload_api_path || '',
            analysisApiPath: item.analysis_api_path || ''
          }))

          setAiAgents(formattedAssistants)
        } else {
          logger.warn('API返回的AI助手列表为空，使用默认数据')
        }
      } else {
        logger.warn('API返回的数据格式不正确，使用默认数据')
      }
    } catch (error) {
      logger.error('获取AI助手列表失败:', sanitizeData(error))

      // 如果是403错误，显示权限不足提示
      if (error.response && error.response.status === 403) {
        toast({
          title: "权限不足",
          description: "您没有权限查看AI助手列表",
          variant: "destructive"
        })
      } else {
        toast({
          title: "获取AI助手列表失败",
          description: "请检查网络连接或稍后重试",
          variant: "destructive"
        })
      }
    } finally {
      setLoading(false)
    }
  }

  // 在组件加载时获取留言列表、角色列表和AI助手列表
  useEffect(() => {
    if (activeTab === "message-management") {
      fetchMessages()
    }

    if (activeTab === "role-management") {
      fetchRoles()
    }

    if (activeTab === "ai-management") {
      fetchAIAssistants()
    }
  }, [activeTab])

  // 用户数据现在由UserManager组件从API获取，不再使用模拟数据

  // 角色数据状态
  const [roles, setRoles] = useState<Role[]>([])

  // 从API获取角色数据
  const fetchRoles = async () => {
    try {
      setLoading(true)

      // 检查是否有认证token
      const token = localStorage.getItem('hefamily_token')
      logger.debug('认证token:', token ? 'token已获取' : '未找到');

      if (!token) {
        logger.warn('未找到认证token，使用默认角色数据')
        setRoles([
          {
            id: "admin_notoken",
            name: "管理员",
            description: "可进行系统管理，用户管理等操作",
            userCount: 1,
            createdAt: "2024-01-14 09:15",
            isPreset: true,
          },
          {
            id: "basic_notoken",
            name: "访问者",
            description: "新注册用户默认角色，仅具有基本查看权限",
            userCount: 0,
            createdAt: "2024-01-14 09:15",
            isPreset: true,
          }
        ])
        return
      }

      // 直接使用axios发送请求，绕过apiService
      logger.debug('开始获取角色数据，API基础URL:', API_BASE_URL);
      let response;
      try {
        // 使用axios直接发送请求
        response = await axios.get(`${API_BASE_URL}/roles`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        logger.debug('获取角色数据成功:', sanitizeData(response));
      } catch (error) {
        logger.error('获取角色数据API调用失败:', sanitizeData(error));

        // 详细记录错误信息
        if (error.response) {
          logger.error('错误响应状态:', error.response.status);
          logger.error('错误响应数据:', sanitizeData(error.response.data));
          logger.error('错误响应头:', sanitizeData(error.response.headers));
        } else if (error.request) {
          logger.error('请求已发送但没有收到响应:', sanitizeData(error.request));
        } else {
          logger.error('请求配置错误:', error.message);
        }

        throw error;
      }

      if (response && response.data) {
        logger.debug('API响应数据结构:', {
          hasData: !!response.data,
          hasDataData: !!(response.data.data),
          dataKeys: Object.keys(response.data),
          dataDataKeys: response.data.data ? Object.keys(response.data.data) : []
        });

        // 获取实际的角色数据
        const rolesData = response.data.data || response.data;

        if (!Array.isArray(rolesData)) {
          logger.error('API返回的角色数据不是数组:', sanitizeData(rolesData));
          throw new Error('API返回的角色数据格式不正确');
        }

        logger.debug('API返回的角色数据:', sanitizeData(rolesData));

        // 将API返回的角色数据格式转换为组件使用的格式
        const formattedRoles = rolesData.map(role => {
          // 为特殊角色提供友好的名称和描述
          let displayName = role.name;
          let description = role.description || '';
          let isPreset = role.is_system || false;

          // 为系统预设角色提供友好的名称和描述
          if (role.name === 'admin') {
            displayName = '管理员';
            description = '可进行系统管理，用户管理等操作';
            isPreset = true;
          } else if (role.name === 'basic_user' || role.name === '初级访问者' || role.name === '访问者') {
            displayName = '访问者';
            description = '新注册用户默认角色，仅具有基本查看权限';
            isPreset = true;
          }

          return {
            id: role.id.toString(),
            name: displayName,
            description: description,
            userCount: role.user_count || 0,
            createdAt: new Date(role.created_at).toLocaleString('zh-CN'),
            isPreset: isPreset
          };
        });

        logger.debug('过滤后的角色数据:', sanitizeData(formattedRoles));

        // 检查是否已存在管理员和访问者角色
        const hasAdmin = formattedRoles.some(role => role.name === '管理员');
        const hasBasicUser = formattedRoles.some(role => role.name === '访问者');

        // 获取已存在的角色ID列表，确保不会有重复的ID
        const existingIds = formattedRoles.map(role => role.id);

        // 如果没有找到管理员角色，添加一个默认的，确保ID不重复
        if (!hasAdmin) {
          // 生成一个不重复的ID
          let adminId = "1";
          while (existingIds.includes(adminId)) {
            adminId = `admin_${Math.random().toString(36).substring(2, 9)}`;
          }

          formattedRoles.push({
            id: adminId,
            name: "管理员",
            description: "可进行系统管理，用户管理等操作",
            userCount: 1,
            createdAt: "2024-01-14 09:15",
            isPreset: true,
          });

          // 将新ID添加到已存在ID列表
          existingIds.push(adminId);
        }

        // 如果没有找到访问者角色，添加一个默认的，确保ID不重复
        if (!hasBasicUser) {
          // 生成一个不重复的ID
          let basicUserId = "2";
          while (existingIds.includes(basicUserId)) {
            basicUserId = `basic_${Math.random().toString(36).substring(2, 9)}`;
          }

          formattedRoles.push({
            id: basicUserId,
            name: "访问者",
            description: "新注册用户默认角色，仅具有基本查看权限",
            userCount: 0,
            createdAt: "2024-01-14 09:15",
            isPreset: true,
          });
        }

        logger.debug('最终角色数据:', sanitizeData(formattedRoles));

        // 设置角色数据
        setRoles(formattedRoles);
      } else {
        // 如果API调用失败，使用默认角色数据
        logger.warn('无法从API获取角色数据，使用默认数据')
        setRoles([
          {
            id: "admin_apifail",
            name: "管理员",
            description: "可进行系统管理，用户管理等操作",
            userCount: 1,
            createdAt: "2024-01-14 09:15",
            isPreset: true,
          },
          {
            id: "basic_apifail",
            name: "访问者",
            description: "新注册用户默认角色，仅具有基本查看权限",
            userCount: 0,
            createdAt: "2024-01-14 09:15",
            isPreset: true,
          }
        ])
      }
    } catch (error) {
      logger.error('获取角色数据失败:', sanitizeData(error))

      // 如果是403错误，显示权限不足提示
      if (error.response && error.response.status === 403) {
        toast({
          title: "权限不足",
          description: "您没有权限查看角色列表，使用默认数据展示。",
          variant: "destructive"
        })
      } else {
        toast({
          title: "获取角色数据失败",
          description: "无法从服务器获取角色数据，请稍后重试。",
          variant: "destructive"
        })
      }

      // 使用默认角色数据作为后备，只保留管理员和初级访问者角色
      setRoles([
        {
          id: "admin_error",
          name: "管理员",
          description: "可进行系统管理，用户管理等操作",
          userCount: 1,
          createdAt: "2024-01-14 09:15",
          isPreset: true,
        },
        {
          id: "basic_error",
          name: "访问者",
          description: "新注册用户默认角色，仅具有基本查看权限",
          userCount: 0,
          createdAt: "2024-01-14 09:15",
          isPreset: true,
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  // 用户过滤已移至UserManager组件

  // 过滤角色
  const filteredRoles = roles.filter((role) => {
    return (
      role.name.toLowerCase().includes(roleSearchQuery.toLowerCase()) ||
      role.description.toLowerCase().includes(roleSearchQuery.toLowerCase())
    )
  })

  // 过滤留言
  const filteredMessages = messages.filter((message) => {
    const matchesSearch =
      message.userName.toLowerCase().includes(messageSearchQuery.toLowerCase()) ||
      message.content.toLowerCase().includes(messageSearchQuery.toLowerCase())

    const matchesStatus = messageStatusFilter === "全部状态" || message.status === messageStatusFilter

    return matchesSearch && matchesStatus
  })

  // 过滤AI代理
  const filteredAiAgents = aiAgents.filter((agent) => {
    const matchesSearch =
      agent.name.toLowerCase().includes(aiSearchQuery.toLowerCase()) ||
      agent.description.toLowerCase().includes(aiSearchQuery.toLowerCase()) ||
      agent.tags.some((tag) => tag.toLowerCase().includes(aiSearchQuery.toLowerCase()))

    const matchesType = aiTypeFilter === "全部类型" || agent.type === aiTypeFilter

    return matchesSearch && matchesType
  })

  // 用户选择处理已移至UserManager组件

  // 处理留言选择
  const handleMessageSelect = (messageId: string) => {
    if (selectedMessages.includes(messageId)) {
      setSelectedMessages(selectedMessages.filter((id) => id !== messageId))
    } else {
      setSelectedMessages([...selectedMessages, messageId])
    }
  }

  // 用户编辑模态框已移至UserManager组件

  // 打开角色编辑模态框
  const openRoleModal = (role: Role | null = null) => {
    setCurrentRole(role)
    setShowRoleModal(true)
  }

  // First, add a new state for the view modal
  const [showAiAgentViewModal, setShowAiAgentViewModal] = useState(false)
  const [viewingAiAgent, setViewingAiAgent] = useState<AIAgent | null>(null)

  // Add a function to open the view modal
  const openAiAgentViewModal = (agent: AIAgent) => {
    setViewingAiAgent(agent)
    setShowAiAgentViewModal(true)
  }

  // 打开AI代理编辑模态框
  const openAiAgentModal = (agent: AIAgent | null = null) => {
    setCurrentAiAgent(agent)
    setShowAiAgentModal(true)
  }

  // 打开AI代理删除确认模态框
  const openDeleteAiAgentModal = (agent: AIAgent) => {
    setAiAgentToDelete(agent)
    setShowDeleteAiAgentModal(true)
  }

  // 删除AI代理
  const deleteAiAgent = async () => {
    if (aiAgentToDelete) {
      try {
        setLoading(true)

        // 检查是否为系统预设助手，但AI研究助手可以删除
        const isSystemAgent = aiAgentToDelete.name.includes("系统预设");
        if (isSystemAgent) {
          throw new Error('系统预设助手不能删除')
        }

        // 获取token
        const token = localStorage.getItem('hefamily_token')
        if (!token) {
          logger.warn('未找到认证token，无法删除AI助手')
          throw new Error('未找到认证token')
        }

        // 调用API删除AI代理
        logger.debug('删除AI代理:', aiAgentToDelete.id)
        await axios.delete(`${API_BASE_URL}/ai/${aiAgentToDelete.id}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        // 更新本地状态
        const updatedAgents = aiAgents.filter((agent) => agent.id !== aiAgentToDelete.id)
        setAiAgents(updatedAgents)
        setSuccessMessage(`已成功删除AI代理 ${aiAgentToDelete.name}！`)
        setShowSuccessToast(true)
        setShowDeleteAiAgentModal(false)
        setTimeout(() => {
          setShowSuccessToast(false)
        }, 3000)
      } catch (error: any) {
        logger.error('删除AI代理失败:', sanitizeData(error))

        // 显示更具体的错误信息
        let errorMessage = "删除AI代理失败，请稍后重试";
        if (error.message === '系统预设助手不能删除') {
          errorMessage = "系统预设助手不能删除";
        } else if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }

        toast({
          title: "删除失败",
          description: errorMessage,
          variant: "destructive"
        })
      } finally {
        setLoading(false)
      }
    }
  }

  // 批量通过留言
  const batchApproveMessages = async () => {
    try {
      // 获取待审核的留言ID
      const pendingMessages = messages
        .filter(message => selectedMessages.includes(message.id) && message.status === "待审核")
        .map(message => message.id)

      if (pendingMessages.length === 0) {
        toast({
          title: "操作失败",
          description: "没有选中待审核的留言",
          variant: "destructive"
        })
        return
      }

      // 显示处理中状态
      setLoading(true)

      // 批量审核留言
      const promises = pendingMessages.map(id =>
        commentService.reviewComment(id, { status: 'approved' })
      )

      // 并行处理所有请求
      await Promise.all(promises)

      // 显示成功消息
      setSuccessMessage(`已批量通过 ${pendingMessages.length} 条留言`)
      setShowSuccessToast(true)
      setTimeout(() => {
        setShowSuccessToast(false)
      }, 3000)

      // 清空选择并刷新列表
      setSelectedMessages([])
      fetchMessages() // 刷新列表
    } catch (error) {
      logger.error("批量审核留言失败:", sanitizeData(error))
      toast({
        title: "操作失败",
        description: "批量审核留言失败，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 批量删除留言
  const batchDeleteMessages = async () => {
    try {
      if (selectedMessages.length === 0) {
        toast({
          title: "操作失败",
          description: "没有选中留言",
          variant: "destructive"
        })
        return
      }

      // 显示确认对话框
      if (!confirm(`确定要删除选中的 ${selectedMessages.length} 条留言吗？此操作不可撤销。`)) {
        return
      }

      // 显示处理中状态
      setLoading(true)

      // 批量删除留言
      const promises = selectedMessages.map(id =>
        commentService.deleteComment(id)
      )

      // 并行处理所有请求
      await Promise.all(promises)

      // 显示成功消息
      setSuccessMessage(`已批量删除 ${selectedMessages.length} 条留言`)
      setShowSuccessToast(true)
      setTimeout(() => {
        setShowSuccessToast(false)
      }, 3000)

      // 清空选择并刷新列表
      setSelectedMessages([])
      fetchMessages() // 刷新列表
    } catch (error) {
      logger.error("批量删除留言失败:", sanitizeData(error))
      toast({
        title: "操作失败",
        description: "批量删除留言失败，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 修改保存数据库配置函数
  const saveDbConfig = () => {
    setSuccessMessage("数据库配置已保存成功！")
    setShowSuccessToast(true)
    setTimeout(() => {
      setShowSuccessToast(false)
    }, 3000)
  }

  // 修改保存系统参数函数
  const saveSystemParams = () => {
    setSuccessMessage("系统参数已更新成功！")
    setShowSuccessToast(true)
    setTimeout(() => {
      setShowSuccessToast(false)
    }, 3000)
  }

  // 修改测试邮件连接函数
  const testEmailConnection = () => {
    setSuccessMessage("邮件服务器连接测试成功！")
    setShowSuccessToast(true)
    setTimeout(() => {
      setShowSuccessToast(false)
    }, 3000)
  }

  // 修改保存安全配置函数
  const saveSecurityConfig = () => {
    setSuccessMessage("安全配置已保存成功！")
    setShowSuccessToast(true)
    setTimeout(() => {
      setShowSuccessToast(false)
    }, 3000)
  }

  // 保存AI代理配置
  /**
   * 保存AI代理配置
   * 调用API保存数据
   * 保存后的数据会通过API同步到其他页面:
   * - 如果是type="assistant"类型，会显示在AI研究助手页面
   * - 如果是type="personal"类型，会显示在对应的个人专题页面
   * - 如果是type="data-query"类型，会显示在数据查询页面
   */
  const saveAiAgentConfig = async () => {
    try {
      setLoading(true)

      // 获取表单数据
      const formElements = document.querySelectorAll('.ai-agent-form input, .ai-agent-form select, .ai-agent-form textarea')
      const formData: Record<string, any> = {}

      formElements.forEach((element: any) => {
        if (element.name) {
          formData[element.name] = element.value
        }
      })

      logger.debug('保存AI代理配置 - 表单数据:', sanitizeData(formData))

      // 获取token
      const token = localStorage.getItem('hefamily_token')
      if (!token) {
        logger.warn('未找到认证token，无法保存AI助手配置')
        throw new Error('未找到认证token')
      }

      if (currentAiAgent) {
        // 编辑现有AI代理
        logger.debug('保存AI代理配置 - 更新现有AI代理:', currentAiAgent.id)

        // 构建更新数据 - 转换为后端格式
        const backendData = {
          name: formData.name || currentAiAgent.name,
          description: formData.description || currentAiAgent.description,
          api_key: formData.apiKey || currentAiAgent.apiKey,
          api_endpoint: formData.apiEndpoint || currentAiAgent.apiEndpoint,
          // 确保即使是空字符串也发送
          app_id: formData.appId !== undefined ? formData.appId : currentAiAgent.appId,
          app_code: formData.appCode !== undefined ? formData.appCode : currentAiAgent.appCode,
          status: formData.status === '正常' ? 'active' : 'inactive',
          tags: formData.tags || (Array.isArray(currentAiAgent.tags) ? currentAiAgent.tags.join(',') : '')
        }

        // 如果是知识库文件同步助手，添加特有字段
        if (currentAiAgent.type === 'knowledge-file') {
          backendData.upload_api_path = formData.uploadApiPath
          backendData.analysis_api_path = formData.analysisApiPath
        }

        logger.debug('保存AI代理配置 - 更新数据:', {
          ...sanitizeData(backendData),
          api_key: backendData.api_key ? '已设置' : '未设置'
        })

        // 直接调用API更新AI代理
        const response = await axios.put(
          `${API_BASE_URL}/ai/${currentAiAgent.id}`,
          backendData,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        )

        logger.debug('保存AI代理配置 - 更新响应:', sanitizeData(response))

        // 获取更新后的数据
        const getResponse = await axios.get(
          `${API_BASE_URL}/ai/${currentAiAgent.id}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        )

        const updatedItem = getResponse.data.data

        // 转换为前端格式
        const updatedAgent: AIAgent = {
          id: updatedItem.id,
          name: updatedItem.name,
          type: updatedItem.type,
          description: updatedItem.description || '',
          tags: updatedItem.tags ? updatedItem.tags.split(',') : [],
          status: updatedItem.status === 'active' ? '正常' : '停用',
          lastUpdated: new Date(updatedItem.updated_at).toLocaleString(),
          apiKey: updatedItem.api_key || '',
          apiEndpoint: updatedItem.api_endpoint || '',
          appId: updatedItem.app_id || '',
          appCode: updatedItem.app_code || '',
          uploadApiPath: updatedItem.upload_api_path || '',
          analysisApiPath: updatedItem.analysis_api_path || ''
        }

        logger.debug('保存AI代理配置 - 更新成功:', sanitizeData(updatedAgent))

        // 更新本地状态
        const updatedAgents = aiAgents.map((agent) => (agent.id === currentAiAgent.id ? updatedAgent : agent))
        setAiAgents(updatedAgents)
        setSuccessMessage("AI代理配置已更新成功！")
      } else {
        // 添加新AI研究助手
        logger.debug('保存AI代理配置 - 添加新AI研究助手')

        // 构建新助手数据 - 后端格式
        const backendData = {
          name: formData.name || "新AI研究助手",
          type: "assistant",
          api_key: formData.apiKey || "",
          api_endpoint: formData.apiEndpoint || "https://ai.glab.vip",
          app_id: formData.appId || "",
          app_code: formData.appCode || "",
          description: formData.description || "新的AI研究助手，可以回答关于信仰家族的各类问题",
          tags: formData.tags || "全能助手,研究辅助",
          status: formData.status === '正常' ? 'active' : 'inactive'
        }

        logger.debug('保存AI代理配置 - 新助手数据:', {
          ...sanitizeData(backendData),
          api_key: backendData.api_key ? '已设置' : '未设置'
        })

        try {
          // 调用API创建AI代理
          const response = await axios.post(
            `${API_BASE_URL}/ai`,
            backendData,
            {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          )

          logger.debug('保存AI代理配置 - 创建响应:', sanitizeData(response))

          if (response.data && response.data.data) {
            const createdItem = response.data.data

            // 转换为前端格式
            const createdAgent: AIAgent = {
              id: createdItem.id,
              name: createdItem.name,
              type: createdItem.type,
              description: createdItem.description || '',
              tags: createdItem.tags ? createdItem.tags.split(',') : [],
              status: createdItem.status === 'active' ? '正常' : '停用',
              lastUpdated: new Date(createdItem.created_at).toLocaleString(),
              apiKey: createdItem.api_key || '',
              apiEndpoint: createdItem.api_endpoint || '',
              appId: createdItem.app_id || '',
              appCode: createdItem.app_code || '',
              uploadApiPath: createdItem.upload_api_path || '',
              analysisApiPath: createdItem.analysis_api_path || ''
            }

            logger.debug('保存AI代理配置 - 创建成功:', sanitizeData(createdAgent))

            // 更新本地状态
            setAiAgents([...aiAgents, createdAgent])
            setSuccessMessage("新AI研究助手已创建成功！")
          } else {
            throw new Error('API返回的数据格式不正确')
          }
        } catch (createError) {
          logger.error('创建AI助手失败:', sanitizeData(createError))

          // 如果API不支持创建，使用模拟数据
          logger.warn('API可能不支持创建，使用模拟数据')

          const createdAgent: AIAgent = {
            id: (aiAgents.length + 1).toString(),
            name: backendData.name,
            type: "assistant",
            apiKey: backendData.api_key,
            apiEndpoint: backendData.api_endpoint,
            appId: backendData.app_id,
            appCode: backendData.app_code,
            description: backendData.description,
            tags: backendData.tags.split(','),
            status: backendData.status === 'active' ? '正常' : '停用',
            lastUpdated: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }).replace(/\//g, "-"),
          }

          logger.debug('保存AI代理配置 - 使用模拟数据:', sanitizeData(createdAgent))

          // 更新本地状态
          setAiAgents([...aiAgents, createdAgent])
          setSuccessMessage("新AI研究助手已创建成功！(模拟数据)")
        }
      }

      // 显示成功消息并关闭模态框
      setShowSuccessToast(true)
      setShowAiAgentModal(false)
      setTimeout(() => {
        setShowSuccessToast(false)
      }, 3000)
    } catch (error) {
      logger.error('保存AI代理配置失败:', sanitizeData(error))

      toast({
        title: "保存失败",
        description: "保存AI代理配置失败，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 检查AI代理是否可以删除
  const canDeleteAiAgent = (agent: AIAgent) => {
    // 只有系统预设助手不能删除，但AI研究助手可以删除
    const isSystemAgent = agent.name.includes("系统预设");
    return agent.type === "assistant" && !isSystemAgent;
  }

  // 添加新AI研究助手
  const addNewAiResearchAssistant = () => {
    setCurrentAiAgent(null)
    setShowAiAgentModal(true)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        <h1 className="text-2xl font-bold mb-6">系统管理</h1>

        <Tabs defaultValue="user-management" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6 flex flex-wrap">
            <TabsTrigger value="user-management" className="flex-1 min-w-[120px]">
              用户管理
            </TabsTrigger>
            <TabsTrigger value="role-management" className="flex-1 min-w-[120px]">
              权限管理
            </TabsTrigger>
            <TabsTrigger value="message-management" className="flex-1 min-w-[120px]">
              留言管理
            </TabsTrigger>
            <TabsTrigger value="system-config" className="flex-1 min-w-[120px]">
              系统配置
            </TabsTrigger>
            <TabsTrigger value="ai-management" className="flex-1 min-w-[120px]">
              AI管理
            </TabsTrigger>
          </TabsList>

          {/* 用户管理 */}
          <TabsContent value="user-management">
            <UserManager />
          </TabsContent>

          {/* 权限管理 */}
          <TabsContent value="role-management">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-bold mb-4">权限管理</h2>
              <p className="text-gray-600 mb-4">在这里您可以管理系统角色和权限，设置不同角色的访问权限。</p>
              <Button className="bg-[#f5a623] hover:bg-[#f5a623]/90" onClick={() => openRoleModal()}>
                添加角色
              </Button>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        角色名称
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        描述
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        用户数量
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        创建时间
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredRoles.map((role) => (
                      <tr key={role.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900">{role.name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{role.description}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-500">{role.userCount}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{role.createdAt}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              className="text-indigo-600 hover:text-indigo-900"
                              onClick={() => openRoleModal(role)}
                            >
                              编辑
                            </button>
                            <button
                              className="text-blue-600 hover:text-blue-900"
                              onClick={async () => {
                                setLoading(true);
                                setRoleToSetPermission(role);

                                try {
                                  // 先检查角色是否存在
                                  try {
                                    // 尝试获取角色信息
                                    const roleResponse = await axios.get(`${API_BASE_URL}/roles/${role.id}`, {
                                      headers: {
                                        'Authorization': `Bearer ${localStorage.getItem('hefamily_token')}`
                                      }
                                    });

                                    console.log('角色信息:', roleResponse.data);

                                    // 如果角色不存在，API会返回404错误，不会执行到这里
                                  } catch (roleError) {
                                    if (roleError.response && roleError.response.status === 404) {
                                      // 角色不存在
                                      toast({
                                        title: "角色不存在",
                                        description: `角色ID=${role.id}在系统中不存在，无法设置权限`,
                                        variant: "destructive"
                                      });
                                      setLoading(false);
                                      return;
                                    }
                                    // 其他错误继续执行
                                  }

                                  // 获取角色当前的权限
                                  const permissions = await getRolePermissions(parseInt(role.id));
                                  console.log('角色当前权限:', permissions);

                                  // 将权限信息添加到角色对象中
                                  const roleWithPermissions = {
                                    ...role,
                                    permissions: permissions
                                  };

                                  // 设置角色并打开模态框
                                  setRoleToSetPermission(roleWithPermissions);
                                  setShowRolePermissionModal(true);

                                  // 记录角色权限代码，用于调试
                                  const permissionCodes = permissions.map(permission => permission.code).filter(Boolean);
                                  console.log('角色权限代码:', permissionCodes);
                                } catch (error) {
                                  console.error('获取角色权限失败:', error);
                                  toast({
                                    title: "获取失败",
                                    description: "获取角色权限失败，请稍后重试",
                                    variant: "destructive"
                                  });
                                } finally {
                                  setLoading(false);
                                }
                              }}
                            >
                              权限设置
                            </button>
                            {!role.isPreset ? (
                              <button
                                className="text-red-600 hover:text-red-900"
                                onClick={() => {
                                  setRoleToDelete(role)
                                  setShowDeleteRoleModal(true)
                                }}
                              >
                                删除
                              </button>
                            ) : (
                              <span className="text-gray-400 cursor-not-allowed" title="预设角色不可删除">
                                删除
                              </span>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {filteredRoles.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">没有找到匹配的角色</p>
                </div>
              )}
            </div>
          </TabsContent>

          {/* 留言管理 */}
          <TabsContent value="message-management">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-bold">留言管理</h2>
              </div>

              <div className="mb-6 flex flex-col md:flex-row gap-4">
                <div className="relative md:w-64">
                  <input
                    type="text"
                    placeholder="搜索用户名/内容"
                    value={messageSearchQuery}
                    onChange={(e) => setMessageSearchQuery(e.target.value)}
                    className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                </div>
                <div className="flex space-x-2">
                  <select
                    className="border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    value={messageStatusFilter}
                    onChange={(e) => setMessageStatusFilter(e.target.value)}
                  >
                    <option value="全部状态">所有状态</option>
                    <option value="待审核">待审核</option>
                    <option value="已通过">已通过</option>
                    <option value="已驳回">已驳回</option>
                  </select>
                </div>
              </div>

              {loadingMessages ? (
                <div className="py-8 text-center">
                  <p className="text-gray-500">加载中...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          <div className="flex items-center">
                            <Checkbox
                              checked={selectedMessages.length === filteredMessages.length && filteredMessages.length > 0}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedMessages(filteredMessages.map((message) => message.id))
                                } else {
                                  setSelectedMessages([])
                                }
                              }}
                            />
                          </div>
                        </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        用户信息
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        留言内容
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        目标对象
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        时间
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        状态
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredMessages.map((message) => (
                      <tr key={message.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <Checkbox
                              checked={selectedMessages.includes(message.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedMessages([...selectedMessages, message.id])
                                } else {
                                  setSelectedMessages(selectedMessages.filter((id) => id !== message.id))
                                }
                              }}
                            />
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                              <User className="h-5 w-5 text-gray-500" />
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">{message.userName}</div>
                              <div className="text-sm text-gray-500">用户ID: {message.userId}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{message.content}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-500">{message.target}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{message.time}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              message.status === "待审核"
                                ? "bg-yellow-100 text-yellow-800"
                                : message.status === "已通过"
                                  ? "bg-green-100 text-green-800"
                                  : "bg-red-100 text-red-800"
                            }`}
                          >
                            {message.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            {message.status === "待审核" ? (
                              <>
                                <button
                                  className="text-green-600 hover:text-green-900"
                                  disabled={loading}
                                  onClick={async () => {
                                    try {
                                      setLoading(true)
                                      await commentService.reviewComment(message.id, { status: 'approved' })
                                      setSuccessMessage("留言已通过审核")
                                      setShowSuccessToast(true)
                                      setTimeout(() => {
                                        setShowSuccessToast(false)
                                      }, 3000)
                                      fetchMessages() // 刷新列表
                                    } catch (error) {
                                      console.error("审核留言失败:", error)
                                      toast({
                                        title: "操作失败",
                                        description: "审核留言失败，请稍后重试",
                                        variant: "destructive"
                                      })
                                    } finally {
                                      setLoading(false)
                                    }
                                  }}
                                >
                                  通过
                                </button>
                                <button
                                  className="text-red-600 hover:text-red-900"
                                  disabled={loading}
                                  onClick={async () => {
                                    try {
                                      setLoading(true)
                                      await commentService.reviewComment(message.id, {
                                        status: 'rejected',
                                        reason: '内容不符合规范'
                                      })
                                      setSuccessMessage("留言已驳回")
                                      setShowSuccessToast(true)
                                      setTimeout(() => {
                                        setShowSuccessToast(false)
                                      }, 3000)
                                      fetchMessages() // 刷新列表
                                    } catch (error) {
                                      console.error("驳回留言失败:", error)
                                      toast({
                                        title: "操作失败",
                                        description: "驳回留言失败，请稍后重试",
                                        variant: "destructive"
                                      })
                                    } finally {
                                      setLoading(false)
                                    }
                                  }}
                                >
                                  驳回
                                </button>
                              </>
                            ) : (
                              <button
                                className="text-red-600 hover:text-red-900"
                                disabled={loading}
                                onClick={async () => {
                                  // 显示确认对话框
                                  if (!confirm("确定要删除这条留言吗？此操作不可撤销。")) {
                                    return
                                  }

                                  try {
                                    setLoading(true)
                                    await commentService.deleteComment(message.id)
                                    setSuccessMessage("留言已删除")
                                    setShowSuccessToast(true)
                                    setTimeout(() => {
                                      setShowSuccessToast(false)
                                    }, 3000)
                                    fetchMessages() // 刷新列表
                                  } catch (error) {
                                    console.error("删除留言失败:", error)
                                    toast({
                                      title: "操作失败",
                                      description: "删除留言失败，请稍后重试",
                                      variant: "destructive"
                                    })
                                  } finally {
                                    setLoading(false)
                                  }
                                }}
                              >
                                删除
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {filteredMessages.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-gray-500">没有找到匹配的留言</p>
                  </div>
                )}
              </div>
              )}

              <div className="mt-6 flex justify-between items-center">
                <div className="text-sm text-gray-700">
                  共 <span className="font-medium">{filteredMessages.length}</span> 条记录
                </div>
                <div className="flex space-x-2">
                  <Button key="prev-page" variant="outline" size="sm" className="flex items-center">
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    上一页
                  </Button>
                  <Button key="page-1" className="bg-[#1e7a43] hover:bg-[#1e7a43]/90 h-8 w-8 p-0">1</Button>
                  <Button key="page-2" variant="outline" size="sm" className="h-8 w-8 p-0">
                    2
                  </Button>
                  <Button key="page-3" variant="outline" size="sm" className="h-8 w-8 p-0">
                    3
                  </Button>
                  <Button key="next-page" variant="outline" size="sm" className="flex items-center">
                    下一页
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>

              <div className="mt-6 border-t pt-6">
                <h3 className="text-md font-medium mb-4">批量操作</h3>
                <div className="flex flex-wrap gap-3">
                  <Button
                    variant="outline"
                    className="text-green-600 border-green-600 hover:bg-green-50"
                    disabled={selectedMessages.length === 0 || loading}
                    onClick={batchApproveMessages}
                  >
                    批量通过
                  </Button>
                  <Button
                    variant="outline"
                    className="text-red-600 border-red-600 hover:bg-red-50"
                    disabled={selectedMessages.length === 0 || loading}
                    onClick={async () => {
                      try {
                        // 获取待审核的留言ID
                        const pendingMessages = messages
                          .filter(message => selectedMessages.includes(message.id) && message.status === "待审核")
                          .map(message => message.id)

                        if (pendingMessages.length === 0) {
                          toast({
                            title: "操作失败",
                            description: "没有选中待审核的留言",
                            variant: "destructive"
                          })
                          return
                        }

                        // 显示处理中状态
                        setLoading(true)

                        // 批量驳回留言
                        const promises = pendingMessages.map(id =>
                          commentService.reviewComment(id, {
                            status: 'rejected',
                            reason: '内容不符合规范'
                          })
                        )

                        // 并行处理所有请求
                        await Promise.all(promises)

                        // 显示成功消息
                        setSuccessMessage(`已批量驳回 ${pendingMessages.length} 条留言`)
                        setShowSuccessToast(true)
                        setTimeout(() => {
                          setShowSuccessToast(false)
                        }, 3000)

                        // 清空选择并刷新列表
                        setSelectedMessages([])
                        fetchMessages() // 刷新列表
                      } catch (error) {
                        console.error("批量驳回留言失败:", error)
                        toast({
                          title: "操作失败",
                          description: "批量驳回留言失败，请稍后重试",
                          variant: "destructive"
                        })
                      } finally {
                        setLoading(false)
                      }
                    }}
                  >
                    批量驳回
                  </Button>
                  <Button
                    variant="outline"
                    className="text-red-600 border-red-600 hover:bg-red-50"
                    disabled={selectedMessages.length === 0 || loading}
                    onClick={batchDeleteMessages}
                  >
                    批量删除
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* 系统配置 */}
          <TabsContent value="system-config">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-bold mb-4">系统配置</h2>
              <p className="text-gray-600 mb-4">在这里您可以配置系统参数，包括数据库设置、邮件服务和安全设置。</p>

              {/* 数据库配置 */}
              <h3 className="text-md font-medium mb-2">数据库配置</h3>
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">数据库地址</label>
                  <input
                    type="text"
                    defaultValue={databaseConfig.host}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setDatabaseConfig({ ...databaseConfig, host: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">数据库名称</label>
                  <input
                    type="text"
                    defaultValue={databaseConfig.dbName}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setDatabaseConfig({ ...databaseConfig, dbName: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                  <input
                    type="text"
                    defaultValue={databaseConfig.username}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setDatabaseConfig({ ...databaseConfig, username: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">密码</label>
                  <input
                    type="password"
                    defaultValue={databaseConfig.password}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setDatabaseConfig({ ...databaseConfig, password: e.target.value })}
                  />
                </div>
              </div>

              {/* 系统参数设置 */}
              <h3 className="text-md font-medium mb-2">系统参数设置</h3>
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">系统名称</label>
                  <input
                    type="text"
                    defaultValue={systemParams.systemName}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setSystemParams({ ...systemParams, systemName: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">日志保留天数</label>
                  <input
                    type="number"
                    defaultValue={systemParams.logRetentionDays}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setSystemParams({ ...systemParams, logRetentionDays: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">最大登录尝试次数</label>
                  <input
                    type="number"
                    defaultValue={systemParams.maxLoginAttempts}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setSystemParams({ ...systemParams, maxLoginAttempts: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">会话超时时间（分钟）</label>
                  <input
                    type="number"
                    defaultValue={systemParams.sessionTimeout}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setSystemParams({ ...systemParams, sessionTimeout: e.target.value })}
                  />
                </div>
              </div>

              {/* 邮件服务配置 */}
              <h3 className="text-md font-medium mb-2">邮件服务配置</h3>
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">SMTP服务器</label>
                  <input
                    type="text"
                    defaultValue={emailConfig.smtpServer}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setEmailConfig({ ...emailConfig, smtpServer: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">端口</label>
                  <input
                    type="number"
                    defaultValue={emailConfig.port}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setEmailConfig({ ...emailConfig, port: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">发件人邮箱</label>
                  <input
                    type="email"
                    defaultValue={emailConfig.senderEmail}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setEmailConfig({ ...emailConfig, senderEmail: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">密码</label>
                  <input
                    type="password"
                    defaultValue={emailConfig.password}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setEmailConfig({ ...emailConfig, password: e.target.value })}
                  />
                </div>
                <Button className="bg-[#f5a623] hover:bg-[#f5a623]/90" onClick={testEmailConnection}>
                  测试连接
                </Button>
              </div>

              {/* 安全配置 */}
              <h3 className="text-md font-medium mb-2">安全配置</h3>
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">密码最小长度</label>
                  <input
                    type="number"
                    defaultValue={securityConfig.passwordMinLength}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    onChange={(e) => setSecurityConfig({ ...securityConfig, passwordMinLength: e.target.value })}
                  />
                </div>
                <div className="flex items-center">
                  <Checkbox
                    id="requireUppercase"
                    checked={securityConfig.requireUppercase}
                    onCheckedChange={(checked) =>
                      setSecurityConfig({ ...securityConfig, requireUppercase: checked as boolean })
                    }
                  />
                  <label htmlFor="requireUppercase" className="ml-2 text-sm">
                    要求大写字母
                  </label>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    id="requireLowercase"
                    checked={securityConfig.requireLowercase}
                    onCheckedChange={(checked) =>
                      setSecurityConfig({ ...securityConfig, requireLowercase: checked as boolean })
                    }
                  />
                  <label htmlFor="requireLowercase" className="ml-2 text-sm">
                    要求小写字母
                  </label>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    id="requireNumbers"
                    checked={securityConfig.requireNumbers}
                    onCheckedChange={(checked) =>
                      setSecurityConfig({ ...securityConfig, requireNumbers: checked as boolean })
                    }
                  />
                  <label htmlFor="requireNumbers" className="ml-2 text-sm">
                    要求数字
                  </label>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    id="requireSpecialChars"
                    checked={securityConfig.requireSpecialChars}
                    onCheckedChange={(checked) =>
                      setSecurityConfig({ ...securityConfig, requireSpecialChars: checked as boolean })
                    }
                  />
                  <label htmlFor="requireSpecialChars" className="ml-2 text-sm">
                    要求特殊字符
                  </label>
                </div>
              </div>

              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      注意：密码规则设置会在用户注册和修改密码时生效，请确保设置合理的密码规则以提高系统安全性。
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button className="bg-[#f5a623] hover:bg-[#f5a623]/90" onClick={saveDbConfig}>
                  保存配置
                </Button>
                <Button className="bg-[#f5a623] hover:bg-[#f5a623]/90 ml-4" onClick={saveSystemParams}>
                  保存参数
                </Button>
                <Button className="bg-[#f5a623] hover:bg-[#f5a623]/90 ml-4" onClick={saveSecurityConfig}>
                  保存安全配置
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* AI管理 */}
          <TabsContent value="ai-management">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-bold">AI代理管理</h2>
                <Button className="bg-[#f5a623] hover:bg-[#f5a623]/90" onClick={addNewAiResearchAssistant}>
                  添加AI研究助手
                </Button>
              </div>

              <div className="mb-6 flex flex-col md:flex-row gap-4">
                <div className="relative md:w-64">
                  <input
                    type="text"
                    placeholder="搜索代理名称/描述/标签"
                    value={aiSearchQuery}
                    onChange={(e) => setAiSearchQuery(e.target.value)}
                    className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                </div>
                <div className="flex space-x-2">
                  <select
                    className="border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    value={aiTypeFilter}
                    onChange={(e) => setAiTypeFilter(e.target.value)}
                  >
                    <option value="全部类型">所有类型</option>
                    <option value="personal">个人专题</option>
                    <option value="data-query">数据查询</option>
                    <option value="assistant">AI助手</option>
                    <option value="knowledge-file">知识库文件同步</option>
                  </select>
                </div>
              </div>



              {filteredAiAgents.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">没有找到匹配的AI代理</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredAiAgents.map((agent) => (
                    <div
                      key={agent.id}
                      className="bg-white border rounded-lg shadow-sm hover:shadow-md transition-shadow"
                    >
                      <div className="p-4">
                        <div className="flex justify-between items-center mb-3">
                          <h3 className="font-medium text-lg">{agent.name}</h3>
                          <span
                            className={`px-2 py-0.5 text-xs rounded-full ${
                              agent.status === "正常" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                            }`}
                          >
                            {agent.status}
                          </span>
                        </div>

                        <div className="mb-3">
                          <span
                            className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                              agent.type === "personal"
                                ? "bg-blue-100 text-blue-800 border border-blue-300"
                                : agent.type === "data-query"
                                  ? "bg-purple-100 text-purple-800 border border-purple-300"
                                  : agent.type === "knowledge-file"
                                    ? "bg-orange-100 text-orange-800 border border-orange-300"
                                    : "bg-green-100 text-green-800 border border-green-300"
                            }`}
                          >
                            {agent.type === "personal"
                              ? "个人专题助手"
                              : agent.type === "data-query"
                                ? "数据查询助手"
                                : agent.type === "knowledge-file"
                                  ? "知识库文件同步助手"
                                  : "AI研究助手"}
                          </span>
                        </div>

                        <div className="mb-3">
                          <div className="text-sm font-medium text-gray-700 mb-1">API地址</div>
                          <div className="text-xs text-gray-600 break-all">{agent.apiEndpoint}</div>
                        </div>

                        <div className="mb-3">
                          <div className="text-sm font-medium text-gray-700 mb-1">描述</div>
                          <div className="text-xs text-gray-600">{agent.description}</div>
                        </div>

                        <div className="mb-3">
                          <div className="text-sm font-medium text-gray-700 mb-1">标签</div>
                          <div className="flex flex-wrap gap-1">
                            {agent.tags.map((tag, index) => (
                              <span key={`list-tag-${agent.id}-${index}`} className="px-2 py-0.5 text-xs bg-gray-100 rounded-full">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div className="text-xs text-gray-500 mb-3">最后更新: {agent.lastUpdated}</div>

                        <div className="flex justify-end pt-2 border-t">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 h-auto"
                            onClick={() => openAiAgentViewModal(agent)}
                          >
                            查看
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 h-auto ml-2"
                            onClick={() => openAiAgentModal(agent)}
                          >
                            编辑
                          </Button>
                          {canDeleteAiAgent(agent) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-800 hover:bg-red-50 px-2 py-1 h-auto ml-2"
                              onClick={() => openDeleteAiAgentModal(agent)}
                            >
                              删除
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        {/* AI代理编辑模态框 */}
        {showAiAgentModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white rounded-lg w-full max-w-2xl p-6 relative max-h-[90vh] overflow-y-auto mx-4">
              <button
                onClick={() => setShowAiAgentModal(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-xl font-bold mb-4">{currentAiAgent ? "编辑AI代理" : "新增AI研究助手"}</h2>

              <div className="space-y-4 ai-agent-form">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">代理名称</label>
                  <input
                    type="text"
                    name="name"
                    defaultValue={currentAiAgent?.name}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">代理类型</label>
                  <select
                    name="type"
                    defaultValue={currentAiAgent?.type || "assistant"}
                    disabled={currentAiAgent !== null && currentAiAgent.id !== "new-knowledge-file"}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                  >
                    <option value="assistant">AI研究助手</option>
                    <option value="personal" disabled>个人专题助手</option>
                    <option value="data-query" disabled>数据查询助手</option>
                    <option value="knowledge-file" disabled={currentAiAgent?.id !== "new-knowledge-file"}>知识库文件同步助手</option>
                  </select>
                  {currentAiAgent !== null && currentAiAgent.id !== "new-knowledge-file" && (
                    <p className="text-xs text-gray-500 mt-1">已存在的助手类型不可修改</p>
                  )}
                  {!currentAiAgent && (
                    <p className="text-xs text-gray-500 mt-1">只能添加AI研究助手类型，其他类型不可新增</p>
                  )}
                  {currentAiAgent?.id === "new-knowledge-file" && (
                    <p className="text-xs text-gray-500 mt-1">知识库文件同步助手用于配置文件上传到AI平台知识库的设置</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">API地址</label>
                  <input
                    type="text"
                    name="apiEndpoint"
                    defaultValue={currentAiAgent?.apiEndpoint}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    placeholder="例如: https://ai.glab.vip"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">API密钥</label>
                  <input
                    type="password"
                    name="apiKey"
                    defaultValue={currentAiAgent?.apiKey}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                  />
                </div>

                {currentAiAgent?.type === "knowledge-file" ? (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">系统知识库数据集ID</label>
                      <input
                        type="text"
                        name="appId"
                        defaultValue={currentAiAgent?.appId}
                        className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                        placeholder="例如: 77199451-730a-4d79-a1c9-9b9e6bfcd747"
                      />
                      <p className="text-xs text-gray-500 mt-1">用于系统知识库文件上传的Dify数据集ID（存储在appId字段）</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">用户知识库数据集ID</label>
                      <input
                        type="text"
                        name="appCode"
                        defaultValue={currentAiAgent?.appCode}
                        className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                        placeholder="例如: 602d59cf-3384-4105-bf91-e1481b30b6b2"
                      />
                      <p className="text-xs text-gray-500 mt-1">用于用户知识库文件上传的Dify数据集ID（存储在appCode字段）</p>
                    </div>
                  </>
                ) : (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">应用ID (AI平台应用ID)</label>
                      <input
                        type="text"
                        name="appId"
                        defaultValue={currentAiAgent?.appId}
                        className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                        placeholder="例如: 600760dd-16b1-411c-87c9-ba1ac06a2741"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">应用代码 (AI平台应用代码)</label>
                      <input
                        type="text"
                        name="appCode"
                        defaultValue={currentAiAgent?.appCode}
                        className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                        placeholder="例如: 8jmDMR8Ukc8UUfi3"
                      />
                    </div>
                  </>
                )}

                {/* 知识库文件同步助手特有字段 */}
                {currentAiAgent?.type === "knowledge-file" && (
                  <>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">系统知识库文件上传API路径</label>
                      <input
                        type="text"
                        name="uploadApiPath"
                        defaultValue={currentAiAgent?.uploadApiPath}
                        className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                        placeholder="例如: /v1/datasets/{datasetId}/document/create-by-file"
                      />
                      <p className="text-xs text-gray-500 mt-1">用于将系统知识库文件上传到AI平台的API路径，{"{datasetId}"}会被替换为系统知识库数据集ID</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">用户知识库文件上传API路径</label>
                      <input
                        type="text"
                        name="analysisApiPath"
                        defaultValue={currentAiAgent?.analysisApiPath}
                        className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                        placeholder="例如: /v1/datasets/{datasetId}/document/create-by-file"
                      />
                      <p className="text-xs text-gray-500 mt-1">用于将用户知识库文件上传到AI平台的API路径，{"{datasetId}"}会被替换为用户知识库数据集ID</p>
                    </div>
                  </>
                )}

                {/* 个人专题助手特有字段 */}
                {currentAiAgent?.type === "personal" && (
                  <div>
                    {/* 个人专题助手没有特有字段 */}
                  </div>
                )}

                {/* 数据查询助手特有字段 */}
                {currentAiAgent?.type === "data-query" && (
                  <div>
                    {/* 数据查询助手没有特有字段 */}
                  </div>
                )}

                {/* 所有AI助手都支持标签 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">标签（用逗号分隔）</label>
                  <input
                    type="text"
                    name="tags"
                    defaultValue={currentAiAgent?.tags?.join(", ")}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    placeholder="例如: 历史研究, 家族史, 档案分析"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                  <textarea
                    name="description"
                    defaultValue={currentAiAgent?.description}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                  <select
                    name="status"
                    defaultValue={currentAiAgent?.status}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                  >
                    <option value="正常">正常</option>
                    <option value="禁用">禁用</option>
                  </select>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <Button variant="outline" onClick={() => setShowAiAgentModal(false)}>
                    取消
                  </Button>
                  <Button
                    className="bg-[#f5a623] hover:bg-[#f5a623]/90"
                    onClick={saveAiAgentConfig}
                    disabled={loading}
                  >
                    {loading ? "保存中..." : (currentAiAgent ? "保存" : "创建")}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* AI代理删除确认模态框 */}
        {showDeleteAiAgentModal && aiAgentToDelete && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white rounded-lg w-full max-w-md p-6 relative max-h-[90vh] overflow-y-auto mx-4">
              <button
                onClick={() => setShowDeleteAiAgentModal(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-xl font-bold mb-4">删除AI代理</h2>
              <p className="mb-4">
                您确定要删除AI代理 <span className="font-bold">{aiAgentToDelete.name}</span> 吗？此操作不可撤销。
              </p>

              <div className="flex justify-end space-x-3 pt-4">
                <Button variant="outline" onClick={() => setShowDeleteAiAgentModal(false)}>
                  取消
                </Button>
                <Button className="bg-red-600 hover:bg-red-700 text-white" onClick={deleteAiAgent}>
                  确认删除
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 用户编辑模态框已移至UserManager组件 */}

        {/* 角色编辑模态框 */}
        {showRoleModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white rounded-lg w-full max-w-md p-6 relative max-h-[90vh] overflow-y-auto mx-4">
              <button
                onClick={() => setShowRoleModal(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-xl font-bold mb-4">{currentRole ? "编辑角色" : "新增角色"}</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">角色名称</label>
                  {currentRole?.isPreset ? (
                    <div>
                      <input
                        type="text"
                        defaultValue={currentRole?.name}
                        className="w-full border border-gray-300 rounded-md py-2 px-3 bg-gray-100 cursor-not-allowed"
                        disabled
                      />
                      <p className="text-xs text-gray-500 mt-1">预设角色名称不可修改</p>
                    </div>
                  ) : (
                    <input
                      type="text"
                      defaultValue={currentRole?.name}
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    />
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">权限描述</label>
                  <textarea
                    defaultValue={currentRole?.description}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    rows={3}
                  />
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                  <Button variant="outline" onClick={() => setShowRoleModal(false)}>
                    取消
                  </Button>
                  <Button className="bg-[#f5a623] hover:bg-[#f5a623]/90">{currentRole ? "保存" : "创建"}</Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 添加重置密码模态框 */}
        {showResetPasswordModal && userToReset && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white rounded-lg w-full max-w-md p-6 relative max-h-[90vh] overflow-y-auto mx-4">
              <button
                onClick={() => setShowResetPasswordModal(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-xl font-bold mb-4">重置密码</h2>
              <p className="mb-4">
                您确定要重置用户 <span className="font-bold">{userToReset.name}</span> 的密码吗？
              </p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">新密码</label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? "隐藏" : "显示"}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    密码必须包含大小写字母、数字和特殊字符，且长度不少于8位
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                  <Button variant="outline" onClick={() => setShowResetPasswordModal(false)}>
                    取消
                  </Button>
                  <Button
                    className="bg-[#f5a623] hover:bg-[#f5a623]/90"
                    onClick={() => {
                      setSuccessMessage(`已成功重置用户 ${userToReset.name} 的密码！`)
                      setShowSuccessToast(true)
                      setShowResetPasswordModal(false)
                      setTimeout(() => {
                        setShowSuccessToast(false)
                      }, 3000)
                    }}
                  >
                    确认重置
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 删除用户确认模态框 */}
        {showDeleteUserModal && userToDelete && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white rounded-lg w-full max-w-md p-6 relative max-h-[90vh] overflow-y-auto mx-4">
              <button
                onClick={() => setShowDeleteUserModal(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-xl font-bold mb-4">删除用户</h2>
              <p className="mb-4">
                您确定要删除用户 <span className="font-bold">{userToDelete.name}</span> 吗？此操作不可撤销。
              </p>

              <div className="flex justify-end space-x-3 pt-4">
                <Button variant="outline" onClick={() => setShowDeleteUserModal(false)}>
                  取消
                </Button>
                <Button
                  className="bg-red-600 hover:bg-red-700 text-white"
                  onClick={() => {
                    setSuccessMessage(`已成功删除用户 ${userToDelete.name}！`)
                    setShowSuccessToast(true)
                    setShowDeleteUserModal(false)
                    setTimeout(() => {
                      setShowSuccessToast(false)
                    }, 3000)
                  }}
                >
                  确认删除
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 角色权限设置模态框 */}
        {showRolePermissionModal && roleToSetPermission && (
          <RolePermissionModal
            roleToSetPermission={roleToSetPermission}
            setShowRolePermissionModal={setShowRolePermissionModal}
            setSuccessMessage={setSuccessMessage}
            setShowSuccessToast={setShowSuccessToast}
            API_BASE_URL={API_BASE_URL}
          />
        )}

        {/* 旧的角色权限设置模态框 - 已废弃 */}
        {false && showRolePermissionModal && roleToSetPermission && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white rounded-lg w-full max-w-3xl p-6 relative max-h-[80vh] overflow-y-auto">
              <button
                onClick={() => setShowRolePermissionModal(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-xl font-bold mb-4">权限设置 - {roleToSetPermission.name}</h2>

              <div className="space-y-6">
                {/* 首页模块 */}
                <div>
                  <h3 className="text-md font-medium mb-2 bg-gray-50 p-2 rounded">首页</h3>
                  <div className="grid grid-cols-2 gap-2 pl-2">
                    <div className="flex items-center">
                      <Checkbox id="home-view" defaultChecked />
                      <label htmlFor="home-view" className="ml-2 text-sm">
                        查看首页
                      </label>
                    </div>
                  </div>
                </div>

                {/* 家族专题模块 */}
                <div>
                  <h3 className="text-md font-medium mb-2 bg-gray-50 p-2 rounded">家族专题</h3>
                  <div className="grid grid-cols-2 gap-2 pl-2">
                    <div className="flex items-center">
                      <Checkbox id="family-view" defaultChecked />
                      <label htmlFor="family-view" className="ml-2 text-sm">
                        查看家族专题
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="family-comment" defaultChecked />
                      <label htmlFor="family-comment" className="ml-2 text-sm">
                        发表评论
                      </label>
                    </div>
                  </div>
                </div>

                {/* 个人专题模块 */}
                <div>
                  <h3 className="text-md font-medium mb-2 bg-gray-50 p-2 rounded">个人专题</h3>
                  <div className="grid grid-cols-2 gap-2 pl-2">
                    <div className="flex items-center">
                      <Checkbox id="personal-view" defaultChecked />
                      <label htmlFor="personal-view" className="ml-2 text-sm">
                        查看个人专题
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="personal-comment" defaultChecked />
                      <label htmlFor="personal-comment" className="ml-2 text-sm">
                        发表评论
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="personal-ai" defaultChecked />
                      <label htmlFor="personal-ai" className="ml-2 text-sm">
                        使用AI助手
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="personal-download" defaultChecked />
                      <label htmlFor="personal-download" className="ml-2 text-sm">
                        下载资料
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="personal-manage-materials" />
                      <label htmlFor="personal-manage-materials" className="ml-2 text-sm">
                        管理相关资料
                      </label>
                    </div>
                  </div>
                </div>

                {/* 知识库模块 */}
                <div>
                  <h3 className="text-md font-medium mb-2 bg-gray-50 p-2 rounded">知识库</h3>
                  <div className="grid grid-cols-2 gap-2 pl-2">
                    <div className="flex items-center">
                      <Checkbox id="knowledge-view" defaultChecked />
                      <label htmlFor="knowledge-view" className="ml-2 text-sm">
                        查看知识库
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="knowledge-create" defaultChecked />
                      <label htmlFor="knowledge-create" className="ml-2 text-sm">
                        创建知识库
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="knowledge-edit" defaultChecked />
                      <label htmlFor="knowledge-edit" className="ml-2 text-sm">
                        编辑知识库
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="knowledge-delete" defaultChecked />
                      <label htmlFor="knowledge-delete" className="ml-2 text-sm">
                        删除知识库
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="knowledge-upload" defaultChecked />
                      <label htmlFor="knowledge-upload" className="ml-2 text-sm">
                        上传文件
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="knowledge-review" />
                      <label htmlFor="knowledge-review" className="ml-2 text-sm">
                        审核文件
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="knowledge-ai" defaultChecked />
                      <label htmlFor="knowledge-ai" className="ml-2 text-sm">
                        使用AI分析
                      </label>
                    </div>
                  </div>
                </div>

                {/* 数据挖掘模块 */}
                <div>
                  <h3 className="text-md font-medium mb-2 bg-gray-50 p-2 rounded">数据查询</h3>
                  <div className="grid grid-cols-2 gap-2 pl-2">
                    <div className="flex items-center">
                      <Checkbox id="data-mining-view" defaultChecked />
                      <label htmlFor="data-mining-view" className="ml-2 text-sm">
                        查看数据查询页面
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="data-mining-query" defaultChecked />
                      <label htmlFor="data-mining-query" className="ml-2 text-sm">
                        执行数据查询
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="data-mining-export" defaultChecked />
                      <label htmlFor="data-mining-export" className="ml-2 text-sm">
                        导出数据
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="data-mining-ai" defaultChecked />
                      <label htmlFor="data-mining-ai" className="ml-2 text-sm">
                        使用AI查询助手
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="data-mining-datasource" />
                      <label htmlFor="data-mining-datasource" className="ml-2 text-sm">
                        管理数据源
                      </label>
                    </div>
                  </div>
                </div>

                {/* AI助手模块 */}
                <div>
                  <h3 className="text-md font-medium mb-2 bg-gray-50 p-2 rounded">AI研究助手</h3>
                  <div className="grid grid-cols-2 gap-2 pl-2">
                    <div className="flex items-center">
                      <Checkbox id="ai-assistant-view" defaultChecked />
                      <label htmlFor="ai-assistant-view" className="ml-2 text-sm">
                        查看AI研究助手页面
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="ai-assistant-use" defaultChecked />
                      <label htmlFor="ai-assistant-use" className="ml-2 text-sm">
                        使用AI研究助手
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="ai-assistant-history" defaultChecked />
                      <label htmlFor="ai-assistant-history" className="ml-2 text-sm">
                        查看对话历史
                      </label>
                    </div>
                    <div className="flex items-center">
                      <Checkbox id="ai-assistant-export" defaultChecked />
                      <label htmlFor="ai-assistant-export" className="ml-2 text-sm">
                        导出对话记录
                      </label>
                    </div>
                  </div>
                </div>

                {/* 权限模块 */}
                <div>
                  <h3 className="text-md font-medium mb-2 bg-gray-50 p-2 rounded">系统权限</h3>
                  <div className="grid grid-cols-2 gap-2 pl-2">
                    {/* 系统管理权限 */}
                    <div className="col-span-2">
                      <h4 className="text-sm font-medium mb-1 text-gray-700">系统管理</h4>
                      <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                        <PermissionCheckbox code="system:access" label="访问系统管理" />
                        <PermissionCheckbox code="user:manage" label="用户管理" />
                        <PermissionCheckbox code="permission:manage" label="权限管理" />
                        <PermissionCheckbox code="role:manage" label="角色管理" />
                        <PermissionCheckbox code="security:manage" label="安全管理" />
                        <PermissionCheckbox code="ai:manage" label="AI管理" />
                      </div>
                    </div>

                    {/* 知识库权限 */}
                    <div className="col-span-2">
                      <h4 className="text-sm font-medium mb-1 text-gray-700">知识库</h4>
                      <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                        <PermissionCheckbox code="knowledge:access" label="访问知识库" />
                        <PermissionCheckbox code="knowledge:create_user" label="创建用户知识库" />
                        <PermissionCheckbox code="knowledge:create_system" label="创建系统知识库" />
                        <PermissionCheckbox code="file:upload" label="上传文件" />
                        <PermissionCheckbox code="file:review" label="审核文件" />
                      </div>
                    </div>

                    {/* 个人专题权限 */}
                    <div className="col-span-2">
                      <h4 className="text-sm font-medium mb-1 text-gray-700">个人专题</h4>
                      <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                        <PermissionCheckbox code="personal:access" label="访问个人专题" />
                        <PermissionCheckbox code="personal:ai_use" label="使用AI助手" />
                        <PermissionCheckbox code="personal:manage_materials" label="管理个人资料" />
                      </div>
                    </div>

                    {/* 数据查询权限 */}
                    <div className="col-span-2">
                      <h4 className="text-sm font-medium mb-1 text-gray-700">数据查询</h4>
                      <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                        <PermissionCheckbox code="data:access" label="访问数据查询" />
                        <PermissionCheckbox code="data:ai_query" label="使用AI查询" />
                      </div>
                    </div>

                    {/* 活动管理权限 */}
                    <div className="col-span-2">
                      <h4 className="text-sm font-medium mb-1 text-gray-700">活动管理</h4>
                      <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                        <PermissionCheckbox code="activity:view" label="查看活动" />
                        <PermissionCheckbox code="activity:manage" label="管理活动" />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <Button variant="outline" onClick={() => setShowRolePermissionModal(false)}>
                    取消
                  </Button>
                  <Button
                    className="bg-[#f5a623] hover:bg-[#f5a623]/90"
                    onClick={async () => {
                      setLoading(true);

                      try {
                        // 先检查角色是否存在
                        try {
                          // 尝试获取角色信息
                          const roleResponse = await axios.get(`${API_BASE_URL}/roles/${roleToSetPermission.id}`, {
                            headers: {
                              'Authorization': `Bearer ${localStorage.getItem('hefamily_token')}`
                            }
                          });

                          console.log('角色信息:', roleResponse.data);

                          // 如果角色不存在，API会返回404错误，不会执行到这里
                        } catch (roleError) {
                          if (roleError.response && roleError.response.status === 404) {
                            // 角色不存在
                            toast({
                              title: "角色不存在",
                              description: `角色ID=${roleToSetPermission.id}在系统中不存在，无法设置权限`,
                              variant: "destructive"
                            });
                            setLoading(false);
                            setShowRolePermissionModal(false);
                            return;
                          }
                          // 其他错误继续执行
                        }

                        // 创建一个函数来获取所有选中的权限复选框
                        const getSelectedPermissions = () => {
                          // 使用一个临时数组来存储选中的权限代码
                          const selectedCodes: string[] = [];

                          // 从全局权限复选框列表中获取选中的权限
                          if (typeof window !== 'undefined' && window.permissionCheckboxes) {
                            Object.entries(window.permissionCheckboxes).forEach(([code, checkbox]) => {
                              if (checkbox.isChecked()) {
                                selectedCodes.push(code);
                              }
                            });
                          }

                          // 如果全局列表为空，尝试从DOM中获取
                          if (selectedCodes.length === 0) {
                            // 查询所有权限复选框
                            const checkboxes = document.querySelectorAll('input[name="permission-checkbox"]');

                            // 遍历所有复选框，找出选中的
                            checkboxes.forEach((checkbox: HTMLInputElement) => {
                              if (checkbox.checked) {
                                const permissionCode = checkbox.getAttribute('data-permission-code');
                                if (permissionCode) {
                                  selectedCodes.push(permissionCode);
                                }
                              }
                            });
                          }

                          return selectedCodes;
                        };

                        // 获取选中的权限代码
                        const permissionCodes = getSelectedPermissions();
                        console.log('选中的权限代码:', permissionCodes);

                        // 获取所有权限列表
                        const allPermissions = await getAllPermissions();
                        console.log('所有权限:', allPermissions);

                        // 找出选中权限代码对应的权限ID
                        const selectedPermissionIds = allPermissions
                          .filter(permission => permissionCodes.includes(permission.code))
                          .map(permission => permission.id);

                        console.log('选中的权限ID:', selectedPermissionIds);

                        // 如果没有选中任何权限，但有权限代码，可能是权限代码与数据库中的不匹配
                        if (permissionCodes.length > 0 && selectedPermissionIds.length === 0) {
                          console.warn('警告：选中的权限代码无法匹配到权限ID，请检查权限代码是否正确');

                          // 输出所有权限代码和选中的权限代码，帮助调试
                          console.log('所有权限代码:', allPermissions.map(p => p.code));
                          console.log('选中的权限代码:', permissionCodes);

                          toast({
                            title: "警告",
                            description: "选中的权限无法匹配到系统权限，请联系管理员",
                            variant: "destructive"
                          });
                        }

                        // 调用API更新角色权限
                        const result = await setRolePermissions(
                          parseInt(roleToSetPermission.id),
                          selectedPermissionIds
                        );

                        if (result.success) {
                          setSuccessMessage(`已成功更新角色 ${roleToSetPermission.name} 的权限设置！`);
                          setShowSuccessToast(true);
                          setShowRolePermissionModal(false);
                          setTimeout(() => {
                            setShowSuccessToast(false);
                          }, 3000);
                        } else {
                          // 显示错误消息
                          toast({
                            title: "更新失败",
                            description: result.message || "更新角色权限失败，请稍后重试",
                            variant: "destructive"
                          });
                        }
                      } catch (error) {
                        console.error('更新角色权限失败:', error);
                        toast({
                          title: "更新失败",
                          description: "更新角色权限失败，请稍后重试",
                          variant: "destructive"
                        });
                      } finally {
                        setLoading(false);
                      }
                    }}
                    disabled={loading}
                  >
                    {loading ? "保存中..." : "保存"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 删除角色确认模态框 */}
        {showDeleteRoleModal && roleToDelete && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white rounded-lg w-full max-w-md p-6 relative max-h-[90vh] overflow-y-auto mx-4">
              <button
                onClick={() => setShowDeleteRoleModal(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-xl font-bold mb-4">删除角色</h2>
              <p className="mb-4">
                您确定要删除角色 <span className="font-bold">{roleToDelete.name}</span> 吗？此操作不可撤销。
              </p>

              <div className="flex justify-end space-x-3 pt-4">
                <Button variant="outline" onClick={() => setShowDeleteRoleModal(false)}>
                  取消
                </Button>
                <Button
                  className="bg-red-600 hover:bg-red-700 text-white"
                  onClick={async () => {
                    try {
                      setLoading(true);

                      // 获取token
                      const token = localStorage.getItem('hefamily_token');
                      if (!token) {
                        toast({
                          title: "认证失败",
                          description: "请先登录以执行此操作",
                          variant: "destructive"
                        });
                        return;
                      }

                      // 调用API删除角色
                      logger.debug(`删除角色: ID=${roleToDelete.id}, 名称=${roleToDelete.name}`);

                      const response = await axios.delete(`${API_BASE_URL}/roles/${roleToDelete.id}`, {
                        headers: {
                          'Authorization': `Bearer ${token}`
                        }
                      });

                      logger.debug('删除角色响应:', response.data);

                      if (response.data && response.data.success) {
                        // 从本地状态中移除该角色
                        setRoles(roles.filter(role => role.id !== roleToDelete.id));

                        // 显示成功消息
                        setSuccessMessage(`已成功删除角色 ${roleToDelete.name}！`);
                        setShowSuccessToast(true);
                        setTimeout(() => {
                          setShowSuccessToast(false);
                        }, 3000);
                      } else {
                        // 显示错误消息
                        toast({
                          title: "删除失败",
                          description: response.data?.message || "删除角色失败，请稍后重试",
                          variant: "destructive"
                        });
                      }
                    } catch (error) {
                      logger.error('删除角色失败:', error);

                      // 显示错误消息
                      let errorMessage = "删除角色失败，请稍后重试";

                      // 如果是因为角色正在被用户使用而无法删除
                      if (error.response && error.response.status === 400) {
                        errorMessage = error.response.data?.message || errorMessage;
                      }

                      toast({
                        title: "删除失败",
                        description: errorMessage,
                        variant: "destructive"
                      });
                    } finally {
                      setLoading(false);
                      setShowDeleteRoleModal(false);
                    }
                  }}
                  disabled={loading}
                >
                  {loading ? "删除中..." : "确认删除"}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* AI代理查看模态框 */}
        {showAiAgentViewModal && viewingAiAgent && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white rounded-lg w-full max-w-2xl p-6 relative max-h-[90vh] overflow-y-auto mx-4">
              <button
                onClick={() => setShowAiAgentViewModal(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-xl font-bold mb-4">{viewingAiAgent.name}</h2>

              <div className="space-y-4">
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-1">类型</div>
                  <div className="text-sm text-gray-900">
                    {viewingAiAgent.type === "personal"
                      ? "个人专题助手"
                      : viewingAiAgent.type === "data-query"
                        ? "数据查询助手"
                        : viewingAiAgent.type === "knowledge-file"
                          ? "知识库文件同步助手"
                          : "AI研究助手"}
                  </div>
                </div>

                <div>
                  <div className="text-sm font-medium text-gray-700 mb-1">API地址</div>
                  <div className="text-sm text-gray-900 break-all">{viewingAiAgent.apiEndpoint}</div>
                </div>

                <ApiKeyDisplay apiKey={viewingAiAgent.apiKey} />

                {viewingAiAgent.type === "knowledge-file" ? (
                  <>
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-1">系统知识库数据集ID</div>
                      <div className="text-sm text-gray-900 break-all">{viewingAiAgent.appId || "未设置"}</div>
                      <div className="text-xs text-gray-500 mt-1">用于系统知识库文件上传的Dify数据集ID（存储在appId字段）</div>
                    </div>

                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-1">用户知识库数据集ID</div>
                      <div className="text-sm text-gray-900 break-all">{viewingAiAgent.appCode || "未设置"}</div>
                      <div className="text-xs text-gray-500 mt-1">用于用户知识库文件上传的Dify数据集ID（存储在appCode字段）</div>
                    </div>
                  </>
                ) : (
                  <>
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-1">应用ID (AI平台应用ID)</div>
                      <div className="text-sm text-gray-900 break-all">{viewingAiAgent.appId || "未设置"}</div>
                    </div>

                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-1">应用代码 (AI平台应用代码)</div>
                      <div className="text-sm text-gray-900 break-all">{viewingAiAgent.appCode || "未设置"}</div>
                    </div>
                  </>
                )}

                {/* 知识库文件同步助手特有字段 */}
                {viewingAiAgent.type === "knowledge-file" && (
                  <>
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-1">系统知识库文件上传API路径</div>
                      <div className="text-sm text-gray-900 break-all">{viewingAiAgent.uploadApiPath || "未设置"}</div>
                      <div className="text-xs text-gray-500 mt-1">用于将系统知识库文件上传到AI平台的API路径，{"{datasetId}"}会被替换为系统知识库数据集ID</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-1">用户知识库文件上传API路径</div>
                      <div className="text-sm text-gray-900 break-all">{viewingAiAgent.analysisApiPath || "未设置"}</div>
                      <div className="text-xs text-gray-500 mt-1">用于将用户知识库文件上传到AI平台的API路径，{"{datasetId}"}会被替换为用户知识库数据集ID</div>
                    </div>
                  </>
                )}

                {/* 初始对话内容字段 - 除了知识库文件同步助手外的所有类型都有 */}
                {viewingAiAgent.type !== "knowledge-file" && (
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-1">初始对话内容</div>
                    <div className="text-sm text-gray-900">{viewingAiAgent.initialMessage || "未设置"}</div>
                  </div>
                )}

                <div>
                  <div className="text-sm font-medium text-gray-700 mb-1">描述</div>
                  <div className="text-sm text-gray-900">{viewingAiAgent.description}</div>
                </div>

                <div>
                  <div className="text-sm font-medium text-gray-700 mb-1">标签</div>
                  <div className="flex flex-wrap gap-1">
                    {viewingAiAgent.tags.map((tag, index) => (
                      <span key={`view-tag-${viewingAiAgent.id}-${index}`} className="px-2 py-0.5 text-xs bg-gray-100 rounded-full">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <div className="text-sm font-medium text-gray-700 mb-1">状态</div>
                  <div className="text-sm text-gray-900">{viewingAiAgent.status}</div>
                </div>

                <div>
                  <div className="text-sm font-medium text-gray-700 mb-1">最后更新</div>
                  <div className="text-sm text-gray-900">{viewingAiAgent.lastUpdated}</div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <Button variant="outline" onClick={() => setShowAiAgentViewModal(false)}>
                    关闭
                  </Button>
                  <Button
                    className="bg-[#f5a623] hover:bg-[#f5a623]/90"
                    onClick={() => {
                      setShowAiAgentViewModal(false)
                      openAiAgentModal(viewingAiAgent)
                    }}
                  >
                    编辑
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}



        {/* 成功提示 */}
        {showSuccessToast && (
          <div className="fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md z-50 animate-fade-in-up">
            <div className="flex">
              <div className="py-1">
                <svg
                  className="h-6 w-6 text-green-500 mr-4"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <p className="font-bold">成功</p>
                <p className="text-sm">{successMessage}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
