"use client"

import { useState, useRef } from 'react'
import { Upload, FileText, X, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { KnowledgeFileUploadProps, KnowledgeFileUploadFields } from './types'
import { FileAnalysisAssistant } from '@/components/ai'

/**
 * 知识库文件上传组件
 * 
 * 用于上传文件到知识库，支持AI分析生成摘要和详细描述
 */
export function KnowledgeFileUpload({ loading, knowledgeBases, onSubmit, onCancel }: KnowledgeFileUploadProps) {
  const [formData, setFormData] = useState<KnowledgeFileUploadFields>({
    knowledge_base_id: '',
    file: null as unknown as File,
    summary: '',
    detailed_description: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [fileName, setFileName] = useState('')
  const [fileSize, setFileSize] = useState(0)
  const [useAI, setUseAI] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理知识库选择
  const handleKnowledgeBaseChange = (value: string) => {
    setFormData({ ...formData, knowledge_base_id: value })
    
    // 清除错误
    if (errors.knowledge_base_id) {
      setErrors({ ...errors, knowledge_base_id: '' })
    }
  }

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setFormData({ ...formData, file })
      setFileName(file.name)
      setFileSize(file.size)
      
      // 清除错误
      if (errors.file) {
        setErrors({ ...errors, file: '' })
      }
    }
  }

  // 处理文本输入
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })
    
    // 清除错误
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' })
    }
  }

  // 处理AI分析完成
  const handleAnalysisComplete = (summary: string, detailedDescription: string) => {
    setFormData({
      ...formData,
      summary,
      detailed_description: detailedDescription
    })
  }

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    
    // 验证知识库
    if (!formData.knowledge_base_id) {
      newErrors.knowledge_base_id = '请选择知识库'
    }
    
    // 验证文件
    if (!formData.file) {
      newErrors.file = '请选择文件'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      onSubmit(formData)
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="knowledge_base_id" className="block text-sm font-medium text-gray-700 mb-1">
            选择知识库
          </Label>
          <Select
            value={formData.knowledge_base_id}
            onValueChange={handleKnowledgeBaseChange}
            disabled={loading}
          >
            <SelectTrigger className={errors.knowledge_base_id ? 'border-red-500' : ''}>
              <SelectValue placeholder="选择知识库" />
            </SelectTrigger>
            <SelectContent>
              {knowledgeBases.map((kb) => (
                <SelectItem key={kb.id} value={kb.id}>
                  {kb.name} ({kb.type})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.knowledge_base_id && <p className="mt-1 text-xs text-red-500">{errors.knowledge_base_id}</p>}
        </div>

        <div>
          <Label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-1">
            选择文件
          </Label>
          <div className="flex items-center gap-2">
            <Input
              id="file"
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
              accept=".pdf,.doc,.docx,.txt,.md"
              disabled={loading}
            />
            <Button
              type="button"
              variant="outline"
              className="flex items-center"
              onClick={() => fileInputRef.current?.click()}
              disabled={loading}
            >
              <Upload className="h-4 w-4 mr-2" />
              选择文件
            </Button>
            {fileName && (
              <div className="flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md flex-1">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-sm truncate">{fileName}</span>
                <span className="text-xs text-gray-500">({formatFileSize(fileSize)})</span>
                <button
                  type="button"
                  className="ml-auto text-gray-500 hover:text-gray-700"
                  onClick={() => {
                    setFormData({ ...formData, file: null as unknown as File })
                    setFileName('')
                    setFileSize(0)
                    if (fileInputRef.current) {
                      fileInputRef.current.value = ''
                    }
                  }}
                  disabled={loading}
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
          {errors.file && <p className="mt-1 text-xs text-red-500">{errors.file}</p>}
        </div>

        <div className="flex items-center gap-2 mt-4">
          <input
            type="checkbox"
            id="use-ai"
            checked={useAI}
            onChange={(e) => setUseAI(e.target.checked)}
            className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            disabled={loading}
          />
          <Label htmlFor="use-ai" className="text-sm font-medium text-gray-700">
            使用AI分析文件内容
          </Label>
        </div>

        {useAI ? (
          <div className="mt-4 border rounded-md p-4">
            <h3 className="text-sm font-medium mb-2">AI文件分析</h3>
            <FileAnalysisAssistant
              onAnalysisComplete={handleAnalysisComplete}
            />
          </div>
        ) : (
          <>
            <div>
              <Label htmlFor="summary" className="block text-sm font-medium text-gray-700 mb-1">
                文件摘要
              </Label>
              <Textarea
                id="summary"
                name="summary"
                value={formData.summary}
                onChange={handleTextChange}
                placeholder="请输入文件摘要"
                className="min-h-[100px]"
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="detailed_description" className="block text-sm font-medium text-gray-700 mb-1">
                详细描述
              </Label>
              <Textarea
                id="detailed_description"
                name="detailed_description"
                value={formData.detailed_description}
                onChange={handleTextChange}
                placeholder="请输入详细描述"
                className="min-h-[200px]"
                disabled={loading}
              />
            </div>
          </>
        )}

        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={loading}>
            取消
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                上传中...
              </>
            ) : (
              '上传文件'
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
