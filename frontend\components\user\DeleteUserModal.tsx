/**
 * 删除用户确认模态框组件
 * 
 * 用于确认删除用户操作
 */

import React from "react"
import { X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { DeleteUserModalProps } from "./types"

/**
 * 删除用户确认模态框组件
 * @param props 组件属性
 */
export const DeleteUserModal: React.FC<DeleteUserModalProps> = ({
  isOpen,
  onClose,
  onDelete,
  user
}) => {
  if (!isOpen || !user) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg w-full max-w-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">确认删除</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <p className="mb-6">
          您确定要删除用户 <strong>{user.name}</strong> 吗？此操作无法撤销。
        </p>

        <div className="flex justify-end gap-4">
          <Button
            variant="outline"
            onClick={onClose}
          >
            取消
          </Button>
          <Button
            onClick={onDelete}
            className="bg-red-600 hover:bg-red-700"
          >
            删除
          </Button>
        </div>
      </div>
    </div>
  )
}
