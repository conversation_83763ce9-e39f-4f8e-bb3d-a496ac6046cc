/**
 * 测试运行脚本
 * 
 * 用于运行所有测试或特定测试
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 获取命令行参数
const args = process.argv.slice(2);
const testType = args[0] || 'all'; // 默认运行所有测试

// 测试目录
const testsDir = path.join(__dirname);
const controllersDir = path.join(testsDir, 'controllers');
const modelsDir = path.join(testsDir, 'models');
const routesDir = path.join(testsDir, 'routes');
const middlewaresDir = path.join(testsDir, 'middlewares');
const unitDir = path.join(testsDir, 'unit');
const integrationDir = path.join(testsDir, 'integration');

// 确保测试目录存在
[controllersDir, modelsDir, routesDir, middlewaresDir, unitDir, integrationDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// 运行测试的函数
function runTests(pattern) {
  try {
    console.log(`运行测试: ${pattern}`);
    execSync(`npx jest ${pattern} --colors`, { stdio: 'inherit' });
  } catch (error) {
    console.error(`测试失败: ${error.message}`);
    process.exit(1);
  }
}

// 根据测试类型运行不同的测试
switch (testType) {
  case 'controllers':
    runTests('controllers');
    break;
  case 'models':
    runTests('models');
    break;
  case 'routes':
    runTests('routes');
    break;
  case 'middlewares':
    runTests('middlewares');
    break;
  case 'unit':
    runTests('unit');
    break;
  case 'integration':
    runTests('integration');
    break;
  case 'coverage':
    runTests('--coverage');
    break;
  case 'all':
  default:
    runTests('');
    break;
}

console.log('测试完成');
