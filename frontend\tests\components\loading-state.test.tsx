/**
 * LoadingState组件测试
 */

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { LoadingState } from '@/components/ui/loading-state'

describe('LoadingState组件', () => {
  // 测试加载状态
  test('应该正确渲染加载状态', () => {
    render(<LoadingState status="loading" loadingText="正在加载数据..." />)

    expect(screen.getByText('正在加载数据...')).toBeInTheDocument()
    expect(document.querySelector('svg.animate-spin')).toBeInTheDocument()
  })

  // 测试成功状态
  test('应该正确渲染成功状态', () => {
    render(<LoadingState status="success" successText="数据加载成功" />)

    expect(screen.getByText('数据加载成功')).toBeInTheDocument()
    expect(document.querySelector('svg.text-green-500')).toBeInTheDocument()
  })

  // 测试错误状态
  test('应该正确渲染错误状态', () => {
    render(<LoadingState status="error" errorText="加载失败，请重试" />)

    expect(screen.getByText('加载失败，请重试')).toBeInTheDocument()
    expect(document.querySelector('svg.text-red-500')).toBeInTheDocument()
  })

  // 测试空闲状态
  test('应该在空闲状态下不渲染任何内容', () => {
    const { container } = render(<LoadingState status="idle" />)

    expect(container.firstChild).toBeNull()
  })

  // 测试全页面模式
  test('应该支持全页面加载模式', () => {
    render(<LoadingState status="loading" fullPage={true} />)

    const overlay = document.querySelector('.fixed.inset-0')
    expect(overlay).toBeInTheDocument()
    expect(overlay).toHaveClass('bg-white/80')
  })

  // 测试不同尺寸
  test('应该支持不同尺寸', () => {
    const { rerender } = render(<LoadingState status="loading" size="sm" />)

    // 小尺寸
    expect(document.querySelector('svg.h-4.w-4')).toBeInTheDocument()

    // 中尺寸
    rerender(<LoadingState status="loading" size="md" />)
    expect(document.querySelector('svg.h-6.w-6')).toBeInTheDocument()

    // 大尺寸
    rerender(<LoadingState status="loading" size="lg" />)
    expect(document.querySelector('svg.h-8.w-8')).toBeInTheDocument()
  })

  // 测试重试功能
  test('应该支持重试功能', () => {
    const handleRetry = jest.fn()
    render(
      <LoadingState
        status="error"
        errorText="加载失败，请重试"
        onRetry={handleRetry}
      />
    )

    const retryButton = screen.getByText('重试')
    fireEvent.click(retryButton)

    expect(handleRetry).toHaveBeenCalledTimes(1)
  })

  // 测试自定义类名
  test('应该支持自定义类名', () => {
    render(
      <LoadingState
        status="loading"
        className="custom-class"
        data-testid="loading-state"
      />
    )

    const loadingState = screen.getByTestId('loading-state')
    expect(loadingState).toHaveClass('custom-class')
  })

  // 测试内联模式
  test('应该默认使用内联模式', () => {
    render(<LoadingState status="loading" data-testid="loading-state" />)

    const loadingState = screen.getByTestId('loading-state')
    expect(loadingState).toHaveClass('flex')
    expect(loadingState).toHaveClass('items-center')
    expect(document.querySelector('.fixed.inset-0')).not.toBeInTheDocument()
  })
})
