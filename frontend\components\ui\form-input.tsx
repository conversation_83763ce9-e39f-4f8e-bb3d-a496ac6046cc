"use client"

import { useState, useEffect, forwardRef } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"
import { AlertCircle, CheckCircle, Eye, EyeOff } from "lucide-react"
import { ValidationResult } from "@/lib/form-validation"

/**
 * 表单输入组件
 * 
 * 提供实时验证和反馈的输入框组件
 */
interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  validators?: ((value: string) => ValidationResult)[]
  showSuccessIndicator?: boolean
  className?: string
  labelClassName?: string
  inputClassName?: string
  validateOnBlur?: boolean
  validateOnChange?: boolean
  onValidationChange?: (isValid: boolean) => void
}

export const FormInput = forwardRef<HTMLInputElement, FormInputProps>(
  (
    {
      label,
      error,
      validators = [],
      showSuccessIndicator = true,
      className,
      labelClassName,
      inputClassName,
      validateOnBlur = true,
      validateOnChange = false,
      onValidationChange,
      ...props
    },
    ref
  ) => {
    const [value, setValue] = useState(props.value?.toString() || "")
    const [touched, setTouched] = useState(false)
    const [validationError, setValidationError] = useState<string | undefined>(error)
    const [showPassword, setShowPassword] = useState(false)
    const isPassword = props.type === "password"

    // 当外部error prop变化时更新内部状态
    useEffect(() => {
      setValidationError(error)
    }, [error])

    // 当value prop变化时更新内部状态
    useEffect(() => {
      if (props.value !== undefined) {
        setValue(props.value.toString())
      }
    }, [props.value])

    // 验证函数
    const validate = (valueToValidate: string) => {
      if (validators.length === 0) return true

      for (const validator of validators) {
        const result = validator(valueToValidate)
        if (!result.valid) {
          setValidationError(result.message)
          onValidationChange?.(false)
          return false
        }
      }

      setValidationError(undefined)
      onValidationChange?.(true)
      return true
    }

    // 处理输入变化
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value
      setValue(newValue)
      
      if (validateOnChange && touched) {
        validate(newValue)
      }
      
      props.onChange?.(e)
    }

    // 处理失去焦点
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setTouched(true)
      
      if (validateOnBlur) {
        validate(value)
      }
      
      props.onBlur?.(e)
    }

    // 切换密码可见性
    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword)
    }

    // 确定输入框类型
    const inputType = isPassword ? (showPassword ? "text" : "password") : props.type

    // 确定输入框状态
    const isValid = touched && !validationError && value !== ""
    const isInvalid = touched && !!validationError

    return (
      <div className={cn("space-y-2", className)}>
        {label && (
          <Label
            htmlFor={props.id}
            className={cn(
              "text-sm font-medium text-gray-700 dark:text-gray-300",
              isInvalid && "text-red-500 dark:text-red-400",
              labelClassName
            )}
          >
            {label}
          </Label>
        )}
        
        <div className="relative">
          <Input
            ref={ref}
            {...props}
            type={inputType}
            value={value}
            onChange={handleChange}
            onBlur={handleBlur}
            className={cn(
              "pr-10",
              isInvalid && "border-red-500 focus:ring-red-500 focus:border-red-500",
              isValid && showSuccessIndicator && "border-green-500 focus:ring-green-500 focus:border-green-500",
              inputClassName
            )}
            aria-invalid={isInvalid}
            aria-describedby={validationError ? `${props.id}-error` : undefined}
          />
          
          {isPassword && (
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          )}
          
          {!isPassword && isValid && showSuccessIndicator && (
            <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
          )}
          
          {isInvalid && (
            <AlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-red-500" />
          )}
        </div>
        
        {isInvalid && validationError && (
          <p
            id={`${props.id}-error`}
            className="text-sm text-red-500 dark:text-red-400 mt-1"
            role="alert"
          >
            {validationError}
          </p>
        )}
      </div>
    )
  }
)

FormInput.displayName = "FormInput"
