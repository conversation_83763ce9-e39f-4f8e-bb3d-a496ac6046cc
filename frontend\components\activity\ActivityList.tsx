/**
 * 活动列表组件
 * 
 * 用于展示活动列表
 */

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from '@/components/ui/use-toast'
import { LoadingState } from '@/components/ui/loading-state'
import { ActivityDetail } from './ActivityDetail'
import { ActivityForm } from './ActivityForm'

// 活动类型
export interface Activity {
  id: number
  title: string
  content: string
  location: string
  start_time: string
  end_time: string
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'
  created_by: number
  created_by_name: string
  created_at: string
  updated_at: string
  participants_count: number
}

// 活动列表组件
export const ActivityList = () => {
  const [activities, setActivities] = useState<Activity[]>([])
  const [filteredActivities, setFilteredActivities] = useState<Activity[]>([])
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // 加载活动列表
  useEffect(() => {
    const fetchActivities = async () => {
      setIsLoading(true)
      setError(null)
      try {
        // 模拟API调用
        const response = await fetch('/api/activities')
        const data = await response.json()
        
        if (!response.ok) {
          throw new Error(data.message || '加载活动失败')
        }
        
        setActivities(data.data || [])
        setFilteredActivities(data.data || [])
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载活动失败')
        toast({
          title: '加载失败',
          description: err instanceof Error ? err.message : '加载活动失败',
          variant: 'destructive'
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchActivities()
  }, [])

  // 过滤活动
  useEffect(() => {
    let filtered = activities
    
    // 按状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(activity => activity.status === statusFilter)
    }
    
    // 按搜索词过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(
        activity => 
          activity.title.toLowerCase().includes(term) || 
          activity.content.toLowerCase().includes(term) ||
          activity.location.toLowerCase().includes(term)
      )
    }
    
    setFilteredActivities(filtered)
  }, [activities, statusFilter, searchTerm])

  // 处理选择活动
  const handleSelectActivity = (activity: Activity) => {
    setSelectedActivity(activity)
    setIsCreating(false)
  }

  // 处理返回列表
  const handleBack = () => {
    setSelectedActivity(null)
    setIsCreating(false)
  }

  // 处理创建活动
  const handleCreateActivity = () => {
    setIsCreating(true)
    setSelectedActivity(null)
  }

  // 处理提交活动
  const handleSubmitActivity = async (activityData: Omit<Activity, 'id' | 'created_by' | 'created_by_name' | 'created_at' | 'updated_at' | 'participants_count'>) => {
    try {
      // 模拟API调用
      const response = await fetch('/api/activities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(activityData)
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.message || '创建活动失败')
      }
      
      // 添加新活动到列表
      const newActivity = data.data
      setActivities([newActivity, ...activities])
      
      // 返回列表
      setIsCreating(false)
      
      toast({
        title: '创建成功',
        description: '活动已成功创建'
      })
    } catch (err) {
      toast({
        title: '创建失败',
        description: err instanceof Error ? err.message : '创建活动失败',
        variant: 'destructive'
      })
    }
  }

  // 获取状态标签
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'upcoming':
        return '即将开始'
      case 'ongoing':
        return '进行中'
      case 'completed':
        return '已结束'
      case 'cancelled':
        return '已取消'
      default:
        return status
    }
  }

  // 渲染活动卡片
  const renderActivityCard = (activity: Activity) => (
    <Card 
      key={activity.id} 
      className="cursor-pointer hover:shadow-md transition-shadow"
      onClick={() => handleSelectActivity(activity)}
    >
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle>{activity.title}</CardTitle>
          <div className={`px-2 py-1 rounded text-xs ${
            activity.status === 'upcoming' ? 'bg-blue-100 text-blue-800' :
            activity.status === 'ongoing' ? 'bg-green-100 text-green-800' :
            activity.status === 'completed' ? 'bg-gray-100 text-gray-800' :
            'bg-red-100 text-red-800'
          }`}>
            {getStatusLabel(activity.status)}
          </div>
        </div>
        <CardDescription>
          地点: {activity.location}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="line-clamp-2 text-sm">{activity.content}</p>
        <div className="mt-2 text-xs text-gray-500">
          <div>开始时间: {new Date(activity.start_time).toLocaleString()}</div>
          <div>结束时间: {new Date(activity.end_time).toLocaleString()}</div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-xs text-gray-500">
          参与人数: {activity.participants_count}
        </div>
        <div className="text-xs text-gray-500">
          创建者: {activity.created_by_name}
        </div>
      </CardFooter>
    </Card>
  )

  return (
    <div className="container mx-auto p-4">
      {selectedActivity ? (
        <ActivityDetail 
          activity={selectedActivity} 
          onBack={handleBack} 
        />
      ) : isCreating ? (
        <div>
          <div className="flex items-center mb-6">
            <Button variant="ghost" onClick={handleBack} className="mr-2">返回</Button>
            <h1 className="text-3xl font-bold">创建活动</h1>
          </div>
          <ActivityForm onSubmit={handleSubmitActivity} />
        </div>
      ) : (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">活动列表</h1>
            <Button onClick={handleCreateActivity}>创建活动</Button>
          </div>
          
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <Input
              placeholder="搜索活动..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="md:w-1/2"
            />
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="md:w-1/4">
                <SelectValue placeholder="筛选状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="upcoming">即将开始</SelectItem>
                <SelectItem value="ongoing">进行中</SelectItem>
                <SelectItem value="completed">已结束</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {isLoading && (
            <LoadingState status="loading" loadingText="正在加载活动..." />
          )}
          
          {error && (
            <LoadingState status="error" errorText={error} />
          )}
          
          {!isLoading && !error && filteredActivities.length === 0 && (
            <div className="text-center p-8 text-gray-500">
              暂无活动
            </div>
          )}
          
          {filteredActivities.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredActivities.map(renderActivityCard)}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default ActivityList
