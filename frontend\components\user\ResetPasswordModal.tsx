/**
 * 重置密码确认对话框
 * 
 * 用于确认是否重置用户密码
 */

import React from "react"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { UserType } from "./types"

/**
 * 重置密码确认对话框属性
 */
interface ResetPasswordModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  user: UserType | null
}

/**
 * 重置密码确认对话框组件
 * @param props 组件属性
 */
export const ResetPasswordModal: React.FC<ResetPasswordModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  user
}) => {
  if (!user) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>重置用户密码</DialogTitle>
          <DialogDescription>
            您确定要重置用户 <span className="font-bold">{user.name}</span> 的密码吗？
            密码将被重置为默认密码：<span className="font-bold">123456</span>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex space-x-2 justify-end">
          <Button variant="outline" onClick={onClose}>取消</Button>
          <Button variant="default" onClick={onConfirm}>确认重置</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
