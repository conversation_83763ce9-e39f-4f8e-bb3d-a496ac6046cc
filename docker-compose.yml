version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: family-platform-frontend
    restart: always
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://hefuf.com
    depends_on:
      - backend
    networks:
      - family-platform-network
    volumes:
      - frontend-build:/app/.next
      - ./frontend/public:/app/public
      - ./logs/frontend:/app/logs

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: family-platform-backend
    restart: always
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=production
      - DB_HOST=db
      - DB_PORT=3306
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - DIFY_API_KEY=${DIFY_API_KEY}
      - DIFY_DATASET_ID=${DIFY_DATASET_ID}
      - SERVER_PORT=5001
    depends_on:
      - db
    networks:
      - family-platform-network
    volumes:
      - ./uploads:/app/uploads
      - ./logs/backend:/app/logs

  db:
    image: mysql:8.0
    container_name: family-platform-db
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_USER=${DB_USER}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    ports:
      - "3306:3306"
    networks:
      - family-platform-network
    volumes:
      - mysql-data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/conf:/etc/mysql/conf.d

networks:
  family-platform-network:
    driver: bridge

volumes:
  mysql-data:
  frontend-build:
