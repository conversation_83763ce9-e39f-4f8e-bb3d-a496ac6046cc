/**
 * 测试环境设置
 *
 * 用于配置测试环境和全局设置
 */

// 设置测试环境变量
process.env.NODE_ENV = 'test';

// 导入必要的模块
const { sequelize } = require('../src/models');

// 在所有测试开始前连接到测试数据库
beforeAll(async () => {
  try {
    await sequelize.authenticate();
    console.log('测试数据库连接成功');

    // 确保测试数据库表结构是最新的
    // 注意：在生产环境中应该使用迁移而不是sync
    // 这里使用force: false以避免删除现有数据
    await sequelize.sync({ force: false });
    console.log('测试数据库表同步完成');
  } catch (error) {
    console.error('无法连接到测试数据库:', error);
    console.error('错误详情:', error.message);
    if (error.original) {
      console.error('原始错误:', error.original.message);
    }
    process.exit(1);
  }
});

// 在所有测试结束后关闭数据库连接
afterAll(async () => {
  await sequelize.close();
  console.log('测试数据库连接已关闭');
});

// 在每个测试前创建事务
beforeEach(async () => {
  try {
    // 使用事务来隔离测试
    global.testTransaction = await sequelize.transaction();
    console.log('测试事务已创建');
  } catch (error) {
    console.error('无法创建测试事务:', error);
    console.error('错误详情:', error.message);
  }
});

// 在每个测试后回滚事务
afterEach(async () => {
  try {
    if (global.testTransaction) {
      await global.testTransaction.rollback();
      console.log('测试事务已回滚');
    }
  } catch (error) {
    console.error('无法回滚测试事务:', error);
    console.error('错误详情:', error.message);
  }
});

// 全局辅助函数
global.setupTestDatabase = async () => {
  try {
    // 创建测试数据库表
    await sequelize.sync({ force: false });
    console.log('测试数据库表设置完成');
    return true;
  } catch (error) {
    console.error('设置测试数据库失败:', error);
    console.error('错误详情:', error.message);
    return false;
  }
};

global.cleanupTestDatabase = async () => {
  try {
    // 清理测试数据
    // 这里我们不删除表，只是清空数据
    // 在实际测试中，应该使用事务来隔离测试数据
    console.log('测试数据库清理完成');
    return true;
  } catch (error) {
    console.error('清理测试数据库失败:', error);
    console.error('错误详情:', error.message);
    return false;
  }
};
