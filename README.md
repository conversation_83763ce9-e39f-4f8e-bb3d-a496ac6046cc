# 和富家族研究平台

和富家族研究平台是一个专注于家族历史研究的综合性平台，提供家族历史展示、个人专题研究、知识库管理、数据查询和AI研究助手等功能。

## 项目结构

项目采用前后端分离的架构：

```
project-root/
├── frontend/           # 前端应用 (Next.js)
│   ├── app/            # 页面和路由
│   ├── components/     # React 组件
│   ├── contexts/       # React 上下文
│   ├── hooks/          # 自定义 React 钩子
│   ├── lib/            # 工具函数和库
│   ├── public/         # 静态资源
│   ├── styles/         # CSS 样式
│   ├── types/          # TypeScript 类型定义
│   └── package.json    # 前端依赖
│
├── backend/            # 后端应用 (Node.js/Express)
│   ├── config/         # 配置文件
│   ├── src/            # 源代码
│   │   ├── controllers/  # 控制器
│   │   ├── models/     # 数据模型
│   │   ├── routes/     # API路由
│   │   ├── services/   # 业务逻辑
│   │   ├── utils/      # 工具函数
│   │   ├── middlewares/  # 中间件
│   │   └── app.js      # 应用入口
│   ├── tests/          # 测试文件
│   └── package.json    # 后端依赖
│
└── docs/               # 项目文档
    ├── system-description.md  # 系统说明文档
    ├── api-design.md          # API设计文档
    └── ...                    # 其他文档
```

## 开发指南

### 前端开发

1. 进入前端目录：
   ```
   cd frontend
   ```

2. 安装依赖：
   ```
   npm install
   ```

3. 启动开发服务器：
   ```
   npm run dev
   ```

### 后端开发

1. 进入后端目录：
   ```
   cd backend
   ```

2. 安装依赖：
   ```
   npm install
   ```

3. 创建环境变量文件：
   ```
   cp .env.example .env
   ```
   然后编辑 `.env` 文件，填入实际的配置值。

4. 启动开发服务器：
   ```
   npm run dev
   ```

## 部署指南

### 前端部署

1. 构建前端应用：
   ```
   cd frontend
   npm run build
   ```

2. 启动生产服务器：
   ```
   npm start
   ```

### 后端部署

1. 进入后端目录：
   ```
   cd backend
   ```

2. 安装生产依赖：
   ```
   npm install --production
   ```

3. 启动生产服务器：
   ```
   npm start
   ```

## 文档

详细的系统说明文档位于 `docs/system-description.md`。

## 许可证

[MIT](LICENSE)
