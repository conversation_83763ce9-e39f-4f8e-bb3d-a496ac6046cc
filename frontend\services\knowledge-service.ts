/**
 * 知识库服务
 *
 * 处理知识库的创建、查询、更新、删除等
 */

import apiService from './api-service'
import { logger, sanitizeData } from '@/utils/logger'

// 知识库类型
export interface KnowledgeBase {
  id: string
  name: string
  description: string
  type: 'system' | 'user'
  creator_id: string
  creator?: {
    id: string
    username: string
    email: string
  }
  file_count: number
  storage_size: number
  created_at: string
  updated_at: string
  hasAccess?: boolean // 添加访问权限标记
}

// 知识库访问权限类型
export interface KnowledgeBaseAccess {
  id: string
  knowledge_base_id: string
  user_id: string
  user?: {
    id: string
    username: string
    email: string
  }
  access_type: 'read' | 'write' | 'admin'
  created_at: string
  updated_at: string
}

// 知识库查询参数
export interface KnowledgeBaseQueryParams {
  page?: number
  limit?: number
  search?: string
  type?: 'system' | 'user'
}

// 创建知识库参数
export interface CreateKnowledgeBaseParams {
  name: string
  description: string
  type: 'system' | 'user'
}

// 更新知识库参数
export interface UpdateKnowledgeBaseParams {
  name?: string
  description?: string
}

// 添加知识库访问权限参数
export interface AddKnowledgeBaseAccessParams {
  user_id: string
  access_type: 'read' | 'write' | 'admin'
}

// 更新知识库访问权限参数
export interface UpdateKnowledgeBaseAccessParams {
  access_type: 'read' | 'write' | 'admin'
}

/**
 * 获取知识库列表
 * @param params 查询参数
 */
export const getKnowledgeBaseList = async (params?: KnowledgeBaseQueryParams): Promise<{
  knowledgeBases: KnowledgeBase[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}> => {
  const response = await apiService.get<{
    knowledgeBases: KnowledgeBase[]
    pagination: {
      total: number
      page: number
      limit: number
      totalPages: number
    }
  }>('/knowledge', params)

  return response
}

/**
 * 获取我的知识库列表
 * @param params 查询参数
 */
export const getMyKnowledgeBaseList = async (params?: KnowledgeBaseQueryParams): Promise<{
  knowledgeBases: KnowledgeBase[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}> => {
  // 使用 type=user 参数获取用户知识库
  const queryParams = { ...params, type: 'user' };

  const response = await apiService.get<{
    knowledgeBases: KnowledgeBase[]
    pagination: {
      total: number
      page: number
      limit: number
      totalPages: number
    }
  }>('/knowledge', queryParams)

  return response
}

/**
 * 获取知识库详情
 * @param id 知识库ID
 */
export const getKnowledgeBaseById = async (id: string): Promise<KnowledgeBase> => {
  return await apiService.get<KnowledgeBase>(`/knowledge/${id}`)
}

/**
 * 创建知识库
 * @param params 创建参数
 */
export const createKnowledgeBase = async (params: CreateKnowledgeBaseParams): Promise<KnowledgeBase> => {
  return await apiService.post<KnowledgeBase>('/knowledge', params)
}

/**
 * 更新知识库
 * @param id 知识库ID
 * @param params 更新参数
 */
export const updateKnowledgeBase = async (id: string, params: UpdateKnowledgeBaseParams): Promise<KnowledgeBase> => {
  return await apiService.put<KnowledgeBase>(`/knowledge/${id}`, params)
}

/**
 * 删除知识库
 * @param id 知识库ID
 */
export const deleteKnowledgeBase = async (id: string): Promise<{ message: string }> => {
  return await apiService.del<{ message: string }>(`/knowledge/${id}`)
}

/**
 * 获取知识库访问权限列表
 * @param id 知识库ID
 */
export const getKnowledgeBaseAccess = async (id: string): Promise<KnowledgeBaseAccess[]> => {
  return await apiService.get<KnowledgeBaseAccess[]>(`/knowledge/${id}/access`)
}

/**
 * 添加知识库访问权限
 * @param id 知识库ID
 * @param params 添加参数
 */
export const addKnowledgeBaseAccess = async (id: string, params: AddKnowledgeBaseAccessParams): Promise<KnowledgeBaseAccess> => {
  return await apiService.post<KnowledgeBaseAccess>(`/knowledge/${id}/access`, params)
}

/**
 * 更新知识库访问权限
 * @param id 知识库ID
 * @param accessId 访问权限ID
 * @param params 更新参数
 */
export const updateKnowledgeBaseAccess = async (
  id: string,
  accessId: string,
  params: UpdateKnowledgeBaseAccessParams
): Promise<KnowledgeBaseAccess> => {
  return await apiService.put<KnowledgeBaseAccess>(`/knowledge/${id}/access/${accessId}`, params)
}

/**
 * 删除知识库访问权限
 * @param id 知识库ID
 * @param accessId 访问权限ID
 */
export const deleteKnowledgeBaseAccess = async (id: string, accessId: string): Promise<{ message: string }> => {
  return await apiService.del<{ message: string }>(`/knowledge/${id}/access/${accessId}`)
}

/**
 * 申请访问知识库
 * @param knowledgeBaseId 知识库ID
 * @param reason 申请理由
 */
export const requestKnowledgeBaseAccess = async (knowledgeBaseId: string, reason: string): Promise<any> => {
  logger.debug('知识库服务 - 申请访问知识库:', { knowledgeBaseId, reason })

  return new Promise((resolve, reject) => {
    try {
      // 获取token
      const token = localStorage.getItem('hefamily_token')
      logger.debug('知识库服务 - 获取到token:', token ? '是' : '否')

      // 使用XMLHttpRequest
      const xhr = new XMLHttpRequest()
      xhr.open('POST', '/api/knowledge-access-requests', true)
      xhr.setRequestHeader('Content-Type', 'application/json')

      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`)
        logger.debug('知识库服务 - 已设置Authorization头')
      }

      xhr.onreadystatechange = function() {
        logger.debug('知识库服务 - XHR状态变化:', xhr.readyState, xhr.status)

        if (xhr.readyState === 4) {
          logger.debug('知识库服务 - XHR完成, 状态码:', xhr.status)
          logger.debug('知识库服务 - 响应头:', xhr.getAllResponseHeaders())

          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const data = JSON.parse(xhr.responseText)
              logger.debug('知识库服务 - 访问申请API响应数据:', sanitizeData(data))
              resolve(data)
            } catch (e) {
              logger.error('知识库服务 - 解析响应JSON失败:', sanitizeData(e))
              logger.debug('知识库服务 - 原始响应:', xhr.responseText)
              reject(new Error('解析响应失败'))
            }
          } else {
            logger.error('知识库服务 - 请求失败, 状态码:', xhr.status)
            try {
              const errorData = JSON.parse(xhr.responseText)
              logger.error('知识库服务 - 错误响应:', sanitizeData(errorData))
              reject(errorData)
            } catch (e) {
              logger.error('知识库服务 - 解析错误响应失败:', sanitizeData(e))
              reject(new Error(`请求失败, 状态码: ${xhr.status}`))
            }
          }
        }
      }

      xhr.onerror = function(e) {
        logger.error('知识库服务 - XHR错误:', sanitizeData(e))
        reject(new Error('网络错误'))
      }

      const requestData = JSON.stringify({
        knowledge_base_id: knowledgeBaseId,
        reason
      })

      logger.debug('知识库服务 - 发送请求数据:', requestData)
      xhr.send(requestData)
    } catch (error) {
      logger.error('知识库服务 - 申请访问知识库失败:', sanitizeData(error))
      reject(error)
    }
  })
}

/**
 * 获取知识库访问申请列表
 * @param knowledgeBaseId 知识库ID
 * @param params 查询参数
 */
export const getKnowledgeBaseAccessRequests = async (knowledgeBaseId: string, params?: {
  page?: number
  limit?: number
  status?: 'pending' | 'approved' | 'rejected'
}): Promise<{
  accessRequests: any[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}> => {
  logger.debug('知识库服务 - 获取知识库访问申请列表:', { knowledgeBaseId, params });
  return await apiService.get(`/knowledge-access-requests/knowledge-base/${knowledgeBaseId}`, params)
}

/**
 * 审核知识库访问申请
 * @param requestId 申请ID
 * @param status 审核状态
 * @param accessType 访问类型（仅当批准时需要）
 */
export const reviewAccessRequest = async (
  requestId: string,
  status: 'approved' | 'rejected',
  accessType?: 'read' | 'write' | 'admin'
): Promise<any> => {
  logger.debug('知识库服务 - 审核访问申请:', { requestId, status, accessType });

  const params: any = { status }
  if (status === 'approved') {
    params.access_type = accessType || 'read'
  }

  try {
    // 使用XMLHttpRequest
    return new Promise((resolve, reject) => {
      const token = localStorage.getItem('hefamily_token');
      logger.debug('知识库服务 - 获取到token:', token ? '是' : '否');

      const xhr = new XMLHttpRequest();
      xhr.open('PUT', `/api/knowledge-access-requests/${requestId}/review`, true);
      xhr.setRequestHeader('Content-Type', 'application/json');

      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        logger.debug('知识库服务 - 已设置Authorization头');
      }

      xhr.onreadystatechange = function() {
        logger.debug('知识库服务 - XHR状态变化:', xhr.readyState, xhr.status);

        if (xhr.readyState === 4) {
          logger.debug('知识库服务 - XHR完成, 状态码:', xhr.status);
          logger.debug('知识库服务 - 响应头:', xhr.getAllResponseHeaders());

          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const data = JSON.parse(xhr.responseText);
              logger.debug('知识库服务 - 审核访问申请API响应数据:', sanitizeData(data));
              resolve(data);
            } catch (e) {
              logger.error('知识库服务 - 解析响应JSON失败:', sanitizeData(e));
              logger.debug('知识库服务 - 原始响应:', xhr.responseText);
              reject(new Error('解析响应失败'));
            }
          } else {
            logger.error('知识库服务 - 请求失败, 状态码:', xhr.status);
            try {
              const errorData = JSON.parse(xhr.responseText);
              logger.error('知识库服务 - 错误响应:', sanitizeData(errorData));
              reject(errorData);
            } catch (e) {
              logger.error('知识库服务 - 解析错误响应失败:', sanitizeData(e));
              reject(new Error(`请求失败, 状态码: ${xhr.status}`));
            }
          }
        }
      };

      xhr.onerror = function(e) {
        logger.error('知识库服务 - XHR错误:', sanitizeData(e));
        reject(new Error('网络错误'));
      };

      const requestData = JSON.stringify(params);
      logger.debug('知识库服务 - 发送请求数据:', requestData);
      xhr.send(requestData);
    });
  } catch (error) {
    logger.error('知识库服务 - 审核访问申请失败:', sanitizeData(error));
    throw error;
  }
}

export default {
  getKnowledgeBaseList,
  getMyKnowledgeBaseList,
  getKnowledgeBaseById,
  createKnowledgeBase,
  updateKnowledgeBase,
  deleteKnowledgeBase,
  getKnowledgeBaseAccess,
  addKnowledgeBaseAccess,
  updateKnowledgeBaseAccess,
  deleteKnowledgeBaseAccess,
  requestKnowledgeBaseAccess,
  getKnowledgeBaseAccessRequests,
  reviewAccessRequest
}
