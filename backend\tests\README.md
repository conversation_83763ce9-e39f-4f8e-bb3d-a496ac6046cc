# 和富家族研究平台测试文档

## 测试概述

本项目采用Jest作为测试框架，对后端API进行全面测试，包括单元测试、集成测试和端到端测试。测试覆盖了所有控制器、模型、中间件和工具函数。

## 测试目录结构

```
tests/
├── controllers/       # 控制器测试
├── models/            # 模型测试
├── routes/            # 路由测试
├── middlewares/       # 中间件测试
├── unit/              # 单元测试
├── integration/       # 集成测试
├── fixtures/          # 测试数据和文件
├── utils/             # 测试辅助工具
├── setup.js           # 测试环境设置
├── run-tests.js       # 测试运行脚本
└── README.md          # 测试文档
```

## 测试类型

### 单元测试

单元测试主要测试独立的函数和方法，确保它们在隔离环境中正确工作。单元测试位于`tests/unit/`目录下。

### 集成测试

集成测试测试多个组件之间的交互，确保它们能够协同工作。集成测试位于`tests/integration/`目录下。

### 控制器测试

控制器测试测试API端点的功能，包括请求处理、响应格式和错误处理。控制器测试位于`tests/controllers/`目录下。

### 模型测试

模型测试测试数据模型的功能，包括验证、关联和方法。模型测试位于`tests/models/`目录下。

### 中间件测试

中间件测试测试Express中间件的功能，包括认证、权限检查和请求处理。中间件测试位于`tests/middlewares/`目录下。

## 测试覆盖的功能

1. **用户管理**
   - 用户注册
   - 用户登录
   - 获取用户信息
   - 更新用户信息
   - 修改密码
   - 管理员获取所有用户
   - 管理员获取特定用户
   - 管理员更新用户
   - 管理员删除用户

2. **知识库管理**
   - 创建知识库
   - 获取知识库列表
   - 获取知识库详情
   - 更新知识库
   - 删除知识库
   - 知识库访问权限管理

3. **文件管理**
   - 上传文件
   - 获取文件列表
   - 获取知识库文件列表
   - 获取文件详情
   - 下载文件
   - 审核文件
   - 分析文件
   - 删除文件

4. **活动管理**
   - 获取活动列表
   - 获取活动详情
   - 创建活动
   - 上传活动图片
   - 更新活动
   - 删除活动
   - 活动附件管理

5. **评论管理**
   - 获取主题评论列表
   - 获取评论列表
   - 创建评论
   - 审核评论
   - 删除评论

6. **通知管理**
   - 获取当前用户的通知列表
   - 获取当前用户的未读通知数量
   - 标记通知为已读
   - 标记所有通知为已读
   - 删除通知
   - 删除所有通知

7. **角色和权限管理**
   - 获取角色列表
   - 获取角色详情
   - 创建角色
   - 更新角色
   - 删除角色
   - 获取角色权限
   - 更新角色权限
   - 获取权限列表
   - 获取权限详情
   - 获取权限模块列表

8. **系统配置管理**
   - 获取系统配置
   - 更新系统配置
   - 获取系统统计信息

9. **AI助手管理**
   - 获取AI助手列表
   - 获取AI助手详情
   - 更新AI助手
   - 个人专题助手查询
   - 数据查询助手查询
   - 设置数据查询助手知识库
   - 获取AI助手对话历史
   - 清除AI助手对话历史

## 运行测试

### 运行所有测试

```bash
npm test
```

### 运行特定类型的测试

```bash
# 运行控制器测试
node tests/run-tests.js controllers

# 运行模型测试
node tests/run-tests.js models

# 运行中间件测试
node tests/run-tests.js middlewares

# 运行单元测试
node tests/run-tests.js unit

# 运行集成测试
node tests/run-tests.js integration

# 运行测试并生成覆盖率报告
node tests/run-tests.js coverage
```

## 测试环境

测试环境使用SQLite内存数据库，以便快速运行测试并避免影响生产数据库。测试环境的配置位于`.env.test`文件中。

## 测试辅助工具

测试辅助工具位于`tests/utils/`目录下，包括创建测试用户、生成测试令牌等功能。这些工具可以在测试中重复使用，以减少重复代码。

## 测试覆盖率

测试覆盖率报告可以通过运行以下命令生成：

```bash
npm test -- --coverage
```

覆盖率报告将显示每个文件的行覆盖率、函数覆盖率、分支覆盖率和语句覆盖率。

## 测试最佳实践

1. **测试隔离**：每个测试应该是独立的，不依赖于其他测试的状态。
2. **测试数据清理**：测试应该在运行后清理创建的测试数据。
3. **测试命名**：测试名称应该清晰地描述测试的内容和预期结果。
4. **测试覆盖**：测试应该覆盖正常情况和边缘情况。
5. **测试断言**：每个测试应该包含明确的断言，验证预期结果。
6. **测试组织**：测试应该按照功能或组件进行组织，便于维护和理解。
7. **测试文档**：测试应该包含必要的注释和文档，说明测试的目的和方法。
8. **测试自动化**：测试应该能够自动运行，不需要人工干预。
9. **测试速度**：测试应该尽可能快速运行，以便频繁执行。
10. **测试可靠性**：测试应该是可靠的，不应该出现随机失败的情况。
