/**
 * Jest配置文件
 */

const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // 指定Next.js应用的根目录，用于加载next.config.js和.env文件
  dir: './',
})

// Jest自定义配置
const customJestConfig = {
  // 添加更多自定义配置
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  moduleNameMapper: {
    // 处理模块别名
    '^@/(.*)$': '<rootDir>/$1',
    // 解决React版本不兼容问题
    '^react$': '<rootDir>/node_modules/react',
    '^react-dom$': '<rootDir>/node_modules/react-dom',
    '^react-dom/client$': '<rootDir>/node_modules/react-dom/client',
  },
  testEnvironment: 'jest-environment-jsdom',
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/.next/',
  ],
  collectCoverage: false,
  collectCoverageFrom: [
    'components/**/*.{js,jsx,ts,tsx}',
    'lib/**/*.{js,jsx,ts,tsx}',
    'app/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
  // 暂时禁用覆盖率阈值检查
  // coverageThreshold: {
  //   global: {
  //     branches: 70,
  //     functions: 70,
  //     lines: 70,
  //     statements: 70,
  //   },
  // },
  transform: {
    // 使用babel-jest处理js/jsx/ts/tsx文件
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
  },
  // 添加测试匹配模式
  testMatch: [
    '<rootDir>/tests/**/*.test.(js|jsx|ts|tsx)'
  ],
  // 添加模块解析器
  moduleDirectories: ['node_modules', '<rootDir>'],
  // 添加模块文件扩展名
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json', 'node'],
  // 解决React版本不兼容问题
  resolver: '<rootDir>/tests/resolver.js',
}

// createJestConfig会将Next.js的配置和自定义配置合并
module.exports = createJestConfig(customJestConfig)
