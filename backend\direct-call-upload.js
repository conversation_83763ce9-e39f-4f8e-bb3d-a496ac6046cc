/**
 * 直接调用uploadFileToDifyDataset函数
 */

const { Sequelize } = require('sequelize');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');
require('dotenv').config();

// 连接数据库
const sequelize = new Sequelize(
  process.env.DB_NAME || 'hefamily_dev',
  process.env.DB_USERNAME || 'root',
  process.env.DB_PASSWORD || 'Misaya9987.',
  {
    host: process.env.DB_HOST || 'localhost',
    dialect: 'mysql',
    logging: console.log
  }
);

// 定义文件模型
const File = sequelize.define('File', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: Sequelize.STRING,
  original_name: Sequelize.STRING,
  path: Sequelize.STRING,
  type: Sequelize.STRING,
  size: Sequelize.INTEGER,
  status: Sequelize.STRING,
  dify_task_id: Sequelize.STRING,
  analysis_status: Sequelize.STRING,
  analysis_error: Sequelize.TEXT
}, {
  tableName: 'files',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

/**
 * 将文件上传到Dify知识库
 * @param {Object} file - 文件对象
 * @returns {Promise<boolean>} 上传是否成功
 */
const uploadFileToDifyDataset = async (file) => {
  console.log(`开始处理文件上传到Dify知识库，文件ID: ${file?.id || '未知'}`);

  try {
    // 检查文件对象是否有效
    if (!file) {
      console.error('文件对象为空');
      return false;
    }

    console.log(`文件信息:`, {
      id: file.id,
      name: file.name,
      original_name: file.original_name,
      path: file.path,
      type: file.type,
      size: file.size,
      status: file.status
    });

    // 检查文件是否存在
    if (!fs.existsSync(file.path)) {
      console.error(`文件不存在: ${file.path}`);
      throw new Error(`文件不存在: ${file.path}`);
    }

    // 准备FormData
    const FormData = require('form-data');
    const formData = new FormData();

    // 添加文件
    formData.append('file', fs.createReadStream(file.path), file.original_name);

    // 添加处理规则
    const processRule = {
      indexing_technique: "high_quality",
      process_rule: {
        rules: {
          pre_processing_rules: [
            { id: "remove_extra_spaces", enabled: true },
            { id: "remove_urls_emails", enabled: true }
          ],
          segmentation: {
            separator: "###",
            max_tokens: 500
          }
        },
        mode: "custom"
      }
    };

    formData.append('data', JSON.stringify(processRule), { type: 'text/plain' });

    // 硬编码Dify API配置
    const datasetId = '77199451-730a-4d79-a1c9-9b9e6bfcd747';
    const apiEndpoint = `https://ai.glab.vip/v1/datasets/${datasetId}/document/create-by-file`;
    const apiKey = "dataset-DLFJlUe25VUHOwMO4HbO4hQk";

    console.log(`准备发送请求到: ${apiEndpoint}`);

    // 发送请求
    try {
      console.log('发送请求头:', {
        'Authorization': `Bearer ${apiKey.substring(0, 5)}...`,
        'Content-Type': formData.getHeaders()['content-type']
      });

      const uploadResponse = await axios.post(
        apiEndpoint,
        formData,
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            ...formData.getHeaders()
          },
          timeout: 120000 // 120秒超时
        }
      );

      console.log(`请求已发送，状态码: ${uploadResponse.status}`);
      console.log(`响应数据:`, uploadResponse.data);

      if (!uploadResponse.data || !uploadResponse.data.document) {
        console.error(`响应数据不包含document字段:`, uploadResponse.data);
        throw new Error('上传文件到Dify知识库失败，响应数据不包含document字段');
      }

      // 更新文件记录，保存Dify文档ID
      const documentId = uploadResponse.data.document.id;
      await file.update({
        dify_task_id: documentId,
        analysis_status: 'completed',
        analysis_completed_at: new Date()
      });

      console.log(`文件 ${file.id} 已成功上传到Dify知识库，文档ID: ${documentId}`);
      return true;
    } catch (error) {
      console.error(`上传文件到Dify知识库失败 (文件ID: ${file?.id}):`, error.message);

      if (error.response) {
        console.error('响应状态码:', error.response.status);
        console.error('响应数据:', error.response.data);
      }

      // 更新文件状态，标记为上传失败
      if (file) {
        try {
          await file.update({
            analysis_status: 'failed',
            analysis_error: error.message || '上传到Dify知识库失败'
          });
        } catch (updateError) {
          console.error(`更新文件状态失败:`, updateError);
        }
      }

      return false;
    }


};

// 主函数
async function main() {
  try {
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 获取文件ID
    const fileId = process.argv[2]; // 从命令行参数获取文件ID
    if (!fileId) {
      console.error('请指定文件ID');
      process.exit(1);
    }

    console.log(`尝试获取ID为 ${fileId} 的文件...`);
    const file = await File.findByPk(fileId);
    if (!file) {
      console.error(`未找到ID为 ${fileId} 的文件`);
      process.exit(1);
    }

    console.log(`找到文件: ${file.id}, ${file.original_name}`);
    console.log(`文件状态: ${file.status}`);
    console.log(`文件路径: ${file.path}`);

    // 直接调用uploadFileToDifyDataset函数
    console.log('直接调用uploadFileToDifyDataset函数...');
    const result = await uploadFileToDifyDataset(file);

    if (result) {
      console.log(`文件 ${file.id} 已成功上传到Dify知识库`);
    } else {
      console.error(`文件 ${file.id} 上传到Dify知识库失败`);
    }

    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('发生错误:', error);
    process.exit(1);
  }
}

// 执行主函数
main().then(() => {
  console.log('测试完成');
  process.exit(0);
}).catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
