/**
 * 控制台拦截器
 *
 * 在生产环境中禁用所有控制台输出，防止敏感信息泄露
 * 在开发环境和测试环境中保持原有功能
 */
import Environment from './environment';

// 保存原始控制台方法的引用
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error,
  debug: console.debug,
  trace: console.trace,
  dir: console.dir,
  table: console.table,
  count: console.count,
  countReset: console.countReset,
  group: console.group,
  groupCollapsed: console.groupCollapsed,
  groupEnd: console.groupEnd,
  time: console.time,
  timeLog: console.timeLog,
  timeEnd: console.timeEnd,
  assert: console.assert,
  clear: console.clear
};

/**
 * 初始化控制台拦截器
 * 在生产环境中禁用所有控制台输出
 */
export function initConsoleInterceptor() {
  // 只在客户端环境中执行
  if (typeof window === 'undefined') {
    return;
  }

  // 检查当前环境
  const isProductionEnv = Environment.isProduction();

  // 在控制台输出当前环境信息（仅在非生产环境）
  if (!isProductionEnv) {
    console.info(`当前环境: ${Environment.getName()} (${Environment.getType()})`);
    console.info('控制台拦截器已初始化，在生产环境中将禁用所有控制台输出');
  }

  // 只在生产环境中禁用控制台
  if (isProductionEnv) {
    // 创建空函数替代所有控制台方法
    const noop = () => {};

    // 覆盖所有控制台方法
    console.log = noop;
    console.info = noop;
    console.warn = noop;
    console.error = noop;
    console.debug = noop;
    console.trace = noop;
    console.dir = noop;
    console.table = noop;
    console.count = noop;
    console.countReset = noop;
    console.group = noop;
    console.groupCollapsed = noop;
    console.groupEnd = noop;
    console.time = noop;
    console.timeLog = noop;
    console.timeEnd = noop;
    console.assert = noop;
    console.clear = noop;

    // 添加一个特殊方法，用于在必要时恢复控制台功能（仅用于调试）
    (window as any).__restoreConsole = () => {
      Object.keys(originalConsole).forEach(key => {
        console[key] = originalConsole[key];
      });
      return '控制台功能已恢复';
    };

    // 添加一个特殊方法，用于再次禁用控制台功能
    (window as any).__disableConsole = () => {
      Object.keys(originalConsole).forEach(key => {
        console[key] = noop;
      });
      return '控制台功能已禁用';
    };
  }
}

/**
 * 恢复控制台原始功能
 * 主要用于测试目的
 */
export function restoreConsole() {
  Object.keys(originalConsole).forEach(key => {
    console[key] = originalConsole[key];
  });
}
