"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Bell, Check, Trash2, X, Refresh<PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import {
  getNotifications,
  getUnreadNotificationCount,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  deleteAllNotifications,
  Notification
} from "@/services/notification-service"
import { useAuth } from "@/contexts/auth-context"
import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"

/**
 * 通知中心组件
 *
 * 显示用户的通知，支持标记已读和删除功能
 */
export function NotificationCenter() {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [isMarkingAllRead, setIsMarkingAllRead] = useState(false)
  const { isLoggedIn } = useAuth()

  // 获取通知
  const fetchNotifications = async () => {
    if (!isLoggedIn) return

    setIsLoading(true)
    try {
      const params: any = { limit: 20 }
      if (activeTab === "unread") {
        params.is_read = false
      }

      const response = await getNotifications(params)
      setNotifications(response.notifications)

      // 更新未读数量
      const unreadResponse = await getUnreadNotificationCount()
      setUnreadCount(unreadResponse.unread_count)
    } catch (error) {
      console.error("获取通知失败:", error)
      toast({
        title: "获取通知失败",
        description: "无法加载通知，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 标记通知为已读
  const handleMarkAsRead = async (id: string) => {
    try {
      await markNotificationAsRead(id)

      // 更新本地通知状态
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id
            ? { ...notification, is_read: true }
            : notification
        )
      )

      // 更新未读数量
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (error) {
      console.error("标记通知已读失败:", error)
      toast({
        title: "操作失败",
        description: "无法标记通知为已读，请稍后再试",
        variant: "destructive"
      })
    }
  }

  // 标记所有通知为已读
  const handleMarkAllAsRead = async () => {
    setIsMarkingAllRead(true)
    try {
      await markAllNotificationsAsRead()

      // 更新本地通知状态
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: true }))
      )

      // 更新未读数量
      setUnreadCount(0)

      toast({
        title: "已全部标记为已读",
        description: "所有通知已标记为已读"
      })
    } catch (error) {
      console.error("标记所有通知已读失败:", error)
      toast({
        title: "操作失败",
        description: "无法标记所有通知为已读，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsMarkingAllRead(false)
    }
  }

  // 删除通知
  const handleDeleteNotification = async (id: string) => {
    try {
      await deleteNotification(id)

      // 更新本地通知状态
      const deletedNotification = notifications.find(n => n.id === id)
      setNotifications(prev => prev.filter(notification => notification.id !== id))

      // 如果删除的是未读通知，更新未读数量
      if (deletedNotification && !deletedNotification.is_read) {
        setUnreadCount(prev => Math.max(0, prev - 1))
      }

      toast({
        title: "通知已删除",
        description: "通知已成功删除"
      })
    } catch (error) {
      console.error("删除通知失败:", error)
      toast({
        title: "操作失败",
        description: "无法删除通知，请稍后再试",
        variant: "destructive"
      })
    }
  }

  // 清空所有通知
  const handleClearAllNotifications = async () => {
    if (!confirm("确定要清空所有通知吗？此操作不可撤销。")) return

    try {
      await deleteAllNotifications()

      // 更新本地通知状态
      setNotifications([])
      setUnreadCount(0)

      toast({
        title: "通知已清空",
        description: "所有通知已成功清空"
      })
    } catch (error) {
      console.error("清空通知失败:", error)
      toast({
        title: "操作失败",
        description: "无法清空通知，请稍后再试",
        variant: "destructive"
      })
    }
  }

  // 处理通知点击
  const handleNotificationClick = (notification: Notification) => {
    // 如果通知未读，标记为已读
    if (!notification.is_read) {
      handleMarkAsRead(notification.id)
    }

    // 根据通知类型和相关ID进行跳转
    if (notification.related_id && notification.related_type) {
      switch (notification.related_type) {
        case "comment":
          // 跳转到评论所在页面
          if (notification.related_id.startsWith('personal-')) {
            // 个人专题评论
            const topicId = notification.related_id.replace('personal-', '')
            window.location.href = `/personal-topic/${topicId}`
          } else if (notification.related_id.startsWith('family-')) {
            // 家族专题评论
            window.location.href = `/family-topic`
          }
          break
        case "file":
          // 跳转到文件详情页
          if (notification.related_id.startsWith('kb-')) {
            // 知识库文件
            const kbId = notification.related_id.split('-')[1]
            const fileId = notification.related_id.split('-')[2]
            window.location.href = `/knowledge-base/${kbId}?file=${fileId}`
          }
          break
        case "activity":
          // 跳转到活动详情页
          window.location.href = `/activity/${notification.related_id}`
          break
        default:
          // 默认不跳转
          break
      }
    }
  }

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: zhCN })
    } catch (error) {
      return "未知时间"
    }
  }

  // 获取通知类型图标和颜色
  const getNotificationTypeInfo = (type: string) => {
    switch (type) {
      case "system":
        return { color: "bg-blue-100 text-blue-800", label: "系统" }
      case "comment":
        return { color: "bg-green-100 text-green-800", label: "评论" }
      case "file":
        return { color: "bg-amber-100 text-amber-800", label: "文件" }
      case "security":
        return { color: "bg-red-100 text-red-800", label: "安全" }
      case "activity":
        return { color: "bg-purple-100 text-purple-800", label: "活动" }
      case "knowledge":
        return { color: "bg-indigo-100 text-indigo-800", label: "知识库" }
      default:
        return { color: "bg-gray-100 text-gray-800", label: "通知" }
    }
  }

  // 当打开通知中心或切换标签时获取通知
  useEffect(() => {
    if (isOpen) {
      fetchNotifications()
    }
  }, [isOpen, activeTab, isLoggedIn])

  // 定期更新未读通知数量
  useEffect(() => {
    if (!isLoggedIn) return

    const fetchUnreadCount = async () => {
      try {
        const response = await getUnreadNotificationCount()
        setUnreadCount(response.unread_count)
      } catch (error) {
        console.error("获取未读通知数量失败:", error)
      }
    }

    fetchUnreadCount()
    const interval = setInterval(fetchUnreadCount, 60000) // 每分钟更新一次

    return () => clearInterval(interval)
  }, [isLoggedIn])

  if (!isLoggedIn) return null

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <button className="p-2 rounded-full text-gray-600 hover:bg-gray-100 transition-colors relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
              {unreadCount > 99 ? "99+" : unreadCount}
            </span>
          )}
        </button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-80 p-0">
        <div className="flex items-center justify-between p-3 border-b">
          <h3 className="font-medium">通知中心</h3>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-500"
              onClick={fetchNotifications}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
              <span className="sr-only">刷新</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-500"
              onClick={() => setIsOpen(false)}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">关闭</span>
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <div className="border-b">
            <TabsList className="w-full justify-start rounded-none border-b bg-transparent p-0">
              <TabsTrigger
                value="all"
                className="rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-[#1e8e3e] data-[state=active]:bg-transparent data-[state=active]:shadow-none"
              >
                全部
              </TabsTrigger>
              <TabsTrigger
                value="unread"
                className="rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-[#1e8e3e] data-[state=active]:bg-transparent data-[state=active]:shadow-none"
              >
                未读
                {unreadCount > 0 && (
                  <Badge variant="secondary" className="ml-1 bg-gray-100">
                    {unreadCount}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="all" className="m-0">
            <div className="flex items-center justify-between p-2 border-b">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                onClick={handleMarkAllAsRead}
                disabled={isMarkingAllRead || unreadCount === 0}
              >
                {isMarkingAllRead ? (
                  <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <Check className="h-3 w-3 mr-1" />
                )}
                全部标为已读
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-red-600 hover:text-red-800 hover:bg-red-50"
                onClick={handleClearAllNotifications}
                disabled={notifications.length === 0}
              >
                <Trash2 className="h-3 w-3 mr-1" />
                清空通知
              </Button>
            </div>

            <ScrollArea className="h-[300px]">
              {isLoading ? (
                <div className="p-4 space-y-4">
                  {Array(3).fill(0).map((_, i) => (
                    <div key={i} className="flex space-x-3">
                      <Skeleton className="h-10 w-10 rounded-md" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-3 w-4/5" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : notifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full p-4 text-center">
                  <Bell className="h-12 w-12 text-gray-300 mb-2" />
                  <p className="text-gray-500">暂无通知</p>
                </div>
              ) : (
                <div>
                  {notifications.map((notification) => {
                    const typeInfo = getNotificationTypeInfo(notification.type)

                    return (
                      <div key={notification.id} className="relative">
                        <div
                          className={`p-3 hover:bg-gray-50 transition-colors ${!notification.is_read ? "bg-blue-50/30" : ""} cursor-pointer`}
                          onClick={() => handleNotificationClick(notification)}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex items-start space-x-2">
                              <Badge variant="secondary" className={`${typeInfo.color} text-[10px]`}>
                                {typeInfo.label}
                              </Badge>
                              <div>
                                <p className={`text-sm ${!notification.is_read ? "font-medium" : ""}`}>
                                  {notification.title}
                                </p>
                                <p className="text-xs text-gray-600 mt-1">{notification.content}</p>
                                <p className="text-xs text-gray-500 mt-1">{formatTime(notification.created_at)}</p>
                              </div>
                            </div>

                            <div className="flex space-x-1 ml-2" onClick={(e) => e.stopPropagation()}>
                              {!notification.is_read && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                                  onClick={() => handleMarkAsRead(notification.id)}
                                >
                                  <Check className="h-3 w-3" />
                                  <span className="sr-only">标记为已读</span>
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 text-red-600 hover:text-red-800 hover:bg-red-50"
                                onClick={() => handleDeleteNotification(notification.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                                <span className="sr-only">删除</span>
                              </Button>
                            </div>
                          </div>
                        </div>
                        <Separator />
                      </div>
                    )
                  })}
                </div>
              )}
            </ScrollArea>

            <div className="p-2 border-t text-center">
              <Link
                href="/notifications"
                className="text-xs text-[#1e8e3e] hover:underline"
                onClick={() => setIsOpen(false)}
              >
                查看全部通知
              </Link>
            </div>
          </TabsContent>

          <TabsContent value="unread" className="m-0">
            <div className="flex items-center justify-between p-2 border-b">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                onClick={handleMarkAllAsRead}
                disabled={isMarkingAllRead || unreadCount === 0}
              >
                {isMarkingAllRead ? (
                  <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <Check className="h-3 w-3 mr-1" />
                )}
                全部标为已读
              </Button>
            </div>

            <ScrollArea className="h-[300px]">
              {isLoading ? (
                <div className="p-4 space-y-4">
                  {Array(3).fill(0).map((_, i) => (
                    <div key={i} className="flex space-x-3">
                      <Skeleton className="h-10 w-10 rounded-md" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-3 w-4/5" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : notifications.filter(n => !n.is_read).length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full p-4 text-center">
                  <Check className="h-12 w-12 text-gray-300 mb-2" />
                  <p className="text-gray-500">没有未读通知</p>
                </div>
              ) : (
                <div>
                  {notifications
                    .filter(notification => !notification.is_read)
                    .map((notification) => {
                      const typeInfo = getNotificationTypeInfo(notification.type)

                      return (
                        <div key={notification.id} className="relative">
                          <div
                            className="p-3 hover:bg-gray-50 transition-colors bg-blue-50/30 cursor-pointer"
                            onClick={() => handleNotificationClick(notification)}
                          >
                            <div className="flex justify-between items-start">
                              <div className="flex items-start space-x-2">
                                <Badge variant="secondary" className={`${typeInfo.color} text-[10px]`}>
                                  {typeInfo.label}
                                </Badge>
                                <div>
                                  <p className="text-sm font-medium">{notification.title}</p>
                                  <p className="text-xs text-gray-600 mt-1">{notification.content}</p>
                                  <p className="text-xs text-gray-500 mt-1">{formatTime(notification.created_at)}</p>
                                </div>
                              </div>

                              <div className="flex space-x-1 ml-2" onClick={(e) => e.stopPropagation()}>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                                  onClick={() => handleMarkAsRead(notification.id)}
                                >
                                  <Check className="h-3 w-3" />
                                  <span className="sr-only">标记为已读</span>
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 text-red-600 hover:text-red-800 hover:bg-red-50"
                                  onClick={() => handleDeleteNotification(notification.id)}
                                >
                                  <Trash2 className="h-3 w-3" />
                                  <span className="sr-only">删除</span>
                                </Button>
                              </div>
                            </div>
                          </div>
                          <Separator />
                        </div>
                      )
                    })}
                </div>
              )}
            </ScrollArea>

            <div className="p-2 border-t text-center">
              <Link
                href="/notifications"
                className="text-xs text-[#1e8e3e] hover:underline"
                onClick={() => setIsOpen(false)}
              >
                查看全部通知
              </Link>
            </div>
          </TabsContent>
        </Tabs>
      </PopoverContent>
    </Popover>
  )
}
