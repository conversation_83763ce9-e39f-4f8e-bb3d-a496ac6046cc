/**
 * 评论列表组件
 * 
 * 用于展示评论列表
 */

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import { toast } from '@/components/ui/use-toast'
import { LoadingState } from '@/components/ui/loading-state'
import { CommentItem } from './CommentItem'
import { CommentForm } from './CommentForm'

// 评论类型
export interface Comment {
  id: number
  content: string
  user_id: number
  user_name: string
  created_at: string
  status: 'pending' | 'approved' | 'rejected'
  parent_id?: number
  children?: Comment[]
}

// 评论列表组件属性
interface CommentListProps {
  targetId: number
  targetType: 'article' | 'topic' | 'knowledge'
  canComment?: boolean
}

// 评论列表组件
export const CommentList: React.FC<CommentListProps> = ({
  targetId,
  targetType,
  canComment = true
}) => {
  const [comments, setComments] = useState<Comment[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  // 加载评论
  const loadComments = async (pageNum = 1, append = false) => {
    setIsLoading(true)
    setError(null)
    
    try {
      // 模拟API调用
      const response = await fetch(`/api/comments?target_id=${targetId}&target_type=${targetType}&page=${pageNum}`)
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.message || '加载评论失败')
      }
      
      const newComments = data.data || []
      
      if (append) {
        setComments([...comments, ...newComments])
      } else {
        setComments(newComments)
      }
      
      setHasMore(newComments.length === 10) // 假设每页10条评论
      setPage(pageNum)
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载评论失败')
      toast({
        title: '加载失败',
        description: err instanceof Error ? err.message : '加载评论失败',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 首次加载评论
  useEffect(() => {
    loadComments()
  }, [targetId, targetType])

  // 加载更多评论
  const handleLoadMore = () => {
    if (hasMore && !isLoading) {
      loadComments(page + 1, true)
    }
  }

  // 添加评论
  const handleAddComment = async (content: string) => {
    try {
      // 模拟API调用
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content,
          target_id: targetId,
          target_type: targetType
        })
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.message || '添加评论失败')
      }
      
      // 添加临时评论（待审核状态）
      const newComment: Comment = {
        id: data.data.id,
        content,
        user_id: 1, // 假设当前用户ID为1
        user_name: '当前用户', // 假设当前用户名
        created_at: new Date().toISOString(),
        status: 'pending'
      }
      
      setComments([newComment, ...comments])
      
      toast({
        title: '评论成功',
        description: '您的评论已提交，等待审核'
      })
    } catch (err) {
      toast({
        title: '评论失败',
        description: err instanceof Error ? err.message : '添加评论失败',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">评论</h2>
      
      {canComment && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">发表评论</h3>
          </CardHeader>
          <CardContent>
            <CommentForm onSubmit={handleAddComment} />
          </CardContent>
        </Card>
      )}
      
      {isLoading && comments.length === 0 && (
        <LoadingState status="loading" loadingText="正在加载评论..." />
      )}
      
      {error && comments.length === 0 && (
        <LoadingState status="error" errorText={error} />
      )}
      
      {comments.length === 0 && !isLoading && !error && (
        <div className="text-center p-4 text-gray-500">
          暂无评论，快来发表第一条评论吧！
        </div>
      )}
      
      {comments.length > 0 && (
        <div className="space-y-4">
          {comments.map(comment => (
            <CommentItem 
              key={comment.id} 
              comment={comment} 
            />
          ))}
          
          {hasMore && (
            <div className="text-center">
              <Button 
                variant="outline" 
                onClick={handleLoadMore}
                disabled={isLoading}
              >
                {isLoading ? '加载中...' : '加载更多'}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default CommentList
