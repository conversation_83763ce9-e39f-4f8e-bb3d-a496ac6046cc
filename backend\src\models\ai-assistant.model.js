/**
 * AI助手模型
 *
 * 定义AI助手数据结构，包括助手名称、类型、配置等信息
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} AIAssistant模型
 */
module.exports = (sequelize, DataTypes) => {
  const AIAssistant = sequelize.define('AIAssistant', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    type: {
      type: DataTypes.ENUM('personal', 'data-query', 'assistant', 'knowledge-file', 'system-knowledge-file', 'user-knowledge-file'),
      allowNull: false,
      comment: '助手类型：个人专题助手、数据查询助手、AI研究助手、知识库文件分析助手、系统知识库文件助手、用户知识库文件助手'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    api_key: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Dify API密钥'
    },
    api_endpoint: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'Dify API端点'
    },
    app_id: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '系统知识库数据集ID（知识库文件分析助手）或Dify应用ID（其他助手）'
    },
    app_code: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '用户知识库数据集ID（知识库文件分析助手）或Dify应用代码（其他助手）'
    },
    upload_api_path: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '文件上传API路径（仅知识库文件分析助手使用）'
    },
    analysis_api_path: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '文件分析API路径（仅知识库文件分析助手使用，已弃用）'
    },
    tags: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '助手标签，用于分类和筛选（仅AI研究助手使用）'
    },
    initial_message: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '初始对话消息'
    },
    is_system: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否为系统预设助手，系统预设助手不可删除'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      allowNull: false,
      defaultValue: 'active',
      comment: '助手状态：活跃、非活跃'
    },
    creator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    last_updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'ai_assistants',
    timestamps: true
  });

  // 关联关系
  AIAssistant.associate = (models) => {
    // AI助手与创建者的多对一关系
    AIAssistant.belongsTo(models.User, {
      foreignKey: 'creator_id',
      as: 'creator'
    });

    // AI助手与最后更新者的多对一关系
    AIAssistant.belongsTo(models.User, {
      foreignKey: 'last_updated_by',
      as: 'lastUpdatedBy'
    });
  };

  return AIAssistant;
};
