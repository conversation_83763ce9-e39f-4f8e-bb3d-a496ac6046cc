'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'

/**
 * 404页面组件
 * 当访问不存在的路径时显示
 */
export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] p-4">
      <div className="max-w-md text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">页面未找到</h2>
        <p className="text-gray-600 mb-6">
          抱歉，您访问的页面不存在或已被移除。
        </p>
        <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
          <Button
            asChild
            className="bg-primary hover:bg-primary/90"
          >
            <Link href="/">
              返回首页
            </Link>
          </Button>
          <Button
            onClick={() => window.history.back()}
            variant="outline"
          >
            返回上一页
          </Button>
        </div>
      </div>
    </div>
  )
}
