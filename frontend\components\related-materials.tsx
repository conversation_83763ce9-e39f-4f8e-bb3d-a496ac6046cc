"use client"

import { useState } from "react"
import {
  FileText,
  FileIcon as <PERSON>Word,
  FileSpreadsheet,
  FileIcon as FilePresentationIcon,
  File,
  Download,
  X,
  ExternalLink,
  FileDown,
  ImageIcon,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface MaterialFile {
  id: number
  name: string
  type: string
  size: string
  uploadedBy: string
  uploadedAt: string
  downloads: number
  views: number
  summary: string
  description: string
  url?: string
}

interface RelatedMaterialsProps {
  files: MaterialFile[]
}

export function RelatedMaterials({ files }: RelatedMaterialsProps) {
  const [selectedFile, setSelectedFile] = useState<MaterialFile | null>(null)

  // 处理文件点击
  const handleFileClick = (file: MaterialFile) => {
    setSelectedFile(file)
  }

  // 获取文件图标
  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf":
        return <FileText className="h-5 w-5 text-red-500" />
      case "docx":
      case "doc":
        return <FileWord className="h-5 w-5 text-blue-500" />
      case "xlsx":
      case "xls":
        return <FileSpreadsheet className="h-5 w-5 text-green-500" />
      case "pptx":
      case "ppt":
        return <FilePresentationIcon className="h-5 w-5 text-orange-500" />
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
      case "webp":
        return <ImageIcon className="h-5 w-5 text-purple-500" />
      default:
        return <File className="h-5 w-5 text-gray-500" />
    }
  }

  // 获取文件类型标签颜色
  const getFileTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf":
        return "bg-red-100 text-red-800 hover:bg-red-200"
      case "docx":
      case "doc":
        return "bg-blue-100 text-blue-800 hover:bg-blue-200"
      case "xlsx":
      case "xls":
        return "bg-green-100 text-green-800 hover:bg-green-200"
      case "pptx":
      case "ppt":
        return "bg-orange-100 text-orange-800 hover:bg-orange-200"
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
      case "webp":
        return "bg-purple-100 text-purple-800 hover:bg-purple-200"
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-200"
    }
  }

  return (
    <Card className="shadow-sm">
      <CardHeader>
        <CardTitle>相关资料</CardTitle>
        <CardDescription>与本专题相关的文档和资料</CardDescription>
      </CardHeader>
      <CardContent>
        {selectedFile ? (
          <div className="space-y-4">
            {/* File details area */}
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                {getFileIcon(selectedFile.type)}
                <h3 className="font-medium">{selectedFile.name}</h3>
              </div>
              <Button variant="ghost" size="icon" onClick={() => setSelectedFile(null)}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            <Separator />

            {/* File summary */}
            <div>
              <h4 className="text-sm font-medium mb-2">概述</h4>
              <p className="text-sm text-gray-600">{selectedFile.summary}</p>
            </div>

            {/* Detailed description */}
            <div>
              <h4 className="text-sm font-medium mb-2">详细描述</h4>
              <p className="text-sm text-gray-600 whitespace-pre-line">{selectedFile.description}</p>
            </div>

            {/* File info and download button */}
            <div className="flex justify-between items-center pt-4">
              <div className="text-xs text-gray-500">
                <p>上传者: {selectedFile.uploadedBy}</p>
                <p>上传时间: {selectedFile.uploadedAt}</p>
                <p>
                  下载次数: {selectedFile.downloads} | 查看次数: {selectedFile.views}
                </p>
              </div>
              <div className="flex gap-2">
                {selectedFile.url && (
                  <Button
                    onClick={() => {
                      if (selectedFile.url) {
                        const link = document.createElement("a")
                        link.href = selectedFile.url
                        link.download = selectedFile.name
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link)
                      } else {
                        alert("下载链接不可用")
                      }
                    }}
                    className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                  >
                    <FileDown className="h-4 w-4 mr-2" />
                    下载文件
                  </Button>
                )}
                {selectedFile.url && selectedFile.type.toLowerCase() === "pdf" && (
                  <Button variant="outline" onClick={() => window.open(selectedFile.url, "_blank")}>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    在新窗口打开
                  </Button>
                )}
              </div>
            </div>
          </div>
        ) : (
          // File list remains the same
          <div className="space-y-4">
            {/* File list */}
            {files.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
              >
                <div className="flex items-center gap-3" onClick={() => handleFileClick(file)}>
                  {getFileIcon(file.type)}
                  <div>
                    <p className="font-medium text-sm">{file.name}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={getFileTypeColor(file.type)} variant="outline">
                        {file.type.toUpperCase()}
                      </Badge>
                      <span className="text-xs text-gray-500">{file.size}</span>
                      <span className="text-xs text-gray-500">{file.uploadedAt}</span>
                    </div>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation()
                    if (file.url) {
                      const link = document.createElement("a")
                      link.href = file.url
                      link.download = file.name
                      document.body.appendChild(link)
                      link.click()
                      document.body.removeChild(link)
                    } else {
                      alert("下载链接不可用")
                    }
                  }}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
