/**
 * 将测试文件上传到知识库
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');
require('dotenv').config();

// 测试文件路径
const testFilePath = path.join(__dirname, 'test-file-for-dify.txt');

// 检查文件是否存在
if (!fs.existsSync(testFilePath)) {
  console.error(`测试文件不存在: ${testFilePath}`);
  process.exit(1);
}

console.log(`测试文件存在: ${testFilePath}`);

// 知识库ID
const knowledgeBaseId = 74; // 系统知识库ID

// 上传文件到知识库
async function uploadFileToKnowledgeBase() {
  try {
    console.log(`开始上传文件到知识库: ${knowledgeBaseId}`);

    // 准备FormData
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath), 'test-file-for-dify.txt');

    // 设置API端点
    const apiEndpoint = 'http://localhost:5001/api/files/upload/' + knowledgeBaseId;

    console.log(`API端点: ${apiEndpoint}`);
    console.log('准备发送POST请求');

    // 获取JWT令牌
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.Yx-Wr9Yx-Wr9Yx-Wr9Yx-Wr9Yx-Wr9Yx-Wr9Yx-Wr9Yx-Wr9Yx-Wr9Yx-Wr9Yx-Wr9';

    // 发送请求
    const response = await axios.post(
      apiEndpoint,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          ...formData.getHeaders()
        }
      }
    );

    console.log('API响应:');
    console.log('状态码:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));

    if (response.data && response.data.success) {
      console.log(`文件上传成功，文件ID: ${response.data.data.id}`);
      return response.data.data.id;
    } else {
      console.error('上传失败，响应数据不包含success字段');
      return null;
    }
  } catch (error) {
    console.error('上传文件到知识库失败:');
    
    if (error.response) {
      // 服务器响应了错误状态码
      console.error('响应状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('未收到响应:', error.request);
    } else {
      // 设置请求时发生错误
      console.error('错误消息:', error.message);
    }
    
    return null;
  }
}

// 执行上传
uploadFileToKnowledgeBase()
  .then(fileId => {
    if (fileId) {
      console.log(`文件上传成功，文件ID: ${fileId}`);
      console.log('现在可以在后端日志中查看是否自动推送到Dify');
    } else {
      console.error('文件上传失败');
    }
  })
  .catch(error => {
    console.error('上传过程中发生错误:', error);
  });
