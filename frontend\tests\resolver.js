/**
 * 自定义Jest解析器
 * 
 * 用于解决React版本不兼容问题
 */

// 导入默认解析器
const { createRequire } = require('module');
const path = require('path');

// 创建自定义解析器
module.exports = (request, options) => {
  // 获取默认解析器
  const defaultResolver = options.defaultResolver;
  
  // 处理React相关模块
  if (request === 'react' || request.startsWith('react/') || 
      request === 'react-dom' || request.startsWith('react-dom/')) {
    try {
      // 使用项目根目录的React版本
      const rootDir = path.resolve(__dirname, '..');
      const require = createRequire(path.join(rootDir, 'package.json'));
      return require.resolve(request);
    } catch (error) {
      // 如果失败，使用默认解析器
      return defaultResolver(request, options);
    }
  }
  
  // 对于其他模块，使用默认解析器
  return defaultResolver(request, options);
};
