/**
 * 修复文件路径
 */

const { Sequelize } = require('sequelize');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 连接数据库
const sequelize = new Sequelize(
  process.env.DB_NAME || 'hefamily_dev',
  process.env.DB_USERNAME || 'root',
  process.env.DB_PASSWORD || 'Misaya9987.',
  {
    host: process.env.DB_HOST || 'localhost',
    dialect: 'mysql',
    logging: console.log
  }
);

// 定义文件模型
const File = sequelize.define('File', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: Sequelize.STRING,
  original_name: Sequelize.STRING,
  path: Sequelize.STRING,
  type: Sequelize.STRING,
  size: Sequelize.INTEGER,
  status: Sequelize.STRING,
  dify_task_id: Sequelize.STRING,
  analysis_status: Sequelize.STRING,
  analysis_error: Sequelize.TEXT
}, {
  tableName: 'files',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 主函数
async function main() {
  try {
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 获取文件ID
    const fileId = process.argv[2]; // 从命令行参数获取文件ID
    if (!fileId) {
      console.error('请指定文件ID');
      process.exit(1);
    }
    
    console.log(`尝试获取ID为 ${fileId} 的文件...`);
    const file = await File.findByPk(fileId);
    if (!file) {
      console.error(`未找到ID为 ${fileId} 的文件`);
      process.exit(1);
    }
    
    console.log(`找到文件: ${file.id}, ${file.original_name}`);
    console.log(`文件状态: ${file.status}`);
    console.log(`当前文件路径: ${file.path}`);
    
    // 检查文件是否存在
    if (fs.existsSync(file.path)) {
      console.log(`文件存在: ${file.path}`);
    } else {
      console.log(`文件不存在: ${file.path}`);
      
      // 尝试在backend/uploads目录中查找文件
      const fileName = path.basename(file.path);
      const newPath = path.join(__dirname, 'uploads', fileName);
      
      console.log(`尝试在backend/uploads目录中查找文件: ${newPath}`);
      
      if (fs.existsSync(newPath)) {
        console.log(`文件存在于backend/uploads目录: ${newPath}`);
        
        // 更新文件路径
        await file.update({ path: newPath });
        console.log(`文件路径已更新为: ${newPath}`);
      } else {
        console.log(`文件在backend/uploads目录中也不存在: ${newPath}`);
        
        // 尝试在根目录的uploads目录中查找文件
        const rootPath = path.join(__dirname, '..', 'uploads', fileName);
        
        console.log(`尝试在根目录的uploads目录中查找文件: ${rootPath}`);
        
        if (fs.existsSync(rootPath)) {
          console.log(`文件存在于根目录的uploads目录: ${rootPath}`);
          
          // 更新文件路径
          await file.update({ path: rootPath });
          console.log(`文件路径已更新为: ${rootPath}`);
        } else {
          console.log(`文件在根目录的uploads目录中也不存在: ${rootPath}`);
          
          // 创建一个测试文件
          console.log('创建一个测试文件...');
          
          // 确保uploads目录存在
          const uploadsDir = path.join(__dirname, 'uploads');
          if (!fs.existsSync(uploadsDir)) {
            fs.mkdirSync(uploadsDir, { recursive: true });
            console.log(`已创建uploads目录: ${uploadsDir}`);
          }
          
          // 创建测试文件
          const testFilePath = path.join(uploadsDir, fileName);
          fs.writeFileSync(testFilePath, '这是一个测试文件，用于测试上传到Dify知识库的功能。');
          console.log(`已创建测试文件: ${testFilePath}`);
          
          // 更新文件路径
          await file.update({ path: testFilePath });
          console.log(`文件路径已更新为: ${testFilePath}`);
        }
      }
    }
    
    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('发生错误:', error);
    process.exit(1);
  }
}

// 执行主函数
main().then(() => {
  console.log('测试完成');
  process.exit(0);
}).catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
