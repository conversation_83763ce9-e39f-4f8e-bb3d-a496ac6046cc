/**
 * ErrorBoundary组件测试
 */

import React from 'react'
import { render, screen, fireEvent } from '../test-utils'
import { ErrorBoundary } from '@/components/ui/error-boundary'

// 创建一个会抛出错误的组件
const ErrorComponent = ({ shouldThrow = false }) => {
  if (shouldThrow) {
    throw new Error('测试错误')
  }
  return <div data-testid="normal-content">正常内容</div>
}

// 禁用控制台错误，避免测试输出过多错误信息
const originalConsoleError = console.error
beforeAll(() => {
  console.error = jest.fn()
})
afterAll(() => {
  console.error = originalConsoleError
})

describe('ErrorBoundary组件', () => {
  // 测试正常渲染
  test('应该正常渲染子组件', () => {
    render(
      <ErrorBoundary>
        <div data-testid="child-component">子组件内容</div>
      </ErrorBoundary>
    )
    
    expect(screen.getByTestId('child-component')).toBeInTheDocument()
  })

  // 测试错误捕获
  test('应该捕获子组件中的错误并显示错误UI', () => {
    render(
      <ErrorBoundary>
        <ErrorComponent shouldThrow={true} />
      </ErrorBoundary>
    )
    
    // 检查错误UI
    expect(screen.getByText('出现错误')).toBeInTheDocument()
    expect(screen.getByText('组件渲染时发生错误。')).toBeInTheDocument()
    expect(screen.getByText('测试错误')).toBeInTheDocument()
    expect(screen.getByText('重试')).toBeInTheDocument()
  })

  // 测试自定义错误UI
  test('应该支持自定义错误UI', () => {
    render(
      <ErrorBoundary
        fallback={<div data-testid="custom-error">自定义错误UI</div>}
      >
        <ErrorComponent shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(screen.getByTestId('custom-error')).toBeInTheDocument()
    expect(screen.queryByText('出现错误')).not.toBeInTheDocument()
  })

  // 测试错误回调
  test('应该调用onError回调', () => {
    const handleError = jest.fn()
    render(
      <ErrorBoundary onError={handleError}>
        <ErrorComponent shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(handleError).toHaveBeenCalledTimes(1)
    expect(handleError.mock.calls[0][0]).toBeInstanceOf(Error)
    expect(handleError.mock.calls[0][0].message).toBe('测试错误')
  })

  // 测试重试功能
  test('应该支持重试功能', () => {
    // 创建一个组件，可以控制是否抛出错误
    const TestComponent = () => {
      const [shouldThrow, setShouldThrow] = React.useState(true)
      
      React.useEffect(() => {
        // 模拟修复错误
        const timer = setTimeout(() => {
          setShouldThrow(false)
        }, 100)
        
        return () => clearTimeout(timer)
      }, [])
      
      if (shouldThrow) {
        throw new Error('临时错误')
      }
      
      return <div data-testid="fixed-component">已修复的组件</div>
    }
    
    render(
      <ErrorBoundary>
        <TestComponent />
      </ErrorBoundary>
    )
    
    // 检查错误UI
    expect(screen.getByText('出现错误')).toBeInTheDocument()
    
    // 点击重试按钮
    fireEvent.click(screen.getByText('重试'))
    
    // 检查组件是否重新渲染
    expect(screen.getByTestId('fixed-component')).toBeInTheDocument()
  })
})
