/**
 * AI助手类型定义
 */

// AI助手类型
export type AIAgentType = 'personal' | 'data-query' | 'assistant' | 'knowledge-file' | 'system-knowledge-file' | 'user-knowledge-file';

// AI助手状态
export type AIAgentStatus = '正常' | '禁用';

// AI助手接口
export interface AIAgent {
  id: string;
  name: string;
  type: AIAgentType;
  apiKey: string;
  apiEndpoint: string;
  appId: string;
  appCode: string;
  uploadApiPath?: string; // 仅知识库文件分析助手使用
  analysisApiPath?: string; // 仅知识库文件分析助手使用
  description: string;
  tags: string[];
  status: AIAgentStatus | string;
  lastUpdated: string;
}

// AI助手查询请求
export interface AIQueryRequest {
  query: string;
  conversation_id?: string;
}

// AI助手查询响应
export interface AIQueryResponse {
  id: string;
  answer: string;
  conversation_id: string;
  created_at: string;
}

// 知识库设置请求
export interface SetKnowledgeBaseRequest {
  knowledge_base_id: string;
}

// 知识库设置响应
export interface SetKnowledgeBaseResponse {
  knowledge_base_id: string;
  knowledge_base_name: string;
}

// 对话历史消息
export interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  created_at: string;
}

// 对话历史
export interface Conversation {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}
