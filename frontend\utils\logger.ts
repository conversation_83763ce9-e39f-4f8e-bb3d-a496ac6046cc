/**
 * 安全日志工具类
 * 根据环境自动调整日志级别，在生产环境中完全禁用日志输出
 */
import Environment from './environment';

export const logger = {
  /**
   * 调试级别日志，仅在非生产环境显示
   * 在生产环境中完全禁用，不会记录任何内容
   * @param message 日志消息
   * @param args 附加参数
   */
  debug: (message: string, ...args: any[]) => {
    // 生产环境 - 不记录任何内容
    if (Environment.isProduction()) {
      return;
    }

    // 非生产环境 - 记录调试信息
    // 过滤敏感信息
    const safeArgs = args.map(arg => sanitizeData(arg));
    console.debug(message, ...safeArgs);
  },

  /**
   * 信息级别日志，仅在非生产环境显示
   * 在生产环境中完全禁用，不会记录任何内容
   * @param message 日志消息
   * @param args 附加参数
   */
  info: (message: string, ...args: any[]) => {
    // 生产环境 - 不记录任何内容
    if (Environment.isProduction()) {
      return;
    }

    // 非生产环境 - 记录完整信息
    // 过滤敏感信息
    const safeArgs = args.map(arg => sanitizeData(arg));
    console.info(message, ...safeArgs);
  },

  /**
   * 警告级别日志，仅在非生产环境显示
   * 在生产环境中完全禁用，不会记录任何内容
   * @param message 警告消息
   * @param args 附加参数
   */
  warn: (message: string, ...args: any[]) => {
    // 生产环境 - 不记录任何内容
    if (Environment.isProduction()) {
      return;
    }

    // 非生产环境 - 记录完整警告
    // 过滤敏感信息
    const safeArgs = args.map(arg => sanitizeData(arg));
    console.warn(message, ...safeArgs);
  },

  /**
   * 错误级别日志，仅在非生产环境显示
   * 在生产环境中完全禁用，不会记录任何内容
   * @param message 错误消息
   * @param error 错误对象
   */
  error: (message: string, error?: any) => {
    // 生产环境 - 不记录任何内容
    if (Environment.isProduction()) {
      return;
    }

    // 非生产环境 - 记录完整错误
    // 过滤敏感信息
    const safeError = error ? sanitizeData(error) : error;
    console.error(message, safeError);
  }
};

/**
 * 对敏感数据进行脱敏处理
 * @param data 需要脱敏的数据
 * @returns 脱敏后的数据
 */
export const sanitizeData = (data: any): any => {
  if (!data) return data;

  // 敏感字段列表
  const sensitiveKeys = [
    'password', 'token', 'apiKey', 'api_key', 'secret', 'auth',
    'authorization', 'credentials', 'private', 'key', 'cert',
    'signature', 'hash', 'salt', 'pin', 'cvv', 'ssn', 'passport'
  ];

  // 需要脱敏的个人信息字段
  const personalInfoKeys = ['email', 'phone', 'mobile', 'address', 'birthday', 'birthdate', 'idcard', 'id_card'];

  if (typeof data === 'object') {
    const result = Array.isArray(data) ? [] : {};

    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        // 检查键名是否包含敏感字段
        const isSensitiveKey = sensitiveKeys.some(sk =>
          key.toLowerCase().includes(sk.toLowerCase())
        );

        // 检查键名是否是个人信息字段
        const isPersonalInfoKey = personalInfoKeys.some(pk =>
          key.toLowerCase() === pk.toLowerCase()
        );

        if (isSensitiveKey) {
          // 敏感字段完全隐藏
          result[key] = '******';
        } else if (isPersonalInfoKey) {
          // 个人信息字段部分隐藏
          if (key.toLowerCase().includes('email') && typeof data[key] === 'string' && data[key]) {
            const email = data[key];
            if (email.includes('@')) {
              const [name, domain] = email.split('@');
              result[key] = `${name.substring(0, 2)}***@${domain}`;
            } else {
              result[key] = email;
            }
          } else if ((key.toLowerCase().includes('phone') || key.toLowerCase().includes('mobile')) &&
                     typeof data[key] === 'string' && data[key]) {
            const phone = data[key];
            if (phone.length > 7) {
              result[key] = `${phone.substring(0, 3)}****${phone.substring(phone.length - 4)}`;
            } else {
              result[key] = phone;
            }
          } else if (typeof data[key] === 'string') {
            // 其他个人信息字段，显示前两个字符和后两个字符
            const value = data[key];
            if (value.length > 4) {
              result[key] = `${value.substring(0, 2)}****${value.substring(value.length - 2)}`;
            } else {
              result[key] = value;
            }
          } else {
            result[key] = data[key];
          }
        } else if (typeof data[key] === 'object' && data[key] !== null) {
          // 递归处理嵌套对象
          result[key] = sanitizeData(data[key]);
        } else if (typeof data[key] === 'string') {
          // 检查字符串值是否包含敏感信息模式
          const value = data[key];

          // 检查是否是JWT令牌
          if (/^eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+$/.test(value)) {
            result[key] = 'JWT_TOKEN_REDACTED';
          }
          // 检查是否是API密钥格式
          else if (/^[A-Za-z0-9]{32,}$/.test(value) || /^[A-Za-z0-9-_]{40,}$/.test(value)) {
            result[key] = 'API_KEY_REDACTED';
          }
          // 检查是否是邮箱地址
          else if (/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
            const [name, domain] = value.split('@');
            result[key] = `${name.substring(0, 2)}***@${domain}`;
          }
          // 检查是否是手机号
          else if (/^1[3-9]\d{9}$/.test(value)) {
            result[key] = `${value.substring(0, 3)}****${value.substring(value.length - 4)}`;
          } else {
            result[key] = value;
          }
        } else {
          result[key] = data[key];
        }
      }
    }

    return result;
  }

  // 处理字符串类型的数据
  if (typeof data === 'string') {
    // 检查是否是JWT令牌
    if (/^eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+$/.test(data)) {
      return 'JWT_TOKEN_REDACTED';
    }
    // 检查是否是API密钥格式
    else if (/^[A-Za-z0-9]{32,}$/.test(data) || /^[A-Za-z0-9-_]{40,}$/.test(data)) {
      return 'API_KEY_REDACTED';
    }
    // 检查是否是邮箱地址
    else if (/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(data)) {
      const [name, domain] = data.split('@');
      return `${name.substring(0, 2)}***@${domain}`;
    }
    // 检查是否是手机号
    else if (/^1[3-9]\d{9}$/.test(data)) {
      return `${data.substring(0, 3)}****${data.substring(data.length - 4)}`;
    }
  }

  return data;
};

/**
 * 对JWT令牌进行脱敏处理
 * @param token JWT令牌
 * @returns 脱敏后的令牌
 */
export const sanitizeToken = (token: string): string => {
  if (!token) return token;

  // 检查是否是JWT令牌格式
  if (/^eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+$/.test(token)) {
    // JWT令牌通常由三部分组成，用点分隔：header.payload.signature
    const parts = token.split('.');
    if (parts.length === 3) {
      // 只显示header的一部分，完全隐藏payload和signature
      return `${parts[0].substring(0, 8)}...`;
    }
  }

  // 非JWT令牌或格式不正确的令牌
  if (token.length > 8) {
    return `${token.substring(0, 4)}...${token.substring(token.length - 4)}`;
  }

  return '******';
};
