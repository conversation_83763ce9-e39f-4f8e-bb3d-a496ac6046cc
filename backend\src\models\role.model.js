/**
 * 角色模型
 *
 * 定义用户角色数据结构，包括角色名称、描述和权限等信息
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} Role模型
 */
module.exports = (sequelize, DataTypes) => {
  const Role = sequelize.define('Role', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true
      }
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    is_system: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否为系统预设角色，系统预设角色不可删除'
    }
  }, {
    tableName: 'roles',
    timestamps: true
  });

  // 实例方法
  Role.prototype.isSystem = function() {
    return this.is_system === true;
  };

  Role.prototype.hasPermission = async function(code) {
    const { Permission } = sequelize.models;
    const permissions = await this.getPermissions({
      where: {
        code: code
      }
    });
    return permissions.length > 0;
  };

  // 静态方法
  Role.findByName = async function(name) {
    return await this.findOne({
      where: {
        name: name
      }
    });
  };

  Role.getSystemRoles = async function() {
    return await this.findAll({
      where: {
        is_system: true
      }
    });
  };

  // 关联关系
  Role.associate = (models) => {
    // 角色与用户的一对多关系
    Role.hasMany(models.User, {
      foreignKey: 'role_id',
      as: 'users'
    });

    // 角色与权限的多对多关系
    Role.belongsToMany(models.Permission, {
      through: 'role_permissions',
      foreignKey: 'role_id',
      otherKey: 'permission_id',
      as: 'permissions'
    });
  };

  return Role;
};
