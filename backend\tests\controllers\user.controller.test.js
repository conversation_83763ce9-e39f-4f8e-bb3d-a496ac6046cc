/**
 * 用户控制器测试
 * 
 * 测试用户相关的API端点和业务逻辑
 */

const request = require('supertest');
const app = require('../../src/app');
const { User, Role } = require('../../src/models');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

describe('用户控制器', () => {
  let testUser;
  let adminUser;
  let adminToken;
  let userToken;

  // 在所有测试前创建测试用户
  beforeAll(async () => {
    // 创建一个普通用户
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash('testpassword', salt);
    
    testUser = await User.create({
      username: 'testuser',
      password: passwordHash,
      email: '<EMAIL>',
      phone: '13800000000',
      role: 'basic_user',
      is_active: true
    });

    // 创建一个管理员用户
    const adminPasswordHash = await bcrypt.hash('adminpassword', salt);
    
    adminUser = await User.create({
      username: 'admin',
      password: adminPasswordHash,
      email: '<EMAIL>',
      phone: '13900000000',
      role: 'admin',
      is_active: true
    });

    // 生成测试用的JWT令牌
    userToken = jwt.sign(
      { id: testUser.id, role: testUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );

    adminToken = jwt.sign(
      { id: adminUser.id, role: adminUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );
  });

  // 在所有测试后清理测试用户
  afterAll(async () => {
    await User.destroy({ where: { id: testUser.id } });
    await User.destroy({ where: { id: adminUser.id } });
  });

  // 测试用户注册
  describe('注册', () => {
    test('应该成功注册新用户', async () => {
      const response = await request(app)
        .post('/api/users/register')
        .send({
          username: 'newuser',
          password: 'Password123!',
          email: '<EMAIL>',
          phone: '13800138000'
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '用户注册成功');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('username', 'newuser');
      
      // 清理创建的用户
      await User.destroy({ where: { username: 'newuser' } });
    });

    test('应该拒绝重复的用户名', async () => {
      const response = await request(app)
        .post('/api/users/register')
        .send({
          username: 'testuser', // 已存在的用户名
          password: 'Password123!',
          email: '<EMAIL>',
          phone: '13800138001'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('用户名已存在');
    });

    test('应该拒绝重复的邮箱', async () => {
      const response = await request(app)
        .post('/api/users/register')
        .send({
          username: 'uniqueuser',
          password: 'Password123!',
          email: '<EMAIL>', // 已存在的邮箱
          phone: '13800138002'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('邮箱已存在');
    });

    test('应该验证密码强度', async () => {
      const response = await request(app)
        .post('/api/users/register')
        .send({
          username: 'weakpassuser',
          password: 'weak', // 弱密码
          email: '<EMAIL>',
          phone: '13800138003'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('密码');
    });
  });

  // 测试用户登录
  describe('登录', () => {
    test('应该使用正确的凭据登录', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          username: 'testuser',
          password: 'testpassword'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '登录成功');
      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('username', 'testuser');
    });

    test('应该拒绝错误的用户名', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          username: 'nonexistentuser',
          password: 'testpassword'
        });
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('用户名或密码不正确');
    });

    test('应该拒绝错误的密码', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          username: 'testuser',
          password: 'wrongpassword'
        });
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('用户名或密码不正确');
    });
  });

  // 测试获取当前用户信息
  describe('获取当前用户信息', () => {
    test('应该返回已认证用户的信息', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('id', testUser.id);
      expect(response.body.user).toHaveProperty('username', 'testuser');
      expect(response.body.user).toHaveProperty('email', '<EMAIL>');
      expect(response.body.user).not.toHaveProperty('password'); // 不应返回密码
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/users/me');
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('未授权');
    });

    test('应该拒绝无效的令牌', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', 'Bearer invalid_token');
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
    });
  });

  // 测试更新用户信息
  describe('更新用户信息', () => {
    test('应该更新已认证用户的信息', async () => {
      const response = await request(app)
        .put('/api/users/me')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          email: '<EMAIL>',
          phone: '13811112222'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '用户信息更新成功');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('email', '<EMAIL>');
      expect(response.body.user).toHaveProperty('phone', '13811112222');
      
      // 恢复原始数据
      await testUser.update({
        email: '<EMAIL>',
        phone: '13800000000'
      });
    });

    test('应该拒绝更新用户名', async () => {
      const response = await request(app)
        .put('/api/users/me')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          username: 'newusername'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('用户名不能修改');
    });
  });

  // 测试管理员功能
  describe('管理员功能', () => {
    test('管理员应该能获取所有用户', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('users');
      expect(Array.isArray(response.body.users)).toBe(true);
      expect(response.body.users.length).toBeGreaterThan(0);
    });

    test('普通用户不应该能获取所有用户', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test('管理员应该能获取特定用户', async () => {
      const response = await request(app)
        .get(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('id', testUser.id);
      expect(response.body.user).toHaveProperty('username', 'testuser');
    });

    test('普通用户不应该能获取特定用户', async () => {
      const response = await request(app)
        .get(`/api/users/${adminUser.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });
  });
});
