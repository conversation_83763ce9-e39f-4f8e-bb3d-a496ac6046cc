'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // 修改api_key字段类型为TEXT
    await queryInterface.changeColumn('ai_assistants', 'api_key', {
      type: Sequelize.TEXT,
      allowNull: false
    });
  },

  async down (queryInterface, Sequelize) {
    // 恢复api_key字段类型为VARCHAR(255)
    await queryInterface.changeColumn('ai_assistants', 'api_key', {
      type: Sequelize.STRING(255),
      allowNull: false
    });
  }
};
