/**
 * 时间轴事件模型
 * 
 * 定义时间轴事件的数据结构和关联关系
 */

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const TimelineEvent = sequelize.define('TimelineEvent', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    year: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '事件年份'
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '事件标题'
    },
    description: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '事件简短描述'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '事件详细内容'
    },
    level: {
      type: DataTypes.ENUM('national', 'family', 'personal'),
      allowNull: false,
      defaultValue: 'personal',
      comment: '事件级别：国家级、家族级、个人级'
    },
    icon: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '事件图标URL'
    }
  }, {
    tableName: 'timeline_events',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return TimelineEvent;
};
