/**
 * 展板图片服务
 *
 * 处理展板图片的CRUD操作
 */

import * as apiService from './api-service'

/**
 * 获取展板图片列表
 * @param type 展板类型（home: 首页, family: 家族专题, personal: 个人专题）
 * @param sub_type 子类型，如个人专题中的具体人物ID
 * @param status 状态（active: 启用, inactive: 禁用）
 * @returns 展板图片列表
 */
export const getExhibitionBoards = async (
  type?: 'home' | 'family' | 'personal',
  sub_type?: string,
  status?: 'active' | 'inactive'
): Promise<any[]> => {
  const params: any = {}
  if (type) params.type = type
  if (sub_type) params.sub_type = sub_type
  if (status) params.status = status

  const response = await apiService.get('/api/exhibition-boards', { params })
  return response.data || []
}

/**
 * 获取展板图片详情
 * @param id 展板图片ID
 * @returns 展板图片详情
 */
export const getExhibitionBoardById = async (id: number | string): Promise<any> => {
  const response = await apiService.get(`/api/exhibition-boards/${id}`)
  return response.data
}

/**
 * 创建展板图片
 * @param data 展板图片数据
 * @returns 创建的展板图片
 */
export const createExhibitionBoard = async (data: any): Promise<any> => {
  const response = await apiService.post('/api/exhibition-boards', data)
  return response.data
}

/**
 * 更新展板图片
 * @param id 展板图片ID
 * @param data 展板图片数据
 * @returns 更新的展板图片
 */
export const updateExhibitionBoard = async (id: number | string, data: any): Promise<any> => {
  const response = await apiService.put(`/api/exhibition-boards/${id}`, data)
  return response.data
}

/**
 * 删除展板图片
 * @param id 展板图片ID
 * @returns 操作结果
 */
export const deleteExhibitionBoard = async (id: number | string): Promise<any> => {
  const response = await apiService.del(`/api/exhibition-boards/${id}`)
  return response
}

/**
 * 上传展板图片
 * @param file 图片文件
 * @returns 上传结果，包含图片URL
 */
export const uploadExhibitionImage = async (file: File): Promise<{ url: string }> => {
  const formData = new FormData()
  formData.append('image', file)

  try {
    console.log('开始上传展板图片:', { fileName: file.name, fileSize: file.size, fileType: file.type });

    // 检查formData内容
    console.log('FormData内容:');
    for (const pair of formData.entries()) {
      console.log(pair[0], pair[1]);
    }

    // 检查token
    const token = localStorage.getItem('hefamily_token');
    console.log('当前token:', token ? '已设置' : '未设置');

    // 检查用户信息
    const userInfo = localStorage.getItem('hefamily_user_info');
    console.log('当前用户信息:', userInfo);

    // 添加更多日志
    console.log('准备调用upload函数，URL为:', '/api/exhibition-boards/upload');

    const response = await apiService.upload<{ url: string }>('/api/exhibition-boards/upload', formData);
    console.log('上传展板图片成功:', response);
    return { url: response.data?.url || '' };
  } catch (error) {
    console.error('上传展板图片失败:', error);
    console.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
    throw error;
  }
}

/**
 * 更新展板图片顺序
 * @param orders 顺序数据，包含id和order
 * @returns 操作结果
 */
export const updateExhibitionBoardOrder = async (orders: { id: number, order: number }[]): Promise<any> => {
  const response = await apiService.put('/api/exhibition-boards/order/batch', { orders })
  return response
}

export default {
  getExhibitionBoards,
  getExhibitionBoardById,
  createExhibitionBoard,
  updateExhibitionBoard,
  deleteExhibitionBoard,
  uploadExhibitionImage,
  updateExhibitionBoardOrder
}
