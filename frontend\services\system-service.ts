/**
 * 系统服务
 *
 * 处理系统配置相关的API请求
 */

import apiService from './api-service';

/**
 * 系统配置接口定义
 */
export interface SystemConfig {
  id: number;
  system_name: string;
  system_logo: string;
  system_description: string;
  password_min_length: number;
  password_require_uppercase: boolean;
  password_require_lowercase: boolean;
  password_require_number: boolean;
  password_require_special: boolean;
  max_login_attempts: number;
  lockout_duration: number;
  session_timeout: number;
  file_upload_max_size: number;
  allowed_file_types: string;
  created_at: string;
  updated_at: string;
}

/**
 * 系统统计信息接口定义
 */
export interface SystemStats {
  user_count: number;
  active_user_count: number;
  knowledge_base_count: number;
  system_knowledge_base_count: number;
  user_knowledge_base_count: number;
  file_count: number;
  approved_file_count: number;
  pending_file_count: number;
  activity_count: number;
  published_activity_count: number;
  draft_activity_count: number;
  comment_count: number;
  approved_comment_count: number;
  pending_comment_count: number;
  storage_usage: number;
  last_updated: string;
}

/**
 * 获取系统配置
 * @returns 系统配置
 */
export async function getSystemConfig(): Promise<SystemConfig> {
  const response = await apiService.get('/system/config');
  if (!response.success) {
    throw new Error(response.message || '获取系统配置失败');
  }
  return response.data;
}

/**
 * 更新系统配置
 * @param data 更新数据
 * @returns 更新后的系统配置
 */
export async function updateSystemConfig(data: Partial<SystemConfig>): Promise<SystemConfig> {
  const response = await apiService.put('/system/config', data);
  if (!response.success) {
    throw new Error(response.message || '更新系统配置失败');
  }
  return response.data;
}

/**
 * 获取系统统计信息
 * @returns 系统统计信息
 */
export async function getSystemStats(): Promise<SystemStats> {
  const response = await apiService.get('/system/stats');
  if (!response.success) {
    throw new Error(response.message || '获取系统统计信息失败');
  }
  return response.data;
}
