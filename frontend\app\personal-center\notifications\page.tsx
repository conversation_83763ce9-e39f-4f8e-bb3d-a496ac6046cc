"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "@/components/ui/use-toast"

/**
 * 通知中心页面
 *
 * 显示用户的所有通知
 */
export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { isLoggedIn } = useAuth()
  const router = useRouter()



  // 加载通知数据
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        setIsLoading(true);
        console.log("通知页面: 开始获取通知数据");

        // 导入通知服务
        const notificationService = await import('@/services/notification-service');

        // 调用API获取通知数据
        const response = await notificationService.getNotifications({ limit: 100 });
        console.log("通知页面: API响应", response);

        if (response && response.notifications) {
          console.log("通知页面: 获取到的通知数据:", response.notifications);
          setNotifications(response.notifications);
        } else {
          console.warn("通知页面: API返回的通知数据为空或格式不正确");
          setNotifications([]);
        }
      } catch (error) {
        console.error("通知页面: 获取通知失败:", error);
        toast({
          title: "获取通知失败",
          description: "无法获取通知数据，请稍后重试",
          variant: "destructive"
        });
        setNotifications([]);
      } finally {
        setIsLoading(false);
      }
    }

    if (isLoggedIn) {
      fetchNotifications()
    } else {
      // 未登录时重定向到首页
      router.push("/")
      toast({
        title: "请先登录",
        description: "您需要登录才能查看通知",
        variant: "destructive"
      })
    }
  }, [isLoggedIn, router])

  // 标记通知为已读
  const markAsRead = async (id: number) => {
    try {
      // 调用API标记通知为已读
      const notificationService = await import('@/services/notification-service');
      await notificationService.markNotificationAsRead(id.toString());

      // 更新本地状态
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id
            ? { ...notification, read: true }
            : notification
        )
      );

      toast({
        title: "标记成功",
        description: "通知已标记为已读"
      });
    } catch (error) {
      console.error("标记通知为已读失败:", error);
      toast({
        title: "操作失败",
        description: "无法标记通知为已读，请稍后重试",
        variant: "destructive"
      });
    }
  }

  // 删除通知
  const deleteNotification = async (id: number) => {
    try {
      // 调用API删除通知
      const notificationService = await import('@/services/notification-service');
      await notificationService.deleteNotification(id);

      // 更新本地状态
      setNotifications(prev => prev.filter(notification => notification.id !== id));

      toast({
        title: "删除成功",
        description: "通知已删除"
      });
    } catch (error) {
      console.error("删除通知失败:", error);
      toast({
        title: "操作失败",
        description: "无法删除通知，请稍后重试",
        variant: "destructive"
      });
    }
  }

  // 全部标记为已读
  const markAllAsRead = async () => {
    try {
      console.log("通知页面: 开始标记所有通知为已读");

      // 调用API标记所有通知为已读
      const notificationService = await import('@/services/notification-service');
      const result = await notificationService.markAllNotificationsAsRead();
      console.log("通知页面: 标记所有通知为已读API响应:", result);

      // 更新本地状态
      setNotifications(prev => prev.map(notification => ({ ...notification, read: true })));

      toast({
        title: "标记成功",
        description: "所有通知已标记为已读"
      });

      // 延迟刷新页面以确保API操作完成
      setTimeout(() => {
        console.log("通知页面: 刷新页面");
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("通知页面: 标记所有通知为已读失败:", error);
      toast({
        title: "操作失败",
        description: "无法标记所有通知为已读，请稍后重试",
        variant: "destructive"
      });
    }
  }

  // 获取未读通知数量
  const unreadCount = notifications.filter(notification => !notification.read).length

  return (
    <div className="container mx-auto py-8">
      {/* 返回按钮 */}
      <div className="mb-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
          返回
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>通知中心</CardTitle>
          <CardDescription>查看您的所有通知</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div>
              {unreadCount > 0 && (
                <span className="text-sm text-gray-500">
                  您有 <span className="font-bold text-red-500">{unreadCount}</span> 条未读通知
                </span>
              )}
            </div>
            <Button
              variant="outline"
              onClick={markAllAsRead}
              disabled={unreadCount === 0}
            >
              全部标记为已读
            </Button>
          </div>

          <Tabs defaultValue="all">
            <TabsList className="mb-4">
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="unread">未读</TabsTrigger>
              <TabsTrigger value="system">系统通知</TabsTrigger>
              <TabsTrigger value="file">文件通知</TabsTrigger>
              <TabsTrigger value="file_review">审核通知</TabsTrigger>
              <TabsTrigger value="comment">评论通知</TabsTrigger>
            </TabsList>

            <TabsContent value="all">
              {renderNotificationList(notifications, markAsRead, deleteNotification, isLoading)}
            </TabsContent>

            <TabsContent value="unread">
              {renderNotificationList(
                notifications.filter(n => !n.read),
                markAsRead,
                deleteNotification,
                isLoading
              )}
            </TabsContent>

            <TabsContent value="system">
              {renderNotificationList(
                notifications.filter(n => n.type === 'system'),
                markAsRead,
                deleteNotification,
                isLoading
              )}
            </TabsContent>

            <TabsContent value="file">
              {renderNotificationList(
                notifications.filter(n => n.type === 'file'),
                markAsRead,
                deleteNotification,
                isLoading
              )}
            </TabsContent>

            <TabsContent value="comment">
              {renderNotificationList(
                notifications.filter(n => n.type === 'comment'),
                markAsRead,
                deleteNotification,
                isLoading
              )}
            </TabsContent>

            <TabsContent value="file_review">
              {renderNotificationList(
                notifications.filter(n => ['file_review', 'file_review_result'].includes(n.type)),
                markAsRead,
                deleteNotification,
                isLoading
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

// 渲染通知列表
function renderNotificationList(
  notifications: any[],
  markAsRead: (id: number) => void,
  deleteNotification: (id: number) => void,
  isLoading: boolean
) {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (notifications.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        暂无通知
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`p-4 border rounded-lg ${notification.read ? 'bg-white' : 'bg-blue-50'}`}
        >
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-medium">{notification.title}</h3>
              <p className="text-gray-600 mt-1">{notification.content}</p>
              <p className="text-xs text-gray-400 mt-2">
                {new Date(notification.created_at).toLocaleString()}
              </p>
            </div>
            <div className="flex space-x-2">
              {!notification.read && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => markAsRead(notification.id)}
                >
                  标记已读
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => deleteNotification(notification.id)}
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
