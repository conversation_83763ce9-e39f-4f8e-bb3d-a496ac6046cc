"use client"

import React, { useState, useEffect } from "react"
import {
  File as FileIcon,
  Download,
  Calendar,
  User,
  Database,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import { getFileById, downloadFile, type File } from "@/services/file-service"
import { useAuth } from "@/contexts/auth-context"
import { handleAuthenticatedAction } from "@/utils/auth-utils"

/**
 * 文件详情组件属性
 */
interface FileDetailProps {
  fileId: string
}

/**
 * 文件详情组件
 *
 * 显示文件的详细信息，包括元数据、摘要和详细描述
 */
export function FileDetail({ fileId }: FileDetailProps) {
  // 状态
  const [file, setFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  /**
   * 加载文件详情
   */
  useEffect(() => {
    const loadFileDetail = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const fileData = await getFileById(fileId)
        setFile(fileData)
      } catch (err: any) {
        console.error("加载文件详情失败:", err)
        setError(err.response?.data?.message || "无法加载文件详情，请稍后再试")
      } finally {
        setIsLoading(false)
      }
    }

    if (fileId) {
      loadFileDetail()
    }
  }, [fileId])

  // 获取认证状态
  const { isLoggedIn } = useAuth()

  /**
   * 处理文件下载
   */
  const handleDownload = () => {
    if (!file) return

    // 使用通用的认证操作处理函数
    handleAuthenticatedAction(
      isLoggedIn,
      async () => {
        try {
          await downloadFile(file.id, file.original_name)

          toast({
            title: "下载开始",
            description: "文件开始下载，请稍候",
          })
        } catch (error) {
          console.error("下载文件失败:", error)
          toast({
            title: "下载失败",
            description: "无法下载文件，请稍后再试",
            variant: "destructive"
          })
        }
      },
      "请先登录后再下载文件"
    )
  }

  /**
   * 获取文件图标
   */
  const getFileIcon = () => {
    if (!file) return <FileIcon className="h-12 w-12 text-gray-400" />

    const type = file.type.split('/')[1] || file.type

    // 根据文件类型返回不同图标
    switch (type) {
      case 'pdf':
        return <FileIcon className="h-12 w-12 text-red-500" />
      case 'doc':
      case 'docx':
      case 'msword':
      case 'vnd.openxmlformats-officedocument.wordprocessingml.document':
        return <FileIcon className="h-12 w-12 text-blue-500" />
      case 'xls':
      case 'xlsx':
      case 'vnd.ms-excel':
      case 'vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return <FileIcon className="h-12 w-12 text-green-500" />
      case 'ppt':
      case 'pptx':
      case 'vnd.ms-powerpoint':
      case 'vnd.openxmlformats-officedocument.presentationml.presentation':
        return <FileIcon className="h-12 w-12 text-orange-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <FileIcon className="h-12 w-12 text-purple-500" />
      default:
        return <FileIcon className="h-12 w-12 text-gray-500" />
    }
  }

  /**
   * 获取状态标签
   */
  const getStatusBadge = () => {
    if (!file) return null

    switch (file.status) {
      case 'approved':
        return (
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">已批准</Badge>
          </div>
        )
      case 'rejected':
        return (
          <div className="flex items-center">
            <XCircle className="h-4 w-4 text-red-500 mr-1" />
            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">已拒绝</Badge>
          </div>
        )
      case 'pending':
        return (
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-yellow-500 mr-1" />
            <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">待审核</Badge>
          </div>
        )
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            {file.status}
          </Badge>
        )
    }
  }

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 加载中状态
  if (isLoading) {
    return (
      <div className="w-full p-6 border rounded-lg">
        <div className="flex items-center space-x-4 mb-6">
          <Skeleton className="h-12 w-12 rounded" />
          <div className="space-y-2">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>

        <div className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
      </div>
    )
  }

  // 错误状态
  if (error) {
    return (
      <div className="w-full p-6 border rounded-lg bg-red-50">
        <div className="flex items-center text-red-600 mb-2">
          <AlertCircle className="h-5 w-5 mr-2" />
          <h3 className="font-medium">加载失败</h3>
        </div>
        <p className="text-red-600">{error}</p>
      </div>
    )
  }

  // 文件不存在
  if (!file) {
    return (
      <div className="w-full p-6 border rounded-lg bg-gray-50">
        <div className="flex items-center justify-center flex-col py-8">
          <FileIcon className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="font-medium text-gray-600">文件不存在或已被删除</h3>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full border rounded-lg overflow-hidden">
      {/* 文件头部 */}
      <div className="p-6 bg-gray-50 border-b">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            {getFileIcon()}
            <div>
              <h2 className="text-xl font-semibold">{file.original_name}</h2>
              <div className="flex items-center mt-1 space-x-3">
                <span className="text-sm text-gray-500">{formatFileSize(file.size)}</span>
                {getStatusBadge()}
              </div>
            </div>
          </div>

          <Button onClick={handleDownload} className="bg-[#1e7a43] hover:bg-[#1e7a43]/90">
            <Download className="h-4 w-4 mr-2" />
            下载
          </Button>
        </div>
      </div>

      {/* 文件信息 */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-2">文件信息</h3>
            <div className="space-y-2">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-sm">上传时间：{new Date(file.created_at).toLocaleString()}</span>
              </div>
              <div className="flex items-center">
                <User className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-sm">上传者：{file.uploader?.username || '未知'}</span>
              </div>
              <div className="flex items-center">
                <Database className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-sm">知识库：{file.knowledgeBase?.name || '未知'}</span>
              </div>
            </div>
          </div>

          {file.status === 'rejected' && file.reject_reason && (
            <div>
              <h3 className="text-sm font-medium text-red-500 mb-2">拒绝原因</h3>
              <div className="p-3 bg-red-50 border border-red-200 rounded-md text-sm text-red-700">
                {file.reject_reason}
              </div>
            </div>
          )}

          {file.reviewer && (
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">审核信息</h3>
              <div className="space-y-2">
                <div className="flex items-center">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-sm">审核者：{file.reviewer.username}</span>
                </div>
                {file.review_time && (
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm">审核时间：{new Date(file.review_time).toLocaleString()}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 移除文件摘要和详细描述部分 */}
      </div>
    </div>
  )
}

export default FileDetail
