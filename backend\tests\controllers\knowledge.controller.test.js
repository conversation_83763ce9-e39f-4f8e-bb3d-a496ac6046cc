/**
 * 知识库控制器测试
 * 
 * 测试知识库相关的API端点和业务逻辑
 */

const request = require('supertest');
const app = require('../../src/app');
const { KnowledgeBase, User, File } = require('../../src/models');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

describe('知识库控制器', () => {
  let testUser;
  let adminUser;
  let anotherUser;
  let systemKnowledgeBase;
  let userKnowledgeBase;
  let userToken;
  let adminToken;
  let anotherUserToken;

  // 在所有测试前创建测试数据
  beforeAll(async () => {
    // 创建测试用户
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash('testpassword', salt);
    
    testUser = await User.create({
      username: 'kbuser',
      password: passwordHash,
      email: '<EMAIL>',
      phone: '13800000002',
      role: 'basic_user',
      is_active: true
    });

    // 创建另一个测试用户
    anotherUser = await User.create({
      username: 'anotherkbuser',
      password: passwordHash,
      email: '<EMAIL>',
      phone: '13800000003',
      role: 'basic_user',
      is_active: true
    });

    // 创建管理员用户
    const adminPasswordHash = await bcrypt.hash('adminpassword', salt);
    
    adminUser = await User.create({
      username: 'kbadmin',
      password: adminPasswordHash,
      email: '<EMAIL>',
      phone: '13900000002',
      role: 'admin',
      is_active: true
    });

    // 创建系统知识库
    systemKnowledgeBase = await KnowledgeBase.create({
      name: 'Test System Knowledge Base',
      description: 'Test system knowledge base for unit testing',
      type: 'system',
      creator_id: adminUser.id,
      status: 'active'
    });

    // 创建用户知识库
    userKnowledgeBase = await KnowledgeBase.create({
      name: 'Test User Knowledge Base',
      description: 'Test user knowledge base for unit testing',
      type: 'user',
      creator_id: testUser.id,
      status: 'active'
    });

    // 生成测试用的JWT令牌
    userToken = jwt.sign(
      { id: testUser.id, role: testUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );

    adminToken = jwt.sign(
      { id: adminUser.id, role: adminUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );

    anotherUserToken = jwt.sign(
      { id: anotherUser.id, role: anotherUser.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await KnowledgeBase.destroy({ where: { id: systemKnowledgeBase.id } });
    await KnowledgeBase.destroy({ where: { id: userKnowledgeBase.id } });
    await User.destroy({ where: { id: testUser.id } });
    await User.destroy({ where: { id: adminUser.id } });
    await User.destroy({ where: { id: anotherUser.id } });
  });

  // 测试获取知识库列表
  describe('获取知识库列表', () => {
    test('应该返回系统知识库列表', async () => {
      const response = await request(app)
        .get('/api/knowledge?type=system')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('knowledgeBases');
      expect(Array.isArray(response.body.data.knowledgeBases)).toBe(true);
      
      // 验证返回的知识库数据
      const kb = response.body.data.knowledgeBases.find(k => k.id === systemKnowledgeBase.id);
      expect(kb).toBeDefined();
      expect(kb).toHaveProperty('name', 'Test System Knowledge Base');
      expect(kb).toHaveProperty('type', 'system');
    });

    test('应该返回用户的知识库列表', async () => {
      const response = await request(app)
        .get('/api/knowledge/my')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('knowledgeBases');
      expect(Array.isArray(response.body.data.knowledgeBases)).toBe(true);
      
      // 验证返回的知识库数据
      const kb = response.body.data.knowledgeBases.find(k => k.id === userKnowledgeBase.id);
      expect(kb).toBeDefined();
      expect(kb).toHaveProperty('name', 'Test User Knowledge Base');
      expect(kb).toHaveProperty('type', 'user');
      expect(kb).toHaveProperty('creator_id', testUser.id);
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/knowledge');
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('未授权');
    });
  });

  // 测试获取知识库详情
  describe('获取知识库详情', () => {
    test('应该返回系统知识库的详情', async () => {
      const response = await request(app)
        .get(`/api/knowledge/${systemKnowledgeBase.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', systemKnowledgeBase.id);
      expect(response.body.data).toHaveProperty('name', 'Test System Knowledge Base');
      expect(response.body.data).toHaveProperty('type', 'system');
    });

    test('创建者应该能获取自己的用户知识库详情', async () => {
      const response = await request(app)
        .get(`/api/knowledge/${userKnowledgeBase.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', userKnowledgeBase.id);
      expect(response.body.data).toHaveProperty('name', 'Test User Knowledge Base');
      expect(response.body.data).toHaveProperty('type', 'user');
    });

    test('非创建者不应该能获取用户知识库详情', async () => {
      const response = await request(app)
        .get(`/api/knowledge/${userKnowledgeBase.id}`)
        .set('Authorization', `Bearer ${anotherUserToken}`);
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test('管理员应该能获取任何知识库的详情', async () => {
      const response = await request(app)
        .get(`/api/knowledge/${userKnowledgeBase.id}`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', userKnowledgeBase.id);
    });

    test('应该返回404当知识库不存在', async () => {
      const response = await request(app)
        .get('/api/knowledge/9999') // 不存在的ID
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('不存在');
    });
  });

  // 测试创建知识库
  describe('创建知识库', () => {
    test('用户应该能创建用户知识库', async () => {
      const response = await request(app)
        .post('/api/knowledge')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          name: 'New User Knowledge Base',
          description: 'New user knowledge base created in test',
          type: 'user'
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '知识库创建成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('name', 'New User Knowledge Base');
      expect(response.body.data).toHaveProperty('type', 'user');
      expect(response.body.data).toHaveProperty('creator_id', testUser.id);
      
      // 清理创建的知识库
      await KnowledgeBase.destroy({ where: { name: 'New User Knowledge Base' } });
    });

    test('普通用户不应该能创建系统知识库', async () => {
      const response = await request(app)
        .post('/api/knowledge')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          name: 'Unauthorized System Knowledge Base',
          description: 'This should not work',
          type: 'system'
        });
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test('管理员应该能创建系统知识库', async () => {
      const response = await request(app)
        .post('/api/knowledge')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'New System Knowledge Base',
          description: 'New system knowledge base created in test',
          type: 'system'
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '知识库创建成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('name', 'New System Knowledge Base');
      expect(response.body.data).toHaveProperty('type', 'system');
      
      // 清理创建的知识库
      await KnowledgeBase.destroy({ where: { name: 'New System Knowledge Base' } });
    });

    test('应该验证知识库名称', async () => {
      const response = await request(app)
        .post('/api/knowledge')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          name: '', // 空名称
          description: 'Invalid knowledge base',
          type: 'user'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('名称不能为空');
    });
  });

  // 测试更新知识库
  describe('更新知识库', () => {
    test('创建者应该能更新自己的用户知识库', async () => {
      const response = await request(app)
        .put(`/api/knowledge/${userKnowledgeBase.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          name: 'Updated User Knowledge Base',
          description: 'Updated description'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '知识库更新成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('name', 'Updated User Knowledge Base');
      expect(response.body.data).toHaveProperty('description', 'Updated description');
      
      // 恢复原始数据
      await userKnowledgeBase.update({
        name: 'Test User Knowledge Base',
        description: 'Test user knowledge base for unit testing'
      });
    });

    test('非创建者不应该能更新用户知识库', async () => {
      const response = await request(app)
        .put(`/api/knowledge/${userKnowledgeBase.id}`)
        .set('Authorization', `Bearer ${anotherUserToken}`)
        .send({
          name: 'Unauthorized Update',
          description: 'This should not work'
        });
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    test('管理员应该能更新任何知识库', async () => {
      const response = await request(app)
        .put(`/api/knowledge/${userKnowledgeBase.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Admin Updated Knowledge Base',
          description: 'Updated by admin'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '知识库更新成功');
      
      // 恢复原始数据
      await userKnowledgeBase.update({
        name: 'Test User Knowledge Base',
        description: 'Test user knowledge base for unit testing'
      });
    });
  });

  // 测试删除知识库
  describe('删除知识库', () => {
    let tempKnowledgeBase;

    beforeEach(async () => {
      // 创建临时知识库用于删除测试
      tempKnowledgeBase = await KnowledgeBase.create({
        name: 'Temp Knowledge Base',
        description: 'Temporary knowledge base for delete test',
        type: 'user',
        creator_id: testUser.id,
        status: 'active'
      });
    });

    test('创建者应该能删除自己的用户知识库', async () => {
      const response = await request(app)
        .delete(`/api/knowledge/${tempKnowledgeBase.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '知识库删除成功');
      
      // 验证知识库已被删除
      const kb = await KnowledgeBase.findByPk(tempKnowledgeBase.id);
      expect(kb).toBeNull();
    });

    test('非创建者不应该能删除用户知识库', async () => {
      // 重新创建临时知识库
      tempKnowledgeBase = await KnowledgeBase.create({
        name: 'Temp Knowledge Base',
        description: 'Temporary knowledge base for delete test',
        type: 'user',
        creator_id: testUser.id,
        status: 'active'
      });

      const response = await request(app)
        .delete(`/api/knowledge/${tempKnowledgeBase.id}`)
        .set('Authorization', `Bearer ${anotherUserToken}`);
      
      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
      
      // 清理临时知识库
      await tempKnowledgeBase.destroy();
    });

    test('管理员应该能删除任何知识库', async () => {
      // 重新创建临时知识库
      tempKnowledgeBase = await KnowledgeBase.create({
        name: 'Temp Knowledge Base',
        description: 'Temporary knowledge base for delete test',
        type: 'user',
        creator_id: testUser.id,
        status: 'active'
      });

      const response = await request(app)
        .delete(`/api/knowledge/${tempKnowledgeBase.id}`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', '知识库删除成功');
      
      // 验证知识库已被删除
      const kb = await KnowledgeBase.findByPk(tempKnowledgeBase.id);
      expect(kb).toBeNull();
    });
  });
});
