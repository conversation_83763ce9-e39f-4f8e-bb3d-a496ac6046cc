/**
 * 评论相关路由
 *
 * 处理评论的创建、查询、审核、删除等请求
 */

const express = require('express');
const router = express.Router();
const commentController = require('../controllers/comment.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/authMiddleware');

// 获取主题评论列表 (所有人可访问)
router.get('/topic/:topicType/:topicId',
  commentController.getCommentsByTopic
);
// 获取评论列表 (需要认证)
router.get('/',
  authMiddleware,
  commentController.getComments
);


// 创建评论 (需要认证)
router.post('/',
  authMiddleware,
  commentController.createComment
);


// 审核评论 (需要认证和权限)
router.put('/:id/review',
  authMiddleware,
  checkPermission('comment:review'),
  commentController.reviewComment
);


// 删除评论 (需要认证)
router.delete('/:id',
  authMiddleware,
  commentController.deleteComment
);

module.exports = router;
