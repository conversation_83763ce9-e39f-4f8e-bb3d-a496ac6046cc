/**
 * 评论相关API
 * 
 * 处理评论的创建、查询、审核、删除等请求
 */

import request from '../index';

/**
 * 获取评论列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.topic_id - 主题ID
 * @param {string} params.topic_type - 主题类型
 * @param {string} params.status - 评论状态
 * @returns {Promise} 评论列表
 */
export function getCommentList(params) {
  return request({
    url: '/comments',
    method: 'get',
    params
  });
}

/**
 * 获取主题评论列表
 * @param {string} topicType - 主题类型
 * @param {string} topicId - 主题ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @returns {Promise} 评论列表
 */
export function getTopicCommentList(topicType, topicId, params) {
  return request({
    url: `/comments/topic/${topicType}/${topicId}`,
    method: 'get',
    params
  });
}

/**
 * 创建评论
 * @param {Object} data - 评论信息
 * @param {string} data.topic_id - 主题ID
 * @param {string} data.topic_type - 主题类型
 * @param {string} data.topic_title - 主题标题
 * @param {string} data.content - 评论内容
 * @returns {Promise} 创建结果
 */
export function createComment(data) {
  return request({
    url: '/comments',
    method: 'post',
    data
  });
}

/**
 * 审核评论
 * @param {string} id - 评论ID
 * @param {Object} data - 审核信息
 * @param {string} data.status - 审核状态
 * @param {string} data.reason - 拒绝原因
 * @returns {Promise} 审核结果
 */
export function reviewComment(id, data) {
  return request({
    url: `/comments/${id}/review`,
    method: 'put',
    data
  });
}

/**
 * 删除评论
 * @param {string} id - 评论ID
 * @returns {Promise} 删除结果
 */
export function deleteComment(id) {
  return request({
    url: `/comments/${id}`,
    method: 'delete'
  });
}

export default {
  getCommentList,
  getTopicCommentList,
  createComment,
  reviewComment,
  deleteComment
};
