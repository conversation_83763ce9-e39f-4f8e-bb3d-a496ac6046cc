/**
 * 测试将文件上传到Dify知识库
 *
 * 这个脚本直接从数据库中获取文件信息，然后尝试上传到Dify知识库
 */

// 导入必要的模块
const fs = require('fs');
const axios = require('axios');
const FormData = require('form-data');
const { Sequelize } = require('sequelize');
require('dotenv').config();

// 连接数据库
console.log('数据库配置:', {
  DB_NAME: process.env.DB_NAME || 'hefamily_dev',
  DB_USER: process.env.DB_USERNAME || 'root',
  DB_HOST: process.env.DB_HOST || 'localhost'
});

const sequelize = new Sequelize(
  process.env.DB_NAME || 'hefamily_dev',
  process.env.DB_USERNAME || 'root',
  process.env.DB_PASSWORD || 'Misaya9987.',
  {
    host: process.env.DB_HOST || 'localhost',
    dialect: 'mysql',
    logging: console.log, // 启用SQL日志
    dialectOptions: {
      charset: 'utf8mb4'
    }
  }
);

// 定义文件模型
const File = sequelize.define('File', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: Sequelize.STRING,
  original_name: Sequelize.STRING,
  path: Sequelize.STRING,
  type: Sequelize.STRING,
  size: Sequelize.INTEGER,
  status: Sequelize.STRING,
  dify_task_id: Sequelize.STRING,
  analysis_status: Sequelize.STRING,
  analysis_error: Sequelize.TEXT
}, {
  tableName: 'files',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 定义AI助手模型
const AIAssistant = sequelize.define('AIAssistant', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: Sequelize.STRING,
  type: Sequelize.STRING,
  status: Sequelize.STRING,
  api_endpoint: Sequelize.STRING,
  api_key: Sequelize.STRING
}, {
  tableName: 'ai_assistants',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

/**
 * 将文件上传到Dify知识库
 * @param {Object} file - 文件对象
 * @returns {Promise<boolean>} 上传是否成功
 */
async function uploadFileToDify(file) {
  console.log(`开始上传文件到Dify知识库: ${file.id}, ${file.original_name}`);

  try {
    // 检查文件是否存在
    if (!fs.existsSync(file.path)) {
      console.error(`文件不存在: ${file.path}`);
      return false;
    }

    console.log(`文件存在: ${file.path}`);

    // 查询知识库文件分析助手
    const assistant = await AIAssistant.findOne({
      where: {
        type: 'knowledge-file',
        status: 'active'
      }
    });

    if (!assistant) {
      console.error('未找到知识库文件分析助手，请在系统管理-AI管理中配置');

      // 创建一个默认的助手
      console.log('创建默认的知识库文件分析助手...');
      const defaultAssistant = await AIAssistant.create({
        name: '知识库文件分析助手',
        type: 'knowledge-file',
        status: 'active',
        api_endpoint: 'https://ai.glab.vip/v1',
        api_key: 'dataset-DLFJlUe25VUHOwMO4HbO4hQk'
      });

      console.log(`已创建默认的知识库文件分析助手: ${defaultAssistant.id}`);
      return await uploadFileToDify(file);
    }

    console.log(`找到知识库文件分析助手: ${assistant.name}`);
    console.log(`API端点: ${assistant.api_endpoint}`);
    console.log(`API密钥: ${assistant.api_key ? '已设置' : '未设置'}`);

    // 准备FormData
    const formData = new FormData();

    // 添加文件
    formData.append('file', fs.createReadStream(file.path), file.original_name);

    // 添加处理规则
    const processRule = {
      indexing_technique: "high_quality",
      process_rule: {
        rules: {
          pre_processing_rules: [
            { id: "remove_extra_spaces", enabled: true },
            { id: "remove_urls_emails", enabled: true }
          ],
          segmentation: {
            separator: "###",
            max_tokens: 500
          }
        },
        mode: "custom"
      }
    };

    formData.append('data', JSON.stringify(processRule), { type: 'text/plain' });

    // 设置API端点
    const datasetId = process.env.DIFY_DATASET_ID || '77199451-730a-4d79-a1c9-9b9e6bfcd747';
    const apiEndpoint = `https://ai.glab.vip/v1/datasets/${datasetId}/document/create-by-file`;

    // 使用API密钥
    const apiKey = assistant.api_key || "dataset-DLFJlUe25VUHOwMO4HbO4hQk";

    console.log(`准备发送请求到: ${apiEndpoint}`);
    console.log(`使用API密钥: ${apiKey.substring(0, 5)}...`);

    // 发送请求
    const response = await axios.post(
      apiEndpoint,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          ...formData.getHeaders()
        },
        timeout: 120000 // 120秒超时
      }
    );

    console.log(`请求已发送，状态码: ${response.status}`);
    console.log(`响应数据:`, response.data);

    if (!response.data || !response.data.document) {
      console.error('上传文件到Dify知识库失败，响应数据不包含document字段');
      return false;
    }

    // 更新文件记录
    const documentId = response.data.document.id;
    await file.update({
      dify_task_id: documentId,
      analysis_status: 'completed',
      analysis_completed_at: new Date()
    });

    console.log(`文件已成功上传到Dify知识库，文档ID: ${documentId}`);
    return true;
  } catch (error) {
    console.error(`上传文件到Dify知识库失败:`, error);

    // 更新文件状态
    if (file) {
      await file.update({
        analysis_status: 'failed',
        analysis_error: error.message || '上传到Dify知识库失败'
      });
    }

    return false;
  }
}

// 主函数
async function main() {
  try {
    console.log('开始执行测试脚本...');

    // 连接数据库
    try {
      await sequelize.authenticate();
      console.log('数据库连接成功');
    } catch (dbError) {
      console.error('数据库连接失败:', dbError);
      throw dbError;
    }

    // 获取最新上传的文件
    const fileId = process.argv[2]; // 从命令行参数获取文件ID
    console.log(`命令行参数文件ID: ${fileId || '未指定'}`);

    let file;
    try {
      if (fileId) {
        // 如果指定了文件ID，则获取该文件
        console.log(`尝试获取ID为 ${fileId} 的文件...`);
        file = await File.findByPk(fileId);
        if (!file) {
          console.error(`未找到ID为 ${fileId} 的文件`);
          process.exit(1);
        }
      } else {
        // 否则获取最新上传的文件
        console.log('尝试获取最新上传的已批准文件...');
        file = await File.findOne({
          where: {
            status: 'approved'
          },
          order: [['created_at', 'DESC']]
        });

        if (!file) {
          console.error('未找到已批准的文件');

          // 尝试获取任何状态的最新文件
          console.log('尝试获取任何状态的最新文件...');
          file = await File.findOne({
            order: [['created_at', 'DESC']]
          });

          if (!file) {
            console.error('数据库中没有任何文件');
            process.exit(1);
          }

          console.log(`找到最新文件(状态: ${file.status}): ${file.id}, ${file.original_name}`);

          // 如果文件不是approved状态，则更新为approved
          if (file.status !== 'approved') {
            console.log(`将文件状态从 ${file.status} 更新为 approved...`);
            await file.update({ status: 'approved' });
            console.log('文件状态已更新为approved');
          }
        }
      }
    } catch (fileError) {
      console.error('获取文件失败:', fileError);
      throw fileError;
    }

    console.log(`找到文件: ${file.id}, ${file.original_name}`);
    console.log(`文件路径: ${file.path}`);
    console.log(`文件状态: ${file.status}`);

    // 上传文件到Dify知识库
    const result = await uploadFileToDify(file);

    if (result) {
      console.log(`文件 ${file.id} 已成功上传到Dify知识库`);
    } else {
      console.error(`文件 ${file.id} 上传到Dify知识库失败`);
    }

    // 关闭数据库连接
    await sequelize.close();
  } catch (error) {
    console.error('发生错误:', error);
    process.exit(1);
  }
}

// 执行主函数
main().then(() => {
  console.log('测试完成');
  process.exit(0);
}).catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
