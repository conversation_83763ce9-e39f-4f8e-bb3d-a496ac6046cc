/**
 * 会话中间件
 * 
 * 用于处理会话数据，如AI助手会话等
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */

const session = require('express-session');

// 配置会话中间件
const sessionMiddleware = session({
  secret: process.env.SESSION_SECRET || 'hefamily_session_secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    maxAge: 24 * 60 * 60 * 1000 // 1天
  }
});

module.exports = sessionMiddleware;
