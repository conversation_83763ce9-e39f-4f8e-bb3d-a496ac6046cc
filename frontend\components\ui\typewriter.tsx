"use client"

import React, { useState, useEffect, useRef, useMemo } from 'react'

interface TypewriterProps {
  text: string
  speed?: number
  className?: string
  onComplete?: () => void
}

/**
 * 打字机效果组件
 *
 * @param text 要显示的文本
 * @param speed 打字速度（毫秒/字符）
 * @param className 自定义CSS类
 * @param onComplete 打字完成时的回调函数
 */
export function Typewriter({
  text,
  speed = 30,
  className = "",
  onComplete
}: TypewriterProps) {
  const [displayText, setDisplayText] = useState("")
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isComplete, setIsComplete] = useState(false)
  const textRef = useRef(text)

  // 当文本变化时重置状态
  useEffect(() => {
    if (text !== textRef.current) {
      textRef.current = text
      setDisplayText("")
      setCurrentIndex(0)
      setIsComplete(false)
    }
  }, [text])

  // 打字效果
  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }, speed)

      return () => clearTimeout(timer)
    } else if (!isComplete) {
      setIsComplete(true)
      onComplete?.()
    }
  }, [currentIndex, text, speed, isComplete, onComplete])

  // 将文本中的换行符转换为<br>标签
  const formattedText = displayText.split('\n').map((line, i, arr) => (
    <React.Fragment key={i}>
      {line}
      {i < arr.length - 1 && <br />}
    </React.Fragment>
  ))

  return (
    <div className={className}>
      {formattedText}
    </div>
  )
}

/**
 * 流式打字机效果组件
 *
 * 用于处理流式响应的打字机效果
 */
export function StreamTypewriter({
  className = ""
}: {
  className?: string
}) {
  const [text, setText] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const [conversationId, setConversationId] = useState<string | null>(null)
  const eventSourceRef = useRef<EventSource | null>(null)

  /**
   * 开始流式打字
   *
   * @param assistantId 助手ID
   * @param query 查询内容
   * @param initialConversationId 初始会话ID
   */
  const startStreaming = async (
    assistantId: number | string,
    query: string,
    initialConversationId?: string | null
  ) => {
    // 重置状态
    setText("")
    setIsTyping(true)

    // 保留初始会话ID
    if (initialConversationId) {
      setConversationId(initialConversationId)
      console.log('使用现有会话ID:', initialConversationId)
    } else {
      setConversationId(null)
      console.log('创建新会话')
    }

    console.log('开始流式打字，助手ID:', assistantId, '初始会话ID:', initialConversationId || '新会话')

    try {
      // 获取认证令牌
      let token = null

      // 添加调试日志
      console.log('开始获取认证令牌')

      // 如果在客户端环境中
      if (typeof window !== 'undefined') {
        // 尝试从localStorage获取token（尝试多种可能的键名）
        token = localStorage.getItem('hefamily_token') ||
                localStorage.getItem('token') ||
                localStorage.getItem('auth_token')

        console.log('从localStorage获取的token:', token ? '已获取' : '未获取')

        // 如果没有找到token，尝试从cookie中获取
        if (!token) {
          console.log('尝试从cookie中获取token')
          const cookies = document.cookie.split(';')
          for (const cookie of cookies) {
            const [name, value] = cookie.trim().split('=')
            if (name === 'token' || name === 'hefamily_token' || name === 'auth_token') {
              token = value
              console.log('从cookie中获取到token')
              break
            }
          }
        }
      }

      // 如果仍然没有找到token，尝试从auth-service获取
      if (!token && typeof window !== 'undefined') {
        try {
          console.log('尝试从auth-service检查登录状态')
          // 导入isLoggedIn函数
          const { isLoggedIn } = await import('@/services/auth-service')
          const loggedIn = isLoggedIn()
          console.log('auth-service登录状态:', loggedIn)

          if (loggedIn) {
            token = localStorage.getItem('hefamily_token')
            console.log('从auth-service确认登录后获取token:', token ? '已获取' : '未获取')
          }
        } catch (e) {
          console.error('导入auth-service失败:', e)
        }
      }

      if (!token) {
        console.error('无法获取认证令牌，用户未登录')
        throw new Error('未登录，请先登录')
      }

      console.log('成功获取认证令牌')

      // 关闭之前的连接
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }

      // 创建请求体
      const requestBody = {
        query,
        conversation_id: initialConversationId || null
      }

      // 创建请求头
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }

      // 创建POST请求获取流式响应
      const response = await fetch(`/api/ai/assistant/${assistantId}/stream`, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '请求失败')
      }

      // 创建EventSource对象
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法创建流式读取器')
      }

      // 处理流式响应
      const decoder = new TextDecoder()
      let buffer = ''

      const processStream = async () => {
        try {
          let hasReceivedAnyToken = false
          let totalTokensReceived = 0

          while (true) {
            const { done, value } = await reader.read()
            if (done) {
              console.log('流式读取完成，总共接收token数:', totalTokensReceived)
              break
            }

            // 解码并添加到缓冲区
            const chunk = decoder.decode(value, { stream: true })
            buffer += chunk
            console.log('接收到新的数据块，长度:', chunk.length)

            // 处理完整的SSE消息
            const lines = buffer.split('\n\n')
            buffer = lines.pop() || ''

            for (const line of lines) {
              if (line.startsWith('data:')) {
                try {
                  const jsonStr = line.substring(5).trim()
                  const data = JSON.parse(jsonStr)

                  // 根据消息类型处理
                  if (data.type === 'token') {
                    hasReceivedAnyToken = true
                    totalTokensReceived++

                    // 添加新的文本片段
                    setText(prev => {
                      const newText = prev + data.content
                      // 每次更新都打印日志，帮助调试
                      console.log(`流式响应更新(token ${totalTokensReceived}):`,
                        data.content.length > 0 ?
                          `"${data.content}"` :
                          "(空token)",
                        "当前总长度:", newText.length)
                      return newText
                    })
                  } else if (data.type === 'end') {
                    // 消息结束，保存会话ID
                    if (data.conversation_id) {
                      console.log('流式响应结束，会话ID:', data.conversation_id)
                      setConversationId(data.conversation_id)
                    }

                    // 检查当前文本内容是否为空
                    if (text.trim() === '') {
                      console.error('流式响应结束，但累积内容为空，尝试使用end事件中的content')
                      // 尝试使用end事件中的content
                      if (data.content && typeof data.content === 'string' && data.content.trim() !== '') {
                        setText(data.content)
                        console.log('使用end事件中的content，长度:', data.content.length, '内容:', data.content)
                      } else {
                        // 如果end事件中也没有content，设置一个默认消息
                        setText('抱歉，生成回复时出现问题，请重试。')
                        console.error('流式响应结束，累积内容和end事件content都为空')
                      }
                    } else {
                      console.log('流式响应结束，累积内容长度:', text.length, '内容:',
                        text.length > 50 ?
                          text.substring(0, 20) + '...' + text.substring(text.length - 20) :
                          text)

                      // 即使有累积内容，也检查end事件中的content是否更完整
                      if (data.content && typeof data.content === 'string' &&
                          data.content.trim() !== '' &&
                          data.content.length > text.length) {
                        console.log('end事件中的content更长，使用end事件中的content')
                        setText(data.content)
                      }
                    }
                  } else if (data.type === 'error') {
                    // 错误消息
                    console.error('流式响应错误:', data.error)
                    setText(prev => prev + '\n\n[错误: ' + data.error + ']')
                  }
                } catch (e) {
                  console.error('解析SSE消息失败:', e)
                }
              }
            }
          }
        } catch (error) {
          console.error('处理流式响应错误:', error)
          setText(prev => prev + '\n\n[连接错误，请重试]')
        } finally {
          // 检查是否收到过任何token
          if (!hasReceivedAnyToken) {
            console.error('流式响应结束，但未收到任何token')
            setText('抱歉，生成回复时出现问题，请重试。')
          } else if (text.trim() === '') {
            // 如果收到了token但最终文本为空
            console.error('流式响应结束，收到了token但最终文本为空')
            setText('抱歉，生成回复时出现问题，请重试。')
          } else if (text.length < 5) {
            // 如果文本太短，可能是不完整的响应
            console.warn('流式响应结束，但文本内容过短:', text)
            // 不修改文本，保留原始内容
          }

          // 最后一次检查文本内容
          console.log('流式响应最终内容:', text)
          setIsTyping(false)
        }
      }

      processStream()

      return {
        cancel: () => {
          reader.cancel()
          setIsTyping(false)
        }
      }
    } catch (error) {
      console.error('流式打字错误:', error)
      setText(prev => prev + '\n\n[错误: ' + (error instanceof Error ? error.message : '未知错误') + ']')
      setIsTyping(false)
      return { cancel: () => {} }
    }
  }

  // 将文本中的换行符转换为<br>标签
  const formattedText = useMemo(() => {
    return text.split('\n').map((line, i, arr) => (
      <React.Fragment key={i}>
        {line}
        {i < arr.length - 1 && <br />}
      </React.Fragment>
    ))
  }, [text])

  // 创建一个可以直接渲染的组件
  const TextComponent = useMemo(() => {
    return (
      <div className={className}>
        {formattedText}
        {isTyping && (
          <span className="inline-block w-2 h-4 bg-gray-500 ml-1 animate-pulse"></span>
        )}
      </div>
    )
  }, [className, formattedText, isTyping])

  return {
    textComponent: TextComponent,
    text,
    isTyping,
    conversationId,
    startStreaming,
    // 添加一个重置方法，用于清空当前文本
    reset: () => {
      setText("")
      setIsTyping(false)
      setConversationId(null)
    }
  }
}
