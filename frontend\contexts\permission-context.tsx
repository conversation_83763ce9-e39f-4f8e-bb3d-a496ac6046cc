"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import permissionService from '@/services/permission-service'
import { useAuth } from './auth-context'

interface PermissionContextType {
  permissions: string[]
  hasPermission: (permissionCode: string) => boolean
  loadPermissions: () => Promise<void>
  isLoading: boolean
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined)

/**
 * 权限上下文提供者
 * 
 * 提供权限相关的状态和方法
 */
export function PermissionProvider({ children }: { children: ReactNode }) {
  const [permissions, setPermissions] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { isLoggedIn } = useAuth()

  // 加载权限
  const loadPermissions = async () => {
    if (!isLoggedIn) {
      setPermissions([])
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    try {
      // 先从本地存储获取
      const storedPermissions = permissionService.getUserPermissionsFromStorage()
      
      if (storedPermissions.length > 0) {
        setPermissions(storedPermissions)
        setIsLoading(false)
      }
      
      // 然后从服务器获取最新的
      const userPermissions = await permissionService.getCurrentUserPermissions()
      const permissionCodes = userPermissions.map(p => p.code)
      
      // 更新状态和本地存储
      setPermissions(permissionCodes)
      permissionService.saveUserPermissionsToStorage(permissionCodes)
    } catch (error) {
      console.error('加载权限失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 检查是否有权限
  const hasPermission = (permissionCode: string): boolean => {
    // 如果没有登录，没有任何权限
    if (!isLoggedIn) return false
    
    // 如果有通配符权限，拥有所有权限
    if (permissions.includes('*')) return true
    
    // 检查特定权限
    return permissions.includes(permissionCode)
  }

  // 当登录状态变化时，重新加载权限
  useEffect(() => {
    if (isLoggedIn) {
      loadPermissions()
    } else {
      setPermissions([])
      permissionService.clearPermissionsFromStorage()
    }
  }, [isLoggedIn])

  const value = {
    permissions,
    hasPermission,
    loadPermissions,
    isLoading
  }

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  )
}

/**
 * 使用权限上下文的钩子
 * 
 * @returns 权限上下文
 */
export function usePermission(): PermissionContextType {
  const context = useContext(PermissionContext)
  if (context === undefined) {
    throw new Error('usePermission must be used within a PermissionProvider')
  }
  return context
}

/**
 * 权限守卫组件
 * 
 * 根据权限控制子组件的渲染
 */
export function PermissionGuard({ 
  permissionCode, 
  children, 
  fallback = null 
}: { 
  permissionCode: string
  children: ReactNode
  fallback?: ReactNode
}) {
  const { hasPermission } = usePermission()
  
  return hasPermission(permissionCode) ? <>{children}</> : <>{fallback}</>
}
