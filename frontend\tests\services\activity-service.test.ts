/**
 * 活动服务测试
 */

import activityService from '@/services/activity-service'
import apiService from '@/services/api-service'

// 模拟API服务
jest.mock('@/services/api-service')

describe('活动服务', () => {
  // 在每个测试前后重置模拟
  beforeEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  afterEach(() => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  })

  // 测试获取活动列表
  describe('getActivities', () => {
    test('应该返回活动列表', async () => {
      // 模拟成功的响应
      const mockResponse = {
        activities: [
          {
            id: '1',
            title: '测试活动1',
            date: '2023-12-01',
            description: '这是测试活动1的描述',
            image: '/images/activity1.jpg',
            status: 'published',
            attachments: [],
            creator: {
              id: '1',
              username: 'admin'
            },
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
          },
          {
            id: '2',
            title: '测试活动2',
            date: '2023-12-15',
            description: '这是测试活动2的描述',
            image: '/images/activity2.jpg',
            status: 'published',
            attachments: [],
            creator: {
              id: '1',
              username: 'admin'
            },
            created_at: '2023-01-02T00:00:00Z',
            updated_at: '2023-01-02T00:00:00Z'
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取活动列表函数
      const result = await activityService.getActivities({ status: 'published' })

      // 验证结果
      expect(result).toEqual({
        activities: mockResponse.activities,
        pagination: mockResponse.pagination
      })
      expect(result.activities).toHaveLength(2)
      expect(result.activities[0].title).toBe('测试活动1')
      expect(result.activities[1].title).toBe('测试活动2')
    })

    test('获取活动列表失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('获取活动列表失败')

      // 模拟API服务的get方法抛出错误
      apiService.get = jest.fn().mockRejectedValue(error)

      // 调用获取活动列表函数并捕获错误
      await expect(activityService.getActivities({ status: 'published' })).rejects.toThrow('获取活动列表失败')
    })
  })

  // 测试获取活动详情
  describe('getActivityById', () => {
    test('应该返回活动详情', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '1',
        title: '测试活动1',
        date: '2023-12-01',
        description: '这是测试活动1的详细描述',
        image: '/images/activity1.jpg',
        status: 'published',
        attachments: [],
        creator: {
          id: '1',
          username: 'admin'
        },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取活动详情函数
      const result = await activityService.getActivityById('1')

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.id).toBe('1')
      expect(result.title).toBe('测试活动1')
    })

    test('获取不存在的活动应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('活动不存在')

      // 模拟API服务的get方法抛出错误
      apiService.get = jest.fn().mockRejectedValue(error)

      // 调用获取活动详情函数并捕获错误
      await expect(activityService.getActivityById('999')).rejects.toThrow('活动不存在')
    })
  })

  // 测试创建活动
  describe('createActivity', () => {
    test('应该成功创建活动', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '3',
        title: '新活动',
        date: '2023-12-31',
        description: '新活动描述',
        image: '',
        status: 'draft',
        attachments: [],
        creator_id: '1',
        creator: {
          id: '1',
          username: 'admin'
        },
        created_at: '2023-01-03T00:00:00Z',
        updated_at: '2023-01-03T00:00:00Z'
      }

      // 模拟API服务的post方法
      apiService.post = jest.fn().mockResolvedValue(mockResponse)

      // 调用创建活动函数
      const result = await activityService.createActivity({
        title: '新活动',
        date: '2023-12-31',
        description: '新活动描述',
        status: 'draft'
      })

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.id).toBe('3')
      expect(result.title).toBe('新活动')
      expect(result.status).toBe('draft')
    })

    test('创建活动失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('标题不能为空')

      // 模拟API服务的post方法抛出错误
      apiService.post = jest.fn().mockRejectedValue(error)

      // 调用创建活动函数并捕获错误
      await expect(activityService.createActivity({
        title: '',
        date: '2023-12-31',
        description: '新活动描述'
      })).rejects.toThrow('标题不能为空')
    })
  })

  // 测试更新活动
  describe('updateActivity', () => {
    test('应该成功更新活动', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '1',
        title: '更新后的活动',
        date: '2023-12-01',
        description: '更新后的描述',
        image: '/images/activity1.jpg',
        status: 'published',
        attachments: [],
        creator_id: '1',
        creator: {
          id: '1',
          username: 'admin'
        },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-04T00:00:00Z'
      }

      // 模拟API服务的put方法
      apiService.put = jest.fn().mockResolvedValue(mockResponse)

      // 调用更新活动函数
      const result = await activityService.updateActivity('1', {
        title: '更新后的活动',
        description: '更新后的描述'
      })

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.title).toBe('更新后的活动')
      expect(result.description).toBe('更新后的描述')
    })

    test('更新活动失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('权限不足')

      // 模拟API服务的put方法抛出错误
      apiService.put = jest.fn().mockRejectedValue(error)

      // 调用更新活动函数并捕获错误
      await expect(activityService.updateActivity('1', {
        title: '未授权的更新',
        description: '这不应该成功'
      })).rejects.toThrow('权限不足')
    })
  })

  // 测试删除活动
  describe('deleteActivity', () => {
    test('应该成功删除活动', async () => {
      // 模拟成功的响应
      const mockResponse = {
        message: '活动删除成功'
      }

      // 模拟API服务的del方法
      apiService.del = jest.fn().mockResolvedValue(mockResponse)

      // 调用删除活动函数
      const result = await activityService.deleteActivity('1')

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.message).toBe('活动删除成功')
    })

    test('删除活动失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('活动不存在')

      // 模拟API服务的del方法抛出错误
      apiService.del = jest.fn().mockRejectedValue(error)

      // 调用删除活动函数并捕获错误
      await expect(activityService.deleteActivity('999')).rejects.toThrow('活动不存在')
    })
  })

  // 测试上传活动图片
  describe('uploadActivityImage', () => {
    test('应该成功上传活动图片', async () => {
      // 模拟成功的响应
      const mockResponse = {
        url: '/images/uploaded-image.jpg'
      }

      // 模拟API服务的upload方法
      apiService.upload = jest.fn().mockResolvedValue(mockResponse)

      // 创建测试文件
      const file = new File(['image content'], 'test-image.jpg', { type: 'image/jpeg' })

      // 调用上传活动图片函数
      const result = await activityService.uploadActivityImage(file)

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.url).toBe('/images/uploaded-image.jpg')
    })

    test('上传活动图片失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('文件格式不支持')

      // 模拟API服务的upload方法抛出错误
      apiService.upload = jest.fn().mockRejectedValue(error)

      // 创建测试文件
      const file = new File(['image content'], 'test-image.xyz', { type: 'image/xyz' })

      // 调用上传活动图片函数并捕获错误
      await expect(activityService.uploadActivityImage(file)).rejects.toThrow('文件格式不支持')
    })
  })

  // 测试获取活动附件列表
  describe('getActivityAttachments', () => {
    test('应该返回活动附件列表', async () => {
      // 模拟成功的响应
      const mockResponse = [
        {
          id: '1',
          name: '附件1.pdf',
          url: '/attachments/1.pdf',
          type: 'pdf',
          size: '1024',
          created_at: '2023-01-01T00:00:00Z',
          uploader: {
            id: '1',
            username: 'admin'
          }
        },
        {
          id: '2',
          name: '附件2.docx',
          url: '/attachments/2.docx',
          type: 'docx',
          size: '2048',
          created_at: '2023-01-02T00:00:00Z',
          uploader: {
            id: '1',
            username: 'admin'
          }
        }
      ]

      // 模拟API服务的get方法
      apiService.get = jest.fn().mockResolvedValue(mockResponse)

      // 调用获取活动附件列表函数
      const result = await activityService.getActivityAttachments('1')

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result).toHaveLength(2)
      expect(result[0].name).toBe('附件1.pdf')
      expect(result[1].name).toBe('附件2.docx')
    })

    test('获取活动附件列表失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('活动不存在')

      // 模拟API服务的get方法抛出错误
      apiService.get = jest.fn().mockRejectedValue(error)

      // 调用获取活动附件列表函数并捕获错误
      await expect(activityService.getActivityAttachments('999')).rejects.toThrow('活动不存在')
    })
  })

  // 测试上传活动附件
  describe('uploadActivityAttachment', () => {
    test('应该成功上传活动附件', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '3',
        name: '新附件.pdf',
        url: '/attachments/3.pdf',
        type: 'pdf',
        size: '3072',
        created_at: '2023-01-03T00:00:00Z',
        uploader_id: '1'
      }

      // 模拟API服务的upload方法
      apiService.upload = jest.fn().mockResolvedValue(mockResponse)

      // 创建测试文件
      const file = new File(['file content'], '新附件.pdf', { type: 'application/pdf' })

      // 调用上传活动附件函数
      const result = await activityService.uploadActivityAttachment('1', file)

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.id).toBe('3')
      expect(result.name).toBe('新附件.pdf')
    })

    test('上传活动附件失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('文件大小超过限制')

      // 模拟API服务的upload方法抛出错误
      apiService.upload = jest.fn().mockRejectedValue(error)

      // 创建测试文件
      const file = new File(['file content'], 'large-file.pdf', { type: 'application/pdf' })

      // 调用上传活动附件函数并捕获错误
      await expect(activityService.uploadActivityAttachment('1', file)).rejects.toThrow('文件大小超过限制')
    })
  })

  // 测试下载活动附件
  describe('downloadActivityAttachment', () => {
    test('应该成功下载活动附件', async () => {
      // 模拟API服务的download方法
      apiService.download = jest.fn().mockResolvedValue(undefined)

      // 调用下载活动附件函数
      await activityService.downloadActivityAttachment('1')

      // 验证API服务被调用
      expect(apiService.download).toHaveBeenCalledWith('/activities/attachments/1/download')
    })

    test('下载活动附件失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('附件不存在')

      // 模拟API服务的download方法抛出错误
      apiService.download = jest.fn().mockRejectedValue(error)

      // 调用下载活动附件函数并捕获错误
      await expect(activityService.downloadActivityAttachment('999')).rejects.toThrow('附件不存在')
    })
  })

  // 测试删除活动附件
  describe('deleteActivityAttachment', () => {
    test('应该成功删除活动附件', async () => {
      // 模拟成功的响应
      const mockResponse = {
        message: '附件删除成功'
      }

      // 模拟API服务的del方法
      apiService.del = jest.fn().mockResolvedValue(mockResponse)

      // 调用删除活动附件函数
      const result = await activityService.deleteActivityAttachment('1')

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.message).toBe('附件删除成功')
    })

    test('删除活动附件失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('附件不存在')

      // 模拟API服务的del方法抛出错误
      apiService.del = jest.fn().mockRejectedValue(error)

      // 调用删除活动附件函数并捕获错误
      await expect(activityService.deleteActivityAttachment('999')).rejects.toThrow('附件不存在')
    })
  })

  // 测试切换活动状态
  describe('toggleActivityStatus', () => {
    test('应该成功切换活动状态', async () => {
      // 模拟成功的响应
      const mockResponse = {
        id: '1',
        status: 'published',
        updated_at: '2023-01-05T00:00:00Z'
      }

      // 模拟API服务的put方法
      apiService.put = jest.fn().mockResolvedValue(mockResponse)

      // 调用切换活动状态函数
      const result = await activityService.toggleActivityStatus('1', 'published')

      // 验证结果
      expect(result).toEqual(mockResponse)
      expect(result.status).toBe('published')
    })

    test('切换活动状态失败应该抛出错误', async () => {
      // 模拟失败的响应
      const error = new Error('权限不足')

      // 模拟API服务的put方法抛出错误
      apiService.put = jest.fn().mockRejectedValue(error)

      // 调用切换活动状态函数并捕获错误
      await expect(activityService.toggleActivityStatus('1', 'published')).rejects.toThrow('权限不足')
    })
  })
});
