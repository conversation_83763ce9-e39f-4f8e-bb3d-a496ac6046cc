# 和富家族研究平台测试指南

## 测试框架

本项目使用以下测试框架和工具：

- **Jest**: 主要测试框架
- **Supertest**: API测试工具
- **SQLite**: 测试数据库
- **Jest HTML Reporter**: 生成HTML测试报告
- **Jest JUnit**: 生成JUnit格式测试报告
- **Jest Sonar Reporter**: 生成Sonar兼容的测试报告

## 测试类型

项目包含以下类型的测试：

1. **单元测试**: 测试独立的函数和方法
2. **集成测试**: 测试多个组件之间的交互
3. **控制器测试**: 测试API端点
4. **模型测试**: 测试数据模型
5. **中间件测试**: 测试Express中间件

## 测试目录结构

```
tests/
├── controllers/       # 控制器测试
├── models/            # 模型测试
├── routes/            # 路由测试
├── middlewares/       # 中间件测试
├── unit/              # 单元测试
├── integration/       # 集成测试
├── fixtures/          # 测试数据和文件
├── utils/             # 测试辅助工具
├── setup.js           # 测试环境设置
├── run-tests.js       # 测试运行脚本
├── check-coverage.js  # 测试覆盖率检查脚本
├── generate-report.js # 测试报告生成脚本
└── TESTING.md         # 测试文档
```

## 运行测试

### 运行所有测试

```bash
npm test
```

### 运行特定类型的测试

```bash
# 运行控制器测试
npm run test:controllers

# 运行模型测试
npm run test:models

# 运行中间件测试
npm run test:middlewares

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration
```

### 监视模式

```bash
npm run test:watch
```

### 生成覆盖率报告

```bash
npm run test:coverage
```

### 检查覆盖率是否达标

```bash
npm run test:check-coverage
```

### 生成详细测试报告

```bash
npm run test:report
```

## 测试覆盖率目标

- 行覆盖率: 90%
- 函数覆盖率: 90%
- 分支覆盖率: 80%
- 语句覆盖率: 90%

## 测试数据库

测试使用SQLite内存数据库，以便快速运行测试并避免影响生产数据库。每次测试运行时，数据库都会重新创建，并在测试完成后销毁。

## 测试事务

所有测试数据操作都在一个事务中进行，并在测试完成后回滚，以确保测试之间的隔离性。

## 测试辅助工具

`tests/utils/testHelpers.js`文件提供了一系列辅助函数，用于创建测试数据和模拟用户认证。

## 编写测试的最佳实践

1. **测试隔离**: 每个测试应该是独立的，不依赖于其他测试的状态。
2. **测试数据清理**: 测试应该在运行后清理创建的测试数据。
3. **测试命名**: 测试名称应该清晰地描述测试的内容和预期结果。
4. **测试覆盖**: 测试应该覆盖正常情况和边缘情况。
5. **测试断言**: 每个测试应该包含明确的断言，验证预期结果。
6. **测试组织**: 测试应该按照功能或组件进行组织，便于维护和理解。
7. **测试文档**: 测试应该包含必要的注释和文档，说明测试的目的和方法。
8. **测试自动化**: 测试应该能够自动运行，不需要人工干预。
9. **测试速度**: 测试应该尽可能快速运行，以便频繁执行。
10. **测试可靠性**: 测试应该是可靠的，不应该出现随机失败的情况。

## 测试报告

测试报告位于`reports/`目录下，包括：

- `test-report.html`: HTML格式的测试报告
- `junit.xml`: JUnit格式的测试报告
- `test-summary.md`: Markdown格式的测试摘要

## 覆盖率报告

覆盖率报告位于`coverage/`目录下，包括：

- `lcov-report/index.html`: HTML格式的覆盖率报告
- `coverage-summary.json`: JSON格式的覆盖率摘要

## 持续集成

本项目的测试可以集成到CI/CD流程中，通过以下命令运行：

```bash
npm run test:check-coverage
```

如果测试覆盖率未达到目标，该命令将返回非零退出码，导致CI/CD流程失败。

## 故障排除

### 测试失败

如果测试失败，请检查以下几点：

1. 确保所有依赖项都已安装：`npm install`
2. 确保测试环境变量已正确设置
3. 检查测试报告以获取详细的错误信息
4. 尝试单独运行失败的测试：`npm test -- -t "测试名称"`

### 覆盖率不足

如果覆盖率不达标，请检查以下几点：

1. 运行`npm run test:check-coverage`查看哪些文件的覆盖率不足
2. 为覆盖率不足的文件添加更多测试用例
3. 确保测试覆盖了所有代码路径，包括错误处理和边缘情况
