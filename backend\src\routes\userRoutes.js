/**
 * 用户相关路由
 *
 * 处理用户注册、登录、个人信息管理等请求
 */

const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const authMiddleware = require('../middlewares/authMiddleware');

// 用户注册
router.post('/register', userController.register);

// 用户登录
router.post('/login', userController.login);

// 获取当前用户信息 (需要认证)
router.get('/me', authMiddleware, userController.getCurrentUser);

// 更新用户信息 (需要认证)
router.put('/me', authMiddleware, userController.updateCurrentUser);

// 修改密码 (需要认证)
router.put('/change-password', authMiddleware, userController.changePassword);

// 获取当前用户的权限 (需要认证)
router.get('/me/permissions', authMiddleware, userController.getCurrentUserPermissions);

// 检查当前用户是否有指定权限 (需要认证)
router.get('/me/check-permission', authMiddleware, userController.checkUserPermission);

// 管理员路由 - 获取所有用户 (需要管理员权限)
router.get('/', authMiddleware, userController.getAllUsers);

// 管理员路由 - 获取特定用户 (需要管理员权限)
router.get('/:id', authMiddleware, userController.getUserById);

// 管理员路由 - 更新用户 (需要管理员权限)
router.put('/:id', authMiddleware, userController.updateUser);

// 管理员路由 - 删除用户 (需要管理员权限)
router.delete('/:id', authMiddleware, userController.deleteUser);

// 管理员路由 - 重置用户密码 (需要管理员权限)
router.post('/:id/reset-password', authMiddleware, userController.resetUserPassword);

module.exports = router;
