# 和富家族研究平台数据库设计文档

## 1. 概述

本文档描述了和富家族研究平台的数据库设计，包括数据库选型、表结构设计、关系模型、索引设计和查询优化等内容。数据库设计遵循简单高效的原则，满足当前用户规模（100-200人）和访问量（日访问几百次）的需求，同时为未来的扩展预留空间。

## 2. 数据库选型

### 2.1 选型考虑因素

在选择数据库时，考虑了以下因素：

1. **系统需求**：系统需要存储结构化数据，包括用户信息、内容数据、知识库资料等
2. **性能需求**：支持100-200名用户同时在线，日访问量在几百次
3. **开发团队技能**：开发团队熟悉关系型数据库
4. **维护成本**：数据库应易于维护，不需要专门的DBA团队
5. **扩展性**：支持未来数据量增长和功能扩展

### 2.2 数据库选择

基于以上考虑因素，选择**MySQL**作为系统的主要数据库：

1. **MySQL优势**：
   - 成熟稳定，广泛应用
   - 性能良好，满足当前需求
   - 易于部署和维护
   - 与Node.js生态系统集成良好
   - 开源免费，降低成本

2. **版本选择**：MySQL 8.0或更高版本，提供更好的性能和功能支持

3. **存储引擎**：InnoDB，支持事务、外键约束和行级锁定

### 2.3 辅助存储（可选）

除了主数据库外，系统可以根据需要使用以下辅助存储：

1. **Redis**（可选）：用于缓存和会话管理，提高系统性能
2. **文件存储服务**：用于存储用户上传的文件，如文档、图片、视频等

## 3. 数据库设计原则

数据库设计遵循以下原则：

1. **简单性**：保持表结构简单清晰，避免过度设计
2. **规范化**：遵循数据库规范化原则，减少数据冗余
3. **性能优化**：为常用查询创建适当的索引
4. **安全性**：敏感数据加密存储，如用户密码
5. **可扩展性**：表设计考虑未来扩展需求
6. **命名规范**：表名和字段名采用统一的命名规范

## 4. 实体关系模型

系统的主要实体及其关系如下：

### 4.1 核心实体

1. **用户（Users）**：系统用户，包括访客、注册用户、管理员等
2. **角色（Roles）**：用户角色，如管理员、研究员、普通用户等
3. **权限（Permissions）**：系统权限，控制用户对功能的访问
4. **内容（Contents）**：系统内容，包括文章、页面等
5. **活动（Activities）**：纪念活动信息
6. **知识库资料（KnowledgeFiles）**：用户上传的资料文件
7. **AI助手（AIAssistants）**：系统中的AI助手配置
8. **通知（Notifications）**：系统通知
9. **评论（Comments）**：用户评论
10. **数据源（DataSources）**：数据查询的数据源

### 4.2 实体关系图

以下是系统的实体关系图（ER图）：

```
+-------------+       +-------------+       +-------------+
|    Users    |<----->|    Roles    |<----->| Permissions |
+-------------+       +-------------+       +-------------+
      |                     |
      |                     |
      v                     v
+-------------+       +-------------+
| Notifications|       |  Contents  |
+-------------+       +-------------+
                            |
                            |
                            v
                     +-------------+       +-------------+
                     |  Comments   |<----->| Activities  |
                     +-------------+       +-------------+
                            |
                            |
                            v
+-------------+       +-------------+       +-------------+
|AIAssistants |<----->|KnowledgeFiles|<---->| DataSources |
+-------------+       +-------------+       +-------------+
```

### 4.3 主要关系说明

1. **用户-角色**：多对一关系，一个用户只能拥有一个角色
2. **角色-权限**：多对多关系，通过角色权限关联表（RolePermissions）关联
3. **用户-内容**：一对多关系，一个用户可以创建多个内容
4. **用户-评论**：一对多关系，一个用户可以发表多个评论
5. **内容-评论**：一对多关系，一个内容可以有多个评论
6. **用户-通知**：一对多关系，一个用户可以有多个通知
7. **用户-知识库资料**：一对多关系，一个用户可以上传多个资料
8. **用户-活动**：多对多关系，通过用户活动关联表（UserActivities）关联

## 5. 表结构设计

### 5.1 用户相关表

#### 5.1.1 用户表（users）

存储系统用户信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 用户ID，自增 |
| username | VARCHAR | 50 | 否 | - | - | 用户名，唯一 |
| password | VARCHAR | 255 | 否 | - | - | 密码，加密存储 |
| email | VARCHAR | 100 | 否 | - | - | 邮箱，唯一 |
| phone | VARCHAR | 20 | 是 | NULL | - | 手机号 |
| real_name | VARCHAR | 50 | 是 | NULL | - | 真实姓名 |
| avatar | VARCHAR | 255 | 是 | NULL | - | 头像URL |
| role_id | INT | - | 否 | 4 | - | 角色ID，外键关联roles表，默认为初级访问者角色 |
| status | TINYINT | - | 否 | 1 | - | 状态：0-禁用，1-正常 |
| last_login | DATETIME | - | 是 | NULL | - | 最后登录时间 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 唯一索引：`username`、`email`
- 普通索引：`status`、`role_id`
- 外键：`role_id` 关联 `roles.id`

#### 5.1.2 角色表（roles）

存储系统角色信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 角色ID，自增 |
| name | VARCHAR | 50 | 否 | - | - | 角色名称，唯一 |
| description | VARCHAR | 255 | 是 | NULL | - | 角色描述 |
| is_system | TINYINT | - | 否 | 0 | - | 是否系统角色：0-否，1-是 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 唯一索引：`name`

#### 5.1.3 权限表（permissions）

存储系统权限信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 权限ID，自增 |
| name | VARCHAR | 50 | 否 | - | - | 权限名称，唯一 |
| code | VARCHAR | 50 | 否 | - | - | 权限代码，唯一 |
| description | VARCHAR | 255 | 是 | NULL | - | 权限描述 |
| module | VARCHAR | 50 | 否 | - | - | 所属模块 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 唯一索引：`code`
- 普通索引：`module`



#### 5.1.4 角色权限关联表（role_permissions）

存储角色与权限的关联关系。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| role_id | INT | - | 否 | - | 是 | 角色ID，外键关联roles表 |
| permission_id | INT | - | 否 | - | 是 | 权限ID，外键关联permissions表 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |

索引：
- 主键：`role_id, permission_id`
- 外键：`role_id` 关联 `roles.id`
- 外键：`permission_id` 关联 `permissions.id`

### 5.2 内容相关表

#### 5.2.1 内容表（contents）

存储系统内容信息，包括文章、页面等。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 内容ID，自增 |
| title | VARCHAR | 255 | 否 | - | - | 内容标题 |
| content | TEXT | - | 否 | - | - | 内容正文 |
| type | VARCHAR | 20 | 否 | 'article' | - | 内容类型：article-文章，page-页面 |
| status | VARCHAR | 20 | 否 | 'draft' | - | 状态：draft-草稿，published-已发布，archived-已归档 |
| author_id | INT | - | 否 | - | - | 作者ID，外键关联users表 |
| cover_image | VARCHAR | 255 | 是 | NULL | - | 封面图片URL |
| view_count | INT | - | 否 | 0 | - | 浏览次数 |
| comment_count | INT | - | 否 | 0 | - | 评论次数 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |
| published_at | DATETIME | - | 是 | NULL | - | 发布时间 |

索引：
- 主键：`id`
- 普通索引：`author_id`、`type`、`status`、`created_at`
- 外键：`author_id` 关联 `users.id`

#### 5.2.2 评论表（comments）

存储用户评论信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 评论ID，自增 |
| content | TEXT | - | 否 | - | - | 评论内容 |
| user_id | INT | - | 否 | - | - | 评论用户ID，外键关联users表 |
| content_id | INT | - | 否 | - | - | 评论内容ID，外键关联contents表 |
| parent_id | INT | - | 是 | NULL | - | 父评论ID，用于回复功能 |
| status | VARCHAR | 20 | 否 | 'pending' | - | 状态：pending-待审核，approved-已通过，rejected-已驳回 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 普通索引：`user_id`、`content_id`、`parent_id`、`status`
- 外键：`user_id` 关联 `users.id`
- 外键：`content_id` 关联 `contents.id`
- 外键：`parent_id` 关联 `comments.id`

#### 5.2.3 活动表（activities）

存储纪念活动信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 活动ID，自增 |
| title | VARCHAR | 255 | 否 | - | - | 活动标题 |
| description | TEXT | - | 否 | - | - | 活动描述 |
| start_time | DATETIME | - | 否 | - | - | 开始时间 |
| end_time | DATETIME | - | 是 | NULL | - | 结束时间 |
| location | VARCHAR | 255 | 是 | NULL | - | 活动地点 |
| image | VARCHAR | 255 | 是 | NULL | - | 活动图片URL |
| organizer | VARCHAR | 100 | 是 | NULL | - | 组织者 |
| status | VARCHAR | 20 | 否 | 'draft' | - | 状态：draft-草稿，published-已发布，archived-已归档 |
| creator_id | INT | - | 否 | - | - | 创建者ID，外键关联users表 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 普通索引：`creator_id`、`status`、`start_time`
- 外键：`creator_id` 关联 `users.id`

#### 5.2.4 活动附件表（activity_attachments）

存储活动相关的附件信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 附件ID，自增 |
| activity_id | INT | - | 否 | - | - | 活动ID，外键关联activities表 |
| name | VARCHAR | 255 | 否 | - | - | 附件名称 |
| file_path | VARCHAR | 255 | 否 | - | - | 文件路径 |
| file_size | INT | - | 否 | 0 | - | 文件大小（字节） |
| file_type | VARCHAR | 50 | 否 | - | - | 文件类型 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 普通索引：`activity_id`
- 外键：`activity_id` 关联 `activities.id`

### 5.3 知识库相关表

#### 5.3.1 知识库分类表（knowledge_categories）

存储知识库资料分类信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 分类ID，自增 |
| name | VARCHAR | 100 | 否 | - | - | 分类名称 |
| description | VARCHAR | 255 | 是 | NULL | - | 分类描述 |
| parent_id | INT | - | 是 | NULL | - | 父分类ID，用于层级分类 |
| sort_order | INT | - | 否 | 0 | - | 排序顺序 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 普通索引：`parent_id`
- 外键：`parent_id` 关联 `knowledge_categories.id`

#### 5.3.2 知识库文件表（knowledge_files）

存储知识库资料文件信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 文件ID，自增 |
| title | VARCHAR | 255 | 否 | - | - | 文件标题 |
| description | TEXT | - | 是 | NULL | - | 文件描述 |
| file_path | VARCHAR | 255 | 否 | - | - | 文件路径 |
| file_size | INT | - | 否 | 0 | - | 文件大小（字节） |
| file_type | VARCHAR | 50 | 否 | - | - | 文件类型 |
| category_id | INT | - | 是 | NULL | - | 分类ID，外键关联knowledge_categories表 |
| uploader_id | INT | - | 否 | - | - | 上传者ID，外键关联users表 |
| status | VARCHAR | 20 | 否 | 'published' | - | 状态：published-已发布，archived-已归档 |
| view_count | INT | - | 否 | 0 | - | 浏览次数 |
| download_count | INT | - | 否 | 0 | - | 下载次数 |
| analysis_result | TEXT | - | 是 | NULL | - | 文件分析结果，JSON格式 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 普通索引：`category_id`、`uploader_id`、`status`、`created_at`
- 外键：`category_id` 关联 `knowledge_categories.id`
- 外键：`uploader_id` 关联 `users.id`

#### 5.3.3 知识库文件标签表（knowledge_file_tags）

存储知识库文件标签信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 标签ID，自增 |
| name | VARCHAR | 50 | 否 | - | - | 标签名称，唯一 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 唯一索引：`name`

#### 5.3.4 知识库文件标签关联表（knowledge_file_tag_relations）

存储知识库文件与标签的关联关系。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| file_id | INT | - | 否 | - | 是 | 文件ID，外键关联knowledge_files表 |
| tag_id | INT | - | 否 | - | 是 | 标签ID，外键关联knowledge_file_tags表 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |

索引：
- 主键：`file_id, tag_id`
- 外键：`file_id` 关联 `knowledge_files.id`
- 外键：`tag_id` 关联 `knowledge_file_tags.id`

### 5.4 数据查询相关表

#### 5.4.1 数据源表（data_sources）

存储数据查询的数据源信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 数据源ID，自增 |
| name | VARCHAR | 100 | 否 | - | - | 数据源名称 |
| description | VARCHAR | 255 | 是 | NULL | - | 数据源描述 |
| type | VARCHAR | 20 | 否 | - | - | 数据源类型：internal-内部，external-外部 |
| connection_info | TEXT | - | 是 | NULL | - | 连接信息，JSON格式 |
| status | VARCHAR | 20 | 否 | 'active' | - | 状态：active-活跃，inactive-不活跃 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 普通索引：`type`、`status`

#### 5.4.2 查询记录表（query_records）

存储用户的查询记录。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 查询记录ID，自增 |
| user_id | INT | - | 否 | - | - | 用户ID，外键关联users表 |
| data_source_id | INT | - | 否 | - | - | 数据源ID，外键关联data_sources表 |
| query_content | TEXT | - | 否 | - | - | 查询内容 |
| query_params | TEXT | - | 是 | NULL | - | 查询参数，JSON格式 |
| result_count | INT | - | 否 | 0 | - | 结果数量 |
| execution_time | INT | - | 否 | 0 | - | 执行时间（毫秒） |
| status | VARCHAR | 20 | 否 | 'completed' | - | 状态：completed-完成，failed-失败 |
| error_message | TEXT | - | 是 | NULL | - | 错误信息 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 普通索引：`user_id`、`data_source_id`、`status`、`created_at`
- 外键：`user_id` 关联 `users.id`
- 外键：`data_source_id` 关联 `data_sources.id`

### 5.5 AI助手相关表

#### 5.5.1 AI助手表（ai_assistants）

存储系统中的AI助手配置信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 助手ID，自增 |
| name | VARCHAR | 100 | 否 | - | - | 助手名称 |
| description | VARCHAR | 255 | 是 | NULL | - | 助手描述 |
| type | VARCHAR | 20 | 否 | - | - | 助手类型：assistant-AI研究助手，personal-个人专题助手，data-query-数据查询助手，knowledge-file-知识库文件分析助手 |
| api_key | VARCHAR | 255 | 否 | - | - | API密钥，加密存储 |
| api_endpoint | VARCHAR | 255 | 否 | - | - | API端点 |
| app_id | VARCHAR | 100 | 是 | NULL | - | Dify应用ID |
| app_code | VARCHAR | 100 | 是 | NULL | - | Dify应用代码 |
| initial_message | TEXT | - | 是 | NULL | - | 初始对话内容 |
| upload_api_path | VARCHAR | 255 | 是 | NULL | - | 上传接口路径（知识库文件分析助手特有） |
| analysis_api_path | VARCHAR | 255 | 是 | NULL | - | 分析接口路径（知识库文件分析助手特有） |
| status | VARCHAR | 20 | 否 | '正常' | - | 状态：正常，禁用 |
| creator_id | INT | - | 否 | - | - | 创建者ID，外键关联users表 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |
| last_updated | DATETIME | - | 是 | NULL | - | 最后更新时间 |

索引：
- 主键：`id`
- 普通索引：`type`、`status`、`creator_id`
- 外键：`creator_id` 关联 `users.id`

#### 5.5.2 AI助手标签表（ai_assistant_tags）

存储AI助手标签信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 标签ID，自增 |
| name | VARCHAR | 50 | 否 | - | - | 标签名称，唯一 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 唯一索引：`name`

#### 5.5.3 AI助手标签关联表（ai_assistant_tag_relations）

存储AI助手与标签的关联关系。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| assistant_id | INT | - | 否 | - | 是 | 助手ID，外键关联ai_assistants表 |
| tag_id | INT | - | 否 | - | 是 | 标签ID，外键关联ai_assistant_tags表 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |

索引：
- 主键：`assistant_id, tag_id`
- 外键：`assistant_id` 关联 `ai_assistants.id`
- 外键：`tag_id` 关联 `ai_assistant_tags.id`

#### 5.5.4 对话历史表（chat_histories）

存储用户与AI助手的对话历史。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 对话ID，自增 |
| user_id | INT | - | 否 | - | - | 用户ID，外键关联users表 |
| assistant_id | INT | - | 否 | - | - | 助手ID，外键关联ai_assistants表 |
| session_id | VARCHAR | 50 | 否 | - | - | 会话ID，唯一标识一次完整对话 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 普通索引：`user_id`、`assistant_id`、`session_id`、`created_at`
- 外键：`user_id` 关联 `users.id`
- 外键：`assistant_id` 关联 `ai_assistants.id`

#### 5.5.5 对话消息表（chat_messages）

存储对话中的具体消息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 消息ID，自增 |
| chat_id | INT | - | 否 | - | - | 对话ID，外键关联chat_histories表 |
| role | VARCHAR | 20 | 否 | - | - | 角色：user-用户，assistant-助手 |
| content | TEXT | - | 否 | - | - | 消息内容 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |

索引：
- 主键：`id`
- 普通索引：`chat_id`、`role`、`created_at`
- 外键：`chat_id` 关联 `chat_histories.id`

### 5.6 通知相关表

#### 5.6.1 通知表（notifications）

存储系统通知信息。

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|--------|------|------|
| id | INT | - | 否 | - | 是 | 通知ID，自增 |
| user_id | INT | - | 否 | - | - | 用户ID，外键关联users表 |
| title | VARCHAR | 255 | 否 | - | - | 通知标题 |
| content | TEXT | - | 否 | - | - | 通知内容 |
| type | VARCHAR | 20 | 否 | 'system' | - | 通知类型：system-系统通知，file-文件通知，comment-评论通知 |
| is_read | TINYINT | - | 否 | 0 | - | 是否已读：0-未读，1-已读 |
| related_id | INT | - | 是 | NULL | - | 相关ID，根据type不同关联不同表 |
| created_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | DATETIME | - | 否 | CURRENT_TIMESTAMP | - | 更新时间 |

索引：
- 主键：`id`
- 普通索引：`user_id`、`type`、`is_read`、`created_at`
- 外键：`user_id` 关联 `users.id`

## 6. 索引设计

### 6.1 索引设计原则

索引设计遵循以下原则：

1. **主键索引**：每个表都有主键索引，通常使用自增整数作为主键
2. **外键索引**：为外键字段创建索引，提高关联查询性能
3. **唯一索引**：为需要唯一性约束的字段创建唯一索引
4. **普通索引**：为常用查询条件创建普通索引
5. **复合索引**：为多字段查询条件创建复合索引
6. **避免过度索引**：只为必要的字段创建索引，避免影响写入性能

### 6.2 主要索引列表

| 表名 | 索引名 | 索引类型 | 索引字段 | 说明 |
|------|--------|----------|----------|------|
| users | PRIMARY | 主键 | id | 用户ID主键索引 |
| users | idx_username | 唯一 | username | 用户名唯一索引 |
| users | idx_email | 唯一 | email | 邮箱唯一索引 |
| users | idx_status | 普通 | status | 状态索引 |
| users | idx_role_id | 普通 | role_id | 角色ID索引 |
| users | fk_role_id | 外键 | role_id | 关联roles.id |
| roles | PRIMARY | 主键 | id | 角色ID主键索引 |
| roles | idx_name | 唯一 | name | 角色名称唯一索引 |
| permissions | PRIMARY | 主键 | id | 权限ID主键索引 |
| permissions | idx_code | 唯一 | code | 权限代码唯一索引 |
| permissions | idx_module | 普通 | module | 所属模块索引 |
| role_permissions | PRIMARY | 主键 | role_id, permission_id | 角色权限关联主键索引 |
| contents | PRIMARY | 主键 | id | 内容ID主键索引 |
| contents | idx_author_type_status | 复合 | author_id, type, status | 作者、类型、状态复合索引 |
| contents | idx_created_at | 普通 | created_at | 创建时间索引 |
| comments | PRIMARY | 主键 | id | 评论ID主键索引 |
| comments | idx_content_id | 普通 | content_id | 内容ID索引 |
| comments | idx_user_id | 普通 | user_id | 用户ID索引 |
| comments | idx_status | 普通 | status | 状态索引 |
| activities | PRIMARY | 主键 | id | 活动ID主键索引 |
| activities | idx_creator_status | 复合 | creator_id, status | 创建者、状态复合索引 |
| activities | idx_start_time | 普通 | start_time | 开始时间索引 |
| knowledge_files | PRIMARY | 主键 | id | 文件ID主键索引 |
| knowledge_files | idx_category_uploader | 复合 | category_id, uploader_id | 分类、上传者复合索引 |
| knowledge_files | idx_status_created | 复合 | status, created_at | 状态、创建时间复合索引 |
| ai_assistants | PRIMARY | 主键 | id | 助手ID主键索引 |
| ai_assistants | idx_type_status | 复合 | type, status | 类型、状态复合索引 |
| chat_histories | PRIMARY | 主键 | id | 对话ID主键索引 |
| chat_histories | idx_user_assistant | 复合 | user_id, assistant_id | 用户、助手复合索引 |
| chat_histories | idx_session_id | 普通 | session_id | 会话ID索引 |
| chat_messages | PRIMARY | 主键 | id | 消息ID主键索引 |
| chat_messages | idx_chat_id | 普通 | chat_id | 对话ID索引 |
| notifications | PRIMARY | 主键 | id | 通知ID主键索引 |
| notifications | idx_user_read | 复合 | user_id, is_read | 用户、已读状态复合索引 |
| notifications | idx_type_created | 复合 | type, created_at | 类型、创建时间复合索引 |

## 7. 查询优化

### 7.1 常用查询优化

为提高系统性能，对常用查询进行优化：

1. **用户登录查询**：
   ```sql
   SELECT * FROM users WHERE username = ? AND status = 1 LIMIT 1;
   ```
   优化：为username创建唯一索引，为status创建普通索引

2. **内容列表查询**：
   ```sql
   SELECT * FROM contents WHERE status = 'published' ORDER BY created_at DESC LIMIT ?, ?;
   ```
   优化：为status和created_at创建复合索引

3. **评论列表查询**：
   ```sql
   SELECT c.*, u.username, u.avatar
   FROM comments c
   JOIN users u ON c.user_id = u.id
   WHERE c.content_id = ? AND c.status = 'approved'
   ORDER BY c.created_at DESC;
   ```
   优化：为comments表的content_id和status创建复合索引

4. **知识库文件查询**：
   ```sql
   SELECT * FROM knowledge_files
   WHERE category_id = ? AND status = 'published'
   ORDER BY created_at DESC LIMIT ?, ?;
   ```
   优化：为category_id、status和created_at创建复合索引

5. **通知列表查询**：
   ```sql
   SELECT * FROM notifications
   WHERE user_id = ? AND is_read = 0
   ORDER BY created_at DESC LIMIT 4;
   ```
   优化：为user_id和is_read创建复合索引

### 7.2 分页查询优化

对于大数据量的分页查询，采用以下优化策略：

1. **基于ID的分页**：
   ```sql
   SELECT * FROM knowledge_files
   WHERE id > ? AND status = 'published'
   ORDER BY id ASC LIMIT ?;
   ```
   优化：使用ID作为分页标记，避免OFFSET性能问题

2. **延迟关联**：
   ```sql
   SELECT f.* FROM knowledge_files f
   JOIN (
     SELECT id FROM knowledge_files
     WHERE category_id = ? AND status = 'published'
     ORDER BY created_at DESC LIMIT ?, ?
   ) AS tmp ON f.id = tmp.id;
   ```
   优化：先查询ID，再关联完整数据，减少数据传输量

### 7.3 全文搜索优化

对于知识库文件的全文搜索，可以考虑以下优化：

1. **MySQL全文索引**：
   ```sql
   CREATE FULLTEXT INDEX idx_fulltext ON knowledge_files(title, description);

   SELECT * FROM knowledge_files
   WHERE MATCH(title, description) AGAINST(? IN BOOLEAN MODE);
   ```

2. **分词搜索**：
   ```sql
   SELECT * FROM knowledge_files
   WHERE title LIKE ? OR description LIKE ?;
   ```
   优化：为title和description创建前缀索引

## 8. 数据库维护

### 8.1 备份策略

为确保数据安全，采用以下备份策略：

1. **全量备份**：每天凌晨进行一次全量备份
2. **增量备份**：每小时进行一次增量备份
3. **备份验证**：定期验证备份数据的完整性
4. **备份存储**：备份数据存储在独立的存储设备上，并定期异地备份

### 8.2 性能监控

定期监控数据库性能，包括：

1. **慢查询日志**：记录执行时间超过1秒的查询
2. **连接数监控**：监控数据库连接数，避免连接数过多
3. **资源使用监控**：监控CPU、内存、磁盘IO等资源使用情况
4. **表大小监控**：监控表大小增长情况，及时优化

### 8.3 数据库优化

定期进行数据库优化，包括：

1. **表优化**：定期执行OPTIMIZE TABLE命令
2. **索引优化**：定期检查索引使用情况，优化或删除不必要的索引
3. **查询优化**：分析慢查询日志，优化慢查询
4. **参数优化**：根据系统负载调整数据库参数

## 9. 数据迁移与升级

### 9.1 数据迁移策略

在系统升级或数据库迁移时，采用以下策略：

1. **版本控制**：使用数据库版本控制工具（如Flyway、Liquibase）管理数据库结构变更
2. **向后兼容**：确保数据库变更向后兼容，不影响现有功能
3. **分步迁移**：大型迁移分步进行，减少系统停机时间
4. **回滚计划**：制定详细的回滚计划，确保迁移失败时能够快速恢复

### 9.2 数据库升级流程

1. **开发环境测试**：在开发环境测试数据库变更
2. **测试环境验证**：在测试环境验证数据库变更
3. **备份生产数据**：升级前备份生产数据
4. **执行升级脚本**：执行数据库升级脚本
5. **验证升级结果**：验证升级后的数据库功能
6. **监控系统性能**：升级后密切监控系统性能

## 10. 结论

和富家族研究平台的数据库设计采用MySQL关系型数据库，通过合理的表结构设计、索引优化和查询优化，满足当前用户规模和访问量的需求，同时为未来的扩展预留空间。数据库设计遵循简单高效的原则，易于维护和扩展，为系统提供稳定可靠的数据存储和访问服务。