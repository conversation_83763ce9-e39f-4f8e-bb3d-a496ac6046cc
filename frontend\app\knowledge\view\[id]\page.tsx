"use client"

import React, { use } from "react"

import { useState, useRef, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { Search, File, Download, User, Upload, X, Check, Lock, ShieldAlert, AlertTriangle, Clock, ChevronLeft, ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { ModalProvider, ModalContent, useModal } from "@/components/modal-provider"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "@/components/ui/use-toast"
import knowledgeService from "@/services/knowledge-service"
import { getKnowledgeBaseFileList, downloadFile } from "@/services/file-service"
import { FileUpload } from "@/components/file-upload"
import { FileList } from "@/components/file-list"

interface KnowledgeFile {
  id: number
  name: string
  type: string
  size: string
  uploadedBy: string
  uploadedAt: string
  downloads: number
  views: number
  reviewStatus?: "pending" | "approved" | "rejected" // 添加审核状态
}

interface KnowledgeViewProps {
  params: {
    id: string
  }
}

function KnowledgeViewContent({ params }: KnowledgeViewProps) {
  const router = useRouter()
  // 使用 React.use() 解包 params 对象
  const unwrappedParams = use(params)
  const id = unwrappedParams.id
  const [searchQuery, setSearchQuery] = useState("")
  const [showUploadArea, setShowUploadArea] = useState(false)
  const [showAccessRequestModal, setShowAccessRequestModal] = useState(false)
  const [accessRequestReason, setAccessRequestReason] = useState("")
  const [accessRequestSubmitted, setAccessRequestSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 知识库和文件数据
  const [knowledgeBase, setKnowledgeBase] = useState<any>(null)
  const [filesRefreshTrigger, setFilesRefreshTrigger] = useState(0)

  const { openModal, closeModal } = useModal()
  const { isLoggedIn, hasPermission, userData } = useAuth()

  // 加载知识库数据
  useEffect(() => {
    loadKnowledgeBase()
  }, [id])

  // 加载知识库数据
  const loadKnowledgeBase = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await knowledgeService.getKnowledgeBaseById(id)
      setKnowledgeBase({
        id: response.id,
        name: response.name,
        creator: response.creator?.username || '系统管理员',
        createdAt: new Date(response.created_at).toLocaleString(),
        filesCount: response.file_count,
        totalSize: formatFileSize(response.storage_size),
        type: response.type === 'system' ? '系统' : '用户',
        description: response.description,
        hasAccess: response.hasAccess
      })
    } catch (error: any) {
      console.error('加载知识库数据失败:', error)
      setError(error.response?.data?.message || "无法加载知识库数据，请稍后再试")
      toast({
        title: "加载失败",
        description: "无法加载知识库数据，请稍后再试",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 判断用户是否有权限访问知识库
   *
   * 权限规则：
   * 1. 系统知识库：所有已登录用户可访问
   * 2. 用户知识库：创建者可访问
   * 3. 用户知识库：被授权的用户可访问
   * 4. 用户知识库：有hasAccess标记的可访问
   */
  const isCurrentUserAuthorized = () => {
    if (!knowledgeBase || !userData) return false

    // 系统知识库：所有已登录用户可访问
    if (knowledgeBase.type === '系统') return true

    // 用户知识库：创建者可访问
    if (knowledgeBase.creator === userData.username) return true

    // 用户知识库：有hasAccess标记的可访问
    if (knowledgeBase.hasAccess) return true

    // 用户知识库：有权限的用户可访问
    return hasPermission('knowledge:view')
  }

  // 处理文件上传完成
  const handleUploadComplete = (file: any) => {
    toast({
      title: "上传成功",
      description: `文件 ${file.original_name} 已成功上传`,
    })

    // 刷新文件列表
    setFilesRefreshTrigger(prev => prev + 1)
  }

  // 处理文件上传错误
  const handleUploadError = (error: any) => {
    // 处理权限错误
    if (error.name === 'PermissionError' || error.response?.status === 403) {
      // 记录权限错误但不作为严重错误
      console.info("文件上传权限错误:", error.message || error.response?.data?.message || "权限不足")

      toast({
        title: "权限不足",
        description: error.message || error.response?.data?.message || "您没有权限上传文件到该知识库",
        variant: "destructive"
      })
    } else {
      // 其他错误正常记录
      console.error('文件上传失败:', error)

      toast({
        title: "上传失败",
        description: error.message || error.response?.data?.message || "文件上传失败，请稍后再试",
        variant: "destructive"
      })
    }
  }

  // 处理文件删除或审核后的刷新
  const handleFileUpdated = () => {
    // 刷新文件列表
    setFilesRefreshTrigger(prev => prev + 1)
  }

  // 处理文件下载
  const handleDownload = async (fileId: string, fileName: string) => {
    try {
      await downloadFile(fileId, fileName)

      toast({
        title: "下载开始",
        description: "文件开始下载，请稍候",
      })
    } catch (error) {
      console.error("下载文件失败:", error)
      toast({
        title: "下载失败",
        description: "无法下载文件，请稍后再试",
        variant: "destructive"
      })
    }
  }

  // 处理申请访问
  const handleRequestAccess = () => {
    setShowAccessRequestModal(true)
  }

  // 提交访问申请
  const handleSubmitAccessRequest = async () => {
    if (!accessRequestReason.trim()) return

    console.log('handleSubmitAccessRequest - 开始提交访问申请:', { id, reason: accessRequestReason })
    setAccessRequestSubmitted(true)

    try {
      // 调用知识库服务的requestKnowledgeBaseAccess函数
      const data = await knowledgeService.requestKnowledgeBaseAccess(id, accessRequestReason)
      console.log('handleSubmitAccessRequest - 访问申请API响应数据:', data)

      if (data.success) {
        toast({
          title: "申请已提交",
          description: "您的访问申请已提交，等待知识库创建者审核",
        })

        // 关闭模态框
        setTimeout(() => {
          setShowAccessRequestModal(false)
          // 重置状态，以便下次打开模态框
          setTimeout(() => {
            setAccessRequestSubmitted(false)
            setAccessRequestReason("")
          }, 500)
        }, 2000)
      } else {
        setAccessRequestSubmitted(false)
        toast({
          variant: "destructive",
          title: "申请失败",
          description: data.message || "提交访问申请失败，请稍后再试",
        })
      }
    } catch (error: any) {
      console.error('handleSubmitAccessRequest - 申请访问失败:', error)
      setAccessRequestSubmitted(false)
      toast({
        variant: "destructive",
        title: "申请失败",
        description: error.message || "提交访问申请失败，请稍后再试",
      })
    }
  }

  // 查看文件详情
  const handleViewDetail = (fileId: string) => {
    // 跳转到文件详情页
    router.push(`/files/${fileId}`)
  }

  // 如果正在加载或发生错误，显示相应状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="container mx-auto px-4 py-8 pt-24">
          {/* 返回按钮 */}
          <div className="mb-4">
            <Button
              variant="ghost"
              className="flex items-center text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              onClick={() => router.push("/knowledge")}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              返回知识库列表
            </Button>
          </div>

          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1e7a43]"></div>
            <span className="ml-2">加载中...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error || !knowledgeBase) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="container mx-auto px-4 py-8 pt-24">
          {/* 返回按钮 */}
          <div className="mb-4">
            <Button
              variant="ghost"
              className="flex items-center text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              onClick={() => router.push("/knowledge")}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              返回知识库列表
            </Button>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-8 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold mb-2">加载失败</h2>
            <p className="text-gray-600 mb-6">{error || "无法加载知识库数据，请稍后再试"}</p>
            <Button onClick={() => router.push("/knowledge")}>
              返回知识库列表
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        {/* 返回按钮 */}
        <div className="mb-4">
          <Button
            variant="ghost"
            className="flex items-center text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            onClick={() => router.push("/knowledge")}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            返回知识库列表
          </Button>
        </div>

        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold mb-2">{knowledgeBase.name}</h1>
            <div className="flex items-center">
              <p className="text-sm text-gray-500">
                {knowledgeBase.filesCount} 个文件 · {knowledgeBase.totalSize} · 创建于 {knowledgeBase.createdAt}
              </p>
              {knowledgeBase.type === "用户" && (
                <span className="ml-2 flex items-center text-xs bg-red-100 text-red-800 px-2 py-0.5 rounded-full">
                  <Lock className="h-3 w-3 mr-1" />
                  用户知识库
                </span>
              )}
            </div>
          </div>
          <div className="flex space-x-3">
            {/* 知识库设置按钮：管理员、知识库创建者或有knowledge:edit权限的用户可见 */}
            {(hasPermission('knowledge:edit') || (userData && knowledgeBase.creator === userData.username)) && (
              <Link href={`/knowledge/settings/${id}`}>
                <Button variant="outline">知识库设置</Button>
              </Link>
            )}
            {knowledgeBase.type === "系统" ? (
              // 系统知识库：有knowledge:upload权限的用户可以上传
              hasPermission('knowledge:upload') && (
                <Button className="bg-[#f5a623] hover:bg-[#f5a623]/90" onClick={() => setShowUploadArea(!showUploadArea)}>
                  上传文件
                </Button>
              )
            ) : (
              // 用户知识库
              isCurrentUserAuthorized() ? (
                // 创建者或有knowledge:upload权限的用户可以上传
                ((userData && knowledgeBase.creator === userData.username) || hasPermission('knowledge:upload')) && (
                  <Button className="bg-[#f5a623] hover:bg-[#f5a623]/90" onClick={() => setShowUploadArea(!showUploadArea)}>
                    上传文件
                  </Button>
                )
              ) : (
                <Button className="bg-[#1e7a43] hover:bg-[#1e7a43]/90" onClick={handleRequestAccess}>
                  申请访问
                </Button>
              )
            )}
          </div>
        </div>

        {/* 内容区域 */}
        {knowledgeBase.type === "用户" && !isCurrentUserAuthorized() ? (
          <div className="bg-gray-50 rounded-lg border border-gray-200 p-12 text-center">
            <div className="flex justify-center mb-4">
              <ShieldAlert className="h-16 w-16 text-gray-400" />
            </div>
            <h2 className="text-xl font-bold mb-2">此知识库为私密知识库</h2>
            <p className="text-gray-600 mb-6">您需要申请访问权限才能查看此知识库内容</p>
            <Button className="bg-[#1e7a43] hover:bg-[#1e7a43]/90" onClick={handleRequestAccess}>
              申请访问
            </Button>
          </div>
        ) : (
          <>
            {/* 文件上传区域 */}
            {showUploadArea && (
              <div className="mb-6">
                <div className="border rounded-lg p-6">
                  <h3 className="text-lg font-medium mb-4">上传文件到知识库</h3>
                  <FileUpload
                    knowledgeBaseId={id}
                    onUploadComplete={handleUploadComplete}
                    onUploadError={handleUploadError}
                    maxSize={50}
                    allowedTypes={['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png']}
                  />
                </div>

                {/* 上传状态通知 */}
                {knowledgeBase.type === "系统" && (
                  <div className="mt-4 bg-amber-50 border border-amber-200 rounded-md p-4">
                    <div className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 mr-3 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-medium text-amber-800 mb-1">文件审核通知</h4>
                        <p className="text-sm text-amber-700">
                          您上传到系统知识库的文件需要管理员审核后才会显示。审核通常在1-2个工作日内完成，请耐心等待。
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 文件列表 */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">知识库文件</h3>
                <div className="text-sm text-gray-500">
                  {knowledgeBase?.file_count > 0 ? (
                    <span>共 {knowledgeBase.file_count} 个文件{knowledgeBase.file_count > 10 ? "，分页显示" : ""}</span>
                  ) : (
                    <span>暂无文件</span>
                  )}
                </div>
              </div>
              {knowledgeBase?.file_count > 10 && (
                <div className="mb-4 p-2 bg-blue-50 border border-blue-200 rounded-md text-blue-700 text-sm">
                  <p className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    提示：文件总数为 {knowledgeBase.file_count} 个，每页显示10个文件，请使用分页控件查看更多文件。
                  </p>
                </div>
              )}
              <FileList
                knowledgeBaseId={id}
                showActions={true}
                showReview={false}
                onFileDeleted={handleFileUpdated}
                refreshTrigger={filesRefreshTrigger}
                hidePageControls={false}
              />
            </div>
          </>
        )}

        {/* 申请访问模态框 */}
        {showAccessRequestModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white rounded-lg w-full max-w-md p-6 relative">
              <button
                onClick={() => setShowAccessRequestModal(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
                disabled={accessRequestSubmitted}
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-xl font-bold mb-4">申请访问知识库</h2>
              <p className="text-gray-600 mb-4">请填写申请理由，知识库创建者将审核您的申请</p>

              {accessRequestSubmitted ? (
                <div className="bg-green-50 border border-green-200 rounded-md p-4 text-center">
                  <Check className="h-8 w-8 text-green-500 mx-auto mb-2" />
                  <p className="text-green-700 font-medium">申请已提交</p>
                  <p className="text-green-600 text-sm">我们会尽快处理您的申请</p>
                </div>
              ) : (
                <>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">申请理由</label>
                    <textarea
                      value={accessRequestReason}
                      onChange={(e) => setAccessRequestReason(e.target.value)}
                      placeholder="请简要说明您申请访问的原因..."
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-[#1e7a43] focus:border-transparent"
                      rows={4}
                    />
                  </div>

                  <div className="flex justify-end space-x-3">
                    <Button variant="outline" onClick={() => setShowAccessRequestModal(false)}>
                      取消
                    </Button>
                    <Button
                      className="bg-[#f5a623] hover:bg-[#f5a623]/90"
                      onClick={(e) => {
                        e.preventDefault();
                        console.log('点击提交申请按钮');

                        // 直接调用API
                        const token = localStorage.getItem('hefamily_token');

                        fetch('/api/knowledge-access-requests', {
                          method: 'POST',
                          headers: {
                            'Content-Type': 'application/json',
                            'Authorization': token ? `Bearer ${token}` : ''
                          },
                          body: JSON.stringify({
                            knowledge_base_id: id,
                            reason: accessRequestReason
                          })
                        })
                        .then(response => response.json())
                        .then(data => {
                          console.log('API响应:', data);

                          if (data.success) {
                            toast({
                              title: "申请已提交",
                              description: "您的访问申请已提交，等待知识库创建者审核",
                            });

                            setTimeout(() => {
                              setShowAccessRequestModal(false);
                              setTimeout(() => {
                                setAccessRequestSubmitted(false);
                                setAccessRequestReason("");
                              }, 500);
                            }, 2000);
                          } else {
                            setAccessRequestSubmitted(false);
                            toast({
                              variant: "destructive",
                              title: "申请失败",
                              description: data.message || "提交访问申请失败，请稍后再试",
                            });
                          }
                        })
                        .catch(error => {
                          console.error('API错误:', error);
                          setAccessRequestSubmitted(false);
                          toast({
                            variant: "destructive",
                            title: "申请失败",
                            description: "提交访问申请失败，请稍后再试",
                          });
                        });

                        setAccessRequestSubmitted(true);
                      }}
                      disabled={!accessRequestReason.trim()}
                    >
                      提交申请
                    </Button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// 修复：确保组件正确导出为默认组件，并使用 React.use() 解包 params
export default function KnowledgeViewPage({ params }: KnowledgeViewProps) {
  return (
    <ModalProvider>
      <KnowledgeViewContent params={params} />
    </ModalProvider>
  )
}
