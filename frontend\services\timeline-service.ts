/**
 * 时间轴事件服务
 * 
 * 处理时间轴事件的创建、查询、更新、删除等操作
 */

import * as apiService from './api-service'

// 事件等级枚举
export enum EventLevel {
  National = "national", // 国家级影响
  Family = "family", // 家族级影响
  Personal = "personal", // 个人级影响
}

// 时间轴事件接口
export interface TimelineEvent {
  id: number
  year: string
  title: string
  description: string
  content: string
  level: EventLevel
  position?: "left" | "right"
  icon?: string
  created_at?: string
  updated_at?: string
}

// 创建时间轴事件参数
export interface CreateTimelineEventParams {
  year: string
  title: string
  description: string
  content: string
  level: EventLevel
  icon?: string
}

// 更新时间轴事件参数
export interface UpdateTimelineEventParams {
  year?: string
  title?: string
  description?: string
  content?: string
  level?: EventLevel
  icon?: string
}

/**
 * 获取时间轴事件列表
 * @returns 时间轴事件列表
 */
export const getTimelineEvents = async (): Promise<TimelineEvent[]> => {
  return await apiService.get<TimelineEvent[]>('/timeline-events')
}

/**
 * 获取时间轴事件详情
 * @param id 事件ID
 * @returns 时间轴事件详情
 */
export const getTimelineEventById = async (id: number): Promise<TimelineEvent> => {
  return await apiService.get<TimelineEvent>(`/timeline-events/${id}`)
}

/**
 * 创建时间轴事件
 * @param params 创建参数
 * @returns 创建的时间轴事件
 */
export const createTimelineEvent = async (params: CreateTimelineEventParams): Promise<TimelineEvent> => {
  return await apiService.post<TimelineEvent>('/timeline-events', params)
}

/**
 * 更新时间轴事件
 * @param id 事件ID
 * @param params 更新参数
 * @returns 更新后的时间轴事件
 */
export const updateTimelineEvent = async (id: number, params: UpdateTimelineEventParams): Promise<TimelineEvent> => {
  return await apiService.put<TimelineEvent>(`/timeline-events/${id}`, params)
}

/**
 * 删除时间轴事件
 * @param id 事件ID
 * @returns 操作结果
 */
export const deleteTimelineEvent = async (id: number): Promise<{ message: string }> => {
  return await apiService.del<{ message: string }>(`/timeline-events/${id}`)
}

/**
 * 上传时间轴事件图标
 * @param file 图片文件
 * @returns 上传结果，包含图片URL
 */
export const uploadTimelineEventIcon = async (file: File): Promise<{ url: string }> => {
  const formData = new FormData()
  formData.append('icon', file)
  
  return await apiService.upload<{ url: string }>('/timeline-events/upload-icon', formData)
}

export default {
  getTimelineEvents,
  getTimelineEventById,
  createTimelineEvent,
  updateTimelineEvent,
  deleteTimelineEvent,
  uploadTimelineEventIcon
}
