/**
 * 创建系统配置表迁移文件
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('system_config', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        defaultValue: 1
      },
      // 密码策略
      password_min_length: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 8,
        comment: '密码最小长度'
      },
      password_require_uppercase: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '密码是否需要包含大写字母'
      },
      password_require_lowercase: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '密码是否需要包含小写字母'
      },
      password_require_number: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '密码是否需要包含数字'
      },
      password_require_special: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '密码是否需要包含特殊字符'
      },
      // 登录安全
      max_login_attempts: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 5,
        comment: '最大登录尝试次数'
      },
      lockout_duration: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 30,
        comment: '锁定时长（分钟）'
      },
      // 会话设置
      session_timeout: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 60,
        comment: '会话超时时间（分钟）'
      },
      // 文件上传设置
      file_upload_max_size: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 10,
        comment: '文件上传最大大小（MB）'
      },
      allowed_file_types: {
        type: Sequelize.STRING(255),
        allowNull: false,
        defaultValue: 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif',
        comment: '允许上传的文件类型，逗号分隔'
      },
      // 系统信息
      system_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        defaultValue: '和富家族研究平台',
        comment: '系统名称'
      },
      system_logo: {
        type: Sequelize.STRING(255),
        allowNull: false,
        defaultValue: '/logo.png',
        comment: '系统Logo路径'
      },
      system_description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '系统描述'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('system_config');
  }
};
