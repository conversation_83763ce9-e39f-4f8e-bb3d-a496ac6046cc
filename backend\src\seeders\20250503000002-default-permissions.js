/**
 * 默认权限种子数据
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 系统管理权限
    const systemPermissions = [
      {
        name: '访问系统管理',
        code: 'system:access',
        description: '访问系统管理页面',
        module: 'system',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '用户管理',
        code: 'user:manage',
        description: '管理用户（创建、编辑、删除）',
        module: 'system',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '角色管理',
        code: 'role:manage',
        description: '管理角色（创建、编辑、删除）',
        module: 'system',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '权限管理',
        code: 'permission:manage',
        description: '管理权限（分配权限）',
        module: 'system',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'AI管理',
        code: 'ai:manage',
        description: '管理AI助手（编辑配置）',
        module: 'system',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '安全配置',
        code: 'security:manage',
        description: '管理安全配置（密码策略等）',
        module: 'system',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // 知识库权限
    const knowledgeBasePermissions = [
      {
        name: '访问知识库',
        code: 'knowledge:access',
        description: '访问知识库页面',
        module: 'knowledge',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '创建系统知识库',
        code: 'knowledge:create_system',
        description: '创建系统知识库',
        module: 'knowledge',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '创建用户知识库',
        code: 'knowledge:create_user',
        description: '创建用户知识库',
        module: 'knowledge',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '上传文件',
        code: 'file:upload',
        description: '上传文件到知识库',
        module: 'knowledge',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '审核文件',
        code: 'file:review',
        description: '审核知识库文件',
        module: 'knowledge',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // 数据查询权限
    const dataQueryPermissions = [
      {
        name: '访问数据查询',
        code: 'data:access',
        description: '访问数据查询页面',
        module: 'data',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '使用AI查询',
        code: 'data:ai_query',
        description: '使用AI助手进行数据查询',
        module: 'data',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // 活动管理权限
    const activityPermissions = [
      {
        name: '管理活动',
        code: 'activity:manage',
        description: '管理活动（创建、编辑、删除）',
        module: 'activity',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '查看活动',
        code: 'activity:view',
        description: '查看活动详情',
        module: 'activity',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // 个人专题权限
    const personalTopicPermissions = [
      {
        name: '访问个人专题',
        code: 'personal:access',
        description: '访问个人专题页面',
        module: 'personal',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '使用个人专题AI助手',
        code: 'personal:ai_use',
        description: '使用个人专题AI助手',
        module: 'personal',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '管理相关资料',
        code: 'personal:manage_materials',
        description: '管理个人专题相关资料',
        module: 'personal',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // 合并所有权限
    const allPermissions = [
      ...systemPermissions,
      ...knowledgeBasePermissions,
      ...dataQueryPermissions,
      ...activityPermissions,
      ...personalTopicPermissions
    ];

    await queryInterface.bulkInsert('permissions', allPermissions, {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('permissions', null, {});
  }
};
