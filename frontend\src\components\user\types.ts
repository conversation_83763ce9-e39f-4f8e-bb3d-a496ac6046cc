/**
 * 用户管理组件类型定义
 */

// 用户状态类型
export type UserStatus = '正常' | '待审核' | '已禁用' | '待激活';

// 用户类型
export interface UserType {
  id: string;
  name: string;
  username: string;
  phone: string;
  email: string;
  roleId: string;
  roleName: string;
  status: UserStatus;
  createdAt: string;
  lastLogin?: string;
}

// 用户表格列配置
export interface UserTableColumn {
  key: string;
  title: string;
  dataIndex: string;
  render?: (value: any, record: UserType) => React.ReactNode;
  sorter?: boolean;
  sortOrder?: 'ascend' | 'descend' | null;
  width?: number | string;
}

// 用户筛选条件
export interface UserFilter {
  search: string;
  status: string;
  roleId: string;
}

// 用户表格分页
export interface UserPagination {
  current: number;
  pageSize: number;
  total: number;
}

// 用户表格排序
export interface UserSorter {
  field: string;
  order: 'ascend' | 'descend' | null;
}

// 用户表格参数
export interface UserTableParams {
  pagination: UserPagination;
  filters: UserFilter;
  sorter: UserSorter;
}

// 用户表格数据
export interface UserTableData {
  list: UserType[];
  pagination: UserPagination;
}

// 用户表格操作
export type UserTableAction = 'edit' | 'delete' | 'resetPassword' | 'enable' | 'disable';

// 用户表格操作处理函数
export type UserTableActionHandler = (action: UserTableAction, user: UserType) => void;

// 用户表格批量操作
export type UserBatchAction = 'enable' | 'disable' | 'delete' | 'changeRole';

// 用户表格批量操作处理函数
export type UserBatchActionHandler = (action: UserBatchAction, userIds: string[]) => void;

// 用户表格属性
export interface UserTableProps {
  loading: boolean;
  data: UserTableData;
  onTableChange: (params: UserTableParams) => void;
  onActionClick: UserTableActionHandler;
  onBatchActionClick: UserBatchActionHandler;
  onSelectionChange: (selectedRowKeys: string[]) => void;
}

// 用户表单字段
export interface UserFormFields {
  id?: string;
  username: string;
  password?: string;
  confirmPassword?: string;
  phone: string;
  email: string;
  roleId: string;
  status: UserStatus;
}

// 用户表单属性
export interface UserFormProps {
  loading: boolean;
  user?: UserType;
  roles: { id: string; name: string }[];
  onSubmit: (values: UserFormFields) => void;
  onCancel: () => void;
}

// 重置密码表单字段
export interface ResetPasswordFormFields {
  password: string;
  confirmPassword: string;
}

// 重置密码表单属性
export interface ResetPasswordFormProps {
  loading: boolean;
  user: UserType;
  onSubmit: (values: ResetPasswordFormFields) => void;
  onCancel: () => void;
}
