/**
 * 知识库访问申请模型
 * 用于管理用户对知识库的访问申请
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} KnowledgeBaseAccessRequest模型
 */
module.exports = (sequelize, DataTypes) => {
  const KnowledgeBaseAccessRequest = sequelize.define('KnowledgeBaseAccessRequest', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    knowledge_base_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'knowledge_bases',
        key: 'id'
      }
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected'),
      allowNull: false,
      defaultValue: 'pending'
    },
    reviewed_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    reviewed_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'knowledge_base_access_requests',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  KnowledgeBaseAccessRequest.associate = (models) => {
    KnowledgeBaseAccessRequest.belongsTo(models.KnowledgeBase, {
      foreignKey: 'knowledge_base_id',
      as: 'knowledgeBase'
    });
    
    KnowledgeBaseAccessRequest.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    
    KnowledgeBaseAccessRequest.belongsTo(models.User, {
      foreignKey: 'reviewed_by',
      as: 'reviewer'
    });
  };

  return KnowledgeBaseAccessRequest;
};
