/**
 * AI助手相关API
 * 
 * 处理AI助手的配置、查询等请求
 */

import request from '../index';

/**
 * 获取AI助手列表
 * @param {Object} params - 查询参数
 * @param {string} params.type - AI助手类型
 * @returns {Promise} AI助手列表
 */
export function getAIAssistantList(params) {
  return request({
    url: '/ai',
    method: 'get',
    params
  });
}

/**
 * 获取AI助手详情
 * @param {string} id - AI助手ID
 * @returns {Promise} AI助手详情
 */
export function getAIAssistantById(id) {
  return request({
    url: `/ai/${id}`,
    method: 'get'
  });
}

/**
 * 更新AI助手
 * @param {string} id - AI助手ID
 * @param {Object} data - AI助手信息
 * @returns {Promise} 更新结果
 */
export function updateAIAssistant(id, data) {
  return request({
    url: `/ai/${id}`,
    method: 'put',
    data
  });
}

/**
 * 个人专题助手查询
 * @param {Object} data - 查询信息
 * @param {string} data.query - 查询内容
 * @param {string} data.conversation_id - 会话ID
 * @returns {Promise} 查询结果
 */
export function queryPersonalAssistant(data) {
  return request({
    url: '/ai/personal/query',
    method: 'post',
    data
  });
}

/**
 * 数据查询助手查询
 * @param {Object} data - 查询信息
 * @param {string} data.query - 查询内容
 * @param {string} data.conversation_id - 会话ID
 * @returns {Promise} 查询结果
 */
export function queryDataAssistant(data) {
  return request({
    url: '/ai/data-query/query',
    method: 'post',
    data
  });
}

/**
 * 设置数据查询助手知识库
 * @param {Object} data - 知识库信息
 * @param {string} data.knowledge_base_id - 知识库ID
 * @returns {Promise} 设置结果
 */
export function setDataAssistantKnowledgeBase(data) {
  return request({
    url: '/ai/data-query/set-knowledge-base',
    method: 'post',
    data
  });
}

/**
 * 获取AI助手对话历史
 * @param {string} assistantType - 助手类型
 * @param {Object} params - 查询参数
 * @param {string} params.conversation_id - 会话ID
 * @returns {Promise} 对话历史
 */
export function getConversationHistory(assistantType, params) {
  return request({
    url: `/ai/conversations/${assistantType}`,
    method: 'get',
    params
  });
}

/**
 * 清除AI助手对话历史
 * @param {string} assistantType - 助手类型
 * @param {Object} data - 清除信息
 * @param {string} data.conversation_id - 会话ID
 * @returns {Promise} 清除结果
 */
export function clearConversationHistory(assistantType, data) {
  return request({
    url: `/ai/conversations/${assistantType}`,
    method: 'delete',
    data
  });
}

export default {
  getAIAssistantList,
  getAIAssistantById,
  updateAIAssistant,
  queryPersonalAssistant,
  queryDataAssistant,
  setDataAssistantKnowledgeBase,
  getConversationHistory,
  clearConversationHistory
};
