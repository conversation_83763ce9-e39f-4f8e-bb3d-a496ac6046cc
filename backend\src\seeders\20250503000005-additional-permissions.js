/**
 * 额外权限种子数据
 */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 基础页面权限
    const basicPagePermissions = [
      {
        name: '访问首页',
        code: 'home:access',
        description: '访问系统首页',
        module: 'basic',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '访问家族专题',
        code: 'family:access',
        description: '访问家族专题页面',
        module: 'basic',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // AI研究助手权限
    const aiAssistantPermissions = [
      {
        name: '使用AI研究助手',
        code: 'assistant:use',
        description: '使用AI研究助手功能',
        module: 'assistant',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // 合并所有权限
    const allPermissions = [
      ...basicPagePermissions,
      ...aiAssistantPermissions
    ];

    // 检查权限是否已存在
    for (const permission of allPermissions) {
      const [existingPermissions] = await queryInterface.sequelize.query(
        `SELECT id FROM permissions WHERE code = '${permission.code}'`
      );

      if (existingPermissions.length === 0) {
        // 权限不存在，创建它
        await queryInterface.bulkInsert('permissions', [permission], {});
      }
    }

    // 获取访问者角色ID
    const [roles] = await queryInterface.sequelize.query(
      `SELECT id FROM roles WHERE name = '访问者'`
    );

    if (roles.length > 0) {
      const visitorRoleId = roles[0].id;

      // 获取所有新添加的权限ID
      for (const permission of allPermissions) {
        const [permissions] = await queryInterface.sequelize.query(
          `SELECT id FROM permissions WHERE code = '${permission.code}'`
        );

        if (permissions.length > 0) {
          // 检查角色是否已有该权限
          const [existingRolePermissions] = await queryInterface.sequelize.query(
            `SELECT id FROM role_permissions WHERE role_id = ${visitorRoleId} AND permission_id = ${permissions[0].id}`
          );

          if (existingRolePermissions.length === 0) {
            // 为访问者角色分配权限
            await queryInterface.bulkInsert('role_permissions', [{
              role_id: visitorRoleId,
              permission_id: permissions[0].id,
              created_at: new Date(),
              updated_at: new Date()
            }], {});
          }
        }
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 获取要删除的权限代码
    const permissionCodes = [
      'home:access',
      'family:access',
      'assistant:use'
    ];

    // 获取权限ID
    const [permissions] = await queryInterface.sequelize.query(
      `SELECT id FROM permissions WHERE code IN ('${permissionCodes.join("','")}')`
    );

    if (permissions.length > 0) {
      const permissionIds = permissions.map(p => p.id);

      // 删除角色权限关联
      await queryInterface.bulkDelete('role_permissions', {
        permission_id: {
          [Sequelize.Op.in]: permissionIds
        }
      }, {});

      // 删除权限
      await queryInterface.bulkDelete('permissions', {
        code: {
          [Sequelize.Op.in]: permissionCodes
        }
      }, {});
    }
  }
};
